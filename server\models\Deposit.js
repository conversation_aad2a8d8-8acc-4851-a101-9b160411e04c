const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Deposit = sequelize.define('Deposit', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
  },
  order_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '订单号',
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '充值金额',
  },
  actual_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '实际到账金额',
  },
  payment_method: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '支付方式',
  },
  payment_account: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '支付账号',
  },
  payment_channel_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '支付通道ID',
  },
  receiving_card_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '收款银行卡ID',
  },
  bank_card_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '银行卡ID',
  },
  transaction_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '交易ID',
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid', 'cancelled', 'completed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态：pending=待支付, paid=已支付, cancelled=已取消, completed=已完成',
  },
  remark: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '备注',
  },
  payment_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '支付时间',
  },
  completion_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '完成时间',
  },
  payment_platform_order_no: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '支付平台订单号',
  },
  callback_status: {
    type: DataTypes.ENUM('no_callback', 'mock_callback', 'channel_callback'),
    allowNull: true,
    defaultValue: 'no_callback',
    comment: '回调状态：no_callback=未回调, mock_callback=模拟回调, channel_callback=通道回调',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'deposits',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Deposit.associate = (models) => {
  // 充值与用户
  Deposit.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });

  // 充值与收款银行卡
  Deposit.belongsTo(models.BankCard, {
    foreignKey: 'receiving_card_id',
    as: 'receiving_card',
  });

  // 充值与用户银行卡
  Deposit.belongsTo(models.BankCard, {
    foreignKey: 'bank_card_id',
    as: 'bank_card',
  });

  // 充值与支付通道
  Deposit.belongsTo(models.PaymentChannel, {
    foreignKey: 'payment_channel_id',
    as: 'payment_channel',
  });

  // 充值与交易
  Deposit.belongsTo(models.Transaction, {
    foreignKey: 'transaction_id',
    as: 'transaction',
  });
};

module.exports = Deposit;
