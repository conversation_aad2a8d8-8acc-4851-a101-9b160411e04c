import request from '@/utils/request'

/**
 * 取款订单服务接口参数类型
 */
export interface WithdrawalQuery {
  page?: number
  limit?: number
  status?: string
  user_id?: number
  user_id_str?: string
  start_date?: string
  end_date?: string
  keyword?: string
}

/**
 * 取款订单数据类型
 */
export interface Withdrawal {
  id: number
  order_number: string
  user_id: number
  amount: number
  fee: number
  actual_amount: number
  bank_card_id: number
  status: string
  remark?: string
  approval_time?: string
  completion_time?: string
  transaction_id?: number
  created_at: string
  updated_at: string
  user?: {
    id: number
    user_id: string
    username: string
    name?: string
    phone?: string
    email?: string
  }
  bank_card?: {
    id: number
    bank_name: string
    card_number: string
    card_holder: string
  }
  transaction?: {
    id: number
    type: string
    amount: number
    status: string
    created_at: string
  }
}

/**
 * 取款订单响应类型
 */
export interface WithdrawalResponse {
  code: number
  message: string
  data: {
    total: number
    page: number
    limit: number
    items: Withdrawal[]
  }
}

/**
 * 取款订单详情响应类型
 */
export interface WithdrawalDetailResponse {
  code: number
  message: string
  data: Withdrawal
}

/**
 * 审核取款请求参数类型
 */
export interface ApproveWithdrawalRequest {
  status: 'completed' | 'rejected'
  remark?: string
}

/**
 * 审核取款响应类型
 */
export interface ApproveWithdrawalResponse {
  code: number
  message: string
  data: Withdrawal
}

/**
 * 模拟取款完成响应类型
 */
export interface MockWithdrawalResponse {
  code: number
  message: string
  data: Withdrawal
}

/**
 * 取款订单服务
 */
export default {
  /**
   * 获取取款订单列表
   * @param params 查询参数
   * @returns 取款订单列表
   */
  getWithdrawals(params?: WithdrawalQuery): Promise<WithdrawalResponse> {
    return request({
      url: '/api/admin/withdrawals',
      method: 'get',
      params
    })
  },

  /**
   * 获取取款订单详情
   * @param id 取款订单ID
   * @returns 取款订单详情
   */
  getWithdrawal(id: number): Promise<WithdrawalDetailResponse> {
    return request({
      url: `/api/admin/withdrawals/${id}`,
      method: 'get'
    })
  },

  /**
   * 审核取款订单
   * @param id 取款订单ID
   * @param data 审核数据
   * @returns 审核结果
   */
  approveWithdrawal(id: number, data: ApproveWithdrawalRequest): Promise<ApproveWithdrawalResponse> {
    return request({
      url: `/api/admin/withdrawals/${id}/approve`,
      method: 'put',
      data
    })
  },

  /**
   * 模拟取款完成
   * @param id 取款订单ID
   * @returns 模拟结果
   */
  mockWithdrawal(id: number): Promise<MockWithdrawalResponse> {
    return request({
      url: `/api/admin/withdrawals/${id}/mock`,
      method: 'post'
    })
  },

  /**
   * 处理支付失败
   * @param id 取款订单ID
   * @param data 处理数据
   * @returns 处理结果
   */
  handlePaymentFailure(id: number, data: { remark?: string }): Promise<any> {
    return request({
      url: `/api/admin/withdrawals/${id}/payment-failure`,
      method: 'post',
      data
    })
  },

  /**
   * 导出取款订单
   * @param params 查询参数
   * @returns 导出结果
   */
  exportWithdrawals(params?: WithdrawalQuery): Promise<Blob> {
    return request({
      url: '/api/admin/withdrawals/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}
