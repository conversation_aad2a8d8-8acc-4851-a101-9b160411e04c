{"version": 3, "file": "mn.min.mjs", "sources": ["../../../../packages/locale/lang/mn.ts"], "sourcesContent": ["export default {\n  name: 'mn',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Тийм',\n      clear: 'Цэвэрлэх',\n    },\n    datepicker: {\n      now: 'Одоо',\n      today: 'Өнөөдөр',\n      cancel: 'Болих',\n      clear: 'Цэвэрлэх',\n      confirm: 'Тийм',\n      selectDate: 'Огноог сонго',\n      selectTime: 'Цагийг сонго',\n      startDate: 'Эхлэх огноо',\n      startTime: 'Эхлэх цаг',\n      endDate: 'Дуусах огноо',\n      endTime: 'Дуусах цаг',\n      prevYear: 'Өмнөх жил',\n      nextYear: 'Дараа жил',\n      prevMonth: 'Өмнөх сар',\n      nextMonth: 'Дараа сар',\n      year: 'он',\n      month1: '1 сар',\n      month2: '2 сар',\n      month3: '3 сар',\n      month4: '4 сар',\n      month5: '5 сар',\n      month6: '6 сар',\n      month7: '7 сар',\n      month8: '8 сар',\n      month9: '9 сар',\n      month10: '10 сар',\n      month11: '11 сар',\n      month12: '12 сар',\n      week: 'Долоо хоног',\n      weeks: {\n        sun: 'Ням',\n        mon: 'Дав',\n        tue: 'Мяг',\n        wed: 'Лха',\n        thu: 'Пүр',\n        fri: 'Баа',\n        sat: 'Бям',\n      },\n      months: {\n        jan: '1 сар',\n        feb: '2 сар',\n        mar: '3 сар',\n        apr: '4 сар',\n        may: '5 сар',\n        jun: '6 сар',\n        jul: '7 сар',\n        aug: '8 сар',\n        sep: '9 сар',\n        oct: '10 сар',\n        nov: '11 сар',\n        dec: '12 сар',\n      },\n    },\n    select: {\n      loading: 'Ачаалж байна',\n      noMatch: 'Тохирох өгөгдөл байхгүй',\n      noData: 'Өгөгдөл байхгүй',\n      placeholder: 'Сонгох',\n    },\n    mention: {\n      loading: 'Ачаалж байна',\n    },\n    cascader: {\n      noMatch: 'Тохирох өгөгдөл байхгүй',\n      loading: 'Ачаалж байна',\n      placeholder: 'Сонгох',\n      noData: 'Өгөгдөл байхгүй',\n    },\n    pagination: {\n      goto: 'Очих',\n      pagesize: '/хуудас',\n      total: 'Нийт {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Зурвас',\n      confirm: 'Тийм',\n      cancel: 'Болих',\n      error: 'Буруу утга',\n    },\n    upload: {\n      deleteTip: 'Устгахын дарж арилга',\n      delete: 'Устгах',\n      preview: 'Өмнөх',\n      continue: 'Үргэлжлүүлэх',\n    },\n    table: {\n      emptyText: 'Өгөгдөл байхгүй',\n      confirmFilter: 'Зөвшөөрөх',\n      resetFilter: 'Цэвэрлэх',\n      clearFilter: 'Бүгд',\n      sumText: 'Нийт',\n    },\n    tree: {\n      emptyText: 'Өгөгдөл байхгүй',\n    },\n    transfer: {\n      noMatch: 'Тохирох өгөгдөл байхгүй',\n      noData: 'Өгөгдөл байхгүй',\n      titles: ['Жагсаалт 1', 'Жагсаалт 2'],\n      filterPlaceholder: 'Утга оруул',\n      noCheckedFormat: '{total} өгөгдөл',\n      hasCheckedFormat: '{checked}/{total} сонгосон',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,4CAA4C,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,kDAAkD,CAAC,OAAO,CAAC,0BAA0B,CAAC,UAAU,CAAC,qEAAqE,CAAC,UAAU,CAAC,qEAAqE,CAAC,SAAS,CAAC,+DAA+D,CAAC,SAAS,CAAC,mDAAmD,CAAC,OAAO,CAAC,qEAAqE,CAAC,OAAO,CAAC,yDAAyD,CAAC,QAAQ,CAAC,mDAAmD,CAAC,QAAQ,CAAC,mDAAmD,CAAC,SAAS,CAAC,mDAAmD,CAAC,SAAS,CAAC,mDAAmD,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,+DAA+D,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,qEAAqE,CAAC,OAAO,CAAC,kIAAkI,CAAC,MAAM,CAAC,uFAAuF,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,qEAAqE,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kIAAkI,CAAC,OAAO,CAAC,qEAAqE,CAAC,WAAW,CAAC,sCAAsC,CAAC,MAAM,CAAC,uFAAuF,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,uCAAuC,CAAC,KAAK,CAAC,kCAAkC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gHAAgH,CAAC,MAAM,CAAC,sCAAsC,CAAC,OAAO,CAAC,gCAAgC,CAAC,QAAQ,CAAC,0EAA0E,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,uFAAuF,CAAC,aAAa,CAAC,wDAAwD,CAAC,WAAW,CAAC,kDAAkD,CAAC,WAAW,CAAC,0BAA0B,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,uFAAuF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kIAAkI,CAAC,MAAM,CAAC,uFAAuF,CAAC,MAAM,CAAC,CAAC,oDAAoD,CAAC,oDAAoD,CAAC,CAAC,iBAAiB,CAAC,yDAAyD,CAAC,eAAe,CAAC,oDAAoD,CAAC,gBAAgB,CAAC,oEAAoE,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}