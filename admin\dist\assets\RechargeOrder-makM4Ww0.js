/* empty css             *//* empty css                *//* empty css                *//* empty css               *//* empty css                  *//* empty css                     */import"./el-form-item-l0sNRNKZ.js";/* empty css                 *//* empty css                  */import{d as B,r as _,a as v,o as M,u as F,c as V,b as n,e as t,w as a,m as j,aa as q,n as p,x as R,j as U,aD as N,E as O,h as T,i as $,aq as P,at as H,a8 as L,a9 as Y,V as z,a0 as A,p as S,g as b,_ as G}from"./index-LncY9lAB.js";const J={class:"recharge-container"},K={class:"page-header"},Q={class:"header-buttons"},W=B({__name:"RechargeOrder",setup(X){const i=F(),u=_(),c=_(!1),r=v({userId:"",projectId:1,amount:"",paymentChannelId:"",remark:""}),k=v({userId:[{required:!0,message:"请输入用户ID",trigger:"blur"},{pattern:/^\d+$/,message:"用户ID必须为数字",trigger:"blur"}],projectId:[{required:!0,message:"请选择项目",trigger:"change"}],amount:[{required:!0,message:"请输入充值金额",trigger:"blur"},{type:"number",message:"金额必须为数字",trigger:"blur"},{type:"number",min:1,message:"金额必须大于0",trigger:"blur"}],paymentChannelId:[{required:!0,message:"请选择支付通道",trigger:"change"}]}),C=_([{id:1,name:"支付宝"},{id:2,name:"微信支付"},{id:3,name:"银行卡转账"},{id:4,name:"USDT"}]),E=()=>{i.push("/deposits")},h=async()=>{u.value&&await u.value.validate(async(o,e)=>{if(o){c.value=!0;try{await new Promise(s=>setTimeout(s,800)),A.alert(`创建充值订单成功！
订单号：${x()}
用户ID：${r.userId}
金额：¥${r.amount}`,"操作成功",{confirmButtonText:"返回列表",callback:()=>{i.push("/deposits")}})}catch(s){console.error("提交失败:",s),S.error("创建订单失败，请重试")}finally{c.value=!1}}else console.error("表单验证失败:",e),S.error("请完善表单信息")})},w=()=>{u.value&&u.value.resetFields()},x=()=>{const o=new Date,e=o.getFullYear()+String(o.getMonth()+1).padStart(2,"0")+String(o.getDate()).padStart(2,"0")+String(o.getHours()).padStart(2,"0")+String(o.getMinutes()).padStart(2,"0")+String(o.getSeconds()).padStart(2,"0"),s=Math.floor(Math.random()*1e7).toString().padStart(7,"0");return e+s};return M(()=>{const o=i.currentRoute.value.query.userId;o&&(r.userId=o)}),(o,e)=>{const s=R,g=j,f=$,d=T,m=H,I=P,D=O,y=q;return b(),V("div",J,[n("div",K,[e[6]||(e[6]=n("h2",null,"充值订单",-1)),n("div",Q,[t(g,{type:"primary",onClick:E},{default:a(()=>[t(s,null,{default:a(()=>[t(U(N))]),_:1}),e[5]||(e[5]=p("返回列表 "))]),_:1})])]),t(y,{class:"form-card"},{default:a(()=>[t(D,{ref_key:"formRef",ref:u,model:r,rules:k,"label-width":"120px",class:"recharge-form"},{default:a(()=>[t(d,{label:"用户ID",prop:"userId"},{default:a(()=>[t(f,{modelValue:r.userId,"onUpdate:modelValue":e[0]||(e[0]=l=>r.userId=l),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])]),_:1}),t(d,{label:"选择项目",prop:"projectId"},{default:a(()=>[t(I,{modelValue:r.projectId,"onUpdate:modelValue":e[1]||(e[1]=l=>r.projectId=l),placeholder:"请选择项目",style:{width:"100%"}},{default:a(()=>[t(m,{label:"余额充值",value:1}),t(m,{label:"VIP充值",value:2}),t(m,{label:"积分充值",value:3})]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"充值金额",prop:"amount"},{default:a(()=>[t(f,{modelValue:r.amount,"onUpdate:modelValue":e[2]||(e[2]=l=>r.amount=l),modelModifiers:{number:!0},type:"number",placeholder:"请输入充值金额",clearable:""},{append:a(()=>e[7]||(e[7]=[p("元")])),_:1},8,["modelValue"])]),_:1}),t(d,{label:"支付通道",prop:"paymentChannelId"},{default:a(()=>[t(I,{modelValue:r.paymentChannelId,"onUpdate:modelValue":e[3]||(e[3]=l=>r.paymentChannelId=l),placeholder:"请选择支付通道",style:{width:"100%"}},{default:a(()=>[(b(!0),V(L,null,Y(C.value,l=>(b(),z(m,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"备注",prop:"remark"},{default:a(()=>[t(f,{modelValue:r.remark,"onUpdate:modelValue":e[4]||(e[4]=l=>r.remark=l),type:"textarea",placeholder:"请输入备注信息（可选）",rows:3},null,8,["modelValue"])]),_:1}),t(d,{class:"form-actions"},{default:a(()=>[t(g,{type:"primary",loading:c.value,onClick:h},{default:a(()=>e[8]||(e[8]=[p("提交")])),_:1},8,["loading"]),t(g,{onClick:w},{default:a(()=>e[9]||(e[9]=[p("重置")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1}),t(y,{class:"notice-card"},{header:a(()=>e[10]||(e[10]=[n("div",{class:"card-header"},[n("span",null,"充值须知")],-1)])),default:a(()=>[e[11]||(e[11]=n("div",{class:"notice-content"},[n("p",null,"1. 充值前请确认用户ID正确无误，避免充值到错误账户。"),n("p",null,"2. 不同支付通道可能有不同的手续费率，请根据实际情况选择。"),n("p",null,"3. 充值完成后，系统会自动为用户账户增加相应金额。"),n("p",null,"4. 如有任何问题，请联系技术支持。")],-1))]),_:1})])}}}),ue=G(W,[["__scopeId","data-v-cea0d70d"]]);export{ue as default};
