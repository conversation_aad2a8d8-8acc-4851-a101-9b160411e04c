/**
 * Base支付集成测试
 * 测试Base支付服务与数据库配置的集成
 */

const { getBasePayService } = require('../services/basePayService');

// 测试充值订单数据
const testDepositOrder = {
  order_number: 'RE' + Date.now(),
  amount: 100.00,
  user_id: 1
};

/**
 * 测试获取Base支付服务实例
 */
async function testGetBasePayService() {
  console.log('=== 测试获取Base支付服务实例 ===');
  
  try {
    // 使用支付通道ID 6（pay2 - Base支付）
    const basePayService = await getBasePayService(6);
    console.log('✅ Base支付服务实例创建成功');
    console.log('商户号:', basePayService.config.merchant_no);
    console.log('代收密钥:', basePayService.config.payin_key ? '已配置' : '未配置');
    console.log('代付密钥:', basePayService.config.payout_key ? '已配置' : '未配置');
    return basePayService;
  } catch (error) {
    console.error('❌ Base支付服务实例创建失败:', error.message);
    return null;
  }
}

/**
 * 测试创建充值订单（不发送真实请求）
 */
async function testCreatePayment(basePayService) {
  console.log('\n=== 测试创建充值订单 ===');
  
  if (!basePayService) {
    console.error('❌ Base支付服务实例不存在，跳过测试');
    return;
  }

  try {
    // 构建代收请求参数（不发送真实请求）
    const orderDate = new Date().toISOString().slice(0, 19).replace('T', ' ');
    const params = {
      version: '1.0',
      mch_id: basePayService.config.merchant_no,
      notify_url: 'https://m.ohyeah012.xyz/api/payment-callbacks/basepay',
      page_url: 'https://m.ohyeah012.xyz/recharge/success',
      mch_order_no: testDepositOrder.order_number,
      pay_type: basePayService.config.pay_type || '1720',
      trade_amount: testDepositOrder.amount.toFixed(2),
      order_date: orderDate,
      goods_name: 'Top Up',
      mch_return_msg: `user_id:${testDepositOrder.user_id}`,
      sign_type: 'MD5'
    };

    // 生成签名
    const signature = basePayService.generateSignature(params, basePayService.config.payin_key);
    params.sign = signature;

    console.log('✅ 代收请求参数构建成功');
    console.log('订单号:', params.mch_order_no);
    console.log('金额:', params.trade_amount);
    console.log('签名:', params.sign);
    console.log('API地址:', basePayService.config.payin_url);
    
    return true;
  } catch (error) {
    console.error('❌ 代收请求参数构建失败:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runIntegrationTests() {
  console.log('Base支付集成测试开始...\n');
  
  try {
    // 测试获取Base支付服务实例
    const basePayService = await testGetBasePayService();
    
    // 测试创建充值订单
    await testCreatePayment(basePayService);
    
    console.log('\n=== 集成测试完成 ===');
    console.log('✅ 所有测试已完成');
    
    // 退出进程
    process.exit(0);
  } catch (error) {
    console.error('❌ 集成测试过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，则执行测试
if (require.main === module) {
  runIntegrationTests();
}

module.exports = {
  testGetBasePayService,
  testCreatePayment,
  runIntegrationTests
};
