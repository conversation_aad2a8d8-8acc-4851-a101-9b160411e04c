{"version": 3, "file": "fi.min.js", "sources": ["../../../../packages/locale/lang/fi.ts"], "sourcesContent": ["export default {\n  name: 'fi',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Nyt',\n      today: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n      cancel: '<PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Valitse päivä',\n      selectTime: 'Valitse aika',\n      startDate: 'Aloituspäivä',\n      startTime: 'Aloitusaika',\n      endDate: 'Lopetuspäivä',\n      endTime: 'Lopetusaika',\n      prevYear: 'Edellinen vuosi',\n      nextYear: 'Seuraava vuosi',\n      prevMonth: 'Edellinen kuukausi',\n      nextMonth: '<PERSON><PERSON><PERSON> kuukausi',\n      year: '',\n      month1: 'tammikuu',\n      month2: 'helmikuu',\n      month3: 'maaliskuu',\n      month4: 'huhtikuu',\n      month5: 'toukokuu',\n      month6: 'kesäkuu',\n      month7: 'heinäkuu',\n      month8: 'elokuu',\n      month9: 'syyskuu',\n      month10: 'lokakuu',\n      month11: 'marraskuu',\n      month12: 'joulukuu',\n      // week: 'week',\n      weeks: {\n        sun: 'su',\n        mon: 'ma',\n        tue: 'ti',\n        wed: 'ke',\n        thu: 'to',\n        fri: 'pe',\n        sat: 'la',\n      },\n      months: {\n        jan: 'tammi',\n        feb: 'helmi',\n        mar: 'maalis',\n        apr: 'huhti',\n        may: 'touko',\n        jun: 'kesä',\n        jul: 'heinä',\n        aug: 'elo',\n        sep: 'syys',\n        oct: 'loka',\n        nov: 'marras',\n        dec: 'joulu',\n      },\n    },\n    select: {\n      loading: 'Lataa',\n      noMatch: 'Ei vastaavia tietoja',\n      noData: 'Ei tietoja',\n      placeholder: 'Valitse',\n    },\n    mention: {\n      loading: 'Lataa',\n    },\n    cascader: {\n      noMatch: 'Ei vastaavia tietoja',\n      loading: 'Lataa',\n      placeholder: 'Valitse',\n      noData: 'Ei tietoja',\n    },\n    pagination: {\n      goto: 'Mene',\n      pagesize: '/sivu',\n      total: 'Yhteensä {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Viesti',\n      confirm: 'OK',\n      cancel: 'Peruuta',\n      error: 'Virheellinen syöte',\n    },\n    upload: {\n      deleteTip: 'Poista Delete-näppäimellä',\n      delete: 'Poista',\n      preview: 'Esikatsele',\n      continue: 'Jatka',\n    },\n    table: {\n      emptyText: 'Ei tietoja',\n      confirmFilter: 'Vahvista',\n      resetFilter: 'Tyhjennä',\n      clearFilter: 'Kaikki',\n      sumText: 'Summa',\n    },\n    tree: {\n      emptyText: 'Ei tietoja',\n    },\n    transfer: {\n      noMatch: 'Ei vastaavia tietoja',\n      noData: 'Ei tietoja',\n      titles: ['Luettelo 1', 'Luettelo 2'],\n      filterPlaceholder: 'Syötä hakusana',\n      noCheckedFormat: '{total} kohdetta',\n      hasCheckedFormat: '{checked}/{total} valittu',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}