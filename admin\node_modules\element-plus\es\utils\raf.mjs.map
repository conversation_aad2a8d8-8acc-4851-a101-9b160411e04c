{"version": 3, "file": "raf.mjs", "sources": ["../../../../packages/utils/raf.ts"], "sourcesContent": ["import { isClient } from './browser'\n\nexport const rAF = (fn: () => void) =>\n  isClient\n    ? window.requestAnimationFrame(fn)\n    : (setTimeout(fn, 16) as unknown as number)\n\nexport const cAF = (handle: number) =>\n  isClient ? window.cancelAnimationFrame(handle) : clearTimeout(handle)\n"], "names": [], "mappings": ";;AACY,MAAC,GAAG,GAAG,CAAC,EAAE,KAAK,QAAQ,GAAG,MAAM,CAAC,qBAAqB,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE;AAChF,MAAC,GAAG,GAAG,CAAC,MAAM,KAAK,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM;;;;"}