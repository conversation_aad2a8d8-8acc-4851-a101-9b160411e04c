import{s as e}from"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";import"./index-LncY9lAB.js";function p(t){return e({url:"/api/admin/projects",method:"get",params:t})}function u(t){return e({url:"/api/admin/projects",method:"post",data:t})}function c(t,r){return e({url:`/api/admin/projects/${t}`,method:"put",data:r})}function a(t){return e({url:`/api/admin/projects/${t}`,method:"delete"})}function d(t){return e({url:"/api/admin/projects/sort/batch",method:"put",data:t})}export{u as createProject,a as deleteProject,p as getProjects,c as updateProject,d as updateProjectSortOrders};
