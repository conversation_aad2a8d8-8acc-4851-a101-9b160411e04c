# 团队详情页真实数据集成说明

## 🎯 **集成概述**

已完成团队详情页面从测试数据到真实数据的集成，现在页面将显示真实的返佣金额和投资状态。

## 🔧 **后端API修改**

### **1. 添加返佣金额查询**

在 `server/controllers/inviteController.js` 的 `getInvitees` 接口中添加了返佣金额查询：

```javascript
// 查询这些用户为当前用户产生的返佣金额
const commissionData = {};
if (userIds.length > 0) {
  const { Commission } = require('../models');
  const commissions = await Commission.findAll({
    attributes: [
      'from_user_id',
      [sequelize.fn('SUM', sequelize.col('amount')), 'total_commission']
    ],
    where: {
      from_user_id: userIds,
      user_id: userId, // 当前用户ID（接收佣金的用户）
      status: 'paid'
    },
    group: ['from_user_id']
  });

  // 将返佣数据存储到对象中
  commissions.forEach(comm => {
    commissionData[comm.from_user_id] = parseFloat(comm.dataValues.total_commission || 0);
  });
}
```

### **2. 返回数据结构修改**

在API返回数据中添加了 `commission_earned` 字段：

```javascript
// 格式化数据
let invitees = rows.map(relation => ({
  id: relation.user.id,
  username: relation.user.username,
  name: relation.user.name || relation.user.username,
  avatar: relation.user.avatar,
  level: relation.level,
  created_at: relation.createdAt,
  recharged: depositUsers.includes(relation.user.id),
  invested: investmentUsers.includes(relation.user.id), // 投资状态
  commission_earned: commissionData[relation.user.id] || 0, // ⭐ 新增返佣金额
  balance: relation.user.balance || 0
}));
```

## 📱 **前端代码修改**

### **1. 移除测试数据生成**

删除了 `generateTestCommission()` 方法，不再生成模拟数据。

### **2. 使用真实API数据**

修改数据格式化逻辑，使用API返回的真实数据：

```javascript
// 格式化数据
const formattedMembers = items.map(item => ({
  id: item.id,
  username: item.username,
  name: item.name || item.username,
  joinDate: this.formatDate(item.created_at),
  recharged: item.recharged || false, // 充值状态
  invested: item.invested || false,   // 投资状态
  commissionEarned: parseFloat(item.commission_earned || 0).toFixed(2), // ⭐ 真实返佣金额
  level: this.currentLevel
}));
```

### **3. 保持状态判断逻辑**

保持原有的三种状态判断逻辑不变：

```javascript
getCommissionStatus(member) {
  if (!member.invested) {
    // 未投资用户
    return { type: 'no-investment', text: 'No commission yet', icon: '💰' };
  } else if (parseFloat(member.commissionEarned || 0) > 0) {
    // 已投资且有返佣
    return { type: 'has-commission', text: `Commission: ₱${member.commissionEarned}`, icon: '🎁' };
  } else {
    // 已投资但无返佣（等待收益产生）
    return { type: 'pending-commission', text: 'Commission pending (awaiting earnings)', icon: '⏳' };
  }
}
```

## 📊 **数据来源说明**

### **返佣金额计算**

返佣金额来自 `commissions` 表，查询条件：

- **from_user_id**: 产生佣金的用户ID（下级用户）
- **user_id**: 接收佣金的用户ID（当前用户）
- **status**: 'paid'（已发放的佣金）
- **聚合**: SUM(amount) 计算总返佣金额

### **投资状态判断**

投资状态来自 `investments` 表，查询条件：

- **user_id**: 用户ID
- **status**: ['active', 'completed']（有效的投资状态）

### **充值状态判断**

充值状态来自 `transactions` 表，查询条件：

- **user_id**: 用户ID
- **type**: 'deposit'（充值类型）
- **status**: 'completed'（已完成的充值）

## 🎯 **业务逻辑**

### **返佣类型**

系统支持两种返佣类型：

1. **充值返佣**: 用户充值时触发，使用 `recharge_commission_rate_X` 参数
2. **收益返佣**: 用户产品产生收益时触发，使用 `income_commission_rate_X` 参数

### **返佣发放流程**

1. **触发条件**: 用户充值成功或产品产生收益
2. **计算佣金**: 根据系统参数计算各级别返佣金额
3. **创建记录**: 在 `commissions` 表中创建佣金记录
4. **发放佣金**: 将佣金添加到上级用户的收入账户
5. **更新状态**: 将佣金记录状态更新为 'paid'

### **显示逻辑**

团队详情页面显示的是**所有类型**的已发放佣金总和，包括：

- 充值返佣
- 收益返佣
- 其他类型的佣金（如果有）

## 🔍 **数据验证**

### **测试场景**

1. **未投资用户**: 显示 "💰 No commission yet"
2. **已投资无返佣**: 显示 "⏳ Commission pending (awaiting earnings)"
3. **已投资有返佣**: 显示 "🎁 Commission: ₱125.50"

### **数据一致性**

- 返佣金额与佣金记录表一致
- 投资状态与投资表一致
- 充值状态与交易表一致

## ⚠️ **注意事项**

### **性能考虑**

1. **查询优化**: 使用 GROUP BY 避免重复数据
2. **索引使用**: 确保相关字段有适当的索引
3. **数据量**: 大量用户时考虑分页和缓存

### **数据准确性**

1. **状态同步**: 确保佣金状态与实际发放状态一致
2. **金额精度**: 使用 DECIMAL 类型确保金额精度
3. **时区处理**: 确保时间字段的时区一致性

### **错误处理**

1. **API异常**: 处理数据库查询异常
2. **数据缺失**: 处理用户或佣金数据不存在的情况
3. **格式错误**: 处理金额格式化异常

## ✅ **集成完成**

现在团队详情页面已经完全使用真实数据：

- ✅ **返佣金额**: 从 commissions 表获取真实的已发放佣金总额
- ✅ **投资状态**: 从 investments 表获取真实的投资状态
- ✅ **充值状态**: 从 transactions 表获取真实的充值状态
- ✅ **状态判断**: 基于真实数据进行三种状态的智能判断
- ✅ **UI展示**: 保持原有的美观界面和用户体验

用户现在可以看到每个下级成员为自己贡献的真实返佣金额，以及准确的投资状态信息。
