{"version": 3, "file": "empty.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/empty.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\nimport ElEmpty from '@element-plus/components/empty'\nimport type { CSSProperties, FunctionalComponent } from 'vue'\n\ntype EmptyRendererProps = {\n  class?: JSX.IntrinsicAttributes['class']\n  style?: CSSProperties\n}\n\nconst Footer: FunctionalComponent<EmptyRendererProps> = (props, { slots }) => {\n  const defaultSlot = renderSlot(slots, 'default', {}, () => [<ElEmpty />])\n  return (\n    <div class={props.class} style={props.style}>\n      {defaultSlot}\n    </div>\n  )\n}\n\nFooter.displayName = 'ElTableV2Empty'\n\nexport default Footer\n"], "names": ["slots", "renderSlot", "_createVNode", "ElEmpty", "props", "class", "style"], "mappings": ";;;;;;;;AASA,EAAA,KAAqD;AAAaA,CAAAA,KAAAA;AAAF,EAAc,MAAA,WAAA,GAAAC,cAAA,CAAA,KAAA,EAAA,SAAA,EAAA,EAAA,EAAA,MAAA,CAAAC,eAAA,CAAAC,aAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAC5E,EAAA,OAAiBD,eAAA,CAAGD,KAAU,EAAA;AAC9B,IAAA,OAAA,EAAA,KAAA,CAAA,KAAA;IAAA,OACcG,EAAAA,KAAK,CAACC,KADpB;AAAA,GAAA,EAAA,CAAA,YACuC,CAACC,CAAAA;AADxC,CAAA,CAAA;AAKD,MAPD,CAAA,WAAA,GAAA,gBAAA,CAAA;;;;;"}