@use 'sass:math';

@use '../common/var' as *;
@use './mixins' as *;

@mixin col-size($size) {
  @include res($size) {
    @for $i from 0 through 24 {
      .#{$namespace}-col-#{$size}-#{$i} {
        display: if($i == 0, none, block);
        max-width: (math.div(1, 24) * $i * 100) * 1%;
        flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
        @include when(guttered) {
          display: if($i == 0, none, block);
        }
      }

      .#{$namespace}-col-#{$size}-offset-#{$i} {
        margin-left: (math.div(1, 24) * $i * 100) * 1%;
      }

      .#{$namespace}-col-#{$size}-pull-#{$i} {
        position: relative;
        right: (math.div(1, 24) * $i * 100) * 1%;
      }

      .#{$namespace}-col-#{$size}-push-#{$i} {
        position: relative;
        left: (math.div(1, 24) * $i * 100) * 1%;
      }
    }
  }
}
