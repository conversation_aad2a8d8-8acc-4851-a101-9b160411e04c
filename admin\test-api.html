<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <h1>API 连接测试</h1>
    
    <div class="test-section">
        <h3>1. 环境信息</h3>
        <div class="result">
当前访问地址: <span id="currentUrl"></span>
当前域名: <span id="currentDomain"></span>
        </div>
    </div>

    <div class="test-section">
        <h3>2. 测试不同的 API 地址</h3>
        <button onclick="testAPI('https://facai.foxmax.xyz/api/admin/auth/login')">测试 facai.foxmax.xyz</button>
        <button onclick="testAPI('https://foxmax.xyz/api/admin/auth/login')">测试 foxmax.xyz</button>
        <button onclick="testAPI('/api/admin/auth/login')">测试相对路径</button>
        <button onclick="testAPI('http://*************/api/admin/auth/login')">测试 IP 地址</button>
        <div id="apiResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 网络连通性测试</h3>
        <button onclick="testPing('https://facai.foxmax.xyz')">Ping facai.foxmax.xyz</button>
        <button onclick="testPing('https://foxmax.xyz')">Ping foxmax.xyz</button>
        <button onclick="testPing('http://*************')">Ping IP</button>
        <div id="pingResult" class="result"></div>
    </div>

    <script>
        // 显示当前环境信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentDomain').textContent = window.location.hostname;

        async function testAPI(url) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = `正在测试: ${url}...`;
            resultDiv.className = 'result';

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: 'test'
                    })
                });

                const data = await response.text();
                
                resultDiv.innerHTML = `
测试地址: ${url}
状态码: ${response.status}
响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}
响应内容: ${data}
                `;
                
                if (response.status === 200 || response.status === 400 || response.status === 401) {
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `
测试地址: ${url}
错误信息: ${error.message}
错误类型: ${error.name}
                `;
                resultDiv.className = 'result error';
            }
        }

        async function testPing(url) {
            const resultDiv = document.getElementById('pingResult');
            resultDiv.textContent = `正在测试连通性: ${url}...`;
            resultDiv.className = 'result';

            try {
                const startTime = Date.now();
                const response = await fetch(url, {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                const endTime = Date.now();
                
                resultDiv.innerHTML = `
测试地址: ${url}
响应时间: ${endTime - startTime}ms
状态: 连接成功
                `;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `
测试地址: ${url}
错误信息: ${error.message}
状态: 连接失败
                `;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
