const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/BasicPanel-DNa5ErK_.js","assets/el-tooltip-l0sNRNKZ.js","assets/index-LncY9lAB.js","assets/index-D6_EvlvT.css","assets/index-t--hEgTQ.js","assets/BasicPanel-C-EFGlfG.css","assets/base-CMRK1q-d.css","assets/el-button-BLVIn-nA.css","assets/el-table-column-Cd4wX_xu.css","assets/el-scrollbar-CYqf3UuD.css","assets/el-tag-DljBBxJR.css","assets/el-select-CC5J7eGq.css","assets/el-switch-pqxnpAn2.css","assets/el-input-D0QJWgJk.css","assets/EmailPanel-BPvx_ges.js","assets/EmailPanel-CKBSISNu.css","assets/DictionaryPanel-yJuWQ_bd.js","assets/DictionaryPanel-DQ0DEcno.css","assets/UserPanel-DGnbxRZi.js","assets/UserPanel-BEDsPLwk.css","assets/RatePanel-CZnQ2aVC.js","assets/RatePanel-Nmag91Lc.css","assets/el-radio-DjqZq32A.css","assets/el-checkbox-group-D_6SYB2i.css","assets/OtherPanel-DmmiEhC0.js","assets/OtherPanel-ClajF8X9.css"])))=>i.map(i=>d[i]);
import{d as V,aW as a,aX as e,r as h,q as y,o as A,c as i,b as s,e as R,w as T,aa as k,V as m,a8 as I,a9 as L,v as O,y as x,aY as g,aZ as B,aK as n,g as r,_ as w}from"./index-LncY9lAB.js";/* empty css             *//* empty css                */const K={class:"system-params-container"},N={class:"params-nav"},q=["onClick"],z=V({__name:"index",setup(F){const d=e(()=>n(()=>import("./BasicPanel-DNa5ErK_.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]))),p=e(()=>n(()=>import("./EmailPanel-BPvx_ges.js"),__vite__mapDeps([14,1,2,3,15,6,8,9,7,10,11,13]))),v=e(()=>n(()=>import("./DictionaryPanel-yJuWQ_bd.js"),__vite__mapDeps([16,1,2,3,17,6,7,8,9,10,13]))),u=e(()=>n(()=>import("./UserPanel-DGnbxRZi.js"),__vite__mapDeps([18,1,2,3,19,6,7,8,9,10,11,13]))),P=e(()=>n(()=>import("./RatePanel-CZnQ2aVC.js"),__vite__mapDeps([20,1,2,3,4,21,6,22,8,9,7,23,12,10,11,13]))),E=e(()=>n(()=>import("./OtherPanel-DmmiEhC0.js"),__vite__mapDeps([24,1,4,2,3,25,6,7,8,9,10]))),_=[{name:"basic",label:"基础配置",component:a(d)},{name:"email",label:"邮件配置",component:a(p)},{name:"dictionary",label:"字典配置",component:a(v)},{name:"user",label:"会员配置",component:a(u)},{name:"rate",label:"资金配置",component:a(P)},{name:"other",label:"其它配置",component:a(E)}],c=h("basic"),b=y(()=>{const t=_.find(o=>o.name===c.value);return t?t.component:null}),f=t=>{c.value=t};return A(()=>{}),(t,o)=>{const C=k;return r(),i("div",K,[o[0]||(o[0]=s("div",{class:"page-header"},[s("h2",null,"系统参数设置")],-1)),R(C,{class:"params-card"},{default:T(()=>[s("div",N,[(r(),i(I,null,L(_,(l,D)=>s("div",{key:D,class:O(["nav-item",{active:c.value===l.name}]),onClick:M=>f(l.name)},x(l.label),11,q)),64))]),(r(),m(B,null,[(r(),m(g(b.value)))],1024))]),_:1})])}}}),X=w(z,[["__scopeId","data-v-0c85d94b"]]);export{X as default};
