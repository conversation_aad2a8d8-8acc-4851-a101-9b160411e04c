/**
 * 基础功能测试
 * 测试方案3.1修改后的基础功能是否正常
 */

const { Investment, Project, User, InvestmentProfit } = require('../models');
const sequelize = require('../config/database');
const profitSystem = require('../services/profitSystem');
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');

// 使用现有数据进行测试，不需要预定义测试数据

/**
 * 清理测试数据
 */
const cleanupTestData = async () => {
  try {
    // 只清理测试创建的收益记录（如果有的话）
    if (global.testProfitRecord) {
      await InvestmentProfit.destroy({ where: { id: global.testProfitRecord.id } });
      logger.info('测试收益记录清理完成');
    }
  } catch (error) {
    logger.warn('清理测试数据时出错:', error.message);
  }
};

// 移除createTestData函数，使用现有数据进行测试

/**
 * 测试1：Redis连接和基础操作
 */
const testRedisConnection = async () => {
  console.log('\n=== 测试1：Redis连接和基础操作 ===');
  
  try {
    // 测试Redis连接
    await redisClient.client.ping();
    console.log('✅ Redis连接正常');
    
    // 测试基础操作
    await redisClient.set('test_key', 'test_value');
    const value = await redisClient.get('test_key');
    
    if (value === 'test_value') {
      console.log('✅ Redis基础操作正常');
    } else {
      console.log('❌ Redis基础操作异常');
      return false;
    }
    
    // 清理测试键
    await redisClient.del('test_key');
    
    return true;
  } catch (error) {
    console.log('❌ Redis测试失败:', error.message);
    return false;
  }
};

/**
 * 测试2：数据库连接和模型操作
 */
const testDatabaseConnection = async () => {
  console.log('\n=== 测试2：数据库连接和模型操作 ===');

  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接正常');

    // 使用现有的活跃投资进行测试
    const existingInvestment = await Investment.findOne({
      where: { status: 'active' },
      include: [
        { model: Project, as: 'project' },
        { model: User, as: 'user' }
      ]
    });

    if (existingInvestment && existingInvestment.project && existingInvestment.user) {
      console.log('✅ 找到现有投资数据');
      console.log(`   - 用户ID: ${existingInvestment.user.id}`);
      console.log(`   - 项目ID: ${existingInvestment.project.id}`);
      console.log(`   - 投资ID: ${existingInvestment.id}`);
      console.log(`   - 投资金额: ${existingInvestment.amount}`);
      console.log(`   - 收益周期: ${existingInvestment.project.profit_time}小时`);

      // 将找到的数据保存到全局变量，供后续测试使用
      global.testInvestment = existingInvestment;

      console.log('✅ 数据库关联查询正常');

    } else {
      console.log('❌ 没有找到可用的投资数据');
      console.log('   - 请确保数据库中有活跃的投资记录');
      return false;
    }

    return true;
  } catch (error) {
    console.log('❌ 数据库测试失败:', error.message);
    return false;
  }
};

/**
 * 测试3：收益系统启动
 */
const testProfitSystemStartup = async () => {
  console.log('\n=== 测试3：收益系统启动 ===');
  
  try {
    // 启动收益系统
    const result = await profitSystem.startProfitSystem();
    
    if (result.success) {
      console.log('✅ 收益系统启动成功');
      console.log(`   - Redis可用: ${result.redis_available}`);
      console.log(`   - 补发收益: ${result.compensated_profits || 0} 笔`);
    } else {
      console.log('❌ 收益系统启动失败:', result.message);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 收益系统启动测试失败:', error.message);
    return false;
  }
};

/**
 * 测试4：Redis任务调度
 */
const testRedisTaskScheduling = async () => {
  console.log('\n=== 测试4：Redis任务调度 ===');

  try {
    // 使用全局变量中的测试投资数据
    const investment = global.testInvestment;

    if (!investment) {
      console.log('❌ 找不到测试投资，请先运行数据库测试');
      return false;
    }

    console.log(`   - 使用投资ID: ${investment.id}`);
    
    // 测试Redis重建逻辑
    const rebuildResult = await profitSystem.simpleRedisRebuild(investment, investment.project);
    
    if (rebuildResult.success) {
      console.log('✅ Redis任务调度成功');
      console.log(`   - 补发收益: ${rebuildResult.compensated} 笔`);
      console.log(`   - 下次收益时间: ${rebuildResult.nextProfitTime}`);
    } else {
      console.log('❌ Redis任务调度失败:', rebuildResult.error);
      return false;
    }
    
    // 验证Redis中是否有任务
    const allTasks = await redisClient.getAllProfitTasks();
    const investmentTask = allTasks.find(task => task.investment_id === investment.id);
    
    if (investmentTask) {
      console.log('✅ Redis任务创建成功');
      console.log(`   - 投资ID: ${investmentTask.investment_id}`);
      console.log(`   - 计划时间: ${investmentTask.next_profit_time}`);
    } else {
      console.log('❌ Redis任务创建失败');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ Redis任务调度测试失败:', error.message);
    return false;
  }
};

/**
 * 测试5：收益记录创建
 */
const testProfitRecordCreation = async () => {
  console.log('\n=== 测试5：收益记录创建 ===');

  try {
    // 使用全局变量中的测试投资数据
    const investment = global.testInvestment;

    if (!investment) {
      console.log('❌ 找不到测试投资，请先运行数据库测试');
      return false;
    }

    console.log(`   - 使用投资ID: ${investment.id}`);
    
    // 创建收益记录
    const profitTime = new Date();
    const profitRecord = await profitSystem.createProfitRecord(investment, profitTime);
    
    if (profitRecord) {
      console.log('✅ 收益记录创建成功');
      console.log(`   - 收益ID: ${profitRecord.id}`);
      console.log(`   - 金额: ${profitRecord.amount}`);
      console.log(`   - 时间: ${profitRecord.profit_time}`);

      // 保存创建的记录，用于后续清理
      global.testProfitRecord = profitRecord;

      // 验证用户余额是否更新
      const originalBalance = investment.user.balance;
      const updatedUser = await User.findByPk(investment.user.id);
      if (updatedUser.balance > originalBalance) {
        console.log('✅ 用户余额更新成功');
        console.log(`   - 原始余额: ${originalBalance}`);
        console.log(`   - 更新后余额: ${updatedUser.balance}`);
        console.log(`   - 增加金额: ${updatedUser.balance - originalBalance}`);
      } else {
        console.log('❌ 用户余额更新失败');
        console.log(`   - 原始余额: ${originalBalance}`);
        console.log(`   - 更新后余额: ${updatedUser.balance}`);
        return false;
      }
      
    } else {
      console.log('❌ 收益记录创建失败');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 收益记录创建测试失败:', error.message);
    return false;
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('开始执行基础功能测试...\n');
  
  let passedTests = 0;
  const totalTests = 5;
  
  try {
    // 清理旧数据
    await cleanupTestData();
    
    // 执行测试
    if (await testRedisConnection()) passedTests++;
    if (await testDatabaseConnection()) passedTests++;
    if (await testProfitSystemStartup()) passedTests++;
    if (await testRedisTaskScheduling()) passedTests++;
    if (await testProfitRecordCreation()) passedTests++;
    
  } catch (error) {
    console.log('\n❌ 测试执行过程中出现错误:', error.message);
  } finally {
    // 清理测试数据
    await cleanupTestData();
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有基础功能测试通过！');
    return true;
  } else {
    console.log('⚠️  部分测试失败，请检查问题');
    return false;
  }
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testRedisConnection,
  testDatabaseConnection,
  testProfitSystemStartup,
  testRedisTaskScheduling,
  testProfitRecordCreation,
  cleanupTestData
};
