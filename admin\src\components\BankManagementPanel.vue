<template>
  <div class="bank-management-panel">
    <div class="panel-header">
      <h3>银行信息管理</h3>
      <el-alert
        type="info"
        :closable="false"
        show-icon
      >
        <p>在此页面可以添加、编辑和删除银行信息。这些银行信息将用于银行编码映射和用户银行卡管理。</p>
      </el-alert>
    </div>

    <div class="panel-content">
      <el-table
        :data="banks"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="银行名称" min-width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'danger'">
              {{ scope.row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button
              :type="scope.row.status ? 'danger' : 'success'"
              size="small"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.status ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="panel-footer">
        <el-button type="primary" @click="handleAdd">添加银行</el-button>
      </div>
    </div>

    <!-- 编辑/添加弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isAddMode ? '添加银行' : '编辑银行'"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form :model="editForm" label-width="100px" :rules="rules" ref="editFormRef">
        <el-form-item label="银行名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入银行名称" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="editForm.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveBank">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import bankMappingService from '@/api/services/bankMappingService';

// 状态变量
const loading = ref(false);
const banks = ref([]);
const editDialogVisible = ref(false);
const isAddMode = ref(false);
const editForm = ref({
  id: null,
  name: '',
  status: true
});
const editFormRef = ref(null);

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入银行名称', trigger: 'blur' },
    { min: 2, max: 50, message: '银行名称长度应在2到50个字符之间', trigger: 'blur' }
  ]
};

// 获取所有银行
const fetchBanks = async () => {
  loading.value = true;
  try {
    const response = await bankMappingService.getAllBanks();

    // 处理响应格式
    if (response && typeof response === 'object') {
      if (response.code === 200) {
        // 新的响应格式：{ code: 200, message: '获取成功', data: [...] }
        banks.value = response.data || [];
      } else if (Array.isArray(response)) {
        // 旧的响应格式：直接是数组
        banks.value = response;
      } else {
        ElMessage.error(response.message || '获取银行列表失败');
      }
    } else {
      ElMessage.error('获取银行列表失败，响应格式错误');
    }
  } catch (error) {
    console.error('获取银行列表错误:', error);
    ElMessage.error('获取银行列表失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
};

// 添加银行
const handleAdd = () => {
  isAddMode.value = true;
  editForm.value = {
    id: null,
    name: '',
    status: true
  };
  editDialogVisible.value = true;
};

// 编辑银行
const handleEdit = (row) => {
  isAddMode.value = false;
  editForm.value = {
    id: row.id,
    name: row.name,
    status: row.status
  };
  editDialogVisible.value = true;
};

// 切换状态
const handleToggleStatus = async (row) => {
  try {
    const response = await bankMappingService.updateBankStatus(row.id, !row.status);

    // 处理响应格式
    if (response && typeof response === 'object' && response.code === 200) {
      ElMessage.success(`${row.status ? '禁用' : '启用'}成功`);
      fetchBanks();
    } else {
      const message = response && response.message ? response.message : `${row.status ? '禁用' : '启用'}失败`;
      ElMessage.error(message);
    }
  } catch (error) {
    console.error(`${row.status ? '禁用' : '启用'}银行错误:`, error);
    ElMessage.error(`${row.status ? '禁用' : '启用'}失败`);
  }
};

// 删除银行
const handleDelete = async (row) => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确定要删除银行 "${row.name}" 吗？此操作不可恢复，且可能影响已关联的银行卡和银行编码映射。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 用户确认删除，调用删除API
    const response = await bankMappingService.deleteBank(row.id);

    // 处理响应格式
    if (response && typeof response === 'object') {
      if (response.code === 200) {
        ElMessage.success('删除成功');
        fetchBanks(); // 刷新列表
      } else if (response.code === 400 && response.message) {
        // 特殊处理各种引用情况
        if (response.message.includes('银行编码映射引用')) {
          ElMessageBox.alert(
            '该银行已被银行编码映射引用，无法直接删除。请先删除相关的银行编码映射，然后再尝试删除此银行。',
            '无法删除',
            {
              confirmButtonText: '我知道了',
              type: 'warning'
            }
          );
        } else if (response.message.includes('银行卡引用')) {
          ElMessageBox.alert(
            '该银行已被用户的银行卡引用，无法直接删除。请先删除或修改相关的银行卡信息，然后再尝试删除此银行。',
            '无法删除',
            {
              confirmButtonText: '我知道了',
              type: 'warning'
            }
          );
        } else {
          // 其他错误情况
          ElMessage.error(response.message || '删除失败');
        }
      } else {
        const message = response.message || '删除失败';
        ElMessage.error(message);
      }
    } else {
      ElMessage.error('删除失败，响应格式错误');
    }
  } catch (error) {
    // 如果是用户取消操作，不显示错误
    if (error === 'cancel' || error.toString().includes('cancel')) {
      return;
    }

    console.error('删除银行错误:', error);
    ElMessage.error('删除失败，请稍后重试');
  }
};

// 保存银行
const saveBank = async () => {
  if (!editFormRef.value) return;

  try {
    await editFormRef.value.validate();

    let response;
    if (isAddMode.value) {
      // 添加银行
      response = await bankMappingService.createBank(editForm.value);
    } else {
      // 更新银行
      response = await bankMappingService.updateBank(editForm.value.id, editForm.value);
    }

    // 处理响应格式
    if (response && typeof response === 'object' && response.code === 200) {
      ElMessage.success(isAddMode.value ? '添加成功' : '更新成功');
      editDialogVisible.value = false;
      fetchBanks();
    } else {
      const message = response && response.message ? response.message : (isAddMode.value ? '添加失败' : '更新失败');
      ElMessage.error(message);
    }
  } catch (error) {
    if (error.name === 'ValidationError') {
      return; // 表单验证错误，已经显示了错误消息
    }
    console.error('保存银行错误:', error);
    ElMessage.error(isAddMode.value ? '添加失败' : '更新失败');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchBanks();
});
</script>

<style scoped>
.bank-management-panel {
  padding: 10px;
}

.panel-header {
  margin-bottom: 20px;
}

.panel-footer {
  margin-top: 20px;
  text-align: right;
}
</style>
