{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/message/index.ts"], "sourcesContent": ["import { withInstallFunction } from '@element-plus/utils'\n\nimport Message from './src/method'\n\nexport const ElMessage = withInstallFunction(Message, '$message')\nexport default ElMessage\n\nexport * from './src/message'\n"], "names": ["withInstallFunction", "Message"], "mappings": ";;;;;;;;AAEY,MAAC,SAAS,GAAGA,2BAAmB,CAACC,iBAAO,EAAE,UAAU;;;;;;;;;"}