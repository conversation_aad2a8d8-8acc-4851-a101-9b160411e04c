/**
 * 移动端支付通道路由
 */
const express = require('express');
const router = express.Router();
const mobilePaymentChannelController = require('../controllers/mobilePaymentChannelController');
const { verifyUserToken } = require('../middlewares/authMiddleware');

// 使用用户认证中间件
router.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/payment-channels:
 *   get:
 *     summary: 获取可用的支付通道列表
 *     tags: [移动端支付通道]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       500:
 *         description: 服务器错误
 */
router.get('/', mobilePaymentChannelController.getAvailablePaymentChannels);

module.exports = router;
