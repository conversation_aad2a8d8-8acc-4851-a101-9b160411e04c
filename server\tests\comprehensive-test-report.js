/**
 * 方案5.1：综合测试报告
 * 运行所有测试并生成详细报告
 */

const timeTests = require('./test-time-functions');
const profitTests = require('./test-profit-system');
const frontendTests = require('./test-frontend-time-display');

/**
 * 生成测试报告
 */
async function generateTestReport() {
  console.log('🧪 方案5.1 综合测试报告');
  console.log('=====================================');
  console.log(`测试时间: ${new Date().toISOString()}`);
  console.log('=====================================\n');

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      successRate: 0
    },
    testResults: {}
  };

  try {
    // 1. 运行时间函数测试
    console.log('📊 1. 时间函数测试');
    console.log('-------------------');
    const timeResults = await timeTests.runTimeTests();
    
    const timeTestsPassed = (
      timeResults.dateUtils?.timeConsistency &&
      timeResults.timezoneUtils?.utcTimeCorrect &&
      timeResults.profitCalculation?.nextProfitTime
    );
    
    report.testResults.timeFunctions = {
      passed: timeTestsPassed,
      details: timeResults
    };
    
    console.log(`结果: ${timeTestsPassed ? '✅ 通过' : '❌ 失败'}\n`);

    // 2. 运行收益系统测试
    console.log('💰 2. 收益系统测试');
    console.log('-------------------');
    const profitResults = profitTests.runProfitSystemTests();
    
    const profitTestsPassed = (
      profitResults.timeCalculation?.firstProfitCalculation?.correct &&
      profitResults.timeCalculation?.nextProfitCalculation?.correct &&
      profitResults.crossTimezone?.timestampConsistency &&
      profitResults.distributionConditions?.every(test => test.correct)
    );
    
    report.testResults.profitSystem = {
      passed: profitTestsPassed,
      details: profitResults
    };
    
    console.log(`结果: ${profitTestsPassed ? '✅ 通过' : '❌ 失败'}\n`);

    // 3. 运行前端时间显示测试
    console.log('🖥️ 3. 前端时间显示测试');
    console.log('----------------------');
    const frontendResults = frontendTests.runFrontendTimeTests();
    
    const frontendTestsPassed = (
      frontendResults.adminFormatting?.every(test => test.success) &&
      frontendResults.mobileFormatting?.every(test => test.success) &&
      frontendResults.timezoneConsistency?.displayConsistency &&
      frontendResults.timezoneConsistency?.timestampConsistency
    );
    
    report.testResults.frontendDisplay = {
      passed: frontendTestsPassed,
      details: frontendResults
    };
    
    console.log(`结果: ${frontendTestsPassed ? '✅ 通过' : '❌ 失败'}\n`);

    // 计算总体统计
    const testCategories = [timeTestsPassed, profitTestsPassed, frontendTestsPassed];
    report.summary.totalTests = testCategories.length;
    report.summary.passedTests = testCategories.filter(Boolean).length;
    report.summary.failedTests = report.summary.totalTests - report.summary.passedTests;
    report.summary.successRate = (report.summary.passedTests / report.summary.totalTests * 100).toFixed(1);

    // 生成详细报告
    console.log('📋 测试总结');
    console.log('=====================================');
    console.log(`总测试类别: ${report.summary.totalTests}`);
    console.log(`通过测试: ${report.summary.passedTests}`);
    console.log(`失败测试: ${report.summary.failedTests}`);
    console.log(`成功率: ${report.summary.successRate}%\n`);

    // 详细结果分析
    console.log('🔍 详细结果分析');
    console.log('=====================================');
    
    console.log('1. 时间函数测试详情：');
    if (timeResults.dateUtils) {
      console.log(`   - dateUtils UTC处理: ${timeResults.dateUtils.timeConsistency ? '✅' : '❌'}`);
      console.log(`   - UTC时间边界: ${timeResults.dateUtils.utcDayBoundaries?.startMatch && timeResults.dateUtils.utcDayBoundaries?.endMatch ? '✅' : '❌'}`);
    }
    if (timeResults.timezoneUtils) {
      console.log(`   - timezoneUtils UTC: ${timeResults.timezoneUtils.utcTimeCorrect ? '✅' : '❌'}`);
      console.log(`   - 系统时区: ${timeResults.timezoneUtils.systemTimezone || '未设置'}`);
    }
    console.log('');

    console.log('2. 收益系统测试详情：');
    if (profitResults.timeCalculation) {
      console.log(`   - 首次收益计算: ${profitResults.timeCalculation.firstProfitCalculation?.correct ? '✅' : '❌'}`);
      console.log(`   - 后续收益计算: ${profitResults.timeCalculation.nextProfitCalculation?.correct ? '✅' : '❌'}`);
    }
    if (profitResults.crossTimezone) {
      console.log(`   - 跨时区一致性: ${profitResults.crossTimezone.timestampConsistency ? '✅' : '❌'}`);
    }
    if (profitResults.distributionConditions) {
      const conditionsPassed = profitResults.distributionConditions.filter(test => test.correct).length;
      console.log(`   - 发放条件判断: ${conditionsPassed}/${profitResults.distributionConditions.length} 通过`);
    }
    console.log('');

    console.log('3. 前端显示测试详情：');
    if (frontendResults.adminFormatting) {
      const adminPassed = frontendResults.adminFormatting.filter(test => test.success).length;
      console.log(`   - 管理端格式化: ${adminPassed}/${frontendResults.adminFormatting.length} 通过`);
    }
    if (frontendResults.mobileFormatting) {
      const mobilePassed = frontendResults.mobileFormatting.filter(test => test.success).length;
      console.log(`   - 移动端格式化: ${mobilePassed}/${frontendResults.mobileFormatting.length} 通过`);
    }
    if (frontendResults.timezoneConsistency) {
      console.log(`   - 时区一致性: ${frontendResults.timezoneConsistency.displayConsistency && frontendResults.timezoneConsistency.timestampConsistency ? '✅' : '❌'}`);
    }
    if (frontendResults.edgeCases) {
      const edgePassed = frontendResults.edgeCases.filter(test => test.handledCorrectly).length;
      console.log(`   - 边界情况处理: ${edgePassed}/${frontendResults.edgeCases.length} 通过`);
    }
    console.log('');

    // 生成建议
    console.log('💡 测试建议');
    console.log('=====================================');
    
    if (report.summary.successRate >= 90) {
      console.log('✅ 测试结果优秀！方案5.1实现质量很高。');
      console.log('建议：');
      console.log('- 可以进行生产环境部署');
      console.log('- 建议先在测试环境进行数据迁移验证');
      console.log('- 准备好监控和回滚方案');
    } else if (report.summary.successRate >= 70) {
      console.log('⚠️ 测试结果良好，但有部分问题需要解决。');
      console.log('建议：');
      console.log('- 修复失败的测试项目');
      console.log('- 重新运行测试确保问题解决');
      console.log('- 在测试环境充分验证后再部署');
    } else {
      console.log('❌ 测试结果不理想，需要重点修复问题。');
      console.log('建议：');
      console.log('- 优先修复核心功能问题');
      console.log('- 逐项解决失败的测试');
      console.log('- 暂缓生产环境部署');
    }
    console.log('');

    // 下一步行动计划
    console.log('🎯 下一步行动计划');
    console.log('=====================================');
    
    if (timeTestsPassed && profitTestsPassed) {
      console.log('1. ✅ 核心功能测试通过，可以进行数据迁移测试');
      console.log('2. 📊 运行数据迁移验证脚本');
      console.log('3. 🔄 在测试环境执行完整的迁移流程');
      console.log('4. 📈 监控收益发放功能是否正常');
    } else {
      console.log('1. ❌ 核心功能测试未完全通过，需要先修复问题');
      console.log('2. 🔧 检查失败的测试项目');
      console.log('3. 🛠️ 修复代码问题');
      console.log('4. 🔄 重新运行测试');
    }
    
    if (frontendTestsPassed) {
      console.log('5. ✅ 前端显示功能正常，用户体验良好');
    } else {
      console.log('5. ⚠️ 前端显示需要优化，关注用户体验');
    }
    
    console.log('6. 📋 准备生产环境部署计划');
    console.log('7. 🛡️ 准备监控和回滚方案');
    console.log('');

    // 风险评估
    console.log('⚠️ 风险评估');
    console.log('=====================================');
    
    const risks = [];
    
    if (!timeTestsPassed) {
      risks.push('🔥 高风险：时间函数处理存在问题，可能影响收益发放准确性');
    }
    
    if (!profitTestsPassed) {
      risks.push('🔥 高风险：收益系统逻辑存在问题，可能导致收益发放错误');
    }
    
    if (!frontendTestsPassed) {
      risks.push('⚠️ 中风险：前端时间显示存在问题，可能影响用户体验');
    }
    
    if (risks.length === 0) {
      console.log('✅ 风险评估：低风险，可以安全部署');
    } else {
      risks.forEach(risk => console.log(risk));
    }
    
    console.log('\n🎉 测试报告生成完成！');
    
    return report;

  } catch (error) {
    console.error('❌ 测试报告生成失败:', error);
    return {
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  generateTestReport();
}

module.exports = {
  generateTestReport
};
