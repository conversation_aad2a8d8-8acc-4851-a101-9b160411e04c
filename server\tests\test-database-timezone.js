/**
 * 方案5.1：数据库时区配置测试
 * 测试数据库连接和时区设置
 */

const { sequelize } = require('../config/database');
const timezoneConfig = require('../utils/timezoneConfig');
const moment = require('moment');

/**
 * 测试数据库连接和时区设置
 */
async function testDatabaseTimezone() {
  console.log('🗄️ 测试数据库时区配置...\n');

  try {
    // 测试1：数据库连接
    console.log('1. 测试数据库连接：');
    await sequelize.authenticate();
    console.log('  ✅ 数据库连接成功\n');

    // 测试2：检查数据库时区设置
    console.log('2. 检查数据库时区设置：');
    const [timezoneResult] = await sequelize.query(`
      SELECT 
        @@session.time_zone as session_timezone,
        @@system_time_zone as system_timezone,
        @@global.time_zone as global_timezone,
        NOW() as current_time,
        UTC_TIMESTAMP() as utc_time
    `);

    const dbInfo = timezoneResult[0];
    console.log(`  会话时区: ${dbInfo.session_timezone}`);
    console.log(`  系统时区: ${dbInfo.system_timezone}`);
    console.log(`  全局时区: ${dbInfo.global_timezone}`);
    console.log(`  数据库当前时间: ${dbInfo.current_time}`);
    console.log(`  数据库UTC时间: ${dbInfo.utc_time}\n`);

    // 测试3：时区配置初始化
    console.log('3. 测试时区配置初始化：');
    const systemTimezone = await timezoneConfig.initialize();
    console.log(`  初始化后的系统时区: ${systemTimezone}\n`);

    // 测试4：验证时区配置是否生效
    console.log('4. 验证时区配置是否生效：');
    const [afterInitResult] = await sequelize.query(`
      SELECT 
        @@session.time_zone as session_timezone,
        NOW() as current_time,
        UTC_TIMESTAMP() as utc_time
    `);

    const afterInit = afterInitResult[0];
    console.log(`  初始化后会话时区: ${afterInit.session_timezone}`);
    console.log(`  初始化后当前时间: ${afterInit.current_time}`);
    console.log(`  初始化后UTC时间: ${afterInit.utc_time}\n`);

    // 测试5：时间差异计算
    console.log('5. 时间差异分析：');
    const dbTime = moment(afterInit.current_time);
    const utcTime = moment(afterInit.utc_time);
    const appTime = moment();

    console.log(`  数据库时间: ${dbTime.format('YYYY-MM-DD HH:mm:ss')}`);
    console.log(`  数据库UTC时间: ${utcTime.format('YYYY-MM-DD HH:mm:ss')}`);
    console.log(`  应用程序时间: ${appTime.format('YYYY-MM-DD HH:mm:ss')}`);

    const dbUtcDiff = dbTime.diff(utcTime, 'hours');
    const appDbDiff = Math.abs(appTime.diff(utcTime, 'seconds'));

    console.log(`  数据库时间与UTC差异: ${dbUtcDiff}小时`);
    console.log(`  应用程序与数据库UTC差异: ${appDbDiff}秒\n`);

    // 测试6：检查系统参数表中的时区设置
    console.log('6. 检查系统参数表中的时区设置：');
    const [paramResult] = await sequelize.query(`
      SELECT param_key, param_value, created_at, updated_at
      FROM system_params 
      WHERE param_key = '[site.timezone]'
      LIMIT 1
    `);

    if (paramResult.length > 0) {
      const param = paramResult[0];
      console.log(`  参数键: ${param.param_key}`);
      console.log(`  参数值: ${param.param_value}`);
      console.log(`  创建时间: ${param.created_at}`);
      console.log(`  更新时间: ${param.updated_at}\n`);
    } else {
      console.log('  ⚠️  未找到时区配置参数\n');
    }

    return {
      success: true,
      databaseConnected: true,
      sessionTimezone: afterInit.session_timezone,
      systemTimezone: systemTimezone,
      timeDifference: appDbDiff,
      timezoneParamExists: paramResult.length > 0,
      timezoneParamValue: paramResult.length > 0 ? paramResult[0].param_value : null
    };

  } catch (error) {
    console.error('❌ 数据库时区测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试时区配置刷新功能
 */
async function testTimezoneRefresh() {
  console.log('🔄 测试时区配置刷新功能...\n');

  try {
    // 获取当前时区配置
    console.log('1. 获取当前时区配置：');
    const currentTimezone = timezoneConfig.getTimezone();
    console.log(`  当前时区: ${currentTimezone}\n`);

    // 测试刷新功能
    console.log('2. 测试刷新功能：');
    const refreshedTimezone = await timezoneConfig.refreshTimezone();
    console.log(`  刷新后时区: ${refreshedTimezone}\n`);

    // 验证刷新后的数据库设置
    console.log('3. 验证刷新后的数据库设置：');
    const [refreshResult] = await sequelize.query(`
      SELECT @@session.time_zone as session_timezone
    `);

    console.log(`  刷新后数据库会话时区: ${refreshResult[0].session_timezone}\n`);

    return {
      success: true,
      currentTimezone: currentTimezone,
      refreshedTimezone: refreshedTimezone,
      databaseTimezone: refreshResult[0].session_timezone,
      consistent: refreshedTimezone === refreshResult[0].session_timezone
    };

  } catch (error) {
    console.error('❌ 时区刷新测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试时间数据的存储和读取
 */
async function testTimeDataStorage() {
  console.log('💾 测试时间数据的存储和读取...\n');

  try {
    // 创建测试表（如果不存在）
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS test_time_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_time DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 插入测试数据
    console.log('1. 插入测试时间数据：');
    const testTime = '2024-01-15 10:30:00'; // 假设这是UTC时间
    await sequelize.query(`
      INSERT INTO test_time_data (test_time) VALUES (?)
    `, {
      replacements: [testTime]
    });

    console.log(`  插入时间: ${testTime}\n`);

    // 读取测试数据
    console.log('2. 读取测试时间数据：');
    const [selectResult] = await sequelize.query(`
      SELECT id, test_time, created_at, updated_at
      FROM test_time_data 
      ORDER BY id DESC 
      LIMIT 1
    `);

    if (selectResult.length > 0) {
      const record = selectResult[0];
      console.log(`  记录ID: ${record.id}`);
      console.log(`  测试时间: ${record.test_time}`);
      console.log(`  创建时间: ${record.created_at}`);
      console.log(`  更新时间: ${record.updated_at}\n`);

      // 验证时间是否按预期存储
      const storedTime = moment(record.test_time);
      const expectedTime = moment(testTime);
      const timeDiff = Math.abs(storedTime.diff(expectedTime, 'seconds'));

      console.log(`  存储时间与预期时间差异: ${timeDiff}秒`);
      console.log(`  时间存储正确: ${timeDiff === 0}\n`);
    }

    // 清理测试数据
    await sequelize.query(`DROP TABLE IF EXISTS test_time_data`);

    return {
      success: true,
      timeStorageCorrect: selectResult.length > 0,
      storedTime: selectResult.length > 0 ? selectResult[0].test_time : null
    };

  } catch (error) {
    console.error('❌ 时间数据存储测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 主测试函数
 */
async function runDatabaseTimezoneTests() {
  console.log('🧪 方案5.1 数据库时区配置测试');
  console.log('=====================================\n');

  const results = {};

  try {
    // 测试数据库时区配置
    results.databaseTimezone = await testDatabaseTimezone();
    
    // 测试时区刷新功能
    results.timezoneRefresh = await testTimezoneRefresh();
    
    // 测试时间数据存储
    results.timeDataStorage = await testTimeDataStorage();

    // 输出测试总结
    console.log('📋 数据库时区测试结果总结：');
    console.log(`数据库连接: ${results.databaseTimezone.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`时区配置: ${results.databaseTimezone.sessionTimezone || '❌ 未设置'}`);
    console.log(`时区刷新: ${results.timezoneRefresh.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`时间存储: ${results.timeDataStorage.success ? '✅ 通过' : '❌ 失败'}`);
    
    if (results.databaseTimezone.timeDifference !== undefined) {
      console.log(`时间同步: ${results.databaseTimezone.timeDifference < 5 ? '✅ 正常' : '⚠️ 差异较大'}`);
    }

    console.log('\n🎉 数据库时区配置测试完成！');
    
    return results;

  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    return { error: error.message };
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runDatabaseTimezoneTests();
}

module.exports = {
  testDatabaseTimezone,
  testTimezoneRefresh,
  testTimeDataStorage,
  runDatabaseTimezoneTests
};
