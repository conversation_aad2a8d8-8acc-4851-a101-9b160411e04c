const axios = require('axios');

async function registerUser() {
  console.log('开始注册用户...');
  try {
    // 使用正确的API路径
    console.log('发送请求到: http://localhost:3000/api/mobile/auth/register');
    const response = await axios.post('http://localhost:3000/api/mobile/auth/register', {
      username: 'testuser456',  // 更改用户名避免重复
      password: 'password123',
      invite_code: '713B27'
    });

    console.log('注册成功:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('注册失败:');
    if (error.response) {
      // 服务器返回了错误响应
      console.error('状态码:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('没有收到响应，请检查服务器是否运行');
    } else {
      // 设置请求时发生错误
      console.error('请求错误:', error.message);
    }
    console.error('完整错误:', error);
  }
}

// 执行注册
registerUser().then(() => {
  console.log('注册流程完成');
}).catch(err => {
  console.error('未捕获的错误:', err);
});
