/* empty css             *//* empty css                   *//* empty css                      *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";import{d as ce,r as c,a as q,q as L,o as ve,c as ge,b as i,e as t,w as a,m as be,f as _e,i as we,aa as Ue,ab as Ve,ac as ye,p as g,x as Te,j as f,ad as A,n as p,aw as Me,ae as P,aB as H,b8 as Ce,af as N,ag as De,ai as he,aj as xe,ak as ke,V as Se,al as Ee,y as I,an as Ie,ap as Re,E as $e,h as ze,aC as Oe,aq as Fe,at as Ye,aA as je,az as Be,g as K,_ as qe}from"./index-LncY9lAB.js";const Le={class:"invite-rewards-container"},Ae={class:"toolbar"},Pe={class:"toolbar-left"},He={class:"toolbar-right"},Ne={class:"table-wrapper"},Ke={class:"operation-buttons-container"},We={class:"pagination-container"},Ge={class:"form-section"},Je={class:"form-row"},Qe={class:"form-row"},Xe={class:"range-input"},Ze={class:"range-input"},et={class:"form-row"},tt={class:"range-input"},lt={class:"form-row"},at={class:"form-row"},st={class:"filter-footer"},ot={class:"form-section"},nt={class:"form-row"},it={class:"form-row"},rt={class:"form-row"},dt={class:"form-section"},ut={class:"form-row full-width"},pt={class:"dialog-footer"},mt={class:"delete-confirm-text"},ft={class:"dialog-footer"},ct=ce({__name:"index",setup(vt){const x=c(!1),u=c([]),_=c([]),w=c(1),M=c(10),y=c(""),C=c(!1),V=c(!1),D=c(!0),o=q({id:"",title:"",depositUsersMin:null,depositUsersMax:null,registerUsersMin:null,registerUsersMax:null,bonusMin:null,bonusMax:null,status:null,createTimeRange:null,updateTimeRange:null}),n=q({id:void 0,title:"",depositUsers:0,registerUsers:0,bonus:0,status:"active",description:""}),R=()=>{x.value=!0,setTimeout(()=>{u.value=Array.from({length:30},(l,e)=>({id:e+1,title:`Invite ${5*(e+1)} users to invest and reward ${1e4*(e+1)}`,depositUsers:5*(e+1),registerUsers:10*(e+1),bonus:1e4*(e+1),status:e%3===0?"inactive":"active",description:`Detailed description for reward plan ${e+1}`,createTime:new Date(Date.now()-e*864e5).toISOString().replace("T"," ").substring(0,19),updateTime:new Date(Date.now()-e*432e5).toISOString().replace("T"," ").substring(0,19)})),x.value=!1},500)},$=L(()=>{let l=[...u.value];if(y.value.trim()){const e=y.value.toLowerCase();l=l.filter(d=>d.title.toLowerCase().includes(e)||d.id.toString().includes(e))}if(o.id&&(l=l.filter(e=>e.id.toString().includes(o.id))),o.title&&(l=l.filter(e=>e.title.toLowerCase().includes(o.title.toLowerCase()))),o.depositUsersMin!==null&&(l=l.filter(e=>e.depositUsers>=o.depositUsersMin)),o.depositUsersMax!==null&&(l=l.filter(e=>e.depositUsers<=o.depositUsersMax)),o.registerUsersMin!==null&&(l=l.filter(e=>e.registerUsers>=o.registerUsersMin)),o.registerUsersMax!==null&&(l=l.filter(e=>e.registerUsers<=o.registerUsersMax)),o.bonusMin!==null&&(l=l.filter(e=>e.bonus>=o.bonusMin)),o.bonusMax!==null&&(l=l.filter(e=>e.bonus<=o.bonusMax)),o.status&&(l=l.filter(e=>e.status===o.status)),o.createTimeRange&&o.createTimeRange.length===2){const[e,d]=o.createTimeRange;l=l.filter(r=>{const v=new Date(r.createTime).getTime();return v>=new Date(e).getTime()&&v<=new Date(d).getTime()})}if(o.updateTimeRange&&o.updateTimeRange.length===2){const[e,d]=o.updateTimeRange;l=l.filter(r=>{const v=new Date(r.updateTime).getTime();return v>=new Date(e).getTime()&&v<=new Date(d).getTime()})}return l}),W=L(()=>{const l=(w.value-1)*M.value,e=l+M.value;return $.value.slice(l,e)}),G=l=>l.toLocaleString("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}),J=l=>{_.value=l},z=()=>{w.value=1},Q=()=>{y.value.trim()||(y.value="",w.value=1)},X=()=>{C.value=!0},Z=()=>{w.value=1,C.value=!1,g.success("筛选条件已应用")},ee=()=>{Object.keys(o).forEach(l=>{l==="id"||l==="title"?o[l]="":o[l]=null}),w.value=1,g.success("筛选条件已重置")},te=()=>{D.value=!0,Object.assign(n,{id:void 0,title:"",depositUsers:0,registerUsers:0,bonus:0,status:"active",description:""}),V.value=!0},O=l=>{l?(D.value=!1,Object.assign(n,{...l}),V.value=!0):_.value.length===1?(D.value=!1,Object.assign(n,{..._.value[0]}),V.value=!0):g.warning("请选择一条记录进行编辑")},le=async()=>{try{if(await new Promise(l=>setTimeout(l,500)),D.value){const l=Math.max(0,...u.value.map(r=>r.id))+1,e=new Date().toISOString().replace("T"," ").substring(0,19),d={id:l,title:n.title||"",depositUsers:n.depositUsers||0,registerUsers:n.registerUsers||0,bonus:n.bonus||0,status:n.status,description:n.description,createTime:e,updateTime:e};u.value.unshift(d),g.success("添加成功")}else{const l=u.value.findIndex(e=>e.id===n.id);l!==-1&&(u.value[l]={...u.value[l],title:n.title||u.value[l].title,depositUsers:n.depositUsers!==void 0?n.depositUsers:u.value[l].depositUsers,registerUsers:n.registerUsers!==void 0?n.registerUsers:u.value[l].registerUsers,bonus:n.bonus!==void 0?n.bonus:u.value[l].bonus,status:n.status,description:n.description,updateTime:new Date().toISOString().replace("T"," ").substring(0,19)},g.success("更新成功"))}V.value=!1}catch(l){console.error("保存失败:",l),g.error("保存失败")}},T=c(!1),k=c(""),S=c(()=>{}),F=l=>{const e=d=>{u.value=u.value.filter(r=>r.id!==d),g.success("删除成功")};l?(k.value=`确定要删除奖励 "${l.title}" 吗？此操作不可恢复。`,S.value=()=>e(l.id),T.value=!0):_.value.length>0?(k.value=`确定要删除选中的 ${_.value.length} 条记录吗？此操作不可恢复。`,S.value=()=>{const d=_.value.map(r=>r.id);u.value=u.value.filter(r=>!d.includes(r.id)),_.value=[],g.success("批量删除成功")},T.value=!0):g.warning("请选择要删除的记录")},ae=()=>{S.value(),T.value=!1},se=()=>{g.info("导出功能开发中")},oe=()=>{g.info("更多选项功能开发中")},ne=l=>{M.value=l,w.value=1},ie=l=>{w.value=l};return ve(()=>{R()}),(l,e)=>{const d=Te,r=be,v=we,b=Ee,re=Ie,de=Re,ue=Ue,pe=Ve,m=ze,U=Oe,h=Ye,Y=Fe,j=je,B=$e,E=ye,me=ke;return K(),ge("div",Le,[i("div",Ae,[i("div",Pe,[t(r,{class:"toolbar-button",type:"default",onClick:R},{default:a(()=>[t(d,null,{default:a(()=>[t(f(A))]),_:1})]),_:1}),t(r,{class:"toolbar-button",type:"primary",onClick:te},{default:a(()=>[t(d,null,{default:a(()=>[t(f(Me))]),_:1}),e[28]||(e[28]=p("添加 "))]),_:1}),t(r,{class:"toolbar-button",type:"primary",onClick:e[0]||(e[0]=s=>O()),disabled:_.value.length!==1},{default:a(()=>[t(d,null,{default:a(()=>[t(f(P))]),_:1}),e[29]||(e[29]=p("编辑 "))]),_:1},8,["disabled"]),t(r,{class:"toolbar-button",type:"danger",onClick:e[1]||(e[1]=s=>F()),disabled:_.value.length===0},{default:a(()=>[t(d,null,{default:a(()=>[t(f(H))]),_:1}),e[30]||(e[30]=p("删除 "))]),_:1},8,["disabled"]),t(r,{class:"toolbar-button",type:"default",onClick:oe},{default:a(()=>[t(d,null,{default:a(()=>[t(f(Ce))]),_:1}),e[31]||(e[31]=p("更多 "))]),_:1})]),i("div",He,[t(v,{modelValue:y.value,"onUpdate:modelValue":e[2]||(e[2]=s=>y.value=s),placeholder:"搜索奖励标题",class:"search-input",onKeyup:_e(z,["enter"]),onBlur:Q},null,8,["modelValue"]),t(r,{class:"search-button",type:"primary",onClick:z},{default:a(()=>[t(d,null,{default:a(()=>[t(f(N))]),_:1})]),_:1}),t(r,{class:"filter-button",type:"default",onClick:X},{default:a(()=>[t(d,null,{default:a(()=>[t(f(De))]),_:1}),e[32]||(e[32]=p("筛选 "))]),_:1}),t(r,{class:"export-button",type:"default",onClick:se},{default:a(()=>[t(d,null,{default:a(()=>[t(f(he))]),_:1}),e[33]||(e[33]=p("导出 "))]),_:1})])]),t(ue,{class:"table-card",shadow:"never"},{default:a(()=>[i("div",Ne,[xe((K(),Se(de,{ref:"rewardsTable",data:W.value,border:"",stripe:"","highlight-current-row":"","row-key":"id",style:{width:"100%"},onSelectionChange:J,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:a(()=>[t(b,{type:"selection",width:"50",align:"center",fixed:"left"}),t(b,{prop:"id",label:"ID","min-width":"70",align:"center",fixed:"left"}),t(b,{prop:"title",label:"标题","min-width":"200",align:"center","show-overflow-tooltip":""}),t(b,{prop:"depositUsers",label:"邀请充值人数","min-width":"120",align:"center"}),t(b,{prop:"registerUsers",label:"邀请注册人数","min-width":"120",align:"center"}),t(b,{prop:"bonus",label:"奖金","min-width":"100",align:"center"},{default:a(s=>[p(I(G(s.row.bonus)),1)]),_:1}),t(b,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:a(s=>[t(re,{type:s.row.status==="active"?"success":"info"},{default:a(()=>[p(I(s.row.status==="active"?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(b,{prop:"createTime",label:"创建时间","min-width":"160",align:"center",sortable:""}),t(b,{prop:"updateTime",label:"更新时间","min-width":"160",align:"center",sortable:""}),t(b,{label:"操作","min-width":"120",align:"center",fixed:"right"},{default:a(s=>[i("div",Ke,[t(r,{class:"operation-button icon-only",size:"small",type:"primary",onClick:fe=>O(s.row)},{default:a(()=>[t(d,null,{default:a(()=>[t(f(P))]),_:1})]),_:2},1032,["onClick"]),t(r,{class:"operation-button icon-only",size:"small",type:"danger",onClick:fe=>F(s.row)},{default:a(()=>[t(d,null,{default:a(()=>[t(f(H))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[me,x.value]])])]),_:1}),i("div",We,[t(pe,{"current-page":w.value,"onUpdate:currentPage":e[3]||(e[3]=s=>w.value=s),"page-size":M.value,"onUpdate:pageSize":e[4]||(e[4]=s=>M.value=s),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:$.value.length,onSizeChange:ne,onCurrentChange:ie},null,8,["current-page","page-size","total"])]),t(E,{modelValue:C.value,"onUpdate:modelValue":e[17]||(e[17]=s=>C.value=s),title:"筛选条件",width:"500px",center:"",top:"5vh",class:"filter-dialog"},{footer:a(()=>[i("div",st,[t(r,{class:"filter-button",type:"primary",onClick:Z},{default:a(()=>[t(d,null,{default:a(()=>[t(f(N))]),_:1}),e[37]||(e[37]=p("搜索 "))]),_:1}),t(r,{class:"filter-button",type:"default",onClick:ee},{default:a(()=>[t(d,null,{default:a(()=>[t(f(A))]),_:1}),e[38]||(e[38]=p("重置 "))]),_:1}),t(r,{class:"filter-button",type:"default",onClick:e[16]||(e[16]=s=>C.value=!1)},{default:a(()=>[t(d,null,{default:a(()=>[t(f(Be))]),_:1}),e[39]||(e[39]=p("取消 "))]),_:1})])]),default:a(()=>[t(B,{model:o,"label-position":"top","label-width":"80px"},{default:a(()=>[i("div",Ge,[i("div",Je,[t(m,{label:"ID"},{default:a(()=>[t(v,{modelValue:o.id,"onUpdate:modelValue":e[5]||(e[5]=s=>o.id=s),placeholder:"请输入ID"},null,8,["modelValue"])]),_:1}),t(m,{label:"标题"},{default:a(()=>[t(v,{modelValue:o.title,"onUpdate:modelValue":e[6]||(e[6]=s=>o.title=s),placeholder:"请输入标题关键词"},null,8,["modelValue"])]),_:1})]),i("div",Qe,[t(m,{label:"邀请充值人数"},{default:a(()=>[i("div",Xe,[t(U,{modelValue:o.depositUsersMin,"onUpdate:modelValue":e[7]||(e[7]=s=>o.depositUsersMin=s),min:0,placeholder:"最小值"},null,8,["modelValue"]),e[34]||(e[34]=i("span",{class:"range-separator"},"-",-1)),t(U,{modelValue:o.depositUsersMax,"onUpdate:modelValue":e[8]||(e[8]=s=>o.depositUsersMax=s),min:0,placeholder:"最大值"},null,8,["modelValue"])])]),_:1}),t(m,{label:"邀请注册人数"},{default:a(()=>[i("div",Ze,[t(U,{modelValue:o.registerUsersMin,"onUpdate:modelValue":e[9]||(e[9]=s=>o.registerUsersMin=s),min:0,placeholder:"最小值"},null,8,["modelValue"]),e[35]||(e[35]=i("span",{class:"range-separator"},"-",-1)),t(U,{modelValue:o.registerUsersMax,"onUpdate:modelValue":e[10]||(e[10]=s=>o.registerUsersMax=s),min:0,placeholder:"最大值"},null,8,["modelValue"])])]),_:1})]),i("div",et,[t(m,{label:"奖金"},{default:a(()=>[i("div",tt,[t(U,{modelValue:o.bonusMin,"onUpdate:modelValue":e[11]||(e[11]=s=>o.bonusMin=s),min:0,placeholder:"最小值"},null,8,["modelValue"]),e[36]||(e[36]=i("span",{class:"range-separator"},"-",-1)),t(U,{modelValue:o.bonusMax,"onUpdate:modelValue":e[12]||(e[12]=s=>o.bonusMax=s),min:0,placeholder:"最大值"},null,8,["modelValue"])])]),_:1}),t(m,{label:"状态"},{default:a(()=>[t(Y,{modelValue:o.status,"onUpdate:modelValue":e[13]||(e[13]=s=>o.status=s),placeholder:"请选择状态",clearable:"",style:{width:"100%"}},{default:a(()=>[t(h,{label:"启用",value:"active"}),t(h,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1})]),i("div",lt,[t(m,{label:"创建时间"},{default:a(()=>[t(j,{modelValue:o.createTimeRange,"onUpdate:modelValue":e[14]||(e[14]=s=>o.createTimeRange=s),type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),i("div",at,[t(m,{label:"更新时间"},{default:a(()=>[t(j,{modelValue:o.updateTimeRange,"onUpdate:modelValue":e[15]||(e[15]=s=>o.updateTimeRange=s),type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(E,{modelValue:V.value,"onUpdate:modelValue":e[25]||(e[25]=s=>V.value=s),title:D.value?"添加邀请奖励":"编辑邀请奖励",width:"600px",center:"",top:"5vh",class:"edit-dialog"},{footer:a(()=>[i("div",pt,[t(r,{type:"primary",onClick:le},{default:a(()=>e[42]||(e[42]=[p("确定")])),_:1}),t(r,{onClick:e[24]||(e[24]=s=>V.value=!1)},{default:a(()=>e[43]||(e[43]=[p("取消")])),_:1})])]),default:a(()=>[t(B,{model:n,"label-position":"top","label-width":"100px"},{default:a(()=>[i("div",ot,[e[40]||(e[40]=i("h3",{class:"form-section-title"},"基本信息",-1)),i("div",nt,[t(m,{label:"标题",required:""},{default:a(()=>[t(v,{modelValue:n.title,"onUpdate:modelValue":e[18]||(e[18]=s=>n.title=s),placeholder:"请输入奖励标题"},null,8,["modelValue"])]),_:1})]),i("div",it,[t(m,{label:"邀请充值人数",required:""},{default:a(()=>[t(U,{modelValue:n.depositUsers,"onUpdate:modelValue":e[19]||(e[19]=s=>n.depositUsers=s),min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(m,{label:"邀请注册人数",required:""},{default:a(()=>[t(U,{modelValue:n.registerUsers,"onUpdate:modelValue":e[20]||(e[20]=s=>n.registerUsers=s),min:0,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),i("div",rt,[t(m,{label:"奖金",required:""},{default:a(()=>[t(U,{modelValue:n.bonus,"onUpdate:modelValue":e[21]||(e[21]=s=>n.bonus=s),min:0,step:1e3,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(m,{label:"状态"},{default:a(()=>[t(Y,{modelValue:n.status,"onUpdate:modelValue":e[22]||(e[22]=s=>n.status=s),placeholder:"请选择状态",style:{width:"100%"}},{default:a(()=>[t(h,{label:"启用",value:"active"}),t(h,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"])]),_:1})])]),i("div",dt,[e[41]||(e[41]=i("h3",{class:"form-section-title"},"详细描述",-1)),i("div",ut,[t(m,{label:"描述"},{default:a(()=>[t(v,{modelValue:n.description,"onUpdate:modelValue":e[23]||(e[23]=s=>n.description=s),type:"textarea",rows:5,placeholder:"请输入奖励描述"},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(E,{modelValue:T.value,"onUpdate:modelValue":e[27]||(e[27]=s=>T.value=s),title:"确认删除",width:"400px",center:""},{footer:a(()=>[i("span",ft,[t(r,{type:"danger",onClick:ae},{default:a(()=>e[44]||(e[44]=[p("确定删除")])),_:1}),t(r,{onClick:e[26]||(e[26]=s=>T.value=!1)},{default:a(()=>e[45]||(e[45]=[p("取消")])),_:1})])]),default:a(()=>[i("p",mt,I(k.value),1)]),_:1},8,["modelValue"])])}}}),It=qe(ct,[["__scopeId","data-v-ea6140b4"]]);export{It as default};
