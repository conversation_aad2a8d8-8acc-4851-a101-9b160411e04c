/**
 * 用户端轮播图路由
 */
const express = require('express');
const router = express.Router();
const mobileBannerController = require('../controllers/mobileBannerController');

/**
 * @swagger
 * /api/mobile/banners:
 *   get:
 *     summary: 获取轮播图列表（用户端）
 *     tags: [用户端]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: boolean
 *           default: true
 *         description: 状态筛选，默认只获取启用状态的轮播图
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/', mobileBannerController.getBanners);

/**
 * @swagger
 * /api/mobile/banners/{id}:
 *   get:
 *     summary: 获取轮播图详情（用户端）
 *     tags: [用户端]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 轮播图ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 轮播图不存在
 */
router.get('/:id', mobileBannerController.getBannerById);

module.exports = router;
