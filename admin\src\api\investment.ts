import request from '@/utils/request'

/**
 * 获取投资列表
 * @param params 查询参数
 * @returns 投资列表
 */
export function getInvestments(params: any) {
  // 如果指定了用户ID，则使用获取用户投资的接口
  if (params.user_id) {
    return request({
      url: `/api/admin/users/${params.user_id}/investments`,
      method: 'get',
      params: {
        page: params.page,
        limit: params.limit,
        status: params.status
      }
    })
  } else {
    // 使用获取所有投资的接口
    return request({
      url: '/api/admin/investments',
      method: 'get',
      params
    })
  }
}

/**
 * 获取投资详情
 * @param id 投资ID
 * @returns 投资详情
 */
export function getInvestment(id: number) {
  return request({
    url: `/api/admin/investments/${id}`,
    method: 'get'
  })
}

/**
 * 删除投资
 * @param id 投资ID
 * @returns 删除结果
 */
export function deleteInvestment(id: number) {
  return request({
    url: `/api/admin/investments/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除投资
 * @param ids 投资ID数组
 * @returns 删除结果
 */
export function batchDeleteInvestments(ids: number[]) {
  return request({
    url: '/api/admin/investments/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 获取投资收益记录
 * @param id 投资ID
 * @param params 查询参数，包括分页信息
 * @returns 收益记录
 */
export function getInvestmentProfits(id: number, params?: any) {
  return request({
    url: `/api/admin/investments/${id}/profits`,
    method: 'get',
    params
  })
}
