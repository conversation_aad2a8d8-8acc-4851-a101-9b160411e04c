const express = require('express');
const roleController = require('../controllers/roleController');
const { verifyAdminToken, checkAdminRole } = require('../middlewares/authMiddleware');

const router = express.Router();

// 所有路由都需要管理员认证
router.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/roles:
 *   get:
 *     summary: 获取角色列表
 *     tags: [角色管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: boolean
 *         description: 状态筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/', checkAdminRole(['super']), roleController.getRoles);

/**
 * @swagger
 * /api/admin/roles/{id}:
 *   get:
 *     summary: 获取角色详情
 *     tags: [角色管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 角色ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 角色不存在
 */
router.get('/:id', checkAdminRole(['super']), roleController.getRole);

/**
 * @swagger
 * /api/admin/roles:
 *   post:
 *     summary: 创建角色
 *     tags: [角色管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               parent_id:
 *                 type: integer
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: integer
 *               status:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 *       409:
 *         description: 角色名称已存在
 */
router.post('/', checkAdminRole(['super']), roleController.createRole);

/**
 * @swagger
 * /api/admin/roles/{id}:
 *   put:
 *     summary: 更新角色
 *     tags: [角色管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 角色ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               parent_id:
 *                 type: integer
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: integer
 *               status:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 角色不存在
 *       409:
 *         description: 角色名称已存在
 */
router.put('/:id', checkAdminRole(['super']), roleController.updateRole);

/**
 * @swagger
 * /api/admin/roles/{id}:
 *   delete:
 *     summary: 删除角色
 *     tags: [角色管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 角色ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 角色不存在
 */
router.delete('/:id', checkAdminRole(['super']), roleController.deleteRole);

/**
 * @swagger
 * /api/admin/permissions:
 *   get:
 *     summary: 获取所有权限
 *     tags: [权限管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/permissions/all', checkAdminRole(['super']), roleController.getAllPermissions);

module.exports = router;
