/**
 * 全局Toast消息服务
 * 提供美化的消息提示功能
 */

class ToastService {
  constructor() {
    this.toastComponent = null;
  }

  // 设置Toast组件实例
  setToastComponent(component) {
    this.toastComponent = component;
  }

  // 显示成功消息
  success(message, title = 'Success', duration = 4000) {
    this.show({
      type: 'success',
      title,
      message,
      duration
    });
  }

  // 显示错误消息
  error(message, title = 'Error', duration = 5000) {
    this.show({
      type: 'error',
      title,
      message,
      duration
    });
  }

  // 显示警告消息
  warning(message, title = 'Warning', duration = 4500) {
    this.show({
      type: 'warning',
      title,
      message,
      duration
    });
  }

  // 显示信息消息
  info(message, title = 'Info', duration = 4000) {
    this.show({
      type: 'info',
      title,
      message,
      duration
    });
  }

  // 显示余额不足消息
  insufficientBalance(message = 'Your balance is insufficient to complete this purchase', title = 'Insufficient Balance', duration = 5000) {
    this.show({
      type: 'insufficient',
      title,
      message,
      duration
    });
  }

  // 显示购买成功消息
  purchaseSuccess(productName, amount) {
    this.success(
      `You have successfully purchased ${productName} for ₱${amount}`,
      'Purchase Successful',
      4000
    );
  }

  // 显示充值成功消息
  topUpSuccess(amount) {
    this.success(
      `₱${amount} has been added to your account`,
      'Top Up Successful',
      4000
    );
  }

  // 显示网络错误消息
  networkError(message = 'Please check your network connection and try again') {
    this.error(
      message,
      'Network Error',
      5000
    );
  }

  // 显示服务器错误消息
  serverError(message = 'Server is temporarily unavailable, please try again later') {
    this.error(
      message,
      'Server Error',
      5000
    );
  }

  // 显示验证错误消息
  validationError(message) {
    this.warning(
      message,
      'Validation Error',
      4000
    );
  }

  // 显示加载中消息（不自动隐藏）
  loading(message = 'Loading...', title = 'Please Wait') {
    this.show({
      type: 'info',
      title,
      message,
      duration: 0 // 不自动隐藏
    });
  }

  // 通用显示方法
  show(options) {
    if (this.toastComponent) {
      this.toastComponent.show(options);
    } else {
      // 如果没有自定义组件，回退到系统Toast
      uni.showToast({
        title: options.message || options.title,
        icon: this.mapTypeToIcon(options.type),
        duration: options.duration || 3000
      });
    }
  }

  // 隐藏Toast
  hide() {
    if (this.toastComponent) {
      this.toastComponent.hide();
    } else {
      uni.hideToast();
    }
  }

  // 映射类型到系统图标
  mapTypeToIcon(type) {
    const iconMap = {
      'success': 'success',
      'error': 'error',
      'warning': 'none',
      'info': 'none',
      'insufficient': 'none'
    };
    return iconMap[type] || 'none';
  }
}

// 创建全局实例
const toast = new ToastService();

// 导出实例和类
export default toast;
export { ToastService };

// 全局方法（可选，用于向后兼容）
export const showToast = {
  success: (message, title, duration) => toast.success(message, title, duration),
  error: (message, title, duration) => toast.error(message, title, duration),
  warning: (message, title, duration) => toast.warning(message, title, duration),
  info: (message, title, duration) => toast.info(message, title, duration),
  insufficientBalance: (message, title, duration) => toast.insufficientBalance(message, title, duration),
  purchaseSuccess: (productName, amount) => toast.purchaseSuccess(productName, amount),
  topUpSuccess: (amount) => toast.topUpSuccess(amount),
  networkError: (message) => toast.networkError(message),
  serverError: (message) => toast.serverError(message),
  validationError: (message) => toast.validationError(message),
  loading: (message, title) => toast.loading(message, title),
  hide: () => toast.hide()
};
