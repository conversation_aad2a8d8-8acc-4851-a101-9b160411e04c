{"version": 3, "sources": ["../../src/utils/join-sql-fragments.ts"], "sourcesContent": ["import { SQLFragment, TruthySQLFragment } from '../generic/sql-fragment';\n\nfunction doesNotWantLeadingSpace(str: string): boolean {\n  return /^[;,)]/.test(str);\n}\nfunction doesNotWantTrailingSpace(str: string): boolean {\n  return /\\($/.test(str);\n}\n\n/**\n * Joins an array of strings with a single space between them,\n * except for:\n *\n * - Strings starting with ';', ',' and ')', which do not get a leading space.\n * - Strings ending with '(', which do not get a trailing space.\n *\n * @param {string[]} parts\n * @returns {string}\n * @private\n */\nfunction singleSpaceJoinHelper(parts: string[]): string {\n  return parts.reduce(\n    ({ skipNextLeadingSpace, result }, part) => {\n      if (skipNextLeadingSpace || doesNotWantLeadingSpace(part)) {\n        result += part.trim();\n      } else {\n        result += ` ${part.trim()}`;\n      }\n      return {\n        skipNextLeadingSpace: doesNotWantTrailingSpace(part),\n        result\n      };\n    },\n    {\n      skipNextLeadingSpace: true,\n      result: ''\n    }\n  ).result;\n}\n\n/**\n * Joins an array with a single space, auto trimming when needed.\n *\n * Certain elements do not get leading/trailing spaces.\n *\n * @param {SQLFragment[]} array The array to be joined. Falsy values are skipped. If an\n * element is another array, this function will be called recursively on that array.\n * Otherwise, if a non-string, non-falsy value is present, a TypeError will be thrown.\n *\n * @returns {string} The joined string.\n *\n * @private\n */\nexport function joinSQLFragments(array: SQLFragment[]): string {\n  if (array.length === 0) return '';\n\n  const truthyArray: TruthySQLFragment[] = array.filter(\n    (x): x is string | SQLFragment[] => !!x\n  );\n  const flattenedArray: string[] = truthyArray.map(\n    (fragment: TruthySQLFragment) => {\n      if (Array.isArray(fragment)) {\n        return joinSQLFragments(fragment);\n      }\n\n      return fragment;\n    }\n  );\n\n  // Ensure strings\n  for (const fragment of flattenedArray) {\n    if (fragment && typeof fragment !== 'string') {\n      throw new JoinSQLFragmentsError(\n        flattenedArray,\n        fragment,\n        `Tried to construct a SQL string with a non-string, non-falsy fragment (${fragment}).`\n      );\n    }\n  }\n\n  // Trim fragments\n  const trimmedArray = flattenedArray.map(x => x.trim());\n\n  // Skip full-whitespace fragments (empty after the above trim)\n  const nonEmptyStringArray = trimmedArray.filter(x => x !== '');\n\n  return singleSpaceJoinHelper(nonEmptyStringArray);\n}\n\nexport class JoinSQLFragmentsError extends TypeError {\n  args: SQLFragment[];\n  fragment: any; // iirc this error is only used when we get an invalid fragment.\n\n  constructor(args: SQLFragment[], fragment: any, message: string) {\n    super(message);\n    \n    this.args = args;\n    this.fragment = fragment;\n    this.name = 'JoinSQLFragmentsError';\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAEA,iCAAiC,KAAsB;AACrD,SAAO,SAAS,KAAK;AAAA;AAEvB,kCAAkC,KAAsB;AACtD,SAAO,MAAM,KAAK;AAAA;AAcpB,+BAA+B,OAAyB;AACtD,SAAO,MAAM,OACX,CAAC,EAAE,sBAAsB,UAAU,SAAS;AAC1C,QAAI,wBAAwB,wBAAwB,OAAO;AACzD,gBAAU,KAAK;AAAA,WACV;AACL,gBAAU,IAAI,KAAK;AAAA;AAErB,WAAO;AAAA,MACL,sBAAsB,yBAAyB;AAAA,MAC/C;AAAA;AAAA,KAGJ;AAAA,IACE,sBAAsB;AAAA,IACtB,QAAQ;AAAA,KAEV;AAAA;AAgBG,0BAA0B,OAA8B;AAC7D,MAAI,MAAM,WAAW;AAAG,WAAO;AAE/B,QAAM,cAAmC,MAAM,OAC7C,CAAC,MAAmC,CAAC,CAAC;AAExC,QAAM,iBAA2B,YAAY,IAC3C,CAAC,aAAgC;AAC/B,QAAI,MAAM,QAAQ,WAAW;AAC3B,aAAO,iBAAiB;AAAA;AAG1B,WAAO;AAAA;AAKX,aAAW,YAAY,gBAAgB;AACrC,QAAI,YAAY,OAAO,aAAa,UAAU;AAC5C,YAAM,IAAI,sBACR,gBACA,UACA,0EAA0E;AAAA;AAAA;AAMhF,QAAM,eAAe,eAAe,IAAI,OAAK,EAAE;AAG/C,QAAM,sBAAsB,aAAa,OAAO,OAAK,MAAM;AAE3D,SAAO,sBAAsB;AAAA;AAGxB,oCAAoC,UAAU;AAAA,EAInD,YAAY,MAAqB,UAAe,SAAiB;AAC/D,UAAM;AAJR;AACA;AAKE,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA;AAAA;", "names": []}