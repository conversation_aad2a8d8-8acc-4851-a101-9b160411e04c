# 后台管理系统开发进度文档

## 最近更新

### 2024-06-16
- 优化管理端首页(dashboard)中的系统时间显示
  - 将"服务器当前时间"改为"系统时间"
  - 确保显示的时间使用系统参数设置的时区
  - 实现系统时间的动态更新，每秒刷新一次
  - 计算系统时区与本地时区的时间偏移量，确保准确性
  - 在服务端响应中添加时区信息，便于前端处理
- 修复管理端交易记录类型显示问题
  - 更新交易类型显示，将"投资"改为"购买"，将"赠金"和"赠投"改为"系统赠送"
  - 修改类型筛选下拉菜单，添加"系统赠送"选项
  - 更新类型标签样式，使"系统赠送"使用金色标签
  - 优化类型筛选逻辑，支持"系统赠送"类型筛选
  - 确保高级筛选中的类型映射与显示一致
  - 添加相关文档说明
- 修复交易记录中的金额计算逻辑
  - 修改赠投（系统赠送）类型的交易记录处理，将金额设为0，确保交易前后余额相同
  - 确保扣除类型的交易金额为负数，正确显示在交易记录中
  - 优化交易记录创建函数，确保交易前后余额正确计算

## 项目概述
本文档记录后台管理系统的开发进度，包括已完成的功能、正在开发的功能以及待开发的功能。

## 已完成功能

### 会员列表页面
- [x] 基础表格展示
- [x] 搜索功能（支持按用户名搜索）
- [x] 筛选功能（支持多种筛选条件）
- [x] 编辑会员信息功能
- [x] 查看下级会员功能
- [x] 赠金功能
- [x] 赠投功能
- [x] 无分页模式（一次性加载所有数据）

#### 会员列表页面优化
- [x] 优化表格列的显示，只保留最重要的信息
- [x] 优化编辑弹窗样式，增加分区和标题
- [x] 优化筛选弹窗布局，使用分区展示所有筛选条件
- [x] 增加筛选条件：积分、收入账户(法币)、充值账户(法币)、总返佣、一级返佣、二级返佣、三级返佣
- [x] 修改下级相关字段名称为更简洁的形式
- [x] 时间选择器支持精确到秒
- [x] 修复搜索功能，确保搜索结果正确展示
- [x] 筛选弹窗增加取消按钮

### 充值订单页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按用户名和订单号搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
  - [x] 添加重置按钮，可一键清除搜索条件
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、金额信息、状态信息、时间信息）
  - [x] 支持多种筛选条件：ID、用户名、项目名称、数量范围、订单金额范围等
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 分页功能
  - [x] 默认每页显示10条记录
  - [x] 支持切换每页显示记录数（10条、20条、30条）
  - [x] 分页与搜索、筛选功能协同工作
- [x] 编辑订单功能
  - [x] 编辑弹窗中设置部分字段为只读（订单金额、实付金额、支付通道等）
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 移除收款账号、上传凭证、收款凭证字段
- [x] 删除订单功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框
  - [x] 删除成功后自动刷新数据
- [x] 手动支付功能
- [x] 数据模拟
  - [x] 实现随机生成订单数据的功能
  - [x] 生成的数据包含多种订单状态和支付方式
  - [x] 支持刷新按钮，点击后重新生成随机数据
- [x] 导出功能（按钮已添加，功能待实现）

### 取款记录页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按用户名搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、金额信息、状态信息、时间信息）
  - [x] 支持多种筛选条件：ID、用户ID、用户名、银行卡号、银行编码、订单流水号、支付通道、支付平台订单号等
  - [x] 金额字段使用范围输入（最小值至最大值），包括取款金额、到账金额、手续费
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 分页功能
  - [x] 默认每页显示10条记录
  - [x] 支持切换每页显示记录数（10条、20条、50条、100条）
  - [x] 分页与搜索、筛选功能协同工作
- [x] 数据模拟
  - [x] 实现随机生成取款记录数据的功能
  - [x] 生成的数据包含多种取款状态和类型
  - [x] 生成的数据包含合理的金额和时间

## 正在开发的功能

### 用户投资页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按用户ID搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、金额信息、周期信息、时间信息）
  - [x] 支持多种筛选条件：ID、用户ID、用户名、项目名称、数量范围等
  - [x] 数量字段使用范围输入（最小值至最大值）
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 分页功能
  - [x] 默认每页显示10条记录
  - [x] 支持切换每页显示记录数（10条、20条、50条、100条）
  - [x] 分页与搜索、筛选功能协同工作
- [x] 删除功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认按钮在左边，取消按钮在右边
  - [x] 完成状态的投资不能删除，并提示“该笔订单已锁定，不能执行删除操作”
  - [x] 删除成功后自动刷新数据
- [x] 数据模拟
  - [x] 实现随机生成投资数据的功能
  - [x] 生成的数据包含多种投资状态和类型
  - [x] 生成的数据包含合理的收益次数和收益时间

### 用户流水页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按用户名搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、金额信息、时间信息）
  - [x] 支持多种筛选条件：ID、用户ID、用户名、类型、变更前余额、变更后余额、业务对象、备注等
  - [x] 金额字段使用范围输入（最小值至最大值）
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 分页功能
  - [x] 默认每页显示10条记录
  - [x] 支持切换每页显示记录数（10条、20条、50条、100条）
  - [x] 分页与搜索、筛选功能协同工作
  - [x] 分页设置保存到本地存储，下次访问时自动使用
- [x] 数据模拟
  - [x] 实现随机生成流水记录数据的功能
  - [x] 生成的数据包含多种流水类型和状态
  - [x] 生成的数据包含合理的金额和时间

### 佣金记录页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按用户名搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、金额信息、时间信息）
  - [x] 支持多种筛选条件：ID、用户ID、用户名、来源用户ID、来源用户名、佣金类型等
  - [x] 金额字段使用范围输入（最小值至最大值）
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 分页功能
  - [x] 默认每页显示10条记录
  - [x] 支持切换每页显示记录数（10条、20条、50条、100条）
  - [x] 分页与搜索、筛选功能协同工作
  - [x] 分页设置保存到本地存储，下次访问时自动使用
- [x] 数据模拟
  - [x] 实现随机生成佣金记录数据的功能
  - [x] 生成的数据包含多种佣金类型
  - [x] 生成的数据包含合理的金额和时间

### 用户银行卡页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按用户ID/用户名搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、银行卡信息、时间信息）
  - [x] 支持多种筛选条件：ID、用户ID、用户名、银行名称、银行卡号、持卡人姓名等
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 编辑功能
  - [x] 实现分区式编辑弹窗（基本信息、银行卡信息）
  - [x] 用户名字段设置为只读状态
  - [x] 优化编辑弹窗样式，增加分区和标题
- [x] 删除功能
  - [x] 实现单条删除和批量删除功能
  - [x] 优化删除确认弹窗，增加警告图标和警告文本
- [x] 分页功能
  - [x] 默认每页显示10条记录
  - [x] 支持切换每页显示记录数（10条、20条、50条、100条）
  - [x] 分页与搜索、筛选功能协同工作
  - [x] 分页设置保存到本地存储，下次访问时自动使用
- [x] 优化
  - [x] 移除证件号码字段，简化表格展示

### 投资项目页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按项目名称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、价格信息、收益设置、数量设置、VIP设置、状态设置、时间范围）
  - [x] 支持多种筛选条件：ID、类型、分类、名称、是否有视频等
  - [x] 数值字段使用范围输入（最小值至最大值）
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 添加功能
  - [x] 实现分区式添加弹窗（基本信息、价格信息、收益设置、数量设置、VIP设置、状态设置）
  - [x] 支持上传项目图片和视频
  - [x] 添加成功后自动刷新数据
- [x] 编辑功能
  - [x] 实现分区式编辑弹窗
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 删除功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认删除按钮在左边，取消按钮在右边
  - [x] 删除成功后自动刷新数据
- [x] 分页功能
  - [x] 默认每页显示10条记录
  - [x] 支持切换每页显示记录数（10条、20条、30条）
  - [x] 分页与搜索、筛选功能协同工作
- [x] 数据模拟
  - [x] 实现随机生成投资项目数据的功能
  - [x] 生成的数据包含多种项目类型和状态
  - [x] 生成的数据包含合理的价格和收益设置
- [x] 每周收益时间功能
  - [x] 将原有的"结算时间"功能修改为"每周收益时间"
  - [x] 支持选择周一至周日中的任意几天作为产品的收益日
  - [x] 提供预设选项：工作日（周一至周五）、周末（周六和周日）、全周（周一至周日）
  - [x] 在筛选条件中添加每周收益日的筛选选项
  - [x] 删除原有的settlement_type字段，使用weekly_profit_days字段替代
- [x] 自定义收益率功能
  - [x] 添加自定义收益率开关，可以启用或禁用自定义收益率
  - [x] 添加自定义结算时间（小时）和自定义收益率（%）设置
  - [x] 当自定义收益率启用时，使用自定义设置而非标准收益设置
  - [x] 自定义收益率仅在每周收益日内生效
  - [x] 在筛选条件中添加自定义收益率的筛选选项

### 系统精简
- [x] 删除幸运转盘页面
- [x] 删除每日抽奖页面
- [x] 删除兑换码页面
- [x] 删除通知消息管理页面
- [x] 删除代理商管理页面
- [x] 从路由配置中移除相关页面
- [x] 删除相关数据库表
- [x] 从侧边栏菜单中移除相关页面链接
- [x] 移除不再需要的图标导入

### 侧边栏菜单优化
- [x] 为系统设置下的所有二级菜单项添加图标
  - [x] 投资项目：Document图标
  - [x] 参数设置：Setting图标
  - [x] 附件管理：Picture图标
  - [x] 个人资料：Avatar图标
  - [x] 用户级别：Medal图标
  - [x] 支付通道：Connection图标
  - [x] 邀请奖励：PriceTag图标
  - [x] 客服管理：Service图标
  - [x] 谷歌验证码：Key图标
  - [x] 轮播图：Collection图标
- [x] 为权限管理下的所有二级菜单项添加图标
  - [x] 管理员设置：User图标
  - [x] 角色组：Tickets图标
- [x] 优化自定义菜单项样式，使图标与文字对齐
- [x] 导入并使用Element Plus提供的图标库
- [x] 为二级菜单添加高亮功能
  - [x] 根据当前路由路径自动高亮对应的菜单项
  - [x] 添加高亮样式（背景色、文字颜色和字体加粗）
  - [x] 添加平滑过渡动画效果
  - [x] 使用Vue的动态类绑定实现高亮状态切换
- [x] 为特定菜单项添加禁用功能
  - [x] 将个人资料菜单项设置为禁用状态
  - [x] 将用户级别菜单项设置为禁用状态
  - [x] 将邀请奖励菜单项设置为禁用状态
  - [x] 参考谷歌验证码菜单项的实现方式
  - [x] 禁用菜单项显示为灰色，鼠标悬停时显示禁止图标
  - [x] 移除禁用菜单项的点击事件和高亮功能

### 参数设置页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
- [x] 搜索功能
  - [x] 支持按参数名称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 编辑功能
  - [x] 实现编辑弹窗
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 分类管理
  - [x] 支持按分类展示参数
  - [x] 支持分类切换
  - [x] 支持分类内参数编辑
- [x] 数据模拟
  - [x] 实现随机生成参数数据的功能
  - [x] 生成的数据包含多种参数类型
  - [x] 生成的数据包含合理的参数值和描述
- [x] 会员配置面板
  - [x] 添加超级登录密码设置项
  - [x] 将参数键设置为 `[site.super_login_password]`
  - [x] 实现保存和获取超级登录密码的功能
  - [x] 确保密码值安全存储在数据库中
  - [x] 将超级登录密码的默认值设为空字符串，需要管理员手动设置
  - [x] 添加注册奖励类型和金额设置项
  - [x] 将注册奖励类型参数键设置为 `[site.reg_bonus_type]`
  - [x] 将注册奖励金额参数键设置为 `[site.reg_bonus_amount]`
  - [x] 支持选择将奖励发放到收入账户或充值账户
  - [x] 实现保存和获取注册奖励设置的功能
- [x] 资金配置页面优化
  - [x] 删除分组标签页，保留充值返佣比例设置和收益返佣比例设置的分组标题
  - [x] 修改多个配置项的标题，使其更符合业务需求
  - [x] 删除不必要的配置项，简化页面
  - [x] 将取款阶梯手续费改为表格形式，支持添加多种手续费设置
  - [x] 添加表格间隔行高亮显示，提高可读性
  - [x] 优化表格样式，使其更加美观易用
  - [x] 删除1级取款奖励(%)字段
  - [x] 删除2级取款奖励(%)字段
  - [x] 删除3级取款奖励(%)字段
  - [x] 确认前端没有与这些字段相关的API请求
  - [x] 确认后端没有专门处理这些字段的控制器方法
  - [x] 确认数据库中没有与这些字段相关的记录
  - [x] 删除每日抽奖收益率(%)字段
  - [x] 删除每日抽奖时间字段
  - [x] 删除每日抽奖类型字段
  - [x] 删除幸运转盘单次消耗金额字段
  - [x] 删除幸运转盘单次需要人数字段
  - [x] 删除幸运转盘最多次数字段
  - [x] 删除幸运转盘类型字段
  - [x] 确认前端没有与这些字段相关的API请求
  - [x] 确认后端没有专门处理这些字段的控制器方法
  - [x] 确认数据库中没有与这些字段相关的表和记录
  - [x] 删除签到奖励字段
  - [x] 删除充值用户才能取款字段
  - [x] 删除已投资用户才能取款字段
  - [x] 删除有未完成投资才能取款字段
  - [x] 删除有直属下级充值才能取款字段
  - [x] 确认前端没有与这些字段相关的API请求
  - [x] 确认后端没有专门处理这些字段的控制器方法
  - [x] 确认数据库中没有与这些字段相关的表和记录

## 待开发功能

### API文档完善
- [x] VIP等级和类型API文档
  - [x] 添加获取VIP等级列表API文档
  - [x] 添加获取VIP类型列表API文档
  - [x] 确保API路径和参数正确
  - [x] 添加响应示例
- [x] 配置API文档
  - [x] 添加获取所有配置数据API文档
  - [x] 添加获取项目类型列表API文档
  - [x] 添加获取项目分类列表API文档
  - [x] 添加获取货币类型列表API文档
  - [x] 添加获取价格类型列表API文档
  - [x] 添加获取支付方式列表API文档
  - [x] 添加获取状态列表API文档
  - [x] 添加获取出售状态列表API文档
  - [x] 添加获取每周收益日列表API文档

### 轮播图页面
- [ ] 基础表格展示
- [ ] 搜索功能
- [ ] 筛选功能
- [ ] 添加轮播图功能
- [ ] 编辑轮播图功能
- [ ] 删除轮播图功能
- [ ] 上下架功能

### 附件管理页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按附件名称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、文件信息、时间信息）
  - [x] 支持多种筛选条件：ID、类别、文件名、文件类型等
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 添加功能
  - [x] 实现添加弹窗，支持上传图片和视频文件
  - [x] 支持设置文件类别和文件名
  - [x] 添加成功后自动刷新数据
  - [x] 选择文件后自动填充文件名
- [x] 编辑功能
  - [x] 实现编辑弹窗，支持修改文件类别和文件名
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 删除功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认删除按钮在左边，取消按钮在右边
  - [x] 删除成功后自动刷新数据
- [x] 视图切换功能
  - [x] 支持列表视图和网格视图切换
  - [x] 网格视图显示文件缩略图和基本信息
  - [x] 列表视图显示详细信息
- [x] 图片预览功能
  - [x] 支持点击图片预览
  - [x] 预览支持左右切换浏览所有图片
  - [x] 预览窗口失去焦点时自动关闭
- [x] 导出功能
  - [x] 支持导出附件列表为CSV文件
  - [x] 导出文件包含所有附件信息

### 客服管理页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按客服名称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、状态信息、时间信息）
  - [x] 支持多种筛选条件：ID、类型、标题等
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 添加功能
  - [x] 实现添加弹窗，支持设置客服类型、标题、URL等
  - [x] 添加成功后自动刷新数据
- [x] 编辑功能
  - [x] 实现编辑弹窗，支持修改客服类型、标题、URL等
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 删除功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认删除按钮在左边，取消按钮在右边
  - [x] 删除成功后自动刷新数据
- [x] 复制链接功能
  - [x] 支持一键复制客服链接
  - [x] 复制成功后显示提示信息
- [x] 导出功能
  - [x] 支持导出客服列表为CSV文件
  - [x] 导出文件包含所有客服信息
- [x] 客服图片管理
  - [x] 添加客服图片标签页
  - [x] 实现客服图片列表展示
  - [x] 支持添加、编辑、删除客服图片
  - [x] 支持从附件管理中选择图片
  - [x] 支持预览图片功能
- [x] 权重管理
  - [x] 添加权重字段，控制客服显示顺序
  - [x] 支持拖拽排序功能
  - [x] 拖拽后自动重新计算权重
  - [x] 确保权重值唯一性
- [x] 修复删除功能
  - [x] 修复批量删除时ID处理问题
  - [x] 修复单条删除时ID处理问题
  - [x] 添加详细日志输出，便于调试
  - [x] 优化错误处理逻辑





### 用户级别页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按级别名称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、升级条件、时间信息）
  - [x] 支持多种筛选条件：ID、级别名称、级别范围等
  - [x] 数值字段使用范围输入（最小值至最大值）
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 添加功能
  - [x] 实现分区式添加弹窗（基本信息、升级条件、媒体资源）
  - [x] 支持上传级别图片
  - [x] 支持富文本编辑器编辑内容
  - [x] 添加成功后自动刷新数据
- [x] 编辑功能
  - [x] 实现分区式编辑弹窗
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 删除功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认删除按钮在左边，取消按钮在右边
  - [x] 删除成功后自动刷新数据
- [x] 导出功能（按钮已添加，功能待实现）

### 支付通道页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
  - [x] 表格宽度充斥整个内容页
- [x] 搜索功能
  - [x] 支持按通道名称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、状态信息、时间信息）
  - [x] 支持多种筛选条件：ID、通道名称、通道标识等
  - [x] 数值字段使用范围输入（最小值至最大值）
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 编辑功能
  - [x] 实现分区式编辑弹窗（基本信息、状态设置、其他信息）
  - [x] 显示ID、通道名称、通道标识、国家区号、存款开关、取款开关、默认、创建时间
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 拖动排序功能
  - [x] 支持通过拖动排序按钮改变通道的显示顺序
  - [x] 拖动时提供实时视觉反馈
  - [x] 拖动结束后自动交换两行的位置
  - [x] 自动更新通道的权重值
- [x] 导出功能（按钮已添加，功能待实现）

### 客服管理页面
- [ ] 基础表格展示
- [ ] 搜索功能
- [ ] 筛选功能
- [ ] 导出功能

### 管理员页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
  - [x] 表格宽度充斥整个内容页
  - [x] 添加页面头部说明文字
- [x] 搜索功能
  - [x] 支持按用户名/昵称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、状态信息、时间信息）
  - [x] 支持多种筛选条件：ID、用户名、昵称、Email、所属组别等
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 添加管理员功能
  - [x] 实现添加弹窗，支持设置用户名、昵称、密码、Email、所属组别等
  - [x] 添加成功后自动刷新数据
  - [x] 添加弹窗底部按钮：确定在左，取消在右
- [x] 删除管理员功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认删除按钮在左边，取消按钮在右边
  - [x] 删除成功后自动刷新数据
- [x] 导出功能（按钮已添加，功能待实现）

### 通知消息管理页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
  - [x] 表格宽度充斥整个内容页
  - [x] 添加页面头部说明文字
- [x] 搜索功能
  - [x] 支持按标题/摘要搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、状态信息、时间信息）
  - [x] 支持多种筛选条件：ID、类型、标题、摘要等
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 添加功能
  - [x] 实现添加弹窗，支持设置类型、标题、摘要、内容等
  - [x] 添加成功后自动刷新数据
  - [x] 添加弹窗底部按钮：确定在左，取消在右
- [x] 编辑功能
  - [x] 实现编辑弹窗，支持修改类型、标题、摘要、内容等
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 删除功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认删除按钮在左边，取消按钮在右边
  - [x] 删除成功后自动刷新数据
- [x] 预览功能
  - [x] 支持预览通知消息内容
  - [x] 预览弹窗显示标题、类型、发布时间、摘要和内容
- [x] 置顶功能
  - [x] 支持通过开关切换置顶状态
  - [x] 状态变更后自动提示用户
- [x] 导出功能（按钮已添加，功能待实现）

### 角色组页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
  - [x] 表格宽度充斥整个内容页
  - [x] 添加页面头部说明文字
- [x] 搜索功能
  - [x] 支持按角色组名称搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮
  - [x] 搜索框为空时自动恢复原始数据
- [x] 编辑功能
  - [x] 实现编辑弹窗，支持设置父级、名称、权限等
  - [x] 添加权限树选择功能，支持多级权限设置
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据

## 全局优化
- [x] 统一表格行高（表格标题行高41px，内容行高48px）
- [x] 统一按钮间距和大小
- [x] 统一对话框样式
- [x] 统一筛选对话框样式
- [x] 统一操作栏按钮样式和间距

## 技术债务
- [ ] 优化页面性能，解决卡顿问题
- [ ] 清理冗余代码
- [ ] 添加单元测试
- [ ] 完善错误处理
- [ ] 添加加载状态和空状态处理

## 设计规范
1. 页面布局、样式、风格应参考投资项目页面，保持视觉一致性
2. 按钮间距和大小应参考投资项目页面
3. 表格的行高在所有页面应保持一致（表格标题行高41像素，内容行高48像素）
4. 对话框应该水平垂直居中显示
5. 对话框的布局应该根据内容进行分区
6. 搜索功能应在用户按回车键、点击搜索按钮或输入框失去焦点时触发
7. 筛选功能应在用户点击筛选按钮后，根据筛选条件筛选并显示符合条件的内容
8. 操作按钮应在表格中居中显示，并保持适当的左右边距
9. 分页功能应默认每页显示10条记录，并支持切换为每页显示20条或30条记录
10. 筛选弹窗应采用分区式布局，并在失去焦点时自动关闭

### 邀请奖励页面
- [x] 基础表格展示
  - [x] 表格行高设置为48像素，表头行高设置为41像素
  - [x] 操作按钮居中显示，并保持适当的左右边距
  - [x] 支持多选功能
- [x] 搜索功能
  - [x] 支持按奖励标题搜索
  - [x] 搜索触发方式：按回车键、点击搜索按钮、输入框失去焦点
  - [x] 搜索框为空时自动恢复原始数据
- [x] 筛选功能
  - [x] 实现分区式筛选弹窗（基本信息、奖励条件、时间信息）
  - [x] 支持多种筛选条件：ID、标题、邀请充值人数、邀请注册人数等
  - [x] 数值字段使用范围输入（最小值至最大值）
  - [x] 支持时间范围筛选，精确到秒
  - [x] 筛选弹窗在失去焦点时自动关闭
  - [x] 添加取消按钮，方便用户关闭弹窗
- [x] 添加功能
  - [x] 实现分区式添加弹窗（基本信息、详细描述）
  - [x] 添加成功后自动刷新数据
- [x] 编辑功能
  - [x] 实现分区式编辑弹窗
  - [x] 编辑弹窗底部按钮：确定在左，取消在右
  - [x] 编辑成功后自动刷新数据
- [x] 删除功能
  - [x] 支持单条删除和批量删除
  - [x] 删除前显示确认对话框，确认删除按钮在左边，取消按钮在右边
  - [x] 删除成功后自动刷新数据
- [x] 导出功能（按钮已添加，功能待实现）

## 更新日志

### 2025-05-06
- 修复投资页面表格不显示数据的问题
  - [x] 修复数据处理逻辑，确保正确处理API返回的数据
  - [x] 添加默认值处理，防止空值导致的错误
  - [x] 修复字段名称不匹配的问题，确保前端和后端字段名称一致
  - [x] 修复筛选功能中的类型错误和字段名称不匹配问题
  - [x] 添加详情按钮，使用户可以查看投资详情
  - [x] 优化操作按钮的样式，使界面更美观
  - [x] 添加调试日志，方便排查问题
  - [x] 修复formatAmount函数，使其能够处理非数字类型的输入
  - [x] 在数据处理过程中，使用Number()函数确保所有数值字段都被正确转换为数字类型

### 2024-06-15
- 讨论系统更新策略
  - 分析多国家部署时的时区处理方案
  - 推荐使用UTC存储，本地化显示的方案
  - 讨论系统后期更新的技术栈和实现方法
  - 分析核心-扩展架构的实现方式
  - 探讨配置驱动的差异化实现
  - 研究分阶段更新策略的技术实现
  - 讨论统一管理后台的架构设计
  - 分析自动化更新基础设施的构建方法

### 2024-06-12
- 优化账户页面显示
  - 修改ID显示区域，只显示用户账户ID，不显示邀请码
  - 将ID后面显示的内容从用户ID改为用户名（手机号）
  - 移除邀请码相关的CSS样式
  - 优化账户信息布局

### 2024-11-30
- 优化资金配置页面
  - 删除分组标签页，保留充值返佣比例设置和收益返佣比例设置的分组标题
  - 修改多个配置项的标题，使其更符合业务需求：
    - 1级提现返佣比例(%) → 1级取款奖励(%)
    - 2级提现返佣比例(%) → 2级取款奖励(%)
    - 3级提现返佣比例(%) → 3级取款奖励(%)
    - 单笔最低充值 → 单笔最低存款
    - 单笔最低提现 → 单笔最低取款
    - 单笔最高提现金额 → 单笔最高取款金额
    - 每天最多提现次数 → 每天最多取款次数
    - 每周最多提现次数 → 每周最多取款次数
    - 首次充值奖励比例(%) → 首次充值返现比例(%)
    - 再次充值奖励比例(%) → 再次充值返现比例(%)
    - 每日提取收益比例(%) → 每日抽奖收益率(%)
    - 每日结算时间 → 每日抽奖时间
    - 每日提取类型 → 每日抽奖类型
    - 幸运转盘单次抽奖金额 → 幸运转盘单次消耗金额
    - 幸运转盘中奖人数 → 幸运转盘单次需要人数
    - 电报群手续费 → 取款阶梯手续费
    - 每日签到奖励 → 签到奖励
    - 充值提现同时开放 → 充值用户才能取款
    - 邀请奖励开关 → 已投资用户才能取款
    - 充值返佣开关 → 有未完成投资才能取款
    - 收益返佣开关 → 有直属下级充值才能取款
    - 结算工作日 → 取款工作日
  - 删除以下配置项：
    - USDT兑换汇率
    - VIP升级金额
    - 自动结算开关
    - 提现审核开关
    - 每日签到奖励（其他资金设置中的）
  - 将取款阶梯手续费改为表格形式，支持添加多种手续费设置
  - 添加表格间隔行高亮显示，提高可读性
  - 优化表格样式，使其更加美观易用

### 2024-06-06
- 优化投资项目页面
  - 实现自定义收益率功能，可以灵活设置收益率和结算时间
  - 添加自定义收益率开关，可以启用或禁用自定义收益率
  - 添加自定义结算时间（小时）和自定义收益率（%）设置
  - 当自定义收益率启用时，使用自定义设置而非标准收益设置
  - 自定义收益率仅在每周收益日内生效
  - 在筛选条件中添加自定义收益率的筛选选项
  - 优化数据库结构，添加自定义收益率相关字段
  - 更新前端和后端代码，确保功能正常运行
- 更新开发文档
  - 记录自定义收益率功能的实现
  - 更新投资项目页面的功能列表
  - 添加最新的开发进度

### 2024-06-05
- 优化投资项目页面
  - 实现每周收益时间功能，替代原有的结算时间功能
  - 支持选择周一至周日中的任意几天作为产品的收益日
  - 提供预设筛选选项：工作日（周一至周五）、周末（周六和周日）、全周（周一至周日）
  - 删除原有的settlement_type字段，使用weekly_profit_days字段替代
  - 优化数据库结构，确保数据一致性
  - 更新前端和后端代码，确保功能正常运行
- 更新开发文档
  - 记录每周收益时间功能的实现
  - 更新投资项目页面的功能列表
  - 添加最新的开发进度

### 2024-05-30
- 完善附件管理页面
  - 实现图片预览功能，支持点击图片预览
  - 优化图片预览，支持左右切换浏览所有图片
  - 添加预览窗口失去焦点时自动关闭功能
  - 实现选择文件后自动填充文件名功能
  - 添加导出附件列表为CSV文件功能
- 优化客服管理页面
  - 调整页面布局，与会员列表页面保持一致
  - 优化操作按钮间距，保持视觉一致性
  - 调整表格样式，提高可读性
  - 优化对话框样式，确保居中显示
- 统一页面布局和样式
  - 统一附件管理页面与投资页面的布局
  - 统一客服管理页面与会员列表页面的布局
  - 统一操作按钮间距，保持视觉一致性
  - 更新开发文档，记录最新的功能实现和优化

### 2024-05-28
- 增强系统安全性
  - 优化浏览器限制功能，提供更友好的错误提示
  - 实现 Token 自动刷新机制，提高用户体验
  - 完善登出功能，确保安全退出
  - 优化错误处理和提示，增强用户体验
  - 添加限流错误处理，提供友好的错误提示
- 更新开发文档
  - 更新前端开发进度文档，添加安全性增强的详细说明
  - 记录最新的功能实现和优化

### 2024-05-27
- 优化管理员设置页面
  - 修复添加管理员时账号已存在的错误处理
  - 优化错误提示，确保只显示一条消息
  - 简化错误处理逻辑，提高用户体验
  - 修复页面加载时显示成功消息的问题
- 更新开发进度文档

### 2024-05-26
- 完成通知消息管理页面的开发
  - 实现通知消息管理页面的基础表格展示，包括ID、类型、标题、摘要等字段
  - 实现通知消息管理页面的搜索功能，支持按标题/摘要搜索
  - 实现通知消息管理页面的筛选功能，支持多种筛选条件
  - 实现通知消息管理页面的添加、编辑、删除功能
  - 实现通知消息管理页面的预览功能，支持预览通知内容
  - 实现通知消息管理页面的置顶功能，支持开关切换置顶状态
  - 添加页面头部说明文字，提升用户体验
  - 表格宽度充斥整个内容区域，提升视觉效果
  - 对话框按钮水平居中显示，确定按钮在左，取消按钮在右
- 更新开发进度文档

### 2024-05-25
- 完成角色组页面的开发
  - 实现角色组页面的基础表格展示，包括ID、父级、名称、状态等字段
  - 实现角色组页面的搜索功能，支持按角色组名称搜索
  - 实现角色组页面的编辑功能，支持设置父级、名称、权限等
  - 添加权限树选择功能，支持多级权限设置
  - 添加页面头部说明文字，提升用户体验
  - 操作栏只保留编辑按钮，简化操作
  - 表格宽度充斥整个内容区域，提升视觉效果
  - 对话框按钮水平居中显示，确定按钮在左，取消按钮在右
- 更新开发进度文档

### 2024-05-24
- 完成管理员设置页面的开发
  - 实现管理员设置页面的基础表格展示，包括ID、用户名、昵称、所属组别等字段
  - 实现管理员设置页面的搜索功能，支持按用户名/昵称搜索
  - 实现管理员设置页面的筛选功能，支持多种筛选条件
  - 实现管理员设置页面的添加、删除功能
  - 添加页面头部说明文字，提升用户体验
  - 操作栏只保留删除按钮，简化操作
  - 表格宽度充斥整个内容区域，提升视觉效果
  - 对话框按钮水平居中显示，确定按钮在左，取消按钮在右
- 更新开发进度文档

### 2024-05-23
- 完成兑换码页面的开发
  - 实现兑换码页面的基础表格展示，包括ID、兑换码、金额、货币等字段
  - 实现兑换码页面的搜索功能，支持按兑换码搜索
  - 实现兑换码页面的筛选功能，支持多种筛选条件
  - 实现兑换码页面的添加、编辑、删除功能
  - 实现兑换码页面的开关功能，支持开关切换兑换码状态
  - 表格宽度充斥整个内容区域，提升视觉效果
  - 对话框按钮水平居中显示，确定按钮在左，取消按钮在右
- 更新开发进度文档

### 2024-05-22
- 完成每日抽奖页面的开发
  - 实现每日抽奖页面的基础表格展示，包括日期、类别、奖品名称等字段
  - 实现每日抽奖页面的搜索功能，支持按奖品名称搜索
  - 实现每日抽奖页面的筛选功能，支持多种筛选条件
  - 实现每日抽奖页面的添加、编辑、删除功能
  - 实现每日抽奖页面的上下架功能，支持开关切换抽奖项状态
  - 添加状态选项卡，支持按全部、进行中、已结束等状态筛选
  - 支持上传奖品图片和复制奖品链接
- 更新开发进度文档

### 2024-05-21
- 完成用户级别页面的开发
  - 实现用户级别页面的搜索功能，支持按级别名称搜索
  - 实现用户级别页面的筛选功能，支持多种筛选条件
  - 实现用户级别页面的添加、编辑、删除功能
  - 添加富文本编辑器编辑内容
- 完成支付通道页面的开发
  - 实现支付通道页面的搜索功能，支持按通道名称搜索
  - 实现支付通道页面的筛选功能，支持多种筛选条件
  - 实现支付通道页面的编辑功能，显示ID、通道名称、通道标识等信息
  - 实现支付通道页面的拖动排序功能，支持交换两行位置
  - 调整表格宽度，使其充斥整个内容页
- 完成邀请奖励页面的开发
  - 实现邀请奖励页面的搜索功能，支持按奖励标题搜索
  - 实现邀请奖励页面的筛选功能，支持多种筛选条件
  - 实现邀请奖励页面的添加、编辑、删除功能
- 更新开发进度文档

### 2024-05-20
- 完成投资项目页面的开发
- 完成幸运转盘页面的开发
- 完成参数设置页面的开发
- 实现投资项目页面的筛选功能，支持多种筛选条件
- 实现投资项目页面的添加、编辑、删除功能
- 实现幸运转盘页面的筛选功能，支持多种筛选条件
- 实现幸运转盘页面的添加、编辑、删除功能
- 实现参数设置页面的分类管理和编辑功能
- 优化删除确认弹窗，确认删除按钮在左边，取消按钮在右边
- 优化编辑弹窗，确定按钮在左边，取消按钮在右边
- 优化操作按钮居中显示，并保持适当的左右边距
- 更新开发进度文档

### 2024-05-16
- 完成用户银行卡页面的开发
- 实现用户银行卡页面的搜索功能，支持按用户ID/用户名搜索
- 实现用户银行卡页面的筛选功能，支持多种筛选条件
- 实现用户银行卡页面的编辑功能，使用分区式弹窗
- 实现用户银行卡页面的删除功能，支持单条和批量删除
- 实现用户银行卡页面的分页功能，支持切换每页显示记录数
- 优化用户银行卡页面，移除证件号码字段，简化表格展示
- 更新开发进度文档

### 2024-05-15
- 完成佣金记录页面的开发
- 实现佣金记录页面的搜索功能，支持按用户名搜索
- 实现佣金记录页面的筛选功能，增加来源用户ID、来源用户名等筛选条件
- 实现佣金记录页面的分页功能，支持切换每页显示记录数
- 实现随机生成佣金记录数据的功能，用于测试分页功能
- 优化搜索功能，确保在失去焦点时也能触发搜索
- 更新开发进度文档

### 2024-05-14
- 完成用户流水页面的开发
- 实现用户流水页面的筛选功能，增加变更前余额、变更后余额、业务对象、备注等筛选条件
- 优化筛选功能，确保重置筛选条件后点击搜索能正确显示所有数据
- 实现分页设置保存到本地存储，下次访问时自动使用
- 更新开发进度文档

### 2024-05-13
- 完成取款记录页面的开发
- 优化取款记录页面的筛选功能，增加到账金额、订单流水号、银行编码、支付通道、支付平台订单号等筛选条件
- 优化取款记录页面的筛选弹窗，实现失去焦点时自动关闭
- 更新开发进度文档

### 2024-05-12
- 完成取款记录页面的基础表格展示
- 完成取款记录页面的搜索功能
- 完成取款记录页面的筛选功能
- 完成取款记录页面的分页功能
- 实现随机生成取款记录数据的功能
- 优化筛选弹窗的分区式布局
- 更新开发进度文档

### 2024-05-11
- 完成用户投资页面的删除功能
- 完成用户投资页面的搜索功能
- 完成用户投资页面的筛选功能
- 完成用户投资页面的分页功能
- 实现随机生成投资数据的功能
- 更新开发进度文档

### 2024-05-10
- 完成充值订单页面的分页功能
- 优化充值订单页面的搜索功能
- 优化充值订单页面的筛选弹窗
- 更新开发进度文档

### 2024-05-09
- 完成充值订单页面的编辑功能
- 完成充值订单页面的删除功能
- 实现随机生成订单数据的功能

### 2023-07-16
- 优化会员列表页面的编辑弹窗样式
- 优化会员列表页面的筛选弹窗布局
- 修复会员列表页面的搜索功能
- 添加项目进度文档

### 2023-07-15
- 完成会员列表页面的基础功能开发
- 完成充值订单页面的基础功能开发
