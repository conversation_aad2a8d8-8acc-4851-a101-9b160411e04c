const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UserLevel = sequelize.define('UserLevel', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '级别名称',
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    comment: '级别值',
  },
  upgrade_users: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '升级所需下级用户数',
  },
  upgrade_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '升级所需投资金额',
  },
  return_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '返利比例',
  },
  upgrade_bonus: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '升级奖励',
  },
  image_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '图片ID',
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '级别说明',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'user_levels',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
UserLevel.associate = (models) => {
  // 用户级别与用户
  UserLevel.hasMany(models.User, {
    foreignKey: 'level_id',
    as: 'users',
  });
  
  // 用户级别与图片
  UserLevel.belongsTo(models.Attachment, {
    foreignKey: 'image_id',
    as: 'image',
  });
  
  // 用户级别与项目
  UserLevel.hasMany(models.Project, {
    foreignKey: 'vip_level_id',
    as: 'projects',
  });
};

module.exports = UserLevel;
