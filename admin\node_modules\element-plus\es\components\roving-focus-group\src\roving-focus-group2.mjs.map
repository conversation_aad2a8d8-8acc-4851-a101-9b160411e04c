{"version": 3, "file": "roving-focus-group2.mjs", "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-group.vue"], "sourcesContent": ["<template>\n  <el-focus-group-collection>\n    <el-roving-focus-group-impl v-bind=\"$attrs\">\n      <slot />\n    </el-roving-focus-group-impl>\n  </el-focus-group-collection>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue'\nimport ElRovingFocusGroupImpl from './roving-focus-group-impl.vue'\nimport { ElCollection as ElFocusGroupCollection } from './roving-focus-group'\n\nexport default defineComponent({\n  name: 'ElRovingFocusGroup',\n  components: {\n    ElFocusGroupCollection,\n    ElRovingFocusGroupImpl,\n  },\n})\n</script>\n"], "names": ["ElFocusGroupCollection", "_createBlock", "_withCtx", "_createVNode", "_normalizeProps", "_guardReactiveProps", "_renderSlot"], "mappings": ";;;;;AAaA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,oBAAA;AAAA,EACN,UAAY,EAAA;AAAA,4BACVA,YAAA;AAAA,IACA,sBAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;;sBAlBCC,WAI4B,CAAA,oCAAA,EAAA,IAAA,EAAA;AAAA,IAAA,OAAA,EAAAC,OAAA,CAH1B,MAE6B;AAAA,MAF7BC,WAAA,CAAA,qCAAA,EAAAC,cAAA,CAAAC,kBAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA;AAAA,QAE6B,OAAA,EAAAH,OAAA,CAAA,MAAA;AAAA,UAAAI,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAFO,CAAM;AAAA,SAAA,CAAA;AAAA,QAAA,CAAA,EAAA,CAAA;AAChC,OAAA,EAAA,EAAA,CAAR;AAAQ,KAAA,CAAA;;;;;;;;"}