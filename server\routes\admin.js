const express = require('express');
const adminController = require('../controllers/adminController');
const { verifyAdminToken, checkAdminRole } = require('../middlewares/authMiddleware');

const router = express.Router();

// 所有路由都需要管理员认证
router.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/admins:
 *   get:
 *     summary: 获取管理员列表
 *     tags: [管理员管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *         description: 状态筛选
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [super, admin, operator]
 *         description: 角色筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/', checkAdminRole(['super', 'admin']), adminController.getAdmins);

/**
 * @swagger
 * /api/admin/admins/{id}:
 *   get:
 *     summary: 获取管理员详情
 *     tags: [管理员管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 管理员ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 管理员不存在
 */
router.get('/:id', checkAdminRole(['super', 'admin']), adminController.getAdmin);

/**
 * @swagger
 * /api/admin/admins:
 *   post:
 *     summary: 创建管理员
 *     tags: [管理员管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *               - name
 *             properties:
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [admin, operator]
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 *       409:
 *         description: 用户名或邮箱已存在
 */
router.post('/', checkAdminRole(['super']), adminController.createAdmin);

/**
 * @swagger
 * /api/admin/admins/{id}:
 *   put:
 *     summary: 更新管理员
 *     tags: [管理员管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 管理员ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [admin, operator]
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 管理员不存在
 *       409:
 *         description: 邮箱已存在
 */
router.put('/:id', checkAdminRole(['super', 'admin']), adminController.updateAdmin);

/**
 * @swagger
 * /api/admin/admins/{id}/password:
 *   put:
 *     summary: 重置管理员密码
 *     tags: [管理员管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 管理员ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: 密码重置成功
 *       404:
 *         description: 管理员不存在
 */
router.put('/:id/password', checkAdminRole(['super']), adminController.resetPassword);

/**
 * @swagger
 * /api/admin/admins/{id}:
 *   delete:
 *     summary: 删除管理员
 *     tags: [管理员管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 管理员ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 管理员不存在
 */
router.delete('/:id', checkAdminRole(['super']), adminController.deleteAdmin);

module.exports = router;
