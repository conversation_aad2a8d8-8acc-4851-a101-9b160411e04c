/* empty css             *//* empty css                   *//* empty css                      *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";import{d as te,r as b,a as ae,o as ne,c as T,b as s,e as t,w as o,m as se,f as oe,i as ie,aq as re,aa as ue,ab as de,ac as ce,a8 as R,p as w,n as u,x as me,j as S,ad as pe,at as fe,af as ve,ag as be,ai as ge,aj as _e,ak as we,V as ye,al as Ve,y as v,an as Me,v as j,ap as xe,a9 as he,aA as Se,g as U,_ as De}from"./index-LncY9lAB.js";import{s as ke}from"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";function Y(y){return ke({url:"/api/admin/transactions",method:"get",params:y})}function Ce(y,g="YYYY-MM-DD HH:mm:ss"){if(!y)return"";try{const r=new Date(y);if(isNaN(r.getTime()))return console.error("无效的日期:",y),"";const p=r.getFullYear(),f=String(r.getMonth()+1).padStart(2,"0"),V=String(r.getDate()).padStart(2,"0"),d=String(r.getHours()).padStart(2,"0"),C=String(r.getMinutes()).padStart(2,"0"),M=String(r.getSeconds()).padStart(2,"0");return g.replace("YYYY",String(p)).replace("MM",f).replace("DD",V).replace("HH",d).replace("mm",C).replace("ss",M)}catch(r){return console.error("日期格式化错误:",r),""}}const Ie={class:"transactions-container"},Be={class:"toolbar"},Ue={class:"toolbar-left"},Ye={class:"toolbar-right"},ze={class:"table-wrapper"},Ee={class:"pagination-container"},Te={class:"filter-container"},Re={class:"filter-section"},je={class:"section-content"},Fe={class:"filter-grid"},He={class:"filter-item"},Ne={class:"filter-item"},Oe={class:"filter-item"},Pe={class:"filter-item"},$e={class:"filter-item"},Ae={class:"filter-item"},Ke={class:"filter-section"},Le={class:"section-content"},qe={class:"filter-grid"},Ge={class:"filter-item"},Je={class:"range-inputs"},Qe={class:"filter-item"},We={class:"range-inputs"},Xe={class:"filter-item"},Ze={class:"range-inputs"},el={class:"filter-section"},ll={class:"section-content"},tl={class:"filter-grid"},al={class:"filter-item"},nl={class:"filter-footer"},sl=te({__name:"index",setup(y){const g=b(""),r=b(""),p=b(1),f=b(10),V=b(48),d=b(!1),C=b([]),M=b([]),x=b(!1),l=ae({id:"",userId:"",username:"",type:"",amountMin:null,amountMax:null,beforeBalanceMin:null,beforeBalanceMax:null,balanceMin:null,balanceMax:null,businessObject:"",remark:"",timeRange:null}),F=()=>{x.value=!0},H=()=>{l.timeRange=null,l.amountMin=null,l.amountMax=null,l.beforeBalanceMin=null,l.beforeBalanceMax=null,l.balanceMin=null,l.balanceMax=null,l.id="",l.userId="",l.username="",l.type="",l.businessObject="",l.remark="",w.success("筛选条件已重置")},N=()=>{H(),h()},O=()=>{console.log("应用筛选条件",l),x.value=!1;const n={page:1,limit:f.value};if(l.id&&(n.id=l.id),l.userId&&(n.user_id=l.userId),l.username&&(n.keyword=l.username),l.type){const e={存款:"deposit",取款:"withdrawal",购买:"investment",系统赠送:"system_gift",收益:"profit",佣金:"commission",扣除:"deduction",退回:"increase"};n.type=e[l.type]||l.type}l.amountMin&&(n.amount_min=l.amountMin),l.amountMax&&(n.amount_max=l.amountMax),l.beforeBalanceMin&&(n.before_balance_min=l.beforeBalanceMin),l.beforeBalanceMax&&(n.before_balance_max=l.beforeBalanceMax),l.balanceMin&&(n.balance_min=l.balanceMin),l.balanceMax&&(n.balance_max=l.balanceMax),l.businessObject&&(n.reference_id=l.businessObject),l.remark&&(n.description=l.remark),l.timeRange&&l.timeRange.length===2&&(n.start_date=l.timeRange[0],n.end_date=l.timeRange[1]),d.value=!0,Y(n).then(e=>{M.value=e.items,V.value=e.total,p.value=1,d.value=!1,w.success(`找到 ${e.total} 条匹配记录`)}).catch(e=>{w.error("筛选数据失败，请稍后重试"),d.value=!1})},P=n=>{switch(n){case"deposit":return"deposit-type";case"withdrawal":return"withdrawal-type";case"investment":case"investment_purchase":return"investment-type";case"investment_gift":case"bonus":return"bonus-type";case"profit":return"profit-type";case"commission":return"commission-type";case"deduction":return"deduction-type";case"increase":return"increase-type";default:return"default-type"}},$=n=>{switch(n){case"deposit":return"存款";case"withdrawal":return"取款";case"investment":case"investment_purchase":return"购买";case"investment_gift":case"bonus":return"系统赠送";case"profit":return"收益";case"commission":return"佣金";case"deduction":return"扣除";case"increase":return"退回";default:return n}},A=n=>{switch(n){case"success":return"success";case"pending":return"warning";case"failed":return"danger";default:return"info"}},K=n=>{switch(n){case"success":return"成功";case"pending":return"处理中";case"failed":return"失败";default:return n}},I=n=>(typeof n=="string"&&(n=parseFloat(n)),typeof n=="number"&&!isNaN(n)?n.toFixed(2):"0.00"),D=()=>{p.value=1,h()},L=n=>{p.value=n,h()},z=n=>{f.value=n,p.value=1,h(),localStorage.setItem("transactionsPageSize",n.toString())},q=n=>{C.value=n},h=()=>{d.value=!0;const n={page:p.value,limit:f.value};g.value&&(n.keyword=g.value),r.value&&(r.value==="system_gift"?n.type=["investment_gift","bonus"]:n.type=r.value),Y(n).then(e=>{M.value=e.items,V.value=e.total,d.value=!1}).catch(e=>{w.error("获取数据失败，请稍后重试"),d.value=!1})},G=()=>{w.info("功能尚未实现")},J=()=>{d.value=!0,g.value="",r.value="",p.value=1,Y({page:1,limit:f.value}).then(n=>{M.value=n.items,V.value=n.total,d.value=!1,w.success("数据已刷新")}).catch(n=>{w.error("刷新数据失败，请稍后重试"),d.value=!1})};return ne(()=>{const n=localStorage.getItem("transactionsPageSize");n&&(f.value=parseInt(n)),h()}),(n,e)=>{const k=me,_=se,c=ie,i=fe,B=re,m=Ve,E=Me,Q=xe,W=ue,X=de,Z=Se,ee=ce,le=we;return U(),T(R,null,[s("div",Ie,[s("div",Be,[s("div",Ue,[t(_,{class:"toolbar-button",type:"default",onClick:J},{default:o(()=>[t(k,null,{default:o(()=>[t(S(pe))]),_:1}),e[19]||(e[19]=u("刷新 "))]),_:1})]),s("div",Ye,[t(c,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=a=>g.value=a),placeholder:"搜索用户名或交易描述",class:"search-input",onKeyup:oe(D,["enter"]),onBlur:D},null,8,["modelValue"]),t(B,{modelValue:r.value,"onUpdate:modelValue":e[1]||(e[1]=a=>r.value=a),placeholder:"全部类型",class:"type-select",onChange:D},{default:o(()=>[t(i,{label:"全部类型",value:""}),t(i,{label:"存款",value:"deposit"}),t(i,{label:"取款",value:"withdrawal"}),t(i,{label:"购买",value:"investment"}),t(i,{label:"系统赠送",value:"system_gift"}),t(i,{label:"收益",value:"profit"}),t(i,{label:"佣金",value:"commission"}),t(i,{label:"扣除",value:"deduction"}),t(i,{label:"退回",value:"increase"})]),_:1},8,["modelValue"]),t(_,{class:"search-button",type:"primary",onClick:D},{default:o(()=>[t(k,null,{default:o(()=>[t(S(ve))]),_:1})]),_:1}),t(_,{class:"toolbar-button filter-button",type:"default",onClick:F},{default:o(()=>[t(k,null,{default:o(()=>[t(S(be))]),_:1}),e[20]||(e[20]=u("筛选 "))]),_:1}),t(_,{class:"toolbar-button export-button",type:"default",onClick:G},{default:o(()=>[t(k,null,{default:o(()=>[t(S(ge))]),_:1}),e[21]||(e[21]=u("导出 "))]),_:1})])]),t(W,{class:"table-card"},{default:o(()=>[s("div",ze,[_e((U(),ye(Q,{ref:"transactionTable",data:M.value,border:"",stripe:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:q},{default:o(()=>[t(m,{type:"selection",width:"40",align:"center",fixed:"left"}),t(m,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),t(m,{label:"用户ID","min-width":"90",align:"center",fixed:"left"},{default:o(a=>[u(v(a.row.user?a.row.user.user_id:"-"),1)]),_:1}),t(m,{label:"用户名","min-width":"100",align:"center",fixed:"left"},{default:o(a=>[u(v(a.row.user?a.row.user.username:"-"),1)]),_:1}),t(m,{label:"类型","min-width":"90",align:"center"},{default:o(a=>[t(E,{class:j(["type-tag",P(a.row.type)]),size:"small"},{default:o(()=>[u(v($(a.row.type)),1)]),_:2},1032,["class"])]),_:1}),t(m,{label:"金额","min-width":"90",align:"center"},{default:o(a=>[s("span",{class:j(a.row.amount>0?"positive":"negative")},v(a.row.amount>0?"+":"")+v(I(a.row.amount)),3)]),_:1}),t(m,{label:"交易前余额","min-width":"120",align:"center"},{default:o(a=>[u(v(I(a.row.before_balance)),1)]),_:1}),t(m,{label:"交易后余额","min-width":"120",align:"center"},{default:o(a=>[u(v(I(a.row.balance)),1)]),_:1}),t(m,{label:"状态","min-width":"80",align:"center"},{default:o(a=>[t(E,{type:A(a.row.status),size:"small"},{default:o(()=>[u(v(K(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(m,{label:"关联记录","min-width":"100",align:"center"},{default:o(a=>[u(v(a.row.reference_id?`${a.row.reference_type}#${a.row.reference_id}`:"-"),1)]),_:1}),t(m,{prop:"description",label:"描述","min-width":"150",align:"center"}),t(m,{label:"创建时间",width:"150",align:"center",sortable:""},{default:o(a=>[u(v(S(Ce)(a.row.created_at)),1)]),_:1})]),_:1},8,["data"])),[[le,d.value]])])]),_:1}),s("div",Ee,[t(X,{"current-page":p.value,"onUpdate:currentPage":e[2]||(e[2]=a=>p.value=a),"page-size":f.value,"onUpdate:pageSize":e[3]||(e[3]=a=>f.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:V.value,onSizeChange:z,onCurrentChange:L,"pager-count":7,background:""},{sizes:o(()=>[t(B,{"model-value":f.value,onChange:z,class:"custom-page-size"},{default:o(()=>[(U(),T(R,null,he([10,20,50,100],a=>t(i,{key:a,value:a,label:`${a}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])])]),t(ee,{modelValue:x.value,"onUpdate:modelValue":e[18]||(e[18]=a=>x.value=a),title:"筛选条件",width:"900px","close-on-click-modal":!0,"close-on-press-escape":!0,class:"filter-dialog"},{footer:o(()=>[s("div",nl,[t(_,{class:"filter-button",type:"primary",onClick:O},{default:o(()=>e[38]||(e[38]=[u(" 搜索 ")])),_:1}),t(_,{class:"filter-button",onClick:N},{default:o(()=>e[39]||(e[39]=[u(" 重置 ")])),_:1}),t(_,{class:"filter-button",onClick:e[17]||(e[17]=a=>x.value=!1)},{default:o(()=>e[40]||(e[40]=[u(" 取消 ")])),_:1})])]),default:o(()=>[s("div",Te,[s("div",Re,[e[28]||(e[28]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"基本信息")],-1)),s("div",je,[s("div",Fe,[s("div",He,[e[22]||(e[22]=s("div",{class:"filter-label"},"ID",-1)),t(c,{modelValue:l.id,"onUpdate:modelValue":e[4]||(e[4]=a=>l.id=a),placeholder:"请输入ID",clearable:""},null,8,["modelValue"])]),s("div",Ne,[e[23]||(e[23]=s("div",{class:"filter-label"},"用户ID",-1)),t(c,{modelValue:l.userId,"onUpdate:modelValue":e[5]||(e[5]=a=>l.userId=a),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])]),s("div",Oe,[e[24]||(e[24]=s("div",{class:"filter-label"},"用户名",-1)),t(c,{modelValue:l.username,"onUpdate:modelValue":e[6]||(e[6]=a=>l.username=a),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),s("div",Pe,[e[25]||(e[25]=s("div",{class:"filter-label"},"类型",-1)),t(B,{modelValue:l.type,"onUpdate:modelValue":e[7]||(e[7]=a=>l.type=a),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:o(()=>[t(i,{label:"全部",value:""}),t(i,{label:"存款",value:"deposit"}),t(i,{label:"取款",value:"withdrawal"}),t(i,{label:"购买",value:"investment"}),t(i,{label:"系统赠送",value:"system_gift"}),t(i,{label:"收益",value:"profit"}),t(i,{label:"佣金",value:"commission"}),t(i,{label:"扣除",value:"deduction"}),t(i,{label:"退回",value:"increase"})]),_:1},8,["modelValue"])]),s("div",$e,[e[26]||(e[26]=s("div",{class:"filter-label"},"业务对象",-1)),t(c,{modelValue:l.businessObject,"onUpdate:modelValue":e[8]||(e[8]=a=>l.businessObject=a),placeholder:"请输入业务对象",clearable:""},null,8,["modelValue"])]),s("div",Ae,[e[27]||(e[27]=s("div",{class:"filter-label"},"备注",-1)),t(c,{modelValue:l.remark,"onUpdate:modelValue":e[9]||(e[9]=a=>l.remark=a),placeholder:"请输入备注",clearable:""},null,8,["modelValue"])])])])]),s("div",Ke,[e[35]||(e[35]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"金额信息")],-1)),s("div",Le,[s("div",qe,[s("div",Ge,[e[30]||(e[30]=s("div",{class:"filter-label"},"金额范围",-1)),s("div",Je,[t(c,{modelValue:l.amountMin,"onUpdate:modelValue":e[10]||(e[10]=a=>l.amountMin=a),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[29]||(e[29]=s("span",null,"-",-1)),t(c,{modelValue:l.amountMax,"onUpdate:modelValue":e[11]||(e[11]=a=>l.amountMax=a),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),s("div",Qe,[e[32]||(e[32]=s("div",{class:"filter-label"},"交易前余额",-1)),s("div",We,[t(c,{modelValue:l.beforeBalanceMin,"onUpdate:modelValue":e[12]||(e[12]=a=>l.beforeBalanceMin=a),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[31]||(e[31]=s("span",null,"-",-1)),t(c,{modelValue:l.beforeBalanceMax,"onUpdate:modelValue":e[13]||(e[13]=a=>l.beforeBalanceMax=a),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),s("div",Xe,[e[34]||(e[34]=s("div",{class:"filter-label"},"交易后余额",-1)),s("div",Ze,[t(c,{modelValue:l.balanceMin,"onUpdate:modelValue":e[14]||(e[14]=a=>l.balanceMin=a),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[33]||(e[33]=s("span",null,"-",-1)),t(c,{modelValue:l.balanceMax,"onUpdate:modelValue":e[15]||(e[15]=a=>l.balanceMax=a),placeholder:"最大值",clearable:""},null,8,["modelValue"])])])])])]),s("div",el,[e[37]||(e[37]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"时间信息")],-1)),s("div",ll,[s("div",tl,[s("div",al,[e[36]||(e[36]=s("div",{class:"filter-label"},"创建时间",-1)),t(Z,{modelValue:l.timeRange,"onUpdate:modelValue":e[16]||(e[16]=a=>l.timeRange=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])])])])])]),_:1},8,["modelValue"])],64)}}}),Vl=De(sl,[["__scopeId","data-v-b3b009bd"]]);export{Vl as default};
