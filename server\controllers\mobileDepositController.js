/**
 * 移动端充值控制器
 * 处理用户充值相关操作
 */
const { Deposit, User, BankCard, SystemParam, PaymentChannel } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { generateOrderNumber } = require('../utils/orderUtils');
const { getKBPayService } = require('../services/kbPayService');
const { getBasePayService } = require('../services/basePayService');

// 创建充值订单
exports.createDeposit = async (req, res) => {
  try {
    const { amount, payment_channel_id, bank_card_id } = req.body;
    const user = req.user;

    // 验证请求数据
    if (!amount || !payment_channel_id) {
      return res.status(400).json({
        code: 400,
        message: '金额和支付通道不能为空',
        data: null
      });
    }

    // 验证金额是否为数字
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return res.status(400).json({
        code: 400,
        message: '金额必须大于0',
        data: null
      });
    }

    // 获取最低充值金额参数
    const minDepositParam = await SystemParam.findOne({
      where: {
        param_key: '[site.min_deposit_amount]'
      }
    });

    // 验证最低充值金额
    if (minDepositParam) {
      const minDepositAmount = parseFloat(minDepositParam.param_value);
      if (!isNaN(minDepositAmount) && numAmount < minDepositAmount) {
        return res.status(400).json({
          code: 400,
          message: `单笔最低充值金额为${minDepositAmount}`,
          data: null
        });
      }
    }

    // 获取支付通道信息
    const paymentChannel = await PaymentChannel.findByPk(payment_channel_id);
    if (!paymentChannel) {
      return res.status(400).json({
        code: 400,
        message: '支付通道不存在',
        data: null
      });
    }

    // 验证支付通道是否启用
    if (!paymentChannel.status || !paymentChannel.deposit_enabled) {
      return res.status(400).json({
        code: 400,
        message: '该支付通道暂不可用',
        data: null
      });
    }

    // 验证充值金额是否在支付通道的限制范围内
    if (paymentChannel.min_deposit_amount && numAmount < paymentChannel.min_deposit_amount) {
      return res.status(400).json({
        code: 400,
        message: `该支付通道单笔最低充值金额为${paymentChannel.min_deposit_amount}`,
        data: null
      });
    }

    if (paymentChannel.max_deposit_amount && numAmount > paymentChannel.max_deposit_amount) {
      return res.status(400).json({
        code: 400,
        message: `该支付通道单笔最高充值金额为${paymentChannel.max_deposit_amount}`,
        data: null
      });
    }

    // 如果支付方式是银行卡，验证银行卡ID
    if (paymentChannel.code === 'bank' && !bank_card_id) {
      return res.status(400).json({
        code: 400,
        message: '银行卡ID不能为空',
        data: null
      });
    }

    // 生成订单号
    const orderNumber = generateOrderNumber('RE');

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 创建充值订单
      const deposit = await Deposit.create({
        user_id: user.id,
        order_number: orderNumber,
        amount: numAmount,
        actual_amount: numAmount, // 实际到账金额与充值金额相同
        payment_method: paymentChannel.code,
        payment_channel_id,
        bank_card_id: paymentChannel.code === 'bank' ? bank_card_id : null,
        status: 'pending', // 初始状态为待支付
        remark: '用户充值'
      }, { transaction });

      // 提交事务
      await transaction.commit();

      // 如果是KB支付，调用KB支付服务创建支付
      if (paymentChannel.code === 'kbpay') {
        try {
          console.log('=== KB支付处理开始 ===');
          console.log('支付通道ID:', payment_channel_id);
          console.log('银行卡ID:', bank_card_id);
          console.log('订单信息:', {
            order_number: deposit.order_number,
            amount: deposit.amount,
            user_id: deposit.user_id
          });

          const kbPayService = await getKBPayService(payment_channel_id);
          // 对于kbpay，bank_card_id实际上是银行ID
          const bankId = bank_card_id;
          const paymentResult = await kbPayService.createPayment(deposit, bankId);

          if (paymentResult.success) {
            return res.status(201).json({
              code: 201,
              message: '充值订单创建成功',
              data: {
                order_id: deposit.id,
                order_number: deposit.order_number,
                amount: deposit.amount,
                payment_method: deposit.payment_method,
                payment_channel_id: deposit.payment_channel_id,
                status: deposit.status,
                created_at: deposit.created_at,
                payment_url: paymentResult.data.url // KB支付链接
              }
            });
          } else {
            return res.status(400).json({
              code: 400,
              message: paymentResult.message || '创建支付失败',
              data: {
                order_id: deposit.id,
                order_number: deposit.order_number
              }
            });
          }
        } catch (error) {
          console.error('KB支付创建错误:', error);
          return res.status(500).json({
            code: 500,
            message: '支付服务异常',
            data: {
              order_id: deposit.id,
              order_number: deposit.order_number
            }
          });
        }
      }

      // 如果是Base支付，调用Base支付服务创建支付
      if (paymentChannel.code === 'basepay') {
        try {
          console.log('=== Base支付处理开始 ===');
          console.log('支付通道ID:', payment_channel_id);
          console.log('订单信息:', {
            order_number: deposit.order_number,
            amount: deposit.amount,
            user_id: deposit.user_id
          });

          const basePayService = await getBasePayService(payment_channel_id);
          const paymentResult = await basePayService.createPayment(deposit);

          if (paymentResult.success) {
            return res.status(201).json({
              code: 201,
              message: '充值订单创建成功',
              data: {
                order_id: deposit.id,
                order_number: deposit.order_number,
                amount: deposit.amount,
                payment_method: deposit.payment_method,
                payment_channel_id: deposit.payment_channel_id,
                status: deposit.status,
                created_at: deposit.created_at,
                payment_url: paymentResult.paymentUrl // Base支付链接
              }
            });
          } else {
            return res.status(400).json({
              code: 400,
              message: paymentResult.message || '创建支付失败',
              data: {
                order_id: deposit.id,
                order_number: deposit.order_number
              }
            });
          }
        } catch (error) {
          console.error('Base支付创建错误:', error);
          return res.status(500).json({
            code: 500,
            message: '支付服务异常',
            data: {
              order_id: deposit.id,
              order_number: deposit.order_number
            }
          });
        }
      }

      // 其他支付方式返回标准响应
      return res.status(201).json({
        code: 201,
        message: '充值订单创建成功',
        data: {
          order_id: deposit.id,
          order_number: deposit.order_number,
          amount: deposit.amount,
          payment_method: deposit.payment_method,
          payment_channel_id: deposit.payment_channel_id,
          status: deposit.status,
          created_at: deposit.created_at
        }
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('创建充值订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取用户充值记录
exports.getUserDeposits = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const user = req.user;

    // 构建查询条件
    const where = {
      user_id: user.id
    };

    // 根据状态筛选
    if (status && status !== 'all') {
      where.status = status;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Deposit.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取用户充值记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 获取充值订单详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 响应对象
 */
exports.getDepositDetail = async (req, res) => {
  try {
    const { orderNumber } = req.params;
    const user = req.user;

    if (!orderNumber) {
      return res.status(400).json({
        code: 400,
        message: '订单号不能为空',
        data: null
      });
    }

    // 查询订单
    const deposit = await Deposit.findOne({
      where: {
        user_id: user.id,
        order_number: orderNumber
      }
    });

    if (!deposit) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在',
        data: null
      });
    }

    // 如果是银行转账，获取银行卡信息
    let bankCard = null;
    if (deposit.payment_method === 'bank' && deposit.bank_card_id) {
      bankCard = await BankCard.findByPk(deposit.bank_card_id);
    }

    // 获取支付说明
    const paymentInstructions = await SystemParam.findOne({
      where: {
        param_key: '[site.payment_instructions]'
      }
    });

    // 如果是KB支付且订单状态为待支付，尝试获取支付URL
    let paymentUrl = null;
    if (deposit.payment_method === 'kbpay' && deposit.status === 'pending') {
      try {
        const kbPayService = await getKBPayService(deposit.payment_channel_id);
        const paymentResult = await kbPayService.createPayment(deposit);
        if (paymentResult.success) {
          paymentUrl = paymentResult.data.url;
        }
      } catch (error) {
        console.error('获取KB支付链接错误:', error);
        // 即使获取失败也继续返回订单详情
      }
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        id: deposit.id,
        order_number: deposit.order_number,
        amount: deposit.amount,
        actual_amount: deposit.actual_amount,
        payment_method: deposit.payment_method,
        status: deposit.status,
        created_at: deposit.created_at,
        bank_card: bankCard ? {
          bank_name: bankCard.bank_name,
          card_number: bankCard.card_number,
          card_holder: bankCard.card_holder
        } : null,
        payment_instructions: paymentInstructions ? paymentInstructions.param_value : null,
        payment_url: paymentUrl // 添加支付URL
      }
    });
  } catch (error) {
    console.error('获取充值订单详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 取消充值订单
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 响应对象
 */
exports.cancelDeposit = async (req, res) => {
  try {
    const { orderNumber } = req.params;
    const user = req.user;

    if (!orderNumber) {
      return res.status(400).json({
        code: 400,
        message: '订单号不能为空',
        data: null
      });
    }

    // 查询订单
    const deposit = await Deposit.findOne({
      where: {
        user_id: user.id,
        order_number: orderNumber
      }
    });

    if (!deposit) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在',
        data: null
      });
    }

    // 检查订单状态
    if (deposit.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: '只能取消待支付的订单',
        data: null
      });
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      await deposit.update({
        status: 'cancelled',
        remark: deposit.remark + ' | 用户取消'
      }, { transaction });

      // 提交事务
      await transaction.commit();

      return res.status(200).json({
        code: 200,
        message: '取消成功',
        data: null
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('取消充值订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

module.exports = exports;
