import{a1 as l,a2 as s,p as n}from"./index-LncY9lAB.js";import{s as o}from"./request-Cd-6Wde0.js";function u(r){return o({url:"/api/admin/auth/login",method:"post",data:r})}function i(){return o({url:"/api/admin/auth/profile",method:"get"})}function m(){return o({url:"/api/admin/auth/logout",method:"post"})}const d=l("user",{state:()=>({token:localStorage.getItem("token")||"",userInfo:null}),getters:{isLoggedIn:r=>!!r.token,username:r=>{var e;return((e=r.userInfo)==null?void 0:e.username)||""},name:r=>{var e;return((e=r.userInfo)==null?void 0:e.name)||""},role:r=>{var e;return((e=r.userInfo)==null?void 0:e.role)||""},avatar:r=>{var e;return((e=r.userInfo)==null?void 0:e.avatar)||""}},actions:{async login(r){try{const e=await u(r);if(e&&e.data){const{token:a,admin:t}=e.data;return this.token=a,localStorage.setItem("token",a),this.userInfo={id:t.id,username:t.username,name:t.nickname||t.username,email:t.email,avatar:t.avatar,role:t.roles&&t.roles.length>0?t.roles[0].name:"",created_at:t.created_at},!0}else if(e&&e.token){const{token:a,admin:t}=e;return this.token=a,localStorage.setItem("token",a),this.userInfo={id:t.id,username:t.username,name:t.nickname||t.username,email:t.email,avatar:t.avatar,role:t.roles&&t.roles.length>0?t.roles[0].name:"",created_at:t.created_at},!0}else throw new Error("登录响应格式错误")}catch(e){return n.error(e.message||"登录失败"),!1}},async fetchUserInfo(){if(!this.token)return null;try{const r=await i();let e;r&&r.data?e=r.data:e=r;const a={id:e.id,username:e.username,name:e.nickname||e.username,email:e.email,avatar:e.avatar,role:e.roles&&e.roles.length>0?e.roles[0].name:"",created_at:e.created_at};return this.userInfo=a,a}catch(r){return n.error(r.message||"获取用户信息失败"),null}},async logout(){try{return this.token&&await m(),this.token="",this.userInfo=null,localStorage.removeItem("token"),s.push("/login"),n.success("登出成功"),!0}catch(r){return console.error("登出错误:",r),n.error(r.message||"登出失败"),this.token="",this.userInfo=null,localStorage.removeItem("token"),s.push("/login"),!1}}}});export{d as u};
