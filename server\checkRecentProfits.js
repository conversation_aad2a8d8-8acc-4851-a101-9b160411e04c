/**
 * 检查最近的收益发放情况
 */
const { InvestmentProfit } = require('./models');
const { Op } = require('sequelize');
const logger = require('./utils/logger');

async function checkRecentProfits() {
  try {
    // 获取最近30分钟内的收益记录
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    
    const recentProfits = await InvestmentProfit.findAll({
      where: {
        created_at: {
          [Op.gte]: thirtyMinutesAgo
        }
      },
      order: [['created_at', 'DESC']]
    });
    
    logger.info(`最近30分钟内发放的收益记录数: ${recentProfits.length}`);
    
    if (recentProfits.length > 0) {
      recentProfits.forEach(profit => {
        logger.info(`收益ID: ${profit.id}, 投资ID: ${profit.investment_id}, 用户ID: ${profit.user_id}, 金额: ${profit.amount}, 时间: ${profit.created_at}`);
      });
    } else {
      logger.info('最近30分钟内没有发放收益');
    }
    
    // 特别检查投资ID 116 的收益记录
    const profit116 = await InvestmentProfit.findAll({
      where: {
        investment_id: 116
      },
      order: [['created_at', 'DESC']]
    });
    
    logger.info(`投资ID 116 的收益记录数: ${profit116.length}`);
    
    if (profit116.length > 0) {
      profit116.forEach(profit => {
        logger.info(`收益ID: ${profit.id}, 投资ID: ${profit.investment_id}, 用户ID: ${profit.user_id}, 金额: ${profit.amount}, 时间: ${profit.created_at}`);
      });
    } else {
      logger.info('投资ID 116 没有收益记录');
    }
    
    process.exit(0);
  } catch (error) {
    logger.error('检查最近收益发放情况失败:', error);
    process.exit(1);
  }
}

// 执行检查
checkRecentProfits();
