const express = require('express');
const orderController = require('../controllers/orderController');
const { verifyAdminToken, verifyUserToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 管理员路由
const adminRouter = express.Router();

// 所有路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/orders:
 *   get:
 *     summary: 获取订单列表
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, paid, completed, cancelled, refunded]
 *         description: 状态筛选
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', orderController.getOrders);

/**
 * @swagger
 * /api/admin/orders/{id}:
 *   get:
 *     summary: 获取订单详情
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 订单不存在
 */
adminRouter.get('/:id', orderController.getOrder);

/**
 * @swagger
 * /api/admin/orders/{id}/status:
 *   put:
 *     summary: 更新订单状态
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [paid, completed, cancelled, refunded]
 *               actual_return:
 *                 type: number
 *                 description: 实际收益，仅在状态为completed时有效
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 订单不存在
 */
adminRouter.put('/:id/status', checkAdminRole(['super', 'admin']), orderController.updateOrderStatus);

// 移动端路由
const mobileRouter = express.Router();

// 所有路由都需要用户认证
mobileRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/orders:
 *   get:
 *     summary: 获取用户订单列表
 *     tags: [订单]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, paid, completed, cancelled, refunded]
 *         description: 状态筛选
 *     responses:
 *       200:
 *         description: 获取成功
 */
mobileRouter.get('/', orderController.getUserOrders);

/**
 * @swagger
 * /api/mobile/orders/{id}:
 *   get:
 *     summary: 获取订单详情
 *     tags: [订单]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 订单不存在
 */
mobileRouter.get('/:id', orderController.getUserOrder);

/**
 * @swagger
 * /api/mobile/orders:
 *   post:
 *     summary: 创建订单
 *     tags: [订单]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - project_id
 *               - amount
 *             properties:
 *               project_id:
 *                 type: integer
 *               amount:
 *                 type: number
 *               payment_method:
 *                 type: string
 *                 enum: [balance, alipay, wechat, bank]
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 *       404:
 *         description: 项目不存在
 */
mobileRouter.post('/', orderController.createOrder);

/**
 * @swagger
 * /api/mobile/orders/{id}/pay:
 *   put:
 *     summary: 支付订单
 *     tags: [订单]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - payment_method
 *             properties:
 *               payment_method:
 *                 type: string
 *                 enum: [balance, alipay, wechat, bank]
 *     responses:
 *       200:
 *         description: 支付成功
 *       400:
 *         description: 请求数据错误
 *       404:
 *         description: 订单不存在
 */
mobileRouter.put('/:id/pay', orderController.payOrder);

/**
 * @swagger
 * /api/mobile/orders/{id}/cancel:
 *   put:
 *     summary: 取消订单
 *     tags: [订单]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 取消成功
 *       400:
 *         description: 请求数据错误
 *       404:
 *         description: 订单不存在
 */
mobileRouter.put('/:id/cancel', orderController.cancelOrder);

module.exports = {
  admin: adminRouter,
  mobile: mobileRouter
};
