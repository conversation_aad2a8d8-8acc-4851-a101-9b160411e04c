{"version": 3, "file": "validator.mjs", "sources": ["../../../../../packages/utils/vue/validator.ts"], "sourcesContent": ["import { componentSizes, datePickTypes } from '@element-plus/constants'\nimport type { ComponentSize, DatePickType } from '@element-plus/constants'\n\nexport const isValidComponentSize = (val: string): val is ComponentSize | '' =>\n  ['', ...componentSizes].includes(val)\n\nexport const isValidDatePickType = (val: string): val is DatePickType =>\n  ([...datePickTypes] as string[]).includes(val)\n"], "names": [], "mappings": ";;;AACY,MAAC,oBAAoB,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,GAAG,cAAc,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE;AACvE,MAAC,mBAAmB,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,CAAC,QAAQ,CAAC,GAAG;;;;"}