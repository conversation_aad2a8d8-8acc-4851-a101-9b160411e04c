# 超级管理员账号和初始用户创建脚本

## 脚本名称
`init-super-admin-and-users.js`

## 功能说明
这个脚本用于创建系统的超级管理员账号和初始用户，包含完整的关系映射和权限配置。

## 超级管理员关系映射
- **Admin**: 管理员基本信息（用户名、密码、昵称等）
- **Role**: 角色系统（超级管理员组、管理员组）
- **Permission**: 权限系统（菜单权限、操作权限）
- **AdminRole**: 管理员-角色关联（多对多关系）
- **RolePermission**: 角色-权限关联（多对多关系）

## 初始用户关系映射
- **User**: 用户基本信息（包含user_id、邀请码、邀请人等）
- **UserLevel**: 用户等级系统（VIP1、VIP2、VIP3）
- **AccountBalance**: 用户账户余额（收入账户、充值账户）
- **InviteCode**: 邀请码管理（用户专属邀请码）
- **UserRelation**: 用户关系链（多级邀请关系）

## 创建的数据

### 超级管理员账号
- **用户名**: task0012
- **密码**: xt00159258
- **角色**: 超级管理员（拥有所有权限）

### 用户等级
- **VIP1**: 初级用户（升级条件：无）
- **VIP2**: 中级用户（升级条件：5个下级用户，投资10,000）
- **VIP3**: 高级用户（升级条件：10个下级用户，投资50,000）

### 测试用户
- **root**: 系统根用户（用户ID: U000001）
- **user**: 测试用户（密码: user00125!，root的下级）
- **testuser2**: 测试用户2（密码: user123，user的下级）

### 用户关系链
```
root (系统根用户)
└── user (一级用户，充值账户余额: 1000)
    └── testuser2 (二级用户，充值账户余额: 500)
```

## 使用方法

### 方法1：直接运行脚本
```bash
cd server/scripts
node init-super-admin-and-users.js
```

### 方法2：使用批处理文件（Windows）
```bash
cd server/scripts
run-init-super-admin-and-users.bat
```

### 方法3：在其他脚本中调用
```javascript
const { initSuperAdminAndUsers } = require('./init-super-admin-and-users');
await initSuperAdminAndUsers();
```

## 注意事项

1. **数据库连接**: 确保数据库连接配置正确，脚本会自动测试连接
2. **表结构**: 脚本会自动同步数据库模型，确保表结构存在
3. **重复运行**: 脚本使用`findOrCreate`方法，可以安全地重复运行
4. **密码安全**: 使用Argon2加密（回退到bcrypt），确保密码安全
5. **事务处理**: 用户创建过程使用数据库事务，确保数据一致性

## 创建的权限列表

### 菜单权限
- 首页查看
- 会员列表查看
- 充值订单查看
- 提现订单查看
- 项目列表查看
- 投资订单查看
- 系统参数设置
- 支付通道管理
- 银行管理
- 角色管理
- 管理员管理

### 操作权限
- 会员详情查看、编辑、删除
- 充值订单审核
- 提现订单审核
- 项目创建、编辑、删除

## 环境要求
- Node.js
- MySQL数据库
- 已配置的.env文件
- 安装了所需的npm包（bcryptjs、argon2、sequelize等）
