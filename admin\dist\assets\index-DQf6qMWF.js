/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";import{d as ue,r as p,a as F,q as ce,o as me,c as Y,b as a,e as l,w as s,m as fe,f as _e,i as pe,aa as ve,ab as be,ac as ge,p as u,n as c,x as ke,j as g,ad as Ve,ae as P,aB as j,af as Ce,ag as he,ai as we,aj as ye,ak as Be,V as De,al as Ee,y as z,ap as Se,aq as Ae,a8 as Ne,a9 as Re,at as xe,E as Ie,h as Te,aH as Ue,aA as Oe,g as M,_ as $e}from"./index-LncY9lAB.js";import{s as A}from"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";function ze(v){return A({url:"/api/admin/bank-cards",method:"get",params:v})}function J(v){return A({url:`/api/admin/bank-cards/${v}`,method:"get"})}function Me(v,R){return A({url:`/api/admin/bank-cards/${v}`,method:"put",data:R})}function He(v){return A({url:`/api/admin/bank-cards/${v}`,method:"delete"})}function Fe(v){return A({url:"/api/admin/bank-cards/batch",method:"delete",data:{ids:v}})}const Ye={class:"user-cards-container"},Pe={class:"toolbar"},je={class:"toolbar-left"},Je={class:"toolbar-right"},qe={class:"table-wrapper"},Ke={class:"operation-buttons-container"},Le={class:"pagination-container"},We={class:"edit-form-container"},Ge={class:"form-section"},Qe={class:"form-row"},Xe={class:"form-section"},Ze={class:"form-row"},el={class:"form-row"},ll={class:"form-row"},al={class:"form-row"},tl={class:"dialog-footer"},ol={class:"delete-content"},sl={class:"dialog-footer"},nl={class:"filter-container"},rl={class:"filter-section"},dl={class:"section-content"},il={class:"filter-grid"},ul={class:"filter-item"},cl={class:"filter-item"},ml={class:"filter-item"},fl={class:"filter-item"},_l={class:"filter-section"},pl={class:"section-content"},vl={class:"filter-grid"},bl={class:"filter-item"},gl={class:"filter-item"},kl={class:"filter-section"},Vl={class:"section-content"},Cl={class:"filter-grid"},hl={class:"filter-item"},wl={class:"filter-footer"},yl=ue({__name:"index",setup(v){const R=o=>{if(!o)return"-";try{const e=new Date(o);if(isNaN(e.getTime()))return o;const d=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),f=String(e.getDate()).padStart(2,"0"),_=String(e.getHours()).padStart(2,"0"),U=String(e.getMinutes()).padStart(2,"0");return`${d}-${r}-${f} ${_}:${U}`}catch(e){return console.error("日期格式化错误:",e),o}},V=p(""),C=p(1),B=p(10),x=p(0),h=p(!1),k=p([]),i=F({id:0,user_id:0,username:"",card_number:"",card_holder:"",bank_name:"",bank_code:""}),n=F({id:"",user_id:"",username:"",bank_name:"",card_number:"",card_holder:"",createTimeRange:[]}),D=p(!1),w=p(!1),E=p(!1),N=p(null),I=p([]),q=ce(()=>I.value),T=()=>{C.value=1,b()},K=o=>{C.value=o,b()},H=o=>{B.value=o,C.value=1,b(),localStorage.setItem("userCardsPageSize",o.toString())},L=o=>{k.value=o},b=async()=>{h.value=!0;try{const o={page:C.value,limit:B.value,card_type:"user"};V.value.trim()&&(V.value.trim().match(/^\d+$/)||V.value.trim().match(/^U\d+$/)?(o.user_id_str=V.value.trim(),console.log("作为用户ID搜索:",o.user_id_str)):(o.username=V.value.trim(),console.log("作为用户名搜索:",o.username))),n.id&&(o.id=n.id),n.user_id&&(o.user_id=n.user_id),n.username&&(o.username=n.username),n.bank_name&&(o.bank_name=n.bank_name),n.card_number&&(o.card_number=n.card_number),n.card_holder&&(o.card_holder=n.card_holder),n.createTimeRange&&n.createTimeRange.length===2&&(o.start_time=n.createTimeRange[0],o.end_time=n.createTimeRange[1]);const e=await ze(o);e?e.code===200&&e.data?(I.value=e.data.items||[],x.value=e.data.total||0):e.items&&e.total!==void 0?(I.value=e.items||[],x.value=e.total||0):u.error("获取数据失败：未知的响应数据格式"):u.error((e==null?void 0:e.message)||"获取数据失败")}catch{u.error("获取数据失败")}finally{h.value=!1}},W=()=>{u.info("功能尚未实现")},G=async()=>{var e;if(k.value.length!==1){u.warning("请选择一条记录进行编辑");return}const o=k.value[0];try{const d=await J(o.id);if(d.code===200&&d.data){const r=d.data;Object.assign(i,{id:r.id,user_id:r.user_id,username:((e=r.user)==null?void 0:e.username)||"",card_number:r.card_number,card_holder:r.card_holder,bank_name:r.bank_name,bank_code:r.bank_code||""}),D.value=!0}else u.error(d.message||"获取银行卡详情失败")}catch(d){console.error("获取银行卡详情错误:",d),u.error("获取银行卡详情失败")}},Q=()=>{if(k.value.length===0){u.warning("请至少选择一条记录进行删除");return}N.value=null,w.value=!0},X=async o=>{var e;try{const d=await J(o.id);if(d.code===200&&d.data){const r=d.data;Object.assign(i,{id:r.id,user_id:r.user_id,username:((e=r.user)==null?void 0:e.username)||"",card_number:r.card_number,card_holder:r.card_holder,bank_name:r.bank_name,bank_code:r.bank_code||""}),D.value=!0}else u.error(d.message||"获取银行卡详情失败")}catch(d){console.error("获取银行卡详情错误:",d),u.error("获取银行卡详情失败")}},Z=o=>{N.value=o,w.value=!0},ee=async()=>{h.value=!0;try{const o={bank_name:i.bank_name,card_number:i.card_number,card_holder:i.card_holder,bank_code:i.bank_code},e=await Me(i.id,o);e.code===200?(u.success("保存成功"),D.value=!1,b()):u.error(e.message||"保存失败")}catch(o){console.error("更新银行卡错误:",o),u.error("保存失败")}finally{h.value=!1}},le=async()=>{h.value=!0;try{if(N.value){const o=await He(N.value.id);o.code===200?(u.success("删除成功"),w.value=!1,b()):u.error(o.message||"删除失败")}else{const o=k.value.map(d=>d.id),e=await Fe(o);e.code===200?(u.success("删除成功"),w.value=!1,b()):u.error(e.message||"删除失败")}}catch{u.error("删除失败")}finally{h.value=!1,k.value=[]}},ae=()=>{Object.keys(n).forEach(o=>{o==="createTimeRange"?n[o]=[]:n[o]=""})},te=()=>{C.value=1,b(),E.value=!1},oe=()=>{E.value=!0};return me(()=>{const o=localStorage.getItem("userCardsPageSize");o&&(B.value=parseInt(o)),b()}),(o,e)=>{const d=ke,r=fe,f=pe,_=Ee,U=Se,se=ve,m=xe,O=Ae,ne=be,S=Te,re=Ie,$=ge,de=Oe,ie=Be;return M(),Y("div",Ye,[a("div",Pe,[a("div",je,[l(r,{class:"toolbar-button",type:"default",onClick:b},{default:s(()=>[l(d,null,{default:s(()=>[l(g(Ve))]),_:1}),e[21]||(e[21]=c("刷新 "))]),_:1}),l(r,{class:"toolbar-button",type:"default",disabled:k.value.length!==1,onClick:G},{default:s(()=>[l(d,null,{default:s(()=>[l(g(P))]),_:1}),e[22]||(e[22]=c("编辑 "))]),_:1},8,["disabled"]),l(r,{class:"toolbar-button",type:"danger",disabled:k.value.length===0,onClick:Q},{default:s(()=>[l(d,null,{default:s(()=>[l(g(j))]),_:1}),e[23]||(e[23]=c("删除 "))]),_:1},8,["disabled"])]),a("div",Je,[l(f,{modelValue:V.value,"onUpdate:modelValue":e[0]||(e[0]=t=>V.value=t),placeholder:"搜索用户ID/用户名",class:"search-input",onKeyup:_e(T,["enter"]),onBlur:T},null,8,["modelValue"]),l(r,{class:"search-button",type:"primary",onClick:T},{default:s(()=>[l(d,null,{default:s(()=>[l(g(Ce))]),_:1})]),_:1}),l(r,{class:"toolbar-button filter-button",type:"default",onClick:oe},{default:s(()=>[l(d,null,{default:s(()=>[l(g(he))]),_:1}),e[24]||(e[24]=c("筛选 "))]),_:1}),l(r,{class:"toolbar-button export-button",type:"default",onClick:W},{default:s(()=>[l(d,null,{default:s(()=>[l(g(we))]),_:1}),e[25]||(e[25]=c("导出 "))]),_:1})])]),l(se,{class:"table-card"},{default:s(()=>[a("div",qe,[ye((M(),De(U,{ref:"userCardsTable",data:q.value,border:"",stripe:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:L,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:s(()=>[l(_,{type:"selection",width:"40",align:"center",fixed:"left"}),l(_,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),l(_,{label:"用户ID",width:"100",align:"center",fixed:"left"},{default:s(t=>{var y;return[c(z(((y=t.row.user)==null?void 0:y.user_id)||"-"),1)]}),_:1}),l(_,{label:"用户名",width:"120",align:"center",fixed:"left"},{default:s(t=>{var y;return[c(z(((y=t.row.user)==null?void 0:y.username)||"-"),1)]}),_:1}),l(_,{prop:"card_number",label:"银行卡号","min-width":"180",align:"center"}),l(_,{prop:"card_holder",label:"持卡人","min-width":"100",align:"center"}),l(_,{prop:"bank_name",label:"银行名称","min-width":"120",align:"center"}),l(_,{prop:"bank_code",label:"银行编码",width:"120",align:"center"}),l(_,{prop:"created_at",label:"创建时间",width:"150",align:"center",sortable:""},{default:s(t=>[c(z(R(t.row.created_at)),1)]),_:1}),l(_,{label:"操作",width:"140",align:"center",fixed:"right"},{default:s(t=>[a("div",Ke,[l(r,{class:"operation-button icon-only",size:"small",type:"default",onClick:y=>X(t.row)},{default:s(()=>[l(d,null,{default:s(()=>[l(g(P))]),_:1})]),_:2},1032,["onClick"]),l(r,{type:"danger",size:"small",onClick:y=>Z(t.row),class:"operation-button"},{default:s(()=>[l(d,null,{default:s(()=>[l(g(j))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[ie,h.value]])])]),_:1}),a("div",Le,[l(ne,{"current-page":C.value,"onUpdate:currentPage":e[1]||(e[1]=t=>C.value=t),"page-size":B.value,"onUpdate:pageSize":e[2]||(e[2]=t=>B.value=t),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:x.value,onSizeChange:H,onCurrentChange:K,"pager-count":7,background:""},{sizes:s(()=>[l(O,{"model-value":B.value,onChange:H,class:"custom-page-size"},{default:s(()=>[(M(),Y(Ne,null,Re([10,20,50,100],t=>l(m,{key:t,value:t,label:`${t}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),l($,{modelValue:D.value,"onUpdate:modelValue":e[9]||(e[9]=t=>D.value=t),title:"编辑银行卡",width:"600px","close-on-click-modal":!1,class:"edit-dialog"},{footer:s(()=>[a("div",tl,[l(r,{type:"primary",onClick:ee},{default:s(()=>e[28]||(e[28]=[c("保存")])),_:1}),l(r,{onClick:e[8]||(e[8]=t=>D.value=!1)},{default:s(()=>e[29]||(e[29]=[c("取消")])),_:1})])]),default:s(()=>[a("div",We,[l(re,{model:i,"label-width":"100px"},{default:s(()=>[a("div",Ge,[e[26]||(e[26]=a("div",{class:"section-title"},"基本信息",-1)),a("div",Qe,[l(S,{label:"用户名"},{default:s(()=>[l(f,{modelValue:i.username,"onUpdate:modelValue":e[3]||(e[3]=t=>i.username=t),readonly:""},null,8,["modelValue"])]),_:1})])]),a("div",Xe,[e[27]||(e[27]=a("div",{class:"section-title"},"银行卡信息",-1)),a("div",Ze,[l(S,{label:"持卡人"},{default:s(()=>[l(f,{modelValue:i.card_holder,"onUpdate:modelValue":e[4]||(e[4]=t=>i.card_holder=t)},null,8,["modelValue"])]),_:1})]),a("div",el,[l(S,{label:"银行名称"},{default:s(()=>[l(O,{modelValue:i.bank_name,"onUpdate:modelValue":e[5]||(e[5]=t=>i.bank_name=t),placeholder:"请选择银行",style:{width:"100%"}},{default:s(()=>[l(m,{label:"BBVA BANCOMER",value:"BBVA BANCOMER"}),l(m,{label:"BANJERCITO",value:"BANJERCITO"}),l(m,{label:"BANOBRAS",value:"BANOBRAS"}),l(m,{label:"HSBC",value:"HSBC"}),l(m,{label:"SANTANDER",value:"SANTANDER"}),l(m,{label:"其他银行",value:"其他银行"})]),_:1},8,["modelValue"])]),_:1})]),a("div",ll,[l(S,{label:"银行卡号"},{default:s(()=>[l(f,{modelValue:i.card_number,"onUpdate:modelValue":e[6]||(e[6]=t=>i.card_number=t)},null,8,["modelValue"])]),_:1})]),a("div",al,[l(S,{label:"银行编码"},{default:s(()=>[l(f,{modelValue:i.bank_code,"onUpdate:modelValue":e[7]||(e[7]=t=>i.bank_code=t)},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])])]),_:1},8,["modelValue"]),l($,{modelValue:w.value,"onUpdate:modelValue":e[11]||(e[11]=t=>w.value=t),title:"确认删除",width:"400px","close-on-click-modal":!1,class:"delete-dialog"},{footer:s(()=>[a("div",sl,[l(r,{type:"danger",onClick:le},{default:s(()=>e[32]||(e[32]=[c("确定删除")])),_:1}),l(r,{onClick:e[10]||(e[10]=t=>w.value=!1)},{default:s(()=>e[33]||(e[33]=[c("取消")])),_:1})])]),default:s(()=>[a("div",ol,[l(d,{class:"delete-icon"},{default:s(()=>[l(g(Ue))]),_:1}),e[30]||(e[30]=a("p",{class:"delete-confirm-text"},"确定要删除所选银行卡吗？",-1)),e[31]||(e[31]=a("p",{class:"delete-warning-text"},"此操作不可恢复。",-1))])]),_:1},8,["modelValue"]),l($,{modelValue:E.value,"onUpdate:modelValue":e[20]||(e[20]=t=>E.value=t),title:"筛选条件",width:"900px","close-on-click-modal":!1,class:"filter-dialog"},{footer:s(()=>[a("div",wl,[l(r,{class:"filter-button",type:"primary",onClick:te},{default:s(()=>e[44]||(e[44]=[c(" 搜索 ")])),_:1}),l(r,{class:"filter-button",onClick:ae},{default:s(()=>e[45]||(e[45]=[c(" 重置 ")])),_:1}),l(r,{class:"filter-button",onClick:e[19]||(e[19]=t=>E.value=!1)},{default:s(()=>e[46]||(e[46]=[c(" 取消 ")])),_:1})])]),default:s(()=>[a("div",nl,[a("div",rl,[e[38]||(e[38]=a("div",{class:"section-header"},[a("div",{class:"section-title"},"基本信息")],-1)),a("div",dl,[a("div",il,[a("div",ul,[e[34]||(e[34]=a("div",{class:"filter-label"},"ID",-1)),l(f,{modelValue:n.id,"onUpdate:modelValue":e[12]||(e[12]=t=>n.id=t),placeholder:"请输入ID",clearable:""},null,8,["modelValue"])]),a("div",cl,[e[35]||(e[35]=a("div",{class:"filter-label"},"用户ID",-1)),l(f,{modelValue:n.user_id,"onUpdate:modelValue":e[13]||(e[13]=t=>n.user_id=t),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])]),a("div",ml,[e[36]||(e[36]=a("div",{class:"filter-label"},"用户名",-1)),l(f,{modelValue:n.username,"onUpdate:modelValue":e[14]||(e[14]=t=>n.username=t),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),a("div",fl,[e[37]||(e[37]=a("div",{class:"filter-label"},"银行名称",-1)),l(O,{modelValue:n.bank_name,"onUpdate:modelValue":e[15]||(e[15]=t=>n.bank_name=t),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:s(()=>[l(m,{label:"全部",value:""}),l(m,{label:"BBVA BANCOMER",value:"BBVA BANCOMER"}),l(m,{label:"BANJERCITO",value:"BANJERCITO"}),l(m,{label:"BANOBRAS",value:"BANOBRAS"}),l(m,{label:"HSBC",value:"HSBC"}),l(m,{label:"SANTANDER",value:"SANTANDER"}),l(m,{label:"其他银行",value:"其他银行"})]),_:1},8,["modelValue"])])])])]),a("div",_l,[e[41]||(e[41]=a("div",{class:"section-header"},[a("div",{class:"section-title"},"银行卡信息")],-1)),a("div",pl,[a("div",vl,[a("div",bl,[e[39]||(e[39]=a("div",{class:"filter-label"},"银行卡号",-1)),l(f,{modelValue:n.card_number,"onUpdate:modelValue":e[16]||(e[16]=t=>n.card_number=t),placeholder:"请输入银行卡号",clearable:""},null,8,["modelValue"])]),a("div",gl,[e[40]||(e[40]=a("div",{class:"filter-label"},"持卡人姓名",-1)),l(f,{modelValue:n.card_holder,"onUpdate:modelValue":e[17]||(e[17]=t=>n.card_holder=t),placeholder:"请输入持卡人姓名",clearable:""},null,8,["modelValue"])])])])]),a("div",kl,[e[43]||(e[43]=a("div",{class:"section-header"},[a("div",{class:"section-title"},"时间信息")],-1)),a("div",Vl,[a("div",Cl,[a("div",hl,[e[42]||(e[42]=a("div",{class:"filter-label"},"创建时间",-1)),l(de,{modelValue:n.createTimeRange,"onUpdate:modelValue":e[18]||(e[18]=t=>n.createTimeRange=t),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])])])])])]),_:1},8,["modelValue"])])}}}),Yl=$e(yl,[["__scopeId","data-v-5b5108f5"]]);export{Yl as default};
