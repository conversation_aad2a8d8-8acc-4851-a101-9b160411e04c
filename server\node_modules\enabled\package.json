{"name": "enabled", "version": "2.0.0", "description": "Check if a certain debug flag is enabled.", "main": "index.js", "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "test": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js", "watch": "mocha --watch test.js"}, "repository": {"type": "git", "url": "git://github.com/3rd-Eden/enabled.git"}, "keywords": ["enabled", "debug", "diagnostics", "flag", "env", "variable", "localstorage"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {}, "devDependencies": {"assume": "2.1.x", "istanbul": "^0.4.5", "mocha": "5.2.x", "pre-commit": "1.2.x"}}