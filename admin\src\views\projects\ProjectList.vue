<template>
  <div class="project-list-container">
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>添加项目
        </el-button>
        <el-button type="default" @click="fetchProjects">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索项目"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>

    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="projectList"
        border
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="项目名称" min-width="150" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'fixed' ? 'primary' : 'success'">
              {{ scope.row.type === 'fixed' ? '固定收益' : '浮动收益' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expected_return" label="预期收益率" width="120">
          <template #default="scope">
            {{ scope.row.expected_return }}%
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="投资期限" width="100">
          <template #default="scope">
            {{ scope.row.duration }}天
          </template>
        </el-table-column>
        <el-table-column prop="min_investment" label="最小投资额" width="120">
          <template #default="scope">
            {{ formatAmount(scope.row.min_investment) }}
          </template>
        </el-table-column>
        <el-table-column prop="max_investment" label="最大投资额" width="120">
          <template #default="scope">
            {{ scope.row.max_investment ? formatAmount(scope.row.max_investment) : '不限' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="项目总额" width="120">
          <template #default="scope">
            {{ formatAmount(scope.row.total_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="current_amount" label="已募集金额" width="120">
          <template #default="scope">
            {{ formatAmount(scope.row.current_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="risk_level" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelType(scope.row.risk_level)">
              {{ getRiskLevelText(scope.row.risk_level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始时间" width="180" />
        <el-table-column prop="end_time" label="结束时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              <el-icon><Edit /></el-icon>编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              <el-icon><Delete /></el-icon>删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 项目表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑项目' : '添加项目'"
      width="800px"
      destroy-on-close
    >
      <el-form
        ref="projectFormRef"
        :model="projectForm"
        :rules="projectRules"
        label-width="120px"
      >
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="项目名称" prop="title">
              <el-input v-model="projectForm.title" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="项目描述" prop="description">
              <el-input
                v-model="projectForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入项目描述"
              />
            </el-form-item>
            <el-form-item label="项目类型" prop="type">
              <el-select v-model="projectForm.type" placeholder="请选择项目类型" style="width: 100%">
                <el-option label="固定收益" value="fixed" />
                <el-option label="浮动收益" value="flexible" />
              </el-select>
            </el-form-item>
            <el-form-item label="风险等级" prop="risk_level">
              <el-select v-model="projectForm.risk_level" placeholder="请选择风险等级" style="width: 100%">
                <el-option label="低风险" value="low" />
                <el-option label="中风险" value="medium" />
                <el-option label="高风险" value="high" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目状态" prop="status">
              <el-select v-model="projectForm.status" placeholder="请选择项目状态" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="待审核" value="pending" />
                <el-option label="已上线" value="active" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="投资设置" name="investment">
            <el-form-item label="预期收益率(%)" prop="expected_return">
              <el-input-number
                v-model="projectForm.expected_return"
                :precision="2"
                :step="0.1"
                :min="0"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="最小投资额" prop="min_investment">
              <el-input-number
                v-model="projectForm.min_investment"
                :min="0"
                :step="100"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="最大投资额" prop="max_investment">
              <el-input-number
                v-model="projectForm.max_investment"
                :min="0"
                :step="1000"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="项目总额" prop="total_amount">
              <el-input-number
                v-model="projectForm.total_amount"
                :min="0"
                :step="10000"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="投资期限(天)" prop="duration">
              <el-input-number
                v-model="projectForm.duration"
                :min="1"
                :step="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="时间设置" name="time">
            <el-form-item label="开始时间" prop="start_time">
              <el-date-picker
                v-model="projectForm.start_time"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="end_time">
              <el-date-picker
                v-model="projectForm.end_time"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="封面图片" name="cover">
            <el-form-item label="封面图片" prop="cover_image">
              <div class="image-preview-container">
                <img v-if="projectForm.cover_image" :src="projectForm.cover_image" class="avatar" />
                <div v-else class="avatar-placeholder">
                  <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
                  <p>请从附件库选择图片</p>
                </div>
                <el-button type="primary" @click="showAttachmentSelector">从附件选择图片</el-button>
              </div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitForm">确定</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
          
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
    >
      <p>确定要删除该项目吗？此操作不可恢复！</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 附件选择器 -->
    <AttachmentSelector
      v-model:visible="attachmentSelectorVisible"
      fileType="image"
      @select="handleAttachmentSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Search, Refresh } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { getProjects, getProject, createProject, updateProject, deleteProject } from '@/api/project'
import AttachmentSelector from '@/components/AttachmentSelector.vue'

// 状态和数据
const loading = ref(false)
const projectList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const selectedProjects = ref([])
const dialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const isEdit = ref(false)
const currentProjectId = ref<number | null>(null)
const activeTab = ref('basic')
const projectFormRef = ref<FormInstance>()

// 项目表单
const projectForm = reactive({
  title: '',
  description: '',
  cover_image: '',
  type: 'fixed',
  expected_return: 5,
  min_investment: 100.00,
  max_investment: 50000,
  duration: 30,
  total_amount: 1000000,
  current_amount: 0,
  start_time: '',
  end_time: '',
  status: 'draft',
  risk_level: 'medium'
})

// 表单验证规则
const projectRules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  expected_return: [
    { required: true, message: '请输入预期收益率', trigger: 'blur' }
  ],
  min_investment: [
    { required: true, message: '请输入最小投资额', trigger: 'blur' }
  ],
  duration: [
    { required: true, message: '请输入投资期限', trigger: 'blur' }
  ],
  total_amount: [
    { required: true, message: '请输入项目总额', trigger: 'blur' }
  ],
  start_time: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  risk_level: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择项目状态', trigger: 'change' }
  ]
})

// 生命周期钩子
onMounted(() => {
  fetchProjects()
})

// 方法
const fetchProjects = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      keyword: searchKeyword.value || undefined
    }
    const response = await getProjects(params)
    projectList.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchProjects()
}

const handleSelectionChange = (selection) => {
  selectedProjects.value = selection
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchProjects()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchProjects()
}

const resetForm = () => {
  if (projectFormRef.value) {
    projectFormRef.value.resetFields()
  }
  Object.assign(projectForm, {
    title: '',
    description: '',
    cover_image: '',
    type: 'fixed',
    expected_return: 5,
    min_investment: 100.00,
    max_investment: 50000,
    duration: 30,
    total_amount: 1000000,
    current_amount: 0,
    start_time: '',
    end_time: '',
    status: 'draft',
    risk_level: 'medium'
  })
}

const handleAdd = () => {
  isEdit.value = false
  currentProjectId.value = null
  resetForm()
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  isEdit.value = true
  currentProjectId.value = row.id
  resetForm()
  
  loading.value = true
  try {
    const projectData = await getProject(row.id)
    Object.assign(projectForm, projectData)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取项目详情失败:', error)
    ElMessage.error('获取项目详情失败')
  } finally {
    loading.value = false
  }
}

const handleDelete = (row) => {
  currentProjectId.value = row.id
  deleteDialogVisible.value = true
}

const confirmDelete = async () => {
  if (!currentProjectId.value) return
  
  loading.value = true
  try {
    await deleteProject(currentProjectId.value)
    ElMessage.success('删除成功')
    deleteDialogVisible.value = false
    fetchProjects()
  } catch (error) {
    console.error('删除项目失败:', error)
    ElMessage.error('删除项目失败')
  } finally {
    loading.value = false
  }
}

const submitForm = async () => {
  if (!projectFormRef.value) return
  
  await projectFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        if (isEdit.value && currentProjectId.value) {
          await updateProject(currentProjectId.value, projectForm)
          ElMessage.success('更新成功')
        } else {
          await createProject(projectForm)
          ElMessage.success('创建成功')
        }
        dialogVisible.value = false
        fetchProjects()
      } catch (error) {
        console.error('保存项目失败:', error)
        ElMessage.error('保存项目失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 附件选择器相关
const attachmentSelectorVisible = ref(false)

const showAttachmentSelector = () => {
  attachmentSelectorVisible.value = true
}

const handleAttachmentSelected = (attachment: any) => {
  if (attachment) {
    projectForm.cover_image = attachment.url || attachment.file_path
    ElMessage.success('图片选择成功')
    attachmentSelectorVisible.value = false
  }
}

// 格式化金额
const formatAmount = (amount) => {
  return amount ? `¥${amount.toLocaleString()}` : '¥0'
}

// 获取风险等级标签类型
const getRiskLevelType = (level) => {
  switch (level) {
    case 'low':
      return 'success'
    case 'medium':
      return 'warning'
    case 'high':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取风险等级文本
const getRiskLevelText = (level) => {
  switch (level) {
    case 'low':
      return '低风险'
    case 'medium':
      return '中风险'
    case 'high':
      return '高风险'
    default:
      return '未知'
  }
}

// 获取状态标签类型
const getStatusType = (status) => {
  switch (status) {
    case 'draft':
      return 'info'
    case 'pending':
      return 'warning'
    case 'active':
      return 'success'
    case 'completed':
      return 'primary'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'draft':
      return '草稿'
    case 'pending':
      return '待审核'
    case 'active':
      return '已上线'
    case 'completed':
      return '已完成'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.project-list-container {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  width: 300px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.avatar-uploader {
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.image-preview-container {
  text-align: center;
}

.avatar-placeholder {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  width: 178px;
  height: 178px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.avatar-placeholder p {
  margin: 5px 0 0 0;
  color: #8c939d;
  font-size: 12px;
}
</style>
