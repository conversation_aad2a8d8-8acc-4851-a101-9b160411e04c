const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Banner = sequelize.define('Banner', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '标题',
  },
  attachment_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '附件ID',
  },
  url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '已废弃字段',
  },
  position: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '图片物理路径',
  },
  sort_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序',
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '状态：true=启用, false=禁用',
  },
  start_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '开始时间',
  },
  end_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '结束时间',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'banners',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Banner.associate = (models) => {
  // 轮播图与附件
  Banner.belongsTo(models.Attachment, {
    foreignKey: 'attachment_id',
    as: 'attachment',
  });
};

module.exports = Banner;
