<template>
  <div class="bank-code-mapping-panel">
    <div class="panel-header">
      <h3>{{ paymentChannelName }} 支付通道配置</h3>
      <el-alert
        type="warning"
        :closable="false"
        show-icon
      >
        <p>注意：此功能为高级配置，请谨慎操作。修改配置可能会影响支付处理。</p>
      </el-alert>
    </div>

    <div class="panel-content">
      <!-- 如果是Base支付通道或KBPay支付通道，显示Tab切换 -->
      <el-tabs v-if="shouldShowTabs" v-model="activeTab" class="config-tabs">
        <el-tab-pane label="银行编码配置" name="bankMapping">
          <div class="tab-content">
            <el-table
              :data="mappings"
              border
              style="width: 100%"
              v-loading="loading"
            >
              <el-table-column prop="bank.name" label="银行名称" width="150" />
              <el-table-column prop="payin_method" :label="isBasePayChannel ? '代收编码(pay_type)' : '代收方式'" width="140" />
              <el-table-column prop="payout_method" :label="isBasePayChannel ? '代付编码(bank_code)' : '代付方式'" width="140" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status ? 'success' : 'danger'">
                    {{ scope.row.status ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                  <el-button
                    :type="scope.row.status ? 'danger' : 'success'"
                    size="small"
                    @click="handleToggleStatus(scope.row)"
                  >
                    {{ scope.row.status ? '禁用' : '启用' }}
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDelete(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="panel-footer">
              <el-button type="primary" @click="handleAddMapping">添加银行编码</el-button>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="商户配置" name="merchantConfig">
          <div class="tab-content">
            <el-form :model="merchantConfigForm" label-width="120px" v-loading="merchantConfigLoading">
              <el-form-item label="商户号" required>
                <el-input
                  v-model="merchantConfigForm.merchant_no"
                  :placeholder="isBasePayChannel ? '请输入Base支付商户号' : '请输入KBPay商户号'"
                  clearable
                />
              </el-form-item>

              <el-form-item label="代收密钥" required>
                <el-input
                  v-model="merchantConfigForm.payin_key"
                  placeholder="请输入代收密钥"
                  type="password"
                  show-password
                  clearable
                />
                <div class="form-tip">用于客户充值功能的签名密钥</div>
              </el-form-item>

              <el-form-item label="代付密钥" required>
                <el-input
                  v-model="merchantConfigForm.payout_key"
                  placeholder="请输入代付密钥"
                  type="password"
                  show-password
                  clearable
                />
                <div class="form-tip">用于提现功能的签名密钥</div>
              </el-form-item>
            </el-form>

            <div class="panel-footer">
              <el-button type="primary" @click="saveMerchantConfig" :loading="merchantConfigSaving">
                保存商户配置
              </el-button>
              <el-button @click="resetMerchantConfig">
                重置
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 如果不是Base支付通道或KBPay支付通道，直接显示银行编码配置 -->
      <div v-else>
        <el-table
          :data="mappings"
          border
          style="width: 100%"
          v-loading="loading"
        >
          <el-table-column prop="bank.name" label="银行名称" width="150" />
          <el-table-column prop="payin_method" label="代收方式" width="140" />
          <el-table-column prop="payout_method" label="代付方式" width="140" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status ? 'success' : 'danger'">
                {{ scope.row.status ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button
                :type="scope.row.status ? 'danger' : 'success'"
                size="small"
                @click="handleToggleStatus(scope.row)"
              >
                {{ scope.row.status ? '禁用' : '启用' }}
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="panel-footer">
          <el-button type="primary" @click="handleAddMapping">添加银行编码</el-button>
        </div>
      </div>
    </div>

    <!-- 编辑/添加弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isAddMode ? '添加银行支付方式' : '编辑银行支付方式'"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="银行" v-if="isAddMode">
          <el-select v-model="editForm.bank_id" placeholder="请选择银行">
            <el-option
              v-for="bank in availableBanks"
              :key="bank.id"
              :label="bank.name"
              :value="bank.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="银行" v-else>
          <el-input v-model="editForm.bank_name" disabled />
        </el-form-item>
        <el-form-item :label="isBasePayChannel ? '代收编码(pay_type)' : '代收方式'">
          <el-input-number
            v-if="!isBasePayChannel"
            v-model="editForm.payin_method"
            placeholder="请输入代收方式编号"
            :min="1"
            :max="9999"
            style="width: 100%"
          />
          <el-input
            v-else
            v-model="editForm.payin_method"
            placeholder="请输入代收编码，如：1720"
            clearable
            style="width: 100%"
          />
          <div v-if="isBasePayChannel" class="form-tip">Base支付代收编码，如：1720（菲律宾二类）</div>
        </el-form-item>

        <el-form-item :label="isBasePayChannel ? '代付编码(bank_code)' : '代付方式'">
          <el-input-number
            v-if="!isBasePayChannel"
            v-model="editForm.payout_method"
            placeholder="请输入代付方式编号"
            :min="1"
            :max="9999"
            style="width: 100%"
          />
          <el-input
            v-else
            v-model="editForm.payout_method"
            placeholder="请输入代付编码，如：IDPT0001"
            clearable
            style="width: 100%"
          />
          <div v-if="isBasePayChannel" class="form-tip">Base支付代付编码，如：IDPT0001</div>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="editForm.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveMapping">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import bankMappingService from '@/api/services/bankMappingService';
import paymentChannelService from '@/api/services/paymentChannelService';

const props = defineProps({
  paymentChannelId: {
    type: Number,
    required: true
  },
  paymentChannelName: {
    type: String,
    default: '未知'
  },
  paymentChannelCode: {
    type: String,
    default: ''
  }
});

// 计算属性：判断是否为Base支付通道
const isBasePayChannel = computed(() => {
  return props.paymentChannelCode === 'basepay' || props.paymentChannelName.toLowerCase().includes('base');
});

// 计算属性：判断是否为KBPay支付通道
const isKBPayChannel = computed(() => {
  return props.paymentChannelCode === 'kbpay' || props.paymentChannelName.toLowerCase().includes('kbpay');
});

// 计算属性：判断是否需要显示Tab布局（BasePay或KBPay）
const shouldShowTabs = computed(() => {
  return isBasePayChannel.value || isKBPayChannel.value;
});

// 状态变量
const loading = ref(false);
const mappings = ref([]);
const availableBanks = ref([]);
const editDialogVisible = ref(false);
const isAddMode = ref(false);
const editForm = ref({
  id: null,
  bank_id: null,
  bank_name: '',
  payin_method: null,
  payout_method: null,
  status: true
});

// Tab相关状态
const activeTab = ref('bankMapping');

// 商户配置相关状态
const merchantConfigLoading = ref(false);
const merchantConfigSaving = ref(false);
const merchantConfigForm = ref({
  merchant_no: '',
  payin_key: '',
  payout_key: ''
});
const originalMerchantConfig = ref({});

// 获取银行编码映射
const fetchMappings = async () => {
  loading.value = true;
  try {


    if (!props.paymentChannelId) {
      console.error('支付通道ID为空，无法获取银行编码映射');
      ElMessage.warning('支付通道ID无效，请重试');
      loading.value = false;
      return;
    }

    const response = await bankMappingService.getBankMappings({
      payment_channel_id: props.paymentChannelId
    });



    // 处理响应格式
    // 由于我们修改了响应拦截器，现在返回的可能是完整的响应对象 { code, message, data }
    if (response && typeof response === 'object') {
      if (response.code === 200) {
        // 新的响应格式：{ code: 200, message: '获取成功', data: [...] }
        mappings.value = response.data || [];

      } else if (Array.isArray(response)) {
        // 旧的响应格式：直接是数组
        mappings.value = response;

      } else {
        console.error('获取银行编码映射失败:', response.message);
        ElMessage.error(response.message || '获取银行编码映射失败');
      }
    } else {
      console.error('获取银行编码映射响应格式错误');
      ElMessage.error('获取银行编码映射失败，响应格式错误');
    }
  } catch (error) {
    console.error('获取银行编码映射错误:', error);
    ElMessage.error('获取银行编码映射失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
};

// 获取所有银行
const fetchBanks = async () => {
  try {
    console.log('正在获取所有银行');
    const response = await bankMappingService.getAllBanks();

    console.log('获取银行列表响应:', response);

    // 处理响应格式
    // 由于我们修改了响应拦截器，现在返回的可能是完整的响应对象 { code, message, data }
    if (response && typeof response === 'object') {
      if (response.code === 200) {
        // 新的响应格式：{ code: 200, message: '获取成功', data: [...] }
        availableBanks.value = response.data || [];
        console.log('成功获取银行列表，数量:', availableBanks.value.length);
      } else if (Array.isArray(response)) {
        // 旧的响应格式：直接是数组
        availableBanks.value = response;
        console.log('成功获取银行列表，数量:', availableBanks.value.length);
      } else {
        console.error('获取银行列表失败:', response.message);
        ElMessage.error(response.message || '获取银行列表失败');
      }
    } else {
      console.error('获取银行列表响应格式错误');
      ElMessage.error('获取银行列表失败，响应格式错误');
    }
  } catch (error) {
    console.error('获取银行列表错误:', error);
    ElMessage.error('获取银行列表失败，请检查网络连接');
  }
};

// 编辑银行支付方式
const handleEdit = (row) => {
  isAddMode.value = false;
  editForm.value = {
    id: row.id,
    bank_id: row.bank_id,
    bank_name: row.bank?.name || '',
    payin_method: row.payin_method,
    payout_method: row.payout_method,
    status: row.status
  };
  editDialogVisible.value = true;
};

// 添加银行支付方式
const handleAddMapping = async () => {
  await fetchBanks();

  // 过滤掉已有映射的银行
  const existingBankIds = mappings.value.map(m => m.bank_id);
  availableBanks.value = availableBanks.value.filter(bank => !existingBankIds.includes(bank.id));

  if (availableBanks.value.length === 0) {
    ElMessage.warning('所有银行都已配置支付方式，无法添加新的映射');
    return;
  }

  isAddMode.value = true;
  editForm.value = {
    id: null,
    bank_id: null,
    bank_name: '',
    payin_method: null,
    payout_method: null,
    status: true
  };
  editDialogVisible.value = true;
};

// 切换状态
const handleToggleStatus = async (row) => {
  try {
    const response = await bankMappingService.upsertBankMapping({
      id: row.id,
      bank_id: row.bank_id,
      payment_channel_id: props.paymentChannelId,
      bank_code: row.bank_code,
      status: !row.status
    });

    // 处理响应格式
    if (response && typeof response === 'object' && response.code === 200) {
      ElMessage.success(`${row.status ? '禁用' : '启用'}成功`);
      fetchMappings();
    } else {
      const message = response && response.message ? response.message : `${row.status ? '禁用' : '启用'}失败`;
      ElMessage.error(message);
    }
  } catch (error) {
    console.error(`${row.status ? '禁用' : '启用'}银行编码映射错误:`, error);
    ElMessage.error(`${row.status ? '禁用' : '启用'}失败`);
  }
};

// 删除银行编码映射
const handleDelete = async (row) => {
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确定要删除 ${row.bank?.name || '未知银行'} 的银行编码映射吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 用户确认删除，调用删除API
    const response = await bankMappingService.deleteBankMapping(row.id);

    // 处理响应格式
    if (response && typeof response === 'object' && response.code === 200) {
      ElMessage.success('删除成功');
      fetchMappings(); // 刷新列表
    } else {
      const message = response && response.message ? response.message : '删除失败';
      ElMessage.error(message);
    }
  } catch (error) {
    // 如果是用户取消操作，不显示错误
    if (error === 'cancel' || error.toString().includes('cancel')) {
      return;
    }

    console.error('删除银行编码映射错误:', error);
    ElMessage.error('删除失败，请稍后重试');
  }
};

// 保存映射
const saveMapping = async () => {
  if (isAddMode.value && !editForm.value.bank_id) {
    ElMessage.warning('请选择银行');
    return;
  }

  try {
    const data = {
      bank_id: editForm.value.bank_id,
      payment_channel_id: props.paymentChannelId,
      payin_method: editForm.value.payin_method,
      payout_method: editForm.value.payout_method,
      status: editForm.value.status
    };

    if (!isAddMode.value && editForm.value.id) {
      data.id = editForm.value.id;
    }

    const response = await bankMappingService.upsertBankMapping(data);

    // 处理响应格式
    if (response && typeof response === 'object' && response.code === 200) {
      ElMessage.success('保存成功');
      editDialogVisible.value = false;
      fetchMappings();
    } else {
      const message = response && response.message ? response.message : '保存失败';
      ElMessage.error(message);
    }
  } catch (error) {
    console.error('保存银行编码映射错误:', error);
    ElMessage.error('保存失败');
  }
};

// 获取商户配置
const fetchMerchantConfig = async () => {
  if (!shouldShowTabs.value) return;

  merchantConfigLoading.value = true;
  try {
    const channelType = isBasePayChannel.value ? 'Base支付' : 'KBPay';


    const response = await paymentChannelService.getPaymentChannel(props.paymentChannelId);

    if (response && response.code === 200 && response.data) {
      const channel = response.data;

      // 解析配置
      let config = {};
      if (channel.config) {
        try {
          config = typeof channel.config === 'string' ? JSON.parse(channel.config) : channel.config;
        } catch (error) {
          console.warn('解析商户配置失败:', error);
        }
      }

      // 填充表单
      merchantConfigForm.value = {
        merchant_no: config.merchant_no || '',
        payin_key: config.payin_key || '',
        payout_key: config.payout_key || ''
      };

      // 保存原始配置
      originalMerchantConfig.value = { ...merchantConfigForm.value };


    } else {
      ElMessage.error('获取商户配置失败');
    }
  } catch (error) {
    const channelType = isBasePayChannel.value ? 'Base支付' : 'KBPay';
    console.error(`获取${channelType}商户配置错误:`, error);
    ElMessage.error('获取商户配置失败，请检查网络连接');
  } finally {
    merchantConfigLoading.value = false;
  }
};

// 保存商户配置
const saveMerchantConfig = async () => {
  // 验证必填字段
  if (!merchantConfigForm.value.merchant_no.trim()) {
    ElMessage.warning('请输入商户号');
    return;
  }
  if (!merchantConfigForm.value.payin_key.trim()) {
    ElMessage.warning('请输入代收密钥');
    return;
  }
  if (!merchantConfigForm.value.payout_key.trim()) {
    ElMessage.warning('请输入代付密钥');
    return;
  }

  merchantConfigSaving.value = true;
  try {
    // 获取当前通道信息
    const channelResponse = await paymentChannelService.getPaymentChannel(props.paymentChannelId);
    if (!channelResponse || channelResponse.code !== 200 || !channelResponse.data) {
      ElMessage.error('获取支付通道信息失败');
      return;
    }

    const currentChannel = channelResponse.data;

    // 解析现有配置
    let existingConfig = {};
    if (currentChannel.config) {
      try {
        existingConfig = typeof currentChannel.config === 'string' ? JSON.parse(currentChannel.config) : currentChannel.config;
      } catch (error) {
        console.warn('解析现有配置失败:', error);
      }
    }

    // 构建新的配置对象（保留其他配置，只更新商户相关配置）
    const newConfig = {
      ...existingConfig,
      merchant_no: merchantConfigForm.value.merchant_no.trim(),
      payin_key: merchantConfigForm.value.payin_key.trim(),
      payout_key: merchantConfigForm.value.payout_key.trim()
    };

    // 构建更新请求数据
    const requestData = {
      name: currentChannel.name,
      code: currentChannel.code,
      countryCode: currentChannel.countryCode || 'PH',
      depositEnabled: currentChannel.depositEnabled,
      withdrawEnabled: currentChannel.withdrawEnabled,
      isDefault: currentChannel.isDefault,
      weight: currentChannel.weight,
      config: JSON.stringify(newConfig)
    };

    // 调用API更新配置
    const response = await paymentChannelService.updatePaymentChannel(props.paymentChannelId, requestData);

    if (response && response.code === 200) {
      ElMessage.success('商户配置保存成功');

      // 更新原始配置
      originalMerchantConfig.value = { ...merchantConfigForm.value };
    } else {
      ElMessage.error(response?.message || '保存商户配置失败');
    }
  } catch (error) {
    const channelType = isBasePayChannel.value ? 'Base支付' : 'KBPay';
    console.error(`保存${channelType}商户配置失败:`, error);
    ElMessage.error('保存商户配置失败：' + (error.message || '未知错误'));
  } finally {
    merchantConfigSaving.value = false;
  }
};

// 重置商户配置
const resetMerchantConfig = () => {
  merchantConfigForm.value = { ...originalMerchantConfig.value };
  ElMessage.info('商户配置已重置');
};

// 组件挂载时获取数据
onMounted(() => {

  if (props.paymentChannelId) {
    fetchMappings();
    // 如果是Base支付通道或KBPay支付通道，同时获取商户配置
    if (shouldShowTabs.value) {
      fetchMerchantConfig();
    }
  } else {
    console.warn('支付通道ID为空，无法获取配置');
    ElMessage.warning('支付通道ID无效，请重试');
  }
});
</script>

<style scoped>
.bank-code-mapping-panel {
  padding: 10px;
}

.panel-header {
  margin-bottom: 20px;
}

.panel-content {
  min-height: 400px;
}

.config-tabs {
  margin-top: 10px;
}

.tab-content {
  padding: 20px 0;
  min-height: 350px;
}

.panel-footer {
  margin-top: 20px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

/* 商户配置表单样式 */
.form-tip {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #495057;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
}

/* Tab样式优化 */
:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  min-height: 300px;
}
</style>
