const { UserLevel } = require('../models');
const { sequelize } = require('../config/database');
const { Op } = require('sequelize');

// 获取VIP等级列表
exports.getVipLevels = async (req, res) => {
  try {
    const levels = await UserLevel.findAll({
      order: [['level', 'ASC']]
    });

    // 格式化数据
    const formattedLevels = levels.map(level => ({
      id: level.id,
      name: level.name,
      level: level.level,
      upgradeUsers: level.upgrade_users,
      upgradeAmount: level.upgrade_amount,
      returnRate: level.return_rate,
      upgradeBonus: level.upgrade_bonus,
      imageUrl: level.image ? level.image.file_path : null
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: formattedLevels
    });
  } catch (error) {
    console.error('获取VIP等级列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取VIP类型列表
exports.getVipTypes = async (req, res) => {
  try {
    // VIP类型是固定的，不需要从数据库获取
    const types = [
      { id: 1, name: '等于', value: '=' },
      { id: 2, name: '大于等于', value: '>=' },
      { id: 3, name: '小于等于', value: '<=' }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: types
    });
  } catch (error) {
    console.error('获取VIP类型列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取VIP等级详情
exports.getVipLevel = async (req, res) => {
  try {
    const { id } = req.params;

    const level = await UserLevel.findByPk(id, {
      include: [
        {
          model: sequelize.models.Attachment,
          as: 'image',
          attributes: ['id', 'file_path'],
          required: false
        }
      ]
    });

    if (!level) {
      return res.status(404).json({
        code: 404,
        message: 'VIP等级不存在',
        data: null
      });
    }

    // 格式化数据
    const formattedLevel = {
      id: level.id,
      name: level.name,
      level: level.level,
      upgradeUsers: level.upgrade_users,
      upgradeAmount: level.upgrade_amount,
      returnRate: level.return_rate,
      upgradeBonus: level.upgrade_bonus,
      imageUrl: level.image ? level.image.file_path : null,
      content: level.content,
      createTime: level.created_at,
      updateTime: level.updated_at
    };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: formattedLevel
    });
  } catch (error) {
    console.error('获取VIP等级详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 创建VIP等级
exports.createVipLevel = async (req, res) => {
  try {
    const {
      name,
      level,
      upgradeUsers,
      upgradeAmount,
      returnRate,
      upgradeBonus,
      imageId,
      content
    } = req.body;

    // 验证请求数据
    if (!name || level === undefined) {
      return res.status(400).json({
        code: 400,
        message: '名称和级别不能为空',
        data: null
      });
    }

    // 检查级别是否已存在
    const existingLevel = await UserLevel.findOne({
      where: { level }
    });

    if (existingLevel) {
      return res.status(409).json({
        code: 409,
        message: '该级别已存在',
        data: null
      });
    }

    // 创建VIP等级
    const vipLevel = await UserLevel.create({
      name,
      level,
      upgrade_users: upgradeUsers || 0,
      upgrade_amount: upgradeAmount || 0,
      return_rate: returnRate || 0,
      upgrade_bonus: upgradeBonus || 0,
      image_id: imageId || null,
      content: content || null
    });

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: vipLevel
    });
  } catch (error) {
    console.error('创建VIP等级错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新VIP等级
exports.updateVipLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      level,
      upgradeUsers,
      upgradeAmount,
      returnRate,
      upgradeBonus,
      imageId,
      content
    } = req.body;

    // 查找VIP等级
    const vipLevel = await UserLevel.findByPk(id);
    if (!vipLevel) {
      return res.status(404).json({
        code: 404,
        message: 'VIP等级不存在',
        data: null
      });
    }

    // 如果要更新级别，检查是否与其他记录冲突
    if (level !== undefined && level !== vipLevel.level) {
      const existingLevel = await UserLevel.findOne({
        where: {
          level,
          id: { [Op.ne]: id }
        }
      });

      if (existingLevel) {
        return res.status(409).json({
          code: 409,
          message: '该级别已存在',
          data: null
        });
      }
    }

    // 更新VIP等级
    await vipLevel.update({
      name: name !== undefined ? name : vipLevel.name,
      level: level !== undefined ? level : vipLevel.level,
      upgrade_users: upgradeUsers !== undefined ? upgradeUsers : vipLevel.upgrade_users,
      upgrade_amount: upgradeAmount !== undefined ? upgradeAmount : vipLevel.upgrade_amount,
      return_rate: returnRate !== undefined ? returnRate : vipLevel.return_rate,
      upgrade_bonus: upgradeBonus !== undefined ? upgradeBonus : vipLevel.upgrade_bonus,
      image_id: imageId !== undefined ? imageId : vipLevel.image_id,
      content: content !== undefined ? content : vipLevel.content
    });

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: vipLevel
    });
  } catch (error) {
    console.error('更新VIP等级错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除VIP等级
exports.deleteVipLevel = async (req, res) => {
  try {
    const { id } = req.params;

    // 查找VIP等级
    const vipLevel = await UserLevel.findByPk(id);
    if (!vipLevel) {
      return res.status(404).json({
        code: 404,
        message: 'VIP等级不存在',
        data: null
      });
    }

    // 检查是否有项目使用该VIP等级
    const projectCount = await sequelize.models.Project.count({
      where: { vip_level_id: id }
    });

    if (projectCount > 0) {
      return res.status(400).json({
        code: 400,
        message: `该VIP等级正在被${projectCount}个项目使用，无法删除`,
        data: null
      });
    }

    // 检查是否有用户使用该VIP等级
    const userCount = await sequelize.models.User.count({
      where: { level_id: id }
    });

    if (userCount > 0) {
      return res.status(400).json({
        code: 400,
        message: `该VIP等级正在被${userCount}个用户使用，无法删除`,
        data: null
      });
    }

    // 删除VIP等级
    await vipLevel.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除VIP等级错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
