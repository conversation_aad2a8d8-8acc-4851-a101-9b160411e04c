# 银行编码简化实现说明

## 📋 **简化原则**

根据业务需求，银行编码的获取逻辑已简化为：**直接使用 `payout_method` 字段，如果没有就显示 "-"**

## 🎯 **简化后的实现**

### **核心逻辑**
```javascript
// 处理银行编码 - 直接使用payout_method字段
let bankCode = '-';

if (withdrawal.bank_card && withdrawal.bank_card.bank_id && withdrawal.payment_channel_id) {
  try {
    const { BankChannelMapping } = require('../models');
    const mapping = await BankChannelMapping.findOne({
      where: {
        bank_id: withdrawal.bank_card.bank_id,
        payment_channel_id: withdrawal.payment_channel_id,
        status: true
      }
    });
    if (mapping && mapping.payout_method) {
      bankCode = mapping.payout_method.toString();
    }
  } catch (error) {
    console.error('获取银行映射代付方式失败:', error);
  }
}
```

### **逻辑说明**
1. **默认值**: 银行编码默认为 `"-"`
2. **唯一数据源**: 只从 `bank_channel_mappings.payout_method` 获取
3. **必要条件**: 需要 `bank_id` 和 `payment_channel_id` 都存在
4. **状态检查**: 只获取 `status = true` 的映射记录
5. **错误处理**: 查询失败时保持默认值 `"-"`

## 📊 **数据流程**

### **成功获取银行编码**
```
取款订单 → 银行卡ID → bank_id + payment_channel_id → 查询映射表 → payout_method → 显示编码
```

### **无法获取银行编码**
```
取款订单 → 缺少bank_id或payment_channel_id → 显示 "-"
取款订单 → 映射表中无记录 → 显示 "-"
取款订单 → payout_method为空 → 显示 "-"
```

## 🔧 **配置要求**

### **数据库配置**
要正确显示银行编码，需要确保：

1. **银行卡表有bank_id**
```sql
-- 检查银行卡是否关联了银行ID
SELECT id, bank_name, bank_id 
FROM bank_cards 
WHERE bank_id IS NULL;
```

2. **取款订单有payment_channel_id**
```sql
-- 检查取款订单是否有支付通道ID
SELECT id, order_number, payment_channel_id 
FROM withdrawals 
WHERE payment_channel_id IS NULL;
```

3. **银行映射表有payout_method**
```sql
-- 检查银行映射表的代付方式配置
SELECT 
  bcm.bank_id,
  b.name as bank_name,
  bcm.payment_channel_id,
  pc.name as channel_name,
  bcm.payout_method,
  bcm.status
FROM bank_channel_mappings bcm
LEFT JOIN banks b ON bcm.bank_id = b.id
LEFT JOIN payment_channels pc ON bcm.payment_channel_id = pc.id
WHERE bcm.payout_method IS NULL OR bcm.status = false;
```

### **配置银行编码**
```sql
-- 为银行配置代付方式编码
INSERT INTO bank_channel_mappings 
(bank_id, payment_channel_id, payout_method, status) 
VALUES (银行ID, 支付通道ID, 代付编码, true);

-- 或更新现有记录
UPDATE bank_channel_mappings 
SET payout_method = 代付编码 
WHERE bank_id = 银行ID AND payment_channel_id = 支付通道ID;
```

## 📈 **显示效果**

### **有配置的情况**
```
银行编码列显示: "101", "201", "301" 等实际的代付方式编码
```

### **无配置的情况**
```
银行编码列显示: "-"
```

## 🔍 **故障排查**

### **银行编码显示为 "-" 的原因**
1. **银行卡未关联银行**: `bank_cards.bank_id` 为空
2. **取款订单无支付通道**: `withdrawals.payment_channel_id` 为空
3. **映射表无记录**: `bank_channel_mappings` 表中没有对应记录
4. **代付方式未配置**: `bank_channel_mappings.payout_method` 为空
5. **映射记录被禁用**: `bank_channel_mappings.status` 为 false

### **排查SQL**
```sql
-- 完整的排查查询
SELECT 
  w.id as withdrawal_id,
  w.order_number,
  bc.id as bank_card_id,
  bc.bank_name,
  bc.bank_id,
  w.payment_channel_id,
  bcm.payout_method,
  bcm.status as mapping_status,
  CASE 
    WHEN bc.bank_id IS NULL THEN '银行卡未关联银行ID'
    WHEN w.payment_channel_id IS NULL THEN '取款订单无支付通道ID'
    WHEN bcm.id IS NULL THEN '映射表中无对应记录'
    WHEN bcm.payout_method IS NULL THEN '代付方式未配置'
    WHEN bcm.status = false THEN '映射记录已禁用'
    ELSE '配置正常'
  END as status_desc
FROM withdrawals w
LEFT JOIN bank_cards bc ON w.bank_card_id = bc.id
LEFT JOIN bank_channel_mappings bcm ON bc.bank_id = bcm.bank_id 
  AND w.payment_channel_id = bcm.payment_channel_id 
  AND bcm.status = true
WHERE w.status IN ('completed', 'processing')
ORDER BY w.created_at DESC
LIMIT 10;
```

## 🎉 **简化的优势**

### **代码简洁**
- ✅ 逻辑清晰，只有一个数据源
- ✅ 代码量少，易于维护
- ✅ 错误处理简单

### **业务明确**
- ✅ 银行编码就是代付方式编码
- ✅ 没有配置就显示 "-"，一目了然
- ✅ 不会产生误导性的假数据

### **维护简单**
- ✅ 只需要维护银行映射表
- ✅ 配置清晰，容易排查问题
- ✅ 数据来源单一，不会混乱

## 📝 **总结**

简化后的银行编码获取逻辑：
- **数据源**: 只使用 `bank_channel_mappings.payout_method`
- **默认值**: 没有配置时显示 `"-"`
- **条件**: 需要 `bank_id` 和 `payment_channel_id`
- **状态**: 只获取启用状态的映射记录

这样的设计简洁明了，符合实际业务需求，易于理解和维护。
