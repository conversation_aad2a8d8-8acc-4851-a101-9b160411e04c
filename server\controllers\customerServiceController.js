/**
 * 客服管理控制器
 */
const { CustomerService } = require('../models');
const { Op } = require('sequelize');
const QRCode = require('qrcode');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// 获取客服列表
exports.getCustomerServices = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      title,
      status,
      weightMin,
      weightMax,
      url,
      sort = 'weight',
      order = 'asc'
    } = req.query;

    // 构建查询条件
    const where = {};

    if (type) {
      where.type = type;
    }

    if (title) {
      where.title = { [Op.like]: `%${title}%` };
    }

    if (status !== undefined && status !== '') {
      where.status = status === 'true' || status === '1' || status === true ? true : false;
    }

    if (url) {
      where.url = { [Op.like]: `%${url}%` };
    }

    if (weightMin) {
      where.weight = { ...where.weight, [Op.gte]: parseInt(weightMin) };
    }

    if (weightMax) {
      where.weight = { ...where.weight, [Op.lte]: parseInt(weightMax) };
    }

    // 构建排序条件
    const orderBy = [];

    // 验证排序字段
    const validSortFields = ['id', 'type', 'title', 'weight', 'status', 'created_at', 'updated_at'];
    const sortField = validSortFields.includes(sort) ? sort : 'weight';

    // 验证排序方向
    const orderDirection = order.toLowerCase() === 'desc' ? 'DESC' : 'ASC';

    // 添加主排序
    orderBy.push([sortField, orderDirection]);

    // 如果主排序不是ID，添加ID作为次要排序
    if (sortField !== 'id') {
      orderBy.push(['id', 'DESC']);
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await CustomerService.findAndCountAll({
      where,
      order: orderBy,
      offset: parseInt(offset),
      limit: parseInt(limit)
    });

    // 格式化数据
    const items = rows.map(item => ({
      id: item.id,
      type: item.type,
      title: item.title,
      url: item.url,
      status: item.status,
      icon: item.icon,
      weight: item.weight,
      description: item.description,
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items
      }
    });
  } catch (error) {
    console.error('获取客服列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取客服详情
exports.getCustomerServiceById = async (req, res) => {
  try {
    const { id } = req.params;

    const customerService = await CustomerService.findByPk(id);

    if (!customerService) {
      return res.status(404).json({
        code: 404,
        message: '客服不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        id: customerService.id,
        type: customerService.type,
        title: customerService.title,
        url: customerService.url,
        status: customerService.status,
        icon: customerService.icon,
        weight: customerService.weight,
        description: customerService.description,
        created_at: customerService.created_at,
        updated_at: customerService.updated_at
      }
    });
  } catch (error) {
    console.error('获取客服详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 创建客服
exports.createCustomerService = async (req, res) => {
  try {
    const { type, title, url, status, icon, weight, description } = req.body;

    // 验证必填字段
    if (!type || !title || !url) {
      return res.status(400).json({
        code: 400,
        message: '类型、标题和URL为必填项',
        data: null
      });
    }

    // 创建客服
    const customerService = await CustomerService.create({
      type,
      title,
      url,
      status: status !== undefined ? status : true,
      icon: icon || '',
      weight: weight || 0,
      description: description || ''
    });

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: {
        id: customerService.id,
        type: customerService.type,
        title: customerService.title,
        url: customerService.url,
        status: customerService.status,
        icon: customerService.icon,
        weight: customerService.weight,
        description: customerService.description,
        created_at: customerService.created_at,
        updated_at: customerService.updated_at
      }
    });
  } catch (error) {
    console.error('创建客服错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新客服
exports.updateCustomerService = async (req, res) => {
  try {
    const { id } = req.params;
    const { type, title, url, status, icon, weight, description } = req.body;

    const customerService = await CustomerService.findByPk(id);

    if (!customerService) {
      return res.status(404).json({
        code: 404,
        message: '客服不存在',
        data: null
      });
    }

    // 确保状态值是布尔值或0/1
    let statusValue = customerService.status;
    if (status !== undefined) {
      // 将状态转换为布尔值或0/1
      if (status === true || status === 1 || status === '1') {
        statusValue = true;
      } else if (status === false || status === 0 || status === '0') {
        statusValue = false;
      }
    }

    // 更新客服
    await customerService.update({
      type: type !== undefined ? type : customerService.type,
      title: title !== undefined ? title : customerService.title,
      url: url !== undefined ? url : customerService.url,
      status: statusValue,
      icon: icon !== undefined ? icon : customerService.icon,
      weight: weight !== undefined ? weight : customerService.weight,
      description: description !== undefined ? description : customerService.description
    });

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: {
        id: customerService.id,
        type: customerService.type,
        title: customerService.title,
        url: customerService.url,
        status: customerService.status,
        icon: customerService.icon,
        weight: customerService.weight,
        description: customerService.description,
        updated_at: customerService.updated_at
      }
    });
  } catch (error) {
    console.error('更新客服错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除客服
exports.deleteCustomerService = async (req, res) => {
  try {
    const { id } = req.params;

    const customerService = await CustomerService.findByPk(id);

    if (!customerService) {
      return res.status(404).json({
        code: 404,
        message: '客服不存在',
        data: null
      });
    }

    // 删除客服
    await customerService.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除客服错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 批量删除客服
exports.batchDeleteCustomerServices = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请选择要删除的客服',
        data: null
      });
    }

    // 批量删除客服
    await CustomerService.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('批量删除客服错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取客服二维码
exports.getCustomerServiceQRCode = async (req, res) => {
  try {
    const { id } = req.params;

    const customerService = await CustomerService.findByPk(id);

    if (!customerService) {
      return res.status(404).json({
        code: 404,
        message: '客服不存在',
        data: null
      });
    }

    // 检查客服状态
    if (!customerService.status) {
      return res.status(400).json({
        code: 400,
        message: '该客服已禁用',
        data: null
      });
    }

    // 生成二维码
    const qrCodeDataURL = await QRCode.toDataURL(customerService.url, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 300,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        id: customerService.id,
        type: customerService.type,
        title: customerService.title,
        url: customerService.url,
        qrcode: qrCodeDataURL
      }
    });
  } catch (error) {
    console.error('获取客服二维码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新客服权重
exports.updateCustomerServiceWeight = async (req, res) => {
  try {
    const { id } = req.params;
    const { weight } = req.body;

    if (weight === undefined || isNaN(parseInt(weight))) {
      return res.status(400).json({
        code: 400,
        message: '权重值无效',
        data: null
      });
    }

    const customerService = await CustomerService.findByPk(id);

    if (!customerService) {
      return res.status(404).json({
        code: 404,
        message: '客服不存在',
        data: null
      });
    }

    // 更新权重
    await customerService.update({
      weight: parseInt(weight)
    });

    return res.status(200).json({
      code: 200,
      message: '更新权重成功',
      data: {
        id: customerService.id,
        weight: customerService.weight
      }
    });
  } catch (error) {
    console.error('更新客服权重错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 批量更新客服权重
exports.updateCustomerServiceWeights = async (req, res) => {
  try {
    const updates = req.body;

    if (!Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的权重更新数据',
        data: null
      });
    }

    // 批量更新权重
    const updatePromises = updates.map(async (item) => {
      const { id, weight } = item;

      if (!id || weight === undefined || isNaN(parseInt(weight))) {
        return null;
      }

      const customerService = await CustomerService.findByPk(id);

      if (!customerService) {
        return null;
      }

      return customerService.update({
        weight: parseInt(weight)
      });
    });

    await Promise.all(updatePromises.filter(Boolean));

    return res.status(200).json({
      code: 200,
      message: '批量更新权重成功',
      data: null
    });
  } catch (error) {
    console.error('批量更新客服权重错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取移动端客服列表
exports.getMobileCustomerServices = async (req, res) => {
  try {
    // 只获取启用状态的客服
    const customerServices = await CustomerService.findAll({
      where: {
        status: true
      },
      order: [['weight', 'ASC'], ['id', 'DESC']] // 权重小的排在前面
    });

    // 格式化数据
    const items = await Promise.all(customerServices.map(async (item) => {
      // 生成二维码
      const qrCodeDataURL = await QRCode.toDataURL(item.url, {
        errorCorrectionLevel: 'H',
        margin: 1,
        width: 300,
        color: {
          dark: '#000000',
          light: '#ffffff'
        }
      });

      return {
        id: item.id,
        type: item.type,
        title: item.title,
        description: item.description,
        url: item.url,
        icon: item.icon,
        qrcode: qrCodeDataURL
      };
    }));

    return res.status(200).json({
      code: 200,
      message: 'Success',
      data: items
    });
  } catch (error) {
    console.error('获取移动端客服列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
