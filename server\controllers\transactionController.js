const { Transaction, User } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 管理员端 - 获取所有交易记录
exports.getAllTransactions = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      user_id,
      type,
      status,
      start_date,
      end_date,
      keyword,
      amount_min,
      amount_max,
      before_balance_min,
      before_balance_max,
      balance_min,
      balance_max
    } = req.query;

    // 构建查询条件
    const where = {};

    // 根据用户ID筛选
    if (user_id) {
      where.user_id = user_id;
    }

    // 根据交易类型筛选
    if (type) {
      where.type = type;
    }

    // 根据交易状态筛选
    if (status) {
      where.status = status;
    }

    // 根据金额范围筛选
    if (amount_min || amount_max) {
      where.amount = {};
      if (amount_min) {
        where.amount[Op.gte] = parseFloat(amount_min);
      }
      if (amount_max) {
        where.amount[Op.lte] = parseFloat(amount_max);
      }
    }

    // 根据交易前余额范围筛选
    if (before_balance_min || before_balance_max) {
      where.before_balance = {};
      if (before_balance_min) {
        where.before_balance[Op.gte] = parseFloat(before_balance_min);
      }
      if (before_balance_max) {
        where.before_balance[Op.lte] = parseFloat(before_balance_max);
      }
    }

    // 根据交易后余额范围筛选
    if (balance_min || balance_max) {
      where.balance = {};
      if (balance_min) {
        where.balance[Op.gte] = parseFloat(balance_min);
      }
      if (balance_max) {
        where.balance[Op.lte] = parseFloat(balance_max);
      }
    }

    // 根据日期范围筛选
    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // 关键词搜索（用户名或描述字段）
    if (keyword) {
      // 使用OR条件，同时搜索描述字段和用户名
      where[Op.or] = [
        { description: { [Op.like]: `%${keyword}%` } },
        { '$user.username$': { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Transaction.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取交易记录列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取交易记录详情
exports.getTransactionById = async (req, res) => {
  try {
    const { id } = req.params;

    const transaction = await Transaction.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email']
        }
      ]
    });

    if (!transaction) {
      return res.status(404).json({
        code: 404,
        message: '交易记录不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: transaction
    });
  } catch (error) {
    console.error('获取交易记录详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户端 - 获取当前用户的交易记录
exports.getUserTransactions = async (req, res) => {
  try {
    const userId = req.user.id; // 从认证中间件获取当前用户ID
    const {
      page = 1,
      limit = 10,
      type,
      start_date,
      end_date,
      amount_min,
      amount_max,
      before_balance_min,
      before_balance_max,
      balance_min,
      balance_max
    } = req.query;

    // 构建查询条件
    const where = { user_id: userId };

    // 根据交易类型筛选
    if (type && type !== 'all') {
      where.type = type;
    }

    // 根据金额范围筛选
    if (amount_min || amount_max) {
      where.amount = {};
      if (amount_min) {
        where.amount[Op.gte] = parseFloat(amount_min);
      }
      if (amount_max) {
        where.amount[Op.lte] = parseFloat(amount_max);
      }
    }

    // 根据交易前余额范围筛选
    if (before_balance_min || before_balance_max) {
      where.before_balance = {};
      if (before_balance_min) {
        where.before_balance[Op.gte] = parseFloat(before_balance_min);
      }
      if (before_balance_max) {
        where.before_balance[Op.lte] = parseFloat(before_balance_max);
      }
    }

    // 根据交易后余额范围筛选
    if (balance_min || balance_max) {
      where.balance = {};
      if (balance_min) {
        where.balance[Op.gte] = parseFloat(balance_min);
      }
      if (balance_max) {
        where.balance[Op.lte] = parseFloat(balance_max);
      }
    }

    // 根据日期范围筛选
    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Transaction.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 格式化返回数据
    const formattedTransactions = rows.map(transaction => ({
      id: transaction.id,
      order_number: transaction.order_number,
      amount: transaction.amount,
      before_balance: transaction.before_balance,
      balance: transaction.balance,
      type: transaction.type,
      status: transaction.status,
      time: transaction.created_at,
      remark: transaction.description
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        list: formattedTransactions
      }
    });
  } catch (error) {
    console.error('获取用户交易记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 导入订单号生成工具
const { generateOrderNumberByType } = require('../utils/orderNumberGenerator');

// 创建交易记录（内部使用，不暴露为API）
exports.createTransaction = async (userId, type, amount, before_balance, balance, description, referenceId = null, referenceType = null, status = 'success', currency = 'CNY', transaction = null) => {
  try {
    const transactionOptions = transaction ? { transaction } : {};

    // 注意：before_balance 和 balance 应该是从数据库中直接获取的值
    // 不再需要计算交易前余额，直接使用传入的值

    // 生成订单号
    const orderNumber = generateOrderNumberByType(type);

    const newTransaction = await Transaction.create({
      user_id: userId,
      order_number: orderNumber,
      type,
      amount,
      before_balance,
      balance,
      currency,
      status,
      reference_id: referenceId,
      reference_type: referenceType,
      description
    }, transactionOptions);

    return newTransaction;
  } catch (error) {
    console.error('创建交易记录错误:', error);
    throw error;
  }
};
