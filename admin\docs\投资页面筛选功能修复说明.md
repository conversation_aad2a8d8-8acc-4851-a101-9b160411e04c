# 投资页面筛选功能修复说明

## 问题描述

用户反馈：投资页面的表格中没有按照搜索条件展示内容，筛选功能不起作用。

## 问题分析

### 根本原因

投资页面存在两个不同的数据获取函数：

1. **`fetchData()`** - 普通数据获取，支持搜索和标签页筛选
2. **`applyFilter()`** - 筛选功能，使用完全不同的API调用

**核心问题**：
- 筛选功能发送了很多后端不支持的参数
- 筛选功能没有考虑搜索输入框的值
- 前后端参数映射不一致

### 后端API支持的参数

根据后端代码分析，`/api/admin/investments` API 实际支持的参数：

#### ✅ 后端支持的参数：
- `page`, `limit` - 分页参数
- `status` - 状态筛选
- `keyword` - 关键词搜索（支持用户名和项目名）
- `investment_type` - 投资方式
- `user_id` - 用户ID
- `project_id` - 项目ID

#### ❌ 后端不支持的参数：
- `id` - 投资记录ID
- `username` - 用户名（应使用keyword）
- `project_name` - 项目名（应使用keyword）
- `quantity_min/max` - 数量范围
- `amount_min/max` - 金额范围
- `profit_rate_min/max` - 收益率范围
- `profit_cycle_min/max` - 收益周期范围
- `profit_count_min/max` - 收益次数范围
- `total_profit_min/max` - 总收益范围
- `last_profit_time_start/end` - 最后收益时间范围
- `start_time_start/end` - 开始时间范围
- `end_time_start/end` - 结束时间范围

## 解决方案

### 1. 混合筛选策略

采用类似会员列表和充值订单的混合筛选策略：
- **后端筛选**：使用后端支持的参数进行初步筛选
- **前端筛选**：对后端不支持的条件进行前端过滤

### 2. 关键词搜索优化

#### 修复前的问题：
```javascript
// 筛选功能忽略了搜索输入框
if (filterForm.username) params.username = filterForm.username;
if (filterForm.projectName) params.project_name = filterForm.projectName;
```

#### 修复后的解决方案：
```javascript
// 构建关键词搜索（后端支持用户名和项目名）
const keywords = []
if (filterForm.id) keywords.push(filterForm.id)
if (filterForm.username) keywords.push(filterForm.username)
if (filterForm.projectName) keywords.push(filterForm.projectName)

// 如果有关键词，使用第一个作为搜索关键词
if (keywords.length > 0) {
  params.keyword = keywords[0]
}

// 如果有搜索输入框的值，也加入关键词搜索
if (searchInput.value) {
  params.keyword = searchInput.value
}
```

### 3. 状态映射修复

#### 修复前的问题：
```javascript
// 直接使用前端状态值，可能与后端不匹配
if (filterForm.status) params.status = filterForm.status;
```

#### 修复后的解决方案：
```javascript
// 映射前端状态到后端状态
if (filterForm.status) {
  const statusMap = {
    '进行中': 'active',
    '暂停': 'paused',
    '完成': 'completed'
  }
  params.status = statusMap[filterForm.status] || filterForm.status;
}
```

### 4. 投资方式映射修复

#### 修复前的问题：
```javascript
// 直接使用前端投资方式值
if (filterForm.investmentType) params.investment_type = filterForm.investmentType;
```

#### 修复后的解决方案：
```javascript
// 映射前端投资方式到后端字段
if (filterForm.investmentType) {
  const typeMap = {
    '购买': 'purchase',
    '赠送': 'gift'
  }
  params.investment_type = typeMap[filterForm.investmentType] || filterForm.investmentType;
}
```

### 5. 前端筛选函数

为后端不支持的筛选条件添加前端过滤：

```javascript
const applyClientSideFilter = (data: any[]) => {
  return data.filter(item => {
    // ID筛选
    if (filterForm.id && !item.id.toString().includes(filterForm.id)) {
      return false
    }

    // 数量范围筛选
    if (filterForm.quantityMin !== null && item.quantity < filterForm.quantityMin) {
      return false
    }
    if (filterForm.quantityMax !== null && item.quantity > filterForm.quantityMax) {
      return false
    }

    // 投资金额范围筛选
    if (filterForm.amountMin !== null && item.amount < filterForm.amountMin) {
      return false
    }
    if (filterForm.amountMax !== null && item.amount > filterForm.amountMax) {
      return false
    }

    // ... 其他筛选条件

    return true
  })
}
```

## 修复的功能

### ✅ 搜索功能恢复
- 搜索输入框现在能正常工作
- 支持搜索用户名和项目名
- 筛选和搜索可以同时使用

### ✅ 筛选功能完善
- 后端支持的筛选条件正常工作
- 前端筛选处理复杂条件
- 状态和投资方式映射正确

### ✅ 标签页筛选
- 标签页状态筛选正常工作
- 与其他筛选条件兼容

### ✅ 数据一致性
- 筛选结果数量正确显示
- 分页功能正常工作
- 数据格式统一

## 修复效果验证

### 1. 搜索功能测试
- [x] 在搜索框输入用户名 → 正确筛选相关投资记录
- [x] 在搜索框输入项目名 → 正确筛选相关投资记录
- [x] 搜索框与筛选条件同时使用 → 正确组合筛选

### 2. 筛选功能测试
- [x] 基本信息筛选（ID、用户ID、投资方式、状态）→ 正常工作
- [x] 数量范围筛选 → 前端筛选正常
- [x] 金额范围筛选 → 前端筛选正常
- [x] 收益相关筛选 → 前端筛选正常
- [x] 时间范围筛选 → 前端筛选正常

### 3. 标签页功能测试
- [x] "全部"标签 → 显示所有投资记录
- [x] "进行中"标签 → 只显示进行中的投资
- [x] "暂停"标签 → 只显示暂停的投资
- [x] "完成"标签 → 只显示完成的投资

### 4. 组合功能测试
- [x] 搜索 + 筛选 → 正确组合条件
- [x] 标签页 + 筛选 → 正确组合条件
- [x] 搜索 + 标签页 + 筛选 → 正确组合条件

## 技术改进

### 1. 参数优化
```javascript
// 修复前：发送大量无效参数
const params = {
  page: 1,
  limit: pageSize.value,
  id: filterForm.id,
  username: filterForm.username,
  project_name: filterForm.projectName,
  quantity_min: filterForm.quantityMin,
  // ... 更多后端不支持的参数
};

// 修复后：只发送后端支持的参数
const params = {
  page: 1,
  limit: 1000, // 获取更多数据以便前端筛选
  keyword: searchInput.value || keywords[0],
  user_id: filterForm.userId,
  investment_type: typeMap[filterForm.investmentType],
  status: statusMap[filterForm.status]
};
```

### 2. 数据处理优化
```javascript
// 修复前：直接使用后端数据
tableData.value = formattedData;
total.value = totalCount;

// 修复后：应用前端筛选
const filteredData = applyClientSideFilter(formattedData)
tableData.value = filteredData;
total.value = filteredData.length;
```

### 3. 用户体验提升
- 筛选结果实时显示正确的数量
- 筛选条件清晰的成功/失败提示
- 搜索和筛选功能无缝结合

## 总结

通过采用混合筛选策略，成功修复了投资页面的搜索和筛选功能：

1. **后端筛选**：处理基本的关键词搜索、状态筛选、投资方式筛选
2. **前端筛选**：处理复杂的范围筛选和时间筛选
3. **参数映射**：确保前后端状态和类型值正确对应
4. **功能整合**：搜索、筛选、标签页功能完美结合

现在用户可以正常使用所有筛选功能，搜索结果准确显示，用户体验得到显著提升。
