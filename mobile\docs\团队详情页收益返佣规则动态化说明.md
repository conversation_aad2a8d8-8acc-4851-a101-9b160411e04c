# 团队详情页收益返佣规则动态化说明

## 🎯 **修改概述**

将团队详情页面底部的提示文字改为从系统参数中动态获取，由管理员在"系统参数 → 收益返佣规则"中创建和管理内容。

## 🔧 **实现方案**

### **1. 前端修改**

#### **模板结构调整**
```html
<!-- 修改前：固定文字，只在有成员时显示 -->
<view class="team-list" v-else-if="filteredMembers.length > 0">
  <!-- 成员列表 -->
  <view class="commission-note">
    <text class="note-text">
      💡 Commission shows the total referral earnings you've received from each team member.
    </text>
  </view>
</view>

<!-- 修改后：动态内容，所有级别都显示 -->
<view class="team-list" v-else-if="filteredMembers.length > 0">
  <!-- 成员列表 -->
</view>
<view class="empty-state" v-else>
  <text class="empty-text">No team members yet</text>
</view>
<!-- 收益返佣规则说明 - 移到外层，所有级别都显示 -->
<view class="commission-note" v-if="commissionRules">
  <view class="note-content" v-html="commissionRules"></view>
</view>
```

#### **数据字段添加**
```javascript
data() {
  return {
    // ... 其他字段
    commissionRules: '' // 收益返佣规则
  };
}
```

#### **API调用添加**
```javascript
async onLoad(options) {
  // 获取收益返佣比例配置
  await this.fetchCommissionRates();
  
  // 获取收益返佣规则 ⭐ 新增
  await this.fetchCommissionRules();
  
  // 验证当前级别是否有效
  this.validateCurrentLevel();
  
  // 获取团队成员
  this.fetchTeamMembers();
}
```

#### **获取规则方法**
```javascript
// 获取收益返佣规则
async fetchCommissionRules() {
  try {
    const response = await getSystemParam('[site.income_notice]');

    if (response && response.code === 200 && response.data && response.data.param_value) {
      this.commissionRules = response.data.param_value;
    }
  } catch (error) {
    console.error('获取收益返佣规则失败:', error);
    // 如果获取失败，不显示规则
    this.commissionRules = '';
  }
}
```

### **2. 样式处理**

#### **移除固定样式**
```scss
/* 修改前：固定样式 */
.commission-note {
  margin-top: 30rpx;
  padding: 20rpx;
  background: rgba(0, 229, 255, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 229, 255, 0.3);
}

.note-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  text-align: center;
}

/* 修改后：最小样式 */
.commission-note {
  margin-top: 30rpx;
  /* 内容样式由管理员在系统参数中控制 */
}
```

## 📝 **系统参数配置**

### **参数名称**
- **参数键**: `[site.income_notice]`
- **参数名**: 收益返佣规则
- **参数类型**: 富文本/HTML

### **管理员操作流程**
1. 登录管理后台
2. 进入"系统参数"页面
3. 找到"收益返佣规则"参数
4. 编辑参数内容（支持HTML格式）
5. 保存设置

### **内容示例**
```html
<div style="padding: 20px; background: rgba(0, 229, 255, 0.1); border-radius: 12px; border: 1px solid rgba(0, 229, 255, 0.3);">
  <p style="font-size: 14px; color: rgba(255, 255, 255, 0.8); line-height: 1.5; text-align: center; margin: 0;">
    💡 Commission shows the total referral earnings you've received from each team member.
  </p>
  <p style="font-size: 12px; color: rgba(255, 255, 255, 0.6); text-align: center; margin: 10px 0 0 0;">
    Earnings commission is generated when your referrals' investments produce returns.
  </p>
</div>
```

## 🎯 **功能特点**

### **1. 灵活性**
- ✅ 管理员可以随时修改规则内容
- ✅ 支持HTML格式，可以添加样式和链接
- ✅ 可以根据业务需要调整说明文字

### **2. 多语言支持**
- ✅ 可以根据不同地区设置不同的规则说明
- ✅ 支持富文本格式，可以添加图片和链接
- ✅ 便于本地化管理

### **3. 实时更新**
- ✅ 修改后立即生效，无需重新发布应用
- ✅ 用户刷新页面即可看到最新内容
- ✅ 支持A/B测试和内容优化

### **4. 条件显示**
- ✅ 只有当规则内容存在时才显示
- ✅ 获取失败时不会显示错误内容
- ✅ 保持页面整洁性

## 🔍 **技术细节**

### **安全考虑**
- ✅ 使用 `v-html` 渲染HTML内容
- ⚠️ 需要确保管理员输入的内容安全
- ⚠️ 建议在后端对HTML内容进行过滤

### **性能优化**
- ✅ 只在页面加载时获取一次
- ✅ 获取失败时不影响其他功能
- ✅ 内容缓存在前端，减少重复请求

### **错误处理**
- ✅ API调用失败时不显示内容
- ✅ 参数不存在时不显示内容
- ✅ 不影响页面其他功能的正常使用

## 📱 **用户体验**

### **显示效果**
- **位置**: 页面底部左侧显示
- **显示条件**:
  - 如果管理员设置了规则：显示自定义的规则内容
  - 如果管理员未设置规则：不显示任何提示
  - 如果获取失败：不显示任何提示
- **显示范围**: 在所有级别下都显示（Level 1、Level 2、Level 3等）

### **内容管理**
- 管理员可以使用HTML标签控制样式
- 可以添加链接、图片等富媒体内容
- 可以根据业务需要随时调整

## ✅ **实现完成**

现在团队详情页面的提示内容完全由管理员控制：

- ✅ **动态获取**: 从系统参数 `site.income_commission_rules` 获取内容
- ✅ **富文本支持**: 支持HTML格式的内容
- ✅ **条件显示**: 只有内容存在时才显示
- ✅ **样式灵活**: 管理员可以在内容中控制样式
- ✅ **实时更新**: 修改后立即生效

## ⚠️ **问题解决**

### **原始问题**
出现 "Resource not found" 错误是因为：

1. **参数键不匹配**:
   - 管理端使用: `[site.income_notice]`
   - 移动端使用: `[site.income_commission_rules]`

2. **API路径**: `/api/mobile/system-params/key/[site.income_notice]`

### **解决方案**
✅ 修改移动端使用正确的参数键 `[site.income_notice]`
✅ 调整布局，将规则说明移到页面底部左侧，在所有级别下都显示

### **验证方法**
1. 在管理后台"系统参数 → 其它配置"中设置"收益返佣规则"内容
2. 在移动端团队详情页面查看是否显示设置的内容
3. 检查浏览器控制台是否还有错误信息

管理员现在可以在后台系统参数中创建和管理收益返佣规则的说明内容，为用户提供更准确和及时的信息。
