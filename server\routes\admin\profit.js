/**
 * 收益管理路由
 */
const express = require('express');
const router = express.Router();
const profitController = require('../../controllers/profitController');
const { verifyAdminToken } = require('../../middlewares/authMiddleware');

// 所有路由都需要管理员权限
router.use(verifyAdminToken);

// 启动收益系统
router.post('/start', profitController.startSystem);

// 停止收益系统
router.post('/stop', profitController.stopSystem);

// 执行补偿检查
router.post('/compensate', profitController.runCompensation);

// 获取所有收益任务
router.get('/tasks', profitController.getAllTasks);

// 重新初始化所有收益任务
router.post('/reinitialize', profitController.reinitializeTasks);

module.exports = router;
