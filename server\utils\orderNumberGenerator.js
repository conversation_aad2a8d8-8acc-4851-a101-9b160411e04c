/**
 * 订单号生成工具
 * 生成规则：前缀 + 年月日小时分钟 + 6位随机数字
 */

/**
 * 生成订单号
 * @param {string} prefix - 订单类型前缀
 * @returns {string} 生成的订单号
 */
function generateOrderNumber(prefix = '') {
  // 获取当前日期时间
  const now = new Date();

  // 格式化年月日小时分钟
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');

  // 生成日期时间部分
  const dateTimePart = `${year}${month}${day}${hours}${minutes}`;

  // 生成6位随机数字
  const randomPart = Math.floor(Math.random() * 900000 + 100000);

  // 组合订单号
  return `${prefix}${dateTimePart}${randomPart}`;
}

/**
 * 交易类型前缀映射
 */
const ORDER_TYPE_PREFIXES = {
  // 充值订单
  DEPOSIT: 'RE',

  // 取款订单
  WITHDRAWAL: 'WM',

  // 投资订单
  INVESTMENT: 'IV',

  // 赠送投资订单
  INVESTMENT_GIFT: 'IG',

  // 购买投资订单
  INVESTMENT_PURCHASE: 'IP',

  // 收益订单
  PROFIT: 'PF',

  // 佣金订单
  COMMISSION: 'CM',

  // 赠金订单
  BONUS: 'BN',

  // 扣除订单
  DEDUCTION: 'DD',

  // 退款订单
  REFUND: 'RF'
};

/**
 * 根据交易类型生成订单号
 * @param {string} type - 交易类型
 * @returns {string} 生成的订单号
 */
function generateOrderNumberByType(type) {
  const prefix = ORDER_TYPE_PREFIXES[type.toUpperCase()] || '';
  return generateOrderNumber(prefix);
}

module.exports = {
  generateOrderNumber,
  generateOrderNumberByType,
  ORDER_TYPE_PREFIXES
};
