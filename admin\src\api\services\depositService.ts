import request from '@/utils/request'
import { Deposit, DepositQuery, DepositResponse, DepositDetailResponse, MockCallbackRequest, MockCallbackResponse } from '../models/deposit'

/**
 * 充值订单服务
 */
export default {
  /**
   * 获取充值订单列表
   * @param params 查询参数
   * @returns 充值订单列表
   */
  getDeposits(params?: DepositQuery): Promise<DepositResponse> {
    return request({
      url: '/api/admin/deposits',
      method: 'get',
      params
    })
  },

  /**
   * 获取充值订单详情
   * @param id 充值订单ID
   * @returns 充值订单详情
   */
  getDeposit(id: number): Promise<DepositDetailResponse> {
    return request({
      url: `/api/admin/deposits/${id}`,
      method: 'get'
    })
  },

  /**
   * 模拟回调
   * @param data 请求参数
   * @returns 响应结果
   */
  mockCallback(data: MockCallbackRequest): Promise<MockCallbackResponse> {
    return request({
      url: `/api/admin/deposits/${data.id}/mock-callback`,
      method: 'post'
    })
  },

  /**
   * 更新支付平台订单号
   * @param id 充值订单ID
   * @param payment_platform_order_no 支付平台订单号
   * @returns 响应结果
   */
  updatePaymentPlatformOrderNo(id: number, payment_platform_order_no: string): Promise<DepositDetailResponse> {
    return request({
      url: `/api/admin/deposits/${id}/payment-platform-order-no`,
      method: 'put',
      data: { payment_platform_order_no }
    })
  },

  /**
   * 导出充值订单
   * @param params 查询参数
   * @returns 导出结果
   */
  exportDeposits(params?: DepositQuery): Promise<Blob> {
    return request({
      url: '/api/admin/deposits/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}
