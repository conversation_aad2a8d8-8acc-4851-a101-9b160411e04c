/**
 * 内存监控脚本
 * 用于监控 Node.js 应用程序的内存使用情况
 */

function formatMemoryUsage(data) {
  return `${Math.round(data / 1024 / 1024 * 100) / 100} MB`;
}

function logMemoryUsage() {
  const memoryData = process.memoryUsage();
  
  const memoryUsage = {
    rss: `常驻集大小: ${formatMemoryUsage(memoryData.rss)} - 进程分配的总内存`,
    heapTotal: `堆总大小: ${formatMemoryUsage(memoryData.heapTotal)} - V8 分配的总内存`,
    heapUsed: `堆已用: ${formatMemoryUsage(memoryData.heapUsed)} - 实际使用的堆内存`,
    external: `外部: ${formatMemoryUsage(memoryData.external)} - C++ 对象绑定到 JS 对象的内存`,
    arrayBuffers: `数组缓冲区: ${formatMemoryUsage(memoryData.arrayBuffers || 0)} - 分配给 ArrayBuffer 和 SharedArrayBuffer 的内存`
  };
  
  console.log('内存使用情况:');
  console.log(memoryUsage);
}

// 立即记录一次内存使用情况
logMemoryUsage();

// 如果作为独立脚本运行，则每5秒记录一次内存使用情况
if (require.main === module) {
  console.log('开始监控内存使用情况，每5秒记录一次...');
  setInterval(logMemoryUsage, 5000);
}

module.exports = { logMemoryUsage };
