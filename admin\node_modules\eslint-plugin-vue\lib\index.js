/*
 * IMPORTANT!
 * This file has been automatically generated,
 * in order to update its content execute "npm run update"
 */
'use strict'

const plugin = {
  meta: require('./meta'),
  configs: {
    // eslintrc configs
    base: require('./configs/base'),
    essential: require('./configs/vue2-essential'),
    'no-layout-rules': require('./configs/no-layout-rules'),
    recommended: require('./configs/vue2-recommended'),
    'strongly-recommended': require('./configs/vue2-strongly-recommended'),
    'vue3-essential': require('./configs/vue3-essential'),
    'vue3-recommended': require('./configs/vue3-recommended'),
    'vue3-strongly-recommended': require('./configs/vue3-strongly-recommended'),

    // flat configs
    'flat/base': require('./configs/flat/base.js'),
    'flat/vue2-essential': require('./configs/flat/vue2-essential.js'),
    'flat/vue2-recommended': require('./configs/flat/vue2-recommended.js'),
    'flat/vue2-strongly-recommended': require('./configs/flat/vue2-strongly-recommended.js'),

    // in flat configs, non-prefixed config is for Vue 3 (unlike eslintrc configs)
    'flat/essential': require('./configs/flat/vue3-essential.js'),
    'flat/recommended': require('./configs/flat/vue3-recommended.js'),
    'flat/strongly-recommended': require('./configs/flat/vue3-strongly-recommended.js')
  },
  rules: {
    'array-bracket-newline': require('./rules/array-bracket-newline'),
    'array-bracket-spacing': require('./rules/array-bracket-spacing'),
    'array-element-newline': require('./rules/array-element-newline'),
    'arrow-spacing': require('./rules/arrow-spacing'),
    'attribute-hyphenation': require('./rules/attribute-hyphenation'),
    'attributes-order': require('./rules/attributes-order'),
    'block-lang': require('./rules/block-lang'),
    'block-order': require('./rules/block-order'),
    'block-spacing': require('./rules/block-spacing'),
    'block-tag-newline': require('./rules/block-tag-newline'),
    'brace-style': require('./rules/brace-style'),
    camelcase: require('./rules/camelcase'),
    'comma-dangle': require('./rules/comma-dangle'),
    'comma-spacing': require('./rules/comma-spacing'),
    'comma-style': require('./rules/comma-style'),
    'comment-directive': require('./rules/comment-directive'),
    'component-api-style': require('./rules/component-api-style'),
    'component-definition-name-casing': require('./rules/component-definition-name-casing'),
    'component-name-in-template-casing': require('./rules/component-name-in-template-casing'),
    'component-options-name-casing': require('./rules/component-options-name-casing'),
    'component-tags-order': require('./rules/component-tags-order'),
    'custom-event-name-casing': require('./rules/custom-event-name-casing'),
    'define-emits-declaration': require('./rules/define-emits-declaration'),
    'define-macros-order': require('./rules/define-macros-order'),
    'define-props-declaration': require('./rules/define-props-declaration'),
    'dot-location': require('./rules/dot-location'),
    'dot-notation': require('./rules/dot-notation'),
    'enforce-style-attribute': require('./rules/enforce-style-attribute'),
    eqeqeq: require('./rules/eqeqeq'),
    'first-attribute-linebreak': require('./rules/first-attribute-linebreak'),
    'func-call-spacing': require('./rules/func-call-spacing'),
    'html-button-has-type': require('./rules/html-button-has-type'),
    'html-closing-bracket-newline': require('./rules/html-closing-bracket-newline'),
    'html-closing-bracket-spacing': require('./rules/html-closing-bracket-spacing'),
    'html-comment-content-newline': require('./rules/html-comment-content-newline'),
    'html-comment-content-spacing': require('./rules/html-comment-content-spacing'),
    'html-comment-indent': require('./rules/html-comment-indent'),
    'html-end-tags': require('./rules/html-end-tags'),
    'html-indent': require('./rules/html-indent'),
    'html-quotes': require('./rules/html-quotes'),
    'html-self-closing': require('./rules/html-self-closing'),
    'jsx-uses-vars': require('./rules/jsx-uses-vars'),
    'key-spacing': require('./rules/key-spacing'),
    'keyword-spacing': require('./rules/keyword-spacing'),
    'match-component-file-name': require('./rules/match-component-file-name'),
    'match-component-import-name': require('./rules/match-component-import-name'),
    'max-attributes-per-line': require('./rules/max-attributes-per-line'),
    'max-len': require('./rules/max-len'),
    'max-lines-per-block': require('./rules/max-lines-per-block'),
    'max-props': require('./rules/max-props'),
    'max-template-depth': require('./rules/max-template-depth'),
    'multi-word-component-names': require('./rules/multi-word-component-names'),
    'multiline-html-element-content-newline': require('./rules/multiline-html-element-content-newline'),
    'multiline-ternary': require('./rules/multiline-ternary'),
    'mustache-interpolation-spacing': require('./rules/mustache-interpolation-spacing'),
    'new-line-between-multi-line-property': require('./rules/new-line-between-multi-line-property'),
    'next-tick-style': require('./rules/next-tick-style'),
    'no-arrow-functions-in-watch': require('./rules/no-arrow-functions-in-watch'),
    'no-async-in-computed-properties': require('./rules/no-async-in-computed-properties'),
    'no-bare-strings-in-template': require('./rules/no-bare-strings-in-template'),
    'no-boolean-default': require('./rules/no-boolean-default'),
    'no-child-content': require('./rules/no-child-content'),
    'no-computed-properties-in-data': require('./rules/no-computed-properties-in-data'),
    'no-console': require('./rules/no-console'),
    'no-constant-condition': require('./rules/no-constant-condition'),
    'no-custom-modifiers-on-v-model': require('./rules/no-custom-modifiers-on-v-model'),
    'no-deprecated-data-object-declaration': require('./rules/no-deprecated-data-object-declaration'),
    'no-deprecated-delete-set': require('./rules/no-deprecated-delete-set'),
    'no-deprecated-destroyed-lifecycle': require('./rules/no-deprecated-destroyed-lifecycle'),
    'no-deprecated-dollar-listeners-api': require('./rules/no-deprecated-dollar-listeners-api'),
    'no-deprecated-dollar-scopedslots-api': require('./rules/no-deprecated-dollar-scopedslots-api'),
    'no-deprecated-events-api': require('./rules/no-deprecated-events-api'),
    'no-deprecated-filter': require('./rules/no-deprecated-filter'),
    'no-deprecated-functional-template': require('./rules/no-deprecated-functional-template'),
    'no-deprecated-html-element-is': require('./rules/no-deprecated-html-element-is'),
    'no-deprecated-inline-template': require('./rules/no-deprecated-inline-template'),
    'no-deprecated-model-definition': require('./rules/no-deprecated-model-definition'),
    'no-deprecated-props-default-this': require('./rules/no-deprecated-props-default-this'),
    'no-deprecated-router-link-tag-prop': require('./rules/no-deprecated-router-link-tag-prop'),
    'no-deprecated-scope-attribute': require('./rules/no-deprecated-scope-attribute'),
    'no-deprecated-slot-attribute': require('./rules/no-deprecated-slot-attribute'),
    'no-deprecated-slot-scope-attribute': require('./rules/no-deprecated-slot-scope-attribute'),
    'no-deprecated-v-bind-sync': require('./rules/no-deprecated-v-bind-sync'),
    'no-deprecated-v-is': require('./rules/no-deprecated-v-is'),
    'no-deprecated-v-on-native-modifier': require('./rules/no-deprecated-v-on-native-modifier'),
    'no-deprecated-v-on-number-modifiers': require('./rules/no-deprecated-v-on-number-modifiers'),
    'no-deprecated-vue-config-keycodes': require('./rules/no-deprecated-vue-config-keycodes'),
    'no-dupe-keys': require('./rules/no-dupe-keys'),
    'no-dupe-v-else-if': require('./rules/no-dupe-v-else-if'),
    'no-duplicate-attr-inheritance': require('./rules/no-duplicate-attr-inheritance'),
    'no-duplicate-attributes': require('./rules/no-duplicate-attributes'),
    'no-empty-component-block': require('./rules/no-empty-component-block'),
    'no-empty-pattern': require('./rules/no-empty-pattern'),
    'no-export-in-script-setup': require('./rules/no-export-in-script-setup'),
    'no-expose-after-await': require('./rules/no-expose-after-await'),
    'no-extra-parens': require('./rules/no-extra-parens'),
    'no-implicit-coercion': require('./rules/no-implicit-coercion'),
    'no-invalid-model-keys': require('./rules/no-invalid-model-keys'),
    'no-irregular-whitespace': require('./rules/no-irregular-whitespace'),
    'no-lifecycle-after-await': require('./rules/no-lifecycle-after-await'),
    'no-lone-template': require('./rules/no-lone-template'),
    'no-loss-of-precision': require('./rules/no-loss-of-precision'),
    'no-multi-spaces': require('./rules/no-multi-spaces'),
    'no-multiple-objects-in-class': require('./rules/no-multiple-objects-in-class'),
    'no-multiple-slot-args': require('./rules/no-multiple-slot-args'),
    'no-multiple-template-root': require('./rules/no-multiple-template-root'),
    'no-mutating-props': require('./rules/no-mutating-props'),
    'no-parsing-error': require('./rules/no-parsing-error'),
    'no-potential-component-option-typo': require('./rules/no-potential-component-option-typo'),
    'no-ref-as-operand': require('./rules/no-ref-as-operand'),
    'no-ref-object-destructure': require('./rules/no-ref-object-destructure'),
    'no-ref-object-reactivity-loss': require('./rules/no-ref-object-reactivity-loss'),
    'no-required-prop-with-default': require('./rules/no-required-prop-with-default'),
    'no-reserved-component-names': require('./rules/no-reserved-component-names'),
    'no-reserved-keys': require('./rules/no-reserved-keys'),
    'no-reserved-props': require('./rules/no-reserved-props'),
    'no-restricted-block': require('./rules/no-restricted-block'),
    'no-restricted-call-after-await': require('./rules/no-restricted-call-after-await'),
    'no-restricted-class': require('./rules/no-restricted-class'),
    'no-restricted-component-names': require('./rules/no-restricted-component-names'),
    'no-restricted-component-options': require('./rules/no-restricted-component-options'),
    'no-restricted-custom-event': require('./rules/no-restricted-custom-event'),
    'no-restricted-html-elements': require('./rules/no-restricted-html-elements'),
    'no-restricted-props': require('./rules/no-restricted-props'),
    'no-restricted-static-attribute': require('./rules/no-restricted-static-attribute'),
    'no-restricted-syntax': require('./rules/no-restricted-syntax'),
    'no-restricted-v-bind': require('./rules/no-restricted-v-bind'),
    'no-restricted-v-on': require('./rules/no-restricted-v-on'),
    'no-root-v-if': require('./rules/no-root-v-if'),
    'no-setup-props-destructure': require('./rules/no-setup-props-destructure'),
    'no-setup-props-reactivity-loss': require('./rules/no-setup-props-reactivity-loss'),
    'no-shared-component-data': require('./rules/no-shared-component-data'),
    'no-side-effects-in-computed-properties': require('./rules/no-side-effects-in-computed-properties'),
    'no-spaces-around-equal-signs-in-attribute': require('./rules/no-spaces-around-equal-signs-in-attribute'),
    'no-sparse-arrays': require('./rules/no-sparse-arrays'),
    'no-static-inline-styles': require('./rules/no-static-inline-styles'),
    'no-template-key': require('./rules/no-template-key'),
    'no-template-shadow': require('./rules/no-template-shadow'),
    'no-template-target-blank': require('./rules/no-template-target-blank'),
    'no-textarea-mustache': require('./rules/no-textarea-mustache'),
    'no-this-in-before-route-enter': require('./rules/no-this-in-before-route-enter'),
    'no-undef-components': require('./rules/no-undef-components'),
    'no-undef-properties': require('./rules/no-undef-properties'),
    'no-unsupported-features': require('./rules/no-unsupported-features'),
    'no-unused-components': require('./rules/no-unused-components'),
    'no-unused-emit-declarations': require('./rules/no-unused-emit-declarations'),
    'no-unused-properties': require('./rules/no-unused-properties'),
    'no-unused-refs': require('./rules/no-unused-refs'),
    'no-unused-vars': require('./rules/no-unused-vars'),
    'no-use-computed-property-like-method': require('./rules/no-use-computed-property-like-method'),
    'no-use-v-else-with-v-for': require('./rules/no-use-v-else-with-v-for'),
    'no-use-v-if-with-v-for': require('./rules/no-use-v-if-with-v-for'),
    'no-useless-concat': require('./rules/no-useless-concat'),
    'no-useless-mustaches': require('./rules/no-useless-mustaches'),
    'no-useless-template-attributes': require('./rules/no-useless-template-attributes'),
    'no-useless-v-bind': require('./rules/no-useless-v-bind'),
    'no-v-for-template-key-on-child': require('./rules/no-v-for-template-key-on-child'),
    'no-v-for-template-key': require('./rules/no-v-for-template-key'),
    'no-v-html': require('./rules/no-v-html'),
    'no-v-model-argument': require('./rules/no-v-model-argument'),
    'no-v-text-v-html-on-component': require('./rules/no-v-text-v-html-on-component'),
    'no-v-text': require('./rules/no-v-text'),
    'no-watch-after-await': require('./rules/no-watch-after-await'),
    'object-curly-newline': require('./rules/object-curly-newline'),
    'object-curly-spacing': require('./rules/object-curly-spacing'),
    'object-property-newline': require('./rules/object-property-newline'),
    'object-shorthand': require('./rules/object-shorthand'),
    'one-component-per-file': require('./rules/one-component-per-file'),
    'operator-linebreak': require('./rules/operator-linebreak'),
    'order-in-components': require('./rules/order-in-components'),
    'padding-line-between-blocks': require('./rules/padding-line-between-blocks'),
    'padding-line-between-tags': require('./rules/padding-line-between-tags'),
    'padding-lines-in-component-definition': require('./rules/padding-lines-in-component-definition'),
    'prefer-define-options': require('./rules/prefer-define-options'),
    'prefer-import-from-vue': require('./rules/prefer-import-from-vue'),
    'prefer-prop-type-boolean-first': require('./rules/prefer-prop-type-boolean-first'),
    'prefer-separate-static-class': require('./rules/prefer-separate-static-class'),
    'prefer-template': require('./rules/prefer-template'),
    'prefer-true-attribute-shorthand': require('./rules/prefer-true-attribute-shorthand'),
    'prefer-use-template-ref': require('./rules/prefer-use-template-ref'),
    'prop-name-casing': require('./rules/prop-name-casing'),
    'quote-props': require('./rules/quote-props'),
    'require-component-is': require('./rules/require-component-is'),
    'require-default-export': require('./rules/require-default-export'),
    'require-default-prop': require('./rules/require-default-prop'),
    'require-direct-export': require('./rules/require-direct-export'),
    'require-emit-validator': require('./rules/require-emit-validator'),
    'require-explicit-emits': require('./rules/require-explicit-emits'),
    'require-explicit-slots': require('./rules/require-explicit-slots'),
    'require-expose': require('./rules/require-expose'),
    'require-macro-variable-name': require('./rules/require-macro-variable-name'),
    'require-name-property': require('./rules/require-name-property'),
    'require-prop-comment': require('./rules/require-prop-comment'),
    'require-prop-type-constructor': require('./rules/require-prop-type-constructor'),
    'require-prop-types': require('./rules/require-prop-types'),
    'require-render-return': require('./rules/require-render-return'),
    'require-slots-as-functions': require('./rules/require-slots-as-functions'),
    'require-toggle-inside-transition': require('./rules/require-toggle-inside-transition'),
    'require-typed-object-prop': require('./rules/require-typed-object-prop'),
    'require-typed-ref': require('./rules/require-typed-ref'),
    'require-v-for-key': require('./rules/require-v-for-key'),
    'require-valid-default-prop': require('./rules/require-valid-default-prop'),
    'restricted-component-names': require('./rules/restricted-component-names'),
    'return-in-computed-property': require('./rules/return-in-computed-property'),
    'return-in-emits-validator': require('./rules/return-in-emits-validator'),
    'script-indent': require('./rules/script-indent'),
    'script-setup-uses-vars': require('./rules/script-setup-uses-vars'),
    'singleline-html-element-content-newline': require('./rules/singleline-html-element-content-newline'),
    'slot-name-casing': require('./rules/slot-name-casing'),
    'sort-keys': require('./rules/sort-keys'),
    'space-in-parens': require('./rules/space-in-parens'),
    'space-infix-ops': require('./rules/space-infix-ops'),
    'space-unary-ops': require('./rules/space-unary-ops'),
    'static-class-names-order': require('./rules/static-class-names-order'),
    'template-curly-spacing': require('./rules/template-curly-spacing'),
    'this-in-template': require('./rules/this-in-template'),
    'use-v-on-exact': require('./rules/use-v-on-exact'),
    'v-bind-style': require('./rules/v-bind-style'),
    'v-for-delimiter-style': require('./rules/v-for-delimiter-style'),
    'v-if-else-key': require('./rules/v-if-else-key'),
    'v-on-event-hyphenation': require('./rules/v-on-event-hyphenation'),
    'v-on-function-call': require('./rules/v-on-function-call'),
    'v-on-handler-style': require('./rules/v-on-handler-style'),
    'v-on-style': require('./rules/v-on-style'),
    'v-slot-style': require('./rules/v-slot-style'),
    'valid-attribute-name': require('./rules/valid-attribute-name'),
    'valid-define-emits': require('./rules/valid-define-emits'),
    'valid-define-options': require('./rules/valid-define-options'),
    'valid-define-props': require('./rules/valid-define-props'),
    'valid-model-definition': require('./rules/valid-model-definition'),
    'valid-next-tick': require('./rules/valid-next-tick'),
    'valid-template-root': require('./rules/valid-template-root'),
    'valid-v-bind-sync': require('./rules/valid-v-bind-sync'),
    'valid-v-bind': require('./rules/valid-v-bind'),
    'valid-v-cloak': require('./rules/valid-v-cloak'),
    'valid-v-else-if': require('./rules/valid-v-else-if'),
    'valid-v-else': require('./rules/valid-v-else'),
    'valid-v-for': require('./rules/valid-v-for'),
    'valid-v-html': require('./rules/valid-v-html'),
    'valid-v-if': require('./rules/valid-v-if'),
    'valid-v-is': require('./rules/valid-v-is'),
    'valid-v-memo': require('./rules/valid-v-memo'),
    'valid-v-model': require('./rules/valid-v-model'),
    'valid-v-on': require('./rules/valid-v-on'),
    'valid-v-once': require('./rules/valid-v-once'),
    'valid-v-pre': require('./rules/valid-v-pre'),
    'valid-v-show': require('./rules/valid-v-show'),
    'valid-v-slot': require('./rules/valid-v-slot'),
    'valid-v-text': require('./rules/valid-v-text')
  },
  processors: {
    '.vue': require('./processor'),
    vue: require('./processor')
  },
  environments: {
    // TODO Remove in the next major version
    /** @deprecated */
    'setup-compiler-macros': {
      globals: {
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly'
      }
    }
  }
}

module.exports = plugin
