{"version": 3, "file": "jumper2.js", "sources": ["../../../../../../../packages/components/pagination/src/components/jumper.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('jump')\" :disabled=\"disabled\">\n    <span :class=\"[ns.e('goto')]\">{{ t('el.pagination.goto') }}</span>\n    <el-input\n      :size=\"size\"\n      :class=\"[ns.e('editor'), ns.is('in-pagination')]\"\n      :min=\"1\"\n      :max=\"pageCount\"\n      :disabled=\"disabled\"\n      :model-value=\"innerValue\"\n      :validate-event=\"false\"\n      :aria-label=\"t('el.pagination.page')\"\n      type=\"number\"\n      @update:model-value=\"handleInput\"\n      @change=\"handleChange\"\n    />\n    <span :class=\"[ns.e('classifier')]\">{{\n      t('el.pagination.pageClassifier')\n    }}</span>\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport ElInput from '@element-plus/components/input'\nimport { usePagination } from '../usePagination'\nimport { paginationJumperProps } from './jumper'\n\ndefineOptions({\n  name: 'ElPaginationJumper',\n})\n\ndefineProps(paginationJumperProps)\nconst { t } = useLocale()\nconst ns = useNamespace('pagination')\nconst { pageCount, disabled, currentPage, changeEvent } = usePagination()\nconst userInput = ref<number | string>()\nconst innerValue = computed(() => userInput.value ?? currentPage?.value)\n\nfunction handleInput(val: number | string) {\n  userInput.value = val ? +val : ''\n}\n\nfunction handleChange(val: number | string) {\n  val = Math.trunc(+val)\n  changeEvent?.(val)\n  userInput.value = undefined\n}\n</script>\n"], "names": ["useLocale", "useNamespace", "usePagination", "ref", "computed"], "mappings": ";;;;;;;;;;;;uCA6Bc,CAAA;AAAA,EACZ,IAAM,EAAA,oBAAA;AACR,CAAA,CAAA,CAAA;;;;;AAGA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,YAAY,CAAA,CAAA;AACpC,IAAA,MAAM,EAAE,SAAW,EAAA,QAAA,EAAU,WAAa,EAAA,WAAA,KAAgBC,2BAAc,EAAA,CAAA;AACxE,IAAA,MAAM,YAAYC,OAAqB,EAAA,CAAA;AACvC,IAAA,MAAM,aAAaC,YAAS,CAAA,MAAM;AAElC,MAAA,IAAA,EAAA,CAAS;AACP,MAAU,OAAA,CAAA,EAAA,GAAA,SAAc,CAAA,KAAO,KAAA,IAAA,GAAA,EAAA,GAAA,WAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,KAAA,CAAA;AAAA,KACjC,CAAA,CAAA;AAEA,IAAA,SAAS,eAAmC,EAAA;AAC1C,MAAM,SAAA,CAAA,KAAW,GAAC,GAAG,GAAA,CAAA,GAAA,GAAA,EAAA,CAAA;AACrB,KAAA;AACA,IAAA,SAAA,YAAkB,CAAA,GAAA,EAAA;AAAA,MACpB,GAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}