/**
 * <AUTHOR>
 * See LICENSE file in root directory for full license.
 */
'use strict'
const baseRule = require('./no-ref-object-reactivity-loss')

module.exports = {
  // eslint-disable-next-line eslint-plugin/require-meta-schema, eslint-plugin/prefer-message-ids, eslint-plugin/require-meta-type -- inherit schema from base rule
  meta: {
    ...baseRule.meta,
    // eslint-disable-next-line eslint-plugin/require-meta-docs-description, internal/no-invalid-meta, internal/no-invalid-meta-docs-categories, eslint-plugin/meta-property-ordering
    docs: {
      ...baseRule.meta.docs,
      url: 'https://eslint.vuejs.org/rules/no-ref-object-destructure.html'
    },
    deprecated: true,
    replacedBy: ['no-ref-object-reactivity-loss']
  },
  /** @param {RuleContext} context */
  create(context) {
    return baseRule.create(context)
  }
}
