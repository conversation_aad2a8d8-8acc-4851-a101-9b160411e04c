/**
 * 方案5.1：收益系统测试
 * 测试收益发放逻辑和时间计算
 */

const profitScheduler = require('../services/profitScheduler');
const moment = require('moment');

/**
 * 测试收益时间计算
 */
function testProfitTimeCalculation() {
  console.log('💰 测试收益时间计算...\n');

  // 模拟投资数据
  const mockInvestments = [
    {
      id: 1,
      start_time: '2024-01-15 02:00:00', // UTC时间
      last_profit_time: null, // 首次收益
      amount: 1000,
      profit_rate: 5
    },
    {
      id: 2,
      start_time: '2024-01-15 02:00:00', // UTC时间
      last_profit_time: '2024-01-15 14:00:00', // UTC时间，已有收益
      amount: 2000,
      profit_rate: 8
    }
  ];

  const mockProject = {
    id: 1,
    profit_time: 24 // 24小时周期
  };

  console.log('1. 测试首次收益时间计算：');
  const firstInvestment = mockInvestments[0];
  const firstProfitTime = profitScheduler.calculateNextProfitTime(firstInvestment, mockProject);
  
  console.log(`  投资ID: ${firstInvestment.id}`);
  console.log(`  开始时间(UTC): ${firstInvestment.start_time}`);
  console.log(`  计算的首次收益时间: ${firstProfitTime.toISOString()}`);
  
  // 验证首次收益时间是否正确
  const expectedFirstTime = new Date(new Date(firstInvestment.start_time).getTime() + 24 * 60 * 60 * 1000);
  const firstTimeCorrect = Math.abs(firstProfitTime.getTime() - expectedFirstTime.getTime()) < 1000;
  console.log(`  预期首次收益时间: ${expectedFirstTime.toISOString()}`);
  console.log(`  计算正确: ${firstTimeCorrect}\n`);

  console.log('2. 测试后续收益时间计算：');
  const secondInvestment = mockInvestments[1];
  const nextProfitTime = profitScheduler.calculateNextProfitTime(secondInvestment, mockProject);
  
  console.log(`  投资ID: ${secondInvestment.id}`);
  console.log(`  上次收益时间(UTC): ${secondInvestment.last_profit_time}`);
  console.log(`  计算的下次收益时间: ${nextProfitTime.toISOString()}`);
  
  // 验证后续收益时间是否正确
  const expectedNextTime = new Date(new Date(secondInvestment.last_profit_time).getTime() + 24 * 60 * 60 * 1000);
  const nextTimeCorrect = Math.abs(nextProfitTime.getTime() - expectedNextTime.getTime()) < 1000;
  console.log(`  预期下次收益时间: ${expectedNextTime.toISOString()}`);
  console.log(`  计算正确: ${nextTimeCorrect}\n`);

  return {
    firstProfitCalculation: {
      correct: firstTimeCorrect,
      calculated: firstProfitTime.toISOString(),
      expected: expectedFirstTime.toISOString()
    },
    nextProfitCalculation: {
      correct: nextTimeCorrect,
      calculated: nextProfitTime.toISOString(),
      expected: expectedNextTime.toISOString()
    }
  };
}

/**
 * 测试跨时区收益时间一致性
 */
function testCrossTimezoneConsistency() {
  console.log('🌍 测试跨时区收益时间一致性...\n');

  // 模拟投资数据
  const investment = {
    id: 1,
    start_time: '2024-01-15 10:00:00', // UTC时间
    last_profit_time: '2024-01-16 10:00:00', // UTC时间
  };

  const project = {
    profit_time: 24 // 24小时周期
  };

  console.log('1. 计算下次收益时间（UTC）：');
  const nextProfitTimeUTC = profitScheduler.calculateNextProfitTime(investment, project);
  console.log(`  下次收益时间(UTC): ${nextProfitTimeUTC.toISOString()}\n`);

  console.log('2. 转换为不同时区的本地时间：');
  const chinaTime = moment.utc(nextProfitTimeUTC).utcOffset('+08:00');
  const australiaTime = moment.utc(nextProfitTimeUTC).utcOffset('+10:00');
  const usTime = moment.utc(nextProfitTimeUTC).utcOffset('-05:00');
  const ukTime = moment.utc(nextProfitTimeUTC).utcOffset('+00:00');

  console.log(`  中国时间(+08:00): ${chinaTime.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  澳洲时间(+10:00): ${australiaTime.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  美国时间(-05:00): ${usTime.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  英国时间(+00:00): ${ukTime.format('YYYY-MM-DD HH:mm:ss')}\n`);

  console.log('3. 验证UTC时间戳一致性：');
  const utcTimestamp = nextProfitTimeUTC.getTime();
  const chinaTimestamp = chinaTime.valueOf();
  const australiaTimestamp = australiaTime.valueOf();
  const usTimestamp = usTime.valueOf();

  console.log(`  UTC时间戳: ${utcTimestamp}`);
  console.log(`  中国时间戳: ${chinaTimestamp}`);
  console.log(`  澳洲时间戳: ${australiaTimestamp}`);
  console.log(`  美国时间戳: ${usTimestamp}`);

  const timestampsConsistent = (
    utcTimestamp === chinaTimestamp &&
    utcTimestamp === australiaTimestamp &&
    utcTimestamp === usTimestamp
  );

  console.log(`  时间戳一致性: ${timestampsConsistent ? '✅ 一致' : '❌ 不一致'}\n`);

  return {
    utcTime: nextProfitTimeUTC.toISOString(),
    localTimes: {
      china: chinaTime.format('YYYY-MM-DD HH:mm:ss'),
      australia: australiaTime.format('YYYY-MM-DD HH:mm:ss'),
      us: usTime.format('YYYY-MM-DD HH:mm:ss'),
      uk: ukTime.format('YYYY-MM-DD HH:mm:ss')
    },
    timestampConsistency: timestampsConsistent,
    utcTimestamp: utcTimestamp
  };
}

/**
 * 测试收益发放条件判断
 */
function testProfitDistributionConditions() {
  console.log('⏰ 测试收益发放条件判断...\n');

  const now = new Date();
  
  // 测试场景
  const testScenarios = [
    {
      name: '应该发放收益（时间已到）',
      investment: {
        id: 1,
        last_profit_time: new Date(now.getTime() - 25 * 60 * 60 * 1000).toISOString() // 25小时前
      },
      project: { profit_time: 24 },
      expectedResult: true
    },
    {
      name: '不应该发放收益（时间未到）',
      investment: {
        id: 2,
        last_profit_time: new Date(now.getTime() - 23 * 60 * 60 * 1000).toISOString() // 23小时前
      },
      project: { profit_time: 24 },
      expectedResult: false
    },
    {
      name: '首次收益（基于开始时间）',
      investment: {
        id: 3,
        start_time: new Date(now.getTime() - 25 * 60 * 60 * 1000).toISOString(), // 25小时前
        last_profit_time: null
      },
      project: { profit_time: 24 },
      expectedResult: true
    }
  ];

  const results = [];

  testScenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.name}：`);
    
    // 计算下一次收益时间
    const nextProfitTime = profitScheduler.calculateNextProfitTime(scenario.investment, scenario.project);
    
    // 判断是否应该发放收益
    const shouldDistribute = now >= nextProfitTime;
    
    console.log(`  当前时间: ${now.toISOString()}`);
    console.log(`  下次收益时间: ${nextProfitTime.toISOString()}`);
    console.log(`  应该发放: ${shouldDistribute}`);
    console.log(`  预期结果: ${scenario.expectedResult}`);
    console.log(`  判断正确: ${shouldDistribute === scenario.expectedResult ? '✅' : '❌'}\n`);

    results.push({
      scenario: scenario.name,
      shouldDistribute: shouldDistribute,
      expected: scenario.expectedResult,
      correct: shouldDistribute === scenario.expectedResult,
      nextProfitTime: nextProfitTime.toISOString()
    });
  });

  return results;
}

/**
 * 主测试函数
 */
function runProfitSystemTests() {
  console.log('🧪 方案5.1 收益系统测试');
  console.log('=====================================\n');

  const results = {};

  try {
    // 测试收益时间计算
    results.timeCalculation = testProfitTimeCalculation();
    
    // 测试跨时区一致性
    results.crossTimezone = testCrossTimezoneConsistency();
    
    // 测试收益发放条件
    results.distributionConditions = testProfitDistributionConditions();

    // 输出测试总结
    console.log('📋 收益系统测试结果总结：');
    console.log(`首次收益计算: ${results.timeCalculation.firstProfitCalculation.correct ? '✅ 通过' : '❌ 失败'}`);
    console.log(`后续收益计算: ${results.timeCalculation.nextProfitCalculation.correct ? '✅ 通过' : '❌ 失败'}`);
    console.log(`跨时区一致性: ${results.crossTimezone.timestampConsistency ? '✅ 通过' : '❌ 失败'}`);
    
    const conditionTests = results.distributionConditions;
    const passedConditions = conditionTests.filter(test => test.correct).length;
    console.log(`发放条件判断: ${passedConditions}/${conditionTests.length} 通过`);

    const allTestsPassed = (
      results.timeCalculation.firstProfitCalculation.correct &&
      results.timeCalculation.nextProfitCalculation.correct &&
      results.crossTimezone.timestampConsistency &&
      passedConditions === conditionTests.length
    );

    console.log(`\n${allTestsPassed ? '🎉 所有测试通过！' : '⚠️ 部分测试失败，请检查上述结果'}`);
    
    return results;

  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    return { error: error.message };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runProfitSystemTests();
}

module.exports = {
  testProfitTimeCalculation,
  testCrossTimezoneConsistency,
  testProfitDistributionConditions,
  runProfitSystemTests
};
