{"version": 3, "file": "no-unsafe-assignment.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-assignment.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AAGxC,kCAYiB;AAWjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,wEAAwE;YAC1E,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,aAAa,EAAE,sCAAsC;YACrD,iBAAiB,EAAE;gBACjB,gEAAgE;gBAChE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,kBAAkB,EAAE,qDAAqD;YACzE,2BAA2B,EACzB,oEAAoE;YACtE,gBAAgB,EACd,0EAA0E;YAC5E,iBAAiB,EAAE,8CAA8C;SAClE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,0CAA0C;QAC1C,SAAS,2BAA2B,CAClC,YAA2B,EAC3B,UAAyB;YAEzB,IAAI,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE,CAAC;gBACtD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,YAAY,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE1D,OAAO,qBAAqB,CAAC,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;QACvE,CAAC;QAED,0CAA0C;QAC1C,SAAS,qBAAqB,CAC5B,YAAmC,EACnC,UAAmB,EACnB,UAAmB;YAEnB,YAAY;YACZ,6BAA6B;YAC7B,IAAI,IAAA,yBAAkB,EAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC5C,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,YAAY;oBAClB,SAAS,EAAE,oBAAoB;iBAChC,CAAC,CAAC;gBACH,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAE3D,iBAAiB;YACjB,0BAA0B;YAC1B,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,KACE,IAAI,aAAa,GAAG,CAAC,EACrB,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,EAC5C,aAAa,IAAI,CAAC,EAClB,CAAC;gBACD,MAAM,eAAe,GAAG,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC7D,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,SAAS;gBACX,CAAC;gBAED,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;oBACxD,qDAAqD;oBACrD,SAAS;gBACX,CAAC;gBAED,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAwB,CAAC;gBACvE,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,SAAS;gBACX,CAAC;gBAED,gEAAgE;gBAChE,IAAI,IAAA,oBAAa,EAAC,UAAU,CAAC,EAAE,CAAC;oBAC9B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,eAAe;wBACrB,SAAS,EAAE,6BAA6B;qBACzC,CAAC,CAAC;oBACH,0DAA0D;oBAC1D,SAAS,GAAG,IAAI,CAAC;gBACnB,CAAC;qBAAM,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE,CAAC;oBAChE,SAAS,GAAG,qBAAqB,CAC/B,eAAe,EACf,UAAU,EACV,UAAU,CACX,CAAC;gBACJ,CAAC;qBAAM,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE,CAAC;oBACjE,SAAS,GAAG,sBAAsB,CAChC,eAAe,EACf,UAAU,EACV,UAAU,CACX,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,0CAA0C;QAC1C,SAAS,4BAA4B,CACnC,YAA2B,EAC3B,UAAyB;YAEzB,IAAI,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE,CAAC;gBACvD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,YAAY,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE1D,OAAO,sBAAsB,CAAC,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;QACxE,CAAC;QAED,0CAA0C;QAC1C,SAAS,sBAAsB,CAC7B,YAAoC,EACpC,UAAmB,EACnB,UAAmB;YAEnB,MAAM,UAAU,GAAG,IAAI,GAAG,CACxB,UAAU;iBACP,aAAa,EAAE;iBACf,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACf,QAAQ,CAAC,OAAO,EAAE;gBAClB,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,UAAU,CAAC;aACxD,CAAC,CACL,CAAC;YAEF,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,KAAK,MAAM,gBAAgB,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBACvD,IAAI,gBAAgB,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;oBACzD,6BAA6B;oBAC7B,SAAS;gBACX,CAAC;gBAED,IAAI,GAAW,CAAC;gBAChB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;oBAC/B,GAAG;wBACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BACrD,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI;4BAC3B,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC3C,CAAC;qBAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;oBAChE,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC3C,CAAC;qBAAM,IACL,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBAC5D,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxC,CAAC;oBACD,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC5D,CAAC;qBAAM,CAAC;oBACN,wCAAwC;oBACxC,SAAS;gBACX,CAAC;gBAED,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACvC,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,SAAS;gBACX,CAAC;gBAED,uEAAuE;gBACvE,IAAI,IAAA,oBAAa,EAAC,UAAU,CAAC,EAAE,CAAC;oBAC9B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,gBAAgB,CAAC,KAAK;wBAC5B,SAAS,EAAE,6BAA6B;qBACzC,CAAC,CAAC;oBACH,SAAS,GAAG,IAAI,CAAC;gBACnB,CAAC;qBAAM,IACL,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAC3D,CAAC;oBACD,SAAS,GAAG,qBAAqB,CAC/B,gBAAgB,CAAC,KAAK,EACtB,UAAU,EACV,UAAU,CACX,CAAC;gBACJ,CAAC;qBAAM,IACL,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAC5D,CAAC;oBACD,SAAS,GAAG,sBAAsB,CAChC,gBAAgB,CAAC,KAAK,EACtB,UAAU,EACV,UAAU,CACX,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,0CAA0C;QAC1C,SAAS,eAAe,CACtB,YAA2B,EAC3B,UAA+B,EAC/B,aAA4B,EAC5B,cAA8B;YAE9B,MAAM,cAAc,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACxE,MAAM,YAAY,GAChB,cAAc,sCAA8B;gBAC1C,CAAC,CAAC,IAAA,wBAAiB,EAAC,OAAO,EAAE,cAA+B,CAAC;oBAC3D,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC;gBAC1C,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE1D,IAAI,IAAA,oBAAa,EAAC,UAAU,CAAC,EAAE,CAAC;gBAC9B,+CAA+C;gBAC/C,IAAI,IAAA,wBAAiB,EAAC,YAAY,CAAC,EAAE,CAAC;oBACpC,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,IAAI,SAAS,GAA0C,eAAe,CAAC;gBAEvE,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,mBAAmB;oBACnB,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,UAAU,CAAC,CAAC;oBACrD,IACE,cAAc;wBACd,IAAA,oBAAa,EACX,IAAA,mCAA4B,EAAC,QAAQ,EAAE,cAAc,CAAC,CACvD,EACD,CAAC;wBACD,SAAS,GAAG,mBAAmB,CAAC;oBAClC,CAAC;gBACH,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,aAAa;oBACnB,SAAS;iBACV,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,cAAc,gCAAwB,EAAE,CAAC;gBAC3C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,MAAM,GAAG,IAAA,yBAAkB,EAC/B,UAAU,EACV,YAAY,EACZ,OAAO,EACP,UAAU,CACX,CAAC;YACF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YACpC,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,kBAAkB;gBAC7B,IAAI,EAAE;oBACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;oBACpC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC;iBACzC;aACF,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,iBAAiB,CACxB,cAAqD;YAErD,OAAO,cAAc;gBACnB,CAAC,CAAC,uDAAuD;;gBAEzD,CAAC,CAAC,iFAAiF;+CAC9D,CAAC;QAC1B,CAAC;QAED,OAAO;YACL,kCAAkC,CAChC,IAAiC;gBAEjC,MAAM,IAAI,GAAG,IAAA,iBAAU,EACrB,IAAI,CAAC,IAAI,EACT,wBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAClD,CAAC;gBACF,IAAI,SAAS,GAAG,eAAe,CAC7B,IAAI,CAAC,EAAE,EACP,IAAI,EACJ,IAAI,EACJ,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,CAC1C,CAAC;gBAEF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,SAAS,GAAG,2BAA2B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBACzD,CAAC;gBACD,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,4BAA4B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC;YACD,mCAAmC,CACjC,IAAiC;gBAEjC,eAAe,CACb,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,KAAM,EACX,IAAI,EACJ,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CACvC,CAAC;YACJ,CAAC;YACD,yDAAyD,CACvD,IAAgE;gBAEhE,IAAI,SAAS,GAAG,eAAe,CAC7B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,KAAK,EACV,IAAI,+BAGL,CAAC;gBAEF,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,SAAS,GAAG,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjE,CAAC;gBACD,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YACD,mDAAmD;YACnD,gCAAgC,CAAC,IAAuB;gBACtD,IACE,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACpD,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,6BAA6B,EAChE,CAAC;oBACD,4BAA4B;oBAC5B,OAAO;gBACT,CAAC;gBAED,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,oCAA4B,CAAC;YACzE,CAAC;YACD,iCAAiC,CAAC,IAA4B;gBAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC3D,IAAI,IAAA,oBAAa,EAAC,QAAQ,CAAC,IAAI,IAAA,yBAAkB,EAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC;oBACrE,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,IAAI;wBACV,SAAS,EAAE,mBAAmB;qBAC/B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,6BAA6B,CAAC,IAA2B;gBACvD,MAAM,KAAK,GAAG,IAAA,iBAAU,EACtB,IAAI,CAAC,KAAK,EACV,wBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CACnD,CAAC;gBACF,IACE,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;oBACpD,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC3D,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,eAAe,CACb,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,UAAU,EAChB,KAAK,CAAC,UAAU,oCAEjB,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}