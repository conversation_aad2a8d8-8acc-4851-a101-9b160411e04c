-- =====================================================
-- 方案5.1：数据迁移脚本 - 将时间数据从+08:00转换为UTC
-- =====================================================
-- 
-- 重要提醒：
-- 1. 执行前请务必备份数据库！
-- 2. 建议在维护窗口期间执行
-- 3. 执行时间预计：35-70分钟（取决于数据量）
-- 4. 执行期间系统将不可用
-- 
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 记录迁移开始时间
SELECT NOW() as '迁移开始时间', 'UTC时区迁移脚本开始执行' as '状态';

-- =====================================================
-- 1. 迁移 investments 表
-- =====================================================
SELECT '正在迁移 investments 表...' as '状态';

UPDATE investments 
SET 
    start_time = CONVERT_TZ(start_time, '+08:00', '+00:00'),
    last_profit_time = CASE 
        WHEN last_profit_time IS NOT NULL 
        THEN CONVERT_TZ(last_profit_time, '+08:00', '+00:00')
        ELSE NULL 
    END,
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

SELECT ROW_COUNT() as '已更新investments记录数';

-- =====================================================
-- 2. 迁移 investment_profits 表
-- =====================================================
SELECT '正在迁移 investment_profits 表...' as '状态';

UPDATE investment_profits 
SET 
    profit_time = CONVERT_TZ(profit_time, '+08:00', '+00:00'),
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

SELECT ROW_COUNT() as '已更新investment_profits记录数';

-- =====================================================
-- 3. 迁移 users 表
-- =====================================================
SELECT '正在迁移 users 表...' as '状态';

UPDATE users 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00'),
    last_login_time = CASE 
        WHEN last_login_time IS NOT NULL 
        THEN CONVERT_TZ(last_login_time, '+08:00', '+00:00')
        ELSE NULL 
    END;

SELECT ROW_COUNT() as '已更新users记录数';

-- =====================================================
-- 4. 迁移 transactions 表
-- =====================================================
SELECT '正在迁移 transactions 表...' as '状态';

UPDATE transactions 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00'),
    completed_at = CASE 
        WHEN completed_at IS NOT NULL 
        THEN CONVERT_TZ(completed_at, '+08:00', '+00:00')
        ELSE NULL 
    END;

SELECT ROW_COUNT() as '已更新transactions记录数';

-- =====================================================
-- 5. 迁移 admin_users 表
-- =====================================================
SELECT '正在迁移 admin_users 表...' as '状态';

UPDATE admin_users 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00'),
    last_login_time = CASE 
        WHEN last_login_time IS NOT NULL 
        THEN CONVERT_TZ(last_login_time, '+08:00', '+00:00')
        ELSE NULL 
    END;

SELECT ROW_COUNT() as '已更新admin_users记录数';

-- =====================================================
-- 6. 迁移 projects 表
-- =====================================================
SELECT '正在迁移 projects 表...' as '状态';

UPDATE projects 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

SELECT ROW_COUNT() as '已更新projects记录数';

-- =====================================================
-- 7. 迁移 system_params 表
-- =====================================================
SELECT '正在迁移 system_params 表...' as '状态';

UPDATE system_params 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

SELECT ROW_COUNT() as '已更新system_params记录数';

-- =====================================================
-- 8. 迁移其他可能包含时间字段的表
-- =====================================================

-- 如果有其他表包含时间字段，请在此处添加
-- 例如：logs表、notifications表等

-- =====================================================
-- 9. 验证迁移结果
-- =====================================================
SELECT '正在验证迁移结果...' as '状态';

-- 检查investments表的时间范围
SELECT 
    'investments' as '表名',
    MIN(start_time) as '最早开始时间',
    MAX(start_time) as '最晚开始时间',
    MIN(last_profit_time) as '最早收益时间',
    MAX(last_profit_time) as '最晚收益时间'
FROM investments;

-- 检查investment_profits表的时间范围
SELECT 
    'investment_profits' as '表名',
    MIN(profit_time) as '最早收益时间',
    MAX(profit_time) as '最晚收益时间',
    COUNT(*) as '总记录数'
FROM investment_profits;

-- 检查users表的时间范围
SELECT 
    'users' as '表名',
    MIN(created_at) as '最早注册时间',
    MAX(created_at) as '最晚注册时间',
    COUNT(*) as '总用户数'
FROM users;

-- =====================================================
-- 10. 更新时区配置（如果需要）
-- =====================================================
SELECT '正在更新时区配置...' as '状态';

-- 如果系统参数中没有时区设置，插入默认UTC设置
INSERT IGNORE INTO system_params (param_key, param_value, created_at, updated_at)
VALUES ('[site.timezone]', '+00:00', UTC_TIMESTAMP(), UTC_TIMESTAMP());

-- 如果已存在时区设置，更新为UTC（可选）
-- UPDATE system_params 
-- SET param_value = '+00:00', updated_at = UTC_TIMESTAMP()
-- WHERE param_key = '[site.timezone]';

-- =====================================================
-- 完成迁移
-- =====================================================
SELECT NOW() as '迁移完成时间', 'UTC时区迁移脚本执行完成' as '状态';

-- 提交事务
COMMIT;

-- =====================================================
-- 迁移后检查清单
-- =====================================================
-- 
-- 请在迁移完成后执行以下检查：
-- 
-- 1. 验证时间数据：
--    SELECT start_time, last_profit_time FROM investments LIMIT 10;
--    SELECT profit_time FROM investment_profits LIMIT 10;
-- 
-- 2. 检查应用程序：
--    - 重启应用服务器
--    - 检查收益发放是否正常
--    - 验证时间显示是否正确
-- 
-- 3. 监控系统：
--    - 观察收益发放日志
--    - 检查用户反馈
--    - 监控系统性能
-- 
-- 4. 如果发现问题，请立即执行回滚：
--    - 恢复数据库备份
--    - 回滚代码更改
--    - 重启服务
-- 
-- =====================================================
