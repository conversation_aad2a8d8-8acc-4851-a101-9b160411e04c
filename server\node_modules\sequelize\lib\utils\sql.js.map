{"version": 3, "sources": ["../../src/utils/sql.ts"], "sourcesContent": ["import isPlainObject from 'lodash/isPlainObject';\nimport type { AbstractDialect } from '../dialects/abstract/index.js';\nimport { escape as escapeSqlValue } from '../sql-string';\n\ntype BindOrReplacements = { [key: string]: unknown } | unknown[];\n\n/**\n * Inlines replacements in places where they would be valid SQL values.\n *\n * @param sqlString The SQL that contains the replacements\n * @param dialect The dialect of the SQL\n * @param replacements if provided, this method will replace ':named' replacements & positional replacements (?)\n *\n * @returns The SQL with replacements rewritten in their dialect-specific syntax.\n */\nexport function injectReplacements(\n  sqlString: string,\n  dialect: AbstractDialect,\n  replacements: BindOrReplacements\n): string {\n  if (replacements == null) {\n    return sqlString;\n  }\n\n  if (!Array.isArray(replacements) && !isPlainObject(replacements)) {\n    throw new TypeError(`\"replacements\" must be an array or a plain object, but received ${JSON.stringify(replacements)} instead.`);\n  }\n\n  const isNamedReplacements = isPlainObject(replacements);\n  const isPositionalReplacements = Array.isArray(replacements);\n  let lastConsumedPositionalReplacementIndex = -1;\n\n  let output = '';\n\n  let currentDollarStringTagName = null;\n  let isString = false;\n  let isColumn = false;\n  let previousSliceEnd = 0;\n  let isSingleLineComment = false;\n  let isCommentBlock = false;\n  let stringIsBackslashEscapable = false;\n\n  for (let i = 0; i < sqlString.length; i++) {\n    const char = sqlString[i];\n\n    if (isColumn) {\n      if (char === dialect.TICK_CHAR_RIGHT) {\n        isColumn = false;\n      }\n\n      continue;\n    }\n\n    if (isString) {\n      if (\n        char === '\\'' &&\n        (!stringIsBackslashEscapable || !isBackslashEscaped(sqlString, i - 1))\n      ) {\n        isString = false;\n        stringIsBackslashEscapable = false;\n      }\n\n      continue;\n    }\n\n    if (currentDollarStringTagName !== null) {\n      if (char !== '$') {\n        continue;\n      }\n\n      const remainingString = sqlString.slice(i, sqlString.length);\n\n      const dollarStringStartMatch = remainingString.match(/^\\$(?<name>[a-z_][0-9a-z_]*)?(\\$)/i);\n      const tagName = dollarStringStartMatch?.groups?.name || '';\n      if (currentDollarStringTagName === tagName) {\n        currentDollarStringTagName = null;\n      }\n\n      continue;\n    }\n\n    if (isSingleLineComment) {\n      if (char === '\\n') {\n        isSingleLineComment = false;\n      }\n\n      continue;\n    }\n\n    if (isCommentBlock) {\n      if (char === '*' && sqlString[i + 1] === '/') {\n        isCommentBlock = false;\n      }\n\n      continue;\n    }\n\n    if (char === dialect.TICK_CHAR_LEFT) {\n      isColumn = true;\n      continue;\n    }\n\n    if (char === '\\'') {\n      isString = true;\n\n      // The following query is supported in almost all dialects,\n      //  SELECT E'test';\n      // but postgres interprets it as an E-prefixed string, while other dialects interpret it as\n      //  SELECT E 'test';\n      // which selects the type E and aliases it to 'test'.\n\n      stringIsBackslashEscapable =\n        // all ''-style strings in this dialect can be backslash escaped\n        dialect.canBackslashEscape() ||\n        // checking if this is a postgres-style E-prefixed string, which also supports backslash escaping\n        dialect.supports.escapeStringConstants &&\n          // is this a E-prefixed string, such as `E'abc'`, `e'abc'` ?\n          (sqlString[i - 1] === 'E' || sqlString[i - 1] === 'e') &&\n          // reject things such as `AE'abc'` (the prefix must be exactly E)\n          canPrecedeNewToken(sqlString[i - 2]);\n\n      continue;\n    }\n\n    if (char === '-' && sqlString.slice(i, i + 3) === '-- ') {\n      isSingleLineComment = true;\n      continue;\n    }\n\n    if (char === '/' && sqlString.slice(i, i + 2) === '/*') {\n      isCommentBlock = true;\n      continue;\n    }\n\n    // either the start of a $bind parameter, or the start of a $tag$string$tag$\n    if (char === '$') {\n      const previousChar = sqlString[i - 1];\n\n      // we are part of an identifier\n      if (/[0-9a-z_]/i.test(previousChar)) {\n        continue;\n      }\n\n      const remainingString = sqlString.slice(i, sqlString.length);\n\n      const dollarStringStartMatch = remainingString.match(/^\\$(?<name>[a-z_][0-9a-z_]*)?(\\$)/i);\n      if (dollarStringStartMatch) {\n        currentDollarStringTagName = dollarStringStartMatch.groups?.name ?? '';\n        i += dollarStringStartMatch[0].length - 1;\n\n        continue;\n      }\n\n      continue;\n    }\n\n    if (isNamedReplacements && char === ':') {\n      const previousChar = sqlString[i - 1];\n      // we want to be conservative with what we consider to be a replacement to avoid risk of conflict with potential operators\n      // users need to add a space before the bind parameter (except after '(', ',', and '=', '[' (for arrays))\n      if (!canPrecedeNewToken(previousChar) && previousChar !== '[') {\n        continue;\n      }\n\n      const remainingString = sqlString.slice(i, sqlString.length);\n\n      const match = remainingString.match(/^:(?<name>[a-z_][0-9a-z_]*)(?:\\)|,|$|\\s|::|;|])/i);\n      const replacementName = match?.groups?.name;\n      if (!replacementName) {\n        continue;\n      }\n\n      // @ts-expect-error -- isPlainObject does not tell typescript that replacements is a plain object, not an array\n      const replacementValue = replacements[replacementName];\n      if (!Object.prototype.hasOwnProperty.call(replacements, replacementName) || replacementValue === undefined) {\n        throw new Error(`Named replacement \":${replacementName}\" has no entry in the replacement map.`);\n      }\n\n      const escapedReplacement = escapeSqlValue(replacementValue, undefined, dialect.name, true);\n\n      // add everything before the bind parameter name\n      output += sqlString.slice(previousSliceEnd, i);\n      // continue after the bind parameter name\n      previousSliceEnd = i + replacementName.length + 1;\n\n      output += escapedReplacement;\n\n      continue;\n    }\n\n    if (isPositionalReplacements && char === '?') {\n      const previousChar = sqlString[i - 1];\n\n      // we want to be conservative with what we consider to be a replacement to avoid risk of conflict with potential operators\n      // users need to add a space before the bind parameter (except after '(', ',', and '=', '[' (for arrays))\n      // -> [ is temporarily added to allow 'ARRAY[:name]' to be replaced\n      // https://github.com/sequelize/sequelize/issues/14410 will make this obsolete.\n      if (!canPrecedeNewToken(previousChar) && previousChar !== '[') {\n        continue;\n      }\n\n      // don't parse ?| and ?& operators as replacements\n      const nextChar = sqlString[i + 1];\n      if (nextChar === '|' || nextChar === '&') {\n        continue;\n      }\n\n      const replacementIndex = ++lastConsumedPositionalReplacementIndex;\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore -- ts < 4.4 loses the information that 'replacements' is an array when using 'isPositionalReplacements' instead of 'Array.isArray'\n      //  but performance matters here.\n      const replacementValue = replacements[lastConsumedPositionalReplacementIndex];\n\n      if (replacementValue === undefined) {\n        throw new Error(`Positional replacement (?) ${replacementIndex} has no entry in the replacement map (replacements[${replacementIndex}] is undefined).`);\n      }\n\n      const escapedReplacement = escapeSqlValue(replacementValue as any, undefined, dialect.name, true);\n\n      // add everything before the bind parameter name\n      output += sqlString.slice(previousSliceEnd, i);\n      // continue after the bind parameter name\n      previousSliceEnd = i + 1;\n\n      output += escapedReplacement;\n    }\n  }\n\n  if (isString) {\n    throw new Error(\n      `The following SQL query includes an unterminated string literal:\\n${sqlString}`\n    );\n  }\n\n  output += sqlString.slice(previousSliceEnd, sqlString.length);\n\n  return output;\n}\n\nfunction canPrecedeNewToken(char: string | undefined): boolean {\n  return char === undefined || /[\\s(>,=]/.test(char);\n}\n\nfunction isBackslashEscaped(string: string, pos: number): boolean {\n  let escaped = false;\n  for (let i = pos; i >= 0; i--) {\n    const char = string[i];\n    if (char !== '\\\\') {\n      break;\n    }\n\n    escaped = !escaped;\n  }\n\n  return escaped;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,2BAA0B;AAE1B,wBAAyC;AAalC,4BACL,WACA,SACA,cACQ;AAnBV;AAoBE,MAAI,gBAAgB,MAAM;AACxB,WAAO;AAAA;AAGT,MAAI,CAAC,MAAM,QAAQ,iBAAiB,CAAC,kCAAc,eAAe;AAChE,UAAM,IAAI,UAAU,mEAAmE,KAAK,UAAU;AAAA;AAGxG,QAAM,sBAAsB,kCAAc;AAC1C,QAAM,2BAA2B,MAAM,QAAQ;AAC/C,MAAI,yCAAyC;AAE7C,MAAI,SAAS;AAEb,MAAI,6BAA6B;AACjC,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,mBAAmB;AACvB,MAAI,sBAAsB;AAC1B,MAAI,iBAAiB;AACrB,MAAI,6BAA6B;AAEjC,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAM,OAAO,UAAU;AAEvB,QAAI,UAAU;AACZ,UAAI,SAAS,QAAQ,iBAAiB;AACpC,mBAAW;AAAA;AAGb;AAAA;AAGF,QAAI,UAAU;AACZ,UACE,SAAS,OACR,EAAC,8BAA8B,CAAC,mBAAmB,WAAW,IAAI,KACnE;AACA,mBAAW;AACX,qCAA6B;AAAA;AAG/B;AAAA;AAGF,QAAI,+BAA+B,MAAM;AACvC,UAAI,SAAS,KAAK;AAChB;AAAA;AAGF,YAAM,kBAAkB,UAAU,MAAM,GAAG,UAAU;AAErD,YAAM,yBAAyB,gBAAgB,MAAM;AACrD,YAAM,UAAU,wEAAwB,WAAxB,mBAAgC,SAAQ;AACxD,UAAI,+BAA+B,SAAS;AAC1C,qCAA6B;AAAA;AAG/B;AAAA;AAGF,QAAI,qBAAqB;AACvB,UAAI,SAAS,MAAM;AACjB,8BAAsB;AAAA;AAGxB;AAAA;AAGF,QAAI,gBAAgB;AAClB,UAAI,SAAS,OAAO,UAAU,IAAI,OAAO,KAAK;AAC5C,yBAAiB;AAAA;AAGnB;AAAA;AAGF,QAAI,SAAS,QAAQ,gBAAgB;AACnC,iBAAW;AACX;AAAA;AAGF,QAAI,SAAS,KAAM;AACjB,iBAAW;AAQX,mCAEE,QAAQ,wBAER,QAAQ,SAAS,yBAEd,WAAU,IAAI,OAAO,OAAO,UAAU,IAAI,OAAO,QAElD,mBAAmB,UAAU,IAAI;AAErC;AAAA;AAGF,QAAI,SAAS,OAAO,UAAU,MAAM,GAAG,IAAI,OAAO,OAAO;AACvD,4BAAsB;AACtB;AAAA;AAGF,QAAI,SAAS,OAAO,UAAU,MAAM,GAAG,IAAI,OAAO,MAAM;AACtD,uBAAiB;AACjB;AAAA;AAIF,QAAI,SAAS,KAAK;AAChB,YAAM,eAAe,UAAU,IAAI;AAGnC,UAAI,aAAa,KAAK,eAAe;AACnC;AAAA;AAGF,YAAM,kBAAkB,UAAU,MAAM,GAAG,UAAU;AAErD,YAAM,yBAAyB,gBAAgB,MAAM;AACrD,UAAI,wBAAwB;AAC1B,qCAA6B,mCAAuB,WAAvB,mBAA+B,SAA/B,YAAuC;AACpE,aAAK,uBAAuB,GAAG,SAAS;AAExC;AAAA;AAGF;AAAA;AAGF,QAAI,uBAAuB,SAAS,KAAK;AACvC,YAAM,eAAe,UAAU,IAAI;AAGnC,UAAI,CAAC,mBAAmB,iBAAiB,iBAAiB,KAAK;AAC7D;AAAA;AAGF,YAAM,kBAAkB,UAAU,MAAM,GAAG,UAAU;AAErD,YAAM,QAAQ,gBAAgB,MAAM;AACpC,YAAM,kBAAkB,qCAAO,WAAP,mBAAe;AACvC,UAAI,CAAC,iBAAiB;AACpB;AAAA;AAIF,YAAM,mBAAmB,aAAa;AACtC,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,cAAc,oBAAoB,qBAAqB,QAAW;AAC1G,cAAM,IAAI,MAAM,uBAAuB;AAAA;AAGzC,YAAM,qBAAqB,8BAAe,kBAAkB,QAAW,QAAQ,MAAM;AAGrF,gBAAU,UAAU,MAAM,kBAAkB;AAE5C,yBAAmB,IAAI,gBAAgB,SAAS;AAEhD,gBAAU;AAEV;AAAA;AAGF,QAAI,4BAA4B,SAAS,KAAK;AAC5C,YAAM,eAAe,UAAU,IAAI;AAMnC,UAAI,CAAC,mBAAmB,iBAAiB,iBAAiB,KAAK;AAC7D;AAAA;AAIF,YAAM,WAAW,UAAU,IAAI;AAC/B,UAAI,aAAa,OAAO,aAAa,KAAK;AACxC;AAAA;AAGF,YAAM,mBAAmB,EAAE;AAI3B,YAAM,mBAAmB,aAAa;AAEtC,UAAI,qBAAqB,QAAW;AAClC,cAAM,IAAI,MAAM,8BAA8B,sEAAsE;AAAA;AAGtH,YAAM,qBAAqB,8BAAe,kBAAyB,QAAW,QAAQ,MAAM;AAG5F,gBAAU,UAAU,MAAM,kBAAkB;AAE5C,yBAAmB,IAAI;AAEvB,gBAAU;AAAA;AAAA;AAId,MAAI,UAAU;AACZ,UAAM,IAAI,MACR;AAAA,EAAqE;AAAA;AAIzE,YAAU,UAAU,MAAM,kBAAkB,UAAU;AAEtD,SAAO;AAAA;AAGT,4BAA4B,MAAmC;AAC7D,SAAO,SAAS,UAAa,WAAW,KAAK;AAAA;AAG/C,4BAA4B,QAAgB,KAAsB;AAChE,MAAI,UAAU;AACd,WAAS,IAAI,KAAK,KAAK,GAAG,KAAK;AAC7B,UAAM,OAAO,OAAO;AACpB,QAAI,SAAS,MAAM;AACjB;AAAA;AAGF,cAAU,CAAC;AAAA;AAGb,SAAO;AAAA;", "names": []}