# MAYA银行payin_method映射问题修复说明

## 📋 **问题描述**

客户绑定菲律宾二类MAYA银行卡时，系统发送给kbpay的payin_method是1110，但应该是1249。

## 🔍 **问题分析**

### **数据库映射关系**
```sql
-- 银行映射表数据
SELECT * FROM bank_channel_mappings;
+----+---------+--------------------+--------------+---------------+
| id | bank_id | payment_channel_id | payin_method | payout_method |
+----+---------+--------------------+--------------+---------------+
| 22 |      16 |                  1 |         1110 |          NULL |
| 24 |      17 |                  1 |         1249 |          NULL |
+----+---------+--------------------+--------------+---------------+

-- 银行表数据
SELECT * FROM banks;
+----+----------------+
| id | name           |
+----+----------------+
| 16 | 菲律宾二类     |
| 17 | 菲律宾二类MAYA |
+----+----------------+
```

### **问题根源**
1. **BankCard模型缺少bank_id字段定义**
2. **前端硬编码银行ID为16**
3. **银行映射查询失败**

## 🔧 **修复方案**

### **1. 修复BankCard模型**
**文件**: `server\models\BankCard.js`

添加缺失的bank_id字段：
```javascript
bank_id: {
  type: DataTypes.INTEGER,
  allowNull: true,
  comment: '银行ID',
},
```

添加银行关联关系：
```javascript
// 银行卡与银行
BankCard.belongsTo(models.Bank, {
  foreignKey: 'bank_id',
  as: 'bank',
});
```

### **2. 修复前端硬编码问题**
**文件**: `mobile\pages\recharge\index.vue`

**修改前**（硬编码）：
```javascript
bank_card_id: paymentType.code === 'kbpay' ? 16 : null // 临时测试：kbpay时传递银行ID 16
```

**修改后**（动态选择）：
```javascript
bank_card_id: paymentType.code === 'kbpay' ? this.selectedBankId : null
```

### **3. 添加银行选择功能**

#### **数据属性**
```javascript
data() {
  return {
    selectedBankId: null, // 选中的银行ID
    banks: [], // 可用银行列表
    formErrors: {
      amount: '',
      paymentType: '',
      bank: ''
    }
  }
}
```

#### **获取银行列表方法**
```javascript
async fetchBanks() {
  try {
    const paymentChannelId = this.paymentTypeIndex > -1 ? this.paymentTypes[this.paymentTypeIndex]?.id : null;
    const response = await getBanks({ payment_channel_id: paymentChannelId });

    if (response && response.code === 200 && response.data) {
      this.banks = response.data.filter(bank => bank.payin_method);
      
      // 默认选择菲律宾二类MAYA银行
      const mayaBank = this.banks.find(bank => bank.name === '菲律宾二类MAYA');
      if (mayaBank) {
        this.selectedBankId = mayaBank.id;
      } else if (this.banks.length > 0) {
        this.selectedBankId = this.banks[0].id;
      }
    }
  } catch (error) {
    console.error('获取银行列表失败:', error);
    this.banks = [];
  }
}
```

#### **UI界面**
添加银行选择器：
```html
<!-- 银行选择（仅当选择kbpay时显示） -->
<view class="form-group" v-if="paymentTypeIndex > -1 && paymentTypes[paymentTypeIndex].code === 'kbpay'">
  <text class="form-label">Select Bank</text>
  <view class="select-wrapper" @click="showBankSelector">
    <view class="form-select" :class="{ 'error-input': formErrors.bank }">
      {{ selectedBankId ? getBankName(selectedBankId) : 'Select bank' }}
    </view>
  </view>
</view>
```

## ✅ **修复验证**

### **测试结果**
从服务器日志可以看到修复成功：

```
=== 查询银行映射信息 ===
银行ID: 17
支付通道ID: 1

银行映射查询结果: {
  id: 24,
  bank_id: 17,
  bank_name: '菲律宾二类MAYA',
  payin_method: 1249,
  payment_channel_id: 1
}

设置payin_method为: 1249
发送给kbpay的payin_method: 1249

KB支付响应: {
  code: 0,
  data: {
    merchant_no: '6086083',
    order_amount: 333,
    order_no: 'RE202505297229367835',
    trade_no: 'KBP1748460368136',
    url: 'https://api.kbpay.io/online/payin/stage?trade_no=KBP1748460368136'
  },
  message: 'success'
}
```

### **关键改进**
1. ✅ **正确识别银行** - 银行ID 17（菲律宾二类MAYA）
2. ✅ **正确映射payin_method** - 1249（而不是1110）
3. ✅ **KB支付成功** - 返回支付链接
4. ✅ **用户体验提升** - 可以选择不同银行

## 📊 **影响范围**

### **修改文件**
- `server\models\BankCard.js` - 添加bank_id字段和关联关系
- `mobile\pages\recharge\index.vue` - 添加银行选择功能

### **数据库**
- 无需修改数据库结构（bank_id字段已存在）
- 银行映射数据正确

### **兼容性**
- ✅ 向后兼容
- ✅ 不影响其他支付方式
- ✅ 不影响现有充值订单

## 🎯 **总结**

通过修复BankCard模型的字段定义和添加银行选择功能，成功解决了MAYA银行payin_method映射错误的问题。现在：

- **菲律宾二类** (ID: 16) → payin_method: 1110
- **菲律宾二类MAYA** (ID: 17) → payin_method: 1249

用户可以正确选择银行，系统会发送正确的payin_method给kbpay，确保支付流程正常运行。
