const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AccountBalance = sequelize.define('AccountBalance', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
  },
  account_type: {
    type: DataTypes.ENUM('income', 'deposit'),
    allowNull: false,
    comment: '账户类型：income=收入账户, deposit=充值账户',
  },

  balance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '账户余额',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'account_balances',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'account_type'],
    },
  ],
});

// 定义关联关系
AccountBalance.associate = (models) => {
  AccountBalance.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });
};

module.exports = AccountBalance;
