const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const BankCard = sequelize.define('BankCard', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '用户ID，NULL表示系统收款卡',
  },
  bank_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '银行ID',
  },
  bank_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '银行名称',
  },
  card_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '卡号',
  },
  card_holder: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '持卡人姓名',
  },
  bank_code: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '银行编码',
  },

  card_type: {
    type: DataTypes.ENUM('user', 'system'),
    allowNull: false,
    comment: '卡类型：user=用户卡, system=系统收款卡',
  },
  daily_limit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: '日限额，仅对系统卡有效',
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '状态：true=启用, false=禁用',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'bank_cards',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
BankCard.associate = (models) => {
  BankCard.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });

  // 银行卡与银行
  BankCard.belongsTo(models.Bank, {
    foreignKey: 'bank_id',
    as: 'bank',
  });

  // 银行卡与充值订单
  BankCard.hasMany(models.Deposit, {
    foreignKey: 'receiving_card_id',
    as: 'deposits',
  });

  // 银行卡与提现记录
  BankCard.hasMany(models.Withdrawal, {
    foreignKey: 'bank_card_id',
    as: 'withdrawals',
  });
};

module.exports = BankCard;
