/**
 * Base支付服务
 */
const axios = require('axios');
const crypto = require('crypto');
const { PaymentChannel, Deposit, Withdrawal, BankChannelMapping } = require('../models');

/**
 * Base支付服务
 */
class BasePayService {
  /**
   * 创建Base支付服务实例
   * @param {Object} config - 支付通道配置
   */
  constructor(config) {
    this.config = config;
  }

  /**
   * 生成MD5签名
   * @param {Object} params - 请求参数
   * @param {string} key - 签名密钥
   * @returns {string} MD5签名
   */
  generateSignature(params, key) {
    // 按照参数名ASCII码从小到大排序
    const sortedParams = {};
    Object.keys(params).sort().forEach(paramKey => {
      if (params[paramKey] !== null && params[paramKey] !== undefined && params[paramKey] !== '') {
        sortedParams[paramKey] = params[paramKey];
      }
    });

    // 构建签名字符串
    let signStr = '';
    for (const paramKey in sortedParams) {
      signStr += `${paramKey}=${sortedParams[paramKey]}&`;
    }
    signStr += `key=${key}`;

    console.log('Base支付签名字符串:', signStr);

    // 生成MD5签名（小写）
    return crypto.createHash('md5').update(signStr).digest('hex');
  }

  /**
   * 创建充值订单（代收）
   * @param {Object} depositOrder - 充值订单信息
   * @param {number} bankId - 银行ID（可选）
   * @returns {Promise<Object>} 支付结果
   */
  async createPayment(depositOrder, bankId) {
    try {
      // 检查代收密钥是否存在
      if (!this.config.payin_key) {
        throw new Error('代收密钥未配置');
      }

      // 构建请求参数
      const orderDate = new Date().toISOString().slice(0, 19).replace('T', ' '); // yyyy-MM-dd HH:mm:ss
      const params = {
        version: '1.0',
        mch_id: this.config.merchant_no,
        notify_url: `https://m.ohyeah012.xyz/api/payment-callbacks/basepay`,
        page_url: `https://m.ohyeah012.xyz/recharge/success`,
        mch_order_no: depositOrder.order_number,
        pay_type: this.config.pay_type || '1720', // 菲律宾二类
        trade_amount: depositOrder.amount.toFixed(2),
        order_date: orderDate,
        goods_name: 'Top Up',
        mch_return_msg: `user_id:${depositOrder.user_id}`
      };

      // 生成签名（不包含sign_type和sign）
      const signature = this.generateSignature(params, this.config.payin_key);

      // 添加不参与签名的字段
      params.sign_type = 'MD5';
      params.sign = signature;

      console.log('=== Base支付代收请求 ===');
      console.log('请求URL:', this.config.payin_url || 'https://pay.aiffpay.com/pay/web');
      console.log('请求参数:', params);

      // 发送请求
      const response = await axios.post(
        this.config.payin_url || 'https://pay.aiffpay.com/pay/web',
        new URLSearchParams(params),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      console.log('=== Base支付代收响应 ===');
      console.log('响应状态:', response.status);
      console.log('响应数据:', response.data);

      // 解析响应
      const result = response.data;

      if (result.respCode === 'SUCCESS' && result.tradeResult === '1') {
        return {
          success: true,
          paymentUrl: result.payInfo,
          platformOrderNo: result.orderNo,
          message: '订单创建成功'
        };
      } else {
        return {
          success: false,
          message: result.tradeMsg || '订单创建失败',
          error: result
        };
      }
    } catch (error) {
      console.error('Base支付代收请求失败:', error);
      return {
        success: false,
        message: error.message || '支付请求失败',
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * 验证回调签名
   * @param {Object} params - 回调参数
   * @param {string} keyType - 密钥类型，'payin_key'或'payout_key'
   * @returns {boolean} 签名是否有效
   */
  verifyCallbackSignature(params, keyType = 'payin_key') {
    // 提取签名
    const receivedSign = params.sign;

    // 创建用于验证的参数对象（不包含sign和signType字段）
    const verifyParams = { ...params };
    delete verifyParams.sign;
    delete verifyParams.signType;

    // 生成签名
    const calculatedSign = this.generateSignature(verifyParams, this.config[keyType]);

    console.log('Base支付回调签名验证:');
    console.log('接收到的签名:', receivedSign);
    console.log('计算出的签名:', calculatedSign);

    // 比较签名
    return receivedSign === calculatedSign;
  }

  /**
   * 创建提现订单（代付）
   * @param {Object} withdrawalOrder - 提现订单信息
   * @param {Object} bankCard - 银行卡信息
   * @returns {Promise<Object>} 代付结果
   */
  async createPayout(withdrawalOrder, bankCard) {
    try {
      // 检查代付密钥是否存在
      if (!this.config.payout_key) {
        throw new Error('代付密钥未配置');
      }

      // 构建请求参数
      const actualAmount = parseFloat(withdrawalOrder.actual_amount) || parseFloat(withdrawalOrder.amount) || 0;

      const applyDate = new Date().toISOString().slice(0, 19).replace('T', ' '); // yyyy-MM-dd HH:mm:ss
      const params = {
        mch_id: this.config.merchant_no,
        mch_transferId: withdrawalOrder.order_number,
        transfer_amount: actualAmount.toFixed(2),
        apply_date: applyDate,
        bank_code: this.config.bank_code || 'GCASH', // 使用GCASH银行代码
        receive_name: bankCard.card_holder,
        receive_account: bankCard.card_number,
        remark: `Withdrawal for user ${withdrawalOrder.user_id}`,
        back_url: `https://m.ohyeah012.xyz/api/payment-callbacks/basepay-payout`
      };

      // 生成签名（不包含sign_type和sign）
      const signature = this.generateSignature(params, this.config.payout_key);

      // 添加不参与签名的字段
      params.sign_type = 'MD5';
      params.sign = signature;



      // 发送请求
      const response = await axios.post(
        this.config.payout_url || 'https://pay.aiffpay.com/pay/transfer',
        new URLSearchParams(params),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );



      // 解析响应
      const result = response.data;

      if (result.respCode === 'SUCCESS') {
        return {
          success: true,
          platformOrderNo: result.tradeNo,
          status: result.tradeResult, // 0:申请成功, 1:转账成功, 2:转账失败, 3:转账拒绝, 4:转账处理中
          message: '代付订单创建成功'
        };
      } else {
        return {
          success: false,
          message: result.errorMsg || '代付订单创建失败',
          error: result
        };
      }
    } catch (error) {
      console.error('Base支付代付请求失败:', error);
      return {
        success: false,
        message: error.message || '代付请求失败',
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * 查询代收订单状态
   * @param {string} orderNo - 商户订单号
   * @returns {Promise<Object>} 查询结果
   */
  async queryPayinOrder(orderNo) {
    try {
      // Base支付暂未提供代收订单查询接口，返回默认状态
      console.log('Base支付代收订单查询:', orderNo);
      return {
        success: false,
        message: 'Base支付暂不支持代收订单查询'
      };
    } catch (error) {
      console.error('Base支付代收订单查询失败:', error);
      return {
        success: false,
        message: error.message || '查询失败',
        error: error.response?.data || error.message
      };
    }
  }

  /**
   * 查询代付订单状态
   * @param {string} orderNo - 商户订单号
   * @returns {Promise<Object>} 查询结果
   */
  async queryPayoutOrder(orderNo) {
    try {
      // Base支付暂未提供代付订单查询接口，返回默认状态
      console.log('Base支付代付订单查询:', orderNo);
      return {
        success: false,
        message: 'Base支付暂不支持代付订单查询'
      };
    } catch (error) {
      console.error('Base支付代付订单查询失败:', error);
      return {
        success: false,
        message: error.message || '查询失败',
        error: error.response?.data || error.message
      };
    }
  }
}

/**
 * 获取Base支付服务实例
 * @param {number} channelId - 支付通道ID
 * @returns {Promise<BasePayService>} Base支付服务实例
 */
async function getBasePayService(channelId) {
  // 查询支付通道
  const paymentChannel = await PaymentChannel.findByPk(channelId);

  if (!paymentChannel) {
    throw new Error('支付通道不存在');
  }

  // 解析配置
  const config = paymentChannel.config ?
    (typeof paymentChannel.config === 'string' ? JSON.parse(paymentChannel.config) : paymentChannel.config)
    : {};

  // 检查必要配置
  if (!config.merchant_no) {
    throw new Error('支付通道配置不完整：缺少商户号');
  }

  // 设置代收API URL
  if (!config.payin_key) {
    console.warn('支付通道配置不完整：缺少代收密钥，代收功能将不可用');
  } else {
    // 使用正式API地址
    config.payin_url = 'https://pay.aiffpay.com/pay/web';
    console.log('Base支付代收API地址已设置为:', config.payin_url);
  }

  // 设置代付API URL
  if (!config.payout_key) {
    console.warn('支付通道配置不完整：缺少代付密钥，代付功能将不可用');
  } else {
    // 使用正式API地址
    config.payout_url = 'https://pay.aiffpay.com/pay/transfer';
    console.log('Base支付代付API地址已设置为:', config.payout_url);
  }

  // 创建服务实例
  return new BasePayService(config);
}

module.exports = {
  BasePayService,
  getBasePayService
};
