/**
 * 时区诊断工具
 * 用于诊断和排查时区相关问题
 */
const moment = require('moment-timezone');
const sequelize = require('../config/database');
const timezoneUtils = require('./timezoneUtils');

/**
 * 运行时区诊断
 * @returns {Promise<Object>} 诊断结果
 */
async function runDiagnostic() {
  try {
    console.log('开始运行时区诊断...');
    
    const results = {
      system: {},
      database: {},
      moment: {},
      nodejs: {},
      timezone_utils: {}
    };
    
    // 获取系统时区设置
    const systemTimezone = await timezoneUtils.getSystemTimezone();
    results.system.timezone = systemTimezone;
    results.system.default_timezone = timezoneUtils.DEFAULT_TIMEZONE;
    
    // 获取数据库时区设置
    const [dbTimeResult] = await sequelize.query('SELECT NOW() as now, @@session.time_zone as timezone, @@system_time_zone as system_timezone');
    results.database.now = dbTimeResult[0].now;
    results.database.session_timezone = dbTimeResult[0].timezone;
    results.database.system_timezone = dbTimeResult[0].system_timezone;
    
    // 获取Moment时区信息
    results.moment.utc_now = moment.utc().format('YYYY-MM-DD HH:mm:ss Z');
    results.moment.local_now = moment().format('YYYY-MM-DD HH:mm:ss Z');
    results.moment.system_timezone_now = moment().utcOffset(systemTimezone).format('YYYY-MM-DD HH:mm:ss Z');
    
    // 获取Node.js时区信息
    results.nodejs.now = new Date().toISOString();
    results.nodejs.local_string = new Date().toString();
    results.nodejs.timezone_offset = new Date().getTimezoneOffset();
    
    // 测试时区工具
    const now = await timezoneUtils.getCurrentTime();
    const todayStart = await timezoneUtils.getTodayStart();
    const todayEnd = await timezoneUtils.getTodayEnd();
    
    results.timezone_utils.now = now.format('YYYY-MM-DD HH:mm:ss Z');
    results.timezone_utils.today_start = todayStart.format('YYYY-MM-DD HH:mm:ss Z');
    results.timezone_utils.today_end = todayEnd.format('YYYY-MM-DD HH:mm:ss Z');
    
    // 测试时区转换
    const testDate = '2025-05-10 12:00:00';
    const convertedDate = await timezoneUtils.convertToSystemTimezone(testDate);
    results.timezone_utils.test_date = testDate;
    results.timezone_utils.converted_date = convertedDate ? convertedDate.format('YYYY-MM-DD HH:mm:ss Z') : 'Invalid date';
    
    // 输出诊断结果
    console.log('时区诊断结果:');
    console.log(JSON.stringify(results, null, 2));
    
    return results;
  } catch (error) {
    console.error('运行时区诊断失败:', error);
    throw error;
  }
}

/**
 * 测试投资收益时间计算
 * @param {number} investmentId - 投资ID
 * @returns {Promise<Object>} 测试结果
 */
async function testProfitTimeCalculation(investmentId) {
  try {
    console.log(`开始测试投资ID ${investmentId} 的收益时间计算...`);
    
    // 导入必要的模型和服务
    const { Investment, Project } = require('../models');
    const profitService = require('../services/profitService');
    
    // 获取投资记录
    const investment = await Investment.findByPk(investmentId);
    if (!investment) {
      throw new Error(`未找到ID为 ${investmentId} 的投资记录`);
    }
    
    // 获取项目信息
    const project = await Project.findByPk(investment.project_id);
    if (!project) {
      throw new Error(`未找到ID为 ${investment.project_id} 的项目`);
    }
    
    // 获取当前时间
    const now = await timezoneUtils.getCurrentTime();
    
    // 获取上次收益时间或投资开始时间
    const lastProfitTime = investment.last_profit_time
      ? await timezoneUtils.convertToSystemTimezone(investment.last_profit_time)
      : await timezoneUtils.convertToSystemTimezone(investment.start_time);
    
    // 确保两个时间对象使用相同的时区
    const systemTimezone = await timezoneUtils.getSystemTimezone();
    const normalizedNow = now.clone().utcOffset(systemTimezone);
    const normalizedLastProfitTime = lastProfitTime ? lastProfitTime.clone().utcOffset(systemTimezone) : null;
    
    // 计算距离上次收益的小时数
    const hoursSinceLastProfit = normalizedLastProfitTime ? normalizedNow.diff(normalizedLastProfitTime, 'hours') : null;
    
    // 收集测试结果
    const results = {
      investment: {
        id: investment.id,
        project_id: investment.project_id,
        start_time: investment.start_time,
        last_profit_time: investment.last_profit_time,
        profit_count: investment.profit_count,
        status: investment.status
      },
      project: {
        id: project.id,
        name: project.name,
        profit_time: project.profit_time,
        max_profit_times: project.max_profit_times
      },
      time_calculation: {
        system_timezone: systemTimezone,
        current_time: normalizedNow.format('YYYY-MM-DD HH:mm:ss Z'),
        last_profit_time: normalizedLastProfitTime ? normalizedLastProfitTime.format('YYYY-MM-DD HH:mm:ss Z') : 'N/A',
        hours_since_last_profit: hoursSinceLastProfit,
        should_distribute_profit: hoursSinceLastProfit >= project.profit_time
      }
    };
    
    // 输出测试结果
    console.log('投资收益时间计算测试结果:');
    console.log(JSON.stringify(results, null, 2));
    
    return results;
  } catch (error) {
    console.error('测试投资收益时间计算失败:', error);
    throw error;
  }
}

module.exports = {
  runDiagnostic,
  testProfitTimeCalculation
};
