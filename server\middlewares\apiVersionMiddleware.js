/**
 * API版本控制中间件
 * 处理API版本控制，支持URL路径和请求头两种方式
 */
const apiVersionMiddleware = (req, res, next) => {
  // 从URL路径中提取版本信息
  const urlVersion = req.path.match(/^\/api\/v(\d+)\//);
  
  // 从请求头中获取版本信息
  const headerVersion = req.headers['accept-version'];
  
  // 确定API版本
  let apiVersion;
  
  if (urlVersion && urlVersion[1]) {
    // 如果URL中包含版本信息，优先使用
    apiVersion = parseInt(urlVersion[1], 10);
  } else if (headerVersion) {
    // 如果请求头中包含版本信息，其次使用
    apiVersion = parseInt(headerVersion, 10);
  } else {
    // 默认使用最新版本
    apiVersion = 1;
  }
  
  // 将API版本添加到请求对象中
  req.apiVersion = apiVersion;
  
  // 添加版本信息到响应头
  res.setHeader('X-API-Version', apiVersion.toString());
  
  next();
};

module.exports = apiVersionMiddleware;
