{"version": 3, "file": "ca.mjs", "sources": ["../../../../../packages/locale/lang/ca.ts"], "sourcesContent": ["export default {\n  name: 'ca',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Confirmar',\n      clear: 'Netejar',\n    },\n    datepicker: {\n      now: 'Ara',\n      today: 'Avui',\n      cancel: 'Cancel·lar',\n      clear: 'Netejar',\n      confirm: 'Confirmar',\n      selectDate: 'Seleccionar data',\n      selectTime: 'Seleccionar hora',\n      startDate: 'Data Inici',\n      startTime: 'Hora Inici',\n      endDate: 'Data Final',\n      endTime: 'Hora Final',\n      prevYear: 'Any anterior',\n      nextYear: 'Pròxim Any',\n      prevMonth: 'Mes anterior',\n      nextMonth: 'Pròxim Mes',\n      year: '',\n      month1: 'Gener',\n      month2: 'Febrer',\n      month3: 'Març',\n      month4: 'Abril',\n      month5: 'Maig',\n      month6: 'Juny',\n      month7: 'Juliol',\n      month8: 'Agost',\n      month9: 'Setembre',\n      month10: 'Octubre',\n      month11: 'Novembre',\n      month12: 'Desembre',\n      // week: 'setmana',\n      weeks: {\n        sun: 'Dg',\n        mon: 'Dl',\n        tue: 'Dt',\n        wed: 'Dc',\n        thu: 'Dj',\n        fri: 'Dv',\n        sat: 'Ds',\n      },\n      months: {\n        jan: 'Gen',\n        feb: 'Febr',\n        mar: 'Març',\n        apr: 'Abr',\n        may: 'Maig',\n        jun: 'Juny',\n        jul: 'Jul',\n        aug: 'Ag',\n        sep: 'Set',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Carregant',\n      noMatch: 'No hi ha dades que coincideixin',\n      noData: 'Sense Dades',\n      placeholder: 'Seleccionar',\n    },\n    mention: {\n      loading: 'Carregant',\n    },\n    cascader: {\n      noMatch: 'No hi ha dades que coincideixin',\n      loading: 'Carregant',\n      placeholder: 'Seleccionar',\n      noData: 'Sense Dades',\n    },\n    pagination: {\n      goto: 'Anar a',\n      pagesize: '/pàgina',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'Acceptar',\n      cancel: 'Cancel·lar',\n      error: 'Entrada invàlida',\n    },\n    upload: {\n      deleteTip: 'premi eliminar per descartar',\n      delete: 'Eliminar',\n      preview: 'Vista Prèvia',\n      continue: 'Continuar',\n    },\n    table: {\n      emptyText: 'Sense Dades',\n      confirmFilter: 'Confirmar',\n      resetFilter: 'Netejar',\n      clearFilter: 'Tot',\n      sumText: 'Tot',\n    },\n    tree: {\n      emptyText: 'Sense Dades',\n    },\n    transfer: {\n      noMatch: 'No hi ha dades que coincideixin',\n      noData: 'Sense Dades',\n      titles: ['Llista 1', 'Llista 2'],\n      filterPlaceholder: 'Introdueix la paraula clau',\n      noCheckedFormat: '{total} ítems',\n      hasCheckedFormat: '{checked}/{total} seleccionats',\n    },\n    image: {\n      error: 'HA FALLAT',\n    },\n    pageHeader: {\n      title: 'Tornar',\n    },\n    popconfirm: {\n      confirmButtonText: 'Sí',\n      cancelButtonText: 'No',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,UAAU,EAAE,kBAAkB;AACpC,MAAM,UAAU,EAAE,kBAAkB;AACpC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,iCAAiC;AAChD,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,WAAW,EAAE,aAAa;AAChC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,iCAAiC;AAChD,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,qBAAqB;AAClC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,8BAA8B;AAC/C,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,QAAQ,EAAE,WAAW;AAC3B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,aAAa,EAAE,WAAW;AAChC,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,aAAa;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,iCAAiC;AAChD,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AACtC,MAAM,iBAAiB,EAAE,4BAA4B;AACrD,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,gBAAgB,EAAE,gCAAgC;AACxD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,WAAW;AACxB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,OAAO;AAChC,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}