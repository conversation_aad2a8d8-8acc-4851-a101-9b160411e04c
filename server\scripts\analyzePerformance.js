/**
 * 综合性能分析脚本
 * 用于分析 Node.js 应用程序的整体性能
 */
const { logMemoryUsage } = require('./memoryMonitor');
const { measureAsyncFunctionExecution } = require('./performanceAnalyzer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 创建日志目录
const logDir = path.join(__dirname, '../logs/performance');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 记录系统信息
function logSystemInfo() {
  console.log('系统信息:');
  console.log(`Node.js 版本: ${process.version}`);
  console.log(`平台: ${process.platform}`);
  console.log(`架构: ${process.arch}`);
  console.log(`进程 ID: ${process.pid}`);
  console.log(`当前工作目录: ${process.cwd()}`);
  
  // 记录 CPU 和内存信息
  try {
    const cpuInfo = execSync('wmic cpu get Name, NumberOfCores, NumberOfLogicalProcessors /format:list').toString();
    const memInfo = execSync('wmic OS get TotalVisibleMemorySize, FreePhysicalMemory /format:list').toString();
    
    console.log('\nCPU 信息:');
    console.log(cpuInfo);
    
    console.log('内存信息:');
    console.log(memInfo);
  } catch (error) {
    console.log('无法获取详细系统信息');
  }
}

// 测试 API 端点性能
async function testApiEndpoint(url, method = 'GET', data = null, headers = {}) {
  try {
    const startTime = Date.now();
    
    const response = await axios({
      method,
      url,
      data,
      headers,
      timeout: 30000
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    return {
      url,
      method,
      statusCode: response.status,
      duration: `${duration}ms`,
      dataSize: JSON.stringify(response.data).length,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      url,
      method,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// 测试常用 API 端点
async function testCommonEndpoints(baseUrl, token) {
  const endpoints = [
    { url: `${baseUrl}/api/admin/stats/dashboard`, method: 'GET' },
    { url: `${baseUrl}/api/admin/users`, method: 'GET' },
    { url: `${baseUrl}/api/admin/projects`, method: 'GET' },
    { url: `${baseUrl}/api/admin/transactions`, method: 'GET' }
  ];
  
  const headers = {
    'Authorization': `Bearer ${token}`
  };
  
  console.log('开始测试 API 端点性能...');
  
  const results = [];
  for (const endpoint of endpoints) {
    console.log(`测试端点: ${endpoint.method} ${endpoint.url}`);
    const result = await measureAsyncFunctionExecution(
      testApiEndpoint,
      `API-${endpoint.method}-${endpoint.url.split('/').pop()}`,
      endpoint.url,
      endpoint.method,
      null,
      headers
    );
    results.push(result);
  }
  
  console.log('\nAPI 端点性能测试结果:');
  console.table(results);
  
  // 保存结果
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filepath = path.join(logDir, `api-performance-${timestamp}.json`);
  fs.writeFileSync(filepath, JSON.stringify(results, null, 2));
  console.log(`API 性能测试结果已保存到: ${filepath}`);
  
  return results;
}

// 主函数
async function main() {
  // 记录系统信息
  logSystemInfo();
  
  // 记录内存使用情况
  console.log('\n初始内存使用情况:');
  logMemoryUsage();
  
  // 提示用户输入 API 基础 URL 和令牌
  console.log('\n请在浏览器中登录管理面板，然后获取令牌');
  console.log('令牌可以从浏览器开发者工具的 Application > Local Storage 中找到');
  console.log('键名为 "token"');
  
  // 在实际使用时，可以通过命令行参数或环境变量获取这些值
  const baseUrl = 'http://localhost:3000'; // 替换为实际的 API 基础 URL
  const token = ''; // 替换为实际的令牌
  
  if (token) {
    // 测试 API 端点性能
    await testCommonEndpoints(baseUrl, token);
  } else {
    console.log('未提供令牌，跳过 API 端点测试');
  }
  
  // 再次记录内存使用情况
  console.log('\n最终内存使用情况:');
  logMemoryUsage();
  
  console.log('\n性能分析完成!');
}

// 如果作为独立脚本运行，则执行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('性能分析过程中出错:', error);
    process.exit(1);
  });
}

module.exports = {
  logSystemInfo,
  testApiEndpoint,
  testCommonEndpoints
};
