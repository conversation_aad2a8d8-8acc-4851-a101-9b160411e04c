const redisClient = require('./utils/redisClient');

async function checkTasks() {
  try {
    // 获取所有收益任务
    const tasks = await redisClient.getAllProfitTasks();
    console.log('所有收益任务:', JSON.stringify(tasks, null, 2));
    
    // 检查特定投资ID的任务
    const investmentId = 116;
    const member = `investment:${investmentId}`;
    const exists = await redisClient.client.zscore('investment:profit:tasks', member);
    console.log(`投资ID ${investmentId} 的任务存在:`, exists !== null);
    
    if (exists) {
      console.log(`投资ID ${investmentId} 的下一次收益时间:`, new Date(parseInt(exists)));
    }
    
    // 检查轮询器状态
    console.log('轮询器模块:', require('./services/profitPoller'));
    
    process.exit(0);
  } catch (error) {
    console.error('检查任务失败:', error);
    process.exit(1);
  }
}

checkTasks();
