# 投资项目字段说明文档

本文档提供投资项目页面各字段的详细说明，帮助管理员正确配置投资项目。

## 基本信息

### 项目名称
表示项目的名称，用于在用户端显示。

### 项目类型
表示项目的类型，目前固定为"产品"。

### 项目分类
表示项目的分类，目前固定为"基础"。

### 货币类型
表示项目使用的货币类型，目前固定为"法币"(CNY)。

### 价格类型
表示项目的价格类型，目前固定为"固定价格"。

### 支付方式
表示用户购买项目时可以使用的支付方式：
- 余额支付：用户使用账户余额支付
- 银行卡支付：用户使用银行卡支付
- 混合支付：用户可以使用多种方式支付

### 价格
表示购买时的价格，单位为元。

### 卖出价格
表示产品卖出时的价格，单位为元。如果是0则代表不允许卖出。

### 购买时间
表示用户可以购买的时间段。例如：如果选中1-5时间段则表示只能在1点至5点的时间段内购买，其它时间段不能购买。

### 最少投资金额
表示最低投资金额只能是这么多，单位为元。

### 最多投资金额
表示最多能购买多少金额，单位为元。

### 排序
表示在用户端显示的顺序，数值越大则表示排序越靠前，数值最大为100。

### 收益率(%)
表示产品的收益率是多少，单位为百分比。

### 投资周期(天)
表示产品在多少天内能产生收益，超出时间范围则不再产生收益。

### 收益时间(小时)
表示从用户购买后多长时间产生一次收益，单位是小时。
例子：用户A在3点购买产品，如果这里的值是12小时产生一次收益，那么用户A首次产生收益时间是在15点产生一次收益，第二次产生收益时间会在第一次收益时间后12小时，每12小时产生一次收益。

### 最多收益次数
控制用户在投资周期内会产生多少次收益，0表示不限制次数。

### 每周收益日
表示产品会在每周哪一天会产生收益。
例子：如果产品选中的每周收益日为周一至周五，那么这个产品只会在周一至周五产生收益，其它时间不产生收益。

### VIP级别
表示用户等级，用户初始等级为VIP0。

### VIP类型
表示是否等于、大于等于、小于等于，需要和VIP级别一起使用：
- 例子1：VIP级别选中VIP0，VIP类型选中大于等于，那么这个产品只有用户等级大于等于0才能购买。因为用户初始等级为0，那么表示所有用户都可以购买这个产品。
- 例子2：VIP级别选中VIP1，VIP类型选中大于等于，那么这个产品只有用户等级大于等于1才能购买，用户等级为0没有购买权限。
- 例子3：VIP级别选中VIP0，VIP类型选中等于，那么这个产品只有用户等级等于0的时候才能购买这个产品，不等于这个等级都不能购买这个产品。

### 状态
- 正常：表示正常售卖的产品
- 下架：表示产品下架，用户端不能看到这个产品，也不能购买这个产品

### 出售状态
- 待售：表示产品不能购买
- 在售：表示产品可以购买
- 售完：表示产品已售完

### 免费项目
表示这个项目是否是免费的，如果这个开启表示用户不用资金就可以购买这个产品，从而享受购买产品后的收益。

### 到期退本
表示用户购买产品后的时间大于等于投资周期则返还购买产品时候的投资金额。

## 自定义收益设置

### 启用自定义收益率
开启后，系统将使用自定义结算时间和自定义收益率，而不是标准收益设置。自定义收益率仅在每周收益日内生效。

### 自定义结算时间(小时)
当启用自定义收益率时，使用此字段设置收益结算时间。

### 自定义收益率(%)
当启用自定义收益率时，使用此字段设置收益率。

## 数量设置

### 项目数量
表示这个产品有多少份，0表示不限制数量。

### 实际数量
表示这个产品还剩多少份，0表示不限制数量。

### 已售数量
表示这个产品已经卖出多少份，由系统自动计算。

### 最多购买次数
表示同一用户最多能购买多少次，0表示不限制次数。

### 同时购买数量
表示同一用户在本次购买中最多能购买多少数量，0表示不限制数量。

## 返佣设置

### 返佣开关
开启后，用户购买此产品将产生返佣。

### 一级返佣(%)
一级邀请人获得的返佣比例。

### 二级返佣(%)
二级邀请人获得的返佣比例。

### 三级返佣(%)
三级邀请人获得的返佣比例。

## 媒体资源

### 项目描述
项目的详细描述，支持富文本编辑。

### 项目图片
项目的展示图片，可以从附件中获取已经上传的图片，也可以选择本地的图片。

### 项目视频
项目的展示视频，可以从附件中获取已经上传的视频，也可以选择本地的视频。
