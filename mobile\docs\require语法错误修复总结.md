# require语法错误修复总结

## 📋 **问题描述**

在H5环境中出现了`ReferenceError: require is not defined`错误：

```
index.vue:496  ReferenceError: require is not defined
    at Proxy.formatCurrency (index.vue:433:29)
    at index.vue:61:40
```

## 🔍 **问题分析**

### **错误原因**
在首页(`mobile/pages/home/<USER>

```javascript
// ❌ 错误的写法 - H5环境不支持require
formatCurrency(value) {
  const { formatPHP } = require('../../utils/currency.js');
  return formatPHP(value);
}
```

### **环境差异**
- **Node.js环境**: 支持`require`语法
- **H5/浏览器环境**: 不支持`require`，只支持ES6的`import/export`
- **uni-app H5编译**: 将代码编译为浏览器可运行的代码，不支持Node.js特有的语法

## 🔧 **修复方案**

### **1. 添加ES6导入**
在文件顶部添加ES6导入语句：

```javascript
// 修复前
import AnnouncementPopup from '../../components/announcement-popup.vue';
import { getPopupNotice, getPopupButtonText, getPopupButtonLink } from '../../services/api/systemParam.js';

// 修复后
import AnnouncementPopup from '../../components/announcement-popup.vue';
import { getPopupNotice, getPopupButtonText, getPopupButtonLink } from '../../services/api/systemParam.js';
import { formatPHP } from '../../utils/currency.js';
```

### **2. 修改方法实现**
简化`formatCurrency`方法，直接使用导入的函数：

```javascript
// 修复前
formatCurrency(value) {
  // 导入全局格式化函数
  const { formatPHP } = require('../../utils/currency.js');
  return formatPHP(value);
}

// 修复后
formatCurrency(value) {
  return formatPHP(value);
}
```

## ✅ **修复效果**

### **错误消除**
- ✅ 消除了`ReferenceError: require is not defined`错误
- ✅ 页面可以正常渲染和显示
- ✅ 货币格式化功能正常工作

### **代码优化**
- ✅ 使用标准的ES6模块语法
- ✅ 提高了代码的可读性和维护性
- ✅ 符合uni-app的开发规范

## 🎯 **技术要点**

### **1. ES6 vs CommonJS**
```javascript
// CommonJS (Node.js) - ❌ H5环境不支持
const { formatPHP } = require('../../utils/currency.js');

// ES6 Modules - ✅ H5环境支持
import { formatPHP } from '../../utils/currency.js';
```

### **2. 导入时机**
- **顶部导入**: 在文件开头统一导入，编译时处理
- **动态导入**: 在方法中使用`import()`进行异步导入
- **require导入**: 仅在Node.js环境中可用

### **3. uni-app环境兼容性**
| 语法 | H5 | 小程序 | App | Node.js |
|------|----|----|-----|---------|
| `import/export` | ✅ | ✅ | ✅ | ✅ |
| `require/module.exports` | ❌ | ❌ | ❌ | ✅ |
| `import()` | ✅ | ✅ | ✅ | ✅ |

## 📚 **相关知识**

### **1. uni-app编译机制**
- uni-app将代码编译为不同平台的代码
- H5平台编译为浏览器可运行的JavaScript
- 不支持Node.js特有的API和语法

### **2. 模块系统选择**
在uni-app项目中应该：
- ✅ 优先使用ES6模块语法
- ✅ 保持跨平台兼容性
- ❌ 避免使用平台特定的语法

### **3. 错误排查方法**
1. **查看控制台错误**: 明确错误类型和位置
2. **检查环境差异**: 确认代码在目标环境的兼容性
3. **使用标准语法**: 选择跨平台兼容的语法

## 📝 **相关文件**

修改的文件：
- `mobile/pages/home/<USER>

依赖的文件：
- `mobile/utils/currency.js` - 货币格式化工具

## 🔄 **预防措施**

### **1. 开发规范**
- 在uni-app项目中统一使用ES6模块语法
- 避免在方法中动态require模块
- 在文件顶部统一管理导入

### **2. 代码检查**
- 使用ESLint检查模块语法
- 在不同环境中测试代码
- 定期检查跨平台兼容性

### **3. 最佳实践**
```javascript
// ✅ 推荐的写法
import { formatPHP, formatAmount } from '../../utils/currency.js';

export default {
  methods: {
    formatCurrency(value) {
      return formatPHP(value);
    }
  }
}
```

这次修复不仅解决了当前的错误，还提高了代码的规范性和可维护性，确保了在所有uni-app支持的平台上都能正常运行。
