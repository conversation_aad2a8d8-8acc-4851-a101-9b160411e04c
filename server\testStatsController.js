/**
 * 测试statsController.js中的getAdminDashboardStats函数
 */
const statsController = require('./controllers/statsController');

// 模拟请求和响应对象
const req = {};
const res = {
  status: function(statusCode) {
    console.log(`状态码: ${statusCode}`);
    return this;
  },
  json: function(data) {
    console.log('返回数据:');
    console.log(JSON.stringify(data, null, 2));
    return this;
  }
};

async function testStatsController() {
  try {
    console.log('开始测试statsController.js中的getAdminDashboardStats函数...');
    
    // 调用getAdminDashboardStats函数
    await statsController.getAdminDashboardStats(req, res);
    
    console.log('测试完成！');
    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testStatsController();
