/**
 * 移动端系统参数路由
 */
const express = require('express');
const router = express.Router();
const systemParamController = require('../controllers/systemParamController');
const { verifyUserToken } = require('../middlewares/authMiddleware');
const { SystemParam } = require('../models');

// 移动端系统参数路由不需要验证token，因为一些公共信息需要在未登录状态下访问
// router.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/system-params/key/{key}:
 *   get:
 *     summary: 获取系统参数详情（移动端）
 *     tags: [移动端]
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数键
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 参数不存在
 */
router.get('/key/:key', systemParamController.getSystemParamByKey);

/**
 * @swagger
 * /api/mobile/system-params/group/{group}:
 *   get:
 *     summary: 获取指定分组的系统参数（移动端）
 *     tags: [移动端]
 *     parameters:
 *       - in: path
 *         name: group
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数分组名称
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/group/:group', systemParamController.getSystemParamsByGroup);

/**
 * @swagger
 * /api/mobile/system-params/team-rules:
 *   get:
 *     summary: 获取团队规则（移动端）
 *     tags: [移动端]
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/team-rules', async (req, res) => {
  try {
    // 查询团队规则参数
    const param = await SystemParam.findOne({
      where: {
        param_key: '[site.team_rules]'
      }
    });

    // 如果找不到参数，返回空数组
    if (!param || !param.param_value) {
      return res.status(200).json({
        code: 200,
        message: 'Success',
        data: []
      });
    }

    // 直接返回原始内容，不进行格式化处理
    // 将HTML内容作为一个整体返回
    const htmlContent = param.param_value;

    // 如果内容是HTML格式，直接返回
    return res.status(200).json({
      code: 200,
      message: 'Success',
      data: [{ content: htmlContent }]
    });
  } catch (error) {
    console.error('Failed to get team rules:', error);
    return res.status(500).json({
      code: 500,
      message: 'Server error',
      data: null
    });
  }
});

module.exports = router;
