{"version": 3, "sources": ["../../src/associations/helpers.js"], "sourcesContent": ["'use strict';\n\nfunction checkNamingCollision(association) {\n  if (Object.prototype.hasOwnProperty.call(association.source.rawAttributes, association.as)) {\n    throw new Error(\n      `Naming collision between attribute '${association.as}'` +\n      ` and association '${association.as}' on model ${association.source.name}` +\n      '. To remedy this, change either foreignKey or as in your association definition'\n    );\n  }\n}\nexports.checkNamingCollision = checkNamingCollision;\n\nfunction addForeignKeyConstraints(newAttribute, source, target, options, key) {\n  // FK constraints are opt-in: users must either set `foreignKeyConstraints`\n  // on the association, or request an `onDelete` or `onUpdate` behavior\n\n  if (options.foreignKeyConstraint || options.onDelete || options.onUpdate) {\n    // Find primary keys: composite keys not supported with this approach\n    const primaryKeys = Object.keys(source.primaryKeys)\n      .map(primaryKeyAttribute => source.rawAttributes[primaryKeyAttribute].field || primaryKeyAttribute);\n\n    if (primaryKeys.length === 1 || !primaryKeys.includes(key)) {\n      newAttribute.references = {\n        model: source.getTableName(),\n        key: key || primaryKeys[0]\n      };\n\n      newAttribute.onDelete = options.onDelete;\n      newAttribute.onUpdate = options.onUpdate;\n    }\n  }\n}\nexports.addForeignKeyConstraints = addForeignKeyConstraints;\n\n/**\n * Mixin (inject) association methods to model prototype\n *\n * @private\n *\n * @param {object} association instance\n * @param {object} obj Model prototype\n * @param {Array} methods Method names to inject\n * @param {object} aliases Mapping between model and association method names\n *\n */\nfunction mixinMethods(association, obj, methods, aliases) {\n  aliases = aliases || {};\n\n  for (const method of methods) {\n    // don't override custom methods\n    if (!Object.prototype.hasOwnProperty.call(obj, association.accessors[method])) {\n      const realMethod = aliases[method] || method;\n\n      obj[association.accessors[method]] = function() {\n        return association[realMethod](this, ...Array.from(arguments));\n      };\n    }\n  }\n}\nexports.mixinMethods = mixinMethods;\n"], "mappings": ";AAEA,8BAA8B,aAAa;AACzC,MAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO,eAAe,YAAY,KAAK;AAC1F,UAAM,IAAI,MACR,uCAAuC,YAAY,wBAC9B,YAAY,gBAAgB,YAAY,OAAO;AAAA;AAAA;AAK1E,QAAQ,uBAAuB;AAE/B,kCAAkC,cAAc,QAAQ,QAAQ,SAAS,KAAK;AAI5E,MAAI,QAAQ,wBAAwB,QAAQ,YAAY,QAAQ,UAAU;AAExE,UAAM,cAAc,OAAO,KAAK,OAAO,aACpC,IAAI,yBAAuB,OAAO,cAAc,qBAAqB,SAAS;AAEjF,QAAI,YAAY,WAAW,KAAK,CAAC,YAAY,SAAS,MAAM;AAC1D,mBAAa,aAAa;AAAA,QACxB,OAAO,OAAO;AAAA,QACd,KAAK,OAAO,YAAY;AAAA;AAG1B,mBAAa,WAAW,QAAQ;AAChC,mBAAa,WAAW,QAAQ;AAAA;AAAA;AAAA;AAItC,QAAQ,2BAA2B;AAanC,sBAAsB,aAAa,KAAK,SAAS,SAAS;AACxD,YAAU,WAAW;AAErB,aAAW,UAAU,SAAS;AAE5B,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,YAAY,UAAU,UAAU;AAC7E,YAAM,aAAa,QAAQ,WAAW;AAEtC,UAAI,YAAY,UAAU,WAAW,WAAW;AAC9C,eAAO,YAAY,YAAY,MAAM,GAAG,MAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAK3D,QAAQ,eAAe;", "names": []}