const express = require('express');
const router = express.Router();
const bankController = require('../../controllers/bankController');
const authMiddleware = require('../../middlewares/authMiddleware');

// 所有路由都需要管理员认证
router.use(authMiddleware.verifyAdminToken);

// 获取所有银行
router.get('/banks', bankController.getAllBanks);

// 获取单个银行
router.get('/banks/:id', bankController.getBank);

// 创建银行
router.post('/banks', bankController.createBank);

// 更新银行
router.put('/banks/:id', bankController.updateBank);

// 更新银行状态
router.put('/banks/:id/status', bankController.updateBankStatus);

// 删除银行
router.delete('/banks/:id', bankController.deleteBank);

module.exports = router;
