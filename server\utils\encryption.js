const crypto = require('crypto');

// 从环境变量获取加密密钥，如果不存在则生成一个
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 
                       crypto.randomBytes(32).toString('hex');

// 从环境变量获取加密算法，默认为aes-256-gcm
const ALGORITHM = process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm';

/**
 * 加密数据
 * @param {string} text 要加密的文本
 * @returns {Object} 包含加密内容、认证标签和初始化向量的对象
 */
function encrypt(text) {
  if (!text) return null;
  
  try {
    // 生成随机初始化向量
    const iv = crypto.randomBytes(16);
    
    // 创建加密器
    const cipher = crypto.createCipheriv(
      ALGORITHM, 
      Buffer.from(ENCRYPTION_KEY, 'hex'), 
      iv
    );
    
    // 加密数据
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // 获取认证标签（仅GCM模式需要）
    const authTag = cipher.getAuthTag();
    
    return {
      content: encrypted,
      tag: authTag.toString('hex'),
      iv: iv.toString('hex')
    };
  } catch (error) {
    console.error('加密错误:', error);
    return null;
  }
}

/**
 * 解密数据
 * @param {Object} encrypted 包含加密内容、认证标签和初始化向量的对象
 * @returns {string|null} 解密后的文本，如果解密失败则返回null
 */
function decrypt(encrypted) {
  if (!encrypted || !encrypted.content || !encrypted.iv) return null;
  
  try {
    // 创建解密器
    const decipher = crypto.createDecipheriv(
      ALGORITHM,
      Buffer.from(ENCRYPTION_KEY, 'hex'),
      Buffer.from(encrypted.iv, 'hex')
    );
    
    // 设置认证标签（仅GCM模式需要）
    if (encrypted.tag) {
      decipher.setAuthTag(Buffer.from(encrypted.tag, 'hex'));
    }
    
    // 解密数据
    let decrypted = decipher.update(encrypted.content, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('解密错误:', error);
    return null;
  }
}

/**
 * 加密对象中的敏感字段
 * @param {Object} obj 要处理的对象
 * @param {Array<string>} sensitiveFields 敏感字段名数组
 * @returns {Object} 处理后的对象
 */
function encryptSensitiveData(obj, sensitiveFields) {
  if (!obj || typeof obj !== 'object') return obj;
  
  const result = { ...obj };
  
  for (const field of sensitiveFields) {
    if (result[field] && typeof result[field] === 'string') {
      const encrypted = encrypt(result[field]);
      if (encrypted) {
        result[field] = JSON.stringify(encrypted);
      }
    }
  }
  
  return result;
}

/**
 * 解密对象中的敏感字段
 * @param {Object} obj 要处理的对象
 * @param {Array<string>} sensitiveFields 敏感字段名数组
 * @returns {Object} 处理后的对象
 */
function decryptSensitiveData(obj, sensitiveFields) {
  if (!obj || typeof obj !== 'object') return obj;
  
  const result = { ...obj };
  
  for (const field of sensitiveFields) {
    if (result[field] && typeof result[field] === 'string') {
      try {
        const encrypted = JSON.parse(result[field]);
        const decrypted = decrypt(encrypted);
        if (decrypted !== null) {
          result[field] = decrypted;
        }
      } catch (error) {
        // 如果不是有效的JSON，说明字段可能未加密
        console.warn(`字段 ${field} 解析失败，可能未加密`);
      }
    }
  }
  
  return result;
}

module.exports = {
  encrypt,
  decrypt,
  encryptSensitiveData,
  decryptSensitiveData
};
