/**
 * Toast消息混入
 * 为所有页面提供统一的消息提示方法
 */
import toast from '@/utils/toast.js';

export default {
  data() {
    return {
      // Toast组件引用
      toastRef: null
    }
  },
  
  onReady() {
    // 页面准备完毕后，设置Toast组件引用
    this.$nextTick(() => {
      if (this.$refs.toastMessage) {
        toast.setToastComponent(this.$refs.toastMessage);
        this.toastRef = this.$refs.toastMessage;
      }
    });
  },
  
  methods: {
    // 成功消息
    $showSuccess(message, title = 'Success', duration = 4000) {
      toast.success(message, title, duration);
    },
    
    // 错误消息
    $showError(message, title = 'Error', duration = 5000) {
      toast.error(message, title, duration);
    },
    
    // 警告消息
    $showWarning(message, title = 'Warning', duration = 4500) {
      toast.warning(message, title, duration);
    },
    
    // 信息消息
    $showInfo(message, title = 'Info', duration = 4000) {
      toast.info(message, title, duration);
    },
    
    // 余额不足消息
    $showInsufficientBalance(message, title = 'Insufficient Balance', duration = 5000) {
      toast.insufficientBalance(message, title, duration);
    },
    
    // 购买成功消息
    $showPurchaseSuccess(productName, amount) {
      toast.purchaseSuccess(productName, amount);
    },
    
    // 充值成功消息
    $showTopUpSuccess(amount) {
      toast.topUpSuccess(amount);
    },
    
    // 网络错误消息
    $showNetworkError(message) {
      toast.networkError(message);
    },
    
    // 服务器错误消息
    $showServerError(message) {
      toast.serverError(message);
    },
    
    // 验证错误消息
    $showValidationError(message) {
      toast.validationError(message);
    },
    
    // 加载中消息
    $showLoading(message = 'Loading...', title = 'Please Wait') {
      toast.loading(message, title);
    },
    
    // 隐藏Toast
    $hideToast() {
      toast.hide();
    },
    
    // 通用Toast方法
    $toast(options) {
      toast.show(options);
    }
  }
};
