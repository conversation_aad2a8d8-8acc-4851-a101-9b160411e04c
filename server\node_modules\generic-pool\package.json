{"name": "generic-pool", "description": "Generic resource pooling for Node.JS", "homepage": "https://github.com/coopernurse/node-pool#readme", "version": "3.9.0", "main": "index.js", "author": {"email": "<EMAIL>", "name": "<PERSON>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "C-h-e-r-r-y", "email": "<EMAIL>"}, {"name": "rebareba", "email": "<EMAIL>"}, {"name": "t3hmrman", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "san00", "email": "<EMAIL>"}, {"name": "<PERSON>He<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jro<PERSON><PERSON>@redventures.com"}, {"name": "lin<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@outlook.com"}, {"name": "travis4all", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "windyr<PERSON>in", "email": "<EMAIL>"}, {"name": "王秋石", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Arek Flinik", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<PERSON><PERSON>@gmail.com"}, {"name": "calibr", "email": "<EMAIL>"}, {"name": "ben<PERSON>-med<PERSON>t", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "gdusbabek", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "molipet", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/coopernurse/node-pool.git"}, "bugs": {"url": "https://github.com/coopernurse/node-pool/issues"}, "keywords": ["pool", "pooling", "throttle"], "devDependencies": {"@types/node": "^8.5.1", "eslint": "^4.9.0", "eslint-config-prettier": "^2.6.0", "eslint-plugin-prettier": "^2.3.1", "eslint-plugin-promise": "^3.3.0", "prettier": "^1.7.4", "tap": "^8.0.0", "typescript": "^2.6.2"}, "engines": {"node": ">= 4"}, "files": ["index.d.ts", "index.js", "lib"], "license": "MIT", "scripts": {"lint": "eslint lib test index.js .eslintrc.js", "lint-fix": "eslint --fix lib test index.js .eslintrc.js", "test": "tap test/*-test.js "}}