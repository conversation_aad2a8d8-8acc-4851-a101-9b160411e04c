开发环境说明
(1).支付下单请求是 HTTPS 协议请求，商户以 POST 方式发送到系统。
(2).平台统一使用 UTF-8 编码方式。
(3).参数名称和参数说明中规定的固定值必须与列表中完全一致（大小写敏感）。

加密规则
1.将所有需要签名的字段按照 ASCII 码从小到大进行排序，并按照按照 k=v&k=v 的格式拼接字符串，并在字符串后面拼接商户私钥用 &key=x 进行拼接，生成待签名 queryString 字符串。
2.对生成的 queryString 字符串进行 MD5 签名，得到小写签名串。
3.除了sign和sign_type以外不为空的参数都需要参与签名(注意：回调以及同步响应的签名方法参数名称为signType)
加密示例

加密前串排序如下：
goods_name=test&mch_id=977977001&mch_order_no=2021-04-13 17:32:28&notify_url=http://www.baidu.com/notify_url.jsp&order_date=2021-04-13 17:32:25&pay_type=122&trade_amount=100&key=520261072ab44e05a475697960941f69

加密结果：
2b3cf11db77544411a96445844c5c841