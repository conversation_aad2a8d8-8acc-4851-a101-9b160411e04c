{"version": 3, "file": "dialog-content2.mjs", "sources": ["../../../../../../packages/components/dialog/src/dialog-content.vue"], "sourcesContent": ["<template>\n  <div :ref=\"composedDialogRef\" :class=\"dialogKls\" :style=\"style\" tabindex=\"-1\">\n    <header\n      ref=\"headerRef\"\n      :class=\"[ns.e('header'), headerClass, { 'show-close': showClose }]\"\n    >\n      <slot name=\"header\">\n        <span role=\"heading\" :aria-level=\"ariaLevel\" :class=\"ns.e('title')\">\n          {{ title }}\n        </span>\n      </slot>\n      <button\n        v-if=\"showClose\"\n        :aria-label=\"t('el.dialog.close')\"\n        :class=\"ns.e('headerbtn')\"\n        type=\"button\"\n        @click=\"$emit('close')\"\n      >\n        <el-icon :class=\"ns.e('close')\">\n          <component :is=\"closeIcon || Close\" />\n        </el-icon>\n      </button>\n    </header>\n    <div :id=\"bodyId\" :class=\"[ns.e('body'), bodyClass]\">\n      <slot />\n    </div>\n    <footer v-if=\"$slots.footer\" :class=\"[ns.e('footer'), footerClass]\">\n      <slot name=\"footer\" />\n    </footer>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { FOCUS_TRAP_INJECTION_KEY } from '@element-plus/components/focus-trap'\nimport { useDraggable, useLocale } from '@element-plus/hooks'\nimport { CloseComponents, composeRefs } from '@element-plus/utils'\nimport { dialogInjectionKey } from './constants'\nimport { dialogContentEmits, dialogContentProps } from './dialog-content'\n\nconst { t } = useLocale()\nconst { Close } = CloseComponents\n\ndefineOptions({ name: 'ElDialogContent' })\nconst props = defineProps(dialogContentProps)\ndefineEmits(dialogContentEmits)\n\nconst { dialogRef, headerRef, bodyId, ns, style } = inject(dialogInjectionKey)!\nconst { focusTrapRef } = inject(FOCUS_TRAP_INJECTION_KEY)!\n\nconst dialogKls = computed(() => [\n  ns.b(),\n  ns.is('fullscreen', props.fullscreen),\n  ns.is('draggable', props.draggable),\n  ns.is('align-center', props.alignCenter),\n  { [ns.m('center')]: props.center },\n])\n\nconst composedDialogRef = composeRefs(focusTrapRef, dialogRef)\n\nconst draggable = computed(() => props.draggable)\nconst overflow = computed(() => props.overflow)\nconst { resetPosition } = useDraggable(\n  dialogRef,\n  headerRef,\n  draggable,\n  overflow\n)\n\ndefineExpose({\n  resetPosition,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_unref", "_normalizeClass", "_normalizeStyle"], "mappings": ";;;;;;;;;;;mCA4Cc,CAAA,EAAE,IAAM,EAAA,iBAAA,EAAkB,CAAA,CAAA;;;;;;;AAHxC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAE,OAAU,GAAA,eAAA,CAAA;AAMlB,IAAM,MAAA,EAAE,WAAW,SAAW,EAAA,MAAA,EAAQ,IAAI,KAAM,EAAA,GAAI,OAAO,kBAAkB,CAAA,CAAA;AAC7E,IAAA,MAAM,EAAE,YAAA,EAAiB,GAAA,MAAA,CAAO,wBAAwB,CAAA,CAAA;AAExD,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAAA,MAC/B,GAAG,CAAE,EAAA;AAAA,MACL,EAAG,CAAA,EAAA,CAAG,YAAc,EAAA,KAAA,CAAM,UAAU,CAAA;AAAA,MACpC,EAAG,CAAA,EAAA,CAAG,WAAa,EAAA,KAAA,CAAM,SAAS,CAAA;AAAA,MAClC,EAAG,CAAA,EAAA,CAAG,cAAgB,EAAA,KAAA,CAAM,WAAW,CAAA;AAAA,MACvC,EAAE,CAAC,EAAG,CAAA,CAAA,CAAE,QAAQ,CAAC,GAAG,MAAM,MAAO,EAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,iBAAA,GAAoB,WAAY,CAAA,YAAA,EAAc,SAAS,CAAA,CAAA;AAE7D,IAAA,MAAM,SAAY,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,SAAS,CAAA,CAAA;AAChD,IAAA,MAAM,QAAW,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,QAAQ,CAAA,CAAA;AAC9C,IAAM,MAAA,EAAE,eAAkB,GAAA,YAAA,CAAA,SAAA,EAAA,SAAA,EAAA,SAAA,EAAA,QAAA,CAAA,CAAA;AAAA,IACxB,MAAA,CAAA;AAAA,MACA,aAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACF,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAEA,QAAa,GAAA,EAAAC,KAAA,CAAA,iBAAA,CAAA;AAAA,QACX,KAAA,EAAAC,cAAA,CAAAD,KAAA,CAAA,SAAA,CAAA,CAAA;AAAA,QACD,KAAA,EAAAE,cAAA,CAAAF,KAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}