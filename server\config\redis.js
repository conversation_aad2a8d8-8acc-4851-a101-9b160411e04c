/**
 * Redis配置
 */
const dotenv = require('dotenv');
dotenv.config();

// Redis配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || '',
  db: process.env.REDIS_DB || 0,
  // 连接超时时间（毫秒）
  connectTimeout: 10000,
  // 重试策略
  retryStrategy: function(times) {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
  // 最大重试次数
  maxRetriesPerRequest: 3
};

module.exports = redisConfig;
