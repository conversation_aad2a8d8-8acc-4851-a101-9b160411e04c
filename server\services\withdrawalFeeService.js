/**
 * 取款手续费计算服务
 * 根据系统配置动态计算取款手续费
 */
const { SystemParam } = require('../models');

/**
 * 获取取款手续费配置
 * @returns {Promise<Object>} 手续费配置
 */
async function getWithdrawalFeeConfig() {
  try {
    console.log('=== 获取手续费配置开始 ===');

    // 获取手续费类型
    const feeTypeParam = await SystemParam.findOne({
      where: { param_key: '[site.withdraw_fee_type]' }
    });
    console.log('手续费类型参数:', feeTypeParam?.param_value);

    // 获取手续费配置
    const feeConfigParam = await SystemParam.findOne({
      where: { param_key: '[site.withdraw_fee_step]' }
    });
    console.log('手续费配置参数:', feeConfigParam?.param_value);

    const feeType = feeTypeParam?.param_value || '固定';
    let feeConfig = null;

    if (feeConfigParam?.param_value) {
      try {
        feeConfig = JSON.parse(feeConfigParam.param_value);
        console.log('解析后的手续费配置:', feeConfig);
      } catch (e) {
        console.error('解析手续费配置失败:', e);
        console.error('原始配置数据:', feeConfigParam.param_value);
        feeConfig = null;
      }
    } else {
      console.log('未找到手续费配置参数，使用默认配置');
    }

    const result = {
      type: feeType,
      config: feeConfig
    };

    console.log('=== 最终手续费配置 ===', result);
    return result;
  } catch (error) {
    console.error('获取取款手续费配置失败:', error);
    console.error('错误堆栈:', error.stack);
    return {
      type: '固定',
      config: {
        type: 'fixed',
        amount: '0'
      }
    };
  }
}

/**
 * 计算取款手续费
 * @param {number} amount - 取款金额
 * @returns {Promise<Object>} 计算结果
 */
async function calculateWithdrawalFee(amount) {
  try {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      throw new Error('取款金额必须大于0');
    }

    const { type, config } = await getWithdrawalFeeConfig();

    console.log('=== 手续费计算开始 ===');
    console.log('取款金额:', numAmount);
    console.log('手续费类型:', type);
    console.log('手续费配置:', config);

    let feeAmount = 0;

    if (type === '固定') {
      // 固定手续费
      if (config && config.type === 'fixed') {
        feeAmount = parseFloat(config.amount) || 0;
      } else {
        // 如果没有配置，默认为0
        feeAmount = 0;
        console.log('警告：未找到固定手续费配置，使用默认值0');
      }
      console.log('固定手续费:', feeAmount);
    } else if (type === '浮动') {
      // 浮动手续费
      if (config && config.type === 'dynamic' && config.data && Array.isArray(config.data)) {
        feeAmount = calculateDynamicFee(numAmount, config.data);
      } else {
        // 如果没有配置，默认为0
        feeAmount = 0;
        console.log('警告：未找到浮动手续费配置，使用默认值0');
      }
      console.log('浮动手续费:', feeAmount);
    } else {
      // 未知类型，默认为0
      feeAmount = 0;
      console.log('警告：未知手续费类型，使用默认值0');
    }

    // 确保手续费不为负数
    feeAmount = Math.max(0, feeAmount);

    // 计算实际到账金额
    const actualAmount = numAmount - feeAmount;

    // 确保实际到账金额不为负数
    if (actualAmount < 0) {
      throw new Error(`手续费${feeAmount}超过了取款金额${numAmount}，请检查手续费配置`);
    }

    // 计算发送给第三方的金额（向下取整）
    const paymentAmount = Math.floor(actualAmount);

    const result = {
      withdrawAmount: numAmount,        // 用户申请取款金额
      feeAmount: feeAmount,            // 手续费金额（保留小数）
      actualAmount: actualAmount,      // 用户实际到账金额（保留小数）
      paymentAmount: paymentAmount     // 发送给第三方的金额（整数）
    };

    console.log('=== 手续费计算结果 ===');
    console.log('申请金额:', result.withdrawAmount);
    console.log('手续费:', result.feeAmount);
    console.log('实际到账:', result.actualAmount);
    console.log('支付金额:', result.paymentAmount);

    return result;
  } catch (error) {
    console.error('计算取款手续费失败:', error);
    throw error;
  }
}

/**
 * 计算浮动手续费
 * @param {number} amount - 取款金额
 * @param {Array} feeRules - 手续费规则数组
 * @returns {number} 手续费金额
 */
function calculateDynamicFee(amount, feeRules) {
  console.log('=== 浮动手续费计算 ===');
  console.log('金额:', amount);
  console.log('规则:', feeRules);

  // 查找匹配的规则
  for (const rule of feeRules) {
    // 安全地解析数值，避免NaN
    const minAmount = parseFloat(rule.minAmount);
    const maxAmount = parseFloat(rule.maxAmount);

    // 跳过无效的规则
    if (isNaN(minAmount) || isNaN(maxAmount)) {
      console.log('跳过无效规则:', rule);
      continue;
    }

    console.log(`检查规则: ${minAmount} - ${maxAmount}`);

    // 检查金额是否在范围内
    if (amount >= minAmount && amount <= maxAmount) {
      console.log('匹配规则:', rule);

      const feeType = rule.feeType;
      const feeValue = parseFloat(rule.feeValue);

      // 跳过无效的手续费值
      if (isNaN(feeValue) || feeValue < 0) {
        console.log('跳过无效手续费值:', feeValue);
        continue;
      }

      let feeAmount = 0;

      if (feeType === 'percentage') {
        // 百分比手续费
        feeAmount = amount * (feeValue / 100);
        console.log(`百分比计算: ${amount} * ${feeValue}% = ${feeAmount}`);
      } else if (feeType === 'fixed') {
        // 固定金额手续费
        feeAmount = feeValue;
        console.log(`固定金额: ${feeAmount}`);
      } else {
        console.log('未知手续费类型:', feeType);
        continue;
      }

      return Math.max(0, feeAmount); // 确保返回非负数
    }
  }

  console.log('未找到匹配规则，手续费为0');
  return 0;
}

/**
 * 验证取款手续费配置
 * @param {Object} config - 手续费配置
 * @returns {Object} 验证结果
 */
function validateFeeConfig(config) {
  const errors = [];

  if (!config) {
    errors.push('手续费配置不能为空');
    return { valid: false, errors };
  }

  if (config.type === 'fixed') {
    const amount = parseFloat(config.amount);
    if (isNaN(amount) || amount < 0) {
      errors.push('固定手续费金额必须是非负数');
    }
  } else if (config.type === 'dynamic') {
    if (!Array.isArray(config.data) || config.data.length === 0) {
      errors.push('浮动手续费必须配置至少一个规则');
    } else {
      config.data.forEach((rule, index) => {
        const minAmount = parseFloat(rule.minAmount);
        const maxAmount = parseFloat(rule.maxAmount);
        const feeValue = parseFloat(rule.feeValue);

        if (isNaN(minAmount) || minAmount < 0) {
          errors.push(`规则${index + 1}: 最小金额必须是非负数`);
        }

        if (isNaN(maxAmount) || maxAmount < 0) {
          errors.push(`规则${index + 1}: 最大金额必须是非负数`);
        }

        if (minAmount >= maxAmount) {
          errors.push(`规则${index + 1}: 最小金额必须小于最大金额`);
        }

        if (isNaN(feeValue) || feeValue < 0) {
          errors.push(`规则${index + 1}: 手续费值必须是非负数`);
        }

        if (!['percentage', 'fixed'].includes(rule.feeType)) {
          errors.push(`规则${index + 1}: 手续费类型必须是percentage或fixed`);
        }
      });
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

module.exports = {
  getWithdrawalFeeConfig,
  calculateWithdrawalFee,
  validateFeeConfig
};
