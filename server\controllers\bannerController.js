const { Banner, Attachment } = require('../models');
const { Op } = require('sequelize');

// 获取轮播图列表
exports.getBanners = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      position,
      search,
      sort_order_min,
      sort_order_max
    } = req.query;

    const offset = (page - 1) * limit;

    // 构建查询条件
    const where = {};

    // 状态筛选
    if (status !== undefined) {
      where.status = status === '1' || status === 1 || status === true;
    }

    // 位置筛选
    if (position) {
      where.position = position;
    }

    // 权重范围筛选
    if (sort_order_min || sort_order_max) {
      where.sort_order = {};
      if (sort_order_min) {
        where.sort_order[Op.gte] = parseInt(sort_order_min);
      }
      if (sort_order_max) {
        where.sort_order[Op.lte] = parseInt(sort_order_max);
      }
    }

    // 搜索条件
    if (search) {
      where[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { url: { [Op.like]: `%${search}%` } }
      ];
    }

    // 查询数据
    const { count, rows } = await Banner.findAndCountAll({
      where,
      order: [['sort_order', 'ASC'], ['id', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      code: 200,
      message: '获取轮播图列表成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取轮播图列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取轮播图列表失败',
      data: null
    });
  }
};

// 获取轮播图详情
exports.getBannerById = async (req, res) => {
  try {
    const { id } = req.params;

    const banner = await Banner.findByPk(id, {
      include: [
        {
          model: Attachment,
          as: 'attachment',
          attributes: ['id', 'file_path', 'filename']
        }
      ]
    });

    if (!banner) {
      return res.status(404).json({
        code: 404,
        message: '轮播图不存在',
        data: null
      });
    }

    res.json({
      code: 200,
      message: '获取轮播图详情成功',
      data: banner
    });
  } catch (error) {
    console.error('获取轮播图详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取轮播图详情失败',
      data: null
    });
  }
};

// 创建轮播图
exports.createBanner = async (req, res) => {
  try {
    const {
      title,
      attachment_id,
      position,
      sort_order = 0,
      status = true,
      start_time,
      end_time
    } = req.body;

    // 验证必填字段
    if (!attachment_id) {
      return res.status(400).json({
        code: 400,
        message: '图片不能为空',
        data: null
      });
    }

    // 获取附件信息，用于设置position
    let attachmentPosition = position;
    if (attachment_id && !position) {
      try {
        const attachment = await Attachment.findByPk(attachment_id);
        if (attachment) {
          attachmentPosition = attachment.file_path;
        }
      } catch (error) {
        console.error('获取附件信息失败:', error);
      }
    }

    // 创建轮播图
    const banner = await Banner.create({
      title,
      attachment_id,
      position: attachmentPosition,
      sort_order,
      status,
      start_time,
      end_time
    });

    res.status(201).json({
      code: 200,
      message: '创建轮播图成功',
      data: banner
    });
  } catch (error) {
    console.error('创建轮播图失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建轮播图失败',
      data: null
    });
  }
};

// 更新轮播图
exports.updateBanner = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      attachment_id,
      position,
      sort_order,
      status,
      start_time,
      end_time
    } = req.body;

    // 查找轮播图
    const banner = await Banner.findByPk(id);

    if (!banner) {
      return res.status(404).json({
        code: 404,
        message: '轮播图不存在',
        data: null
      });
    }

    // 获取附件信息，用于设置position
    let attachmentPosition = position;
    if (attachment_id && attachment_id !== banner.attachment_id) {
      try {
        const attachment = await Attachment.findByPk(attachment_id);
        if (attachment) {
          attachmentPosition = attachment.file_path;
        }
      } catch (error) {
        console.error('获取附件信息失败:', error);
      }
    }

    // 更新轮播图
    await banner.update({
      title,
      attachment_id,
      position: attachmentPosition,
      sort_order,
      status,
      start_time,
      end_time
    });

    res.json({
      code: 200,
      message: '更新轮播图成功',
      data: banner
    });
  } catch (error) {
    console.error('更新轮播图失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新轮播图失败',
      data: null
    });
  }
};

// 删除轮播图
exports.deleteBanner = async (req, res) => {
  try {
    const { id } = req.params;

    // 查找轮播图
    const banner = await Banner.findByPk(id);

    if (!banner) {
      return res.status(404).json({
        code: 404,
        message: '轮播图不存在',
        data: null
      });
    }

    // 删除轮播图
    await banner.destroy();

    res.json({
      code: 200,
      message: '删除轮播图成功',
      data: null
    });
  } catch (error) {
    console.error('删除轮播图失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除轮播图失败',
      data: null
    });
  }
};

// 批量删除轮播图
exports.batchDeleteBanners = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请选择要删除的轮播图',
        data: null
      });
    }

    // 批量删除轮播图
    await Banner.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    res.json({
      code: 200,
      message: '批量删除轮播图成功',
      data: null
    });
  } catch (error) {
    console.error('批量删除轮播图失败:', error);
    res.status(500).json({
      code: 500,
      message: '批量删除轮播图失败',
      data: null
    });
  }
};

// 更新轮播图排序
exports.updateBannerSortOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const { sort_order } = req.body;

    if (sort_order === undefined || sort_order === null) {
      return res.status(400).json({
        code: 400,
        message: '排序值不能为空',
        data: null
      });
    }

    // 查找轮播图
    const banner = await Banner.findByPk(id);

    if (!banner) {
      return res.status(404).json({
        code: 404,
        message: '轮播图不存在',
        data: null
      });
    }

    // 更新排序
    await banner.update({
      sort_order
    });

    res.json({
      code: 200,
      message: '更新轮播图排序成功',
      data: banner
    });
  } catch (error) {
    console.error('更新轮播图排序失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新轮播图排序失败',
      data: null
    });
  }
};
