/**
 * Token管理服务
 * 使用Redis实现token存储和自动续期功能
 */
const jwt = require('jsonwebtoken');
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const { isBlacklisted, addToBlacklist } = require('../utils/jwtBlacklist');

// Redis键前缀
const ADMIN_TOKEN_PREFIX = 'admin:token:';

// 从环境变量获取配置，如果不存在则使用默认值
const RELATIVE_EXPIRE_TIME = process.env.JWT_ADMIN_EXPIRE
  ? parseDuration(process.env.JWT_ADMIN_EXPIRE)
  : 60 * 60; // 默认1小时

// 绝对过期时间（秒）
const ABSOLUTE_EXPIRE_TIME = process.env.JWT_ADMIN_ABSOLUTE_EXPIRE
  ? parseDuration(process.env.JWT_ADMIN_ABSOLUTE_EXPIRE)
  : 8 * 60 * 60; // 默认8小时

// 刷新阈值（秒）
const REFRESH_THRESHOLD = process.env.JWT_REFRESH_THRESHOLD
  ? parseDuration(process.env.JWT_REFRESH_THRESHOLD)
  : 15 * 60; // 默认15分钟

/**
 * 解析持续时间字符串（如 '1h', '30m'）为秒数
 */
function parseDuration(durationStr) {
  const match = durationStr.match(/^(\d+)([smhdw])$/);
  if (!match) return 0;

  const value = parseInt(match[1]);
  const unit = match[2];

  switch (unit) {
    case 's': return value;
    case 'm': return value * 60;
    case 'h': return value * 60 * 60;
    case 'd': return value * 24 * 60 * 60;
    case 'w': return value * 7 * 24 * 60 * 60;
    default: return 0;
  }
}

/**
 * 生成管理员token
 * @param {Object} admin - 管理员对象
 * @returns {Promise<string>} - 返回生成的token
 */
exports.generateAdminToken = async (admin) => {
  try {
    // 生成JWT令牌
    const token = jwt.sign(
      {
        id: admin.id,
        type: 'admin',
        username: admin.username,
        is_super: admin.is_super,
        // 添加一些额外的安全字段
        iat: Math.floor(Date.now() / 1000), // 令牌签发时间
        jti: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15) // 唯一令牌标识符
      },
      process.env.JWT_SECRET || 'admin_secret_key_for_testing_purposes_only',
      {
        expiresIn: RELATIVE_EXPIRE_TIME, // 相对过期时间为1小时
        algorithm: 'HS512' // 使用更强的算法
      }
    );

    // 存储token信息到Redis
    const tokenKey = `${ADMIN_TOKEN_PREFIX}${admin.id}`;
    const now = Math.floor(Date.now() / 1000);

    const tokenInfo = {
      token,
      adminId: admin.id,
      username: admin.username,
      createdAt: now,
      lastActivityAt: now,
      relativeExpireAt: now + RELATIVE_EXPIRE_TIME,
      absoluteExpireAt: now + ABSOLUTE_EXPIRE_TIME
    };

    // 设置Redis过期时间为绝对过期时间
    await redisClient.set(tokenKey, tokenInfo, ABSOLUTE_EXPIRE_TIME);

    return token;
  } catch (error) {
    logger.error('生成管理员token错误:', error);
    throw error;
  }
};

/**
 * 验证管理员token
 * @param {string} token - JWT令牌
 * @returns {Promise<Object>} - 返回验证结果
 */
exports.verifyAdminToken = async (token) => {
  try {
    // 检查token是否在黑名单中
    const tokenBlacklisted = await isBlacklisted(token);
    if (tokenBlacklisted) {
      return { valid: false, message: '令牌已失效，请重新登录' };
    }

    // 验证JWT令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'admin_secret_key_for_testing_purposes_only', {
      algorithms: ['HS512', 'HS256'] // 支持新旧算法，确保兼容性
    });

    // 检查令牌类型
    if (decoded.type !== 'admin') {
      return { valid: false, message: '无效的令牌类型' };
    }

    // 获取Redis中的token信息
    const tokenKey = `${ADMIN_TOKEN_PREFIX}${decoded.id}`;
    const tokenInfo = await redisClient.get(tokenKey);

    // 如果Redis中没有token信息，说明token已过期或被删除
    if (!tokenInfo) {
      return { valid: false, message: '令牌已过期，请重新登录' };
    }

    // 检查是否是最新的token
    if (tokenInfo.token !== token) {
      return { valid: false, message: '令牌已被其他设备登录替换，请重新登录' };
    }

    // 检查是否超过绝对过期时间
    const now = Math.floor(Date.now() / 1000);
    if (now >= tokenInfo.absoluteExpireAt) {
      return { valid: false, message: '令牌已过期，请重新登录' };
    }

    // 检查是否超过相对过期时间
    if (now >= tokenInfo.relativeExpireAt) {
      // 检查最后活动时间
      const lastActivityDiff = now - tokenInfo.lastActivityAt;

      // 如果最后活动时间超过相对过期时间，则token过期
      if (lastActivityDiff >= RELATIVE_EXPIRE_TIME) {
        return { valid: false, message: '令牌已过期，请重新登录' };
      }
    }

    // 更新最后活动时间
    tokenInfo.lastActivityAt = now;
    await redisClient.set(tokenKey, tokenInfo, ABSOLUTE_EXPIRE_TIME - (now - tokenInfo.createdAt));

    return { valid: true, decoded, tokenInfo };
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return { valid: false, message: '令牌已过期' };
    }

    if (error.name === 'JsonWebTokenError') {
      return { valid: false, message: '无效的令牌' };
    }

    logger.error('验证管理员token错误:', error);
    return { valid: false, message: '验证令牌时发生错误' };
  }
};

/**
 * 刷新管理员token
 * @param {string} oldToken - 旧token
 * @returns {Promise<Object>} - 返回刷新结果
 */
exports.refreshAdminToken = async (oldToken) => {
  try {
    // 验证旧token
    const verifyResult = await exports.verifyAdminToken(oldToken);
    if (!verifyResult.valid) {
      return { success: false, message: verifyResult.message };
    }

    const { decoded, tokenInfo } = verifyResult;

    // 检查是否需要刷新
    const now = Math.floor(Date.now() / 1000);
    const timeRemaining = tokenInfo.relativeExpireAt - now;

    // 如果剩余时间大于刷新阈值，不需要刷新
    if (timeRemaining > REFRESH_THRESHOLD) {
      return { success: false, message: '令牌尚未达到刷新阈值', needRefresh: false };
    }

    // 生成新token
    const newToken = jwt.sign(
      {
        id: decoded.id,
        type: decoded.type,
        username: decoded.username,
        is_super: decoded.is_super,
        iat: now,
        jti: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
      },
      process.env.JWT_SECRET || 'admin_secret_key_for_testing_purposes_only',
      {
        expiresIn: RELATIVE_EXPIRE_TIME,
        algorithm: 'HS512'
      }
    );

    // 更新Redis中的token信息
    const tokenKey = `${ADMIN_TOKEN_PREFIX}${decoded.id}`;
    const newTokenInfo = {
      ...tokenInfo,
      token: newToken,
      lastActivityAt: now,
      relativeExpireAt: now + RELATIVE_EXPIRE_TIME
    };

    // 计算剩余的绝对过期时间
    const remainingAbsoluteTime = tokenInfo.absoluteExpireAt - now;
    await redisClient.set(tokenKey, newTokenInfo, remainingAbsoluteTime);

    return { success: true, token: newToken, message: '令牌刷新成功' };
  } catch (error) {
    logger.error('刷新管理员token错误:', error);
    return { success: false, message: '刷新令牌时发生错误' };
  }
};

/**
 * 使管理员token失效
 * @param {string} token - JWT令牌
 * @returns {Promise<boolean>} - 返回是否成功
 */
exports.invalidateAdminToken = async (token) => {
  try {
    // 解码token（不验证）
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.id) {
      return false;
    }

    // 将token加入黑名单
    await addToBlacklist(token);

    // 删除Redis中的token信息
    const tokenKey = `${ADMIN_TOKEN_PREFIX}${decoded.id}`;
    await redisClient.del(tokenKey);

    return true;
  } catch (error) {
    logger.error('使管理员token失效错误:', error);
    return false;
  }
};

/**
 * 刷新用户token
 * @param {string} oldToken - 旧token
 * @returns {Promise<Object>} - 返回刷新结果
 */
exports.refreshUserToken = async (oldToken) => {
  try {
    const jwt = require('jsonwebtoken');

    // 验证旧token
    const decoded = jwt.verify(oldToken, process.env.JWT_SECRET, {
      algorithms: [process.env.JWT_ALGORITHM || 'HS512', 'HS256']
    });

    // 检查token类型
    if (decoded.type !== 'user') {
      return { success: false, message: '无效的令牌类型' };
    }

    // 检查是否需要刷新（距离过期少于1天时刷新）
    const now = Math.floor(Date.now() / 1000);
    const timeRemaining = decoded.exp - now;
    const refreshThreshold = 24 * 60 * 60; // 1天

    // 如果剩余时间大于刷新阈值，不需要刷新
    if (timeRemaining > refreshThreshold) {
      return { success: false, message: '令牌尚未达到刷新阈值', needRefresh: false };
    }

    // 生成新token
    const newToken = jwt.sign(
      {
        id: decoded.id,
        type: decoded.type,
        username: decoded.username,
        iat: now,
        jti: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
      },
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_USER_EXPIRE || '7d',
        algorithm: process.env.JWT_ALGORITHM || 'HS512'
      }
    );

    return { success: true, token: newToken, message: '令牌刷新成功' };
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return { success: false, message: '令牌已过期' };
    }

    if (error.name === 'JsonWebTokenError') {
      return { success: false, message: '无效的令牌' };
    }

    console.error('刷新用户token错误:', error);
    return { success: false, message: '刷新令牌时发生错误' };
  }
};
