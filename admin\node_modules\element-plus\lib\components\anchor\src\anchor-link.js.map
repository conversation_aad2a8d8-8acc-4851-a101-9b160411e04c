{"version": 3, "file": "anchor-link.js", "sources": ["../../../../../../packages/components/anchor/src/anchor-link.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const anchorLinkProps = buildProps({\n  /**\n   * @description the text content of the anchor link\n   */\n  title: String,\n  /**\n   * @description The address of the anchor link\n   */\n  href: String,\n})\n\nexport type AnchorLinkProps = ExtractPropTypes<typeof anchorLinkProps>\n"], "names": ["buildProps"], "mappings": ";;;;;;AACY,MAAC,eAAe,GAAGA,kBAAU,CAAC;AAC1C,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,IAAI,EAAE,MAAM;AACd,CAAC;;;;"}