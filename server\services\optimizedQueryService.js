/**
 * 优化的查询服务
 * 用于执行优化的数据库查询
 */
const sequelize = require('../config/database');
const { QueryTypes } = require('sequelize');
const cacheUtils = require('../utils/cacheUtils');

// 缓存键前缀
const CACHE_KEY_PREFIX = 'query:';

// 缓存过期时间（毫秒）
const CACHE_TTL = 5 * 60 * 1000; // 5分钟

/**
 * 执行优化的用户交易查询
 * @param {number} limit - 限制结果数量
 * @returns {Promise<Array>} - 查询结果
 */
async function getUsersWithTransactions(limit = 10) {
  const cacheKey = `${CACHE_KEY_PREFIX}users_with_transactions:${limit}`;
  
  // 使用缓存工具获取或设置缓存
  return await cacheUtils.getOrSet(cacheKey, async () => {
    // 使用优化的SQL查询
    const query = `
      SELECT 
        u.id, 
        u.username, 
        u.user_id,
        u.email, 
        u.mobile, 
        u.status,
        COUNT(t.id) as transaction_count,
        SUM(CASE WHEN t.type = 'deposit' THEN t.amount ELSE 0 END) as total_deposit,
        SUM(CASE WHEN t.type = 'withdrawal' THEN t.amount ELSE 0 END) as total_withdrawal
      FROM 
        users u
      LEFT JOIN 
        transactions t ON u.id = t.user_id
      GROUP BY 
        u.id
      ORDER BY 
        transaction_count DESC
      LIMIT ?;
    `;
    
    // 执行查询
    const results = await sequelize.query(query, {
      replacements: [limit],
      type: QueryTypes.SELECT
    });
    
    return results;
  }, CACHE_TTL);
}

/**
 * 执行优化的最近交易查询
 * @param {number} limit - 限制结果数量
 * @returns {Promise<Array>} - 查询结果
 */
async function getRecentTransactions(limit = 100) {
  const cacheKey = `${CACHE_KEY_PREFIX}recent_transactions:${limit}`;
  
  // 使用缓存工具获取或设置缓存
  return await cacheUtils.getOrSet(cacheKey, async () => {
    // 使用优化的SQL查询，使用索引
    const query = `
      SELECT 
        t.*,
        u.username,
        u.user_id
      FROM 
        transactions t
      JOIN 
        users u ON t.user_id = u.id
      ORDER BY 
        t.created_at DESC
      LIMIT ?;
    `;
    
    // 执行查询
    const results = await sequelize.query(query, {
      replacements: [limit],
      type: QueryTypes.SELECT
    });
    
    return results;
  }, CACHE_TTL);
}

/**
 * 执行优化的系统参数查询
 * @param {string} groupName - 参数组名，可选
 * @returns {Promise<Array>} - 查询结果
 */
async function getSystemParams(groupName = null) {
  const cacheKey = `${CACHE_KEY_PREFIX}system_params:${groupName || 'all'}`;
  
  // 使用缓存工具获取或设置缓存
  return await cacheUtils.getOrSet(cacheKey, async () => {
    // 构建查询
    let query = 'SELECT * FROM system_params';
    const replacements = [];
    
    // 如果指定了参数组名，添加条件
    if (groupName) {
      query += ' WHERE group_name = ?';
      replacements.push(groupName);
    }
    
    // 执行查询
    const results = await sequelize.query(query, {
      replacements,
      type: QueryTypes.SELECT
    });
    
    return results;
  }, CACHE_TTL);
}

/**
 * 清除查询缓存
 */
function clearQueryCache() {
  // 获取所有缓存键
  const stats = cacheUtils.getStats();
  
  // 删除查询相关的缓存
  stats.keys.forEach(key => {
    if (key.startsWith(CACHE_KEY_PREFIX)) {
      cacheUtils.del(key);
    }
  });
}

module.exports = {
  getUsersWithTransactions,
  getRecentTransactions,
  getSystemParams,
  clearQueryCache
};
