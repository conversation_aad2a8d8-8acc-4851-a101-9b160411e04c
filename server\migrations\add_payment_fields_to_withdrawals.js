/**
 * 添加支付通道相关字段到withdrawals表
 */
const sequelize = require('../config/database');
const { DataTypes } = require('sequelize');

async function up() {
  const queryInterface = sequelize.getQueryInterface();
  
  try {
    // 修改status字段，添加新的枚举值
    await queryInterface.changeColumn('withdrawals', 'status', {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'processing', 'completed', 'failed'),
      allowNull: false,
      defaultValue: 'pending'
    });
    
    // 添加支付通道ID字段
    await queryInterface.addColumn('withdrawals', 'payment_channel_id', {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '支付通道ID'
    });
    
    // 添加支付通道订单号字段
    await queryInterface.addColumn('withdrawals', 'payment_channel_order_id', {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '支付通道订单号'
    });
    
    // 添加支付平台订单号字段
    await queryInterface.addColumn('withdrawals', 'payment_platform_order_no', {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '支付平台订单号'
    });
    
    // 添加支付通道返回数据字段
    await queryInterface.addColumn('withdrawals', 'payment_channel_data', {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '支付通道返回数据'
    });
    
    // 添加回调状态字段
    await queryInterface.addColumn('withdrawals', 'callback_status', {
      type: DataTypes.ENUM('none', 'channel_callback', 'manual'),
      allowNull: false,
      defaultValue: 'none',
      comment: '回调状态：none=未回调, channel_callback=通道回调, manual=手动完成'
    });
    
    // 添加手续费字段（如果不存在）
    const tableInfo = await queryInterface.describeTable('withdrawals');
    if (!tableInfo.fee) {
      await queryInterface.addColumn('withdrawals', 'fee', {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
        comment: '手续费'
      });
    }
    
    console.log('成功添加支付通道相关字段到withdrawals表');
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  }
}

async function down() {
  const queryInterface = sequelize.getQueryInterface();
  
  try {
    // 删除添加的字段
    await queryInterface.removeColumn('withdrawals', 'payment_channel_id');
    await queryInterface.removeColumn('withdrawals', 'payment_channel_order_id');
    await queryInterface.removeColumn('withdrawals', 'payment_platform_order_no');
    await queryInterface.removeColumn('withdrawals', 'payment_channel_data');
    await queryInterface.removeColumn('withdrawals', 'callback_status');
    
    // 恢复status字段的原始枚举值
    await queryInterface.changeColumn('withdrawals', 'status', {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'completed'),
      allowNull: false,
      defaultValue: 'pending'
    });
    
    console.log('成功回滚withdrawals表的支付通道相关字段');
  } catch (error) {
    console.error('回滚失败:', error);
    throw error;
  }
}

module.exports = { up, down };
