/**
 * 充值订单数据模型
 */
export interface Deposit {
  id: number
  user_id?: number
  userId?: number
  username?: string
  user?: {
    username: string
  }
  order_number?: string
  orderNumber?: string
  amount: number
  actual_amount?: number
  actualAmount?: number
  payment_method?: string
  paymentMethod?: string
  payment_status?: string
  paymentStatus?: string
  status: string
  orderType?: string
  payment_time?: string
  paymentTime?: string
  payment_platform_order_no?: string
  paymentPlatformOrderNo?: string
  callback_status?: 'no_callback' | 'mock_callback' | 'channel_callback' | string
  callbackStatus?: '未回调' | '模拟回调' | '通道回调' | string
  commission_status?: string
  commissionStatus?: string
  created_at?: string
  createdAt?: string
  updated_at?: string
  updatedAt?: string

  // 兼容旧代码的字段
  projectName?: string
  quantity?: number
  platformOrderNumber?: string
  receivingAccount?: string
}

/**
 * 充值订单查询参数
 */
export interface DepositQuery {
  id?: string
  userId?: string
  username?: string
  orderNumber?: string
  paymentMethod?: string
  status?: string
  paymentStatus?: string
  orderType?: string
  callbackStatus?: string
  commissionStatus?: string
  startDate?: string
  endDate?: string
  page?: number
  limit?: number
}

/**
 * 充值订单响应数据
 */
export interface DepositResponse {
  code: number
  message: string
  data: {
    total: number
    items: Deposit[]
  }
}

/**
 * 充值订单详情响应
 */
export interface DepositDetailResponse {
  code: number
  message: string
  data: Deposit
}

/**
 * 模拟回调请求参数
 */
export interface MockCallbackRequest {
  id: number
}

/**
 * 模拟回调响应
 */
export interface MockCallbackResponse {
  code: number
  message: string
  data: null
}
