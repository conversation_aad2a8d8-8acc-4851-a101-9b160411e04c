const { Sequelize } = require('sequelize');
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// 数据库配置
const config = {
  database: 'fox_db',
  username: 'root',
  password: 'MySQL3352~!',
  host: 'localhost',
  dialect: 'mysql'
};

// 创建 Sequelize 实例
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    dialect: config.dialect,
    logging: console.log
  }
);

// 测试数据库连接
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功！');
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error);
    return false;
  }
}

// 执行迁移脚本
async function runMigration() {
  try {
    const migrationPath = path.join(__dirname, 'migrations', 'add_user_code.js');
    
    // 检查迁移脚本是否存在
    if (!fs.existsSync(migrationPath)) {
      console.error('迁移脚本不存在:', migrationPath);
      return;
    }
    
    console.log('开始执行迁移脚本...');
    
    // 加载迁移脚本
    const migration = require(migrationPath);
    
    // 执行迁移
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    
    console.log('迁移脚本执行成功！');
  } catch (error) {
    console.error('迁移脚本执行失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 主函数
async function main() {
  const connected = await testConnection();
  if (connected) {
    await runMigration();
  }
}

// 执行主函数
main();
