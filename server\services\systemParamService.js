/**
 * 系统参数服务
 * 用于获取和设置系统参数
 */
const { SystemParam } = require('../models');
const cacheUtils = require('../utils/cacheUtils');

// 缓存键前缀
const CACHE_KEY_PREFIX = 'system_param:';

// 缓存过期时间（毫秒）
const CACHE_TTL = 30 * 60 * 1000; // 30分钟

/**
 * 获取系统参数
 * @param {string} key - 参数键
 * @param {string} defaultValue - 默认值
 * @returns {Promise<string>} - 参数值
 */
async function getParam(key, defaultValue = '') {
  const cacheKey = `${CACHE_KEY_PREFIX}${key}`;
  
  // 使用缓存工具获取或设置缓存
  return await cacheUtils.getOrSet(cacheKey, async () => {
    // 从数据库获取参数
    const param = await SystemParam.findOne({
      where: { param_key: key }
    });
    
    // 返回参数值或默认值
    return param ? param.param_value : defaultValue;
  }, CACHE_TTL);
}

/**
 * 设置系统参数
 * @param {string} key - 参数键
 * @param {string} value - 参数值
 * @returns {Promise<SystemParam>} - 更新后的参数
 */
async function setParam(key, value) {
  // 查找参数
  let param = await SystemParam.findOne({
    where: { param_key: key }
  });
  
  // 如果参数不存在，则创建
  if (!param) {
    param = await SystemParam.create({
      param_key: key,
      param_value: value,
      group_name: 'default'
    });
  } else {
    // 更新参数值
    param.param_value = value;
    await param.save();
  }
  
  // 删除缓存
  const cacheKey = `${CACHE_KEY_PREFIX}${key}`;
  cacheUtils.del(cacheKey);
  
  return param;
}

/**
 * 获取系统参数组
 * @param {string} groupName - 参数组名
 * @returns {Promise<Array<SystemParam>>} - 参数组
 */
async function getParamGroup(groupName) {
  const cacheKey = `${CACHE_KEY_PREFIX}group:${groupName}`;
  
  // 使用缓存工具获取或设置缓存
  return await cacheUtils.getOrSet(cacheKey, async () => {
    // 从数据库获取参数组
    const params = await SystemParam.findAll({
      where: { group_name: groupName }
    });
    
    return params;
  }, CACHE_TTL);
}

/**
 * 批量更新系统参数
 * @param {Array<Object>} params - 参数数组，每个对象包含 param_key 和 param_value
 * @returns {Promise<Array<SystemParam>>} - 更新后的参数数组
 */
async function batchUpdateParams(params) {
  const results = [];
  
  // 批量更新参数
  for (const param of params) {
    const result = await setParam(param.param_key, param.param_value);
    results.push(result);
  }
  
  return results;
}

/**
 * 清除系统参数缓存
 */
function clearParamCache() {
  // 获取所有缓存键
  const stats = cacheUtils.getStats();
  
  // 删除系统参数相关的缓存
  stats.keys.forEach(key => {
    if (key.startsWith(CACHE_KEY_PREFIX)) {
      cacheUtils.del(key);
    }
  });
}

module.exports = {
  getParam,
  setParam,
  getParamGroup,
  batchUpdateParams,
  clearParamCache
};
