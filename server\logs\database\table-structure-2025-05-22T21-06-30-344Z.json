{"account_balances": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "account_type", "Type": "enum('income','deposit')", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "balance", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "account_balances", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 71, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "account_balances", "Non_unique": 0, "Key_name": "user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 39, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "account_balances", "Non_unique": 0, "Key_name": "user_id", "Seq_in_index": 2, "Column_name": "account_type", "Collation": "A", "Cardinality": 71, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "account_balances", "Non_unique": 0, "Key_name": "account_balances_user_id_account_type_unique", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 39, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "account_balances", "Non_unique": 0, "Key_name": "account_balances_user_id_account_type_unique", "Seq_in_index": 2, "Column_name": "account_type", "Collation": "A", "Cardinality": 71, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "account_balances", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 39, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "account_balances", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 79, "Avg_row_length": 207, "Data_length": 16384, "Max_data_length": 0, "Index_length": 49152, "Data_free": 0, "Auto_increment": 102, "Create_time": "2025-05-06 11:43:44", "Update_time": "2025-05-22 14:59:54", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "admin_roles": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "admin_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "role_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "admin_roles", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "admin_roles", "Non_unique": 0, "Key_name": "unique_admin_role", "Seq_in_index": 1, "Column_name": "admin_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "admin_roles", "Non_unique": 0, "Key_name": "unique_admin_role", "Seq_in_index": 2, "Column_name": "role_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "admin_roles", "Non_unique": 1, "Key_name": "role_id", "Seq_in_index": 1, "Column_name": "role_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "admin_roles", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 1, "Avg_row_length": 16384, "Data_length": 16384, "Max_data_length": 0, "Index_length": 32768, "Data_free": 0, "Auto_increment": 22, "Create_time": "2025-04-20 17:06:33", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "admin_with_roles": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "role", "Type": "<PERSON><PERSON><PERSON>(5)", "Null": "NO", "Key": "", "Default": "", "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "last_login_at", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "roles", "Type": "json", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "indices": [], "stats": {"Name": "admin_with_roles", "Engine": null, "Version": null, "Row_format": null, "Rows": null, "Avg_row_length": null, "Data_length": null, "Max_data_length": null, "Index_length": null, "Data_free": null, "Auto_increment": null, "Create_time": "2025-05-22 14:57:16", "Update_time": null, "Check_time": null, "Collation": null, "Checksum": null, "Create_options": null, "Comment": "VIEW"}}, "admins": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "nickname", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "is_super", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "last_login", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "admins", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "admins", "Non_unique": 0, "Key_name": "username", "Seq_in_index": 1, "Column_name": "username", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "admins", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 2, "Avg_row_length": 8192, "Data_length": 16384, "Max_data_length": 0, "Index_length": 16384, "Data_free": 0, "Auto_increment": 22, "Create_time": "2025-04-20 17:06:33", "Update_time": "2025-05-22 14:00:35", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "attachments": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "category", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "MUL", "Default": "未归类", "Extra": ""}, {"Field": "filename", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "original_name", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "file_path", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "file_size", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "width", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "height", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "frame_count", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "file_type", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "mime_type", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "metadata", "Type": "json", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "storage_engine", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "YES", "Key": "", "Default": "local", "Extra": ""}, {"Field": "url", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "attachments", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 3, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "attachments", "Non_unique": 1, "Key_name": "idx_category", "Seq_in_index": 1, "Column_name": "category", "Collation": "A", "Cardinality": 3, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "attachments", "Non_unique": 1, "Key_name": "idx_file_type", "Seq_in_index": 1, "Column_name": "file_type", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "attachments", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 3, "Avg_row_length": 5461, "Data_length": 16384, "Max_data_length": 0, "Index_length": 32768, "Data_free": 0, "Auto_increment": 20, "Create_time": "2025-04-28 05:11:06", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "bank_cards": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "bank_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "bank_name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "card_number", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "card_holder", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "bank_code", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "card_type", "Type": "enum('user','system')", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "daily_limit", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "MUL", "Default": "1", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "bank_cards", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_cards", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_cards", "Non_unique": 1, "Key_name": "idx_card_type", "Seq_in_index": 1, "Column_name": "card_type", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_cards", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_cards", "Non_unique": 1, "Key_name": "fk_bank_cards_bank_id", "Seq_in_index": 1, "Column_name": "bank_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_cards", "Non_unique": 1, "Key_name": "bank_cards_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_cards", "Non_unique": 1, "Key_name": "bank_cards_card_type", "Seq_in_index": 1, "Column_name": "card_type", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_cards", "Non_unique": 1, "Key_name": "bank_cards_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "bank_cards", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 1, "Avg_row_length": 16384, "Data_length": 16384, "Max_data_length": 0, "Index_length": 65536, "Data_free": 0, "Auto_increment": 10, "Create_time": "2025-05-21 16:39:16", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "bank_channel_mappings": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "bank_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "payment_channel_id", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "bank_code", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "bank_channel_mappings", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_channel_mappings", "Non_unique": 0, "Key_name": "bank_id", "Seq_in_index": 1, "Column_name": "bank_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "bank_channel_mappings", "Non_unique": 0, "Key_name": "bank_id", "Seq_in_index": 2, "Column_name": "payment_channel_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "bank_channel_mappings", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 1, "Avg_row_length": 16384, "Data_length": 16384, "Max_data_length": 0, "Index_length": 16384, "Data_free": 0, "Auto_increment": 17, "Create_time": "2025-05-17 07:57:11", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "banks": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "banks", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "banks", "Non_unique": 0, "Key_name": "name", "Seq_in_index": 1, "Column_name": "name", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "banks", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 1, "Avg_row_length": 16384, "Data_length": 16384, "Max_data_length": 0, "Index_length": 16384, "Data_free": 0, "Auto_increment": 14, "Create_time": "2025-05-17 07:56:51", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "banners": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "attachment_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "url", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "position", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "MUL", "Default": "home", "Extra": ""}, {"Field": "sort_order", "Type": "int", "Null": "YES", "Key": "MUL", "Default": "0", "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "MUL", "Default": "1", "Extra": ""}, {"Field": "start_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "end_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "banners", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "banners", "Non_unique": 1, "Key_name": "idx_position", "Seq_in_index": 1, "Column_name": "position", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "banners", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "banners", "Non_unique": 1, "Key_name": "idx_sort_order", "Seq_in_index": 1, "Column_name": "sort_order", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "banners", "Non_unique": 1, "Key_name": "fk_banners_attachment", "Seq_in_index": 1, "Column_name": "attachment_id", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "banners", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 2, "Avg_row_length": 8192, "Data_length": 16384, "Max_data_length": 0, "Index_length": 65536, "Data_free": 0, "Auto_increment": 23, "Create_time": "2025-04-25 14:54:44", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "commissions": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "from_user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "investment_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "level", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "rate", "Type": "decimal(5,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "enum('pending','paid')", "Null": "YES", "Key": "MUL", "Default": "pending", "Extra": ""}, {"Field": "transaction_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "type", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "commissions", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 5481, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "commissions", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 22, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "commissions", "Non_unique": 1, "Key_name": "idx_from_user_id", "Seq_in_index": 1, "Column_name": "from_user_id", "Collation": "A", "Cardinality": 21, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "commissions", "Non_unique": 1, "Key_name": "idx_investment_id", "Seq_in_index": 1, "Column_name": "investment_id", "Collation": "A", "Cardinality": 21, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "commissions", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "commissions", "Non_unique": 1, "Key_name": "fk_commissions_transaction", "Seq_in_index": 1, "Column_name": "transaction_id", "Collation": "A", "Cardinality": 5357, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "commissions", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 5581, "Avg_row_length": 82, "Data_length": 458752, "Max_data_length": 0, "Index_length": 671744, "Data_free": 4194304, "Auto_increment": 5458, "Create_time": "2025-05-08 11:19:11", "Update_time": "2025-05-22 14:59:54", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "customer_service_images": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "attachment_id", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "file_name", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "file_path", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "file_size", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "customer_service_images", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "customer_service_images", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 1, "Avg_row_length": 16384, "Data_length": 16384, "Max_data_length": 0, "Index_length": 0, "Data_free": 0, "Auto_increment": 16, "Create_time": "2025-05-04 07:35:50", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "customer_services": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "type", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "url", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "icon", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "weight", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "description", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "customer_services", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "customer_services", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 0, "Avg_row_length": 0, "Data_length": 16384, "Max_data_length": 0, "Index_length": 0, "Data_free": 0, "Auto_increment": 25, "Create_time": "2025-04-23 12:16:52", "Update_time": "2025-05-21 15:14:01", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "daily_statistics": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "date", "Type": "date", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "new_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "deposit_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "deposit_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "deposit_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "withdrawal_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "withdrawal_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "withdrawal_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "investment_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "investment_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "investment_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "profit_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "profit_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "commission_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "commission_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "register_deposit_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "platform_profit", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "daily_statistics", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 30, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "daily_statistics", "Non_unique": 0, "Key_name": "date", "Seq_in_index": 1, "Column_name": "date", "Collation": "A", "Cardinality": 30, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "daily_statistics", "Non_unique": 1, "Key_name": "idx_date", "Seq_in_index": 1, "Column_name": "date", "Collation": "A", "Cardinality": 30, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "daily_statistics", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 32, "Avg_row_length": 512, "Data_length": 16384, "Max_data_length": 0, "Index_length": 32768, "Data_free": 0, "Auto_increment": 34, "Create_time": "2025-05-20 13:14:15", "Update_time": "2025-05-22 14:00:35", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "deposits": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "order_number", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "actual_amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_method", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_account", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "receiving_card_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "transaction_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "status", "Type": "enum('pending','paid','cancelled','completed')", "Null": "YES", "Key": "MUL", "Default": "pending", "Extra": ""}, {"Field": "remark", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "completion_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "callback_status", "Type": "enum('no_callback','mock_callback','channel_callback')", "Null": "YES", "Key": "", "Default": "no_callback", "Extra": ""}, {"Field": "payment_platform_order_no", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "MUL", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "payment_channel_id", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "bank_card_id", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "deposits", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 46, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "deposits", "Non_unique": 0, "Key_name": "order_number", "Seq_in_index": 1, "Column_name": "order_number", "Collation": "A", "Cardinality": 46, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "deposits", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 5, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "deposits", "Non_unique": 1, "Key_name": "idx_order_number", "Seq_in_index": 1, "Column_name": "order_number", "Collation": "A", "Cardinality": 46, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "deposits", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 3, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "deposits", "Non_unique": 1, "Key_name": "idx_created_at", "Seq_in_index": 1, "Column_name": "created_at", "Collation": "A", "Cardinality": 46, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "deposits", "Non_unique": 1, "Key_name": "fk_deposits_receiving_card", "Seq_in_index": 1, "Column_name": "receiving_card_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "deposits", "Non_unique": 1, "Key_name": "fk_deposits_transaction", "Seq_in_index": 1, "Column_name": "transaction_id", "Collation": "A", "Cardinality": 16, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "deposits", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 47, "Avg_row_length": 348, "Data_length": 16384, "Max_data_length": 0, "Index_length": 114688, "Data_free": 0, "Auto_increment": 48, "Create_time": "2025-05-19 15:54:31", "Update_time": "2025-05-21 21:26:04", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "investment_profits": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "investment_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "profit_time", "Type": "datetime", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "distribution_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "theoretical_profit_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "enum('pending','paid')", "Null": "YES", "Key": "MUL", "Default": "pending", "Extra": ""}, {"Field": "transaction_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "MUL", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "investment_profits", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 889, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 0, "Key_name": "idx_investment_theoretical_time", "Seq_in_index": 1, "Column_name": "investment_id", "Collation": "A", "Cardinality": 36, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 0, "Key_name": "idx_investment_theoretical_time", "Seq_in_index": 2, "Column_name": "theoretical_profit_time", "Collation": "A", "Cardinality": 255, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "idx_investment_id", "Seq_in_index": 1, "Column_name": "investment_id", "Collation": "A", "Cardinality": 36, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 20, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "idx_profit_time", "Seq_in_index": 1, "Column_name": "profit_time", "Collation": "A", "Cardinality": 525, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "fk_investment_profits_transaction", "Seq_in_index": 1, "Column_name": "transaction_id", "Collation": "A", "Cardinality": 889, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "investment_profits_investment_id_idx", "Seq_in_index": 1, "Column_name": "investment_id", "Collation": "A", "Cardinality": 36, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "investment_profits_user_id_idx", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 20, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investment_profits", "Non_unique": 1, "Key_name": "investment_profits_created_at_idx", "Seq_in_index": 1, "Column_name": "created_at", "Collation": "A", "Cardinality": 400, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "investment_profits", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 970, "Avg_row_length": 101, "Data_length": 98304, "Max_data_length": 0, "Index_length": 147456, "Data_free": 5242880, "Auto_increment": 4720, "Create_time": "2025-05-21 16:37:20", "Update_time": "2025-05-22 14:59:54", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "investments": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "project_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "quantity", "Type": "int", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "profit_rate", "Type": "decimal(5,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "profit_cycle", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "total_profit", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "profit_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "last_profit_time", "Type": "datetime", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "status", "Type": "enum('active','paused','completed')", "Null": "YES", "Key": "MUL", "Default": "active", "Extra": ""}, {"Field": "start_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "end_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "MUL", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "is_gift", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}], "indices": [{"Table": "investments", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 35, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investments", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 19, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investments", "Non_unique": 1, "Key_name": "idx_project_id", "Seq_in_index": 1, "Column_name": "project_id", "Collation": "A", "Cardinality": 6, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investments", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investments", "Non_unique": 1, "Key_name": "idx_created_at", "Seq_in_index": 1, "Column_name": "created_at", "Collation": "A", "Cardinality": 35, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "investments", "Non_unique": 1, "Key_name": "investments_last_profit_time_idx", "Seq_in_index": 1, "Column_name": "last_profit_time", "Collation": "A", "Cardinality": 17, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "investments", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 36, "Avg_row_length": 455, "Data_length": 16384, "Max_data_length": 0, "Index_length": 81920, "Data_free": 0, "Auto_increment": 137, "Create_time": "2025-05-09 15:09:38", "Update_time": "2025-05-22 14:59:54", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "invite_codes": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "code", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "used_count", "Type": "int", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "max_uses", "Type": "int", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "invite_codes", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 45, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "invite_codes", "Non_unique": 0, "Key_name": "code", "Seq_in_index": 1, "Column_name": "code", "Collation": "A", "Cardinality": 45, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "invite_codes", "Non_unique": 1, "Key_name": "fk_invite_user", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 45, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "invite_codes", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 49, "Avg_row_length": 334, "Data_length": 16384, "Max_data_length": 0, "Index_length": 32768, "Data_free": 0, "Auto_increment": 61, "Create_time": "2025-04-25 15:49:29", "Update_time": "2025-05-21 19:59:32", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "invite_rewards": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "title", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "deposit_users", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "register_users", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "bonus", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "enum('active','inactive')", "Null": "YES", "Key": "", "Default": "active", "Extra": ""}, {"Field": "description", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "invite_rewards", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "invite_rewards", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 0, "Avg_row_length": 0, "Data_length": 16384, "Max_data_length": 0, "Index_length": 0, "Data_free": 0, "Auto_increment": 1, "Create_time": "2025-04-23 12:16:14", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "orders": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "order_no", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "project_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "amount", "Type": "decimal(12,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "expected_return", "Type": "decimal(12,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "actual_return", "Type": "decimal(12,2)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "enum('pending','paid','completed','cancelled','refunded')", "Null": "NO", "Key": "", "Default": "pending", "Extra": ""}, {"Field": "payment_method", "Type": "enum('balance','alipay','wechat','bank')", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "start_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "end_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "orders", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "orders", "Non_unique": 0, "Key_name": "order_no", "Seq_in_index": 1, "Column_name": "order_no", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "orders", "Non_unique": 1, "Key_name": "user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "orders", "Non_unique": 1, "Key_name": "project_id", "Seq_in_index": 1, "Column_name": "project_id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "orders", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 0, "Avg_row_length": 0, "Data_length": 16384, "Max_data_length": 0, "Index_length": 49152, "Data_free": 0, "Auto_increment": 1, "Create_time": "2025-05-05 06:39:31", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "payment_channels": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "code", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "country_code", "Type": "<PERSON><PERSON><PERSON>(10)", "Null": "YES", "Key": "", "Default": "CN", "Extra": ""}, {"Field": "deposit_enabled", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "withdraw_enabled", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "is_default", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "weight", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "config", "Type": "json", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "icon", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "min_deposit_amount", "Type": "decimal(10,2)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "max_deposit_amount", "Type": "decimal(10,2)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "payment_channels", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "payment_channels", "Non_unique": 0, "Key_name": "code", "Seq_in_index": 1, "Column_name": "code", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "payment_channels", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 2, "Avg_row_length": 8192, "Data_length": 16384, "Max_data_length": 0, "Index_length": 16384, "Data_free": 0, "Auto_increment": 6, "Create_time": "2025-05-17 16:13:04", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "permissions": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "code", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "description", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "type", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "permissions", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 50, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "permissions", "Non_unique": 0, "Key_name": "name", "Seq_in_index": 1, "Column_name": "name", "Collation": "A", "Cardinality": 50, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "permissions", "Non_unique": 0, "Key_name": "code", "Seq_in_index": 1, "Column_name": "code", "Collation": "A", "Cardinality": 50, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "permissions", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 50, "Avg_row_length": 327, "Data_length": 16384, "Max_data_length": 0, "Index_length": 32768, "Data_free": 0, "Auto_increment": 66, "Create_time": "2025-04-20 17:06:33", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "projects": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "type", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "category", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "description", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "image_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "video_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "price", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "min_investment", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "max_investment", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "duration", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "duration_unit", "Type": "<PERSON><PERSON><PERSON>(10)", "Null": "YES", "Key": "", "Default": "天", "Extra": ""}, {"Field": "expected_return", "Type": "decimal(5,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "profit_time", "Type": "int", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "quantity", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "sold_quantity", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "vip_level_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "currency", "Type": "<PERSON><PERSON><PERSON>(10)", "Null": "YES", "Key": "", "Default": "CNY", "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "NO", "Key": "MUL", "Default": "1", "Extra": ""}, {"Field": "commission_enabled", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "level1_commission", "Type": "decimal(5,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "level2_commission", "Type": "decimal(5,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "level3_commission", "Type": "decimal(5,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "sell_price", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "price_type", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "", "Default": "固定价格", "Extra": ""}, {"Field": "payment_method", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "", "Default": "余额支付", "Extra": ""}, {"Field": "purchase_time", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": "00:00:00-23:59:59", "Extra": ""}, {"Field": "actual_quantity", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "sort_order", "Type": "int", "Null": "YES", "Key": "", "Default": "100", "Extra": ""}, {"Field": "max_purchase_times", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "simultaneous_purchases", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "vip_type_operator", "Type": "<PERSON><PERSON><PERSON>(10)", "Null": "YES", "Key": "", "Default": "=", "Extra": ""}, {"Field": "sell_status", "Type": "tinyint", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "return_principal", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "is_free", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "max_profit_times", "Type": "int", "Null": "NO", "Key": "", "Default": "0", "Extra": ""}, {"Field": "weekly_profit_days", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "YES", "Key": "", "Default": "1,2,3,4,5,6,7", "Extra": ""}, {"Field": "custom_return_enabled", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "custom_return_hours", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "custom_return_rate", "Type": "decimal(5,2)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "projects", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 7, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "projects", "Non_unique": 1, "Key_name": "idx_type", "Seq_in_index": 1, "Column_name": "type", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "projects", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "projects", "Non_unique": 1, "Key_name": "idx_vip_level_id", "Seq_in_index": 1, "Column_name": "vip_level_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "projects", "Non_unique": 1, "Key_name": "fk_projects_image", "Seq_in_index": 1, "Column_name": "image_id", "Collation": "A", "Cardinality": 4, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "projects", "Non_unique": 1, "Key_name": "fk_projects_video", "Seq_in_index": 1, "Column_name": "video_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "projects", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 7, "Avg_row_length": 2340, "Data_length": 16384, "Max_data_length": 0, "Index_length": 81920, "Data_free": 0, "Auto_increment": 46, "Create_time": "2025-04-30 09:53:13", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "receiving_bank_cards": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "bank_name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "card_number", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "card_holder", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "branch", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "daily_limit", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "is_default", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "receiving_bank_cards", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "receiving_bank_cards", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 0, "Avg_row_length": 0, "Data_length": 16384, "Max_data_length": 0, "Index_length": 0, "Data_free": 0, "Auto_increment": 1, "Create_time": "2025-04-23 12:09:09", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "role_permissions": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "role_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "permission_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "role_permissions", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 20, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "role_permissions", "Non_unique": 0, "Key_name": "unique_role_permission", "Seq_in_index": 1, "Column_name": "role_id", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "role_permissions", "Non_unique": 0, "Key_name": "unique_role_permission", "Seq_in_index": 2, "Column_name": "permission_id", "Collation": "A", "Cardinality": 20, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "role_permissions", "Non_unique": 1, "Key_name": "permission_id", "Seq_in_index": 1, "Column_name": "permission_id", "Collation": "A", "Cardinality": 20, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "role_permissions", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 20, "Avg_row_length": 819, "Data_length": 16384, "Max_data_length": 0, "Index_length": 32768, "Data_free": 0, "Auto_increment": 64, "Create_time": "2025-04-20 17:06:33", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "roles": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "description", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "status", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}], "indices": [{"Table": "roles", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "roles", "Non_unique": 0, "Key_name": "name", "Seq_in_index": 1, "Column_name": "name", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "roles", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 2, "Avg_row_length": 8192, "Data_length": 16384, "Max_data_length": 0, "Index_length": 16384, "Data_free": 0, "Auto_increment": 3, "Create_time": "2025-04-20 17:27:01", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "sequelizemeta": {"structure": [{"Field": "name", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "PRI", "Default": null, "Extra": ""}], "indices": [{"Table": "sequel<PERSON>met<PERSON>", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "name", "Collation": "A", "Cardinality": 18, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "sequel<PERSON>met<PERSON>", "Non_unique": 0, "Key_name": "name", "Seq_in_index": 1, "Column_name": "name", "Collation": "A", "Cardinality": 18, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "sequel<PERSON>met<PERSON>", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 20, "Avg_row_length": 819, "Data_length": 16384, "Max_data_length": 0, "Index_length": 16384, "Data_free": 0, "Auto_increment": null, "Create_time": "2025-04-26 20:08:25", "Update_time": "2025-05-22 00:41:52", "Check_time": null, "Collation": "utf8mb3_unicode_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "system_params": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "param_key", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "param_value", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "param_type", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": "text", "Extra": ""}, {"Field": "description", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "group_name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "MUL", "Default": "basic", "Extra": ""}, {"Field": "sort_order", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "system_params", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 84, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "system_params", "Non_unique": 0, "Key_name": "param_key", "Seq_in_index": 1, "Column_name": "param_key", "Collation": "A", "Cardinality": 84, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "system_params", "Non_unique": 1, "Key_name": "idx_param_key", "Seq_in_index": 1, "Column_name": "param_key", "Collation": "A", "Cardinality": 84, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "system_params", "Non_unique": 1, "Key_name": "idx_group_name", "Seq_in_index": 1, "Column_name": "group_name", "Collation": "A", "Cardinality": 4, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "system_params", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 84, "Avg_row_length": 195, "Data_length": 16384, "Max_data_length": 0, "Index_length": 49152, "Data_free": 0, "Auto_increment": 97, "Create_time": "2025-04-25 14:54:43", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "total_statistics": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "total_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_deposit_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "total_deposit_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_deposit_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_withdrawal_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "total_withdrawal_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_withdrawal_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_investment_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "total_investment_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_investment_user_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_profit_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "total_profit_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_commission_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "total_commission_count", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "total_platform_profit", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "last_updated_date", "Type": "date", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "total_statistics", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "total_statistics", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 0, "Avg_row_length": 0, "Data_length": 16384, "Max_data_length": 0, "Index_length": 0, "Data_free": 0, "Auto_increment": 2, "Create_time": "2025-05-20 13:14:31", "Update_time": "2025-05-22 14:00:35", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "transactions": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "order_number", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "type", "Type": "enum('deposit','withdrawal','investment','investment_gift','investment_purchase','profit','commission','bonus','increase','deduction')", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "before_balance", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "balance", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "currency", "Type": "<PERSON><PERSON><PERSON>(10)", "Null": "YES", "Key": "", "Default": "CNY", "Extra": ""}, {"Field": "status", "Type": "enum('pending','success','failed')", "Null": "YES", "Key": "MUL", "Default": "pending", "Extra": ""}, {"Field": "reference_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "reference_type", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "description", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "MUL", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "transactions", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 9576, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "transactions", "Non_unique": 0, "Key_name": "idx_order_number", "Seq_in_index": 1, "Column_name": "order_number", "Collation": "A", "Cardinality": 9779, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "transactions", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 40, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "transactions", "Non_unique": 1, "Key_name": "idx_type", "Seq_in_index": 1, "Column_name": "type", "Collation": "A", "Cardinality": 8, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "transactions", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "transactions", "Non_unique": 1, "Key_name": "idx_created_at", "Seq_in_index": 1, "Column_name": "created_at", "Collation": "A", "Cardinality": 2957, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "transactions", "Non_unique": 1, "Key_name": "idx_reference", "Seq_in_index": 1, "Column_name": "reference_id", "Collation": "A", "Cardinality": 111, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "transactions", "Non_unique": 1, "Key_name": "idx_reference", "Seq_in_index": 2, "Column_name": "reference_type", "Collation": "A", "Cardinality": 125, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "transactions", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 10512, "Avg_row_length": 151, "Data_length": 1589248, "Max_data_length": 0, "Index_length": 1589248, "Data_free": 4194304, "Auto_increment": 10395, "Create_time": "2025-05-21 16:36:28", "Update_time": "2025-05-22 14:59:54", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "user_bank_cards": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "bank_name", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "card_number", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "card_holder", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "branch", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "is_default", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "status", "Type": "tinyint(1)", "Null": "YES", "Key": "", "Default": "1", "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "user_bank_cards", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_bank_cards", "Non_unique": 1, "Key_name": "user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 0, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "user_bank_cards", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 0, "Avg_row_length": 0, "Data_length": 16384, "Max_data_length": 0, "Index_length": 16384, "Data_free": 0, "Auto_increment": 1, "Create_time": "2025-04-23 12:08:12", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "user_levels": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "level", "Type": "int", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "upgrade_users", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "upgrade_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "return_rate", "Type": "decimal(5,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "upgrade_bonus", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "image_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "content", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}], "indices": [{"Table": "user_levels", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 10, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_levels", "Non_unique": 0, "Key_name": "level", "Seq_in_index": 1, "Column_name": "level", "Collation": "A", "Cardinality": 10, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_levels", "Non_unique": 1, "Key_name": "idx_level", "Seq_in_index": 1, "Column_name": "level", "Collation": "A", "Cardinality": 10, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_levels", "Non_unique": 1, "Key_name": "fk_user_levels_image", "Seq_in_index": 1, "Column_name": "image_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "user_levels", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 10, "Avg_row_length": 1638, "Data_length": 16384, "Max_data_length": 0, "Index_length": 49152, "Data_free": 0, "Auto_increment": 11, "Create_time": "2025-04-25 14:54:43", "Update_time": null, "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "user_relations": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "parent_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "level", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "invite_code_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "updated_at", "Type": "datetime", "Null": "NO", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "user_relations", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 113, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_relations", "Non_unique": 0, "Key_name": "uk_user_parent_level", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 50, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_relations", "Non_unique": 0, "Key_name": "uk_user_parent_level", "Seq_in_index": 2, "Column_name": "parent_id", "Collation": "A", "Cardinality": 113, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_relations", "Non_unique": 0, "Key_name": "uk_user_parent_level", "Seq_in_index": 3, "Column_name": "level", "Collation": "A", "Cardinality": 113, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_relations", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 50, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_relations", "Non_unique": 1, "Key_name": "idx_parent_id", "Seq_in_index": 1, "Column_name": "parent_id", "Collation": "A", "Cardinality": 34, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_relations", "Non_unique": 1, "Key_name": "idx_level", "Seq_in_index": 1, "Column_name": "level", "Collation": "A", "Cardinality": 3, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "user_relations", "Non_unique": 1, "Key_name": "fk_relation_invite_code", "Seq_in_index": 1, "Column_name": "invite_code_id", "Collation": "A", "Cardinality": 35, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "user_relations", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 113, "Avg_row_length": 144, "Data_length": 16384, "Max_data_length": 0, "Index_length": 81920, "Data_free": 0, "Auto_increment": 122, "Create_time": "2025-05-04 12:01:22", "Update_time": "2025-05-21 19:59:32", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "users": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "username", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "password", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "name", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "email", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "phone", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "country_code", "Type": "<PERSON><PERSON><PERSON>(10)", "Null": "YES", "Key": "", "Default": "+86", "Extra": ""}, {"Field": "avatar", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": "https://via.placeholder.com/30", "Extra": ""}, {"Field": "invite_code", "Type": "<PERSON><PERSON><PERSON>(20)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "inviter_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "level_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": "1", "Extra": ""}, {"Field": "gender", "Type": "enum('男','女')", "Null": "YES", "Key": "", "Default": "男", "Extra": ""}, {"Field": "points", "Type": "int", "Null": "YES", "Key": "", "Default": "0", "Extra": ""}, {"Field": "balance", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "frozen_amount", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "status", "Type": "enum('active','inactive','blocked')", "Null": "YES", "Key": "MUL", "Default": "active", "Extra": ""}, {"Field": "allow_purchase", "Type": "tinyint(1)", "Null": "NO", "Key": "", "Default": "1", "Extra": ""}, {"Field": "last_login", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "user_id", "Type": "<PERSON><PERSON><PERSON>(10)", "Null": "YES", "Key": "UNI", "Default": null, "Extra": ""}], "indices": [{"Table": "users", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 51, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 0, "Key_name": "username", "Seq_in_index": 1, "Column_name": "username", "Collation": "A", "Cardinality": 51, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 0, "Key_name": "invite_code", "Seq_in_index": 1, "Column_name": "invite_code", "Collation": "A", "Cardinality": 51, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 0, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 51, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 1, "Key_name": "idx_username", "Seq_in_index": 1, "Column_name": "username", "Collation": "A", "Cardinality": 51, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 1, "Key_name": "idx_invite_code", "Seq_in_index": 1, "Column_name": "invite_code", "Collation": "A", "Cardinality": 51, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 1, "Key_name": "idx_inviter_id", "Seq_in_index": 1, "Column_name": "inviter_id", "Collation": "A", "Cardinality": 34, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "users", "Non_unique": 1, "Key_name": "fk_users_level", "Seq_in_index": 1, "Column_name": "level_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "users", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 52, "Avg_row_length": 315, "Data_length": 16384, "Max_data_length": 0, "Index_length": 131072, "Data_free": 0, "Auto_increment": 63, "Create_time": "2025-05-06 00:29:36", "Update_time": "2025-05-21 22:31:06", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}, "withdrawals": {"structure": [{"Field": "id", "Type": "int", "Null": "NO", "Key": "PRI", "Default": null, "Extra": "auto_increment"}, {"Field": "user_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "order_number", "Type": "<PERSON><PERSON><PERSON>(50)", "Null": "NO", "Key": "UNI", "Default": null, "Extra": ""}, {"Field": "amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "actual_amount", "Type": "decimal(15,2)", "Null": "NO", "Key": "", "Default": null, "Extra": ""}, {"Field": "bank_card_id", "Type": "int", "Null": "NO", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "transaction_id", "Type": "int", "Null": "YES", "Key": "MUL", "Default": null, "Extra": ""}, {"Field": "status", "Type": "enum('pending','approved','rejected','processing','completed','failed')", "Null": "NO", "Key": "MUL", "Default": "pending", "Extra": ""}, {"Field": "remark", "Type": "<PERSON><PERSON><PERSON>(255)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "approval_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "completion_time", "Type": "datetime", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "created_at", "Type": "timestamp", "Null": "YES", "Key": "MUL", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED"}, {"Field": "updated_at", "Type": "timestamp", "Null": "YES", "Key": "", "Default": "CURRENT_TIMESTAMP", "Extra": "DEFAULT_GENERATED on update CURRENT_TIMESTAMP"}, {"Field": "payment_channel_id", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_channel_order_id", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_platform_order_no", "Type": "<PERSON><PERSON><PERSON>(100)", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "payment_channel_data", "Type": "text", "Null": "YES", "Key": "", "Default": null, "Extra": ""}, {"Field": "callback_status", "Type": "enum('none','channel_callback','manual')", "Null": "NO", "Key": "", "Default": "none", "Extra": ""}, {"Field": "fee", "Type": "decimal(15,2)", "Null": "YES", "Key": "", "Default": "0.00", "Extra": ""}, {"Field": "refund_transaction_id", "Type": "int", "Null": "YES", "Key": "", "Default": null, "Extra": ""}], "indices": [{"Table": "withdrawals", "Non_unique": 0, "Key_name": "PRIMARY", "Seq_in_index": 1, "Column_name": "id", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "withdrawals", "Non_unique": 0, "Key_name": "order_number", "Seq_in_index": 1, "Column_name": "order_number", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "withdrawals", "Non_unique": 1, "Key_name": "idx_user_id", "Seq_in_index": 1, "Column_name": "user_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "withdrawals", "Non_unique": 1, "Key_name": "idx_order_number", "Seq_in_index": 1, "Column_name": "order_number", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "withdrawals", "Non_unique": 1, "Key_name": "idx_status", "Seq_in_index": 1, "Column_name": "status", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "withdrawals", "Non_unique": 1, "Key_name": "idx_created_at", "Seq_in_index": 1, "Column_name": "created_at", "Collation": "A", "Cardinality": 2, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "withdrawals", "Non_unique": 1, "Key_name": "fk_withdrawals_bank_card", "Seq_in_index": 1, "Column_name": "bank_card_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}, {"Table": "withdrawals", "Non_unique": 1, "Key_name": "fk_withdrawals_transaction", "Seq_in_index": 1, "Column_name": "transaction_id", "Collation": "A", "Cardinality": 1, "Sub_part": null, "Packed": null, "Null": "YES", "Index_type": "BTREE", "Comment": "", "Index_comment": "", "Visible": "YES", "Expression": null}], "stats": {"Name": "withdrawals", "Engine": "InnoDB", "Version": 10, "Row_format": "Dynamic", "Rows": 9, "Avg_row_length": 1820, "Data_length": 16384, "Max_data_length": 0, "Index_length": 114688, "Data_free": 0, "Auto_increment": 12, "Create_time": "2025-05-22 00:41:51", "Update_time": "2025-05-22 01:56:56", "Check_time": null, "Collation": "utf8mb4_0900_ai_ci", "Checksum": null, "Create_options": "", "Comment": ""}}}