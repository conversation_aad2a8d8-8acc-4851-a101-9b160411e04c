# 银行编码数据来源说明

## 📋 **概述**

银行编码(bank_code)在取款记录页面中的获取有多个数据来源，系统按照优先级顺序获取，确保显示最准确的银行编码信息。

## 🏦 **数据来源层级**

### **1. 数据库表结构**

#### **BankCard表 - 银行卡信息**
```sql
-- 银行卡表中的bank_code字段
bank_code VARCHAR(100) NULL COMMENT '银行编码'
```

#### **BankChannelMapping表 - 银行通道映射**
```sql
-- 银行通道映射表中的payout_method字段（用于取款）
payout_method INT NULL COMMENT '代付方式编号'
-- 银行通道映射表中的payin_method字段（用于充值）
payin_method INT NULL COMMENT '代收方式编号'
```

#### **Bank表 - 银行基础信息**
```sql
-- 银行基础信息表
name VARCHAR(100) NOT NULL COMMENT '银行名称'
```

### **2. 获取优先级**

系统按照以下优先级获取银行编码：

#### **优先级1: 银行卡表直接存储的编码**
```javascript
// 最高优先级：银行卡表中直接存储的bank_code字段
if (withdrawal.bank_card && withdrawal.bank_card.bank_code) {
  bankCode = withdrawal.bank_card.bank_code;
}
```

**数据来源**: `bank_cards.bank_code`
**特点**: 
- ✅ 直接存储，查询速度快
- ✅ 可以手动设置特殊编码
- ❌ 可能为空或未设置

#### **优先级2: 银行通道映射表（代付方式）**
```javascript
// 第二优先级：从银行映射表获取代付方式（用于取款）
else if (withdrawal.bank_card && withdrawal.bank_card.bank_id && withdrawal.payment_channel_id) {
  const { BankChannelMapping } = require('../models');
  const mapping = await BankChannelMapping.findOne({
    where: {
      bank_id: withdrawal.bank_card.bank_id,
      payment_channel_id: withdrawal.payment_channel_id,
      status: true
    }
  });
  if (mapping && mapping.payout_method) {
    bankCode = mapping.payout_method.toString();
  }
}
```

**数据来源**: `bank_channel_mappings.payout_method`（代付方式编号）
**特点**:
- ✅ 支持不同支付通道的不同编码
- ✅ 动态配置，灵活性高
- ✅ 支持启用/禁用状态
- ❌ 需要bank_id和payment_channel_id都存在

#### **优先级3: 银行名称映射生成**
```javascript
// 第三优先级：根据银行名称生成默认编码
if (!bankCode && withdrawal.bank_card && withdrawal.bank_card.bank_name) {
  const bankCodeMap = {
    'BDO': 'BDO',                    // Banco de Oro
    'BPI': 'BPI',                    // Bank of the Philippine Islands
    'Metrobank': 'MBT',              // Metropolitan Bank & Trust Company
    'Landbank': 'LBP',               // Land Bank of the Philippines
    'PNB': 'PNB',                    // Philippine National Bank
    'UnionBank': 'UBP',              // Union Bank of the Philippines
    'Security Bank': 'SBC',          // Security Bank Corporation
    'RCBC': 'RCBC',                  // Rizal Commercial Banking Corporation
    'Chinabank': 'CBC',              // China Banking Corporation
    'EastWest Bank': 'EWB'           // EastWest Banking Corporation
  };
  bankCode = bankCodeMap[withdrawal.bank_card.bank_name] || 
             withdrawal.bank_card.bank_name.substring(0, 3).toUpperCase();
}
```

**数据来源**: 硬编码映射表 + 银行名称
**特点**:
- ✅ 兜底方案，确保总有编码显示
- ✅ 覆盖主要菲律宾银行
- ✅ 未知银行自动生成编码
- ❌ 静态映射，需要手动维护

## 📊 **实际应用场景**

### **场景1: 完整配置的银行卡**
```javascript
// 银行卡信息
{
  bank_id: 1,
  bank_name: 'BDO',
  bank_code: 'BDO_CUSTOM',  // ← 直接使用此编码
  // ...
}

// 结果: 显示 "BDO_CUSTOM"
```

### **场景2: 有银行映射的银行卡**
```javascript
// 银行卡信息
{
  bank_id: 1,
  bank_name: 'BDO',
  bank_code: null,  // 为空
  // ...
}

// 银行映射信息
{
  bank_id: 1,
  payment_channel_id: 1,
  payout_method: 201,  // ← 使用此代付方式编码
  status: true
}

// 结果: 显示 "201"
```

### **场景3: 只有银行名称的银行卡**
```javascript
// 银行卡信息
{
  bank_id: null,
  bank_name: 'BDO',
  bank_code: null,
  // ...
}

// 结果: 通过映射表查找 "BDO" → 显示 "BDO"
```

### **场景4: 未知银行**
```javascript
// 银行卡信息
{
  bank_id: null,
  bank_name: 'Unknown Bank',
  bank_code: null,
  // ...
}

// 结果: 取前3个字符 → 显示 "UNK"
```

## 🔧 **配置和管理**

### **1. 银行卡表配置**
```sql
-- 直接在银行卡表中设置编码
UPDATE bank_cards 
SET bank_code = 'BDO_SPECIAL' 
WHERE id = 1;
```

### **2. 银行映射表配置**
```sql
-- 在银行映射表中配置代付方式编码（用于取款）
INSERT INTO bank_channel_mappings
(bank_id, payment_channel_id, payout_method, status)
VALUES (1, 1, 201, true);

-- 同时也可以配置代收方式编码（用于充值）
UPDATE bank_channel_mappings
SET payin_method = 101
WHERE bank_id = 1 AND payment_channel_id = 1;
```

### **3. 管理端配置界面**
- 银行卡管理页面可以直接设置`bank_code`
- 银行映射管理页面可以配置`payout_method`（代付方式）和`payin_method`（代收方式）
- 支持启用/禁用映射关系

## 🎯 **最佳实践**

### **推荐配置方式**
1. **标准银行**: 使用银行映射表配置，支持多通道
2. **特殊需求**: 在银行卡表中直接设置特殊编码
3. **新银行**: 先添加到映射表，再配置编码

### **数据一致性**
```javascript
// 确保数据一致性的检查
const validateBankCode = async (bankCard, paymentChannelId) => {
  // 检查直接编码
  if (bankCard.bank_code) {
    return bankCard.bank_code;
  }
  
  // 检查映射编码（取款使用代付方式）
  if (bankCard.bank_id && paymentChannelId) {
    const mapping = await BankChannelMapping.findOne({
      where: { bank_id: bankCard.bank_id, payment_channel_id: paymentChannelId }
    });
    if (mapping) {
      return mapping.payout_method.toString();
    }
  }
  
  // 生成默认编码
  return generateDefaultBankCode(bankCard.bank_name);
};
```

## 📈 **性能考虑**

### **查询优化**
- 优先使用已存储的编码，避免额外查询
- 银行映射查询添加索引
- 缓存常用银行编码映射

### **缓存策略**
```javascript
// 可以考虑的缓存方案
const bankCodeCache = new Map();

const getCachedBankCode = (bankId, channelId) => {
  const key = `${bankId}_${channelId}`;
  return bankCodeCache.get(key);
};
```

## 🔍 **故障排查**

### **常见问题**
1. **编码显示为空**: 检查三个优先级的数据源
2. **编码不正确**: 验证银行映射表配置
3. **编码不统一**: 检查不同通道的映射配置

### **调试方法**
```javascript
// 调试银行编码获取过程
console.log('银行卡信息:', withdrawal.bank_card);
console.log('支付通道ID:', withdrawal.payment_channel_id);
console.log('最终银行编码:', bankCode);
```

## 📝 **总结**

银行编码的获取采用了三级优先级机制：

1. **直接存储** → 最快，最准确
2. **动态映射** → 灵活，支持多通道
3. **名称生成** → 兜底，确保显示

这种设计既保证了性能，又提供了灵活性，同时确保在任何情况下都能显示合适的银行编码。
