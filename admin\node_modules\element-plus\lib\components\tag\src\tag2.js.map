{"version": 3, "file": "tag2.js", "sources": ["../../../../../../packages/components/tag/src/tag.vue"], "sourcesContent": ["<template>\n  <span\n    v-if=\"disableTransitions\"\n    :class=\"containerKls\"\n    :style=\"{ backgroundColor: color }\"\n    @click=\"handleClick\"\n  >\n    <span :class=\"ns.e('content')\">\n      <slot />\n    </span>\n    <el-icon v-if=\"closable\" :class=\"ns.e('close')\" @click.stop=\"handleClose\">\n      <Close />\n    </el-icon>\n  </span>\n  <transition\n    v-else\n    :name=\"`${ns.namespace.value}-zoom-in-center`\"\n    appear\n    @vue:mounted=\"handleVNodeMounted\"\n  >\n    <span\n      :class=\"containerKls\"\n      :style=\"{ backgroundColor: color }\"\n      @click=\"handleClick\"\n    >\n      <span :class=\"ns.e('content')\">\n        <slot />\n      </span>\n      <el-icon v-if=\"closable\" :class=\"ns.e('close')\" @click.stop=\"handleClose\">\n        <Close />\n      </el-icon>\n    </span>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { Close } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from '@element-plus/components/form'\n\nimport { tagEmits, tagProps } from './tag'\nimport type { VNode } from 'vue'\n\ndefineOptions({\n  name: 'ElTag',\n})\nconst props = defineProps(tagProps)\nconst emit = defineEmits(tagEmits)\n\nconst tagSize = useFormSize()\nconst ns = useNamespace('tag')\nconst containerKls = computed(() => {\n  const { type, hit, effect, closable, round } = props\n  return [\n    ns.b(),\n    ns.is('closable', closable),\n    ns.m(type || 'primary'),\n    ns.m(tagSize.value),\n    ns.m(effect),\n    ns.is('hit', hit),\n    ns.is('round', round),\n  ]\n})\n\n// methods\nconst handleClose = (event: MouseEvent) => {\n  emit('close', event)\n}\n\nconst handleClick = (event: MouseEvent) => {\n  emit('click', event)\n}\n\nconst handleVNodeMounted = (vnode: VNode) => {\n  // @ts-ignore\n  if (vnode?.component?.subTree?.component?.bum) {\n    // @ts-ignore\n    vnode.component.subTree.component.bum = null\n  }\n}\n</script>\n"], "names": ["useFormSize", "useNamespace", "computed"], "mappings": ";;;;;;;;;;;;uCA6Cc,CAAA;AAAA,EACZ,IAAM,EAAA,OAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,UAAUA,8BAAY,EAAA,CAAA;AAC5B,IAAM,MAAA,EAAA,GAAKC,mBAAa,KAAK,CAAA,CAAA;AAC7B,IAAM,MAAA,YAAA,GAAeC,aAAS,MAAM;AAClC,MAAA,MAAM,EAAE,IAAM,EAAA,GAAA,EAAK,MAAQ,EAAA,QAAA,EAAU,OAAU,GAAA,KAAA,CAAA;AAC/C,MAAO,OAAA;AAAA,QACL,GAAG,CAAE,EAAA;AAAA,QACL,EAAA,CAAG,EAAG,CAAA,UAAA,EAAY,QAAQ,CAAA;AAAA,QAC1B,EAAA,CAAG,CAAE,CAAA,IAAA,IAAQ,SAAS,CAAA;AAAA,QACtB,EAAA,CAAG,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,QAClB,EAAA,CAAG,EAAE,MAAM,CAAA;AAAA,QACX,EAAA,CAAG,EAAG,CAAA,KAAA,EAAO,GAAG,CAAA;AAAA,QAChB,EAAA,CAAG,EAAG,CAAA,OAAA,EAAS,KAAK,CAAA;AAAA,OACtB,CAAA;AAAA,KACD,CAAA,CAAA;AAGD,IAAM,MAAA,WAAA,GAAc,CAAC,KAAsB,KAAA;AACzC,MAAA,IAAA,CAAK,SAAS,KAAK,CAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAAC,KAAsB,KAAA;AACzC,MAAA,IAAA,CAAK,SAAS,KAAK,CAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAM,MAAA,kBAAA,GAAqB,CAAC,KAAiB,KAAA;AAE3C,MAAA,IAAI,EAAO,EAAA,EAAA,EAAA,EAAA,CAAA;AAET,MAAM,IAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAkB,GAAA,KAAA,IAAA,IAAA,GAAgB,KAAA,CAAA,GAAA,KAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,EAAA;AAAA,QAC1C,KAAA,CAAA,SAAA,CAAA,OAAA,CAAA,SAAA,CAAA,GAAA,GAAA,IAAA,CAAA;AAAA,OACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}