/**
 * 统一刷新管理器
 * 用于管理页面数据刷新，避免重复请求
 */

class RefreshManager {
  constructor() {
    this.lastRefreshTimes = new Map();
    this.refreshInterval = 5000; // 5秒防重复间隔
  }

  /**
   * 检查是否可以刷新
   * @param {string} pageKey - 页面标识
   * @returns {boolean} - 是否可以刷新
   */
  canRefresh(pageKey) {
    const lastTime = this.lastRefreshTimes.get(pageKey) || 0;
    const now = Date.now();
    return (now - lastTime) > this.refreshInterval;
  }

  /**
   * 标记已刷新
   * @param {string} pageKey - 页面标识
   */
  markRefreshed(pageKey) {
    this.lastRefreshTimes.set(pageKey, Date.now());
  }

  /**
   * 强制刷新（忽略时间限制）
   * @param {string} pageKey - 页面标识
   */
  forceRefresh(pageKey) {
    this.lastRefreshTimes.set(pageKey, 0);
  }

  /**
   * 清除所有刷新记录
   */
  clearAll() {
    this.lastRefreshTimes.clear();
  }

  /**
   * 清除指定页面的刷新记录
   * @param {string} pageKey - 页面标识
   */
  clearPage(pageKey) {
    this.lastRefreshTimes.delete(pageKey);
  }

  /**
   * 获取刷新统计信息
   * @returns {Object} - 统计信息
   */
  getStats() {
    return {
      totalPages: this.lastRefreshTimes.size,
      pages: Array.from(this.lastRefreshTimes.keys()),
      refreshInterval: this.refreshInterval
    };
  }

  /**
   * 设置刷新间隔
   * @param {number} interval - 间隔时间（毫秒）
   */
  setRefreshInterval(interval) {
    this.refreshInterval = interval;
  }
}

// 导出单例
export default new RefreshManager();
