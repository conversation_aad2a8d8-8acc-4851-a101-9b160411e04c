const rateLimit = require('express-rate-limit');

// 创建通用限流器
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP在windowMs内最多100次请求
  standardHeaders: true, // 返回标准的RateLimit头部
  legacyHeaders: false, // 禁用X-RateLimit-*头部
  message: {
    code: 429,
    message: '请求过于频繁，请稍后再试',
    data: null
  }
});

// 创建登录限流器
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 每个IP在windowMs内最多20次尝试
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    code: 429,
    message: '尝试次数过多，请15分钟后再试',
    data: null
  }
});

// 创建API限流器
const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 60, // 每个IP在windowMs内最多60次请求
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    code: 429,
    message: 'API请求过于频繁，请稍后再试',
    data: null
  }
});

module.exports = {
  generalLimiter,
  loginLimiter,
  apiLimiter
};
