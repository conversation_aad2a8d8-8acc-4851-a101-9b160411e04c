const express = require('express');
const userAuthController = require('../controllers/userAuthController');
const inviteController = require('../controllers/inviteController');
const { verifyUserToken } = require('../middlewares/authMiddleware');
// 禁用 rateLimiter 中间件，因为它可能导致错误
// const { loginLimiter: rateLimiter } = require('../middlewares/rateLimitMiddleware');
const { userRegisterValidation, userLoginValidation } = require('../middlewares/validationMiddleware');

const router = express.Router();

// 公开路由
// 完全禁用速率限制
router.post('/register', userRegisterValidation, userAuthController.register);
router.post('/login', userLoginValidation, userAuthController.login);
router.get('/invite-code/:code', inviteController.validateInviteCode);

// 需要认证的路由
router.use(verifyUserToken);

router.post('/logout', userAuthController.logout);
router.get('/profile', userAuthController.getProfile);
router.put('/password', userAuthController.updatePassword);

// 邀请相关路由
router.post('/invite-code', inviteController.createInviteCode);
router.get('/invite-code', inviteController.getInviteCode);
router.get('/invitees', inviteController.getInvitees);

module.exports = router;
