/**
 * 收益系统（方案3.1简化版）
 * 负责初始化和管理整个收益系统，包含自动补发功能
 */
const { Investment, Project } = require('../models');
const profitPoller = require('./profitPoller');
const logger = require('../utils/logger');
const redisClient = require('../utils/redisClient');

/**
 * 初始化所有活跃投资的收益任务（方案3.1简化版）
 */
const initializeAllInvestmentTasks = async () => {
  try {
    logger.info('开始初始化所有活跃投资的收益任务...');

    // 获取所有活跃的投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      },
      include: [
        {
          model: Project,
          as: 'project'
        }
      ]
    });

    logger.info(`找到 ${activeInvestments.length} 条活跃投资记录`);

    let scheduledCount = 0;
    let compensatedCount = 0;

    // 逐个处理投资记录
    for (const investment of activeInvestments) {
      try {
        // 使用简化的Redis重建逻辑
        const result = await simpleRedisRebuild(investment, investment.project);

        if (result.success) {
          scheduledCount++;
          compensatedCount += result.compensated || 0;
        }
      } catch (error) {
        logger.error(`初始化投资ID ${investment.id} 的收益任务失败:`, error);
      }
    }

    logger.info(`收益任务初始化完成，安排 ${scheduledCount} 条任务，补发 ${compensatedCount} 笔收益`);
    return {
      success: true,
      scheduled: scheduledCount,
      compensated: compensatedCount
    };
  } catch (error) {
    logger.error('初始化投资收益任务失败:', error);
    return {
      success: false,
      message: '初始化失败: ' + error.message
    };
  }
};

/**
 * 简化的Redis重建逻辑（方案3.1）
 */
const simpleRedisRebuild = async (investment, project) => {
  try {
    const now = new Date(); // UTC时间
    const profitCycleMs = project.profit_time * 60 * 60 * 1000;

    // 计算从最后收益时间到现在应该有多少笔收益（UTC时间计算）
    const lastProfitTime = investment.last_profit_time ?
      new Date(investment.last_profit_time) : // UTC时间
      new Date(investment.start_time || investment.created_at); // UTC时间

    const timeDiff = now.getTime() - lastProfitTime.getTime();
    const expectedProfitCount = Math.floor(timeDiff / profitCycleMs);

    let compensatedCount = 0;

    // 如果有遗漏收益，自动补发
    if (expectedProfitCount > 0) {
      compensatedCount = await autoCompensateMissedProfits(
        investment,
        lastProfitTime,
        expectedProfitCount,
        profitCycleMs
      );
    }

    // 安排下次正常收益（UTC时间）
    const nextProfitTime = new Date(lastProfitTime.getTime() + (expectedProfitCount + 1) * profitCycleMs);
    if (nextProfitTime > now) {
      await redisClient.addProfitTask(investment.id, nextProfitTime);
    }

    return {
      success: true,
      compensated: compensatedCount,
      nextProfitTime: nextProfitTime
    };

  } catch (error) {
    logger.error(`投资ID ${investment.id} Redis重建失败:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * 自动补发遗漏的收益
 */
const autoCompensateMissedProfits = async (investment, lastProfitTime, missedCount, cycleMs) => {
  let compensatedCount = 0;

  for (let i = 1; i <= missedCount; i++) {
    try {
      // 计算理论收益时间（UTC时间）
      const theoreticalTime = new Date(lastProfitTime.getTime() + i * cycleMs);

      // 检查是否已存在
      const { InvestmentProfit } = require('../models');
      const existing = await InvestmentProfit.findOne({
        where: {
          investment_id: investment.id,
          profit_time: theoreticalTime
        }
      });

      if (!existing) {
        // 补发收益
        await createProfitRecord(investment, theoreticalTime);
        compensatedCount++;
        logger.info(`自动补发成功：投资${investment.id}，时间(UTC)${theoreticalTime.toISOString()}`);
      }

    } catch (error) {
      logger.error(`补发失败：投资${investment.id}，第${i}笔:`, error);
      // 单笔失败不影响其他补发
    }
  }

  return compensatedCount;
};

/**
 * 统一的收益创建函数
 */
const createProfitRecord = async (investment, profitTime) => {
  const { InvestmentProfit, User, Investment: InvestmentModel } = require('../models');
  const sequelize = require('../config/database');
  const transaction = await sequelize.transaction();

  try {
    // 创建收益记录
    const profitRecord = await InvestmentProfit.create({
      investment_id: investment.id,
      user_id: investment.user_id,
      amount: investment.daily_profit || investment.amount * (investment.profit_rate / 100),
      profit_time: profitTime,
      status: 'paid'
    }, { transaction });

    // 更新用户余额（使用balance字段）
    await User.increment('balance', {
      by: profitRecord.amount,
      where: { id: investment.user_id },
      transaction
    });

    // 更新投资的最后收益时间
    await InvestmentModel.update(
      { last_profit_time: profitTime },
      { where: { id: investment.id }, transaction }
    );

    await transaction.commit();
    return profitRecord;

  } catch (error) {
    await transaction.rollback();

    // 如果是重复记录，静默跳过
    if (error.name === 'SequelizeUniqueConstraintError') {
      logger.info(`收益已存在，跳过：投资${investment.id}，时间(UTC)${profitTime.toISOString()}`);
      return null;
    }

    throw error;
  }
};

/**
 * 检查Redis是否可用
 * @returns {Promise<boolean>} - Redis是否可用
 */
async function isRedisAvailable() {
  try {
    await redisClient.client.ping();
    return true;
  } catch (error) {
    logger.warn('Redis不可用:', error.message);
    return false;
  }
}

/**
 * 启动收益系统（方案3.1简化版）
 */
const startProfitSystem = async () => {
  try {
    logger.info('开始启动收益系统（方案3.1）...');

    // 检查Redis是否可用
    const redisAvailable = await isRedisAvailable();

    if (!redisAvailable) {
      logger.error('Redis不可用，收益系统无法启动');
      return {
        success: false,
        message: 'Redis不可用，收益系统无法启动',
        redis_available: false
      };
    }

    // 初始化所有活跃投资的收益任务（包含自动补发）
    const initResult = await initializeAllInvestmentTasks();

    // 启动轮询器
    profitPoller.startPoller();

    logger.info(`收益系统启动成功，补发了 ${initResult.compensated || 0} 笔遗漏收益`);

    return {
      success: true,
      message: '收益系统启动成功',
      redis_available: redisAvailable,
      compensated_profits: initResult.compensated || 0
    };
  } catch (error) {
    logger.error('启动收益系统失败:', error);
    return {
      success: false,
      message: '启动收益系统失败: ' + error.message
    };
  }
};

/**
 * 停止收益系统
 */
const stopProfitSystem = () => {
  try {
    // 停止轮询器
    profitPoller.stopPoller();

    logger.info('收益系统已停止');

    return {
      success: true,
      message: '收益系统已停止'
    };
  } catch (error) {
    logger.error('停止收益系统失败:', error);
    return {
      success: false,
      message: '停止收益系统失败: ' + error.message
    };
  }
};

module.exports = {
  initializeAllInvestmentTasks,
  startProfitSystem,
  stopProfitSystem,
  simpleRedisRebuild,
  createProfitRecord
};
