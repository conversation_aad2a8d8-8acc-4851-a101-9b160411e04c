/**
 * 移动端支付通道控制器
 */
const { PaymentChannel } = require('../models');
const { Op } = require('sequelize');

/**
 * 获取可用的支付通道列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 响应对象
 */
exports.getAvailablePaymentChannels = async (req, res) => {
  try {
    // 获取启用的支付通道
    const paymentChannels = await PaymentChannel.findAll({
      where: {
        status: true,
        deposit_enabled: true
      },
      order: [
        ['weight', 'DESC'],
        ['id', 'ASC']
      ],
      attributes: [
        'id',
        'name',
        'code',
        'icon',
        'min_deposit_amount',
        'max_deposit_amount',
        'deposit_enabled',
        'status',
        'is_default'
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: paymentChannels
    });
  } catch (error) {
    console.error('获取支付通道列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
