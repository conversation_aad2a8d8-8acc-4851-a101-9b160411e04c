/**
 * 添加字段到充值订单表
 */
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

async function up() {
  try {
    // 检查表是否存在
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'deposits'");
    if (tables.length === 0) {
      console.log('deposits 表不存在，跳过迁移');
      return;
    }

    // 检查字段是否存在
    const [columns] = await sequelize.query("SHOW COLUMNS FROM deposits");
    const columnNames = columns.map(column => column.Field);

    // 添加 payment_channel_id 字段
    if (!columnNames.includes('payment_channel_id')) {
      await sequelize.query(`
        ALTER TABLE deposits
        ADD COLUMN payment_channel_id INT NULL COMMENT '支付通道ID'
      `);
      console.log('添加 payment_channel_id 字段成功');
    }

    // 添加 bank_card_id 字段
    if (!columnNames.includes('bank_card_id')) {
      await sequelize.query(`
        ALTER TABLE deposits
        ADD COLUMN bank_card_id INT NULL COMMENT '银行卡ID'
      `);
      console.log('添加 bank_card_id 字段成功');
    }

    console.log('迁移完成');
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  }
}

async function down() {
  try {
    // 检查表是否存在
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'deposits'");
    if (tables.length === 0) {
      console.log('deposits 表不存在，跳过回滚');
      return;
    }

    // 删除字段
    await sequelize.query(`
      ALTER TABLE deposits
      DROP COLUMN IF EXISTS payment_channel_id,
      DROP COLUMN IF EXISTS bank_card_id
    `);

    console.log('回滚完成');
  } catch (error) {
    console.error('回滚失败:', error);
    throw error;
  }
}

module.exports = { up, down };
