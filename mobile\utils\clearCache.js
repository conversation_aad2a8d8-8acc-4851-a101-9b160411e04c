/**
 * 清除缓存工具函数
 * 用于清除前端缓存，确保获取最新的数据
 */

/**
 * 清除所有缓存
 */
export function clearAllCache() {
  try {
    // 清除本地存储
    uni.clearStorageSync();

    // 清除页面缓存
    const pages = getCurrentPages();
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      if (currentPage && currentPage.$vm) {
        // 重新加载当前页面数据
        if (typeof currentPage.$vm.fetchTransactions === 'function') {
          currentPage.$vm.fetchTransactions(true);
        }
      }
    }

    // 显示成功提示
    uni.showToast({
      title: '缓存已清除',
      icon: 'success',
      duration: 2000
    });

    return true;
  } catch (error) {
    // 显示错误提示
    uni.showToast({
      title: '清除缓存失败',
      icon: 'none',
      duration: 2000
    });

    return false;
  }
}

/**
 * 清除特定页面的缓存
 * @param {string} pagePath - 页面路径
 */
export function clearPageCache(pagePath) {
  try {
    // 获取当前页面栈
    const pages = getCurrentPages();
    if (pages && pages.length > 0) {
      // 查找指定页面
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        if (page.route === pagePath) {
          // 重新加载页面数据
          if (page.$vm && typeof page.$vm.fetchTransactions === 'function') {
            page.$vm.fetchTransactions(true);
          }
          break;
        }
      }
    }

    return true;
  } catch (error) {
    return false;
  }
}

export default {
  clearAllCache,
  clearPageCache
};
