/**
 * APP下载控制器
 */
const QRCode = require('qrcode');
const { SystemParam } = require('../models');

/**
 * 生成APP下载二维码
 */
exports.generateQRCode = async (req, res) => {
  try {
    // 获取网站域名
    const domainParam = await SystemParam.findOne({
      where: { param_key: '[site.domain]' }
    });

    let baseUrl;
    if (domainParam && domainParam.param_value && domainParam.param_value.trim() !== '') {
      // 使用系统参数中的域名
      baseUrl = domainParam.param_value.trim();
      // 确保域名包含协议
      if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
        baseUrl = `https://${baseUrl}`;
      }
    } else {
      // 使用当前请求的域名
      baseUrl = `${req.protocol}://${req.get('host')}`;
    }

    // 构建下载链接
    const downloadUrl = `${baseUrl}/download`;

    // 生成二维码
    const qrCodeDataURL = await QRCode.toDataURL(downloadUrl, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: 400
    });

    res.status(200).json({
      code: 200,
      message: '生成成功',
      data: {
        qrCodeDataURL,
        downloadUrl,
        baseUrl
      }
    });
  } catch (error) {
    console.error('生成二维码失败:', error);
    res.status(500).json({
      code: 500,
      message: '生成二维码失败',
      data: null
    });
  }
};

/**
 * 获取APP下载信息
 */
exports.getDownloadInfo = async (req, res) => {
  try {
    // 获取网站域名
    const domainParam = await SystemParam.findOne({
      where: { param_key: '[site.domain]' }
    });

    let baseUrl;
    if (domainParam && domainParam.param_value && domainParam.param_value.trim() !== '') {
      // 使用系统参数中的域名
      baseUrl = domainParam.param_value.trim();
      // 确保域名包含协议
      if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
        baseUrl = `https://${baseUrl}`;
      }
    } else {
      // 使用当前请求的域名
      baseUrl = `${req.protocol}://${req.get('host')}`;
    }

    // 构建下载链接
    const downloadUrl = `${baseUrl}/downloads/app.apk`;

    // 模拟APP信息（后续可以从系统参数中获取）
    const appInfo = {
      version: '1.2.0',
      size: '10MB',
      updateTime: new Date().toISOString().split('T')[0],
      downloadUrl,
      qrCodeUrl: `${req.protocol}://${req.get('host')}/api/app/qrcode`
    };

    res.status(200).json({
      code: 200,
      message: '获取成功',
      data: appInfo
    });
  } catch (error) {
    console.error('获取APP下载信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取APP下载信息失败',
      data: null
    });
  }
};

/**
 * APP下载重定向
 */
exports.downloadRedirect = async (req, res) => {
  try {
    // 获取网站域名
    const domainParam = await SystemParam.findOne({
      where: { param_key: '[site.domain]' }
    });

    let baseUrl;
    if (domainParam && domainParam.param_value && domainParam.param_value.trim() !== '') {
      // 使用系统参数中的域名
      baseUrl = domainParam.param_value.trim();
      // 确保域名包含协议
      if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
        baseUrl = `https://${baseUrl}`;
      }
    } else {
      // 使用当前请求的域名
      baseUrl = `${req.protocol}://${req.get('host')}`;
    }

    // 构建实际的APK下载链接
    const apkUrl = `${baseUrl}/downloads/app.apk`;

    // 重定向到实际的下载链接
    res.redirect(302, apkUrl);
  } catch (error) {
    console.error('下载重定向失败:', error);
    res.status(500).json({
      code: 500,
      message: '下载服务暂时不可用',
      data: null
    });
  }
};
