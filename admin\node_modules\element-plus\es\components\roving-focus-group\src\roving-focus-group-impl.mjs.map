{"version": 3, "file": "roving-focus-group-impl.mjs", "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-group-impl.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  inject,\n  provide,\n  readonly,\n  ref,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport {\n  ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n  rovingFocusGroupProps,\n} from './roving-focus-group'\nimport { ROVING_FOCUS_GROUP_INJECTION_KEY } from './tokens'\nimport { focusFirst } from './utils'\n\nimport type { StyleValue } from 'vue'\n\nconst CURRENT_TAB_ID_CHANGE_EVT = 'currentTabIdChange'\n\nconst ENTRY_FOCUS_EVT = 'rovingFocusGroup.entryFocus'\nconst EVT_OPTS: EventInit = { bubbles: false, cancelable: true }\nexport default defineComponent({\n  name: 'ElRovingFocusGroupImpl',\n  inheritAttrs: false,\n  props: rovingFocusGroupProps,\n  emits: [CURRENT_TAB_ID_CHANGE_EVT, 'entryFocus'],\n  setup(props, { emit }) {\n    const currentTabbedId = ref<string | null>(\n      (props.currentTabId || props.defaultCurrentTabId) ?? null\n    )\n    const isBackingOut = ref(false)\n    const isClickFocus = ref(false)\n    const rovingFocusGroupRef = ref<HTMLElement>()\n    const { getItems } = inject(\n      ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n      undefined\n    )!\n    const rovingFocusGroupRootStyle = computed(() => {\n      // casting to any for fix compiler error since HTMLElement.StyleValue does not\n      // support CSSProperties\n      return [\n        {\n          outline: 'none',\n        },\n        props.style as StyleValue,\n      ] as any\n    })\n\n    const onItemFocus = (tabbedId: string) => {\n      emit(CURRENT_TAB_ID_CHANGE_EVT, tabbedId)\n    }\n\n    const onItemShiftTab = () => {\n      isBackingOut.value = true\n    }\n\n    const onMousedown = composeEventHandlers(\n      (e: Event) => {\n        props.onMousedown?.(e)\n      },\n      () => {\n        isClickFocus.value = true\n      }\n    )\n\n    const onFocus = composeEventHandlers(\n      (e: FocusEvent) => {\n        props.onFocus?.(e)\n      },\n      (e) => {\n        const isKeyboardFocus = !unref(isClickFocus)\n        const { target, currentTarget } = e\n        if (\n          target === currentTarget &&\n          isKeyboardFocus &&\n          !unref(isBackingOut)\n        ) {\n          const entryFocusEvt = new Event(ENTRY_FOCUS_EVT, EVT_OPTS)\n          currentTarget?.dispatchEvent(entryFocusEvt)\n\n          if (!entryFocusEvt.defaultPrevented) {\n            const items = getItems<{\n              id: string\n              focusable: boolean\n              active: boolean\n            }>().filter((item) => item.focusable)\n            const activeItem = items.find((item) => item.active)\n            const currentItem = items.find(\n              (item) => item.id === unref(currentTabbedId)\n            )\n            const candidates = [activeItem!, currentItem!, ...items].filter(\n              Boolean\n            )\n            const candidateNodes = candidates.map((item) => item.ref!)\n            focusFirst(candidateNodes)\n          }\n        }\n\n        isClickFocus.value = false\n      }\n    )\n\n    const onBlur = composeEventHandlers(\n      (e: Event) => {\n        props.onBlur?.(e)\n      },\n      () => {\n        isBackingOut.value = false\n      }\n    )\n\n    const handleEntryFocus = (...args: any[]) => {\n      emit('entryFocus', ...args)\n    }\n\n    provide(ROVING_FOCUS_GROUP_INJECTION_KEY, {\n      currentTabbedId: readonly(currentTabbedId),\n      loop: toRef(props, 'loop'),\n      tabIndex: computed(() => {\n        return unref(isBackingOut) ? -1 : 0\n      }),\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      orientation: toRef(props, 'orientation'),\n      dir: toRef(props, 'dir'),\n      onItemFocus,\n      onItemShiftTab,\n      onBlur,\n      onFocus,\n      onMousedown,\n    })\n\n    watch(\n      () => props.currentTabId,\n      (val) => {\n        currentTabbedId.value = val ?? null\n      }\n    )\n\n    useEventListener(rovingFocusGroupRef, ENTRY_FOCUS_EVT, handleEntryFocus)\n  },\n})\n</script>\n"], "names": ["ROVING_FOCUS_COLLECTION_INJECTION_KEY", "_renderSlot"], "mappings": ";;;;;;;;AA2BA,MAAM,yBAA4B,GAAA,oBAAA,CAAA;AAElC,MAAM,eAAkB,GAAA,6BAAA,CAAA;AACxB,MAAM,QAAsB,GAAA,EAAE,OAAS,EAAA,KAAA,EAAO,YAAY,IAAK,EAAA,CAAA;AAC/D,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,wBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAAA,EACd,KAAO,EAAA,qBAAA;AAAA,EACP,KAAA,EAAO,CAAC,yBAAA,EAA2B,YAAY,CAAA;AAAA,EAC/C,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAA,IAAA,EAAM,CAAkB;AAAA,IACrB,MAAA,eAAsB,GAAA,GAAA,CAAA,CAAA,EAAM,GAAwB,KAAA,CAAA,YAAA,IAAA,KAAA,CAAA,mBAAA,KAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AAAA,IACvD,MAAA,YAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACA,IAAM,MAAA,YAAA,GAAe,IAAI,KAAK,CAAA,CAAA;AAC9B,IAAM,MAAA,mBAAmB,GAAK,GAAA,EAAA,CAAA;AAC9B,IAAA,MAAM,sBAAsBA,wBAAiB,EAAA,KAAA,CAAA,CAAA,CAAA;AAC7C,IAAM,MAAA,yBAAe,GAAA,QAAA,CAAA,MAAA;AAAA,MACnB,OAAA;AAAA,QACA;AAAA,UACF,OAAA,EAAA,MAAA;AACA,SAAM;AAGJ,QAAO,KAAA,CAAA,KAAA;AAAA,OACL,CAAA;AAAA,KAAA,CAAA,CAAA;AACW,IACX,MAAA,WAAA,GAAA,CAAA,QAAA,KAAA;AAAA,MAAA,IACM,CAAA,yBAAA,EAAA,QAAA,CAAA,CAAA;AAAA,KACR,CAAA;AAAA,IACF,MAAC,cAAA,GAAA,MAAA;AAED,MAAM,YAAA,CAAA,KAAc,GAAsB,IAAA,CAAA;AACxC,KAAA,CAAA;AAAwC,IAC1C,MAAA,WAAA,GAAA,oBAAA,CAAA,CAAA,CAAA,KAAA;AAEA,MAAA,IAAM;AACJ,MAAA,CAAA,GAAA,GAAA,KAAA,CAAa,WAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KACvB,EAAA,MAAA;AAEA,MAAA,YAAoB,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,KAAA,CAClB,CAAC;AACC,IAAA,MAAA,8BAAqB,CAAA,CAAA,CAAA,KAAA;AAAA,MACvB,IAAA,GAAA,CAAA;AAAA,MACA,CAAM,GAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AACJ,KAAA,EAAA,CAAA,CAAA,KAAA;AAAqB,MACvB,MAAA,eAAA,GAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA;AAAA,MACF,MAAA,EAAA,MAAA,EAAA,aAAA,EAAA,GAAA,CAAA,CAAA;AAEA,MAAA,IAAM,MAAU,KAAA,aAAA,IAAA,eAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA,EAAA;AAAA,QACK,MAAA,aAAA,GAAA,IAAA,KAAA,CAAA,eAAA,EAAA,QAAA,CAAA,CAAA;AACjB,QAAA,iBAAiB,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,aAAA,CAAA,aAAA,CAAA,CAAA;AAAA,QACnB,IAAA,CAAA,aAAA,CAAA,gBAAA,EAAA;AAAA,UACO,MAAA,KAAA,GAAA,QAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AACL,UAAM,MAAA,UAAA,GAAA,KAAmB,CAAA,IAAM,CAAY,CAAA,IAAA,KAAA,IAAA,CAAA,MAAA,CAAA,CAAA;AAC3C,UAAM,MAAE,WAAQ,GAAA,KAAA,CAAA,IAAkB,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,EAAA,KAAA,KAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAClC,UAAA,gBAEE,GAAA,CAAA,UAAA,EAAA,WAAA,EAAA,GAAA,KACC,CAAA,CAAA,cACD,CAAA,CAAA;AACA,UAAA,MAAM,cAAgB,GAAA,UAAU,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAyB,CAAA,GAAA,CAAA,CAAA;AACzD,UAAA,UAAA,CAAA;AAEA,SAAI;AACF,OAAA;AAKA,MAAA,YAAM,cAAmB,CAAA;AACzB,KAAA,CAAA,CAAA;AAA0B,IAAA,MAAA,MACvB,GAAA,uBAA2B,KAAe;AAAA,MAC7C,IAAA,GAAA,CAAA;AACA,MAAA,CAAA,GAAA,GAAA,KAAM,gBAAc,GAAA,KAAA,CAAA,GAA2B,GAAA,CAAA,IAAA,CAAA,KAAG,KAAK;AAAE,KACvD,EAAA,MAAA;AAAA,MACF,YAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,gBAAyB,GAAA,CAAA,GAAA,IAAA,KAAA;AAAA,MAC3B,IAAA,CAAA,YAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,OAAA,CAAA,gCAAqB,EAAA;AAAA,MACvB,eAAA,EAAA,QAAA,CAAA,eAAA,CAAA;AAAA,MACF,IAAA,EAAA,KAAA,CAAA,KAAA,EAAA,MAAA,CAAA;AAEA,MAAA,QAAe,EAAA,QAAA,CAAA,MAAA;AAAA,QACC,OAAA,KAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AACZ,OAAA,CAAA;AAAgB,MAClB,mBAAA;AAAA,MACA,yBAAM;AACJ,MAAA,WAAA,EAAA,KAAqB,CAAA,KAAA,EAAA,aAAA,CAAA;AAAA,MACvB,GAAA,EAAA,KAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AAAA,MACF,WAAA;AAEA,MAAM,cAAA;AACJ,MAAK,MAAA;AAAqB,MAC5B,OAAA;AAEA,MAAA,WAA0C;AAAA,KACxC,CAAA,CAAA;AAAyC,IACzC,KAAA,CAAA,MAAY,KAAA,CAAA,YAAa,EAAA,CAAA,GAAA,KAAA;AAAA,MACzB,qBAAyB,GAAA,GAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA;AACvB,KAAO,CAAA,CAAA;AAA2B,IAAA,gBACnC,CAAA,mBAAA,EAAA,eAAA,EAAA,gBAAA,CAAA,CAAA;AAAA,GACD;AAAA,CACA,CAAA,CAAA;AACuC,SACvC,WAAW,CAAA,IAAY,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACvB,OAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,CACA;AACA,6BACA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,6BAAA,CAAA,CAAA,CAAA;;;;"}