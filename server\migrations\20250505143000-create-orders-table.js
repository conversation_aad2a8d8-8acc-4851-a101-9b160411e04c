'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('orders', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      order_no: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      project_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
      },
      amount: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      expected_return: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
        comment: '预期收益',
      },
      actual_return: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
        comment: '实际收益',
      },
      status: {
        type: Sequelize.ENUM('pending', 'paid', 'completed', 'cancelled', 'refunded'),
        defaultValue: 'pending',
        allowNull: false,
      },
      payment_method: {
        type: Sequelize.ENUM('balance', 'alipay', 'wechat', 'bank'),
        allowNull: true,
      },
      payment_time: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      start_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '投资开始时间',
      },
      end_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '投资结束时间',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('orders');
  }
};
