'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

const composeEventHandlers = (theirs<PERSON>and<PERSON>, oursHandler, { checkForDefaultPrevented = true } = {}) => {
  const handleEvent = (event) => {
    const shouldPrevent = theirsHandler == null ? void 0 : theirsHandler(event);
    if (checkForDefaultPrevented === false || !shouldPrevent) {
      return oursHandler == null ? void 0 : oursHandler(event);
    }
  };
  return handleEvent;
};
const whenMouse = (handler) => {
  return (e) => e.pointerType === "mouse" ? handler(e) : void 0;
};

exports.composeEventHandlers = composeEventHandlers;
exports.whenMouse = whenMouse;
//# sourceMappingURL=event.js.map
