/**
 * 修复佣金记录中的from_user_id字段
 *
 * 这个脚本用于修复历史佣金记录中的from_user_id字段，
 * 确保所有佣金记录的from_user_id字段都指向最初产生收益的用户，
 * 而不是中间的上级用户。
 */

// 导入必要的模块
const { Commission, UserRelation, Investment, InvestmentProfit } = require('../models');
const sequelize = require('../config/database');
const { Op } = require('sequelize');

// 主函数
async function fixCommissionFromUser() {
  console.log('开始修复佣金记录中的from_user_id字段...');

  // 开启事务
  const transaction = await sequelize.transaction();

  try {
    // 获取所有二级和三级佣金记录
    const commissions = await Commission.findAll({
      where: {
        level: {
          [Op.in]: [2, 3] // 只处理二级和三级佣金
        },
        type: {
          [Op.like]: '%收益返佣%' // 只处理收益返佣
        }
      },
      include: [
        {
          model: Investment,
          as: 'investment',
          required: true
        }
      ],
      transaction
    });

    console.log(`找到 ${commissions.length} 条需要修复的佣金记录`);

    // 用于存储已处理的投资ID和对应的用户ID
    const investmentUserMap = new Map();

    // 处理每条佣金记录
    for (const commission of commissions) {
      const investmentId = commission.investment_id;

      // 如果已经处理过这个投资ID，直接使用缓存的用户ID
      if (investmentUserMap.has(investmentId)) {
        const originalUserId = investmentUserMap.get(investmentId);
        await updateCommission(commission, originalUserId, transaction);
        continue;
      }

      // 获取投资记录
      const investment = await Investment.findByPk(investmentId, { transaction });
      if (!investment) {
        console.log(`警告: 投资ID ${investmentId} 不存在，跳过佣金记录 ${commission.id}`);
        continue;
      }

      // 获取投资的用户ID（最初产生收益的用户）
      const originalUserId = investment.user_id;

      // 缓存投资ID和用户ID的映射
      investmentUserMap.set(investmentId, originalUserId);

      // 更新佣金记录
      await updateCommission(commission, originalUserId, transaction);
    }

    // 提交事务
    await transaction.commit();

    console.log('佣金记录修复完成！');
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('修复佣金记录时发生错误:', error);
    throw error;
  }
}

// 更新佣金记录的from_user_id字段
async function updateCommission(commission, originalUserId, transaction) {
  const oldFromUserId = commission.from_user_id;

  // 如果from_user_id已经是正确的，跳过更新
  if (oldFromUserId === originalUserId) {
    console.log(`佣金记录 ${commission.id} 的from_user_id已经是正确的，跳过`);
    return;
  }

  // 更新佣金记录
  commission.from_user_id = originalUserId;
  await commission.save({ transaction });

  console.log(`已更新佣金记录 ${commission.id}: from_user_id 从 ${oldFromUserId} 更新为 ${originalUserId}`);
}

// 执行主函数
fixCommissionFromUser()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
