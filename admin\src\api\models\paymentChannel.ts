/**
 * 支付通道数据模型
 */
export interface PaymentChannel {
  id: number
  name: string
  code: string
  countryCode: string
  depositEnabled: number
  withdrawEnabled: number
  isDefault: number
  weight: number
  // 余额字段已移除
  config?: string
  createTime: string
  updateTime: string

  // UI状态属性（不会发送到服务器）
  depositLoading?: boolean
  withdrawLoading?: boolean
  defaultLoading?: boolean
  weightLoading?: boolean
}

/**
 * 支付通道创建/更新请求参数
 */
export interface PaymentChannelRequest {
  id?: number
  name: string
  code: string
  countryCode?: string
  depositEnabled: number
  withdrawEnabled: number
  isDefault: number
  weight: number
  // 余额字段已移除
  config?: string
}

/**
 * 支付通道查询参数
 */
export interface PaymentChannelQuery {
  id?: string
  name?: string
  code?: string
  depositEnabled?: number | null
  withdrawEnabled?: number | null
  isDefault?: number | null
  weightMin?: number | null
  weightMax?: number | null
  // 余额范围字段已移除
  createTimeStart?: string
  createTimeEnd?: string
  page?: number
  limit?: number
}

/**
 * 支付通道响应数据
 */
export interface PaymentChannelResponse {
  code: number
  message: string
  data: {
    total: number
    items: PaymentChannel[]
  }
}
