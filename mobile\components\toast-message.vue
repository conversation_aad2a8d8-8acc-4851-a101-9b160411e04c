<template>
  <view v-if="visible" class="toast-overlay" @click="hide">
    <view class="toast-container" :class="[`toast-${type}`, animationClass]">
      <view class="toast-icon">
        <!-- 成功图标 -->
        <view v-if="type === 'success'" class="icon-success">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>
          </svg>
        </view>
        
        <!-- 错误图标 -->
        <view v-else-if="type === 'error'" class="icon-error">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </view>
        
        <!-- 警告图标 -->
        <view v-else-if="type === 'warning'" class="icon-warning">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" fill="currentColor"/>
          </svg>
        </view>
        
        <!-- 信息图标 -->
        <view v-else-if="type === 'info'" class="icon-info">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" fill="currentColor"/>
          </svg>
        </view>
        
        <!-- 余额不足专用图标 -->
        <view v-else-if="type === 'insufficient'" class="icon-insufficient">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z" fill="currentColor"/>
          </svg>
        </view>
      </view>
      
      <view class="toast-content">
        <text class="toast-title" v-if="title">{{ title }}</text>
        <text class="toast-message">{{ message }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ToastMessage',
  data() {
    return {
      visible: false,
      type: 'info', // success, error, warning, info, insufficient
      title: '',
      message: '',
      duration: 5000,
      animationClass: '',
      timer: null
    }
  },
  methods: {
    show(options = {}) {
      const {
        type = 'info',
        title = '',
        message = '',
        duration = 5000
      } = options;
      
      this.type = type;
      this.title = title;
      this.message = message;
      this.duration = duration;
      this.visible = true;
      this.animationClass = 'toast-enter';
      
      // 清除之前的定时器
      if (this.timer) {
        clearTimeout(this.timer);
      }
      
      // 设置自动隐藏
      if (duration > 0) {
        this.timer = setTimeout(() => {
          this.hide();
        }, duration);
      }
    },
    
    hide() {
      this.animationClass = 'toast-leave';
      setTimeout(() => {
        this.visible = false;
        this.animationClass = '';
      }, 300);
      
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    }
  },
  
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }
}
</script>

<style lang="scss" scoped>
.toast-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
}

.toast-container {
  max-width: 600rpx;
  min-width: 400rpx;
  margin: 0 40rpx;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: none;
}

.toast-icon {
  flex-shrink: 0;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.toast-title {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.2;
}

.toast-message {
  font-size: 28rpx;
  line-height: 1.4;
  opacity: 0.9;
}

/* 成功样式 */
.toast-success {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.95), rgba(56, 142, 60, 0.95));
  color: white;
  border: 2px solid rgba(76, 175, 80, 0.8);

  .toast-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

/* 错误样式 */
.toast-error {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.95), rgba(211, 47, 47, 0.95));
  color: white;
  border: 2px solid rgba(244, 67, 54, 0.8);

  .toast-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

/* 警告样式 */
.toast-warning {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.95), rgba(245, 124, 0, 0.95));
  color: white;
  border: 2px solid rgba(255, 152, 0, 0.8);

  .toast-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

/* 信息样式 */
.toast-info {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.95), rgba(25, 118, 210, 0.95));
  color: white;
  border: 2px solid rgba(33, 150, 243, 0.8);

  .toast-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

/* 余额不足专用样式 */
.toast-insufficient {
  background: linear-gradient(135deg, rgba(255, 87, 34, 0.95), rgba(230, 74, 25, 0.95));
  color: white;
  border: 2px solid rgba(255, 87, 34, 0.8);

  .toast-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

/* 动画效果 */
.toast-enter {
  animation: toastEnter 0.3s ease-out;
}

.toast-leave {
  animation: toastLeave 0.3s ease-in;
}

@keyframes toastEnter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes toastLeave {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateY(-20rpx);
  }
}

/* PC端响应式 */
@media screen and (min-width: 768px) {
  .toast-container {
    max-width: 400px;
    min-width: 300px;
  }
}
</style>
