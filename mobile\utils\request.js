/**
 * 请求工具类
 * 封装uni.request，添加拦截器，统一处理请求和响应
 */

// API基础URL
const BASE_URL = '/api';

// 请求拦截器
const requestInterceptor = (config) => {
  // 获取token
  const token = uni.getStorageSync('userToken');

  // 如果有token，添加到请求头
  if (token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${token}`
    };
  }

  // 添加内容类型
  config.header = {
    ...config.header,
    'Content-Type': 'application/json'
  };

  // 添加基础URL
  config.url = BASE_URL + config.url;

  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  // 获取响应数据
  const { statusCode, data } = response;

  // 如果状态码不是2xx，抛出错误
  if (statusCode < 200 || statusCode >= 300) {
    // 处理401未授权错误
    if (statusCode === 401) {

      // 清除本地存储的token和用户信息
      uni.removeStorageSync('userToken');
      uni.removeStorageSync('userInfo');

      // 显示错误提示
      uni.showToast({
        title: 'Login expired, please login again',
        icon: 'none',
        duration: 2000
      });

      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/index/index'
        });
      }, 2000);

      return Promise.reject(new Error('Login expired, please login again'));
    }

    // 处理404错误
    if (statusCode === 404) {
      const errorMessage = 'Resource not found, please check if server is running or proxy configuration is correct';

      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });

      return Promise.reject(new Error(errorMessage));
    }

    // 处理400错误（输入验证失败）
    if (statusCode === 400) {
      let errorMessage = 'Please check your input and try again';

      // 如果是输入验证失败，提供更友好的提示
      if (data?.message && data.message.includes('validation')) {
        errorMessage = 'Please enter valid username and password';
      }
      // 如果是余额不足，直接使用原始错误信息，不显示Toast（让业务逻辑处理）
      else if (data?.message && (data.message.includes('余额不足') || data.message.includes('Insufficient balance'))) {
        const error = new Error(data.message);
        error.response = { data: data, status: statusCode };
        return Promise.reject(error);
      }

      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });

      return Promise.reject(new Error(errorMessage));
    }

    // 处理其他错误
    const errorMessage = data?.message || `Request failed, status code: ${statusCode}`;

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000
    });

    return Promise.reject(new Error(errorMessage));
  }

  // 检查响应中是否有新的token
  const newToken = response.header && response.header['X-New-Token'];
  if (newToken) {
    // 更新本地存储的token
    uni.setStorageSync('userToken', newToken);
  }

  // 返回响应数据
  return data;
};

// 封装请求方法
const request = (options) => {
  // 应用请求拦截器
  const config = requestInterceptor(options);



  // 处理GET请求的参数
  if (config.method === 'GET' && config.params) {
    // 将params转换为URL参数
    const queryString = Object.keys(config.params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(config.params[key])}`)
      .join('&');

    // 如果有参数，添加到URL
    if (queryString) {
      config.url += (config.url.includes('?') ? '&' : '?') + queryString;

    }
  }

  // 发起请求
  return new Promise((resolve, reject) => {
    // 准备fetch选项
    const fetchOptions = {
      method: config.method,
      headers: config.header,
      credentials: 'include', // 包含凭证信息（类似withCredentials）
    };

    // 如果有请求体，添加到选项中
    if (config.method !== 'GET' && config.data) {
      fetchOptions.body = JSON.stringify(config.data);
    }



    // 使用fetch API发起请求
    // 添加随机查询参数避免缓存
    const cacheBuster = `_t=${Date.now()}`;
    const url = config.url.includes('?') ? `${config.url}&${cacheBuster}` : `${config.url}?${cacheBuster}`;

    fetch(url, fetchOptions)
      .then(response => {


        // 构建类似uni.request的响应格式
        return response.text().then(text => {
          let data = {};

          // 尝试解析JSON
          if (text) {
            try {
              data = JSON.parse(text);
            } catch (e) {

              // 如果状态码不是2xx，创建一个错误对象
              if (!response.ok) {
                data = {
                  code: response.status,
                  message: response.statusText || '请求失败',
                  data: null
                };
              }
            }
          } else if (!response.ok) {
            // 如果没有响应体且状态码不是2xx
            data = {
              code: response.status,
              message: response.statusText || '请求失败',
              data: null
            };
          }

          const adaptedResponse = {
            statusCode: response.status,
            data: data,
            header: {}
          };

          // 将响应头转换为对象
          response.headers.forEach((value, key) => {
            adaptedResponse.header[key] = value;
          });

          return adaptedResponse;
        });
      })
      .then(adaptedResponse => {
        try {
          // 应用响应拦截器
          const result = responseInterceptor(adaptedResponse);
          resolve(result);
        } catch (error) {

          reject(error);
        }
      })
      .catch(error => {

        // 处理请求失败
        uni.showToast({
          title: 'Network request failed, please check your connection',
          icon: 'none',
          duration: 2000
        });
        reject(new Error('Network request failed, please check your connection'));
      });
  });
};

// 封装GET请求
const get = (url, options = {}) => {
  return request({
    url,
    method: 'GET',
    data: options.params || {},
    params: options.params || {}
  });
};

// 封装POST请求
const post = (url, data = {}) => {
  return request({
    url,
    method: 'POST',
    data
  });
};

// 封装PUT请求
const put = (url, data = {}) => {
  return request({
    url,
    method: 'PUT',
    data
  });
};

// 封装DELETE请求
const del = (url) => {
  return request({
    url,
    method: 'DELETE'
  });
};

// 导出请求方法
export {
  request,
  get,
  post,
  put,
  del as delete
};

// 导出默认对象
export default {
  request,
  get,
  post,
  put,
  delete: del
};
