/**
 * 获取管理端首页统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAdminDashboardStats = async (req, res) => {
  try {
    // 引入统计服务
    const statisticsService = require('../services/statisticsService');
    const dateUtils = require('../utils/dateUtils');
    const moment = require('moment');
    const { User, Transaction, Deposit } = require('../models');
    const { Op } = require('sequelize');

    // 获取今日、昨日和累计统计数据
    const todayStats = await statisticsService.getTodayStatistics();
    const yesterdayStats = await statisticsService.getYesterdayStatistics();
    const totalStats = await statisticsService.getTotalStatistics();
    
    // 获取最近30天的统计数据
    const recentStats = await statisticsService.getRecentDaysStatistics(30);
    
    // 获取系统时区设置
    const systemTimezone = '-08:00'; // 使用固定的系统时区

    // 获取当前服务器时间
    const currentTime = new Date();
    const formattedCurrentTime = dateUtils.formatDateTime(currentTime);
    
    // 获取最近的充值记录
    const recentDeposits = await Transaction.findAll({
      where: {
        type: 'deposit',
        status: 'completed'
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'user_id']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: 5
    });
    
    // 格式化充值记录
    const formattedDeposits = recentDeposits.map(deposit => ({
      id: deposit.id,
      user_id: deposit.user ? deposit.user.user_id : '',
      username: deposit.user ? deposit.user.username : '',
      nickname: deposit.user ? deposit.user.nickname : '',
      amount: parseFloat(deposit.amount).toFixed(2),
      created_at: dateUtils.formatDateTime(deposit.created_at)
    }));
    
    // 获取最近的注册用户
    const recentUsers = await User.findAll({
      attributes: ['id', 'username', 'nickname', 'user_id', 'created_at'],
      order: [['created_at', 'DESC']],
      limit: 5
    });
    
    // 格式化用户记录
    const formattedUsers = recentUsers.map(user => ({
      id: user.id,
      user_id: user.user_id,
      username: user.username,
      nickname: user.nickname,
      created_at: dateUtils.formatDateTime(user.created_at)
    }));
    
    // 准备图表数据
    const chartDates = [];
    const chartData = {
      registerCounts: [],
      registerDepositCounts: [],
      totalDepositCounts: []
    };
    const moneyChartData = {
      investmentAmounts: [],
      depositAmounts: [],
      withdrawalAmounts: []
    };
    
    // 处理最近30天的统计数据
    recentStats.forEach(stat => {
      const formattedDate = moment(stat.date).format('YYYY-MM-DD');
      chartDates.push(formattedDate);
      
      // 注册人数
      chartData.registerCounts.push(stat.new_user_count);
      
      // 注册并充值人数
      chartData.registerDepositCounts.push(stat.register_deposit_count);
      
      // 充值人数
      chartData.totalDepositCounts.push(stat.deposit_user_count);
      
      // 投资金额
      moneyChartData.investmentAmounts.push(parseFloat(stat.investment_amount).toFixed(2));
      
      // 充值金额
      moneyChartData.depositAmounts.push(parseFloat(stat.deposit_amount).toFixed(2));
      
      // 取款金额
      moneyChartData.withdrawalAmounts.push(parseFloat(stat.withdrawal_amount).toFixed(2));
    });
    
    // 计算平台利润
    const todayPlatformProfit = parseFloat(todayStats.deposit_amount) - parseFloat(todayStats.withdrawal_amount);
    const yesterdayPlatformProfit = parseFloat(yesterdayStats.deposit_amount) - parseFloat(yesterdayStats.withdrawal_amount);
    const totalPlatformProfit = parseFloat(totalStats.total_deposit_amount) - parseFloat(totalStats.total_withdrawal_amount);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        currentTime: formattedCurrentTime,
        timezone: systemTimezone,
        users: {
          today: todayStats.new_user_count,
          yesterday: yesterdayStats.new_user_count,
          total: totalStats.total_user_count
        },
        deposits: {
          today: parseFloat(todayStats.deposit_amount).toFixed(2),
          yesterday: parseFloat(yesterdayStats.deposit_amount).toFixed(2),
          total: parseFloat(totalStats.total_deposit_amount).toFixed(2)
        },
        depositCounts: {
          today: todayStats.deposit_count,
          yesterday: yesterdayStats.deposit_count,
          total: totalStats.total_deposit_count
        },
        depositUserCounts: {
          today: todayStats.deposit_user_count,
          yesterday: yesterdayStats.deposit_user_count,
          total: totalStats.total_deposit_user_count
        },
        withdrawals: {
          today: parseFloat(todayStats.withdrawal_amount).toFixed(2),
          yesterday: parseFloat(yesterdayStats.withdrawal_amount).toFixed(2),
          total: parseFloat(totalStats.total_withdrawal_amount).toFixed(2)
        },
        withdrawalCounts: {
          today: todayStats.withdrawal_count,
          yesterday: yesterdayStats.withdrawal_count,
          total: totalStats.total_withdrawal_count
        },
        investments: {
          today: parseFloat(todayStats.investment_amount).toFixed(2),
          yesterday: parseFloat(yesterdayStats.investment_amount).toFixed(2),
          total: parseFloat(totalStats.total_investment_amount).toFixed(2)
        },
        platformProfits: {
          today: parseFloat(todayPlatformProfit).toFixed(2),
          yesterday: parseFloat(yesterdayPlatformProfit).toFixed(2),
          total: parseFloat(totalPlatformProfit).toFixed(2)
        },
        recentDeposits: formattedDeposits,
        recentUsers: formattedUsers,
        chartData: {
          dates: chartDates,
          registerCounts: chartData.registerCounts,
          registerDepositCounts: chartData.registerDepositCounts,
          totalDepositCounts: chartData.totalDepositCounts
        },
        moneyChartData: {
          dates: chartDates,
          investmentAmounts: moneyChartData.investmentAmounts,
          depositAmounts: moneyChartData.depositAmounts,
          withdrawalAmounts: moneyChartData.withdrawalAmounts
        }
      }
    });
  } catch (error) {
    console.error('获取管理端首页统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
