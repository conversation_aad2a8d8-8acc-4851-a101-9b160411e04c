{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/modelsObject.json#", "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "required": ["id", "properties"], "properties": {"id": {"type": "string"}, "description": {"type": "string"}, "properties": {"type": "object", "additionalProperties": {"$ref": "#/definitions/propertyObject"}}, "subTypes": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "discriminator": {"type": "string"}}, "dependencies": {"subTypes": ["discriminator"]}, "definitions": {"propertyObject": {"allOf": [{"not": {"$ref": "#"}}, {"$ref": "dataTypeBase.json#"}]}}}