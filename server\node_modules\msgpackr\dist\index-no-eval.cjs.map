{"version": 3, "file": "index-no-eval.cjs", "sources": ["../unpack.js", "../pack.js", "../iterators.js", "../index.js"], "sourcesContent": ["var decoder\ntry {\n\tdecoder = new TextDecoder()\n} catch(error) {}\nvar src\nvar srcEnd\nvar position = 0\nvar alreadySet\nconst EMPTY_ARRAY = []\nvar strings = EMPTY_ARRAY\nvar stringPosition = 0\nvar currentUnpackr = {}\nvar currentStructures\nvar srcString\nvar srcStringStart = 0\nvar srcStringEnd = 0\nvar bundledStrings\nvar referenceMap\nvar currentExtensions = []\nvar dataView\nvar defaultOptions = {\n\tuseRecords: false,\n\tmapsAsObjects: true\n}\nexport class C1Type {}\nexport const C1 = new C1Type()\nC1.name = 'MessagePack 0xC1'\nvar sequentialMode = false\nvar inlineObjectReadThreshold = 2\nvar readStruct, onLoadedStructures, onSaveState\nvar BlockedFunction // we use search and replace to change the next call to BlockedFunction to avoid CSP issues for\n// no-eval build\ntry {\n\tnew Function('')\n} catch(error) {\n\t// if eval variants are not supported, do not create inline object readers ever\n\tinlineObjectReadThreshold = Infinity\n}\n\nexport class Unpackr {\n\tconstructor(options) {\n\t\tif (options) {\n\t\t\tif (options.useRecords === false && options.mapsAsObjects === undefined)\n\t\t\t\toptions.mapsAsObjects = true\n\t\t\tif (options.sequential && options.trusted !== false) {\n\t\t\t\toptions.trusted = true;\n\t\t\t\tif (!options.structures && options.useRecords != false) {\n\t\t\t\t\toptions.structures = []\n\t\t\t\t\tif (!options.maxSharedStructures)\n\t\t\t\t\t\toptions.maxSharedStructures = 0\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (options.structures)\n\t\t\t\toptions.structures.sharedLength = options.structures.length\n\t\t\telse if (options.getStructures) {\n\t\t\t\t(options.structures = []).uninitialized = true // this is what we use to denote an uninitialized structures\n\t\t\t\toptions.structures.sharedLength = 0\n\t\t\t}\n\t\t\tif (options.int64AsNumber) {\n\t\t\t\toptions.int64AsType = 'number'\n\t\t\t}\n\t\t}\n\t\tObject.assign(this, options)\n\t}\n\tunpack(source, options) {\n\t\tif (src) {\n\t\t\t// re-entrant execution, save the state and restore it after we do this unpack\n\t\t\treturn saveState(() => {\n\t\t\t\tclearSource()\n\t\t\t\treturn this ? this.unpack(source, options) : Unpackr.prototype.unpack.call(defaultOptions, source, options)\n\t\t\t})\n\t\t}\n\t\tif (!source.buffer && source.constructor === ArrayBuffer)\n\t\t\tsource = typeof Buffer !== 'undefined' ? Buffer.from(source) : new Uint8Array(source);\n\t\tif (typeof options === 'object') {\n\t\t\tsrcEnd = options.end || source.length\n\t\t\tposition = options.start || 0\n\t\t} else {\n\t\t\tposition = 0\n\t\t\tsrcEnd = options > -1 ? options : source.length\n\t\t}\n\t\tstringPosition = 0\n\t\tsrcStringEnd = 0\n\t\tsrcString = null\n\t\tstrings = EMPTY_ARRAY\n\t\tbundledStrings = null\n\t\tsrc = source\n\t\t// this provides cached access to the data view for a buffer if it is getting reused, which is a recommend\n\t\t// technique for getting data from a database where it can be copied into an existing buffer instead of creating\n\t\t// new ones\n\t\ttry {\n\t\t\tdataView = source.dataView || (source.dataView = new DataView(source.buffer, source.byteOffset, source.byteLength))\n\t\t} catch(error) {\n\t\t\t// if it doesn't have a buffer, maybe it is the wrong type of object\n\t\t\tsrc = null\n\t\t\tif (source instanceof Uint8Array)\n\t\t\t\tthrow error\n\t\t\tthrow new Error('Source must be a Uint8Array or Buffer but was a ' + ((source && typeof source == 'object') ? source.constructor.name : typeof source))\n\t\t}\n\t\tif (this instanceof Unpackr) {\n\t\t\tcurrentUnpackr = this\n\t\t\tif (this.structures) {\n\t\t\t\tcurrentStructures = this.structures\n\t\t\t\treturn checkedRead(options)\n\t\t\t} else if (!currentStructures || currentStructures.length > 0) {\n\t\t\t\tcurrentStructures = []\n\t\t\t}\n\t\t} else {\n\t\t\tcurrentUnpackr = defaultOptions\n\t\t\tif (!currentStructures || currentStructures.length > 0)\n\t\t\t\tcurrentStructures = []\n\t\t}\n\t\treturn checkedRead(options)\n\t}\n\tunpackMultiple(source, forEach) {\n\t\tlet values, lastPosition = 0\n\t\ttry {\n\t\t\tsequentialMode = true\n\t\t\tlet size = source.length\n\t\t\tlet value = this ? this.unpack(source, size) : defaultUnpackr.unpack(source, size)\n\t\t\tif (forEach) {\n\t\t\t\tif (forEach(value, lastPosition, position) === false) return;\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tif (forEach(checkedRead(), lastPosition, position) === false) {\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tvalues = [ value ]\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tvalues.push(checkedRead())\n\t\t\t\t}\n\t\t\t\treturn values\n\t\t\t}\n\t\t} catch(error) {\n\t\t\terror.lastPosition = lastPosition\n\t\t\terror.values = values\n\t\t\tthrow error\n\t\t} finally {\n\t\t\tsequentialMode = false\n\t\t\tclearSource()\n\t\t}\n\t}\n\t_mergeStructures(loadedStructures, existingStructures) {\n\t\tif (onLoadedStructures)\n\t\t\tloadedStructures = onLoadedStructures.call(this, loadedStructures);\n\t\tloadedStructures = loadedStructures || []\n\t\tif (Object.isFrozen(loadedStructures))\n\t\t\tloadedStructures = loadedStructures.map(structure => structure.slice(0))\n\t\tfor (let i = 0, l = loadedStructures.length; i < l; i++) {\n\t\t\tlet structure = loadedStructures[i]\n\t\t\tif (structure) {\n\t\t\t\tstructure.isShared = true\n\t\t\t\tif (i >= 32)\n\t\t\t\t\tstructure.highByte = (i - 32) >> 5\n\t\t\t}\n\t\t}\n\t\tloadedStructures.sharedLength = loadedStructures.length\n\t\tfor (let id in existingStructures || []) {\n\t\t\tif (id >= 0) {\n\t\t\t\tlet structure = loadedStructures[id]\n\t\t\t\tlet existing = existingStructures[id]\n\t\t\t\tif (existing) {\n\t\t\t\t\tif (structure)\n\t\t\t\t\t\t(loadedStructures.restoreStructures || (loadedStructures.restoreStructures = []))[id] = structure\n\t\t\t\t\tloadedStructures[id] = existing\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this.structures = loadedStructures\n\t}\n\tdecode(source, options) {\n\t\treturn this.unpack(source, options)\n\t}\n}\nexport function getPosition() {\n\treturn position\n}\nexport function checkedRead(options) {\n\ttry {\n\t\tif (!currentUnpackr.trusted && !sequentialMode) {\n\t\t\tlet sharedLength = currentStructures.sharedLength || 0\n\t\t\tif (sharedLength < currentStructures.length)\n\t\t\t\tcurrentStructures.length = sharedLength\n\t\t}\n\t\tlet result\n\t\tif (currentUnpackr.randomAccessStructure && src[position] < 0x40 && src[position] >= 0x20 && readStruct) {\n\t\t\tresult = readStruct(src, position, srcEnd, currentUnpackr)\n\t\t\tsrc = null // dispose of this so that recursive unpack calls don't save state\n\t\t\tif (!(options && options.lazy) && result)\n\t\t\t\tresult = result.toJSON()\n\t\t\tposition = srcEnd\n\t\t} else\n\t\t\tresult = read()\n\t\tif (bundledStrings) { // bundled strings to skip past\n\t\t\tposition = bundledStrings.postBundlePosition\n\t\t\tbundledStrings = null\n\t\t}\n\t\tif (sequentialMode)\n\t\t\t// we only need to restore the structures if there was an error, but if we completed a read,\n\t\t\t// we can clear this out and keep the structures we read\n\t\t\tcurrentStructures.restoreStructures = null\n\n\t\tif (position == srcEnd) {\n\t\t\t// finished reading this source, cleanup references\n\t\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\t\trestoreStructures()\n\t\t\tcurrentStructures = null\n\t\t\tsrc = null\n\t\t\tif (referenceMap)\n\t\t\t\treferenceMap = null\n\t\t} else if (position > srcEnd) {\n\t\t\t// over read\n\t\t\tthrow new Error('Unexpected end of MessagePack data')\n\t\t} else if (!sequentialMode) {\n\t\t\tlet jsonView;\n\t\t\ttry {\n\t\t\t\tjsonView = JSON.stringify(result, (_, value) => typeof value === \"bigint\" ? `${value}n` : value).slice(0, 100)\n\t\t\t} catch(error) {\n\t\t\t\tjsonView = '(JSON view not available ' + error + ')'\n\t\t\t}\n\t\t\tthrow new Error('Data read, but end of buffer not reached ' + jsonView)\n\t\t}\n\t\t// else more to read, but we are reading sequentially, so don't clear source yet\n\t\treturn result\n\t} catch(error) {\n\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\trestoreStructures()\n\t\tclearSource()\n\t\tif (error instanceof RangeError || error.message.startsWith('Unexpected end of buffer') || position > srcEnd) {\n\t\t\terror.incomplete = true\n\t\t}\n\t\tthrow error\n\t}\n}\n\nfunction restoreStructures() {\n\tfor (let id in currentStructures.restoreStructures) {\n\t\tcurrentStructures[id] = currentStructures.restoreStructures[id]\n\t}\n\tcurrentStructures.restoreStructures = null\n}\n\nexport function read() {\n\tlet token = src[position++]\n\tif (token < 0xa0) {\n\t\tif (token < 0x80) {\n\t\t\tif (token < 0x40)\n\t\t\t\treturn token\n\t\t\telse {\n\t\t\t\tlet structure = currentStructures[token & 0x3f] ||\n\t\t\t\t\tcurrentUnpackr.getStructures && loadStructures()[token & 0x3f]\n\t\t\t\tif (structure) {\n\t\t\t\t\tif (!structure.read) {\n\t\t\t\t\t\tstructure.read = createStructureReader(structure, token & 0x3f)\n\t\t\t\t\t}\n\t\t\t\t\treturn structure.read()\n\t\t\t\t} else\n\t\t\t\t\treturn token\n\t\t\t}\n\t\t} else if (token < 0x90) {\n\t\t\t// map\n\t\t\ttoken -= 0x80\n\t\t\tif (currentUnpackr.mapsAsObjects) {\n\t\t\t\tlet object = {}\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tlet key = readKey()\n\t\t\t\t\tif (key === '__proto__')\n\t\t\t\t\t\tkey = '__proto_'\n\t\t\t\t\tobject[key] = read()\n\t\t\t\t}\n\t\t\t\treturn object\n\t\t\t} else {\n\t\t\t\tlet map = new Map()\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tmap.set(read(), read())\n\t\t\t\t}\n\t\t\t\treturn map\n\t\t\t}\n\t\t} else {\n\t\t\ttoken -= 0x90\n\t\t\tlet array = new Array(token)\n\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\tarray[i] = read()\n\t\t\t}\n\t\t\tif (currentUnpackr.freezeData)\n\t\t\t\treturn Object.freeze(array)\n\t\t\treturn array\n\t\t}\n\t} else if (token < 0xc0) {\n\t\t// fixstr\n\t\tlet length = token - 0xa0\n\t\tif (srcStringEnd >= position) {\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\t}\n\t\tif (srcStringEnd == 0 && srcEnd < 140) {\n\t\t\t// for small blocks, avoiding the overhead of the extract call is helpful\n\t\t\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\t\t\tif (string != null)\n\t\t\t\treturn string\n\t\t}\n\t\treturn readFixedString(length)\n\t} else {\n\t\tlet value\n\t\tswitch (token) {\n\t\t\tcase 0xc0: return null\n\t\t\tcase 0xc1:\n\t\t\t\tif (bundledStrings) {\n\t\t\t\t\tvalue = read() // followed by the length of the string in characters (not bytes!)\n\t\t\t\t\tif (value > 0)\n\t\t\t\t\t\treturn bundledStrings[1].slice(bundledStrings.position1, bundledStrings.position1 += value)\n\t\t\t\t\telse\n\t\t\t\t\t\treturn bundledStrings[0].slice(bundledStrings.position0, bundledStrings.position0 -= value)\n\t\t\t\t}\n\t\t\t\treturn C1; // \"never-used\", return special object to denote that\n\t\t\tcase 0xc2: return false\n\t\t\tcase 0xc3: return true\n\t\t\tcase 0xc4:\n\t\t\t\t// bin 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value === undefined)\n\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc5:\n\t\t\t\t// bin 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc6:\n\t\t\t\t// bin 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc7:\n\t\t\t\t// ext 8\n\t\t\t\treturn readExt(src[position++])\n\t\t\tcase 0xc8:\n\t\t\t\t// ext 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xc9:\n\t\t\t\t// ext 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xca:\n\t\t\t\tvalue = dataView.getFloat32(position)\n\t\t\t\tif (currentUnpackr.useFloat32 > 2) {\n\t\t\t\t\t// this does rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\tlet multiplier = mult10[((src[position] & 0x7f) << 1) | (src[position + 1] >> 7)]\n\t\t\t\t\tposition += 4\n\t\t\t\t\treturn ((multiplier * value + (value > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n\t\t\t\t}\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcb:\n\t\t\t\tvalue = dataView.getFloat64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\t\t\t// uint handlers\n\t\t\tcase 0xcc:\n\t\t\t\treturn src[position++]\n\t\t\tcase 0xcd:\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xce:\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcf:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getUint32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\t\tif (value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\t// int handlers\n\t\t\tcase 0xd0:\n\t\t\t\treturn dataView.getInt8(position++)\n\t\t\tcase 0xd1:\n\t\t\t\tvalue = dataView.getInt16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xd2:\n\t\t\t\tvalue = dataView.getInt32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xd3:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getInt32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\t\tif (value>=BigInt(-2)<<BigInt(52)&&value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\tcase 0xd4:\n\t\t\t\t// fixext 1\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f)\n\t\t\t\t} else {\n\t\t\t\t\tlet extension = currentExtensions[value]\n\t\t\t\t\tif (extension) {\n\t\t\t\t\t\tif (extension.read) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension.read(read())\n\t\t\t\t\t\t} else if (extension.noBuffer) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension()\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treturn extension(src.subarray(position, ++position))\n\t\t\t\t\t} else\n\t\t\t\t\t\tthrow new Error('Unknown extension ' + value)\n\t\t\t\t}\n\t\t\tcase 0xd5:\n\t\t\t\t// fixext 2\n\t\t\t\tvalue = src[position]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\tposition++\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f, src[position++])\n\t\t\t\t} else\n\t\t\t\t\treturn readExt(2)\n\t\t\tcase 0xd6:\n\t\t\t\t// fixext 4\n\t\t\t\treturn readExt(4)\n\t\t\tcase 0xd7:\n\t\t\t\t// fixext 8\n\t\t\t\treturn readExt(8)\n\t\t\tcase 0xd8:\n\t\t\t\t// fixext 16\n\t\t\t\treturn readExt(16)\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString8(value)\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString16(value)\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString32(value)\n\t\t\tcase 0xdc:\n\t\t\t// array 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xdd:\n\t\t\t// array 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xde:\n\t\t\t// map 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readMap(value)\n\t\t\tcase 0xdf:\n\t\t\t// map 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readMap(value)\n\t\t\tdefault: // negative int\n\t\t\t\tif (token >= 0xe0)\n\t\t\t\t\treturn token - 0x100\n\t\t\t\tif (token === undefined) {\n\t\t\t\t\tlet error = new Error('Unexpected end of MessagePack data')\n\t\t\t\t\terror.incomplete = true\n\t\t\t\t\tthrow error\n\t\t\t\t}\n\t\t\t\tthrow new Error('Unknown MessagePack token ' + token)\n\n\t\t}\n\t}\n}\nconst validName = /^[a-zA-Z_$][a-zA-Z\\d_$]*$/\nfunction createStructureReader(structure, firstId) {\n\tfunction readObject() {\n\t\t// This initial function is quick to instantiate, but runs slower. After several iterations pay the cost to build the faster function\n\t\tif (readObject.count++ > inlineObjectReadThreshold) {\n\t\t\tlet readObject = structure.read = (new Function('r', 'return function(){return ' + (currentUnpackr.freezeData ? 'Object.freeze' : '') +\n\t\t\t\t'({' + structure.map(key => key === '__proto__' ? '__proto_:r()' : validName.test(key) ? key + ':r()' : ('[' + JSON.stringify(key) + ']:r()')).join(',') + '})}'))(read)\n\t\t\tif (structure.highByte === 0)\n\t\t\t\tstructure.read = createSecondByteReader(firstId, structure.read)\n\t\t\treturn readObject() // second byte is already read, if there is one so immediately read object\n\t\t}\n\t\tlet object = {}\n\t\tfor (let i = 0, l = structure.length; i < l; i++) {\n\t\t\tlet key = structure[i]\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_'\n\t\t\tobject[key] = read()\n\t\t}\n\t\tif (currentUnpackr.freezeData)\n\t\t\treturn Object.freeze(object);\n\t\treturn object\n\t}\n\treadObject.count = 0\n\tif (structure.highByte === 0) {\n\t\treturn createSecondByteReader(firstId, readObject)\n\t}\n\treturn readObject\n}\n\nconst createSecondByteReader = (firstId, read0) => {\n\treturn function() {\n\t\tlet highByte = src[position++]\n\t\tif (highByte === 0)\n\t\t\treturn read0()\n\t\tlet id = firstId < 32 ? -(firstId + (highByte << 5)) : firstId + (highByte << 5)\n\t\tlet structure = currentStructures[id] || loadStructures()[id]\n\t\tif (!structure) {\n\t\t\tthrow new Error('Record id is not defined for ' + id)\n\t\t}\n\t\tif (!structure.read)\n\t\t\tstructure.read = createStructureReader(structure, firstId)\n\t\treturn structure.read()\n\t}\n}\n\nexport function loadStructures() {\n\tlet loadedStructures = saveState(() => {\n\t\t// save the state in case getStructures modifies our buffer\n\t\tsrc = null\n\t\treturn currentUnpackr.getStructures()\n\t})\n\treturn currentStructures = currentUnpackr._mergeStructures(loadedStructures, currentStructures)\n}\n\nvar readFixedString = readStringJS\nvar readString8 = readStringJS\nvar readString16 = readStringJS\nvar readString32 = readStringJS\nexport let isNativeAccelerationEnabled = false\n\nexport function setExtractor(extractStrings) {\n\tisNativeAccelerationEnabled = true\n\treadFixedString = readString(1)\n\treadString8 = readString(2)\n\treadString16 = readString(3)\n\treadString32 = readString(5)\n\tfunction readString(headerLength) {\n\t\treturn function readString(length) {\n\t\t\tlet string = strings[stringPosition++]\n\t\t\tif (string == null) {\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\treturn readStringJS(length)\n\t\t\t\tlet byteOffset = src.byteOffset\n\t\t\t\tlet extraction = extractStrings(position - headerLength + byteOffset, srcEnd + byteOffset, src.buffer)\n\t\t\t\tif (typeof extraction == 'string') {\n\t\t\t\t\tstring = extraction\n\t\t\t\t\tstrings = EMPTY_ARRAY\n\t\t\t\t} else {\n\t\t\t\t\tstrings = extraction\n\t\t\t\t\tstringPosition = 1\n\t\t\t\t\tsrcStringEnd = 1 // even if a utf-8 string was decoded, must indicate we are in the midst of extracted strings and can't skip strings\n\t\t\t\t\tstring = strings[0]\n\t\t\t\t\tif (string === undefined)\n\t\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet srcStringLength = string.length\n\t\t\tif (srcStringLength <= length) {\n\t\t\t\tposition += length\n\t\t\t\treturn string\n\t\t\t}\n\t\t\tsrcString = string\n\t\t\tsrcStringStart = position\n\t\t\tsrcStringEnd = position + srcStringLength\n\t\t\tposition += length\n\t\t\treturn string.slice(0, length) // we know we just want the beginning\n\t\t}\n\t}\n}\nfunction readStringJS(length) {\n\tlet result\n\tif (length < 16) {\n\t\tif (result = shortStringInJS(length))\n\t\t\treturn result\n\t}\n\tif (length > 64 && decoder)\n\t\treturn decoder.decode(src.subarray(position, position += length))\n\tconst end = position + length\n\tconst units = []\n\tresult = ''\n\twhile (position < end) {\n\t\tconst byte1 = src[position++]\n\t\tif ((byte1 & 0x80) === 0) {\n\t\t\t// 1 byte\n\t\t\tunits.push(byte1)\n\t\t} else if ((byte1 & 0xe0) === 0xc0) {\n\t\t\t// 2 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 6) | byte2)\n\t\t} else if ((byte1 & 0xf0) === 0xe0) {\n\t\t\t// 3 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3)\n\t\t} else if ((byte1 & 0xf8) === 0xf0) {\n\t\t\t// 4 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tconst byte4 = src[position++] & 0x3f\n\t\t\tlet unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4\n\t\t\tif (unit > 0xffff) {\n\t\t\t\tunit -= 0x10000\n\t\t\t\tunits.push(((unit >>> 10) & 0x3ff) | 0xd800)\n\t\t\t\tunit = 0xdc00 | (unit & 0x3ff)\n\t\t\t}\n\t\t\tunits.push(unit)\n\t\t} else {\n\t\t\tunits.push(byte1)\n\t\t}\n\n\t\tif (units.length >= 0x1000) {\n\t\t\tresult += fromCharCode.apply(String, units)\n\t\t\tunits.length = 0\n\t\t}\n\t}\n\n\tif (units.length > 0) {\n\t\tresult += fromCharCode.apply(String, units)\n\t}\n\n\treturn result\n}\nexport function readString(source, start, length) {\n\tlet existingSrc = src;\n\tsrc = source;\n\tposition = start;\n\ttry {\n\t\treturn readStringJS(length);\n\t} finally {\n\t\tsrc = existingSrc;\n\t}\n}\n\nfunction readArray(length) {\n\tlet array = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tarray[i] = read()\n\t}\n\tif (currentUnpackr.freezeData)\n\t\treturn Object.freeze(array)\n\treturn array\n}\n\nfunction readMap(length) {\n\tif (currentUnpackr.mapsAsObjects) {\n\t\tlet object = {}\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tlet key = readKey()\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_';\n\t\t\tobject[key] = read()\n\t\t}\n\t\treturn object\n\t} else {\n\t\tlet map = new Map()\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tmap.set(read(), read())\n\t\t}\n\t\treturn map\n\t}\n}\n\nvar fromCharCode = String.fromCharCode\nfunction longStringInJS(length) {\n\tlet start = position\n\tlet bytes = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tconst byte = src[position++];\n\t\tif ((byte & 0x80) > 0) {\n\t\t\t\tposition = start\n\t\t\t\treturn\n\t\t\t}\n\t\t\tbytes[i] = byte\n\t\t}\n\t\treturn fromCharCode.apply(String, bytes)\n}\nfunction shortStringInJS(length) {\n\tif (length < 4) {\n\t\tif (length < 2) {\n\t\t\tif (length === 0)\n\t\t\t\treturn ''\n\t\t\telse {\n\t\t\t\tlet a = src[position++]\n\t\t\t\tif ((a & 0x80) > 1) {\n\t\t\t\t\tposition -= 1\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a)\n\t\t\t}\n\t\t} else {\n\t\t\tlet a = src[position++]\n\t\t\tlet b = src[position++]\n\t\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0) {\n\t\t\t\tposition -= 2\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 3)\n\t\t\t\treturn fromCharCode(a, b)\n\t\t\tlet c = src[position++]\n\t\t\tif ((c & 0x80) > 0) {\n\t\t\t\tposition -= 3\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c)\n\t\t}\n\t} else {\n\t\tlet a = src[position++]\n\t\tlet b = src[position++]\n\t\tlet c = src[position++]\n\t\tlet d = src[position++]\n\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0 || (c & 0x80) > 0 || (d & 0x80) > 0) {\n\t\t\tposition -= 4\n\t\t\treturn\n\t\t}\n\t\tif (length < 6) {\n\t\t\tif (length === 4)\n\t\t\t\treturn fromCharCode(a, b, c, d)\n\t\t\telse {\n\t\t\t\tlet e = src[position++]\n\t\t\t\tif ((e & 0x80) > 0) {\n\t\t\t\t\tposition -= 5\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e)\n\t\t\t}\n\t\t} else if (length < 8) {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0) {\n\t\t\t\tposition -= 6\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 7)\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f)\n\t\t\tlet g = src[position++]\n\t\t\tif ((g & 0x80) > 0) {\n\t\t\t\tposition -= 7\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c, d, e, f, g)\n\t\t} else {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tlet g = src[position++]\n\t\t\tlet h = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0 || (g & 0x80) > 0 || (h & 0x80) > 0) {\n\t\t\t\tposition -= 8\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 10) {\n\t\t\t\tif (length === 8)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h)\n\t\t\t\telse {\n\t\t\t\t\tlet i = src[position++]\n\t\t\t\t\tif ((i & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 9\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i)\n\t\t\t\t}\n\t\t\t} else if (length < 12) {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0) {\n\t\t\t\t\tposition -= 10\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 11)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j)\n\t\t\t\tlet k = src[position++]\n\t\t\t\tif ((k & 0x80) > 0) {\n\t\t\t\t\tposition -= 11\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k)\n\t\t\t} else {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tlet k = src[position++]\n\t\t\t\tlet l = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0 || (k & 0x80) > 0 || (l & 0x80) > 0) {\n\t\t\t\t\tposition -= 12\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 14) {\n\t\t\t\t\tif (length === 12)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l)\n\t\t\t\t\telse {\n\t\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\t\tif ((m & 0x80) > 0) {\n\t\t\t\t\t\t\tposition -= 13\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\tlet n = src[position++]\n\t\t\t\t\tif ((m & 0x80) > 0 || (n & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 14\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tif (length < 15)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n)\n\t\t\t\t\tlet o = src[position++]\n\t\t\t\t\tif ((o & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 15\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction readOnlyJSString() {\n\tlet token = src[position++]\n\tlet length\n\tif (token < 0xc0) {\n\t\t// fixstr\n\t\tlength = token - 0xa0\n\t} else {\n\t\tswitch(token) {\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tlength = src[position++]\n\t\t\t\tbreak\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tlength = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tbreak\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tlength = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\tthrow new Error('Expected string')\n\t\t}\n\t}\n\treturn readStringJS(length)\n}\n\n\nfunction readBin(length) {\n\treturn currentUnpackr.copyBuffers ?\n\t\t// specifically use the copying slice (not the node one)\n\t\tUint8Array.prototype.slice.call(src, position, position += length) :\n\t\tsrc.subarray(position, position += length)\n}\nfunction readExt(length) {\n\tlet type = src[position++]\n\tif (currentExtensions[type]) {\n\t\tlet end\n\t\treturn currentExtensions[type](src.subarray(position, end = (position += length)), (readPosition) => {\n\t\t\tposition = readPosition;\n\t\t\ttry {\n\t\t\t\treturn read();\n\t\t\t} finally {\n\t\t\t\tposition = end;\n\t\t\t}\n\t\t})\n\t}\n\telse\n\t\tthrow new Error('Unknown extension type ' + type)\n}\n\nvar keyCache = new Array(4096)\nfunction readKey() {\n\tlet length = src[position++]\n\tif (length >= 0xa0 && length < 0xc0) {\n\t\t// fixstr, potentially use key cache\n\t\tlength = length - 0xa0\n\t\tif (srcStringEnd >= position) // if it has been extracted, must use it (and faster anyway)\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\telse if (!(srcStringEnd == 0 && srcEnd < 180))\n\t\t\treturn readFixedString(length)\n\t} else { // not cacheable, go back and do a standard read\n\t\tposition--\n\t\treturn asSafeString(read())\n\t}\n\tlet key = ((length << 5) ^ (length > 1 ? dataView.getUint16(position) : length > 0 ? src[position] : 0)) & 0xfff\n\tlet entry = keyCache[key]\n\tlet checkPosition = position\n\tlet end = position + length - 3\n\tlet chunk\n\tlet i = 0\n\tif (entry && entry.bytes == length) {\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = dataView.getUint32(checkPosition)\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t\tcheckPosition += 4\n\t\t}\n\t\tend += 3\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = src[checkPosition++]\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\tif (checkPosition === end) {\n\t\t\tposition = checkPosition\n\t\t\treturn entry.string\n\t\t}\n\t\tend -= 3\n\t\tcheckPosition = position\n\t}\n\tentry = []\n\tkeyCache[key] = entry\n\tentry.bytes = length\n\twhile (checkPosition < end) {\n\t\tchunk = dataView.getUint32(checkPosition)\n\t\tentry.push(chunk)\n\t\tcheckPosition += 4\n\t}\n\tend += 3\n\twhile (checkPosition < end) {\n\t\tchunk = src[checkPosition++]\n\t\tentry.push(chunk)\n\t}\n\t// for small blocks, avoiding the overhead of the extract call is helpful\n\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\tif (string != null)\n\t\treturn entry.string = string\n\treturn entry.string = readFixedString(length)\n}\n\nfunction asSafeString(property) {\n\t// protect against expensive (DoS) string conversions\n\tif (typeof property === 'string') return property;\n\tif (typeof property === 'number' || typeof property === 'boolean' || typeof property === 'bigint') return property.toString();\n\tif (property == null) return property + '';\n\tif (currentUnpackr.allowArraysInMapKeys && Array.isArray(property) && property.flat().every(item => ['string', 'number', 'boolean', 'bigint'].includes(typeof item))) {\n\t\treturn property.flat().toString();\n\t}\n\tthrow new Error(`Invalid property type for record: ${typeof property}`);\n}\n// the registration of the record definition extension (as \"r\")\nconst recordDefinition = (id, highByte) => {\n\tlet structure = read().map(asSafeString) // ensure that all keys are strings and\n\t// that the array is mutable\n\tlet firstByte = id\n\tif (highByte !== undefined) {\n\t\tid = id < 32 ? -((highByte << 5) + id) : ((highByte << 5) + id)\n\t\tstructure.highByte = highByte\n\t}\n\tlet existingStructure = currentStructures[id]\n\t// If it is a shared structure, we need to restore any changes after reading.\n\t// Also in sequential mode, we may get incomplete reads and thus errors, and we need to restore\n\t// to the state prior to an incomplete read in order to properly resume.\n\tif (existingStructure && (existingStructure.isShared || sequentialMode)) {\n\t\t(currentStructures.restoreStructures || (currentStructures.restoreStructures = []))[id] = existingStructure\n\t}\n\tcurrentStructures[id] = structure\n\tstructure.read = createStructureReader(structure, firstByte)\n\treturn structure.read()\n}\ncurrentExtensions[0] = () => {} // notepack defines extension 0 to mean undefined, so use that as the default here\ncurrentExtensions[0].noBuffer = true\n\ncurrentExtensions[0x42] = (data) => {\n\t// decode bigint\n\tlet length = data.length;\n\tlet value = BigInt(data[0] & 0x80 ? data[0] - 0x100 : data[0]);\n\tfor (let i = 1; i < length; i++) {\n\t\tvalue <<= BigInt(8);\n\t\tvalue += BigInt(data[i]);\n\t}\n\treturn value;\n}\n\nlet errors = { Error, TypeError, ReferenceError };\ncurrentExtensions[0x65] = () => {\n\tlet data = read()\n\treturn (errors[data[0]] || Error)(data[1], { cause: data[2] })\n}\n\ncurrentExtensions[0x69] = (data) => {\n\t// id extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tif (!referenceMap)\n\t\treferenceMap = new Map()\n\tlet token = src[position]\n\tlet target\n\t// TODO: handle any other types that can cycle and make the code more robust if there are other extensions\n\tif (token >= 0x90 && token < 0xa0 || token == 0xdc || token == 0xdd)\n\t\ttarget = []\n\telse if (token >= 0x80 && token < 0x90 || token == 0xde || token == 0xdf)\n\t\ttarget = new Map()\n\telse if ((token >= 0xc7 && token <= 0xc9 || token >= 0xd4 && token <= 0xd8) && src[position + 1] === 0x73)\n\t\ttarget = new Set()\n\telse\n\t\ttarget = {}\n\n\tlet refEntry = { target } // a placeholder object\n\treferenceMap.set(id, refEntry)\n\tlet targetProperties = read() // read the next value as the target object to id\n\tif (!refEntry.used) {\n\t\t// no cycle, can just use the returned read object\n\t\treturn refEntry.target = targetProperties // replace the placeholder with the real one\n\t} else {\n\t\t// there is a cycle, so we have to assign properties to original target\n\t\tObject.assign(target, targetProperties)\n\t}\n\n\t// copy over map/set entries if we're able to\n\tif (target instanceof Map)\n\t\tfor (let [k, v] of targetProperties.entries()) target.set(k, v)\n\tif (target instanceof Set)\n\t\tfor (let i of Array.from(targetProperties)) target.add(i)\n\treturn target\n}\n\ncurrentExtensions[0x70] = (data) => {\n\t// pointer extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tlet refEntry = referenceMap.get(id)\n\trefEntry.used = true\n\treturn refEntry.target\n}\n\ncurrentExtensions[0x73] = () => new Set(read())\n\nexport const typedArrays = ['Int8','Uint8','Uint8Clamped','Int16','Uint16','Int32','Uint32','Float32','Float64','BigInt64','BigUint64'].map(type => type + 'Array')\n\nlet glbl = typeof globalThis === 'object' ? globalThis : window;\ncurrentExtensions[0x74] = (data) => {\n\tlet typeCode = data[0]\n\t// we always have to slice to get a new ArrayBuffer that is aligned\n\tlet buffer = Uint8Array.prototype.slice.call(data, 1).buffer\n\n\tlet typedArrayName = typedArrays[typeCode]\n\tif (!typedArrayName) {\n\t\tif (typeCode === 16) return buffer\n\t\tif (typeCode === 17) return new DataView(buffer)\n\t\tthrow new Error('Could not find typed array for code ' + typeCode)\n\t}\n\treturn new glbl[typedArrayName](buffer)\n}\ncurrentExtensions[0x78] = () => {\n\tlet data = read()\n\treturn new RegExp(data[0], data[1])\n}\nconst TEMP_BUNDLE = []\ncurrentExtensions[0x62] = (data) => {\n\tlet dataSize = (data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]\n\tlet dataPosition = position\n\tposition += dataSize - data.length\n\tbundledStrings = TEMP_BUNDLE\n\tbundledStrings = [readOnlyJSString(), readOnlyJSString()]\n\tbundledStrings.position0 = 0\n\tbundledStrings.position1 = 0\n\tbundledStrings.postBundlePosition = position\n\tposition = dataPosition\n\treturn read()\n}\n\ncurrentExtensions[0xff] = (data) => {\n\t// 32-bit date extension\n\tif (data.length == 4)\n\t\treturn new Date((data[0] * 0x1000000 + (data[1] << 16) + (data[2] << 8) + data[3]) * 1000)\n\telse if (data.length == 8)\n\t\treturn new Date(\n\t\t\t((data[0] << 22) + (data[1] << 14) + (data[2] << 6) + (data[3] >> 2)) / 1000000 +\n\t\t\t((data[3] & 0x3) * 0x100000000 + data[4] * 0x1000000 + (data[5] << 16) + (data[6] << 8) + data[7]) * 1000)\n\telse if (data.length == 12)\n\t\treturn new Date(\n\t\t\t((data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]) / 1000000 +\n\t\t\t(((data[4] & 0x80) ? -0x1000000000000 : 0) + data[6] * 0x10000000000 + data[7] * 0x100000000 + data[8] * 0x1000000 + (data[9] << 16) + (data[10] << 8) + data[11]) * 1000)\n\telse\n\t\treturn new Date('invalid')\n}\n// registration of bulk record definition?\n// currentExtensions[0x52] = () =>\n\nfunction saveState(callback) {\n\tif (onSaveState)\n\t\tonSaveState();\n\tlet savedSrcEnd = srcEnd\n\tlet savedPosition = position\n\tlet savedStringPosition = stringPosition\n\tlet savedSrcStringStart = srcStringStart\n\tlet savedSrcStringEnd = srcStringEnd\n\tlet savedSrcString = srcString\n\tlet savedStrings = strings\n\tlet savedReferenceMap = referenceMap\n\tlet savedBundledStrings = bundledStrings\n\n\t// TODO: We may need to revisit this if we do more external calls to user code (since it could be slow)\n\tlet savedSrc = new Uint8Array(src.slice(0, srcEnd)) // we copy the data in case it changes while external data is processed\n\tlet savedStructures = currentStructures\n\tlet savedStructuresContents = currentStructures.slice(0, currentStructures.length)\n\tlet savedPackr = currentUnpackr\n\tlet savedSequentialMode = sequentialMode\n\tlet value = callback()\n\tsrcEnd = savedSrcEnd\n\tposition = savedPosition\n\tstringPosition = savedStringPosition\n\tsrcStringStart = savedSrcStringStart\n\tsrcStringEnd = savedSrcStringEnd\n\tsrcString = savedSrcString\n\tstrings = savedStrings\n\treferenceMap = savedReferenceMap\n\tbundledStrings = savedBundledStrings\n\tsrc = savedSrc\n\tsequentialMode = savedSequentialMode\n\tcurrentStructures = savedStructures\n\tcurrentStructures.splice(0, currentStructures.length, ...savedStructuresContents)\n\tcurrentUnpackr = savedPackr\n\tdataView = new DataView(src.buffer, src.byteOffset, src.byteLength)\n\treturn value\n}\nexport function clearSource() {\n\tsrc = null\n\treferenceMap = null\n\tcurrentStructures = null\n}\n\nexport function addExtension(extension) {\n\tif (extension.unpack)\n\t\tcurrentExtensions[extension.type] = extension.unpack\n\telse\n\t\tcurrentExtensions[extension.type] = extension\n}\n\nexport const mult10 = new Array(147) // this is a table matching binary exponents to the multiplier to determine significant digit rounding\nfor (let i = 0; i < 256; i++) {\n\tmult10[i] = +('1e' + Math.floor(45.15 - i * 0.30103))\n}\nexport const Decoder = Unpackr\nvar defaultUnpackr = new Unpackr({ useRecords: false })\nexport const unpack = defaultUnpackr.unpack\nexport const unpackMultiple = defaultUnpackr.unpackMultiple\nexport const decode = defaultUnpackr.unpack\nexport const FLOAT32_OPTIONS = {\n\tNEVER: 0,\n\tALWAYS: 1,\n\tDECIMAL_ROUND: 3,\n\tDECIMAL_FIT: 4\n}\nlet f32Array = new Float32Array(1)\nlet u8Array = new Uint8Array(f32Array.buffer, 0, 4)\nexport function roundFloat32(float32Number) {\n\tf32Array[0] = float32Number\n\tlet multiplier = mult10[((u8Array[3] & 0x7f) << 1) | (u8Array[2] >> 7)]\n\treturn ((multiplier * float32Number + (float32Number > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n}\nexport function setReadStruct(updatedReadStruct, loadedStructs, saveState) {\n\treadStruct = updatedReadStruct;\n\tonLoadedStructures = loadedStructs;\n\tonSaveState = saveState;\n}\n", "import { Unpackr, mult10, C1Type, typedArrays, addExtension as unpackAddExtension } from './unpack.js'\nlet textEncoder\ntry {\n\ttextEncoder = new TextEncoder()\n} catch (error) {}\nlet extensions, extensionClasses\nconst hasNodeBuffer = typeof Buffer !== 'undefined'\nconst ByteArrayAllocate = hasNodeBuffer ?\n\tfunction(length) { return Buffer.allocUnsafeSlow(length) } : Uint8Array\nconst ByteArray = hasNodeBuffer ? Buffer : Uint8Array\nconst MAX_BUFFER_SIZE = hasNodeBuffer ? 0x100000000 : 0x7fd00000\nlet target, keysTarget\nlet targetView\nlet position = 0\nlet safeEnd\nlet bundledStrings = null\nlet writeStructSlots\nconst MAX_BUNDLE_SIZE = 0x5500 // maximum characters such that the encoded bytes fits in 16 bits.\nconst hasNonLatin = /[\\u0080-\\uFFFF]/\nexport const RECORD_SYMBOL = Symbol('record-id')\nexport class Packr extends Unpackr {\n\tconstructor(options) {\n\t\tsuper(options)\n\t\tthis.offset = 0\n\t\tlet typeBuffer\n\t\tlet start\n\t\tlet hasSharedUpdate\n\t\tlet structures\n\t\tlet referenceMap\n\t\tlet encodeUtf8 = ByteArray.prototype.utf8Write ? function(string, position) {\n\t\t\treturn target.utf8Write(string, position, target.byteLength - position)\n\t\t} : (textEncoder && textEncoder.encodeInto) ?\n\t\t\tfunction(string, position) {\n\t\t\t\treturn textEncoder.encodeInto(string, target.subarray(position)).written\n\t\t\t} : false\n\n\t\tlet packr = this\n\t\tif (!options)\n\t\t\toptions = {}\n\t\tlet isSequential = options && options.sequential\n\t\tlet hasSharedStructures = options.structures || options.saveStructures\n\t\tlet maxSharedStructures = options.maxSharedStructures\n\t\tif (maxSharedStructures == null)\n\t\t\tmaxSharedStructures = hasSharedStructures ? 32 : 0\n\t\tif (maxSharedStructures > 8160)\n\t\t\tthrow new Error('Maximum maxSharedStructure is 8160')\n\t\tif (options.structuredClone && options.moreTypes == undefined) {\n\t\t\tthis.moreTypes = true\n\t\t}\n\t\tlet maxOwnStructures = options.maxOwnStructures\n\t\tif (maxOwnStructures == null)\n\t\t\tmaxOwnStructures = hasSharedStructures ? 32 : 64\n\t\tif (!this.structures && options.useRecords != false)\n\t\t\tthis.structures = []\n\t\t// two byte record ids for shared structures\n\t\tlet useTwoByteRecords = maxSharedStructures > 32 || (maxOwnStructures + maxSharedStructures > 64)\n\t\tlet sharedLimitId = maxSharedStructures + 0x40\n\t\tlet maxStructureId = maxSharedStructures + maxOwnStructures + 0x40\n\t\tif (maxStructureId > 8256) {\n\t\t\tthrow new Error('Maximum maxSharedStructure + maxOwnStructure is 8192')\n\t\t}\n\t\tlet recordIdsToRemove = []\n\t\tlet transitionsCount = 0\n\t\tlet serializationsSinceTransitionRebuild = 0\n\n\t\tthis.pack = this.encode = function(value, encodeOptions) {\n\t\t\tif (!target) {\n\t\t\t\ttarget = new ByteArrayAllocate(8192)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, 8192))\n\t\t\t\tposition = 0\n\t\t\t}\n\t\t\tsafeEnd = target.length - 10\n\t\t\tif (safeEnd - position < 0x800) {\n\t\t\t\t// don't start too close to the end,\n\t\t\t\ttarget = new ByteArrayAllocate(target.length)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, target.length))\n\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\tposition = 0\n\t\t\t} else\n\t\t\t\tposition = (position + 7) & 0x7ffffff8 // Word align to make any future copying of this buffer faster\n\t\t\tstart = position\n\t\t\tif (encodeOptions & RESERVE_START_SPACE) position += (encodeOptions & 0xff)\n\t\t\treferenceMap = packr.structuredClone ? new Map() : null\n\t\t\tif (packr.bundleStrings && typeof value !== 'string') {\n\t\t\t\tbundledStrings = []\n\t\t\t\tbundledStrings.size = Infinity // force a new bundle start on first string\n\t\t\t} else\n\t\t\t\tbundledStrings = null\n\t\t\tstructures = packr.structures\n\t\t\tif (structures) {\n\t\t\t\tif (structures.uninitialized)\n\t\t\t\t\tstructures = packr._mergeStructures(packr.getStructures())\n\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\tif (sharedLength > maxSharedStructures) {\n\t\t\t\t\t//if (maxSharedStructures <= 32 && structures.sharedLength > 32) // TODO: could support this, but would need to update the limit ids\n\t\t\t\t\tthrow new Error('Shared structures is larger than maximum shared structures, try increasing maxSharedStructures to ' + structures.sharedLength)\n\t\t\t\t}\n\t\t\t\tif (!structures.transitions) {\n\t\t\t\t\t// rebuild our structure transitions\n\t\t\t\t\tstructures.transitions = Object.create(null)\n\t\t\t\t\tfor (let i = 0; i < sharedLength; i++) {\n\t\t\t\t\t\tlet keys = structures[i]\n\t\t\t\t\t\tif (!keys)\n\t\t\t\t\t\t\tcontinue\n\t\t\t\t\t\tlet nextTransition, transition = structures.transitions\n\t\t\t\t\t\tfor (let j = 0, l = keys.length; j < l; j++) {\n\t\t\t\t\t\t\tlet key = keys[j]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttransition[RECORD_SYMBOL] = i + 0x40\n\t\t\t\t\t}\n\t\t\t\t\tthis.lastNamedStructuresLength = sharedLength\n\t\t\t\t}\n\t\t\t\tif (!isSequential) {\n\t\t\t\t\tstructures.nextId = sharedLength + 0x40\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (hasSharedUpdate)\n\t\t\t\thasSharedUpdate = false\n\t\t\tlet encodingError;\n\t\t\ttry {\n\t\t\t\tif (packr.randomAccessStructure && value && value.constructor && value.constructor === Object)\n\t\t\t\t\twriteStruct(value);\n\t\t\t\telse\n\t\t\t\t\tpack(value)\n\t\t\t\tlet lastBundle = bundledStrings;\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\twriteBundles(start, pack, 0)\n\t\t\t\tif (referenceMap && referenceMap.idsToInsert) {\n\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert.sort((a, b) => a.offset > b.offset ? 1 : -1);\n\t\t\t\t\tlet i = idsToInsert.length;\n\t\t\t\t\tlet incrementPosition = -1;\n\t\t\t\t\twhile (lastBundle && i > 0) {\n\t\t\t\t\t\tlet insertionPoint = idsToInsert[--i].offset + start;\n\t\t\t\t\t\tif (insertionPoint < (lastBundle.stringsPosition + start) && incrementPosition === -1)\n\t\t\t\t\t\t\tincrementPosition = 0;\n\t\t\t\t\t\tif (insertionPoint > (lastBundle.position + start)) {\n\t\t\t\t\t\t\tif (incrementPosition >= 0)\n\t\t\t\t\t\t\t\tincrementPosition += 6;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (incrementPosition >= 0) {\n\t\t\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t\t\t\tincrementPosition = -1; // reset\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlastBundle = lastBundle.previous;\n\t\t\t\t\t\t\ti++;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (incrementPosition >= 0 && lastBundle) {\n\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t}\n\t\t\t\t\tposition += idsToInsert.length * 6;\n\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\tpackr.offset = position\n\t\t\t\t\tlet serialized = insertIds(target.subarray(start, position), idsToInsert)\n\t\t\t\t\treferenceMap = null\n\t\t\t\t\treturn serialized\n\t\t\t\t}\n\t\t\t\tpackr.offset = position // update the offset so next serialization doesn't write over our buffer, but can continue writing to same buffer sequentially\n\t\t\t\tif (encodeOptions & REUSE_BUFFER_MODE) {\n\t\t\t\t\ttarget.start = start\n\t\t\t\t\ttarget.end = position\n\t\t\t\t\treturn target\n\t\t\t\t}\n\t\t\t\treturn target.subarray(start, position) // position can change if we call pack again in saveStructures, so we get the buffer now\n\t\t\t} catch(error) {\n\t\t\t\tencodingError = error;\n\t\t\t\tthrow error;\n\t\t\t} finally {\n\t\t\t\tif (structures) {\n\t\t\t\t\tresetStructures();\n\t\t\t\t\tif (hasSharedUpdate && packr.saveStructures) {\n\t\t\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\t\t\t// we can't rely on start/end with REUSE_BUFFER_MODE since they will (probably) change when we save\n\t\t\t\t\t\tlet returnBuffer = target.subarray(start, position)\n\t\t\t\t\t\tlet newSharedData = prepareStructures(structures, packr);\n\t\t\t\t\t\tif (!encodingError) { // TODO: If there is an encoding error, should make the structures as uninitialized so they get rebuilt next time\n\t\t\t\t\t\t\tif (packr.saveStructures(newSharedData, newSharedData.isCompatible) === false) {\n\t\t\t\t\t\t\t\t// get updated structures and try again if the update failed\n\t\t\t\t\t\t\t\treturn packr.pack(value, encodeOptions)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpackr.lastNamedStructuresLength = sharedLength\n\t\t\t\t\t\t\t// don't keep large buffers around\n\t\t\t\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\t\t\t\treturn returnBuffer\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// don't keep large buffers around, they take too much memory and cause problems (limit at 1GB)\n\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\tif (encodeOptions & RESET_BUFFER_MODE)\n\t\t\t\t\tposition = start\n\t\t\t}\n\t\t}\n\t\tconst resetStructures = () => {\n\t\t\tif (serializationsSinceTransitionRebuild < 10)\n\t\t\t\tserializationsSinceTransitionRebuild++\n\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\tif (structures.length > sharedLength && !isSequential)\n\t\t\t\tstructures.length = sharedLength\n\t\t\tif (transitionsCount > 10000) {\n\t\t\t\t// force a rebuild occasionally after a lot of transitions so it can get cleaned up\n\t\t\t\tstructures.transitions = null\n\t\t\t\tserializationsSinceTransitionRebuild = 0\n\t\t\t\ttransitionsCount = 0\n\t\t\t\tif (recordIdsToRemove.length > 0)\n\t\t\t\t\trecordIdsToRemove = []\n\t\t\t} else if (recordIdsToRemove.length > 0 && !isSequential) {\n\t\t\t\tfor (let i = 0, l = recordIdsToRemove.length; i < l; i++) {\n\t\t\t\t\trecordIdsToRemove[i][RECORD_SYMBOL] = 0\n\t\t\t\t}\n\t\t\t\trecordIdsToRemove = []\n\t\t\t}\n\t\t}\n\t\tconst packArray = (value) => {\n\t\t\tvar length = value.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x90 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xdc\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdd\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\tpack(value[i])\n\t\t\t}\n\t\t}\n\t\tconst pack = (value) => {\n\t\t\tif (position > safeEnd)\n\t\t\t\ttarget = makeRoom(position)\n\n\t\t\tvar type = typeof value\n\t\t\tvar length\n\t\t\tif (type === 'string') {\n\t\t\t\tlet strLength = value.length\n\t\t\t\tif (bundledStrings && strLength >= 4 && strLength < 0x1000) {\n\t\t\t\t\tif ((bundledStrings.size += strLength) > MAX_BUNDLE_SIZE) {\n\t\t\t\t\t\tlet extStart\n\t\t\t\t\t\tlet maxBytes = (bundledStrings[0] ? bundledStrings[0].length * 3 + bundledStrings[1].length : 0) + 10\n\t\t\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\t\t\t\t\t\tlet lastBundle\n\t\t\t\t\t\tif (bundledStrings.position) { // here we use the 0x62 extension to write the last bundle and reserve space for the reference pointer to the next/current bundle\n\t\t\t\t\t\t\tlastBundle = bundledStrings\n\t\t\t\t\t\t\ttarget[position] = 0xc8 // ext 16\n\t\t\t\t\t\t\tposition += 3 // reserve for the writing bundle size\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t\twriteBundles(start, pack, 0) // write the last bundles\n\t\t\t\t\t\t\ttargetView.setUint16(extStart + start - 3, position - start - extStart)\n\t\t\t\t\t\t} else { // here we use the 0x62 extension just to reserve the space for the reference pointer to the bundle (will be updated once the bundle is written)\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbundledStrings = ['', ''] // create new ones\n\t\t\t\t\t\tbundledStrings.previous = lastBundle;\n\t\t\t\t\t\tbundledStrings.size = 0\n\t\t\t\t\t\tbundledStrings.position = extStart\n\t\t\t\t\t}\n\t\t\t\t\tlet twoByte = hasNonLatin.test(value)\n\t\t\t\t\tbundledStrings[twoByte ? 0 : 1] += value\n\t\t\t\t\ttarget[position++] = 0xc1\n\t\t\t\t\tpack(twoByte ? -strLength : strLength);\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet headerSize\n\t\t\t\t// first we estimate the header size, so we can write to the correct location\n\t\t\t\tif (strLength < 0x20) {\n\t\t\t\t\theaderSize = 1\n\t\t\t\t} else if (strLength < 0x100) {\n\t\t\t\t\theaderSize = 2\n\t\t\t\t} else if (strLength < 0x10000) {\n\t\t\t\t\theaderSize = 3\n\t\t\t\t} else {\n\t\t\t\t\theaderSize = 5\n\t\t\t\t}\n\t\t\t\tlet maxBytes = strLength * 3\n\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\n\t\t\t\tif (strLength < 0x40 || !encodeUtf8) {\n\t\t\t\t\tlet i, c1, c2, strPosition = position + headerSize\n\t\t\t\t\tfor (i = 0; i < strLength; i++) {\n\t\t\t\t\t\tc1 = value.charCodeAt(i)\n\t\t\t\t\t\tif (c1 < 0x80) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1\n\t\t\t\t\t\t} else if (c1 < 0x800) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 | 0xc0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t(c1 & 0xfc00) === 0xd800 &&\n\t\t\t\t\t\t\t((c2 = value.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tc1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff)\n\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 18 | 0xf0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 | 0xe0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlength = strPosition - position - headerSize\n\t\t\t\t} else {\n\t\t\t\t\tlength = encodeUtf8(value, position + headerSize)\n\t\t\t\t}\n\n\t\t\t\tif (length < 0x20) {\n\t\t\t\t\ttarget[position++] = 0xa0 | length\n\t\t\t\t} else if (length < 0x100) {\n\t\t\t\t\tif (headerSize < 2) {\n\t\t\t\t\t\ttarget.copyWithin(position + 2, position + 1, position + 1 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\t\ttarget[position++] = length\n\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\tif (headerSize < 3) {\n\t\t\t\t\t\ttarget.copyWithin(position + 3, position + 2, position + 2 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xda\n\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t} else {\n\t\t\t\t\tif (headerSize < 5) {\n\t\t\t\t\t\ttarget.copyWithin(position + 5, position + 3, position + 3 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xdb\n\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\tposition += 4\n\t\t\t\t}\n\t\t\t\tposition += length\n\t\t\t} else if (type === 'number') {\n\t\t\t\tif (value >>> 0 === value) {// positive integer, 32-bit or less\n\t\t\t\t\t// positive uint\n\t\t\t\t\tif (value < 0x20 || (value < 0x80 && this.useRecords === false) || (value < 0x40 && !this.randomAccessStructure)) {\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x100) {\n\t\t\t\t\t\ttarget[position++] = 0xcc\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x10000) {\n\t\t\t\t\t\ttarget[position++] = 0xcd\n\t\t\t\t\t\ttarget[position++] = value >> 8\n\t\t\t\t\t\ttarget[position++] = value & 0xff\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xce\n\t\t\t\t\t\ttargetView.setUint32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else if (value >> 0 === value) { // negative integer\n\t\t\t\t\tif (value >= -0x20) {\n\t\t\t\t\t\ttarget[position++] = 0x100 + value\n\t\t\t\t\t} else if (value >= -0x80) {\n\t\t\t\t\t\ttarget[position++] = 0xd0\n\t\t\t\t\t\ttarget[position++] = value + 0x100\n\t\t\t\t\t} else if (value >= -0x8000) {\n\t\t\t\t\t\ttarget[position++] = 0xd1\n\t\t\t\t\t\ttargetView.setInt16(position, value)\n\t\t\t\t\t\tposition += 2\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xd2\n\t\t\t\t\t\ttargetView.setInt32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet useFloat32\n\t\t\t\t\tif ((useFloat32 = this.useFloat32) > 0 && value < 0x100000000 && value >= -0x80000000) {\n\t\t\t\t\t\ttarget[position++] = 0xca\n\t\t\t\t\t\ttargetView.setFloat32(position, value)\n\t\t\t\t\t\tlet xShifted\n\t\t\t\t\t\tif (useFloat32 < 4 ||\n\t\t\t\t\t\t\t\t// this checks for rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\t\t((xShifted = value * mult10[((target[position] & 0x7f) << 1) | (target[position + 1] >> 7)]) >> 0) === xShifted) {\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\tposition-- // move back into position for writing a double\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\ttargetView.setFloat64(position, value)\n\t\t\t\t\tposition += 8\n\t\t\t\t}\n\t\t\t} else if (type === 'object' || type === 'function') {\n\t\t\t\tif (!value)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\tif (referenceMap) {\n\t\t\t\t\t\tlet referee = referenceMap.get(value)\n\t\t\t\t\t\tif (referee) {\n\t\t\t\t\t\t\tif (!referee.id) {\n\t\t\t\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert || (referenceMap.idsToInsert = [])\n\t\t\t\t\t\t\t\treferee.id = idsToInsert.push(referee)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x70 // \"p\" for pointer\n\t\t\t\t\t\t\ttargetView.setUint32(position, referee.id)\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treferenceMap.set(value, { offset: position - start })\n\t\t\t\t\t}\n\t\t\t\t\tlet constructor = value.constructor\n\t\t\t\t\tif (constructor === Object) {\n\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t} else if (constructor === Array) {\n\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t} else if (constructor === Map) {\n\t\t\t\t\t\tif (this.mapAsEmptyObject) target[position++] = 0x80\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tlength = value.size\n\t\t\t\t\t\t\tif (length < 0x10) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xde\n\t\t\t\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tfor (let [key, entryValue] of value) {\n\t\t\t\t\t\t\t\tpack(key)\n\t\t\t\t\t\t\t\tpack(entryValue)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfor (let i = 0, l = extensions.length; i < l; i++) {\n\t\t\t\t\t\t\tlet extensionClass = extensionClasses[i]\n\t\t\t\t\t\t\tif (value instanceof extensionClass) {\n\t\t\t\t\t\t\t\tlet extension = extensions[i]\n\t\t\t\t\t\t\t\tif (extension.write) {\n\t\t\t\t\t\t\t\t\tif (extension.type) {\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0xd4 // one byte \"tag\" extension\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = extension.type\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlet writeResult = extension.write.call(this, value)\n\t\t\t\t\t\t\t\t\tif (writeResult === value) { // avoid infinite recursion\n\t\t\t\t\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tpack(writeResult)\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlet currentTarget = target\n\t\t\t\t\t\t\t\tlet currentTargetView = targetView\n\t\t\t\t\t\t\t\tlet currentPosition = position\n\t\t\t\t\t\t\t\ttarget = null\n\t\t\t\t\t\t\t\tlet result\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tresult = extension.pack.call(this, value, (size) => {\n\t\t\t\t\t\t\t\t\t\t// restore target and use it\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\tcurrentTarget = null\n\t\t\t\t\t\t\t\t\t\tposition += size\n\t\t\t\t\t\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\ttarget, targetView, position: position - size\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}, pack)\n\t\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\t\t// restore current target information (unless already restored)\n\t\t\t\t\t\t\t\t\tif (currentTarget) {\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\ttargetView = currentTargetView\n\t\t\t\t\t\t\t\t\t\tposition = currentPosition\n\t\t\t\t\t\t\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (result) {\n\t\t\t\t\t\t\t\t\tif (result.length + position > safeEnd)\n\t\t\t\t\t\t\t\t\t\tmakeRoom(result.length + position)\n\t\t\t\t\t\t\t\t\tposition = writeExtensionData(result, target, position, extension.type)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// check isArray after extensions, because extensions can extend Array\n\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// use this as an alternate mechanism for expressing how to serialize\n\t\t\t\t\t\t\tif (value.toJSON) {\n\t\t\t\t\t\t\t\tconst json = value.toJSON()\n\t\t\t\t\t\t\t\t// if for some reason value.toJSON returns itself it'll loop forever\n\t\t\t\t\t\t\t\tif (json !== value)\n\t\t\t\t\t\t\t\t\treturn pack(json)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// if there is a writeFunction, use it, otherwise just encode as undefined\n\t\t\t\t\t\t\tif (type === 'function')\n\t\t\t\t\t\t\t\treturn pack(this.writeFunction && this.writeFunction(value));\n\n\t\t\t\t\t\t\t// no extension found, write as plain object\n\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (type === 'boolean') {\n\t\t\t\ttarget[position++] = value ? 0xc3 : 0xc2\n\t\t\t} else if (type === 'bigint') {\n\t\t\t\tif (value < 0x8000000000000000 && value >= -0x8000000000000000) {\n\t\t\t\t\t// use a signed int as long as it fits\n\t\t\t\t\ttarget[position++] = 0xd3\n\t\t\t\t\ttargetView.setBigInt64(position, value)\n\t\t\t\t} else if (value < 0x10000000000000000 && value > 0) {\n\t\t\t\t\t// if we can fit an unsigned int, use that\n\t\t\t\t\ttarget[position++] = 0xcf\n\t\t\t\t\ttargetView.setBigUint64(position, value)\n\t\t\t\t} else {\n\t\t\t\t\t// overflow\n\t\t\t\t\tif (this.largeBigIntToFloat) {\n\t\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\t\ttargetView.setFloat64(position, Number(value))\n\t\t\t\t\t} else if (this.largeBigIntToString) {\n\t\t\t\t\t\treturn pack(value.toString());\n\t\t\t\t\t} else if ((this.useBigIntExtension || this.moreTypes) && value < BigInt(2)**BigInt(1023) && value > -(BigInt(2)**BigInt(1023))) {\n\t\t\t\t\t\ttarget[position++] = 0xc7\n\t\t\t\t\t\tposition++;\n\t\t\t\t\t\ttarget[position++] = 0x42 // \"B\" for BigInt\n\t\t\t\t\t\tlet bytes = [];\n\t\t\t\t\t\tlet alignedSign;\n\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\tlet byte = value & BigInt(0xff);\n\t\t\t\t\t\t\talignedSign = (byte & BigInt(0x80)) === (value < BigInt(0) ? BigInt(0x80) : BigInt(0));\n\t\t\t\t\t\t\tbytes.push(byte);\n\t\t\t\t\t\t\tvalue >>= BigInt(8);\n\t\t\t\t\t\t} while (!((value === BigInt(0) || value === BigInt(-1)) && alignedSign));\n\t\t\t\t\t\ttarget[position-2] = bytes.length;\n\t\t\t\t\t\tfor (let i = bytes.length; i > 0;) {\n\t\t\t\t\t\t\ttarget[position++] = Number(bytes[--i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new RangeError(value + ' was too large to fit in MessagePack 64-bit integer format, use' +\n\t\t\t\t\t\t\t' useBigIntExtension, or set largeBigIntToFloat to convert to float-64, or set' +\n\t\t\t\t\t\t\t' largeBigIntToString to convert to string')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tposition += 8\n\t\t\t} else if (type === 'undefined') {\n\t\t\t\tif (this.encodeUndefinedAsNil)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\ttarget[position++] = 0xd4 // a number of implementations use fixext1 with type 0, data 0 to denote undefined, so we follow suite\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow new Error('Unknown type: ' + type)\n\t\t\t}\n\t\t}\n\n\t\tconst writePlainObject = (this.variableMapSize || this.coercibleKeyAsNumber || this.skipValues) ? (object) => {\n\t\t\t// this method is slightly slower, but generates \"preferred serialization\" (optimally small for smaller objects)\n\t\t\tlet keys;\n\t\t\tif (this.skipValues) {\n\t\t\t\tkeys = [];\n\t\t\t\tfor (let key in object) {\n\t\t\t\t\tif ((typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) &&\n\t\t\t\t\t\t!this.skipValues.includes(object[key]))\n\t\t\t\t\t\tkeys.push(key);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tkeys = Object.keys(object)\n\t\t\t}\n\t\t\tlet length = keys.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xde\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tlet key\n\t\t\tif (this.coercibleKeyAsNumber) {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tkey = keys[i]\n\t\t\t\t\tlet num = Number(key)\n\t\t\t\t\tpack(isNaN(num) ? key : num)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\n\t\t\t} else {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tpack(key = keys[i])\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\ttarget[position++] = 0xde // always using map 16, so we can preallocate and set the length afterwards\n\t\t\tlet objectOffset = position - start\n\t\t\tposition += 2\n\t\t\tlet size = 0\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(key)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t\tsize++\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (size > 0xffff) {\n\t\t\t\tthrow new Error('Object is too large to serialize with fast 16-bit map size,' +\n\t\t\t\t' use the \"variableMapSize\" option to serialize this object');\n\t\t\t}\n\t\t\ttarget[objectOffset++ + start] = size >> 8\n\t\t\ttarget[objectOffset + start] = size & 0xff\n\t\t}\n\n\t\tconst writeRecord = this.useRecords === false ? writePlainObject :\n\t\t(options.progressiveRecords && !useTwoByteRecords) ?  // this is about 2% faster for highly stable structures, since it only requires one for-in loop (but much more expensive when new structure needs to be written)\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet objectOffset = position++ - start\n\t\t\tlet wroteKeys\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\tif (nextTransition)\n\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\telse {\n\t\t\t\t\t\t// record doesn't exist, create full new record and insert it\n\t\t\t\t\t\tlet keys = Object.keys(object)\n\t\t\t\t\t\tlet lastTransition = transition\n\t\t\t\t\t\ttransition = structures.transitions\n\t\t\t\t\t\tlet newTransitions = 0\n\t\t\t\t\t\tfor (let i = 0, l = keys.length; i < l; i++) {\n\t\t\t\t\t\t\tlet key = keys[i]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t\tnewTransitions++\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (objectOffset + start + 1 == position) {\n\t\t\t\t\t\t\t// first key, so we don't need to insert, we can just write record directly\n\t\t\t\t\t\t\tposition--\n\t\t\t\t\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\t\t\t\t} else // otherwise we need to insert the record, moving existing data after the record\n\t\t\t\t\t\t\tinsertNewRecord(transition, keys, objectOffset, newTransitions)\n\t\t\t\t\t\twroteKeys = true\n\t\t\t\t\t\ttransition = lastTransition[key]\n\t\t\t\t\t}\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!wroteKeys) {\n\t\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\t\tif (recordId)\n\t\t\t\t\ttarget[objectOffset + start] = recordId\n\t\t\t\telse\n\t\t\t\t\tinsertNewRecord(transition, Object.keys(object), objectOffset, 0)\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet newTransitions = 0\n\t\t\tfor (let key in object) if (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\tnextTransition = transition[key]\n\t\t\t\tif (!nextTransition) {\n\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\tnewTransitions++\n\t\t\t\t}\n\t\t\t\ttransition = nextTransition\n\t\t\t}\n\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\tif (recordId) {\n\t\t\t\tif (recordId >= 0x60 && useTwoByteRecords) {\n\t\t\t\t\ttarget[position++] = ((recordId -= 0x60) & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = recordId >> 5\n\t\t\t\t} else\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t} else {\n\t\t\t\tnewRecord(transition, transition.__keys__ || Object.keys(object), newTransitions)\n\t\t\t}\n\t\t\t// now write the values\n\t\t\tfor (let key in object)\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t}\n\n\t\t// create reference to useRecords if useRecords is a function\n\t\tconst checkUseRecords = typeof this.useRecords == 'function' && this.useRecords;\n\n\t\tconst writeObject = checkUseRecords ? (object) => {\n\t\t\tcheckUseRecords(object) ? writeRecord(object) : writePlainObject(object)\n\t\t} : writeRecord\n\n\t\tconst makeRoom = (end) => {\n\t\t\tlet newSize\n\t\t\tif (end > 0x1000000) {\n\t\t\t\t// special handling for really large buffers\n\t\t\t\tif ((end - start) > MAX_BUFFER_SIZE)\n\t\t\t\t\tthrow new Error('Packed buffer would be larger than maximum buffer size')\n\t\t\t\tnewSize = Math.min(MAX_BUFFER_SIZE,\n\t\t\t\t\tMath.round(Math.max((end - start) * (end > 0x4000000 ? 1.25 : 2), 0x400000) / 0x1000) * 0x1000)\n\t\t\t} else // faster handling for smaller buffers\n\t\t\t\tnewSize = ((Math.max((end - start) << 2, target.length - 1) >> 12) + 1) << 12\n\t\t\tlet newBuffer = new ByteArrayAllocate(newSize)\n\t\t\ttargetView = newBuffer.dataView || (newBuffer.dataView = new DataView(newBuffer.buffer, 0, newSize))\n\t\t\tend = Math.min(end, target.length)\n\t\t\tif (target.copy)\n\t\t\t\ttarget.copy(newBuffer, 0, start, end)\n\t\t\telse\n\t\t\t\tnewBuffer.set(target.slice(start, end))\n\t\t\tposition -= start\n\t\t\tstart = 0\n\t\t\tsafeEnd = newBuffer.length - 10\n\t\t\treturn target = newBuffer\n\t\t}\n\t\tconst newRecord = (transition, keys, newTransitions) => {\n\t\t\tlet recordId = structures.nextId\n\t\t\tif (!recordId)\n\t\t\t\trecordId = 0x40\n\t\t\tif (recordId < sharedLimitId && this.shouldShareStructure && !this.shouldShareStructure(keys)) {\n\t\t\t\trecordId = structures.nextOwnId\n\t\t\t\tif (!(recordId < maxStructureId))\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextOwnId = recordId + 1\n\t\t\t} else {\n\t\t\t\tif (recordId >= maxStructureId)// cycle back around\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextId = recordId + 1\n\t\t\t}\n\t\t\tlet highByte = keys.highByte = recordId >= 0x60 && useTwoByteRecords ? (recordId - 0x60) >> 5 : -1\n\t\t\ttransition[RECORD_SYMBOL] = recordId\n\t\t\ttransition.__keys__ = keys\n\t\t\tstructures[recordId - 0x40] = keys\n\n\t\t\tif (recordId < sharedLimitId) {\n\t\t\t\tkeys.isShared = true\n\t\t\t\tstructures.sharedLength = recordId - 0x3f\n\t\t\t\thasSharedUpdate = true\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = 0xd5 // fixext 2\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = 0xd4 // fixext 1\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\n\t\t\t\tif (newTransitions)\n\t\t\t\t\ttransitionsCount += serializationsSinceTransitionRebuild * newTransitions\n\t\t\t\t// record the removal of the id, we can maintain our shared structure\n\t\t\t\tif (recordIdsToRemove.length >= maxOwnStructures)\n\t\t\t\t\trecordIdsToRemove.shift()[RECORD_SYMBOL] = 0 // we are cycling back through, and have to remove old ones\n\t\t\t\trecordIdsToRemove.push(transition)\n\t\t\t\tpack(keys)\n\t\t\t}\n\t\t}\n\t\tconst insertNewRecord = (transition, keys, insertionOffset, newTransitions) => {\n\t\t\tlet mainTarget = target\n\t\t\tlet mainPosition = position\n\t\t\tlet mainSafeEnd = safeEnd\n\t\t\tlet mainStart = start\n\t\t\ttarget = keysTarget\n\t\t\tposition = 0\n\t\t\tstart = 0\n\t\t\tif (!target)\n\t\t\t\tkeysTarget = target = new ByteArrayAllocate(8192)\n\t\t\tsafeEnd = target.length - 10\n\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\tkeysTarget = target\n\t\t\tlet keysPosition = position\n\t\t\ttarget = mainTarget\n\t\t\tposition = mainPosition\n\t\t\tsafeEnd = mainSafeEnd\n\t\t\tstart = mainStart\n\t\t\tif (keysPosition > 1) {\n\t\t\t\tlet newEnd = position + keysPosition - 1\n\t\t\t\tif (newEnd > safeEnd)\n\t\t\t\t\tmakeRoom(newEnd)\n\t\t\t\tlet insertionPosition = insertionOffset + start\n\t\t\t\ttarget.copyWithin(insertionPosition + keysPosition, insertionPosition + 1, position)\n\t\t\t\ttarget.set(keysTarget.slice(0, keysPosition), insertionPosition)\n\t\t\t\tposition = newEnd\n\t\t\t} else {\n\t\t\t\ttarget[insertionOffset + start] = keysTarget[0]\n\t\t\t}\n\t\t}\n\t\tconst writeStruct = (object) => {\n\t\t\tlet newPosition = writeStructSlots(object, target, start, position, structures, makeRoom, (value, newPosition, notifySharedUpdate) => {\n\t\t\t\tif (notifySharedUpdate)\n\t\t\t\t\treturn hasSharedUpdate = true;\n\t\t\t\tposition = newPosition;\n\t\t\t\tlet startTarget = target;\n\t\t\t\tpack(value);\n\t\t\t\tresetStructures();\n\t\t\t\tif (startTarget !== target) {\n\t\t\t\t\treturn { position, targetView, target }; // indicate the buffer was re-allocated\n\t\t\t\t}\n\t\t\t\treturn position;\n\t\t\t}, this);\n\t\t\tif (newPosition === 0) // bail and go to a msgpack object\n\t\t\t\treturn writeObject(object);\n\t\t\tposition = newPosition;\n\t\t}\n\t}\n\tuseBuffer(buffer) {\n\t\t// this means we are finished using our own buffer and we can write over it safely\n\t\ttarget = buffer\n\t\ttarget.dataView || (target.dataView = new DataView(target.buffer, target.byteOffset, target.byteLength))\n\t\tposition = 0\n\t}\n\tset position (value) {\n\t\tposition = value;\n\t}\n\tget position() {\n\t\treturn position;\n\t}\n\tclearSharedData() {\n\t\tif (this.structures)\n\t\t\tthis.structures = []\n\t\tif (this.typedStructs)\n\t\t\tthis.typedStructs = []\n\t}\n}\n\nextensionClasses = [ Date, Set, Error, RegExp, ArrayBuffer, Object.getPrototypeOf(Uint8Array.prototype).constructor /*TypedArray*/, DataView, C1Type ]\nextensions = [{\n\tpack(date, allocateForWrite, pack) {\n\t\tlet seconds = date.getTime() / 1000\n\t\tif ((this.useTimestamp32 || date.getMilliseconds() === 0) && seconds >= 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 32\n\t\t\tlet { target, targetView, position} = allocateForWrite(6)\n\t\t\ttarget[position++] = 0xd6\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, seconds)\n\t\t} else if (seconds > 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 64\n\t\t\tlet { target, targetView, position} = allocateForWrite(10)\n\t\t\ttarget[position++] = 0xd7\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 4000000 + ((seconds / 1000 / 0x100000000) >> 0))\n\t\t\ttargetView.setUint32(position + 4, seconds)\n\t\t} else if (isNaN(seconds)) {\n\t\t\tif (this.onInvalidDate) {\n\t\t\t\tallocateForWrite(0)\n\t\t\t\treturn pack(this.onInvalidDate())\n\t\t\t}\n\t\t\t// Intentionally invalid timestamp\n\t\t\tlet { target, targetView, position} = allocateForWrite(3)\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0xff\n\t\t\ttarget[position++] = 0xff\n\t\t} else {\n\t\t\t// Timestamp 96\n\t\t\tlet { target, targetView, position} = allocateForWrite(15)\n\t\t\ttarget[position++] = 0xc7\n\t\t\ttarget[position++] = 12\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 1000000)\n\t\t\ttargetView.setBigInt64(position + 4, BigInt(Math.floor(seconds)))\n\t\t}\n\t}\n}, {\n\tpack(set, allocateForWrite, pack) {\n\t\tif (this.setAsEmptyObject) {\n\t\t\tallocateForWrite(0);\n\t\t\treturn pack({})\n\t\t}\n\t\tlet array = Array.from(set)\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x73 // 's' for Set\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack(array)\n\t}\n}, {\n\tpack(error, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x65 // 'e' for error\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ error.name, error.message, error.cause ])\n\t}\n}, {\n\tpack(regex, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x78 // 'x' for regeXp\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ regex.source, regex.flags ])\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x10, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(typedArray, allocateForWrite) {\n\t\tlet constructor = typedArray.constructor\n\t\tif (constructor !== ByteArray && this.moreTypes)\n\t\t\twriteExtBuffer(typedArray, typedArrays.indexOf(constructor.name), allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(typedArray, allocateForWrite)\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x11, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(c1, allocateForWrite) { // specific 0xC1 object\n\t\tlet { target, position} = allocateForWrite(1)\n\t\ttarget[position] = 0xc1\n\t}\n}]\n\nfunction writeExtBuffer(typedArray, type, allocateForWrite, encode) {\n\tlet length = typedArray.byteLength\n\tif (length + 1 < 0x100) {\n\t\tvar { target, position } = allocateForWrite(4 + length)\n\t\ttarget[position++] = 0xc7\n\t\ttarget[position++] = length + 1\n\t} else if (length + 1 < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(5 + length)\n\t\ttarget[position++] = 0xc8\n\t\ttarget[position++] = (length + 1) >> 8\n\t\ttarget[position++] = (length + 1) & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(7 + length)\n\t\ttarget[position++] = 0xc9\n\t\ttargetView.setUint32(position, length + 1) // plus one for the type byte\n\t\tposition += 4\n\t}\n\ttarget[position++] = 0x74 // \"t\" for typed array\n\ttarget[position++] = type\n\tif (!typedArray.buffer) typedArray = new Uint8Array(typedArray)\n\ttarget.set(new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength), position)\n}\nfunction writeBuffer(buffer, allocateForWrite) {\n\tlet length = buffer.byteLength\n\tvar target, position\n\tif (length < 0x100) {\n\t\tvar { target, position } = allocateForWrite(length + 2)\n\t\ttarget[position++] = 0xc4\n\t\ttarget[position++] = length\n\t} else if (length < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(length + 3)\n\t\ttarget[position++] = 0xc5\n\t\ttarget[position++] = length >> 8\n\t\ttarget[position++] = length & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(length + 5)\n\t\ttarget[position++] = 0xc6\n\t\ttargetView.setUint32(position, length)\n\t\tposition += 4\n\t}\n\ttarget.set(buffer, position)\n}\n\nfunction writeExtensionData(result, target, position, type) {\n\tlet length = result.length\n\tswitch (length) {\n\t\tcase 1:\n\t\t\ttarget[position++] = 0xd4\n\t\t\tbreak\n\t\tcase 2:\n\t\t\ttarget[position++] = 0xd5\n\t\t\tbreak\n\t\tcase 4:\n\t\t\ttarget[position++] = 0xd6\n\t\t\tbreak\n\t\tcase 8:\n\t\t\ttarget[position++] = 0xd7\n\t\t\tbreak\n\t\tcase 16:\n\t\t\ttarget[position++] = 0xd8\n\t\t\tbreak\n\t\tdefault:\n\t\t\tif (length < 0x100) {\n\t\t\t\ttarget[position++] = 0xc7\n\t\t\t\ttarget[position++] = length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xc8\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xc9\n\t\t\t\ttarget[position++] = length >> 24\n\t\t\t\ttarget[position++] = (length >> 16) & 0xff\n\t\t\t\ttarget[position++] = (length >> 8) & 0xff\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t}\n\t}\n\ttarget[position++] = type\n\ttarget.set(result, position)\n\tposition += length\n\treturn position\n}\n\nfunction insertIds(serialized, idsToInsert) {\n\t// insert the ids that need to be referenced for structured clones\n\tlet nextId\n\tlet distanceToMove = idsToInsert.length * 6\n\tlet lastEnd = serialized.length - distanceToMove\n\twhile (nextId = idsToInsert.pop()) {\n\t\tlet offset = nextId.offset\n\t\tlet id = nextId.id\n\t\tserialized.copyWithin(offset + distanceToMove, offset, lastEnd)\n\t\tdistanceToMove -= 6\n\t\tlet position = offset + distanceToMove\n\t\tserialized[position++] = 0xd6\n\t\tserialized[position++] = 0x69 // 'i'\n\t\tserialized[position++] = id >> 24\n\t\tserialized[position++] = (id >> 16) & 0xff\n\t\tserialized[position++] = (id >> 8) & 0xff\n\t\tserialized[position++] = id & 0xff\n\t\tlastEnd = offset\n\t}\n\treturn serialized\n}\n\nfunction writeBundles(start, pack, incrementPosition) {\n\tif (bundledStrings.length > 0) {\n\t\ttargetView.setUint32(bundledStrings.position + start, position + incrementPosition - bundledStrings.position - start)\n\t\tbundledStrings.stringsPosition = position - start;\n\t\tlet writeStrings = bundledStrings\n\t\tbundledStrings = null\n\t\tpack(writeStrings[0])\n\t\tpack(writeStrings[1])\n\t}\n}\n\nexport function addExtension(extension) {\n\tif (extension.Class) {\n\t\tif (!extension.pack && !extension.write)\n\t\t\tthrow new Error('Extension has no pack or write function')\n\t\tif (extension.pack && !extension.type)\n\t\t\tthrow new Error('Extension has no type (numeric code to identify the extension)')\n\t\textensionClasses.unshift(extension.Class)\n\t\textensions.unshift(extension)\n\t}\n\tunpackAddExtension(extension)\n}\nfunction prepareStructures(structures, packr) {\n\tstructures.isCompatible = (existingStructures) => {\n\t\tlet compatible = !existingStructures || ((packr.lastNamedStructuresLength || 0) === existingStructures.length)\n\t\tif (!compatible) // we want to merge these existing structures immediately since we already have it and we are in the right transaction\n\t\t\tpackr._mergeStructures(existingStructures);\n\t\treturn compatible;\n\t}\n\treturn structures\n}\nexport function setWriteStructSlots(writeSlots, makeStructures) {\n\twriteStructSlots = writeSlots;\n\tprepareStructures = makeStructures;\n}\n\nlet defaultPackr = new Packr({ useRecords: false })\nexport const pack = defaultPackr.pack\nexport const encode = defaultPackr.pack\nexport const Encoder = Packr\nexport { FLOAT32_OPTIONS } from './unpack.js'\nimport { FLOAT32_OPTIONS } from './unpack.js'\nexport const { NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT } = FLOAT32_OPTIONS\nexport const REUSE_BUFFER_MODE = 512\nexport const RESET_BUFFER_MODE = 1024\nexport const RESERVE_START_SPACE = 2048", "import { Packr } from './pack.js'\nimport { Unpackr } from './unpack.js'\n\n/**\n * Given an Iterable first argument, returns an Iterable where each value is packed as a Buffer\n * If the argument is only Async Iterable, the return value will be an Async Iterable.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterator} objectIterator - iterable source, like a Readable object stream, an array, Set, or custom object\n * @param {options} [options] - msgpackr pack options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator>}\n */\nexport function packIter (objectIterator, options = {}) {\n  if (!objectIterator || typeof objectIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, or a Promise for an Async Iterable')\n  } else if (typeof objectIterator[Symbol.iterator] === 'function') {\n    return packIterSync(objectIterator, options)\n  } else if (typeof objectIterator.then === 'function' || typeof objectIterator[Symbol.asyncIterator] === 'function') {\n    return packIterAsync(objectIterator, options)\n  } else {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a Promise')\n  }\n}\n\nfunction * packIterSync (objectIterator, options) {\n  const packr = new Packr(options)\n  for (const value of objectIterator) {\n    yield packr.pack(value)\n  }\n}\n\nasync function * packIterAsync (objectIterator, options) {\n  const packr = new Packr(options)\n  for await (const value of objectIterator) {\n    yield packr.pack(value)\n  }\n}\n\n/**\n * Given an Iterable/Iterator input which yields buffers, returns an IterableIterator which yields sync decoded objects\n * Or, given an Async Iterable/Iterator which yields promises resolving in buffers, returns an AsyncIterableIterator.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterableIterator} bufferIterator\n * @param {object} [options] - unpackr options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator}\n */\nexport function unpackIter (bufferIterator, options = {}) {\n  if (!bufferIterator || typeof bufferIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a promise')\n  }\n\n  const unpackr = new Unpackr(options)\n  let incomplete\n  const parser = (chunk) => {\n    let yields\n    // if there's incomplete data from previous chunk, concatinate and try again\n    if (incomplete) {\n      chunk = Buffer.concat([incomplete, chunk])\n      incomplete = undefined\n    }\n\n    try {\n      yields = unpackr.unpackMultiple(chunk)\n    } catch (err) {\n      if (err.incomplete) {\n        incomplete = chunk.slice(err.lastPosition)\n        yields = err.values\n      } else {\n        throw err\n      }\n    }\n    return yields\n  }\n\n  if (typeof bufferIterator[Symbol.iterator] === 'function') {\n    return (function * iter () {\n      for (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  } else if (typeof bufferIterator[Symbol.asyncIterator] === 'function') {\n    return (async function * iter () {\n      for await (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  }\n}\nexport const decodeIter = unpackIter\nexport const encodeIter = packIter", "export { Packr, Encoder, addExtension, pack, encode, NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT, REUSE_BUFFER_MODE, RESET_BUFFER_MODE, RESERVE_START_SPACE } from './pack.js'\nexport { Unpackr, Decoder, C1, unpack, unpackMultiple, decode, FLOAT32_OPTIONS, clearSource, roundFloat32, isNativeAccelerationEnabled } from './unpack.js'\nexport { decodeIter, encodeIter } from './iterators.js'\nexport const useRecords = false\nexport const mapsAsObjects = true\n"], "names": ["position", "bundledStrings", "addExtension", "unpackAddExtension"], "mappings": ";;;;;;CAAA,IAAI,QAAO;CACX,IAAI;CACJ,CAAC,OAAO,GAAG,IAAI,WAAW,GAAE;CAC5B,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;CACjB,IAAI,IAAG;CACP,IAAI,OAAM;CACV,IAAIA,UAAQ,GAAG,EAAC;CAKhB,IAAI,cAAc,GAAG,GAAE;CACvB,IAAI,kBAAiB;CACrB,IAAI,UAAS;CACb,IAAI,cAAc,GAAG,EAAC;CACtB,IAAI,YAAY,GAAG,EAAC;CACpB,IAAIC,iBAAc;CAClB,IAAI,aAAY;CAChB,IAAI,iBAAiB,GAAG,GAAE;CAC1B,IAAI,SAAQ;CACZ,IAAI,cAAc,GAAG;CACrB,CAAC,UAAU,EAAE,KAAK;CAClB,CAAC,aAAa,EAAE,IAAI;CACpB,EAAC;CACM,MAAM,MAAM,CAAC,EAAE;AACV,OAAC,EAAE,GAAG,IAAI,MAAM,GAAE;CAC9B,EAAE,CAAC,IAAI,GAAG,mBAAkB;CAC5B,IAAI,cAAc,GAAG,MAAK;CAC1B,IAAI,yBAAyB,GAAG,EAAC;AAC9B,KAAC,UAAU,CAAiC;CAC/C,IAAI,gBAAe;CACnB;CACA,IAAI;CACJ,CAAC,IAAI,iBAAS,EAAE,EAAC;CACjB,CAAC,CAAC,MAAM,KAAK,EAAE;CACf;CACA,CAAC,yBAAyB,GAAG,SAAQ;CACrC,CAAC;AACD;CACO,MAAM,OAAO,CAAC;CACrB,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,IAAI,OAAO,EAAE;CACf,GAAG,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS;CAC1E,IAAI,OAAO,CAAC,aAAa,GAAG,KAAI;CAChC,GAAG,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;CACxD,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;CAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,KAAK,EAAE;CAC5D,KAAK,OAAO,CAAC,UAAU,GAAG,GAAE;CAC5B,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB;CACrC,MAAM,OAAO,CAAC,mBAAmB,GAAG,EAAC;CACrC,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,OAAO,CAAC,UAAU;CACzB,IAAI,OAAO,CAAC,UAAU,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,OAAM;CAC/D,QAAQ,IAAI,OAAO,CAAC,aAAa,EAAE;CACnC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,aAAa,GAAG,KAAI;CAClD,IAAI,OAAO,CAAC,UAAU,CAAC,YAAY,GAAG,EAAC;CACvC,IAAI;CACJ,GAAG,IAAI,OAAO,CAAC,aAAa,EAAE;CAC9B,IAAI,OAAO,CAAC,WAAW,GAAG,SAAQ;CAClC,IAAI;CACJ,GAAG;CACH,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAC;CAC9B,EAAE;CACF,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;CACzB,EAAE,IAAI,GAAG,EAAE;CACX;CACA,GAAG,OAAO,SAAS,CAAC,MAAM;CAC1B,IAAI,WAAW,GAAE;CACjB,IAAI,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC;CAC/G,IAAI,CAAC;CACL,GAAG;CACH,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,KAAK,WAAW;CAC1D,GAAG,MAAM,GAAG,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;CACzF,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;CACnC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,OAAM;CACxC,GAAGD,UAAQ,GAAG,OAAO,CAAC,KAAK,IAAI,EAAC;CAChC,GAAG,MAAM;CACT,GAAGA,UAAQ,GAAG,EAAC;CACf,GAAG,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,OAAM;CAClD,GAAG;CAEH,EAAE,YAAY,GAAG,EAAC;CAClB,EAAE,SAAS,GAAG,KAAI;CAElB,EAAEC,gBAAc,GAAG,KAAI;CACvB,EAAE,GAAG,GAAG,OAAM;CACd;CACA;CACA;CACA,EAAE,IAAI;CACN,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAC;CACtH,GAAG,CAAC,MAAM,KAAK,EAAE;CACjB;CACA,GAAG,GAAG,GAAG,KAAI;CACb,GAAG,IAAI,MAAM,YAAY,UAAU;CACnC,IAAI,MAAM,KAAK;CACf,GAAG,MAAM,IAAI,KAAK,CAAC,kDAAkD,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC;CAC1J,GAAG;CACH,EAAE,IAAI,IAAI,YAAY,OAAO,EAAE;CAC/B,GAAG,cAAc,GAAG,KAAI;CACxB,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;CACxB,IAAI,iBAAiB,GAAG,IAAI,CAAC,WAAU;CACvC,IAAI,OAAO,WAAW,CAAC,OAAO,CAAC;CAC/B,IAAI,MAAM,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;CAClE,IAAI,iBAAiB,GAAG,GAAE;CAC1B,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,cAAc,GAAG,eAAc;CAClC,GAAG,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;CACzD,IAAI,iBAAiB,GAAG,GAAE;CAC1B,GAAG;CACH,EAAE,OAAO,WAAW,CAAC,OAAO,CAAC;CAC7B,EAAE;CACF,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE;CACjC,EAAE,IAAI,MAAM,EAAE,YAAY,GAAG,EAAC;CAC9B,EAAE,IAAI;CACN,GAAG,cAAc,GAAG,KAAI;CACxB,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,OAAM;CAC3B,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAC;CACrF,GAAG,IAAI,OAAO,EAAE;CAChB,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,YAAY,EAAED,UAAQ,CAAC,KAAK,KAAK,EAAE,OAAO;CACjE,IAAI,MAAMA,UAAQ,GAAG,IAAI,EAAE;CAC3B,KAAK,YAAY,GAAGA,WAAQ;CAC5B,KAAK,IAAI,OAAO,CAAC,WAAW,EAAE,EAAE,YAAY,EAAEA,UAAQ,CAAC,KAAK,KAAK,EAAE;CACnE,MAAM,MAAM;CACZ,MAAM;CACN,KAAK;CACL,IAAI;CACJ,QAAQ;CACR,IAAI,MAAM,GAAG,EAAE,KAAK,GAAE;CACtB,IAAI,MAAMA,UAAQ,GAAG,IAAI,EAAE;CAC3B,KAAK,YAAY,GAAGA,WAAQ;CAC5B,KAAK,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAC;CAC/B,KAAK;CACL,IAAI,OAAO,MAAM;CACjB,IAAI;CACJ,GAAG,CAAC,MAAM,KAAK,EAAE;CACjB,GAAG,KAAK,CAAC,YAAY,GAAG,aAAY;CACpC,GAAG,KAAK,CAAC,MAAM,GAAG,OAAM;CACxB,GAAG,MAAM,KAAK;CACd,GAAG,SAAS;CACZ,GAAG,cAAc,GAAG,MAAK;CACzB,GAAG,WAAW,GAAE;CAChB,GAAG;CACH,EAAE;CACF,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;CAGxD,EAAE,gBAAgB,GAAG,gBAAgB,IAAI,GAAE;CAC3C,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC;CACvC,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;CAC3E,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC3D,GAAG,IAAI,SAAS,GAAG,gBAAgB,CAAC,CAAC,EAAC;CACtC,GAAG,IAAI,SAAS,EAAE;CAClB,IAAI,SAAS,CAAC,QAAQ,GAAG,KAAI;CAC7B,IAAI,IAAI,CAAC,IAAI,EAAE;CACf,KAAK,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAC;CACvC,IAAI;CACJ,GAAG;CACH,EAAE,gBAAgB,CAAC,YAAY,GAAG,gBAAgB,CAAC,OAAM;CACzD,EAAE,KAAK,IAAI,EAAE,IAAI,kBAAkB,IAAI,EAAE,EAAE;CAC3C,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE;CAChB,IAAI,IAAI,SAAS,GAAG,gBAAgB,CAAC,EAAE,EAAC;CACxC,IAAI,IAAI,QAAQ,GAAG,kBAAkB,CAAC,EAAE,EAAC;CACzC,IAAI,IAAI,QAAQ,EAAE;CAClB,KAAK,IAAI,SAAS;CAClB,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,KAAK,gBAAgB,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,UAAS;CACvG,KAAK,gBAAgB,CAAC,EAAE,CAAC,GAAG,SAAQ;CACpC,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE,OAAO,IAAI,CAAC,UAAU,GAAG,gBAAgB;CAC3C,EAAE;CACF,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;CACzB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;CACrC,EAAE;CACF,CAAC;CAIM,SAAS,WAAW,CAAC,OAAO,EAAE;CACrC,CAAC,IAAI;CACL,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE;CAClD,GAAG,IAAI,YAAY,GAAG,iBAAiB,CAAC,YAAY,IAAI,EAAC;CACzD,GAAG,IAAI,YAAY,GAAG,iBAAiB,CAAC,MAAM;CAC9C,IAAI,iBAAiB,CAAC,MAAM,GAAG,aAAY;CAC3C,GAAG;CACH,EAAE,IAAI,OAAM;CACZ,EAAE,IAAI,cAAc,CAAC,qBAAqB,IAAI,GAAG,CAACA,UAAQ,CAAC,GAAG,IAAI,IAAI,GAAG,CAACA,UAAQ,CAAC,IAAI,IAAI,IAAI,UAAU,EAAE;CAC3G,GAAG,MAAM,GAAG,UAAU,CAAC,GAAG,EAAEA,UAAQ,EAAE,MAAM,EAAE,cAAc,EAAC;CAC7D,GAAG,GAAG,GAAG,KAAI;CACb,GAAG,IAAI,EAAE,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM;CAC3C,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,GAAE;CAC5B,GAAGA,UAAQ,GAAG,OAAM;CACpB,GAAG;CACH,GAAG,MAAM,GAAG,IAAI,GAAE;CAClB,EAAE,IAAIC,gBAAc,EAAE;CACtB,GAAGD,UAAQ,GAAGC,gBAAc,CAAC,mBAAkB;CAC/C,GAAGA,gBAAc,GAAG,KAAI;CACxB,GAAG;CACH,EAAE,IAAI,cAAc;CACpB;CACA;CACA,GAAG,iBAAiB,CAAC,iBAAiB,GAAG,KAAI;AAC7C;CACA,EAAE,IAAID,UAAQ,IAAI,MAAM,EAAE;CAC1B;CACA,GAAG,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB;CAC/D,IAAI,iBAAiB,GAAE;CACvB,GAAG,iBAAiB,GAAG,KAAI;CAC3B,GAAG,GAAG,GAAG,KAAI;CACb,GAAG,IAAI,YAAY;CACnB,IAAI,YAAY,GAAG,KAAI;CACvB,GAAG,MAAM,IAAIA,UAAQ,GAAG,MAAM,EAAE;CAChC;CACA,GAAG,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC;CACxD,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;CAC9B,GAAG,IAAI,QAAQ,CAAC;CAChB,GAAG,IAAI;CACP,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAC;CAClH,IAAI,CAAC,MAAM,KAAK,EAAE;CAClB,IAAI,QAAQ,GAAG,2BAA2B,GAAG,KAAK,GAAG,IAAG;CACxD,IAAI;CACJ,GAAG,MAAM,IAAI,KAAK,CAAC,2CAA2C,GAAG,QAAQ,CAAC;CAC1E,GAAG;CACH;CACA,EAAE,OAAO,MAAM;CACf,EAAE,CAAC,MAAM,KAAK,EAAE;CAChB,EAAE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB;CAC9D,GAAG,iBAAiB,GAAE;CACtB,EAAE,WAAW,GAAE;CACf,EAAE,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,0BAA0B,CAAC,IAAIA,UAAQ,GAAG,MAAM,EAAE;CAChH,GAAG,KAAK,CAAC,UAAU,GAAG,KAAI;CAC1B,GAAG;CACH,EAAE,MAAM,KAAK;CACb,EAAE;CACF,CAAC;AACD;CACA,SAAS,iBAAiB,GAAG;CAC7B,CAAC,KAAK,IAAI,EAAE,IAAI,iBAAiB,CAAC,iBAAiB,EAAE;CACrD,EAAE,iBAAiB,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,EAAE,EAAC;CACjE,EAAE;CACF,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,KAAI;CAC3C,CAAC;AACD;CACO,SAAS,IAAI,GAAG;CACvB,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE;CACnB,EAAE,IAAI,KAAK,GAAG,IAAI,EAAE;CACpB,GAAG,IAAI,KAAK,GAAG,IAAI;CACnB,IAAI,OAAO,KAAK;CAChB,QAAQ;CACR,IAAI,IAAI,SAAS,GAAG,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC;CACnD,KAAK,cAAc,CAAC,aAAa,IAAI,cAAc,EAAE,CAAC,KAAK,GAAG,IAAI,EAAC;CACnE,IAAI,IAAI,SAAS,EAAE;CACnB,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;CAC1B,MAAM,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI,EAAC;CACrE,MAAM;CACN,KAAK,OAAO,SAAS,CAAC,IAAI,EAAE;CAC5B,KAAK;CACL,KAAK,OAAO,KAAK;CACjB,IAAI;CACJ,GAAG,MAAM,IAAI,KAAK,GAAG,IAAI,EAAE;CAC3B;CACA,GAAG,KAAK,IAAI,KAAI;CAChB,GAAG,IAAI,cAAc,CAAC,aAAa,EAAE;CACrC,IAAI,IAAI,MAAM,GAAG,GAAE;CACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CACpC,KAAK,IAAI,GAAG,GAAG,OAAO,GAAE;CACxB,KAAK,IAAI,GAAG,KAAK,WAAW;CAC5B,MAAM,GAAG,GAAG,WAAU;CACtB,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;CACzB,KAAK;CACL,IAAI,OAAO,MAAM;CACjB,IAAI,MAAM;CACV,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;CACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CACpC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAC;CAC5B,KAAK;CACL,IAAI,OAAO,GAAG;CACd,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,KAAK,IAAI,KAAI;CAChB,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,EAAC;CAC/B,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CACnC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAE;CACrB,IAAI;CACJ,GAAG,IAAI,cAAc,CAAC,UAAU;CAChC,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;CAC/B,GAAG,OAAO,KAAK;CACf,GAAG;CACH,EAAE,MAAM,IAAI,KAAK,GAAG,IAAI,EAAE;CAC1B;CACA,EAAE,IAAI,MAAM,GAAG,KAAK,GAAG,KAAI;CAC3B,EAAE,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAChC,GAAG,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;CAC3F,GAAG;CACH,EAAE,IAAI,YAAY,IAAI,CAAC,IAAI,MAAM,GAAG,GAAG,EAAE;CACzC;CACA,GAAG,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,EAAC;CAC9E,GAAG,IAAI,MAAM,IAAI,IAAI;CACrB,IAAI,OAAO,MAAM;CACjB,GAAG;CACH,EAAE,OAAO,eAAe,CAAC,MAAM,CAAC;CAChC,EAAE,MAAM;CACR,EAAE,IAAI,MAAK;CACX,EAAE,QAAQ,KAAK;CACf,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI;CACzB,GAAG,KAAK,IAAI;CACZ,IAAI,IAAIC,gBAAc,EAAE;CACxB,KAAK,KAAK,GAAG,IAAI,GAAE;CACnB,KAAK,IAAI,KAAK,GAAG,CAAC;CAClB,MAAM,OAAOA,gBAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAACA,gBAAc,CAAC,SAAS,EAAEA,gBAAc,CAAC,SAAS,IAAI,KAAK,CAAC;CACjG;CACA,MAAM,OAAOA,gBAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAACA,gBAAc,CAAC,SAAS,EAAEA,gBAAc,CAAC,SAAS,IAAI,KAAK,CAAC;CACjG,KAAK;CACL,IAAI,OAAO,EAAE,CAAC;CACd,GAAG,KAAK,IAAI,EAAE,OAAO,KAAK;CAC1B,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACD,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,KAAK,KAAK,SAAS;CAC3B,KAAK,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;CAChD,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;CACnC,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAACA,UAAQ,EAAC;CACzC,IAAI,IAAI,cAAc,CAAC,UAAU,GAAG,CAAC,EAAE;CACvC;CACA,KAAK,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAACA,UAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAACA,UAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;CACtF,KAAKA,UAAQ,IAAI,EAAC;CAClB,KAAK,OAAO,CAAC,CAAC,UAAU,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU;CAC/E,KAAK;CACL,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAACA,UAAQ,EAAC;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB;CACA,GAAG,KAAK,IAAI;CACZ,IAAI,OAAO,GAAG,CAACA,UAAQ,EAAE,CAAC;CAC1B,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACjD,KAAK,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,CAAC,GAAG,YAAW;CACvD,KAAK,KAAK,IAAI,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC9C,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACxD,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,CAAC,CAAC,QAAQ,GAAE;CACvD,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,EAAE;CACtD,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,EAAC;CAC5C,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAC;CAC1D,KAAK;CACL,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,EAAC;CAC5C,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;AAChB;CACA;CACA,GAAG,KAAK,IAAI;CACZ,IAAI,OAAO,QAAQ,CAAC,OAAO,CAACA,UAAQ,EAAE,CAAC;CACvC,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,EAAC;CACvC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,EAAC;CACvC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;CAChB,GAAG,KAAK,IAAI;CACZ,IAAI,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACjD,KAAK,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,CAAC,GAAG,YAAW;CACtD,KAAK,KAAK,IAAI,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC9C,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;CACxD,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,CAAC,CAAC,QAAQ,GAAE;CACtD,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,EAAE;CACtD,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,EAAC;CAC3C,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAC;CACzF,KAAK;CACL,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,EAAC;CAC3C,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,KAAK;AAChB;CACA,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;CACvB,KAAK,OAAO,gBAAgB,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;CACpD,KAAK,MAAM;CACX,KAAK,IAAI,SAAS,GAAG,iBAAiB,CAAC,KAAK,EAAC;CAC7C,KAAK,IAAI,SAAS,EAAE;CACpB,MAAM,IAAI,SAAS,CAAC,IAAI,EAAE;CAC1B,OAAOA,UAAQ,GAAE;CACjB,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACpC,OAAO,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE;CACrC,OAAOA,UAAQ,GAAE;CACjB,OAAO,OAAO,SAAS,EAAE;CACzB,OAAO;CACP,OAAO,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAE,EAAEA,UAAQ,CAAC,CAAC;CAC3D,MAAM;CACN,MAAM,MAAM,IAAI,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;CACnD,KAAK;CACL,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAC;CACzB,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;CACvB,KAAKA,UAAQ,GAAE;CACf,KAAK,OAAO,gBAAgB,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;CACrE,KAAK;CACL,KAAK,OAAO,OAAO,CAAC,CAAC,CAAC;CACtB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC;CACrB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC;CACrB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC;CACtB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;CAC5F,KAAK;CACL,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC;CAC7B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;CAC5F,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC;CAC9B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;CAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;CAC5F,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC;CAC9B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC;CAC3B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC;CAC3B,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACxC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;CACzB,GAAG;CACH,IAAI,IAAI,KAAK,IAAI,IAAI;CACrB,KAAK,OAAO,KAAK,GAAG,KAAK;CACzB,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;CAC7B,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,oCAAoC,EAAC;CAChE,KAAK,KAAK,CAAC,UAAU,GAAG,KAAI;CAC5B,KAAK,MAAM,KAAK;CAChB,KAAK;CACL,IAAI,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,KAAK,CAAC;AACzD;CACA,GAAG;CACH,EAAE;CACF,CAAC;CACD,MAAM,SAAS,GAAG,4BAA2B;CAC7C,SAAS,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAE;CACnD,CAAC,SAAS,UAAU,GAAG;CACvB;CACA,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE,GAAG,yBAAyB,EAAE;CACtD,GAAG,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,eAAQ,EAAC,GAAG,EAAE,2BAA2B,IAAI,cAAc,CAAC,UAAU,GAAG,eAAe,GAAG,EAAE,CAAC;CACxI,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,WAAW,GAAG,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAC;CAC5K,GAAG,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC;CAC/B,IAAI,SAAS,CAAC,IAAI,GAAG,sBAAsB,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,EAAC;CACpE,GAAG,OAAO,UAAU,EAAE;CACtB,GAAG;CACH,EAAE,IAAI,MAAM,GAAG,GAAE;CACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACpD,GAAG,IAAI,GAAG,GAAG,SAAS,CAAC,CAAC,EAAC;CACzB,GAAG,IAAI,GAAG,KAAK,WAAW;CAC1B,IAAI,GAAG,GAAG,WAAU;CACpB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;CACvB,GAAG;CACH,EAAE,IAAI,cAAc,CAAC,UAAU;CAC/B,GAAG,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;CAChC,EAAE,OAAO,MAAM;CACf,EAAE;CACF,CAAC,UAAU,CAAC,KAAK,GAAG,EAAC;CACrB,CAAC,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE;CAC/B,EAAE,OAAO,sBAAsB,CAAC,OAAO,EAAE,UAAU,CAAC;CACpD,EAAE;CACF,CAAC,OAAO,UAAU;CAClB,CAAC;AACD;CACA,MAAM,sBAAsB,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK;CACnD,CAAC,OAAO,WAAW;CACnB,EAAE,IAAI,QAAQ,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAChC,EAAE,IAAI,QAAQ,KAAK,CAAC;CACpB,GAAG,OAAO,KAAK,EAAE;CACjB,EAAE,IAAI,EAAE,GAAG,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,QAAQ,IAAI,CAAC,EAAC;CAClF,EAAE,IAAI,SAAS,GAAG,iBAAiB,CAAC,EAAE,CAAC,IAAI,cAAc,EAAE,CAAC,EAAE,EAAC;CAC/D,EAAE,IAAI,CAAC,SAAS,EAAE;CAClB,GAAG,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,EAAE,CAAC;CACxD,GAAG;CACH,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;CACrB,GAAG,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAC;CAC7D,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE;CACzB,EAAE;CACF,EAAC;AACD;CACO,SAAS,cAAc,GAAG;CACjC,CAAC,IAAI,gBAAgB,GAAG,SAAS,CAAC,MAAM;CACxC;CACA,EAAE,GAAG,GAAG,KAAI;CACZ,EAAE,OAAO,cAAc,CAAC,aAAa,EAAE;CACvC,EAAE,EAAC;CACH,CAAC,OAAO,iBAAiB,GAAG,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;CAChG,CAAC;AACD;CACA,IAAI,eAAe,GAAG,aAAY;CAClC,IAAI,WAAW,GAAG,aAAY;CAC9B,IAAI,YAAY,GAAG,aAAY;CAC/B,IAAI,YAAY,GAAG,aAAY;AACrB,KAAC,2BAA2B,GAAG,MAAK;CAyC9C,SAAS,YAAY,CAAC,MAAM,EAAE;CAC9B,CAAC,IAAI,OAAM;CACX,CAAC,IAAI,MAAM,GAAG,EAAE,EAAE;CAClB,EAAE,IAAI,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;CACtC,GAAG,OAAO,MAAM;CAChB,EAAE;CACF,CAAC,IAAI,MAAM,GAAG,EAAE,IAAI,OAAO;CAC3B,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC,CAAC;CACnE,CAAC,MAAM,GAAG,GAAGA,UAAQ,GAAG,OAAM;CAC9B,CAAC,MAAM,KAAK,GAAG,GAAE;CACjB,CAAC,MAAM,GAAG,GAAE;CACZ,CAAC,OAAOA,UAAQ,GAAG,GAAG,EAAE;CACxB,EAAE,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC/B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;CAC5B;CACA,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACpB,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;CACtC;CACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,EAAC;CAC5C,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;CACtC;CACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,EAAC;CAC5D,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;CACtC;CACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;CACvC,GAAG,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAK;CAClF,GAAG,IAAI,IAAI,GAAG,MAAM,EAAE;CACtB,IAAI,IAAI,IAAI,QAAO;CACnB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,MAAM,EAAC;CAChD,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,EAAC;CAClC,IAAI;CACJ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;CACnB,GAAG,MAAM;CACT,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACpB,GAAG;AACH;CACA,EAAE,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,EAAE;CAC9B,GAAG,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAC;CAC9C,GAAG,KAAK,CAAC,MAAM,GAAG,EAAC;CACnB,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CACvB,EAAE,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAC;CAC7C,EAAE;AACF;CACA,CAAC,OAAO,MAAM;CACd,CAAC;AAWD;CACA,SAAS,SAAS,CAAC,MAAM,EAAE;CAC3B,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAC;CAC9B,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CAClC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAE;CACnB,EAAE;CACF,CAAC,IAAI,cAAc,CAAC,UAAU;CAC9B,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;CAC7B,CAAC,OAAO,KAAK;CACb,CAAC;AACD;CACA,SAAS,OAAO,CAAC,MAAM,EAAE;CACzB,CAAC,IAAI,cAAc,CAAC,aAAa,EAAE;CACnC,EAAE,IAAI,MAAM,GAAG,GAAE;CACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACnC,GAAG,IAAI,GAAG,GAAG,OAAO,GAAE;CACtB,GAAG,IAAI,GAAG,KAAK,WAAW;CAC1B,IAAI,GAAG,GAAG,UAAU,CAAC;CACrB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;CACvB,GAAG;CACH,EAAE,OAAO,MAAM;CACf,EAAE,MAAM;CACR,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;CACrB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACnC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAC;CAC1B,GAAG;CACH,EAAE,OAAO,GAAG;CACZ,EAAE;CACF,CAAC;AACD;CACA,IAAI,YAAY,GAAG,MAAM,CAAC,aAAY;CACtC,SAAS,cAAc,CAAC,MAAM,EAAE;CAChC,CAAC,IAAI,KAAK,GAAGA,WAAQ;CACrB,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAC;CAC9B,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CAClC,EAAE,MAAM,IAAI,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;CAC/B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE;CACzB,IAAIA,UAAQ,GAAG,MAAK;CACpB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAI;CAClB,GAAG;CACH,EAAE,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;CAC1C,CAAC;CACD,SAAS,eAAe,CAAC,MAAM,EAAE;CACjC,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;CACjB,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;CAClB,GAAG,IAAI,MAAM,KAAK,CAAC;CACnB,IAAI,OAAO,EAAE;CACb,QAAQ;CACR,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACxB,KAAKA,UAAQ,IAAI,EAAC;CAClB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,CAAC,CAAC;CAC1B,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,CAAC;CACjB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;CAC7B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACvB,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC/B,GAAG;CACH,EAAE,MAAM;CACR,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CACzB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC5E,GAAGA,UAAQ,IAAI,EAAC;CAChB,GAAG,MAAM;CACT,GAAG;CACH,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;CAClB,GAAG,IAAI,MAAM,KAAK,CAAC;CACnB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnC,QAAQ;CACR,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACxB,KAAKA,UAAQ,IAAI,EAAC;CAClB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACtC,IAAI;CACJ,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;CACzB,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,CAAC;CACjB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACzC,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACvB,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC3C,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC7E,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,EAAE,EAAE;CACpB,IAAI,IAAI,MAAM,KAAK,CAAC;CACpB,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAChD,SAAS;CACT,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzB,MAAMA,UAAQ,IAAI,EAAC;CACnB,MAAM,MAAM;CACZ,MAAM;CACN,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnD,KAAK;CACL,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE,EAAE;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC1C,KAAKA,UAAQ,IAAI,GAAE;CACnB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,IAAI,MAAM,GAAG,EAAE;CACnB,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACtD,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACxB,KAAKA,UAAQ,IAAI,GAAE;CACnB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACxD,IAAI,MAAM;CACV,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC9E,KAAKA,UAAQ,IAAI,GAAE;CACnB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,IAAI,MAAM,GAAG,EAAE,EAAE;CACrB,KAAK,IAAI,MAAM,KAAK,EAAE;CACtB,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAC7D,UAAU;CACV,MAAM,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC7B,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC1B,OAAOA,UAAQ,IAAI,GAAE;CACrB,OAAO,MAAM;CACb,OAAO;CACP,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAChE,MAAM;CACN,KAAK,MAAM;CACX,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CAC3C,MAAMA,UAAQ,IAAI,GAAE;CACpB,MAAM,MAAM;CACZ,MAAM;CACN,KAAK,IAAI,MAAM,GAAG,EAAE;CACpB,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACnE,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;CACzB,MAAMA,UAAQ,IAAI,GAAE;CACpB,MAAM,MAAM;CACZ,MAAM;CACN,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACrE,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;CACF,CAAC;AACD;CACA,SAAS,gBAAgB,GAAG;CAC5B,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,CAAC,IAAI,OAAM;CACX,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE;CACnB;CACA,EAAE,MAAM,GAAG,KAAK,GAAG,KAAI;CACvB,EAAE,MAAM;CACR,EAAE,OAAO,KAAK;CACd,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,MAAM,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC5B,IAAI,KAAK;CACT,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,KAAK;CACT,GAAG,KAAK,IAAI;CACZ;CACA,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;CACzC,IAAIA,UAAQ,IAAI,EAAC;CACjB,IAAI,KAAK;CACT,GAAG;CACH,IAAI,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC;CACtC,GAAG;CACH,EAAE;CACF,CAAC,OAAO,YAAY,CAAC,MAAM,CAAC;CAC5B,CAAC;AACD;AACA;CACA,SAAS,OAAO,CAAC,MAAM,EAAE;CACzB,CAAC,OAAO,cAAc,CAAC,WAAW;CAClC;CACA,EAAE,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAEA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC;CACpE,EAAE,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC;CAC5C,CAAC;CACD,SAAS,OAAO,CAAC,MAAM,EAAE;CACzB,CAAC,IAAI,IAAI,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC3B,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;CAC9B,EAAE,IAAI,IAAG;CACT,EAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAE,GAAG,IAAIA,UAAQ,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,YAAY,KAAK;CACvG,GAAGA,UAAQ,GAAG,YAAY,CAAC;CAC3B,GAAG,IAAI;CACP,IAAI,OAAO,IAAI,EAAE,CAAC;CAClB,IAAI,SAAS;CACb,IAAIA,UAAQ,GAAG,GAAG,CAAC;CACnB,IAAI;CACJ,GAAG,CAAC;CACJ,EAAE;CACF;CACA,EAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC;CACnD,CAAC;AACD;CACA,IAAI,QAAQ,GAAG,IAAI,KAAK,CAAC,IAAI,EAAC;CAC9B,SAAS,OAAO,GAAG;CACnB,CAAC,IAAI,MAAM,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;CAC7B,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;CACtC;CACA,EAAE,MAAM,GAAG,MAAM,GAAG,KAAI;CACxB,EAAE,IAAI,YAAY,IAAIA,UAAQ;CAC9B,GAAG,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;CAC3F,OAAO,IAAI,EAAE,YAAY,IAAI,CAAC,IAAI,MAAM,GAAG,GAAG,CAAC;CAC/C,GAAG,OAAO,eAAe,CAAC,MAAM,CAAC;CACjC,EAAE,MAAM;CACR,EAAEA,UAAQ,GAAE;CACZ,EAAE,OAAO,YAAY,CAAC,IAAI,EAAE,CAAC;CAC7B,EAAE;CACF,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,GAAG,CAACA,UAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,MAAK;CACjH,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAC;CAC1B,CAAC,IAAI,aAAa,GAAGA,WAAQ;CAC7B,CAAC,IAAI,GAAG,GAAGA,UAAQ,GAAG,MAAM,GAAG,EAAC;CAChC,CAAC,IAAI,MAAK;CACV,CAAC,IAAI,CAAC,GAAG,EAAC;CACV,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,EAAE;CACrC,EAAE,OAAO,aAAa,GAAG,GAAG,EAAE;CAC9B,GAAG,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAC;CAC5C,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;CAC5B,IAAI,aAAa,GAAG,WAAU;CAC9B,IAAI,KAAK;CACT,IAAI;CACJ,GAAG,aAAa,IAAI,EAAC;CACrB,GAAG;CACH,EAAE,GAAG,IAAI,EAAC;CACV,EAAE,OAAO,aAAa,GAAG,GAAG,EAAE;CAC9B,GAAG,KAAK,GAAG,GAAG,CAAC,aAAa,EAAE,EAAC;CAC/B,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;CAC5B,IAAI,aAAa,GAAG,WAAU;CAC9B,IAAI,KAAK;CACT,IAAI;CACJ,GAAG;CACH,EAAE,IAAI,aAAa,KAAK,GAAG,EAAE;CAC7B,GAAGA,UAAQ,GAAG,cAAa;CAC3B,GAAG,OAAO,KAAK,CAAC,MAAM;CACtB,GAAG;CACH,EAAE,GAAG,IAAI,EAAC;CACV,EAAE,aAAa,GAAGA,WAAQ;CAC1B,EAAE;CACF,CAAC,KAAK,GAAG,GAAE;CACX,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAK;CACtB,CAAC,KAAK,CAAC,KAAK,GAAG,OAAM;CACrB,CAAC,OAAO,aAAa,GAAG,GAAG,EAAE;CAC7B,EAAE,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAC;CAC3C,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACnB,EAAE,aAAa,IAAI,EAAC;CACpB,EAAE;CACF,CAAC,GAAG,IAAI,EAAC;CACT,CAAC,OAAO,aAAa,GAAG,GAAG,EAAE;CAC7B,EAAE,KAAK,GAAG,GAAG,CAAC,aAAa,EAAE,EAAC;CAC9B,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CACnB,EAAE;CACF;CACA,CAAC,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,EAAC;CAC5E,CAAC,IAAI,MAAM,IAAI,IAAI;CACnB,EAAE,OAAO,KAAK,CAAC,MAAM,GAAG,MAAM;CAC9B,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;CAC9C,CAAC;AACD;CACA,SAAS,YAAY,CAAC,QAAQ,EAAE;CAChC;CACA,CAAC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,QAAQ,CAAC;CACnD,CAAC,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;CAC/H,CAAC,IAAI,QAAQ,IAAI,IAAI,EAAE,OAAO,QAAQ,GAAG,EAAE,CAAC;CAC5C,CAAC,IAAI,cAAc,CAAC,oBAAoB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE;CACvK,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;CACpC,EAAE;CACF,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzE,CAAC;CACD;CACA,MAAM,gBAAgB,GAAG,CAAC,EAAE,EAAE,QAAQ,KAAK;CAC3C,CAAC,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,YAAY,EAAC;CACzC;CACA,CAAC,IAAI,SAAS,GAAG,GAAE;CACnB,CAAC,IAAI,QAAQ,KAAK,SAAS,EAAE;CAC7B,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,EAAC;CACjE,EAAE,SAAS,CAAC,QAAQ,GAAG,SAAQ;CAC/B,EAAE;CACF,CAAC,IAAI,iBAAiB,GAAG,iBAAiB,CAAC,EAAE,EAAC;CAC9C;CACA;CACA;CACA,CAAC,IAAI,iBAAiB,KAAK,iBAAiB,CAAC,QAAQ,IAAI,cAAc,CAAC,EAAE;CAC1E,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,kBAAiB;CAC7G,EAAE;CACF,CAAC,iBAAiB,CAAC,EAAE,CAAC,GAAG,UAAS;CAClC,CAAC,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,SAAS,EAAC;CAC7D,CAAC,OAAO,SAAS,CAAC,IAAI,EAAE;CACxB,EAAC;CACD,iBAAiB,CAAC,CAAC,CAAC,GAAG,MAAM,GAAE;CAC/B,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAI;AACpC;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC1B,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CAChE,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CAClC,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;CACtB,EAAE,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CAC3B,EAAE;CACF,CAAC,OAAO,KAAK,CAAC;CACd,EAAC;AACD;CACA,IAAI,MAAM,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC;CAClD,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM;CAChC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAE;CAClB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;CAC/D,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,cAAc,CAAC,eAAe,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;CACxG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC1C,CAAC,IAAI,CAAC,YAAY;CAClB,EAAE,YAAY,GAAG,IAAI,GAAG,GAAE;CAC1B,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAC;CAC1B,CAAC,IAAI,OAAM;CACX;CACA,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;CACpE,EAAE,MAAM,GAAG,GAAE;CACb,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;CACzE,EAAE,MAAM,GAAG,IAAI,GAAG,GAAE;CACpB,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,CAACA,UAAQ,GAAG,CAAC,CAAC,KAAK,IAAI;CAC1G,EAAE,MAAM,GAAG,IAAI,GAAG,GAAE;CACpB;CACA,EAAE,MAAM,GAAG,GAAE;AACb;CACA,CAAC,IAAI,QAAQ,GAAG,EAAE,MAAM,GAAE;CAC1B,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAC;CAC/B,CAAC,IAAI,gBAAgB,GAAG,IAAI,GAAE;CAC9B,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;CACrB;CACA,EAAE,OAAO,QAAQ,CAAC,MAAM,GAAG,gBAAgB;CAC3C,EAAE,MAAM;CACR;CACA,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,EAAC;CACzC,EAAE;AACF;CACA;CACA,CAAC,IAAI,MAAM,YAAY,GAAG;CAC1B,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAC;CACjE,CAAC,IAAI,MAAM,YAAY,GAAG;CAC1B,EAAE,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAC;CAC3D,CAAC,OAAO,MAAM;CACd,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,cAAc,CAAC,eAAe,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;CACxG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;CAC1C,CAAC,IAAI,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,EAAE,EAAC;CACpC,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAI;CACrB,CAAC,OAAO,QAAQ,CAAC,MAAM;CACvB,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,EAAC;AAC/C;CACO,MAAM,WAAW,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,OAAO,EAAC;AACnK;CACA,IAAI,IAAI,GAAG,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,MAAM,CAAC;CAChE,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAC;CACvB;CACA,CAAC,IAAI,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAM;AAC7D;CACA,CAAC,IAAI,cAAc,GAAG,WAAW,CAAC,QAAQ,EAAC;CAC3C,CAAC,IAAI,CAAC,cAAc,EAAE;CACtB,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE,OAAO,MAAM;CACpC,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC;CAClD,EAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,QAAQ,CAAC;CACpE,EAAE;CACF,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;CACxC,EAAC;CACD,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM;CAChC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAE;CAClB,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;CACpC,EAAC;CACD,MAAM,WAAW,GAAG,GAAE;CACtB,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAC;CAC5E,CAAC,IAAI,YAAY,GAAGA,WAAQ;CAC5B,CAACA,UAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAM;CACnC,CAACC,gBAAc,GAAG,YAAW;CAC7B,CAACA,gBAAc,GAAG,CAAC,gBAAgB,EAAE,EAAE,gBAAgB,EAAE,EAAC;CAC1D,CAACA,gBAAc,CAAC,SAAS,GAAG,EAAC;CAC7B,CAACA,gBAAc,CAAC,SAAS,GAAG,EAAC;CAC7B,CAACA,gBAAc,CAAC,kBAAkB,GAAGD,WAAQ;CAC7C,CAACA,UAAQ,GAAG,aAAY;CACxB,CAAC,OAAO,IAAI,EAAE;CACd,EAAC;AACD;CACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;CACpC;CACA,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;CACrB,EAAE,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;CAC5F,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;CAC1B,EAAE,OAAO,IAAI,IAAI;CACjB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO;CAClF,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;CAC7G,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE;CAC3B,EAAE,OAAO,IAAI,IAAI;CACjB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO;CAC3E,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;CAC7K;CACA,EAAE,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC;CAC5B,EAAC;CACD;CACA;AACA;CACA,SAAS,SAAS,CAAC,QAAQ,EAAE;CAG7B,CAAC,IAAI,WAAW,GAAG,OAAM;CACzB,CAAC,IAAI,aAAa,GAAGA,WAAQ;CAE7B,CAAC,IAAI,mBAAmB,GAAG,eAAc;CACzC,CAAC,IAAI,iBAAiB,GAAG,aAAY;CACrC,CAAC,IAAI,cAAc,GAAG,UAAS;CAE/B,CAAC,IAAI,iBAAiB,GAAG,aAAY;CACrC,CAAC,IAAI,mBAAmB,GAAGC,iBAAc;AACzC;CACA;CACA,CAAC,IAAI,QAAQ,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,EAAC;CACpD,CAAC,IAAI,eAAe,GAAG,kBAAiB;CACxC,CAAC,IAAI,uBAAuB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,MAAM,EAAC;CACnF,CAAC,IAAI,UAAU,GAAG,eAAc;CAChC,CAAC,IAAI,mBAAmB,GAAG,eAAc;CACzC,CAAC,IAAI,KAAK,GAAG,QAAQ,GAAE;CACvB,CAAC,MAAM,GAAG,YAAW;CACrB,CAACD,UAAQ,GAAG,cAAa;CAEzB,CAAC,cAAc,GAAG,oBAAmB;CACrC,CAAC,YAAY,GAAG,kBAAiB;CACjC,CAAC,SAAS,GAAG,eAAc;CAE3B,CAAC,YAAY,GAAG,kBAAiB;CACjC,CAACC,gBAAc,GAAG,oBAAmB;CACrC,CAAC,GAAG,GAAG,SAAQ;CACf,CAAC,cAAc,GAAG,oBAAmB;CACrC,CAAC,iBAAiB,GAAG,gBAAe;CACpC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,iBAAiB,CAAC,MAAM,EAAE,GAAG,uBAAuB,EAAC;CAClF,CAAC,cAAc,GAAG,WAAU;CAC5B,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAC;CACpE,CAAC,OAAO,KAAK;CACb,CAAC;CACM,SAAS,WAAW,GAAG;CAC9B,CAAC,GAAG,GAAG,KAAI;CACX,CAAC,YAAY,GAAG,KAAI;CACpB,CAAC,iBAAiB,GAAG,KAAI;CACzB,CAAC;AACD;CACO,SAASC,cAAY,CAAC,SAAS,EAAE;CACxC,CAAC,IAAI,SAAS,CAAC,MAAM;CACrB,EAAE,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAM;CACtD;CACA,EAAE,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAS;CAC/C,CAAC;AACD;CACO,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,EAAC;CACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CAC9B,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,EAAC;CACtD,CAAC;AACW,OAAC,OAAO,GAAG,QAAO;CAC9B,IAAI,cAAc,GAAG,IAAI,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAC;AAC3C,OAAC,MAAM,GAAG,cAAc,CAAC,OAAM;AAC/B,OAAC,cAAc,GAAG,cAAc,CAAC,eAAc;AAC/C,OAAC,MAAM,GAAG,cAAc,CAAC,OAAM;AAC/B,OAAC,eAAe,GAAG;CAC/B,CAAC,KAAK,EAAE,CAAC;CACT,CAAC,MAAM,EAAE,CAAC;CACV,CAAC,aAAa,EAAE,CAAC;CACjB,CAAC,WAAW,EAAE,CAAC;CACf,EAAC;CACD,IAAI,QAAQ,GAAG,IAAI,YAAY,CAAC,CAAC,EAAC;CAClC,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAC;CAC5C,SAAS,YAAY,CAAC,aAAa,EAAE;CAC5C,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,cAAa;CAC5B,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;CACxE,CAAC,OAAO,CAAC,CAAC,UAAU,GAAG,aAAa,IAAI,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU;CAC3F;;CCpqCA,IAAI,YAAW;CACf,IAAI;CACJ,CAAC,WAAW,GAAG,IAAI,WAAW,GAAE;CAChC,CAAC,CAAC,OAAO,KAAK,EAAE,EAAE;CAClB,IAAI,UAAU,EAAE,iBAAgB;CAChC,MAAM,aAAa,GAAG,OAAO,MAAM,KAAK,YAAW;CACnD,MAAM,iBAAiB,GAAG,aAAa;CACvC,CAAC,SAAS,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,GAAG,WAAU;CACxE,MAAM,SAAS,GAAG,aAAa,GAAG,MAAM,GAAG,WAAU;CACrD,MAAM,eAAe,GAAG,aAAa,GAAG,WAAW,GAAG,WAAU;CAChE,IAAI,MAAM,EAAE,WAAU;CACtB,IAAI,WAAU;CACd,IAAI,QAAQ,GAAG,EAAC;CAChB,IAAI,QAAO;CACX,IAAI,cAAc,GAAG,KAAI;CACzB,IAAI,iBAAgB;CACpB,MAAM,eAAe,GAAG,OAAM;CAC9B,MAAM,WAAW,GAAG,kBAAiB;CAC9B,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,EAAC;CACzC,MAAM,KAAK,SAAS,OAAO,CAAC;CACnC,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,KAAK,CAAC,OAAO,EAAC;CAChB,EAAE,IAAI,CAAC,MAAM,GAAG,EAAC;CAEjB,EAAE,IAAI,MAAK;CACX,EAAE,IAAI,gBAAe;CACrB,EAAE,IAAI,WAAU;CAChB,EAAE,IAAI,aAAY;CAClB,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,MAAM,EAAE,QAAQ,EAAE;CAC9E,GAAG,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;CAC1E,GAAG,GAAG,CAAC,WAAW,IAAI,WAAW,CAAC,UAAU;CAC5C,GAAG,SAAS,MAAM,EAAE,QAAQ,EAAE;CAC9B,IAAI,OAAO,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;CAC5E,IAAI,GAAG,MAAK;AACZ;CACA,EAAE,IAAI,KAAK,GAAG,KAAI;CAClB,EAAE,IAAI,CAAC,OAAO;CACd,GAAG,OAAO,GAAG,GAAE;CACf,EAAE,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,WAAU;CAClD,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,eAAc;CACxE,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,oBAAmB;CACvD,EAAE,IAAI,mBAAmB,IAAI,IAAI;CACjC,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,EAAE,GAAG,EAAC;CACrD,EAAE,IAAI,mBAAmB,GAAG,IAAI;CAChC,GAAG,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC;CACxD,EAAE,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,SAAS,IAAI,SAAS,EAAE;CACjE,GAAG,IAAI,CAAC,SAAS,GAAG,KAAI;CACxB,GAAG;CACH,EAAE,IAAI,gBAAgB,GAAG,OAAO,CAAC,iBAAgB;CACjD,EAAE,IAAI,gBAAgB,IAAI,IAAI;CAC9B,GAAG,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,GAAG,GAAE;CACnD,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,KAAK;CACrD,GAAG,IAAI,CAAC,UAAU,GAAG,GAAE;CACvB;CACA,EAAE,IAAI,iBAAiB,GAAG,mBAAmB,GAAG,EAAE,KAAK,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAC;CACnG,EAAE,IAAI,aAAa,GAAG,mBAAmB,GAAG,KAAI;CAChD,EAAE,IAAI,cAAc,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,KAAI;CACpE,EAAE,IAAI,cAAc,GAAG,IAAI,EAAE;CAC7B,GAAG,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC;CAC1E,GAAG;CACH,EAAE,IAAI,iBAAiB,GAAG,GAAE;CAC5B,EAAE,IAAI,gBAAgB,GAAG,EAAC;CAC1B,EAAE,IAAI,oCAAoC,GAAG,EAAC;AAC9C;CACA,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,KAAK,EAAE,aAAa,EAAE;CAC3D,GAAG,IAAI,CAAC,MAAM,EAAE;CAChB,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAC;CACxC,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,EAAC;CAC5F,IAAI,QAAQ,GAAG,EAAC;CAChB,IAAI;CACJ,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CAC/B,GAAG,IAAI,OAAO,GAAG,QAAQ,GAAG,KAAK,EAAE;CACnC;CACA,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAC;CACjD,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAC;CACrG,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CAChC,IAAI,QAAQ,GAAG,EAAC;CAChB,IAAI;CACJ,IAAI,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,WAAU;CAC1C,GAAG,KAAK,GAAG,SAAQ;CACnB,GAAG,IAAI,aAAa,GAAG,mBAAmB,EAAE,QAAQ,KAAK,aAAa,GAAG,IAAI,EAAC;CAC9E,GAAG,YAAY,GAAG,KAAK,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,GAAG,KAAI;CAC1D,GAAG,IAAI,KAAK,CAAC,aAAa,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACzD,IAAI,cAAc,GAAG,GAAE;CACvB,IAAI,cAAc,CAAC,IAAI,GAAG,SAAQ;CAClC,IAAI;CACJ,IAAI,cAAc,GAAG,KAAI;CACzB,GAAG,UAAU,GAAG,KAAK,CAAC,WAAU;CAChC,GAAG,IAAI,UAAU,EAAE;CACnB,IAAI,IAAI,UAAU,CAAC,aAAa;CAChC,KAAK,UAAU,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,EAAE,EAAC;CAC/D,IAAI,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;CACnD,IAAI,IAAI,YAAY,GAAG,mBAAmB,EAAE;CAC5C;CACA,KAAK,MAAM,IAAI,KAAK,CAAC,oGAAoG,GAAG,UAAU,CAAC,YAAY,CAAC;CACpJ,KAAK;CACL,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;CACjC;CACA,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CACjD,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;CAC5C,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,EAAC;CAC9B,MAAM,IAAI,CAAC,IAAI;CACf,OAAO,QAAQ;CACf,MAAM,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,YAAW;CAC7D,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACnD,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;CACxB,OAAO,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACvC,OAAO,IAAI,CAAC,cAAc,EAAE;CAC5B,QAAQ,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CAC9D,QAAQ;CACR,OAAO,UAAU,GAAG,eAAc;CAClC,OAAO;CACP,MAAM,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAI;CAC1C,MAAM;CACN,KAAK,IAAI,CAAC,yBAAyB,GAAG,aAAY;CAClD,KAAK;CACL,IAAI,IAAI,CAAC,YAAY,EAAE;CACvB,KAAK,UAAU,CAAC,MAAM,GAAG,YAAY,GAAG,KAAI;CAC5C,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,eAAe;CACtB,IAAI,eAAe,GAAG,MAAK;CAC3B,GAAG,IAAI,aAAa,CAAC;CACrB,GAAG,IAAI;CACP,IAAI,IAAI,KAAK,CAAC,qBAAqB,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM;CACjG,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC;CACxB;CACA,KAAK,IAAI,CAAC,KAAK,EAAC;CAChB,IAAI,IAAI,UAAU,GAAG,cAAc,CAAC;CACpC,IAAI,IAAI,cAAc;CACtB,KAAK,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAC;CACjC,IAAI,IAAI,YAAY,IAAI,YAAY,CAAC,WAAW,EAAE;CAClD,KAAK,IAAI,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC7F,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAChC,KAAK,IAAI,iBAAiB,GAAG,CAAC,CAAC,CAAC;CAChC,KAAK,OAAO,UAAU,IAAI,CAAC,GAAG,CAAC,EAAE;CACjC,MAAM,IAAI,cAAc,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;CAC3D,MAAM,IAAI,cAAc,IAAI,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC,IAAI,iBAAiB,KAAK,CAAC,CAAC;CAC3F,OAAO,iBAAiB,GAAG,CAAC,CAAC;CAC7B,MAAM,IAAI,cAAc,IAAI,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE;CAC1D,OAAO,IAAI,iBAAiB,IAAI,CAAC;CACjC,QAAQ,iBAAiB,IAAI,CAAC,CAAC;CAC/B,OAAO,MAAM;CACb,OAAO,IAAI,iBAAiB,IAAI,CAAC,EAAE;CACnC;CACA,QAAQ,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK;CACxD,SAAS,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,iBAAiB,EAAC;CAC/E,QAAQ,iBAAiB,GAAG,CAAC,CAAC,CAAC;CAC/B,QAAQ;CACR,OAAO,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC;CACxC,OAAO,CAAC,EAAE,CAAC;CACX,OAAO;CACP,MAAM;CACN,KAAK,IAAI,iBAAiB,IAAI,CAAC,IAAI,UAAU,EAAE;CAC/C;CACA,MAAM,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK;CACtD,OAAO,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,iBAAiB,EAAC;CAC7E,MAAM;CACN,KAAK,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;CACxC,KAAK,IAAI,QAAQ,GAAG,OAAO;CAC3B,MAAM,QAAQ,CAAC,QAAQ,EAAC;CACxB,KAAK,KAAK,CAAC,MAAM,GAAG,SAAQ;CAC5B,KAAK,IAAI,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAC;CAC9E,KAAK,YAAY,GAAG,KAAI;CACxB,KAAK,OAAO,UAAU;CACtB,KAAK;CACL,IAAI,KAAK,CAAC,MAAM,GAAG,SAAQ;CAC3B,IAAI,IAAI,aAAa,GAAG,iBAAiB,EAAE;CAC3C,KAAK,MAAM,CAAC,KAAK,GAAG,MAAK;CACzB,KAAK,MAAM,CAAC,GAAG,GAAG,SAAQ;CAC1B,KAAK,OAAO,MAAM;CAClB,KAAK;CACL,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;CAC3C,IAAI,CAAC,MAAM,KAAK,EAAE;CAClB,IAAI,aAAa,GAAG,KAAK,CAAC;CAC1B,IAAI,MAAM,KAAK,CAAC;CAChB,IAAI,SAAS;CACb,IAAI,IAAI,UAAU,EAAE;CACpB,KAAK,eAAe,EAAE,CAAC;CACvB,KAAK,IAAI,eAAe,IAAI,KAAK,CAAC,cAAc,EAAE;CAClD,MAAM,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;CACrD;CACA,MAAM,IAAI,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAC;CACzD,MAAM,IAAI,aAAa,GAAG,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;CAC/D,MAAM,IAAI,CAAC,aAAa,EAAE;CAC1B,OAAO,IAAI,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,aAAa,CAAC,YAAY,CAAC,KAAK,KAAK,EAAE;CACtF;CACA,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;CAC/C,QAAQ;CACR,OAAO,KAAK,CAAC,yBAAyB,GAAG,aAAY;CACrD;CACA,OAAO,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,KAAI;CACpD,OAAO,OAAO,YAAY;CAC1B,OAAO;CACP,MAAM;CACN,KAAK;CACL;CACA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,KAAI;CACjD,IAAI,IAAI,aAAa,GAAG,iBAAiB;CACzC,KAAK,QAAQ,GAAG,MAAK;CACrB,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,eAAe,GAAG,MAAM;CAChC,GAAG,IAAI,oCAAoC,GAAG,EAAE;CAChD,IAAI,oCAAoC,GAAE;CAC1C,GAAG,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;CAClD,GAAG,IAAI,UAAU,CAAC,MAAM,GAAG,YAAY,IAAI,CAAC,YAAY;CACxD,IAAI,UAAU,CAAC,MAAM,GAAG,aAAY;CACpC,GAAG,IAAI,gBAAgB,GAAG,KAAK,EAAE;CACjC;CACA,IAAI,UAAU,CAAC,WAAW,GAAG,KAAI;CACjC,IAAI,oCAAoC,GAAG,EAAC;CAC5C,IAAI,gBAAgB,GAAG,EAAC;CACxB,IAAI,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;CACpC,KAAK,iBAAiB,GAAG,GAAE;CAC3B,IAAI,MAAM,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE;CAC7D,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC9D,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,EAAC;CAC5C,KAAK;CACL,IAAI,iBAAiB,GAAG,GAAE;CAC1B,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,KAAK;CAC/B,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC,OAAM;CAC5B,GAAG,IAAI,MAAM,GAAG,IAAI,EAAE;CACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CACtC,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC1C,IAAI,QAAQ,IAAI,EAAC;CACjB,IAAI;CACJ,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACpC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;CAClB,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,IAAI,GAAG,CAAC,KAAK,KAAK;CAC1B,GAAG,IAAI,QAAQ,GAAG,OAAO;CACzB,IAAI,MAAM,GAAG,QAAQ,CAAC,QAAQ,EAAC;AAC/B;CACA,GAAG,IAAI,IAAI,GAAG,OAAO,MAAK;CAC1B,GAAG,IAAI,OAAM;CACb,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE;CAC1B,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,OAAM;CAChC,IAAI,IAAI,cAAc,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,MAAM,EAAE;CAChE,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,SAAS,IAAI,eAAe,EAAE;CAC/D,MAAM,IAAI,SAAQ;CAClB,MAAM,IAAI,QAAQ,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,GAAE;CAC3G,MAAM,IAAI,QAAQ,GAAG,QAAQ,GAAG,OAAO;CACvC,OAAO,MAAM,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAC;CAC7C,MAAM,IAAI,WAAU;CACpB,MAAM,IAAI,cAAc,CAAC,QAAQ,EAAE;CACnC,OAAO,UAAU,GAAG,eAAc;CAClC,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAI;CAC9B,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,QAAQ,GAAG,QAAQ,GAAG,MAAK;CAClC,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAC;CACnC,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,GAAG,QAAQ,EAAC;CAC9E,OAAO,MAAM;CACb,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,QAAQ,GAAG,QAAQ,GAAG,MAAK;CAClC,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO;CACP,MAAM,cAAc,GAAG,CAAC,EAAE,EAAE,EAAE,EAAC;CAC/B,MAAM,cAAc,CAAC,QAAQ,GAAG,UAAU,CAAC;CAC3C,MAAM,cAAc,CAAC,IAAI,GAAG,EAAC;CAC7B,MAAM,cAAc,CAAC,QAAQ,GAAG,SAAQ;CACxC,MAAM;CACN,KAAK,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,EAAC;CAC1C,KAAK,cAAc,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,MAAK;CAC7C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;CAC5C,KAAK,MAAM;CACX,KAAK;CACL,IAAI,IAAI,WAAU;CAClB;CACA,IAAI,IAAI,SAAS,GAAG,IAAI,EAAE;CAC1B,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK,MAAM,IAAI,SAAS,GAAG,KAAK,EAAE;CAClC,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK,MAAM,IAAI,SAAS,GAAG,OAAO,EAAE;CACpC,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK,MAAM;CACX,KAAK,UAAU,GAAG,EAAC;CACnB,KAAK;CACL,IAAI,IAAI,QAAQ,GAAG,SAAS,GAAG,EAAC;CAChC,IAAI,IAAI,QAAQ,GAAG,QAAQ,GAAG,OAAO;CACrC,KAAK,MAAM,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAC;AAC3C;CACA,IAAI,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;CACzC,KAAK,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,GAAG,QAAQ,GAAG,WAAU;CACvD,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;CACrC,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAC;CAC9B,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE;CACrB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,GAAE;CACjC,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,EAAE;CAC7B,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,KAAI;CAC7C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO,MAAM;CACb,OAAO,CAAC,EAAE,GAAG,MAAM,MAAM,MAAM;CAC/B,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,MAAM,MAAM;CAC3D,QAAQ;CACR,OAAO,EAAE,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM,EAAC;CAC3D,OAAO,CAAC,GAAE;CACV,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;CAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,KAAI;CACrD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;CACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO,MAAM;CACb,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;CAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;CACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;CAC/C,OAAO;CACP,MAAM;CACN,KAAK,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAU;CACjD,KAAK,MAAM;CACX,KAAK,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,QAAQ,GAAG,UAAU,EAAC;CACtD,KAAK;AACL;CACA,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;CACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CACvC,KAAK,MAAM,IAAI,MAAM,GAAG,KAAK,EAAE;CAC/B,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;CACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;CAC1E,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;CAChC,KAAK,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CACjC,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;CACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;CAC1E,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACrC,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACvC,KAAK,MAAM;CACX,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;CACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;CAC1E,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC3C,KAAK,QAAQ,IAAI,EAAC;CAClB,KAAK;CACL,IAAI,QAAQ,IAAI,OAAM;CACtB,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;CACjC,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK,EAAE;CAC/B;CACA,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE;CACvH,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAK;CAChC,MAAM,MAAM,IAAI,KAAK,GAAG,KAAK,EAAE;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAK;CAChC,MAAM,MAAM,IAAI,KAAK,GAAG,OAAO,EAAE;CACjC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,IAAI,EAAC;CACrC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,KAAI;CACvC,MAAM,MAAM;CACZ,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC3C,MAAM,QAAQ,IAAI,EAAC;CACnB,MAAM;CACN,KAAK,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE;CACrC,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;CACzB,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,MAAK;CACxC,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;CAChC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,MAAK;CACxC,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;CAClC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC1C,MAAM,QAAQ,IAAI,EAAC;CACnB,MAAM,MAAM;CACZ,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC1C,MAAM,QAAQ,IAAI,EAAC;CACnB,MAAM;CACN,KAAK,MAAM;CACX,KAAK,IAAI,WAAU;CACnB,KAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,KAAK,GAAG,WAAW,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE;CAC5F,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC5C,MAAM,IAAI,SAAQ;CAClB,MAAM,IAAI,UAAU,GAAG,CAAC;CACxB;CACA,QAAQ,CAAC,CAAC,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE;CACzH,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,MAAM;CACb,OAAO;CACP,OAAO,QAAQ,GAAE;CACjB,MAAM;CACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC3C,KAAK,QAAQ,IAAI,EAAC;CAClB,KAAK;CACL,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;CACxD,IAAI,IAAI,CAAC,KAAK;CACd,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,SAAS;CACT,KAAK,IAAI,YAAY,EAAE;CACvB,MAAM,IAAI,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAC;CAC3C,MAAM,IAAI,OAAO,EAAE;CACnB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;CACxB,QAAQ,IAAI,WAAW,GAAG,YAAY,CAAC,WAAW,KAAK,YAAY,CAAC,WAAW,GAAG,EAAE,EAAC;CACrF,QAAQ,OAAO,CAAC,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC;CAC9C,QAAQ;CACR,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAChC,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,EAAC;CACjD,OAAO,QAAQ,IAAI,EAAC;CACpB,OAAO,MAAM;CACb,OAAO;CACP,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAC;CAC5D,MAAM;CACN,KAAK,IAAI,WAAW,GAAG,KAAK,CAAC,YAAW;CACxC,KAAK,IAAI,WAAW,KAAK,MAAM,EAAE;CACjC,MAAM,WAAW,CAAC,KAAK,EAAC;CACxB,MAAM,MAAM,IAAI,WAAW,KAAK,KAAK,EAAE;CACvC,MAAM,SAAS,CAAC,KAAK,EAAC;CACtB,MAAM,MAAM,IAAI,WAAW,KAAK,GAAG,EAAE;CACrC,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1D,WAAW;CACX,OAAO,MAAM,GAAG,KAAK,CAAC,KAAI;CAC1B,OAAO,IAAI,MAAM,GAAG,IAAI,EAAE;CAC1B,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CAC1C,QAAQ,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CACpC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CACjC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACxC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CAC1C,QAAQ,MAAM;CACd,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CACjC,QAAQ,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC9C,QAAQ,QAAQ,IAAI,EAAC;CACrB,QAAQ;CACR,OAAO,KAAK,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,KAAK,EAAE;CAC5C,QAAQ,IAAI,CAAC,GAAG,EAAC;CACjB,QAAQ,IAAI,CAAC,UAAU,EAAC;CACxB,QAAQ;CACR,OAAO;CACP,MAAM,MAAM;CACZ,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACzD,OAAO,IAAI,cAAc,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC/C,OAAO,IAAI,KAAK,YAAY,cAAc,EAAE;CAC5C,QAAQ,IAAI,SAAS,GAAG,UAAU,CAAC,CAAC,EAAC;CACrC,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE;CAC7B,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE;CAC7B,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CACnC,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC,KAAI;CAC7C,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CAChC,UAAU;CACV,SAAS,IAAI,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC;CAC5D,SAAS,IAAI,WAAW,KAAK,KAAK,EAAE;CACpC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;CACpC,WAAW,SAAS,CAAC,KAAK,EAAC;CAC3B,WAAW,MAAM;CACjB,WAAW,WAAW,CAAC,KAAK,EAAC;CAC7B,WAAW;CACX,UAAU,MAAM;CAChB,UAAU,IAAI,CAAC,WAAW,EAAC;CAC3B,UAAU;CACV,SAAS,MAAM;CACf,SAAS;CACT,QAAQ,IAAI,aAAa,GAAG,OAAM;CAClC,QAAQ,IAAI,iBAAiB,GAAG,WAAU;CAC1C,QAAQ,IAAI,eAAe,GAAG,SAAQ;CACtC,QAAQ,MAAM,GAAG,KAAI;CACrB,QAAQ,IAAI,OAAM;CAClB,QAAQ,IAAI;CACZ,SAAS,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,IAAI,KAAK;CAC7D;CACA,UAAU,MAAM,GAAG,cAAa;CAChC,UAAU,aAAa,GAAG,KAAI;CAC9B,UAAU,QAAQ,IAAI,KAAI;CAC1B,UAAU,IAAI,QAAQ,GAAG,OAAO;CAChC,WAAW,QAAQ,CAAC,QAAQ,EAAC;CAC7B,UAAU,OAAO;CACjB,WAAW,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;CACxD,WAAW;CACX,UAAU,EAAE,IAAI,EAAC;CACjB,SAAS,SAAS;CAClB;CACA,SAAS,IAAI,aAAa,EAAE;CAC5B,UAAU,MAAM,GAAG,cAAa;CAChC,UAAU,UAAU,GAAG,kBAAiB;CACxC,UAAU,QAAQ,GAAG,gBAAe;CACpC,UAAU,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CACtC,UAAU;CACV,SAAS;CACT,QAAQ,IAAI,MAAM,EAAE;CACpB,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;CAC/C,UAAU,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,EAAC;CAC5C,SAAS,QAAQ,GAAG,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAC;CAChF,SAAS;CACT,QAAQ,MAAM;CACd,QAAQ;CACR,OAAO;CACP;CACA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;CAChC,OAAO,SAAS,CAAC,KAAK,EAAC;CACvB,OAAO,MAAM;CACb;CACA,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE;CACzB,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,GAAE;CACnC;CACA,QAAQ,IAAI,IAAI,KAAK,KAAK;CAC1B,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC;CAC1B,QAAQ;AACR;CACA;CACA,OAAO,IAAI,IAAI,KAAK,UAAU;CAC9B,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACrE;CACA;CACA,OAAO,WAAW,CAAC,KAAK,EAAC;CACzB,OAAO;CACP,MAAM;CACN,KAAK;CACL,IAAI,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;CAClC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAI;CAC5C,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;CACjC,IAAI,IAAI,KAAK,GAAG,kBAAkB,IAAI,KAAK,IAAI,CAAC,kBAAkB,EAAE;CACpE;CACA,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC5C,KAAK,MAAM,IAAI,KAAK,GAAG,mBAAmB,IAAI,KAAK,GAAG,CAAC,EAAE;CACzD;CACA,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,EAAC;CAC7C,KAAK,MAAM;CACX;CACA,KAAK,IAAI,IAAI,CAAC,kBAAkB,EAAE;CAClC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,EAAC;CACpD,MAAM,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;CAC1C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;CACpC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;CACtI,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,QAAQ,EAAE,CAAC;CACjB,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,MAAM,IAAI,KAAK,GAAG,EAAE,CAAC;CACrB,MAAM,IAAI,WAAW,CAAC;CACtB,MAAM,GAAG;CACT,OAAO,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;CACvC,OAAO,WAAW,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9F,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACxB,OAAO,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;CAC3B,OAAO,QAAQ,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,EAAE;CAChF,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;CACxC,MAAM,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG;CACzC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAC/C,OAAO;CACP,MAAM,MAAM;CACZ,MAAM,MAAM;CACZ,MAAM,MAAM,IAAI,UAAU,CAAC,KAAK,GAAG,iEAAiE;CACpG,OAAO,+EAA+E;CACtF,OAAO,2CAA2C,CAAC;CACnD,MAAM;CACN,KAAK;CACL,IAAI,QAAQ,IAAI,EAAC;CACjB,IAAI,MAAM,IAAI,IAAI,KAAK,WAAW,EAAE;CACpC,IAAI,IAAI,IAAI,CAAC,oBAAoB;CACjC,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,SAAS;CACT,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CAC3B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CAC3B,KAAK;CACL,IAAI,MAAM;CACV,IAAI,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;CAC5C,IAAI;CACJ,IAAG;AACH;CACA,EAAE,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,KAAK;CAChH;CACA,GAAG,IAAI,IAAI,CAAC;CACZ,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;CACxB,IAAI,IAAI,GAAG,EAAE,CAAC;CACd,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAC5B,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC;CACnF,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;CAC5C,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACrB,KAAK;CACL,IAAI,MAAM;CACV,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAC;CAC9B,IAAI;CACJ,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC,OAAM;CAC3B,GAAG,IAAI,MAAM,GAAG,IAAI,EAAE;CACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;CACtC,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CAC1C,IAAI,QAAQ,IAAI,EAAC;CACjB,IAAI;CACJ,GAAG,IAAI,IAAG;CACV,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE;CAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACrC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;CAClB,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,EAAC;CAC1B,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC;CACjC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;AACL;CACA,IAAI,MAAM;CACV,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;CACrC,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,EAAC;CACxB,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE,CAAC,MAAM,KAAK;CACd,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,IAAI,YAAY,GAAG,QAAQ,GAAG,MAAK;CACtC,GAAG,QAAQ,IAAI,EAAC;CAChB,GAAG,IAAI,IAAI,GAAG,EAAC;CACf,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAC3B,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CACnF,KAAK,IAAI,CAAC,GAAG,EAAC;CACd,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK,IAAI,GAAE;CACX,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,IAAI,GAAG,MAAM,EAAE;CACtB,IAAI,MAAM,IAAI,KAAK,CAAC,6DAA6D;CACjF,IAAI,4DAA4D,CAAC,CAAC;CAClE,IAAI;CACJ,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,IAAI,EAAC;CAC7C,GAAG,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,KAAI;CAC7C,IAAG;AACH;CACA,EAAE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,KAAK,KAAK,GAAG,gBAAgB;CAClE,EAAE,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,iBAAiB;CACnD,EAAE,CAAC,MAAM,KAAK;CACd,GAAG,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;CAC5G,GAAG,IAAI,YAAY,GAAG,QAAQ,EAAE,GAAG,MAAK;CACxC,GAAG,IAAI,UAAS;CAChB,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAC3B,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CACnF,KAAK,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACrC,KAAK,IAAI,cAAc;CACvB,MAAM,UAAU,GAAG,eAAc;CACjC,UAAU;CACV;CACA,MAAM,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAC;CACpC,MAAM,IAAI,cAAc,GAAG,WAAU;CACrC,MAAM,UAAU,GAAG,UAAU,CAAC,YAAW;CACzC,MAAM,IAAI,cAAc,GAAG,EAAC;CAC5B,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACnD,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;CACxB,OAAO,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACvC,OAAO,IAAI,CAAC,cAAc,EAAE;CAC5B,QAAQ,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CAC9D,QAAQ,cAAc,GAAE;CACxB,QAAQ;CACR,OAAO,UAAU,GAAG,eAAc;CAClC,OAAO;CACP,MAAM,IAAI,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,QAAQ,EAAE;CAChD;CACA,OAAO,QAAQ,GAAE;CACjB,OAAO,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAC;CAClD,OAAO;CACP,OAAO,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,EAAC;CACtE,MAAM,SAAS,GAAG,KAAI;CACtB,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,EAAC;CACtC,MAAM;CACN,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;CACL,IAAI;CACJ,GAAG,IAAI,CAAC,SAAS,EAAE;CACnB,IAAI,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,EAAC;CAC5C,IAAI,IAAI,QAAQ;CAChB,KAAK,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,SAAQ;CAC5C;CACA,KAAK,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC;CACtE,IAAI;CACJ,GAAG;CACH,EAAE,CAAC,MAAM,KAAK;CACd,GAAG,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;CAC5G,GAAG,IAAI,cAAc,GAAG,EAAC;CACzB,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CAC1G,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;CACpC,IAAI,IAAI,CAAC,cAAc,EAAE;CACzB,KAAK,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;CAC3D,KAAK,cAAc,GAAE;CACrB,KAAK;CACL,IAAI,UAAU,GAAG,eAAc;CAC/B,IAAI;CACJ,GAAG,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,EAAC;CAC3C,GAAG,IAAI,QAAQ,EAAE;CACjB,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,iBAAiB,EAAE;CAC/C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,KAAI;CAC5D,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,QAAQ,IAAI,EAAC;CACvC,KAAK;CACL,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,IAAI,MAAM;CACV,IAAI,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,cAAc,EAAC;CACrF,IAAI;CACJ;CACA,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM;CACzB,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CACnF,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;CACtB,KAAK;CACL,IAAG;AACH;CACA;CACA,EAAE,MAAM,eAAe,GAAG,OAAO,IAAI,CAAC,UAAU,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;AAClF;CACA,EAAE,MAAM,WAAW,GAAG,eAAe,GAAG,CAAC,MAAM,KAAK;CACpD,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAC;CAC3E,GAAG,GAAG,YAAW;AACjB;CACA,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;CAC5B,GAAG,IAAI,QAAO;CACd,GAAG,IAAI,GAAG,GAAG,SAAS,EAAE;CACxB;CACA,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,eAAe;CACvC,KAAK,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC;CAC9E,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe;CACtC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAC;CACpG,IAAI;CACJ,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,GAAE;CACjF,GAAG,IAAI,SAAS,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAC;CACjD,GAAG,UAAU,GAAG,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,EAAC;CACvG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAC;CACrC,GAAG,IAAI,MAAM,CAAC,IAAI;CAClB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC;CACzC;CACA,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAC;CAC3C,GAAG,QAAQ,IAAI,MAAK;CACpB,GAAG,KAAK,GAAG,EAAC;CACZ,GAAG,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,GAAE;CAClC,GAAG,OAAO,MAAM,GAAG,SAAS;CAC5B,IAAG;CACH,EAAE,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,KAAK;CAC1D,GAAG,IAAI,QAAQ,GAAG,UAAU,CAAC,OAAM;CACnC,GAAG,IAAI,CAAC,QAAQ;CAChB,IAAI,QAAQ,GAAG,KAAI;CACnB,GAAG,IAAI,QAAQ,GAAG,aAAa,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;CAClG,IAAI,QAAQ,GAAG,UAAU,CAAC,UAAS;CACnC,IAAI,IAAI,EAAE,QAAQ,GAAG,cAAc,CAAC;CACpC,KAAK,QAAQ,GAAG,cAAa;CAC7B,IAAI,UAAU,CAAC,SAAS,GAAG,QAAQ,GAAG,EAAC;CACvC,IAAI,MAAM;CACV,IAAI,IAAI,QAAQ,IAAI,cAAc;CAClC,KAAK,QAAQ,GAAG,cAAa;CAC7B,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAC;CACpC,IAAI;CACJ,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,EAAC;CACrG,GAAG,UAAU,CAAC,aAAa,CAAC,GAAG,SAAQ;CACvC,GAAG,UAAU,CAAC,QAAQ,GAAG,KAAI;CAC7B,GAAG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAI;AACrC;CACA,GAAG,IAAI,QAAQ,GAAG,aAAa,EAAE;CACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAI;CACxB,IAAI,UAAU,CAAC,YAAY,GAAG,QAAQ,GAAG,KAAI;CAC7C,IAAI,eAAe,GAAG,KAAI;CAC1B,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;CACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,IAAI,KAAI;CAClD,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK,MAAM;CACX,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK;CACL,IAAI,MAAM;CACV,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;CACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,IAAI,KAAI;CAClD,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK,MAAM;CACX,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;CAClC,KAAK;AACL;CACA,IAAI,IAAI,cAAc;CACtB,KAAK,gBAAgB,IAAI,oCAAoC,GAAG,eAAc;CAC9E;CACA,IAAI,IAAI,iBAAiB,CAAC,MAAM,IAAI,gBAAgB;CACpD,KAAK,iBAAiB,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC,GAAG,EAAC;CACjD,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAC;CACtC,IAAI,IAAI,CAAC,IAAI,EAAC;CACd,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,eAAe,EAAE,cAAc,KAAK;CACjF,GAAG,IAAI,UAAU,GAAG,OAAM;CAC1B,GAAG,IAAI,YAAY,GAAG,SAAQ;CAC9B,GAAG,IAAI,WAAW,GAAG,QAAO;CAC5B,GAAG,IAAI,SAAS,GAAG,MAAK;CACxB,GAAG,MAAM,GAAG,WAAU;CACtB,GAAG,QAAQ,GAAG,EAAC;CACf,GAAG,KAAK,GAAG,EAAC;CACZ,GAAG,IAAI,CAAC,MAAM;CACd,IAAI,UAAU,GAAG,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAC;CACrD,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;CAC/B,GAAG,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAC;CAC9C,GAAG,UAAU,GAAG,OAAM;CACtB,GAAG,IAAI,YAAY,GAAG,SAAQ;CAC9B,GAAG,MAAM,GAAG,WAAU;CACtB,GAAG,QAAQ,GAAG,aAAY;CAC1B,GAAG,OAAO,GAAG,YAAW;CACxB,GAAG,KAAK,GAAG,UAAS;CACpB,GAAG,IAAI,YAAY,GAAG,CAAC,EAAE;CACzB,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,EAAC;CAC5C,IAAI,IAAI,MAAM,GAAG,OAAO;CACxB,KAAK,QAAQ,CAAC,MAAM,EAAC;CACrB,IAAI,IAAI,iBAAiB,GAAG,eAAe,GAAG,MAAK;CACnD,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,GAAG,YAAY,EAAE,iBAAiB,GAAG,CAAC,EAAE,QAAQ,EAAC;CACxF,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,iBAAiB,EAAC;CACpE,IAAI,QAAQ,GAAG,OAAM;CACrB,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,eAAe,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,EAAC;CACnD,IAAI;CACJ,IAAG;CACH,EAAE,MAAM,WAAW,GAAG,CAAC,MAAM,KAAK;CAClC,GAAG,IAAI,WAAW,GAAG,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,kBAAkB,KAAK;CACzI,IAAI,IAAI,kBAAkB;CAC1B,KAAK,OAAO,eAAe,GAAG,IAAI,CAAC;CACnC,IAAI,QAAQ,GAAG,WAAW,CAAC;CAC3B,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC;CAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;CAChB,IAAI,eAAe,EAAE,CAAC;CACtB,IAAI,IAAI,WAAW,KAAK,MAAM,EAAE;CAChC,KAAK,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;CAC7C,KAAK;CACL,IAAI,OAAO,QAAQ,CAAC;CACpB,IAAI,EAAE,IAAI,CAAC,CAAC;CACZ,GAAG,IAAI,WAAW,KAAK,CAAC;CACxB,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;CAC/B,GAAG,QAAQ,GAAG,WAAW,CAAC;CAC1B,IAAG;CACH,EAAE;CACF,CAAC,SAAS,CAAC,MAAM,EAAE;CACnB;CACA,EAAE,MAAM,GAAG,OAAM;CACjB,EAAE,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAC;CAC1G,EAAE,QAAQ,GAAG,EAAC;CACd,EAAE;CACF,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,EAAE;CACtB,EAAE,QAAQ,GAAG,KAAK,CAAC;CACnB,EAAE;CACF,CAAC,IAAI,QAAQ,GAAG;CAChB,EAAE,OAAO,QAAQ,CAAC;CAClB,EAAE;CACF,CAAC,eAAe,GAAG;CACnB,EAAE,IAAI,IAAI,CAAC,UAAU;CACrB,GAAG,IAAI,CAAC,UAAU,GAAG,GAAE;CACvB,EAAE,IAAI,IAAI,CAAC,YAAY;CACvB,GAAG,IAAI,CAAC,YAAY,GAAG,GAAE;CACzB,EAAE;CACF,CAAC;AACD;CACA,gBAAgB,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,iBAAiB,QAAQ,EAAE,MAAM,GAAE;CACtJ,UAAU,GAAG,CAAC;CACd,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACpC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,KAAI;CACrC,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,OAAO,GAAG,WAAW,EAAE;CACtG;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC5D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAC;CAC1C,GAAG,MAAM,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,WAAW,EAAE;CACnD;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAC;CAC7D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,WAAW,KAAK,CAAC,CAAC,EAAC;CAC3G,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,EAAE,OAAO,EAAC;CAC9C,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;CAC7B,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE;CAC3B,IAAI,gBAAgB,CAAC,CAAC,EAAC;CACvB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;CACrC,IAAI;CACJ;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC5D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM;CACT;CACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAC;CAC7D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAE;CAC1B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,OAAO,EAAC;CACnE,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAC;CACpE,GAAG;CACH,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,GAAG,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACnC,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;CACvB,GAAG,OAAO,IAAI,CAAC,EAAE,CAAC;CAClB,GAAG;CACH,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC;CAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;CACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;CACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CACzB,GAAG;CACH,EAAE,IAAI,CAAC,KAAK,EAAC;CACb,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACrC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;CACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;CACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CACzB,GAAG;CACH,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,EAAC;CAClD,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE;CACrC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;CACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;CACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;CACzB,GAAG;CACH,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,EAAC;CACrC,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE;CACrC,EAAE,IAAI,IAAI,CAAC,SAAS;CACpB,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAC;CACtD;CACA,GAAG,WAAW,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,gBAAgB,EAAC;CACxG,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,EAAE;CACpC,EAAE,IAAI,WAAW,GAAG,UAAU,CAAC,YAAW;CAC1C,EAAE,IAAI,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS;CACjD,GAAG,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,gBAAgB,EAAC;CACtF;CACA,GAAG,WAAW,CAAC,UAAU,EAAE,gBAAgB,EAAC;CAC5C,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE;CACrC,EAAE,IAAI,IAAI,CAAC,SAAS;CACpB,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAC;CACtD;CACA,GAAG,WAAW,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,gBAAgB,EAAC;CACxG,EAAE;CACF,CAAC,EAAE;CACH,CAAC,IAAI,CAAC,EAAE,EAAE,gBAAgB,EAAE;CAC5B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;CAC/C,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAI;CACzB,EAAE;CACF,CAAC,EAAC;AACF;CACA,SAAS,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE;CACpE,CAAC,IAAI,MAAM,GAAG,UAAU,CAAC,WAAU;CACnC,CAAC,IAAI,MAAM,GAAG,CAAC,GAAG,KAAK,EAAE;CACzB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,EAAC;CACjC,EAAE,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,OAAO,EAAE;CAClC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,EAAC;CACxC,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,KAAI;CAC1C,EAAE,MAAM;CACR,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;CACrE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAC;CAC5C,EAAE,QAAQ,IAAI,EAAC;CACf,EAAE;CACF,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1B,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1B,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,EAAC;CAChE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAC;CACtG,CAAC;CACD,SAAS,WAAW,CAAC,MAAM,EAAE,gBAAgB,EAAE;CAC/C,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,WAAU;CAC/B,CAAC,IAAI,MAAM,EAAE,SAAQ;CACrB,CAAC,IAAI,MAAM,GAAG,KAAK,EAAE;CACrB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;CAC7B,EAAE,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAC9B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;CACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CAClC,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACpC,EAAE,MAAM;CACR,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;CACrE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC3B,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;CACxC,EAAE,QAAQ,IAAI,EAAC;CACf,EAAE;CACF,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAC;CAC7B,CAAC;AACD;CACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;CAC5D,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,OAAM;CAC3B,CAAC,QAAQ,MAAM;CACf,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,CAAC;CACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE,KAAK,EAAE;CACT,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC5B,GAAG,KAAK;CACR,EAAE;CACF,GAAG,IAAI,MAAM,GAAG,KAAK,EAAE;CACvB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;CAC/B,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;CAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;CACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI,MAAM;CACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,GAAE;CACrC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,IAAI,KAAI;CAC9C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,KAAI;CAC7C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;CACtC,IAAI;CACJ,EAAE;CACF,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC1B,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAC;CAC7B,CAAC,QAAQ,IAAI,OAAM;CACnB,CAAC,OAAO,QAAQ;CAChB,CAAC;AACD;CACA,SAAS,SAAS,CAAC,UAAU,EAAE,WAAW,EAAE;CAC5C;CACA,CAAC,IAAI,OAAM;CACX,CAAC,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,EAAC;CAC5C,CAAC,IAAI,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,eAAc;CACjD,CAAC,OAAO,MAAM,GAAG,WAAW,CAAC,GAAG,EAAE,EAAE;CACpC,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,OAAM;CAC5B,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,GAAE;CACpB,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,cAAc,EAAE,MAAM,EAAE,OAAO,EAAC;CACjE,EAAE,cAAc,IAAI,EAAC;CACrB,EAAE,IAAI,QAAQ,GAAG,MAAM,GAAG,eAAc;CACxC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;CAC/B,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,GAAE;CACnC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,KAAI;CAC5C,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,KAAI;CAC3C,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAI;CACpC,EAAE,OAAO,GAAG,OAAM;CAClB,EAAE;CACF,CAAC,OAAO,UAAU;CAClB,CAAC;AACD;CACA,SAAS,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,iBAAiB,EAAE;CACtD,CAAC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;CAChC,EAAE,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAE,QAAQ,GAAG,iBAAiB,GAAG,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAC;CACvH,EAAE,cAAc,CAAC,eAAe,GAAG,QAAQ,GAAG,KAAK,CAAC;CACpD,EAAE,IAAI,YAAY,GAAG,eAAc;CACnC,EAAE,cAAc,GAAG,KAAI;CACvB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC;CACvB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC;CACvB,EAAE;CACF,CAAC;AACD;CACO,SAAS,YAAY,CAAC,SAAS,EAAE;CACxC,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE;CACtB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;CACzC,GAAG,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC;CAC7D,EAAE,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI;CACvC,GAAG,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC;CACpF,EAAE,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAC;CAC3C,EAAE,UAAU,CAAC,OAAO,CAAC,SAAS,EAAC;CAC/B,EAAE;CACF,CAACC,cAAkB,CAAC,SAAS,EAAC;CAC9B,CAAC;CACD,SAAS,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE;CAC9C,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,kBAAkB,KAAK;CACnD,EAAE,IAAI,UAAU,GAAG,CAAC,kBAAkB,KAAK,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,MAAM,kBAAkB,CAAC,MAAM,EAAC;CAChH,EAAE,IAAI,CAAC,UAAU;CACjB,GAAG,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;CAC9C,EAAE,OAAO,UAAU,CAAC;CACpB,GAAE;CACF,CAAC,OAAO,UAAU;CAClB,CAAC;AAKD;CACA,IAAI,YAAY,GAAG,IAAI,KAAK,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAC;AACvC,OAAC,IAAI,GAAG,YAAY,CAAC,KAAI;AACzB,OAAC,MAAM,GAAG,YAAY,CAAC,KAAI;AAC3B,OAAC,OAAO,GAAG,MAAK;AAGhB,OAAC,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,gBAAe;AAChE,OAAC,iBAAiB,GAAG,IAAG;AACxB,OAAC,iBAAiB,GAAG,KAAI;AACzB,OAAC,mBAAmB,GAAG;;CCnlCnC;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,QAAQ,EAAE,cAAc,EAAE,OAAO,GAAG,EAAE,EAAE;CACxD,EAAE,IAAI,CAAC,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;CAC7D,IAAI,MAAM,IAAI,KAAK,CAAC,wFAAwF,CAAC;CAC7G,GAAG,MAAM,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;CACpE,IAAI,OAAO,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;CAChD,GAAG,MAAM,IAAI,OAAO,cAAc,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,UAAU,EAAE;CACtH,IAAI,OAAO,aAAa,CAAC,cAAc,EAAE,OAAO,CAAC;CACjD,GAAG,MAAM;CACT,IAAI,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC;CACjH,GAAG;CACH,CAAC;AACD;CACA,WAAW,YAAY,EAAE,cAAc,EAAE,OAAO,EAAE;CAClD,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,EAAC;CAClC,EAAE,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE;CACtC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CAC3B,GAAG;CACH,CAAC;AACD;CACA,iBAAiB,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE;CACzD,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,EAAC;CAClC,EAAE,WAAW,MAAM,KAAK,IAAI,cAAc,EAAE;CAC5C,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;CAC3B,GAAG;CACH,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,SAAS,UAAU,EAAE,cAAc,EAAE,OAAO,GAAG,EAAE,EAAE;CAC1D,EAAE,IAAI,CAAC,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;CAC7D,IAAI,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC;CACjH,GAAG;AACH;CACA,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,EAAC;CACtC,EAAE,IAAI,WAAU;CAChB,EAAE,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK;CAC5B,IAAI,IAAI,OAAM;CACd;CACA,IAAI,IAAI,UAAU,EAAE;CACpB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC;CAChD,MAAM,UAAU,GAAG,UAAS;CAC5B,KAAK;AACL;CACA,IAAI,IAAI;CACR,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,EAAC;CAC5C,KAAK,CAAC,OAAO,GAAG,EAAE;CAClB,MAAM,IAAI,GAAG,CAAC,UAAU,EAAE;CAC1B,QAAQ,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAC;CAClD,QAAQ,MAAM,GAAG,GAAG,CAAC,OAAM;CAC3B,OAAO,MAAM;CACb,QAAQ,MAAM,GAAG;CACjB,OAAO;CACP,KAAK;CACL,IAAI,OAAO,MAAM;CACjB,IAAG;AACH;CACA,EAAE,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;CAC7D,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI;CAC/B,MAAM,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE;CAC1C,QAAQ,QAAQ,MAAM,CAAC,KAAK,EAAC;CAC7B,OAAO;CACP,KAAK,GAAG;CACR,GAAG,MAAM,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,UAAU,EAAE;CACzE,IAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI;CACrC,MAAM,WAAW,MAAM,KAAK,IAAI,cAAc,EAAE;CAChD,QAAQ,QAAQ,MAAM,CAAC,KAAK,EAAC;CAC7B,OAAO;CACP,KAAK,GAAG;CACR,GAAG;CACH,CAAC;AACW,OAAC,UAAU,GAAG,WAAU;AACxB,OAAC,UAAU,GAAG;;ACnFd,OAAC,UAAU,GAAG,MAAK;AACnB,OAAC,aAAa,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}