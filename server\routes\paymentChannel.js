const express = require('express');
const router = express.Router();
const paymentChannelController = require('../controllers/paymentChannelController');
const { verifyAdminToken } = require('../middlewares/authMiddleware');

// 所有路由都需要管理员认证
router.use(verifyAdminToken);

// 获取支付通道列表
router.get('/', paymentChannelController.getPaymentChannels);

// 获取支付通道详情
router.get('/:id', paymentChannelController.getPaymentChannel);

// 创建支付通道
router.post('/', paymentChannelController.createPaymentChannel);

// 更新支付通道
router.put('/:id', paymentChannelController.updatePaymentChannel);

// 删除支付通道
router.delete('/:id', paymentChannelController.deletePaymentChannel);

// 更新支付通道状态
router.patch('/:id/status', paymentChannelController.updatePaymentChannelStatus);
router.put('/:id/status', paymentChannelController.updatePaymentChannelStatus);

// 批量更新支付通道权重
router.post('/batch-weights', paymentChannelController.updatePaymentChannelWeights);

module.exports = router;
