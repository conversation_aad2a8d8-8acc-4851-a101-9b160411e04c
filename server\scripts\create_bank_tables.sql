-- 创建银行表
CREATE TABLE IF NOT EXISTS banks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '银行名称',
    status BOOLEAN DEFAULT TRUE COMMENT '状态：true=启用, false=禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (name)
);

-- 创建银行编码映射表
CREATE TABLE IF NOT EXISTS bank_channel_mappings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bank_id INT NOT NULL COMMENT '银行ID',
    payment_channel_id INT NOT NULL COMMENT '支付通道ID',
    bank_code VARCHAR(100) NOT NULL COMMENT '银行编码',
    status BOOLEAN DEFAULT TRUE COMMENT '状态：true=启用, false=禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (bank_id, payment_channel_id),
    FOREIGN KEY (bank_id) REFERENCES banks(id) ON DELETE CASCADE
);

-- 初始化银行数据
INSERT INTO banks (name, status) VALUES
('BBVA BANCOMER', TRUE),
('BANJERCITO', TRUE),
('BANOBRAS', TRUE),
('HSBC', TRUE),
('SANTANDER', TRUE),
('其他银行', TRUE);

-- 修改bank_cards表，添加bank_id字段，移除bank_code字段
ALTER TABLE bank_cards ADD COLUMN bank_id INT COMMENT '银行ID' AFTER user_id;
ALTER TABLE bank_cards ADD CONSTRAINT fk_bank_cards_bank_id FOREIGN KEY (bank_id) REFERENCES banks(id);
