{"version": 3, "file": "description2.mjs", "sources": ["../../../../../../packages/components/descriptions/src/description.vue"], "sourcesContent": ["<template>\n  <div :class=\"descriptionKls\">\n    <div\n      v-if=\"title || extra || $slots.title || $slots.extra\"\n      :class=\"ns.e('header')\"\n    >\n      <div :class=\"ns.e('title')\">\n        <slot name=\"title\">{{ title }}</slot>\n      </div>\n      <div :class=\"ns.e('extra')\">\n        <slot name=\"extra\">{{ extra }}</slot>\n      </div>\n    </div>\n\n    <div :class=\"ns.e('body')\">\n      <table :class=\"[ns.e('table'), ns.is('bordered', border)]\">\n        <tbody>\n          <template v-for=\"(row, _index) in getRows()\" :key=\"_index\">\n            <el-descriptions-row :row=\"row\" />\n          </template>\n        </tbody>\n      </table>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, useSlots } from 'vue'\nimport { flattedChildren } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from '@element-plus/components/form'\nimport ElDescriptionsRow from './descriptions-row.vue'\nimport { descriptionsKey } from './token'\nimport { descriptionProps } from './description'\nimport { COMPONENT_NAME } from './constants'\n\nimport type { IDescriptionsInject } from './descriptions.type'\nimport type { DescriptionItemVNode } from './description-item'\n\ndefineOptions({\n  name: 'ElDescriptions',\n})\n\nconst props = defineProps(descriptionProps)\n\nconst ns = useNamespace('descriptions')\n\nconst descriptionsSize = useFormSize()\n\nconst slots = useSlots()\n\nprovide(descriptionsKey, props as IDescriptionsInject)\n\nconst descriptionKls = computed(() => [ns.b(), ns.m(descriptionsSize.value)])\n\nconst filledNode = (\n  node: DescriptionItemVNode,\n  span: number,\n  count: number,\n  isLast = false\n) => {\n  if (!node.props) {\n    node.props = {}\n  }\n  if (span > count) {\n    node.props.span = count\n  }\n  if (isLast) {\n    // set the last span\n    node.props.span = span\n  }\n  return node\n}\n\nconst getRows = () => {\n  if (!slots.default) return []\n\n  const children = flattedChildren(slots.default()).filter(\n    (node): node is DescriptionItemVNode =>\n      (node as any)?.type?.name === COMPONENT_NAME\n  )\n  const rows: DescriptionItemVNode[][] = []\n  let temp: DescriptionItemVNode[] = []\n  let count = props.column\n  let totalSpan = 0 // all spans number of item\n  const rowspanTemp: number[] = [] // the number of row spans\n\n  children.forEach((node, index) => {\n    const span = node.props?.span || 1\n    const rowspan = node.props?.rowspan || 1\n    const rowNo = rows.length\n    rowspanTemp[rowNo] ||= 0\n\n    if (rowspan > 1) {\n      for (let i = 1; i < rowspan; i++) {\n        rowspanTemp[rowNo + i] ||= 0\n        rowspanTemp[rowNo + i]++\n        totalSpan++\n      }\n    }\n    if (rowspanTemp[rowNo] > 0) {\n      count -= rowspanTemp[rowNo]\n      rowspanTemp[rowNo] = 0\n    }\n    if (index < children.length - 1) {\n      totalSpan += span > count ? count : span\n    }\n\n    if (index === children.length - 1) {\n      // calculate the last item span\n      const lastSpan = props.column - (totalSpan % props.column)\n      temp.push(filledNode(node, lastSpan, count, true))\n      rows.push(temp)\n      return\n    }\n\n    if (span < count) {\n      count -= span\n      temp.push(node)\n    } else {\n      temp.push(filledNode(node, span, count))\n      rows.push(temp)\n      count = props.column\n      temp = []\n    }\n  })\n\n  return rows\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;mCAuCc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAK,aAAa,cAAc,CAAA,CAAA;AAEtC,IAAA,MAAM,mBAAmB,WAAY,EAAA,CAAA;AAErC,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAA,OAAA,CAAQ,iBAAiB,KAA4B,CAAA,CAAA;AAErD,IAAA,MAAM,cAAiB,GAAA,QAAA,CAAS,MAAM,CAAC,EAAG,CAAA,CAAA,EAAK,EAAA,EAAA,CAAG,CAAE,CAAA,gBAAA,CAAiB,KAAK,CAAC,CAAC,CAAA,CAAA;AAE5E,IAAA,MAAM,aAAa,CACjB,IAAA,EACA,IACA,EAAA,KAAA,EACA,SAAS,KACN,KAAA;AACH,MAAI,IAAA,CAAC,KAAK,KAAO,EAAA;AACf,QAAA,IAAA,CAAK,QAAQ,EAAC,CAAA;AAAA,OAChB;AACA,MAAA,IAAI,OAAO,KAAO,EAAA;AAChB,QAAA,IAAA,CAAK,MAAM,IAAO,GAAA,KAAA,CAAA;AAAA,OACpB;AACA,MAAA,IAAI,MAAQ,EAAA;AAEV,QAAA,IAAA,CAAK,MAAM,IAAO,GAAA,IAAA,CAAA;AAAA,OACpB;AACA,MAAO,OAAA,IAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAA,MAAM,UAAU,MAAM;AACpB,MAAA,IAAI,CAAC,KAAA,CAAM,OAAS;AAEpB,QAAA,OAAiB,EAAA,CAAA;AAAiC,MAAA,MAC/C,QACgB,GAAA,eAAe,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA;AAAA,QAClC,IAAA,EAAA,CAAA;AACA,QAAA,WAAuC,GAAC,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,MAAA,cAAA,CAAA;AACxC,OAAA,CAAA,CAAA;AACA,MAAA,aAAkB,EAAA,CAAA;AAClB,MAAA,IAAI,IAAY,GAAA,EAAA,CAAA;AAChB,MAAA,IAAA,oBAA+B,CAAA;AAE/B,MAAS,IAAA,SAAA,GAAA,CAAQ,CAAC;AAChB,MAAM,MAAA,WAAY,GAAA,EAAA,CAAA;AAClB,MAAM,QAAA,CAAA,OAAA,CAAA,CAAU,IAAK,EAAA,KAAA,KAAkB;AACvC,QAAA,IAAA,EAAM,QAAQ,CAAK;AACnB,QAAA,MAAA,IAAA,OAAiB,GAAM,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,CAAA,CAAA;AAEvB,QAAA,gBAAiB,CAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,KAAA,CAAA,CAAA;AACf,QAAA,MAAA,KAAS,GAAI,IAAG,CAAI,MAAA,CAAA;AAClB,QAAY,WAAA,CAAA,KAAA,CAAA,KAAA,WAAe,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAC3B,QAAA,IAAA,OAAA,GAAA,CAAA;AACA,UAAA,KAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,EAAA,CAAA,EAAA,EAAA;AAAA,YACF,WAAA,CAAA,EAAA,GAAA,KAAA,GAAA,CAAA,CAAA,KAAA,WAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,YACF,WAAA,CAAA,KAAA,GAAA,CAAA,CAAA,EAAA,CAAA;AACA,YAAI,SAAA,EAAA,CAAY;AACd,WAAA;AACA,SAAA;AAAqB,QACvB,IAAA,WAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAI,KAAA,IAAA,WAAiB,CAAA,KAAA,CAAA,CAAS;AAC5B,UAAa,WAAA,CAAA,KAAA,CAAA;AAAuB,SACtC;AAEA,QAAI,IAAA,KAAA,GAAA,QAAmB,CAAA,MAAA,GAAA,CAAA,EAAY;AAEjC,UAAA,SAAiB,IAAA,IAAA,GAAA,KAAgB,GAAA,KAAA,GAAA,IAAA,CAAA;AACjC,SAAA;AACA,QAAA,IAAA,UAAc,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACd,UAAA,MAAA,QAAA,GAAA,KAAA,CAAA,MAAA,GAAA,SAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAAA,UACF,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAEA,UAAA,SAAW,CAAO,IAAA,CAAA,CAAA;AAChB,UAAS,OAAA;AACT,SAAA;AAAc,QAChB,IAAO,IAAA,GAAA,KAAA,EAAA;AACL,UAAA,KAAK,IAAK,IAAA,CAAA;AACV,UAAA,IAAA,CAAK,KAAK,IAAI,CAAA,CAAA;AACd,SAAA,MAAA;AACA,UAAA,IAAA,CAAA,IAAQ,CAAA,UAAA,CAAA,IAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,UACV,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAAA,UACD,KAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAED,UAAO,IAAA,GAAA,EAAA,CAAA;AAAA,SACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}