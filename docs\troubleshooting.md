# FOX项目问题总结与解决方案

本文档记录了FOX项目开发过程中遇到的问题和解决方案，以便团队成员参考和学习。

## 前端问题

### 1. API路径冲突问题

**问题描述**：
浏览器将 `/api/user` 请求解析为 `/api/user.js` 文件，导致API请求失败，返回404错误。

**原因分析**：
这个问题的根本原因在于 JavaScript 模块系统和构建工具（如 Vite）的工作机制：

1. ES 模块规范中，导入语句如 `import { login } from '@/api/user'` 实际上是在请求一个模块
2. 浏览器原生的 ES 模块系统需要完整的文件路径，包括后缀名
3. 当写 `import x from './y'` 时，浏览器实际上会尝试加载 `./y.js`
4. Vite 在开发模式下使用原生 ES 模块，将导入转换为带有 `.js` 后缀的请求
5. 当使用 `@/api/user` 这样的路径时，Vite 会将其解析为 `/api/user.js`
6. 这个路径与API端点 `/api/user/login` 的前缀相同，导致冲突

**解决方案**：
使用 Vite 的 `resolve.alias` 配置解决路径冲突：

1. 创建新的 API 服务模块目录结构：`services/api/`
2. 在 `vite.config.js` 中配置别名：
   ```javascript
   resolve: {
     alias: {
       '@api': resolve(__dirname, './services/api')
     }
   }
   ```
3. 修改页面中的导入语句，使用新的 API 服务模块：
   ```javascript
   import { user } from '@api';
   const { login } = user;
   ```

**预防措施**：
1. 在新项目中，使用不同的 API 基础路径，如 `/api-server`
2. 或者使用专门的 API 客户端库，如 Axios
3. 或者使用直接的 fetch API 发送请求，避免模块导入问题

### 2. 登录测试页面与正常登录页面的区别

**问题描述**：
登录测试页面能够正常工作，而正常登录页面出现API路径冲突问题。

**原因分析**：
1. 登录测试页面使用直接的 fetch API 发送请求，不涉及模块导入
2. 正常登录页面使用模块导入的方式，导致路径冲突

**解决方案**：
1. 使用 Vite 的 `resolve.alias` 配置解决路径冲突
2. 或者在所有页面中使用直接的 fetch API 发送请求

## 后端问题

### 1. 邀请码验证失败问题

**问题描述**：
用户在注册时验证邀请码失败，返回404错误，提示"邀请码无效"。

**原因分析**：
1. 用户表中有 invite_code 字段，值为 "USER1CODE1"
2. 但是在 invite_codes 表中没有对应的记录
3. 验证邀请码的函数 `validateInviteCode` 是在 invite_codes 表中查找邀请码，而不是在 users 表中

**解决方案**：
1. 在 invite_codes 表中为 user1 用户创建了一个邀请码记录，代码为 "USER1CODE"
2. 更新了 users 表中 user1 用户的 invite_code 字段，使其与 invite_codes 表中的记录一致

**预防措施**：
1. 确保用户表和邀请码表中的数据始终保持一致
2. 可能需要使用事务或触发器来确保这一点
3. 检查注册流程，确保每次注册都正确创建邀请码记录

### 2. 密码验证失败问题

**问题描述**：
用户登录时密码验证失败，即使输入了正确的密码。

**原因分析**：
1. 数据库中存储的密码哈希与用户输入的密码不匹配
2. 可能是密码哈希算法不一致或者密码哈希生成时的参数不同

**解决方案**：
1. 使用 bcrypt 生成新的密码哈希，并更新数据库
2. 添加密码验证测试脚本，确保密码验证正常

**预防措施**：
1. 使用一致的密码哈希算法和参数
2. 在用户注册和密码修改时使用相同的哈希生成方法
3. 添加单元测试，确保密码验证功能正常工作

## 数据库问题

### 1. 数据不一致问题

**问题描述**：
用户表和邀请码表中的数据不一致，导致功能异常。

**原因分析**：
1. 用户表中有 invite_code 字段，但在 invite_codes 表中没有对应的记录
2. 可能是在用户创建时没有正确创建邀请码记录，或者数据迁移过程中出现问题

**解决方案**：
1. 检查并修复数据不一致的问题
2. 确保两个表中的数据保持同步

**预防措施**：
1. 使用事务确保数据一致性
2. 添加数据完整性检查脚本，定期检查数据一致性
3. 在关键操作（如用户注册）中添加日志，便于问题排查
