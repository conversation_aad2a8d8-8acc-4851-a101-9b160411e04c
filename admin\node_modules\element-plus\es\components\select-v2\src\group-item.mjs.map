{"version": 3, "file": "group-item.mjs", "sources": ["../../../../../../packages/components/select-v2/src/group-item.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"ns.be('group', 'title')\"\n    :style=\"{ ...style, lineHeight: `${height}px` }\"\n  >\n    {{ item.label }}\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport type { CSSProperties, PropType } from 'vue'\n\nexport default defineComponent({\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    style: {\n      type: Object as PropType<CSSProperties>,\n    },\n    height: Number,\n  },\n  setup() {\n    const ns = useNamespace('select')\n    return {\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_normalizeStyle", "_toDisplayString"], "mappings": ";;;;AAcA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,KACR;AAAA,IACA,MAAQ,EAAA,MAAA;AAAA,GACV;AAAA,EACA,KAAQ,GAAA;AACN,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;AA9BC,EAAA,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,IAKM,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;AAAA,IAAA,KAAA,EAAAC,cAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AAAA,GAJH,EAAAC,eAAO,CAAA,IAAA,CAAA,IAAA,CAAA,KAAG,CAAE,EAAA,CAAA,CAAA,CAAA;AAAA,CAAA;AAGV,gBAAA,gBAAK,WAAK,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,gBAAA,CAAA,CAAA,CAAA;;;;"}