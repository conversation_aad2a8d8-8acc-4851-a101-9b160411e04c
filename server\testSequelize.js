/**
 * 测试 sequelize 对象
 */
const sequelize = require('./config/database');

async function testSequelize() {
  try {
    console.log('测试 sequelize 对象...');
    
    // 检查 sequelize 对象
    console.log('sequelize 对象类型:', typeof sequelize);
    console.log('sequelize 对象:', sequelize);
    
    // 测试事务创建
    console.log('测试事务创建...');
    const transaction = await sequelize.transaction();
    console.log('事务创建成功:', transaction);
    
    // 回滚事务
    await transaction.rollback();
    console.log('事务回滚成功');
    
    console.log('测试成功');
    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 执行测试
testSequelize();
