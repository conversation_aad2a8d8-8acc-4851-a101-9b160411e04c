{"version": 3, "file": "fr.js", "sources": ["../../../../../packages/locale/lang/fr.ts"], "sourcesContent": ["export default {\n  name: 'fr',\n  el: {\n    breadcrumb: {\n      label: `<PERSON><PERSON> d'Ariane`,\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Effacer',\n      defaultLabel: 'color picker',\n      description:\n        'La couleur actuelle est {color}. Appuyer sur Entrée pour sélectionner une nouvelle couleur.',\n    },\n    datepicker: {\n      now: 'Maintenant',\n      today: 'Auj.',\n      cancel: 'Annule<PERSON>',\n      clear: 'Effacer',\n      confirm: 'OK',\n      dateTablePrompt:\n        'Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le jour du mois',\n      monthTablePrompt:\n        'Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner le mois',\n      yearTablePrompt:\n        \"Utiliser les touches fléchées et appuyer sur Entrée pour sélectionner l'année\",\n      selectedDate: 'Date sélectionnée',\n      selectDate: 'Choisir date',\n      selectTime: 'Choisir horaire',\n      startDate: 'Date début',\n      startTime: 'Horaire début',\n      endDate: 'Date fin',\n      endTime: '<PERSON>raire fin',\n      prevYear: '<PERSON><PERSON> précédente',\n      nextYear: '<PERSON><PERSON> suivante',\n      prevMonth: 'Mo<PERSON> précédent',\n      nextMonth: 'Mois suivant',\n      year: '', // In french, like in english, we don't say \"Année\" after the year number.\n      month1: 'Janvier',\n      month2: 'Février',\n      month3: 'Mars',\n      month4: 'Avril',\n      month5: 'Mai',\n      month6: 'Juin',\n      month7: 'Juillet',\n      month8: 'Août',\n      month9: 'Septembre',\n      month10: 'Octobre',\n      month11: 'Novembre',\n      month12: 'Décembre',\n      week: 'Semaine',\n      weeks: {\n        sun: 'Dim',\n        mon: 'Lun',\n        tue: 'Mar',\n        wed: 'Mer',\n        thu: 'Jeu',\n        fri: 'Ven',\n        sat: 'Sam',\n      },\n      weeksFull: {\n        sun: 'Dimanche',\n        mon: 'Lundi',\n        tue: 'Mardi',\n        wed: 'Mercredi',\n        thu: 'Jeudi',\n        fri: 'Vendredi',\n        sat: 'Samedi',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Fév',\n        mar: 'Mar',\n        apr: 'Avr',\n        may: 'Mai',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aoû',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Déc',\n      },\n    },\n    inputNumber: {\n      decrease: 'décrémenter',\n      increase: 'incrémenter',\n    },\n    select: {\n      loading: 'Chargement',\n      noMatch: 'Aucune correspondance',\n      noData: 'Aucune donnée',\n      placeholder: 'Choisir',\n    },\n    mention: {\n      loading: 'Chargement',\n    },\n    cascader: {\n      noMatch: 'Aucune correspondance',\n      loading: 'Chargement',\n      placeholder: 'Choisir',\n      noData: 'Aucune donnée',\n    },\n    pagination: {\n      goto: 'Aller à',\n      pagesize: '/page',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page',\n      prev: 'Aller à la page précédente',\n      next: 'Aller à la page suivante',\n      currentPage: 'page {pager}',\n      prevPages: '{pager} pages précédentes',\n      nextPages: '{pager} pages suivantes',\n      deprecationWarning:\n        'Utilisations obsolètes détectées, veuillez vous référer à la documentation el-pagination pour plus de détails',\n    },\n    dialog: {\n      close: 'Fermer la boîte de dialogue',\n    },\n    drawer: {\n      close: 'Fermer la boîte de dialogue',\n    },\n    messagebox: {\n      title: 'Message',\n      confirm: 'Confirmer',\n      cancel: 'Annuler',\n      error: 'Erreur',\n      close: 'Fermer la boîte de dialogue',\n    },\n    upload: {\n      deleteTip: 'Cliquer sur supprimer pour retirer le fichier',\n      delete: 'Supprimer',\n      preview: 'Aperçu',\n      continue: 'Continuer',\n    },\n    slider: {\n      defaultLabel: 'curseur entre {min} et {max}',\n      defaultRangeStartLabel: 'choisir la valeur de départ',\n      defaultRangeEndLabel: 'sélectionner la valeur finale',\n    },\n    table: {\n      emptyText: 'Aucune donnée',\n      confirmFilter: 'Confirmer',\n      resetFilter: 'Réinitialiser',\n      clearFilter: 'Tous',\n      sumText: 'Somme',\n    },\n    tour: {\n      next: 'suivant',\n      previous: 'précédent',\n      finish: 'fin',\n    },\n    tree: {\n      emptyText: 'Aucune donnée',\n    },\n    transfer: {\n      noMatch: 'Aucune correspondance',\n      noData: 'Aucune donnée',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Entrer un mot clef',\n      noCheckedFormat: '{total} elements',\n      hasCheckedFormat: '{checked}/{total} coché(s)',\n    },\n    image: {\n      error: 'ECHEC',\n    },\n    pageHeader: {\n      title: 'Retour',\n    },\n    popconfirm: {\n      confirmButtonText: 'Oui',\n      cancelButtonText: 'Non',\n    },\n    carousel: {\n      leftArrow: 'Flèche du carrousel vers la gauche',\n      rightArrow: 'Flèche du carrousel vers la droite',\n      indicator: 'Passer au carrousel index {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,CAAC,YAAY,CAAC;AAC3B,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,YAAY,EAAE,cAAc;AAClC,MAAM,WAAW,EAAE,mGAAmG;AACtH,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,YAAY;AACvB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,eAAe,EAAE,mGAAmG;AAC1H,MAAM,gBAAgB,EAAE,2FAA2F;AACnH,MAAM,eAAe,EAAE,8FAA8F;AACrH,MAAM,YAAY,EAAE,yBAAyB;AAC7C,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,SAAS,EAAE,sBAAsB;AACvC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,QAAQ,EAAE,gBAAgB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,WAAW,EAAE,SAAS;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,YAAY;AAC3B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,kBAAkB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qCAAqC;AACjD,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,iCAAiC;AAClD,MAAM,SAAS,EAAE,yBAAyB;AAC1C,MAAM,kBAAkB,EAAE,oIAAoI;AAC9J,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,+CAA+C;AAChE,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,WAAW;AAC3B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,8BAA8B;AAClD,MAAM,sBAAsB,EAAE,gCAAgC;AAC9D,MAAM,oBAAoB,EAAE,kCAAkC;AAC9D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,aAAa,EAAE,WAAW;AAChC,MAAM,WAAW,EAAE,kBAAkB;AACrC,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,MAAM,EAAE,KAAK;AACnB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,kBAAkB;AACnC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,gBAAgB,EAAE,+BAA+B;AACvD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,OAAO;AACpB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,KAAK;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,uCAAuC;AACxD,MAAM,UAAU,EAAE,uCAAuC;AACzD,MAAM,SAAS,EAAE,mCAAmC;AACpD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}