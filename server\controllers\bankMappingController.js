const { Bank, BankChannelMapping, PaymentChannel } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 获取所有银行
exports.getAllBanks = async (req, res) => {
  try {
    const banks = await Bank.findAll({
      order: [['name', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: banks
    });
  } catch (error) {
    console.error('获取银行列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取银行编码映射
exports.getBankMappings = async (req, res) => {
  try {
    const { bank_id, payment_channel_id } = req.query;

    // 构建查询条件
    const where = {};
    if (bank_id) where.bank_id = bank_id;
    if (payment_channel_id) where.payment_channel_id = payment_channel_id;

    const mappings = await BankChannelMapping.findAll({
      where,
      include: [
        {
          model: Bank,
          as: 'bank',
          attributes: ['id', 'name']
        },
        {
          model: PaymentChannel,
          as: 'payment_channel',
          attributes: ['id', 'name']
        }
      ],
      order: [['bank_id', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: mappings
    });
  } catch (error) {
    console.error('获取银行编码映射错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 添加/更新银行编码映射
exports.upsertBankMapping = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id, bank_id, payment_channel_id, payin_method, payout_method, status } = req.body;

    // 验证请求数据
    if (!bank_id || !payment_channel_id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '银行ID和支付通道ID不能为空',
        data: null
      });
    }

    let mapping;

    if (id) {
      // 更新现有映射
      mapping = await BankChannelMapping.findByPk(id);

      if (!mapping) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '银行编码映射不存在',
          data: null
        });
      }

      await mapping.update({
        payin_method,
        payout_method,
        status: status !== undefined ? status : mapping.status
      }, { transaction });
    } else {
      // 查找是否已存在映射
      mapping = await BankChannelMapping.findOne({
        where: {
          bank_id,
          payment_channel_id
        }
      });

      if (mapping) {
        // 更新现有映射
        await mapping.update({
          payin_method,
          payout_method,
          status: status !== undefined ? status : mapping.status
        }, { transaction });
      } else {
        // 创建新映射
        mapping = await BankChannelMapping.create({
          bank_id,
          payment_channel_id,
          payin_method,
          payout_method,
          status: status !== undefined ? status : true
        }, { transaction });
      }
    }

    await transaction.commit();

    // 查询更新后的映射，包含银行信息
    const updatedMapping = await BankChannelMapping.findByPk(mapping.id, {
      include: [
        {
          model: Bank,
          as: 'bank',
          attributes: ['id', 'name']
        },
        {
          model: PaymentChannel,
          as: 'payment_channel',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '保存成功',
      data: updatedMapping
    });
  } catch (error) {
    await transaction.rollback();
    console.error('保存银行编码映射错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除银行编码映射
exports.deleteBankMapping = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    const mapping = await BankChannelMapping.findByPk(id);

    if (!mapping) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '银行编码映射不存在',
        data: null
      });
    }

    await mapping.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('删除银行编码映射错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
