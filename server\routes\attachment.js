const express = require('express');
const router = express.Router();
const attachmentController = require('../controllers/attachmentController');
const { verifyAdminToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 所有路由都需要验证token和管理员权限
router.use(verifyAdminToken);

// 获取附件列表
router.get('/', attachmentController.getAttachments);

// 获取附件详情
router.get('/:id', attachmentController.getAttachmentById);

// 上传附件
router.post('/upload', attachmentController.uploadAttachment);

// 更新附件信息
router.put('/:id', attachmentController.updateAttachment);

// 删除附件
router.delete('/:id', attachmentController.deleteAttachment);

// 批量删除附件
router.post('/batch-delete', attachmentController.batchDeleteAttachments);

// 扫描服务器附件
router.post('/scan', attachmentController.scanServerAttachments);

module.exports = router;
