const sequelize = require('../config/database');
const Admin = require('../models/Admin');
const argon2 = require('argon2');
const bcrypt = require('bcryptjs');

async function testAdminLogin() {
  try {
    console.log('测试admin登录...');

    // 查找admin用户
    const admin = await Admin.findOne({ where: { username: 'admin' } });

    if (!admin) {
      console.log('未找到admin用户');
      return;
    }

    console.log('找到admin用户');
    console.log('用户名:', admin.username);
    console.log('密码格式:', admin.password.substring(0, 20) + '...');
    console.log('完整密码:', admin.password);

    // 测试密码验证
    const testPassword = 'admin123';

    // 手动测试Argon2验证
    console.log('\n手动测试Argon2验证:');
    try {
      const argon2Result = await argon2.verify(admin.password, testPassword);
      console.log('Argon2验证结果:', argon2Result);
    } catch (error) {
      console.log('Argon2验证错误:', error.message);
    }

    // 测试模型方法
    console.log('\n测试模型validatePassword方法:');
    const isValid = await admin.validatePassword(testPassword);
    console.log(`测试密码 "${testPassword}" 验证结果:`, isValid ? '成功' : '失败');

    if (isValid) {
      console.log('✅ 密码验证成功！现在可以正常登录了');
    } else {
      console.log('❌ 密码验证失败，需要进一步检查');
    }

  } catch (error) {
    console.error('测试登录时出错:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
testAdminLogin();
