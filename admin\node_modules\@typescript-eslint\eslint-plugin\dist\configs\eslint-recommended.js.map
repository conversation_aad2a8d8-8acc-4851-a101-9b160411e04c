{"version": 3, "file": "eslint-recommended.js", "sourceRoot": "", "sources": ["../../src/configs/eslint-recommended.ts"], "names": [], "mappings": ";AAOA,iBAAS;IACP,SAAS,EAAE;QACT;YACE,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;YAC1C,KAAK,EAAE;gBACL,mBAAmB,EAAE,KAAK,EAAE,sBAAsB;gBAClD,eAAe,EAAE,KAAK,EAAE,WAAW;gBACnC,iBAAiB,EAAE,KAAK,EAAE,WAAW;gBACrC,cAAc,EAAE,KAAK,EAAE,WAAW;gBAClC,uBAAuB,EAAE,KAAK,EAAE,sBAAsB;gBACtD,cAAc,EAAE,KAAK,EAAE,WAAW;gBAClC,gBAAgB,EAAE,KAAK,EAAE,WAAW;gBACpC,kBAAkB,EAAE,KAAK,EAAE,sBAAsB;gBACjD,eAAe,EAAE,KAAK,EAAE,WAAW;gBACnC,cAAc,EAAE,KAAK,EAAE,WAAW;gBAClC,cAAc,EAAE,KAAK,EAAE,WAAW;gBAClC,kBAAkB,EAAE,KAAK,EAAE,WAAW;gBACtC,sBAAsB,EAAE,KAAK,EAAE,uBAAuB;gBACtD,UAAU,EAAE,KAAK,EAAE,sBAAsB;gBACzC,gBAAgB,EAAE,KAAK,EAAE,WAAW;gBACpC,oBAAoB,EAAE,KAAK,EAAE,iCAAiC;gBAC9D,QAAQ,EAAE,OAAO,EAAE,+DAA+D;gBAClF,cAAc,EAAE,OAAO,EAAE,sCAAsC;gBAC/D,oBAAoB,EAAE,OAAO,EAAE,yDAAyD;gBACxF,eAAe,EAAE,OAAO,EAAE,6DAA6D;aACxF;SACF;KACF;CAC6B,CAAC"}