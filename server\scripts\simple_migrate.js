/**
 * 简单的数据迁移脚本
 * 使用SQL直接更新理论收益时间
 */
const mysql = require('mysql2/promise');
require('dotenv').config();

async function simpleMigrate() {
  console.log('开始执行简单迁移脚本...');
  
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'MySQL3352~!',
    database: process.env.DB_NAME || 'fox_db'
  });
  
  try {
    // 查询所有收益记录
    console.log('查询收益记录...');
    const [profits] = await connection.query('SELECT * FROM investment_profits');
    console.log(`找到 ${profits.length} 条收益记录`);
    
    if (profits.length === 0) {
      console.log('没有找到收益记录，无需迁移');
      return { success: 0, failed: 0 };
    }
    
    // 使用SQL更新理论收益时间（简单地将理论收益时间设置为实际收益时间）
    console.log('更新理论收益时间...');
    const [result] = await connection.query(
      'UPDATE investment_profits SET theoretical_profit_time = profit_time WHERE theoretical_profit_time IS NULL'
    );
    
    console.log(`更新了 ${result.affectedRows} 条记录`);
    return { success: result.affectedRows, failed: 0 };
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

// 执行迁移
simpleMigrate()
  .then(result => {
    console.log('迁移结果:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('迁移过程出错:', error);
    process.exit(1);
  });
