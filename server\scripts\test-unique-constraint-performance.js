/**
 * 测试唯一约束对数据库性能的影响
 */
const sequelize = require('../config/database');
const { InvestmentProfit } = require('../models');

async function testInsertPerformance() {
  try {
    console.log('开始测试唯一约束性能影响...');
    
    // 测试数据准备
    const testData = [];
    for (let i = 1; i <= 1000; i++) {
      testData.push({
        investment_id: Math.floor(Math.random() * 1000) + 1,
        user_id: Math.floor(Math.random() * 1000) + 1,
        amount: (Math.random() * 1000).toFixed(2),
        profit_time: new Date(),
        status: 'paid'
      });
    }
    
    // 测试1：批量插入性能
    console.log('\n=== 测试批量插入性能 ===');
    const startTime1 = Date.now();
    
    try {
      await InvestmentProfit.bulkCreate(testData, {
        ignoreDuplicates: true // 忽略重复记录
      });
      const endTime1 = Date.now();
      console.log(`批量插入1000条记录耗时: ${endTime1 - startTime1}ms`);
      console.log(`平均每条记录: ${(endTime1 - startTime1) / 1000}ms`);
    } catch (error) {
      console.error('批量插入失败:', error.message);
    }
    
    // 测试2：单条插入性能
    console.log('\n=== 测试单条插入性能 ===');
    const singleInsertTimes = [];
    
    for (let i = 0; i < 100; i++) {
      const startTime = Date.now();
      try {
        await InvestmentProfit.create({
          investment_id: Math.floor(Math.random() * 10000) + 1,
          user_id: Math.floor(Math.random() * 10000) + 1,
          amount: (Math.random() * 1000).toFixed(2),
          profit_time: new Date(),
          status: 'paid'
        });
        const endTime = Date.now();
        singleInsertTimes.push(endTime - startTime);
      } catch (error) {
        // 忽略重复错误
        if (!error.message.includes('Duplicate entry')) {
          console.error('插入失败:', error.message);
        }
      }
    }
    
    const avgTime = singleInsertTimes.reduce((a, b) => a + b, 0) / singleInsertTimes.length;
    const maxTime = Math.max(...singleInsertTimes);
    const minTime = Math.min(...singleInsertTimes);
    
    console.log(`单条插入平均耗时: ${avgTime.toFixed(2)}ms`);
    console.log(`最大耗时: ${maxTime}ms`);
    console.log(`最小耗时: ${minTime}ms`);
    
    // 测试3：重复插入处理
    console.log('\n=== 测试重复插入处理 ===');
    const duplicateStartTime = Date.now();
    let duplicateCount = 0;
    
    for (let i = 0; i < 100; i++) {
      try {
        await InvestmentProfit.create({
          investment_id: 1, // 固定投资ID
          user_id: 1,
          amount: '100.00',
          profit_time: new Date('2025-01-01'), // 固定日期
          status: 'paid'
        });
      } catch (error) {
        if (error.message.includes('Duplicate entry')) {
          duplicateCount++;
        }
      }
    }
    
    const duplicateEndTime = Date.now();
    console.log(`重复插入测试完成，检测到 ${duplicateCount} 次重复`);
    console.log(`重复检测平均耗时: ${(duplicateEndTime - duplicateStartTime) / 100}ms`);
    
    // 测试4：查询性能
    console.log('\n=== 测试查询性能 ===');
    const queryStartTime = Date.now();
    
    const results = await InvestmentProfit.findAll({
      where: {
        investment_id: 1
      },
      limit: 100
    });
    
    const queryEndTime = Date.now();
    console.log(`查询100条记录耗时: ${queryEndTime - queryStartTime}ms`);
    console.log(`查询结果数量: ${results.length}`);
    
  } catch (error) {
    console.error('性能测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
testInsertPerformance();
