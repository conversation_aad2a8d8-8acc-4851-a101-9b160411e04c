'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('investment_profits', 'distribution_time', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: '发放时间（系统实际执行发放操作的时间）',
      after: 'profit_time'
    });

    // 更新现有记录，将created_at作为distribution_time
    await queryInterface.sequelize.query(`
      UPDATE investment_profits 
      SET distribution_time = created_at 
      WHERE distribution_time IS NULL
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('investment_profits', 'distribution_time');
  }
};
