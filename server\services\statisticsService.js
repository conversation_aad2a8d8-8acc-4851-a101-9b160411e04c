/**
 * 统计服务
 * 用于计算和更新统计数据
 */
const {
  User,
  Transaction,
  Deposit,
  Withdrawal,
  Investment,
  InvestmentProfit,
  Commission,
  DailyStatistic,
  TotalStatistic
} = require('../models');
const sequelize = require('../config/database');
const { Op } = require('sequelize');
const moment = require('moment');
const dateUtils = require('../utils/dateUtils');
const logger = require('../utils/logger');

/**
 * 计算指定日期的统计数据
 * @param {Date} date 日期
 * @returns {Promise<Object>} 统计数据
 */
async function calculateDailyStatistics(date) {
  try {
    // 格式化日期，确保只有日期部分
    const targetDate = moment(date).format('YYYY-MM-DD');
    const startDate = moment(targetDate).startOf('day').toDate();
    const endDate = moment(targetDate).endOf('day').toDate();

    logger.info(`计算 ${targetDate} 的统计数据`);

    // 新增会员数
    const newUserCount = await User.count({
      where: {
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    // 充值数据 - 基于充值订单状态，只有completed状态才计入统计
    const depositAmount = await Deposit.sum('amount', {
      where: {
        status: 'completed',
        completion_time: {
          [Op.between]: [startDate, endDate]
        }
      }
    }) || 0;

    const depositCount = await Deposit.count({
      where: {
        status: 'completed',
        completion_time: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    const depositUserCount = await Deposit.count({
      where: {
        status: 'completed',
        completion_time: {
          [Op.between]: [startDate, endDate]
        }
      },
      distinct: true,
      col: 'user_id'
    });

    // 取款数据 - 基于取款订单状态，只有completed状态才计入统计
    const withdrawalAmount = await Withdrawal.sum('amount', {
      where: {
        status: 'completed',
        completion_time: {
          [Op.between]: [startDate, endDate]
        }
      }
    }) || 0;

    const withdrawalCount = await Withdrawal.count({
      where: {
        status: 'completed',
        completion_time: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    const withdrawalUserCount = await Withdrawal.count({
      where: {
        status: 'completed',
        completion_time: {
          [Op.between]: [startDate, endDate]
        }
      },
      distinct: true,
      col: 'user_id'
    });

    // 投资数据
    const investmentAmount = await Transaction.sum('amount', {
      where: {
        type: 'investment',
        status: 'success',
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    }) || 0;

    const investmentCount = await Transaction.count({
      where: {
        type: 'investment',
        status: 'success',
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    const investmentUserCount = await Transaction.count({
      where: {
        type: 'investment',
        status: 'success',
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      },
      distinct: true,
      col: 'user_id'
    });

    // 收益数据
    const profitAmount = await Transaction.sum('amount', {
      where: {
        type: 'profit',
        status: 'success',
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    }) || 0;

    const profitCount = await Transaction.count({
      where: {
        type: 'profit',
        status: 'success',
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    // 佣金数据
    const commissionAmount = await Transaction.sum('amount', {
      where: {
        type: 'commission',
        status: 'success',
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    }) || 0;

    const commissionCount = await Transaction.count({
      where: {
        type: 'commission',
        status: 'success',
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    // 注册并充值人数 - 基于充值订单状态
    const registerDepositCount = await User.count({
      where: {
        created_at: {
          [Op.between]: [startDate, endDate]
        },
        id: {
          [Op.in]: sequelize.literal(`(
            SELECT DISTINCT user_id FROM deposits
            WHERE status = 'completed'
            AND completion_time BETWEEN '${moment(startDate).format('YYYY-MM-DD HH:mm:ss')}' AND '${moment(endDate).format('YYYY-MM-DD HH:mm:ss')}'
          )`)
        }
      }
    });

    // 计算平台利润
    const platformProfit = parseFloat(depositAmount) - parseFloat(withdrawalAmount);

    // 返回统计数据
    return {
      date: targetDate,
      new_user_count: newUserCount,
      deposit_amount: depositAmount,
      deposit_count: depositCount,
      deposit_user_count: depositUserCount,
      withdrawal_amount: withdrawalAmount,
      withdrawal_count: withdrawalCount,
      withdrawal_user_count: withdrawalUserCount,
      investment_amount: investmentAmount,
      investment_count: investmentCount,
      investment_user_count: investmentUserCount,
      profit_amount: profitAmount,
      profit_count: profitCount,
      commission_amount: commissionAmount,
      commission_count: commissionCount,
      register_deposit_count: registerDepositCount,
      platform_profit: platformProfit
    };
  } catch (error) {
    logger.error(`计算日统计数据失败: ${error.message}`, error);
    throw error;
  }
}

/**
 * 更新指定日期的统计数据
 * @param {Date} date 日期
 * @returns {Promise<Object>} 更新结果
 */
async function updateDailyStatistics(date) {
  const transaction = await sequelize.transaction();

  try {
    // 计算统计数据
    const stats = await calculateDailyStatistics(date);
    const formattedDate = moment(date).format('YYYY-MM-DD');

    // 查找或创建日统计记录
    let dailyStats = await DailyStatistic.findOne({
      where: { date: formattedDate },
      transaction
    });

    if (dailyStats) {
      // 更新现有记录
      await dailyStats.update(stats, { transaction });
      logger.info(`更新 ${formattedDate} 的统计数据成功`);
    } else {
      // 创建新记录
      dailyStats = await DailyStatistic.create(stats, { transaction });
      logger.info(`创建 ${formattedDate} 的统计数据成功`);
    }

    // 更新累计统计数据
    await updateTotalStatistics(transaction);

    // 提交事务
    await transaction.commit();

    return {
      success: true,
      message: `${formattedDate} 的统计数据已更新`
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    logger.error(`更新日统计数据失败: ${error.message}`, error);

    return {
      success: false,
      message: `更新统计数据失败: ${error.message}`
    };
  }
}

/**
 * 更新累计统计数据
 * @param {Transaction} transaction 事务对象
 * @returns {Promise<Object>} 更新结果
 */
async function updateTotalStatistics(transaction) {
  try {
    // 计算累计数据
    const totalUserCount = await User.count({ transaction });

    // 总充值数据 - 基于充值订单状态
    const totalDepositAmount = await Deposit.sum('amount', {
      where: {
        status: 'completed'
      },
      transaction
    }) || 0;

    const totalDepositCount = await Deposit.count({
      where: {
        status: 'completed'
      },
      transaction
    });

    const totalDepositUserCount = await Deposit.count({
      where: {
        status: 'completed'
      },
      distinct: true,
      col: 'user_id',
      transaction
    });

    // 总取款数据 - 基于取款订单状态
    const totalWithdrawalAmount = await Withdrawal.sum('amount', {
      where: {
        status: 'completed'
      },
      transaction
    }) || 0;

    const totalWithdrawalCount = await Withdrawal.count({
      where: {
        status: 'completed'
      },
      transaction
    });

    const totalWithdrawalUserCount = await Withdrawal.count({
      where: {
        status: 'completed'
      },
      distinct: true,
      col: 'user_id',
      transaction
    });

    const totalInvestmentAmount = await Transaction.sum('amount', {
      where: {
        type: 'investment',
        status: 'success'
      },
      transaction
    }) || 0;

    const totalInvestmentCount = await Transaction.count({
      where: {
        type: 'investment',
        status: 'success'
      },
      transaction
    });

    const totalInvestmentUserCount = await Transaction.count({
      where: {
        type: 'investment',
        status: 'success'
      },
      distinct: true,
      col: 'user_id',
      transaction
    });

    const totalProfitAmount = await Transaction.sum('amount', {
      where: {
        type: 'profit',
        status: 'success'
      },
      transaction
    }) || 0;

    const totalProfitCount = await Transaction.count({
      where: {
        type: 'profit',
        status: 'success'
      },
      transaction
    });

    const totalCommissionAmount = await Transaction.sum('amount', {
      where: {
        type: 'commission',
        status: 'success'
      },
      transaction
    }) || 0;

    const totalCommissionCount = await Transaction.count({
      where: {
        type: 'commission',
        status: 'success'
      },
      transaction
    });

    // 计算总平台利润
    const totalPlatformProfit = parseFloat(totalDepositAmount) - parseFloat(totalWithdrawalAmount);

    // 获取或创建累计统计记录
    let totalStats = await TotalStatistic.findOne({ transaction });

    if (!totalStats) {
      totalStats = await TotalStatistic.create({
        total_user_count: totalUserCount,
        total_deposit_amount: totalDepositAmount,
        total_deposit_count: totalDepositCount,
        total_deposit_user_count: totalDepositUserCount,
        total_withdrawal_amount: totalWithdrawalAmount,
        total_withdrawal_count: totalWithdrawalCount,
        total_withdrawal_user_count: totalWithdrawalUserCount,
        total_investment_amount: totalInvestmentAmount,
        total_investment_count: totalInvestmentCount,
        total_investment_user_count: totalInvestmentUserCount,
        total_profit_amount: totalProfitAmount,
        total_profit_count: totalProfitCount,
        total_commission_amount: totalCommissionAmount,
        total_commission_count: totalCommissionCount,
        total_platform_profit: totalPlatformProfit,
        last_updated_date: moment().format('YYYY-MM-DD')
      }, { transaction });
    } else {
      await totalStats.update({
        total_user_count: totalUserCount,
        total_deposit_amount: totalDepositAmount,
        total_deposit_count: totalDepositCount,
        total_deposit_user_count: totalDepositUserCount,
        total_withdrawal_amount: totalWithdrawalAmount,
        total_withdrawal_count: totalWithdrawalCount,
        total_withdrawal_user_count: totalWithdrawalUserCount,
        total_investment_amount: totalInvestmentAmount,
        total_investment_count: totalInvestmentCount,
        total_investment_user_count: totalInvestmentUserCount,
        total_profit_amount: totalProfitAmount,
        total_profit_count: totalProfitCount,
        total_commission_amount: totalCommissionAmount,
        total_commission_count: totalCommissionCount,
        total_platform_profit: totalPlatformProfit,
        last_updated_date: moment().format('YYYY-MM-DD')
      }, { transaction });
    }

    logger.info('更新累计统计数据成功');

    return {
      success: true,
      message: '累计统计数据已更新'
    };
  } catch (error) {
    logger.error(`更新累计统计数据失败: ${error.message}`, error);
    throw error;
  }
}

/**
 * 初始化历史统计数据
 * @param {Date} startDate 开始日期
 * @param {Date} endDate 结束日期
 * @returns {Promise<Object>} 初始化结果
 */
async function initializeHistoricalStatistics(startDate, endDate) {
  try {
    const start = moment(startDate).startOf('day');
    const end = moment(endDate).endOf('day');

    logger.info(`初始化 ${start.format('YYYY-MM-DD')} 到 ${end.format('YYYY-MM-DD')} 的历史统计数据`);

    // 生成日期范围
    const dates = [];
    let currentDate = start.clone();

    while (currentDate.isSameOrBefore(end, 'day')) {
      dates.push(currentDate.format('YYYY-MM-DD'));
      currentDate.add(1, 'day');
    }

    // 逐日更新统计数据
    for (const date of dates) {
      logger.info(`处理 ${date} 的统计数据`);
      await updateDailyStatistics(date);
    }

    return {
      success: true,
      message: `${dates.length} 天的历史统计数据已初始化`
    };
  } catch (error) {
    logger.error(`初始化历史统计数据失败: ${error.message}`, error);

    return {
      success: false,
      message: `初始化历史统计数据失败: ${error.message}`
    };
  }
}

/**
 * 获取今日统计数据
 * @returns {Promise<Object>} 今日统计数据
 */
async function getTodayStatistics() {
  try {
    const today = moment().format('YYYY-MM-DD');

    // 查找今日统计记录
    let stats = await DailyStatistic.findOne({
      where: { date: today }
    });

    // 如果没有今日记录，计算并创建
    if (!stats) {
      const result = await updateDailyStatistics(today);
      if (result.success) {
        // 重新查询创建的记录
        stats = await DailyStatistic.findOne({
          where: { date: today }
        });
      } else {
        throw new Error(result.message);
      }
    }

    return stats;
  } catch (error) {
    logger.error(`获取今日统计数据失败: ${error.message}`, error);
    throw error;
  }
}

/**
 * 获取昨日统计数据
 * @returns {Promise<Object>} 昨日统计数据
 */
async function getYesterdayStatistics() {
  try {
    const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');

    // 查找昨日统计记录
    let stats = await DailyStatistic.findOne({
      where: { date: yesterday }
    });

    // 如果没有昨日记录，计算并创建
    if (!stats) {
      const result = await updateDailyStatistics(yesterday);
      if (result.success) {
        // 重新查询创建的记录
        stats = await DailyStatistic.findOne({
          where: { date: yesterday }
        });
      } else {
        throw new Error(result.message);
      }
    }

    return stats;
  } catch (error) {
    logger.error(`获取昨日统计数据失败: ${error.message}`, error);
    throw error;
  }
}

/**
 * 获取累计统计数据
 * @returns {Promise<Object>} 累计统计数据
 */
async function getTotalStatistics() {
  try {
    // 查找累计统计记录
    let stats = await TotalStatistic.findOne();

    // 如果没有记录，更新并创建
    if (!stats) {
      const transaction = await sequelize.transaction();
      try {
        await updateTotalStatistics(transaction);
        await transaction.commit();
        stats = await TotalStatistic.findOne();
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    }

    return stats;
  } catch (error) {
    logger.error(`获取累计统计数据失败: ${error.message}`, error);
    throw error;
  }
}

/**
 * 获取最近N天的统计数据
 * @param {Number} days 天数
 * @returns {Promise<Array>} 统计数据数组
 */
async function getRecentDaysStatistics(days = 30) {
  try {
    const endDate = moment().format('YYYY-MM-DD');
    const startDate = moment().subtract(days - 1, 'days').format('YYYY-MM-DD');

    // 查询最近N天的统计数据
    const stats = await DailyStatistic.findAll({
      where: {
        date: {
          [Op.between]: [startDate, endDate]
        }
      },
      order: [['date', 'ASC']]
    });

    return stats;
  } catch (error) {
    logger.error(`获取最近${days}天统计数据失败: ${error.message}`, error);
    throw error;
  }
}

module.exports = {
  calculateDailyStatistics,
  updateDailyStatistics,
  updateTotalStatistics,
  initializeHistoricalStatistics,
  getTodayStatistics,
  getYesterdayStatistics,
  getTotalStatistics,
  getRecentDaysStatistics
};
