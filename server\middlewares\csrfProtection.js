/**
 * CSRF保护中间件
 * 通过验证请求头中的Referer或Origin来防止CSRF攻击
 */
const csrfProtection = (req, res, next) => {
  // 开发环境下禁用CSRF保护
  if (process.env.NODE_ENV === 'development') {
    return next();
  }

  // 跳过GET、HEAD、OPTIONS请求，因为这些请求通常不会修改数据
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // 获取Referer或Origin
  const referer = req.headers.referer || '';
  const origin = req.headers.origin || '';

  // 获取允许的域名列表（从环境变量中获取，默认为localhost）
  const allowedDomains = (process.env.ALLOWED_DOMAINS || 'localhost:8080,localhost:8081,localhost:3000,127.0.0.1:8080,127.0.0.1:8081,127.0.0.1:3000')
    .split(',')
    .map(domain => domain.trim());

  // 检查Referer或Origin是否来自允许的域名
  const isValidReferer = allowedDomains.some(domain =>
    referer.includes(domain) || origin.includes(domain)
  );

  // 如果Referer或Origin不是来自允许的域名，则拒绝请求
  if (!isValidReferer) {
    console.warn(`CSRF保护: 拒绝来自 ${referer || origin} 的请求`);
    return res.status(403).json({
      code: 403,
      message: '禁止访问：无效的来源',
      data: null
    });
  }

  // 如果验证通过，继续处理请求
  next();
};

module.exports = csrfProtection;
