{"version": 3, "file": "table-grid.js", "sources": ["../../../../../../packages/components/table-v2/src/table-grid.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport {\n  DynamicSizeGrid,\n  FixedSizeGrid,\n} from '@element-plus/components/virtual-list'\nimport { isNumber, isObject } from '@element-plus/utils'\nimport { Header } from './components'\nimport { TableV2InjectionKey } from './tokens'\nimport { tableV2GridProps } from './grid'\nimport { sum } from './utils'\n\nimport type { UnwrapRef } from 'vue'\nimport type {\n  DynamicSizeGridInstance,\n  GridDefaultSlotParams,\n  GridItemKeyGetter,\n  GridItemRenderedEvtParams,\n  GridScrollOptions,\n  ResetAfterIndex,\n  Alignment as ScrollStrategy,\n} from '@element-plus/components/virtual-list'\nimport type { TableV2HeaderInstance } from './components'\nimport type { TableV2GridProps } from './grid'\n\nconst COMPONENT_NAME = 'ElTableV2Grid'\n\nconst useTableGrid = (props: TableV2GridProps) => {\n  const headerRef = ref<TableV2HeaderInstance>()\n  const bodyRef = ref<DynamicSizeGridInstance>()\n  const scrollLeft = ref(0)\n\n  const totalHeight = computed(() => {\n    const { data, rowHeight, estimatedRowHeight } = props\n\n    if (estimatedRowHeight) {\n      return\n    }\n\n    return data.length * (rowHeight as number)\n  })\n\n  const fixedRowHeight = computed(() => {\n    const { fixedData, rowHeight } = props\n\n    return (fixedData?.length || 0) * (rowHeight as number)\n  })\n\n  const headerHeight = computed(() => sum(props.headerHeight))\n\n  const gridHeight = computed(() => {\n    const { height } = props\n    return Math.max(0, height - unref(headerHeight) - unref(fixedRowHeight))\n  })\n\n  const hasHeader = computed(() => {\n    return unref(headerHeight) + unref(fixedRowHeight) > 0\n  })\n\n  const itemKey: GridItemKeyGetter = ({ data, rowIndex }) =>\n    data[rowIndex][props.rowKey]\n\n  function onItemRendered({\n    rowCacheStart,\n    rowCacheEnd,\n    rowVisibleStart,\n    rowVisibleEnd,\n  }: GridItemRenderedEvtParams) {\n    props.onRowsRendered?.({\n      rowCacheStart,\n      rowCacheEnd,\n      rowVisibleStart,\n      rowVisibleEnd,\n    })\n  }\n\n  function resetAfterRowIndex(index: number, forceUpdate: boolean) {\n    bodyRef.value?.resetAfterRowIndex(index, forceUpdate)\n  }\n\n  function scrollTo(x: number, y: number): void\n  function scrollTo(options: GridScrollOptions): void\n  function scrollTo(leftOrOptions: number | GridScrollOptions, top?: number) {\n    const header$ = unref(headerRef)\n    const body$ = unref(bodyRef)\n\n    if (isObject(leftOrOptions)) {\n      header$?.scrollToLeft(leftOrOptions.scrollLeft)\n      scrollLeft.value = leftOrOptions.scrollLeft!\n      body$?.scrollTo(leftOrOptions)\n    } else {\n      header$?.scrollToLeft(leftOrOptions)\n      scrollLeft.value = leftOrOptions\n      body$?.scrollTo({\n        scrollLeft: leftOrOptions,\n        scrollTop: top,\n      })\n    }\n  }\n\n  function scrollToTop(scrollTop: number) {\n    unref(bodyRef)?.scrollTo({\n      scrollTop,\n    })\n  }\n\n  function scrollToRow(row: number, strategy: ScrollStrategy) {\n    unref(bodyRef)?.scrollToItem(row, 1, strategy)\n  }\n\n  function forceUpdate() {\n    unref(bodyRef)?.$forceUpdate()\n    unref(headerRef)?.$forceUpdate()\n  }\n\n  watch(\n    () => props.bodyWidth,\n    () => {\n      if (isNumber(props.estimatedRowHeight))\n        bodyRef.value?.resetAfter({ columnIndex: 0 }, false)\n    }\n  )\n\n  return {\n    bodyRef,\n    forceUpdate,\n    fixedRowHeight,\n    gridHeight,\n    hasHeader,\n    headerHeight,\n    headerRef,\n    totalHeight,\n\n    itemKey,\n    onItemRendered,\n    resetAfterRowIndex,\n    scrollTo,\n    scrollToTop,\n    scrollToRow,\n    scrollLeft,\n  }\n}\n\nconst TableGrid = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2GridProps,\n  setup(props, { slots, expose }) {\n    const { ns } = inject(TableV2InjectionKey)!\n\n    const {\n      bodyRef,\n      fixedRowHeight,\n      gridHeight,\n      hasHeader,\n      headerRef,\n      headerHeight,\n      totalHeight,\n\n      forceUpdate,\n      itemKey,\n      onItemRendered,\n      resetAfterRowIndex,\n      scrollTo,\n      scrollToTop,\n      scrollToRow,\n      scrollLeft,\n    } = useTableGrid(props)\n\n    provide('tableV2GridScrollLeft', scrollLeft)\n\n    expose({\n      forceUpdate,\n      /**\n       * @description fetch total height\n       */\n      totalHeight,\n      /**\n       * @description scroll to a position\n       */\n      scrollTo,\n      /**\n       * @description scroll vertically to position y\n       */\n      scrollToTop,\n      /**\n       * @description scroll to a given row\n       * @params row {Number} which row to scroll to\n       * @params strategy {ScrollStrategy} use what strategy to scroll to\n       */\n      scrollToRow,\n      /**\n       * @description reset rendered state after row index\n       */\n      resetAfterRowIndex,\n    })\n\n    const getColumnWidth = () => props.bodyWidth\n\n    return () => {\n      const {\n        cache,\n        columns,\n        data,\n        fixedData,\n        useIsScrolling,\n        scrollbarAlwaysOn,\n        scrollbarEndGap,\n        scrollbarStartGap,\n        style,\n        rowHeight,\n        bodyWidth,\n        estimatedRowHeight,\n        headerWidth,\n        height,\n        width,\n\n        getRowHeight,\n        onScroll,\n      } = props\n\n      const isDynamicRowEnabled = isNumber(estimatedRowHeight)\n      const Grid = isDynamicRowEnabled ? DynamicSizeGrid : FixedSizeGrid\n      const _headerHeight = unref(headerHeight)\n\n      return (\n        <div role=\"table\" class={[ns.e('table'), props.class]} style={style}>\n          <Grid\n            ref={bodyRef}\n            // special attrs\n            data={data}\n            useIsScrolling={useIsScrolling}\n            itemKey={itemKey}\n            // column attrs\n            columnCache={0}\n            columnWidth={isDynamicRowEnabled ? getColumnWidth : bodyWidth}\n            totalColumn={1}\n            // row attrs\n            totalRow={data.length}\n            rowCache={cache}\n            rowHeight={isDynamicRowEnabled ? getRowHeight : rowHeight}\n            // DOM attrs\n            width={width}\n            height={unref(gridHeight)}\n            class={ns.e('body')}\n            role=\"rowgroup\"\n            scrollbarStartGap={scrollbarStartGap}\n            scrollbarEndGap={scrollbarEndGap}\n            scrollbarAlwaysOn={scrollbarAlwaysOn}\n            // handlers\n            onScroll={onScroll}\n            onItemRendered={onItemRendered}\n            perfMode={false}\n          >\n            {{\n              default: (params: GridDefaultSlotParams) => {\n                const rowData = data[params.rowIndex]\n                return slots.row?.({\n                  ...params,\n                  columns,\n                  rowData,\n                })\n              },\n            }}\n          </Grid>\n          {unref(hasHeader) && (\n            <Header\n              ref={headerRef}\n              class={ns.e('header-wrapper')}\n              columns={columns}\n              headerData={data}\n              headerHeight={props.headerHeight}\n              fixedHeaderData={fixedData}\n              rowWidth={headerWidth}\n              rowHeight={rowHeight}\n              width={width}\n              height={Math.min(_headerHeight + unref(fixedRowHeight), height)}\n            >\n              {{\n                dynamic: slots.header,\n                fixed: slots.row,\n              }}\n            </Header>\n          )}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableGrid\n\nexport type TableGridRowSlotParams = {\n  columns: TableV2GridProps['columns']\n  rowData: any\n} & GridDefaultSlotParams\n\nexport type TableGridInstance = InstanceType<typeof TableGrid> &\n  UnwrapRef<{\n    forceUpdate: () => void\n    /**\n     * @description fetch total height\n     */\n    totalHeight: number\n\n    /**\n     * @description scrollTo a position\n     * @param { number | ScrollToOptions } arg1\n     * @param { number } arg2\n     */\n    scrollTo(leftOrOptions: number | GridScrollOptions, top?: number): void\n\n    /**\n     * @description scroll vertically to position y\n     */\n    scrollToTop(scrollTop: number): void\n    /**\n     * @description scroll to a given row\n     * @params row {Number} which row to scroll to\n     * @params @optional strategy {ScrollStrategy} use what strategy to scroll to\n     */\n    scrollToRow(row: number, strategy: ScrollStrategy): void\n    /**\n     * @description reset rendered state after row index\n     * @param { number } rowIndex\n     * @param { boolean } forceUpdate\n     */\n    resetAfterRowIndex: ResetAfterIndex\n  }>\n"], "names": ["COMPONENT_NAME", "useTableGrid", "props", "headerRef", "ref", "bodyRef", "scrollLeft", "computed", "data", "rowHeight", "estimatedRowHeight", "sum", "headerHeight", "gridHeight", "unref", "height", "Math", "max", "<PERSON><PERSON><PERSON><PERSON>", "itemKey", "rowIndex", "rowCacheStart", "rowCacheEnd", "rowVisibleEnd", "forceUpdate", "isObject", "leftOrOptions", "header$", "scrollToLeft", "value", "scrollTop", "top", "scrollToTop", "watch", "isNumber", "$forceUpdate", "columnIndex", "fixedRowHeight", "totalHeight", "onItemRendered", "resetAfterRowIndex", "defineComponent", "TableGrid", "name", "expose", "ns", "inject", "TableV2InjectionKey", "scrollTo", "scrollToRow", "provide", "DynamicSizeGrid", "FixedSizeGrid", "_createVNode", "cache", "columns", "fixedData", "useIsScrolling", "scrollbarAlwaysOn", "scrollbarEndGap", "scrollbarStartGap", "style", "bodyWidth", "headerWidth", "width", "onScroll", "isDynamicRowEnabled", "_headerHeight", "class"], "mappings": ";;;;;;;;;;;;;;AAgCA,MAAMA,cAAc,GAAG,eAAvB,CAAA;;AAEA,EAAMC,MAAAA,SAAAA,GAAgBC,OAAAA,EAAAA,CAAD;EACnB,MAAMC,OAAAA,GAAYC,OAAAA,EAAG,CAArB;EACA,MAAMC,UAAUD,GAAGA,OAAnB,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,MAAME,WAAU,GAAMC,YAAtB,CAAA,MAAA;AAEA,IAAA,MAAiB;MACT,IAAA;MAAEC,SAAF;MAAQC,kBAAR;AAAmBC,KAAAA,GAAAA,KAAAA,CAAAA;AAAnB,IAAA,IAA0CR,kBAAhD,EAAA;;AAEA,KAAA;AACE,IAAA,OAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA;AACD,GAAA,CAAA,CAAA;;AAED,IAAA,MAAA;AACD,MARD,SAAA;AAUA,MAAA,SAAoB;KACZ,GAAA,KAAA,CAAA;WAAA,CAAA,CAAA,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,SAAA,CAAA;AAAaO,GAAAA,CAAAA,CAAAA;AAAb,EAAA,MAA2BP,YAAjC,GAAAK,YAAA,CAAA,MAAAI,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEA,EAAA,MAAA,UAAiB,GAAAJ,YAAT,CAAqB;AAC9B,IAJD,MAAA;MAMMK,MAAAA;AAEN,KAAA,GAAMC,KAAU,CAAA;IACd,OAAM,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,GAAAC,SAAA,CAAA,YAAA,CAAA,GAAAA,SAAA,CAAA,cAAA,CAAA,CAAA,CAAA;AAAEC,GAAAA,CAAAA,CAAAA;AAAF,EAAA,MAAab,SAAnB,GAAAK,YAAA,CAAA,MAAA;AACA,IAAA,OAAOS,SAAKC,CAAAA,YAAa,CAAA,GAAQH,SAACF,eAAf,CAA+BE,KAAK;AACxD,GAH0B,CAA3B,CAAA;AAKA,EAAA,MAAMI,OAAS,GAAA,CAAA;IACb,IAAOJ;AACR,IAFD,QAAA;;EAIA,SAAMK,cAA8B,CAAA;IAAEX,aAAF;AAAQY,IAAAA,WAAAA;IAAT,eAC5BA;;AAEP,GAAA,EAAA;IACEC,IADsB,EAAA,CAAA;IAEtBC,CAFsB,EAAA,GAAA,KAAA,CAAA,cAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA;MAAA,aAAA;AAItBC,MAAAA,WAAAA;AAJsB,MAKM,eAAA;MACvB;MACHF,CADqB;;WAAA,kBAAA,CAAA,KAAA,EAAA,YAAA,EAAA;AAIrBE,IAAAA,IAAAA,EAAAA,CAAAA;IAJqB,CAAvB,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAMD,GAAA;;AAED,IAAA,MAAA,OAAA,GAAAT,SAAA,CAAA,SAAA,CAAA,CAA2CU;AACzCnB,IAAAA,MAAAA,KAAA,GAAAS;AACD,IAAA,IAAAW,eAAA,CAAA,aAAA,CAAA,EAAA;;AAID,MAAA,UAAA,CAAA,KAAA,GAAA,aAAA,CAA2E,UAAA,CAAA;AACzE,MAAA,KAAa,IAAA,IAAA,GAAQ,cAArB,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AACA,KAAA,MAAW;;AAEX,MAAA,UAAY,CAACC,KAAD,GAAA,aAAiB,CAAA;AAC3BC,MAAAA,KAAAA,IAASC,IAAAA,GAAAA,KAAT,CAAsBF,GAAAA,KAAAA,CAAAA,QAAtB,CAAA;AACApB,QAAAA,UAAWuB,EAAAA,aAAQH;QACd,SAAL,EAAA,GAAgBA;AACjB,OAAM,CAAA,CAAA;;;WAGA,WAAW,CAAA,SAAA,EAAA;AACdpB,IAAAA,IAAAA,EAAAA,CAAAA;AACAwB,IAAAA,CAAAA,EAAAA,GAAAA,SAAAA,CAAAA,OAAWC,CAAAA,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,QAAAA,CAAAA;MAFG,SAAhB;AAID,KAAA,CAAA,CAAA;AACF,GAAA;;IAEQC,IAAAA,EAAAA,CAAAA;AACPlB,IAAAA,CAAAA,EAAAA,GAAMT,SAAAA,CAAAA,aAAmB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,EAAA,QAAA,CAAA,CAAA;AACvByB,GAAAA;WADF,WAAA,GAAA;AAGD,IAAA,IAAA,EAAA,EAAA,EAAA,CAAA;;AAED,IAAA,CAAA,EAAA,GAAAhB,SAAA,CAAA,SAAA,CAAA,YAA4D,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;;AAE3D,EAAAmB,SAAA,CAAA,MAAA,KAAA,CAAA,SAAA,EAAA,MAAA;;AAED,IAAA,IAAAC,oBAAA,CAAuB,kBAAA,CAAA;AACrBpB,MAAAA,CAAAA,EAAK,GAACT,OAAN,CAAgB8B,KAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AACArB,QAAAA,WAAK,EAAL,CAAkBqB;AACnB,OAAA,EAAA,KAAA,CAAA,CAAA;;AAEDF,EAAAA,OACQ/B;AAEJ,IAAA,OAAY;AACkBkC,IAAAA,WAAAA;AAAF,IAAA,cAA1B;AACH,IALH,UAAA;IAQO,SAAA;IACL/B,YADK;IAELmB,SAFK;IAGLa,WAHK;IAILxB,OAJK;IAKLK,cALK;IAMLN,kBANK;IAOLT,QAPK;IAQLmC,WARK;IAULnB,WAVK;IAWLoB,UAXK;IAYLC;;MAZK,SAAA,GAAAC,mBAAA,CAAA;MAAA,EAAA,cAAA;AAgBLnC,EAAAA,KAAAA,EAAAA,qBAAAA;EAhBK,KAAP,CAAA,KAAA,EAAA;AAkBD,IAlHD,KAAA;;AAoHA,GAAMoC,EAAAA;AACJC,IAAAA,MADgC;AAEhCzC,MAAAA;;IACK;MAAQ,OAAA;AAAS0C,MAAAA,cAAAA;AAAT,MAAmB,UAAA;MACxB,SAAA;AAAEC,MAAAA,SAAAA;MAAOC,YAAOC;MAEhB,WAAA;MACJ1C,WADI;MAEJgC,OAFI;MAGJxB,cAHI;MAIJK,kBAJI;MAKJf,QALI;MAMJS,WANI;MAOJ0B,WAPI;MASJd,UATI;QAAA,YAAA,CAAA,KAAA,CAAA,CAAA;eAAA,CAAA,uBAAA,EAAA,UAAA,CAAA,CAAA;UAAA,CAAA;MAaJwB,WAbI;MAcJhB,WAdI;MAeJiB,QAfI;AAgBJ3C,MAAAA,WAAAA;MACEL,WAAAA;AAEJiD,MAAAA,kBAAQ;AAERN,KAAAA,CAAAA,CAAAA;UAAO,cAAA,GAAA,MAAA,KAAA,CAAA,SAAA,CAAA;;AAEL,MAAA,MAAA;AACN,QAAA,KAAA;AACA,QAAA,OAAA;QAJW,IAAA;;AAML,QAAA,cAAA;AACN,QAAA,iBAAA;AACA,QAAA,eAAA;QARW,iBAAA;;AAUL,QAAA,SAAA;AACN,QAAA,SAAA;AACA,QAAA,kBAAA;QAZW,WAAA;;AAcL,QAAA,KAAA;AACN,QAAA,YAAA;AACA,QAAA,QAAA;AACA,OAAA,GAAA,KAAA,CAAA;AACA,MAAA,MAAA,mBAAA,GAAAV,cAAA,CAAA,kBAAA,CAAA,CAAA;MACMe,MAnBK,IAAA,GAAA,mBAAA,GAAAE,0BAAA,GAAAC,wBAAA,CAAA;;AAoBL,MAAA,OAAAC,eAAA,CAAA,KAAA,EAAA;AACN,QAAA,MAAA,EAAA,OAAA;AACA,QAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA;AACMb,QAAAA,OAAAA,EAAAA,KAAAA;AAvBK,OAAP,EAAA,CAAAa,eAAA,CAAA,IAAA,EAAA;;AA0BA,QAAA,MAAoB,EAAA,IAAA;;AAEpB,QAAA,SAAa,EAAA,OAAA;QACL,aAAA,EAAA,CAAA;QACJC,aADI,EAAA,mBAAA,GAAA,cAAA,GAAA,SAAA;QAEJC,aAFI,EAAA,CAAA;QAGJ/C,UAHI,EAAA,IAAA,CAAA,MAAA;QAIJgD,UAJI,EAAA,KAAA;QAKJC,WALI,EAAA,mBAAA,GAAA,YAAA,GAAA,SAAA;QAMJC,OANI,EAAA,KAAA;QAOJC,QAPI,EAAA7C,SAAA,CAAA,UAAA,CAAA;QAQJ8C,OARI,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;QASJC,MATI,EAAA,UAAA;QAUJpD,mBAVI,EAAA,iBAAA;QAWJqD,iBAXI,EAAA,eAAA;QAYJpD,mBAZI,EAAA,iBAAA;QAaJqD,UAbI,EAAA,QAAA;QAcJhD,gBAdI,EAAA,cAAA;QAeJiD,UAfI,EAAA,KAAA;SAAA;AAkBJC,QAAAA,OAAAA,EAAAA,CAAAA,MAAAA,KAAAA;AAlBI,UAmBF/D,IAnBJ,EAAA,CAAA;AAqBA,UAAA,MAAyB,OAAA,GAAA,IAAA,CAAA,MAAW,CAAA;AACpC,UAAA,OAAagE,CAAAA,EAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAAA,IAAsBf,GAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAH,IAAhC,CAAA,KAAA,EAAA;;AACA,YAAMgB,OAAa;;AAEnB,WAAA,CAAA,CAAA;AAAA,SAAA;QAAA,EAC2BrD,SAAA,CAAA,SAAC,CAAA,IAAAuC,eAAqBe,CAAAA,iBADjD,EAAA;QAAA,KACgEP,EAAAA,SAAAA;AADhE,QAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA;AAAA,QAAA,SAAA,EAAA,OAAA;AAAA,QAAA,YAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA,KAAA,CAAA,YAAA;AAAA,QAAA,iBAAA,EAAA,SAAA;AAAA,QAAA,UAAA,EAAA,WAAA;AAAA,QAAA,WAAA,EAAA,SAUsC;AAVtC,QAAA,OAAA,EAAA,KAAA;QAAA,QAagBrD,EAAAA,IAAAA,CAAAA,GAbhB,CAAA,aAAA,GAAAM,SAAA,CAAA,cAAA,CAAA,EAAA,MAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAeoC,CAAA,MAAA;AAfpC,QAAA,KAAA,EAAA,KAAA,CAAA,GAAA;QAAA,CAkBcA,CAAAA,CAAAA;AAlBd,KAAA,CAAA;AAAA,GAAA;AAAA,CAAA,CAAA,CAAA;AAAA,YAAA,SAAA;;;;"}