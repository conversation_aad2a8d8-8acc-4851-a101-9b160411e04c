/**
 * 投资相关 API 服务
 */
import { get, post } from '../../utils/request';

// 投资详情API已移除

/**
 * 获取用户投资记录
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=10] - 每页数量
 * @param {string} [params.status] - 投资状态
 * @returns {Promise<Object>} 投资记录数据
 */
export const getUserInvestments = (params = {}) => {
  return get('/mobile/investments', {
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      status: params.status,
      ...params
    }
  });
};

/**
 * 创建投资
 * @param {Object} data - 投资数据
 * @param {number} data.project_id - 项目ID
 * @param {number} data.amount - 投资金额
 * @param {number} [data.quantity=1] - 购买数量
 * @returns {Promise<Object>} 创建结果
 */
export const createInvestment = (data) => {
  return post('/mobile/investments', data);
};
