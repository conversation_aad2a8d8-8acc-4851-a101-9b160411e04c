/**
 * 时间格式化工具 (简化版)
 * 直接使用浏览器本地时区处理所有时间
 */
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// 注册插件
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 统一格式化日期时间
 * 将UTC时间转换为本地时区后显示
 *
 * @param {string|Date|null} dateTime - 日期时间字符串或Date对象
 * @param {string} format - 输出格式，默认为'YYYY-MM-DD HH:mm:ss'
 * @returns {string} - 格式化后的日期时间字符串，如果输入为空则返回'-'
 */
export function formatDateTime(dateTime, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateTime) return '-';

  try {
    // 方案5.1：将UTC时间转换为本地时区显示
    return dayjs.utc(dateTime).local().format(format);
  } catch (error) {
    console.error('日期格式化错误:', error, dateTime);
    return '-';
  }
}

/**
 * 格式化日期（不包含时间）
 *
 * @param {string|Date|null} dateTime - 日期时间字符串或Date对象
 * @returns {string} - 格式化后的日期字符串，如果输入为空则返回'-'
 */
export function formatDate(dateTime) {
  return formatDateTime(dateTime, 'YYYY-MM-DD');
}

/**
 * 格式化时间（不包含日期）
 *
 * @param {string|Date|null} dateTime - 日期时间字符串或Date对象
 * @returns {string} - 格式化后的时间字符串，如果输入为空则返回'-'
 */
export function formatTime(dateTime) {
  return formatDateTime(dateTime, 'HH:mm:ss');
}
