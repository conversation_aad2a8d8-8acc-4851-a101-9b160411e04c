-- FOX投资平台数据迁移脚本
-- 作者：FOX开发团队
-- 日期：2025-06-04

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 迁移用户级别表数据
INSERT INTO user_levels_new (
    id, name, level, upgrade_users, upgrade_amount,
    return_rate, upgrade_bonus, content, created_at, updated_at
)
SELECT
    id, name, level, upgrade_users, upgrade_amount,
    return_rate, upgrade_bonus, content, created_at, updated_at
FROM user_levels;

-- 更新image_id字段（假设image_url对应附件表中的记录）
UPDATE user_levels_new ul
JOIN attachments a ON a.file_path = (SELECT image_url FROM user_levels WHERE id = ul.id)
SET ul.image_id = a.id
WHERE ul.id IN (SELECT id FROM user_levels WHERE image_url IS NOT NULL);

-- 2. 迁移附件表数据
INSERT INTO attachments_new (
    id, category, filename, original_name, file_path,
    file_size, file_type, mime_type, storage_engine, created_at, updated_at
)
SELECT
    id, category, filename, original_name, file_path,
    file_size, file_type, mime_type, storage_engine, created_at, updated_at
FROM attachments;

-- 将metadata字段从TEXT转换为JSON
UPDATE attachments_new
SET metadata = CAST(
    (SELECT metadata FROM attachments WHERE id = attachments_new.id)
    AS JSON
)
WHERE id IN (SELECT id FROM attachments WHERE metadata IS NOT NULL);

-- 3. 迁移用户表数据
INSERT INTO users_new (
    id, username, password, name, email, phone, avatar,
    invite_code, inviter_id, balance,
    last_login, created_at, updated_at
)
SELECT
    id, username, password, name, email, phone, avatar,
    invite_code, inviter_id, balance,
    last_login, created_at, updated_at
FROM users;

-- 设置用户级别关联
-- 注释掉，因为users表中没有level字段
-- UPDATE users_new
-- SET level_id = (SELECT id FROM user_levels_new WHERE level =
--                 (SELECT level FROM users WHERE id = users_new.id))
-- WHERE id IN (SELECT id FROM users WHERE level IS NOT NULL);

-- 设置用户状态
UPDATE users_new
SET status = CASE
    WHEN (SELECT status FROM users WHERE id = users_new.id) = 'banned' THEN 'blocked'
    WHEN (SELECT status FROM users WHERE id = users_new.id) = 'inactive' THEN 'inactive'
    ELSE 'active'
END;

-- 4. 创建账户余额表数据
-- 注释掉，因为users表中没有账户余额相关字段
-- 我们将为每个用户创建一个默认的账户余额记录

-- 为每个用户创建一个默认的收入账户 - CNY
INSERT INTO account_balances (user_id, account_type, currency, balance)
SELECT id, 'income', 'CNY', 0.00
FROM users;

-- 为每个用户创建一个默认的充值账户 - CNY
INSERT INTO account_balances (user_id, account_type, currency, balance)
SELECT id, 'deposit', 'CNY', balance
FROM users;

-- 5. 迁移银行卡表数据
-- 用户银行卡
INSERT INTO bank_cards (
    user_id, bank_name, card_number, card_holder, branch,
    is_default, card_type, status, created_at, updated_at
)
SELECT
    user_id, bank_name, card_number, card_holder, branch,
    is_default, 'user', status, created_at, updated_at
FROM user_bank_cards;

-- 系统收款银行卡
INSERT INTO bank_cards (
    bank_name, card_number, card_holder, branch,
    is_default, card_type, daily_limit, status, created_at, updated_at
)
SELECT
    bank_name, card_number, card_holder, branch,
    is_default, 'system', daily_limit, status, created_at, updated_at
FROM receiving_bank_cards;

-- 6. 迁移投资项目表数据
INSERT INTO projects_new (
    id, type, category, name, description, price,
    min_investment, max_investment, duration, duration_unit,
    expected_return, profit_time, quantity, sold_quantity,
    currency, status, commission_enabled,
    level1_commission, level2_commission, level3_commission,
    created_at, updated_at
)
SELECT
    id, type, category, name, description, price,
    min_investment, max_investment, duration, duration_unit,
    expected_return, profit_time, quantity, sold_quantity,
    currency, status, commission_enabled,
    level1_commission, level2_commission, level3_commission,
    created_at, updated_at
FROM projects;

-- 更新image_id和video_id字段
UPDATE projects_new p
JOIN attachments_new a ON a.file_path = (SELECT image_url FROM projects WHERE id = p.id)
SET p.image_id = a.id
WHERE p.id IN (SELECT id FROM projects WHERE image_url IS NOT NULL);

UPDATE projects_new p
JOIN attachments_new a ON a.file_path = (SELECT video_url FROM projects WHERE id = p.id)
SET p.video_id = a.id
WHERE p.id IN (SELECT id FROM projects WHERE video_url IS NOT NULL);

-- 更新vip_level_id字段
UPDATE projects_new p
JOIN user_levels_new ul ON ul.name = (SELECT vip_level FROM projects WHERE id = p.id)
SET p.vip_level_id = ul.id
WHERE p.id IN (SELECT id FROM projects WHERE vip_level IS NOT NULL);

-- 7. 迁移交易表数据
INSERT INTO transactions_new (
    id, user_id, amount, balance, description, created_at
)
SELECT
    id, user_id, amount, balance, description, created_at
FROM transactions;

-- 设置交易类型
UPDATE transactions_new
SET type = CASE
    WHEN (SELECT type FROM transactions WHERE id = transactions_new.id) = '充值' THEN 'deposit'
    WHEN (SELECT type FROM transactions WHERE id = transactions_new.id) = '提现' THEN 'withdrawal'
    WHEN (SELECT type FROM transactions WHERE id = transactions_new.id) = '投资' THEN 'investment'
    WHEN (SELECT type FROM transactions WHERE id = transactions_new.id) = '收益' THEN 'profit'
    WHEN (SELECT type FROM transactions WHERE id = transactions_new.id) = '佣金' THEN 'commission'
    WHEN (SELECT type FROM transactions WHERE id = transactions_new.id) = '赠金' THEN 'bonus'
    WHEN (SELECT type FROM transactions WHERE id = transactions_new.id) = '扣除' THEN 'deduction'
    ELSE 'deposit'
END;

-- 设置交易状态
UPDATE transactions_new
SET status = CASE
    WHEN (SELECT status FROM transactions WHERE id = transactions_new.id) = '成功' THEN 'success'
    WHEN (SELECT status FROM transactions WHERE id = transactions_new.id) = '失败' THEN 'failed'
    WHEN (SELECT status FROM transactions WHERE id = transactions_new.id) = '处理中' THEN 'pending'
    ELSE 'success'
END;

-- 设置reference_id和reference_type
UPDATE transactions_new t
JOIN deposits d ON d.id = (SELECT related_id FROM transactions WHERE id = t.id AND type = '充值')
SET t.reference_id = d.id, t.reference_type = 'deposits'
WHERE t.type = 'deposit' AND t.id IN (SELECT id FROM transactions WHERE related_id IS NOT NULL AND type = '充值');

UPDATE transactions_new t
JOIN withdrawals w ON w.id = (SELECT related_id FROM transactions WHERE id = t.id AND type = '提现')
SET t.reference_id = w.id, t.reference_type = 'withdrawals'
WHERE t.type = 'withdrawal' AND t.id IN (SELECT id FROM transactions WHERE related_id IS NOT NULL AND type = '提现');

UPDATE transactions_new t
JOIN investments i ON i.id = (SELECT related_id FROM transactions WHERE id = t.id AND type = '投资')
SET t.reference_id = i.id, t.reference_type = 'investments'
WHERE t.type = 'investment' AND t.id IN (SELECT id FROM transactions WHERE related_id IS NOT NULL AND type = '投资');

UPDATE transactions_new t
JOIN investment_profits ip ON ip.id = (SELECT related_id FROM transactions WHERE id = t.id AND type = '收益')
SET t.reference_id = ip.id, t.reference_type = 'investment_profits'
WHERE t.type = 'profit' AND t.id IN (SELECT id FROM transactions WHERE related_id IS NOT NULL AND type = '收益');

UPDATE transactions_new t
JOIN commissions c ON c.id = (SELECT related_id FROM transactions WHERE id = t.id AND type = '佣金')
SET t.reference_id = c.id, t.reference_type = 'commissions'
WHERE t.type = 'commission' AND t.id IN (SELECT id FROM transactions WHERE related_id IS NOT NULL AND type = '佣金');

-- 8. 迁移投资记录表数据
INSERT INTO investments_new (
    id, user_id, project_id, amount, quantity,
    profit_rate, profit_cycle, total_profit, profit_count,
    start_time, created_at, updated_at
)
SELECT
    id, user_id, project_id, total_investment, quantity,
    profit_rate, profit_cycle, total_profit, profit_count,
    first_investment_time, created_at, updated_at
FROM investments;

-- 设置投资状态
UPDATE investments_new
SET status = CASE
    WHEN (SELECT status FROM investments WHERE id = investments_new.id) = '进行中' THEN 'active'
    WHEN (SELECT status FROM investments WHERE id = investments_new.id) = '暂停' THEN 'paused'
    WHEN (SELECT status FROM investments WHERE id = investments_new.id) = '完成' THEN 'completed'
    ELSE 'active'
END;

-- 设置结束时间
UPDATE investments_new i
SET end_time = DATE_ADD(i.start_time, INTERVAL
    (SELECT duration FROM projects_new WHERE id = i.project_id)
    * CASE
        WHEN (SELECT duration_unit FROM projects_new WHERE id = i.project_id) = '天' THEN 1
        WHEN (SELECT duration_unit FROM projects_new WHERE id = i.project_id) = '月' THEN 30
        WHEN (SELECT duration_unit FROM projects_new WHERE id = i.project_id) = '年' THEN 365
        ELSE 1
      END DAY)
WHERE i.id IN (SELECT id FROM investments);

-- 9. 迁移投资收益记录表数据
INSERT INTO investment_profits_new (
    id, investment_id, user_id, amount, profit_time, created_at
)
SELECT
    id, investment_id, user_id, amount, profit_time, created_at
FROM investment_profits;

-- 设置收益状态
UPDATE investment_profits_new
SET status = CASE
    WHEN (SELECT status FROM investment_profits WHERE id = investment_profits_new.id) = '已发放' THEN 'paid'
    WHEN (SELECT status FROM investment_profits WHERE id = investment_profits_new.id) = '未发放' THEN 'pending'
    ELSE 'paid'
END;

-- 关联交易记录
UPDATE investment_profits_new ip
JOIN transactions_new t ON t.reference_id = ip.id AND t.reference_type = 'investment_profits'
SET ip.transaction_id = t.id
WHERE ip.id IN (SELECT id FROM investment_profits);

-- 10. 迁移佣金记录表数据
INSERT INTO commissions_new (
    id, user_id, from_user_id, investment_id, level,
    amount, rate, created_at
)
SELECT
    id, user_id, from_user_id, investment_id, level,
    amount, rate, created_at
FROM commissions;

-- 设置佣金状态
UPDATE commissions_new
SET status = CASE
    WHEN (SELECT status FROM commissions WHERE id = commissions_new.id) = '已发放' THEN 'paid'
    WHEN (SELECT status FROM commissions WHERE id = commissions_new.id) = '未发放' THEN 'pending'
    ELSE 'paid'
END;

-- 关联交易记录
UPDATE commissions_new c
JOIN transactions_new t ON t.reference_id = c.id AND t.reference_type = 'commissions'
SET c.transaction_id = t.id
WHERE c.id IN (SELECT id FROM commissions);

-- 11. 迁移充值订单表数据
INSERT INTO deposits_new (
    id, user_id, order_number, amount, actual_amount,
    payment_method, payment_account, remark,
    payment_time, completion_time, created_at, updated_at
)
SELECT
    id, user_id, order_number, amount, actual_amount,
    payment_method, payment_account, remark,
    payment_time, completion_time, created_at, updated_at
FROM deposits;

-- 设置充值状态
UPDATE deposits_new
SET status = CASE
    WHEN (SELECT status FROM deposits WHERE id = deposits_new.id) = '待支付' THEN 'pending'
    WHEN (SELECT status FROM deposits WHERE id = deposits_new.id) = '已支付' THEN 'paid'
    WHEN (SELECT status FROM deposits WHERE id = deposits_new.id) = '已取消' THEN 'cancelled'
    WHEN (SELECT status FROM deposits WHERE id = deposits_new.id) = '已完成' THEN 'completed'
    ELSE 'pending'
END;

-- 关联收款银行卡
UPDATE deposits_new d
JOIN bank_cards bc ON bc.id = (
    SELECT id FROM bank_cards
    WHERE card_type = 'system' AND id = (
        SELECT receiving_card_id FROM deposits WHERE id = d.id
    )
)
SET d.receiving_card_id = bc.id
WHERE d.id IN (SELECT id FROM deposits WHERE receiving_card_id IS NOT NULL);

-- 关联交易记录
UPDATE deposits_new d
JOIN transactions_new t ON t.reference_id = d.id AND t.reference_type = 'deposits'
SET d.transaction_id = t.id
WHERE d.id IN (SELECT id FROM deposits);

-- 12. 迁移提现记录表数据
INSERT INTO withdrawals_new (
    id, user_id, order_number, amount, actual_amount,
    remark, approval_time, completion_time, created_at, updated_at
)
SELECT
    id, user_id, order_number, amount, actual_amount,
    remark, approval_time, completion_time, created_at, updated_at
FROM withdrawals;

-- 设置提现状态
UPDATE withdrawals_new
SET status = CASE
    WHEN (SELECT status FROM withdrawals WHERE id = withdrawals_new.id) = '待审核' THEN 'pending'
    WHEN (SELECT status FROM withdrawals WHERE id = withdrawals_new.id) = '审核通过' THEN 'approved'
    WHEN (SELECT status FROM withdrawals WHERE id = withdrawals_new.id) = '已拒绝' THEN 'rejected'
    WHEN (SELECT status FROM withdrawals WHERE id = withdrawals_new.id) = '已完成' THEN 'completed'
    ELSE 'pending'
END;

-- 关联用户银行卡
UPDATE withdrawals_new w
JOIN bank_cards bc ON bc.id = (
    SELECT id FROM bank_cards
    WHERE card_type = 'user' AND user_id = w.user_id AND id = (
        SELECT bank_card_id FROM withdrawals WHERE id = w.id
    )
)
SET w.bank_card_id = bc.id
WHERE w.id IN (SELECT id FROM withdrawals WHERE bank_card_id IS NOT NULL);

-- 关联交易记录
UPDATE withdrawals_new w
JOIN transactions_new t ON t.reference_id = w.id AND t.reference_type = 'withdrawals'
SET w.transaction_id = t.id
WHERE w.id IN (SELECT id FROM withdrawals);

-- 13. 迁移系统参数表数据
INSERT INTO system_params_new (
    id, param_key, param_value, param_type, description,
    group_name, sort_order, created_at, updated_at
)
SELECT
    id, param_key, param_value, param_type, description,
    group_name, sort_order, created_at, updated_at
FROM system_params;

-- 14. 迁移轮播图表数据
INSERT INTO banners_new (
    id, title, url, position, sort_order,
    status, start_time, end_time, created_at, updated_at
)
SELECT
    id, title, url, position, sort_order,
    status, start_time, end_time, created_at, updated_at
FROM banners;

-- 关联附件
UPDATE banners_new b
JOIN attachments_new a ON a.file_path = (SELECT image_url FROM banners WHERE id = b.id)
SET b.attachment_id = a.id
WHERE b.id IN (SELECT id FROM banners WHERE image_url IS NOT NULL);

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
