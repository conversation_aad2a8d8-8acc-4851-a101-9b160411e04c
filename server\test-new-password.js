const bcrypt = require('bcryptjs');

async function testPassword() {
  // 数据库中存储的新密码哈希
  const storedHash = '$2a$10$6.HYIVCvepWix3WzNduUGO0MQliWwNDpjpKZcpUVNTTyG7lRCZwvu';
  
  // 用户输入的密码
  const inputPassword = '123456';
  
  // 验证密码
  const isMatch = await bcrypt.compare(inputPassword, storedHash);
  
  console.log(`密码 "${inputPassword}" 与存储的哈希值匹配: ${isMatch}`);
}

testPassword().catch(console.error);
