/**
 * 系统参数路由
 */
const express = require('express');
const router = express.Router();
const systemParamController = require('../controllers/systemParamController');
const { verifyAdminToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 所有路由都需要验证token
router.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/system-params:
 *   get:
 *     summary: 获取系统参数列表
 *     tags: [系统参数]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: group_name
 *         schema:
 *           type: string
 *         description: 参数分组名称
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/', systemParamController.getSystemParams);

/**
 * @swagger
 * /api/admin/system-params/group/{group}:
 *   get:
 *     summary: 获取指定分组的系统参数
 *     tags: [系统参数]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数分组名称
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/group/:group', systemParamController.getSystemParamsByGroup);

/**
 * @swagger
 * /api/admin/system-params/key/{key}:
 *   get:
 *     summary: 获取系统参数详情
 *     tags: [系统参数]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数键
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 参数不存在
 */
router.get('/key/:key', systemParamController.getSystemParamByKey);

/**
 * @swagger
 * /api/admin/system-params:
 *   post:
 *     summary: 更新系统参数
 *     tags: [系统参数]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               params:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     param_key:
 *                       type: string
 *                     param_value:
 *                       type: string
 *                     param_type:
 *                       type: string
 *                     description:
 *                       type: string
 *                     group_name:
 *                       type: string
 *                     sort_order:
 *                       type: integer
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未认证
 */
router.post('/', systemParamController.updateSystemParams);

/**
 * @swagger
 * /api/admin/system-params/create:
 *   post:
 *     summary: 创建系统参数
 *     tags: [系统参数]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               param_key:
 *                 type: string
 *               param_value:
 *                 type: string
 *               param_type:
 *                 type: string
 *               description:
 *                 type: string
 *               group_name:
 *                 type: string
 *               sort_order:
 *                 type: integer
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未认证
 */
router.post('/create', systemParamController.createSystemParam);

/**
 * @swagger
 * /api/admin/system-params/{key}:
 *   delete:
 *     summary: 删除系统参数
 *     tags: [系统参数]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数键
 *     responses:
 *       200:
 *         description: 删除成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 参数不存在
 */
router.delete('/:key', systemParamController.deleteSystemParam);

module.exports = router;
