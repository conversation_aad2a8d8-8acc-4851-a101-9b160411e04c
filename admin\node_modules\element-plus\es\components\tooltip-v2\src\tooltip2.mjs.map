{"version": 3, "file": "tooltip2.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/tooltip.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { tooltipV2RootProps } from './root'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2ArrowProps } from './arrow'\nimport { tooltipV2ContentProps } from './content'\n\nimport type { ExtractPropTypes, TeleportProps, TransitionProps } from 'vue'\n\nexport const tooltipV2Props = buildProps({\n  ...tooltipV2RootProps,\n  ...tooltipV2ArrowProps,\n  ...tooltipV2TriggerProps,\n  ...tooltipV2ContentProps,\n  alwaysOn: Boolean,\n  fullTransition: Boolean,\n  transitionProps: {\n    type: definePropType<TransitionProps | null>(Object),\n    default: null,\n  },\n  teleported: Boolean,\n  to: {\n    type: definePropType<TeleportProps['to']>(String),\n    default: 'body',\n  },\n} as const)\n\nexport type TooltipV2Props = ExtractPropTypes<typeof tooltipV2Props>\n"], "names": [], "mappings": ";;;;;;AAKY,MAAC,cAAc,GAAG,UAAU,CAAC;AACzC,EAAE,GAAG,kBAAkB;AACvB,EAAE,GAAG,mBAAmB;AACxB,EAAE,GAAG,qBAAqB;AAC1B,EAAE,GAAG,qBAAqB;AAC1B,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,cAAc,EAAE,OAAO;AACzB,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}