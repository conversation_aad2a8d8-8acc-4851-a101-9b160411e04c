/* empty css             *//* empty css                   *//* empty css                      *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                    *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                */import"./el-tooltip-l0sNRNKZ.js";/* empty css                        *//* empty css                  */import{d as Ue,r as v,a as Ie,q as $e,o as Le,a_ as Be,c as z,b as d,V as f,e as l,a8 as B,a9 as O,w as o,m as Ae,a7 as R,f as je,i as Fe,v as G,ab as Pe,ac as qe,p as m,aa as Ke,y as T,n as b,x as Me,j as c,ad as Ne,aw as Oe,ae as J,aB as H,af as Re,ai as Ge,a$ as Je,b0 as He,aj as Qe,ak as We,ap as Xe,al as Ye,an as Ze,aL as el,K as ie,b1 as se,b2 as re,b3 as de,b4 as ll,aT as tl,b5 as al,aq as ol,at as nl,E as il,av as sl,h as rl,g as s,_ as dl}from"./index-LncY9lAB.js";import{g as ul,a as cl,u as fl,b as ml,d as pl,c as vl}from"./attachments-CNgOoo0P.js";import"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";const gl={class:"attachments-container"},_l={class:"tabs"},bl=["onClick"],yl={class:"toolbar"},wl={class:"toolbar-left"},hl={class:"toolbar-right"},kl={class:"view-toggle"},El={class:"table-wrapper"},Vl={class:"operation-buttons-container"},Cl={class:"grid-wrapper"},Tl={class:"grid-item"},xl={class:"grid-item-select"},Sl={class:"grid-item-preview"},zl={class:"grid-item-info"},Dl={class:"grid-item-name"},Ul={class:"grid-item-meta"},Il={class:"grid-item-actions"},$l={class:"pagination-container"},Ll={class:"dialog-footer"},Bl={class:"dialog-footer"},Al=Ue({__name:"index",setup(jl){const V=v(!1),h=v([]),A=v(0),x=v(1),D=v(10),j=v(""),F=v("all"),U=v("list"),y=v([]),k=v(!1),S=v(!1),I=v([]),n=Ie({id:null,category:"",filename:"",filePath:"",fileSize:0,width:null,height:null,fileType:"",mimeType:"",metadata:"",storageEngine:"local",uploadTime:"",url:""}),ue=[{label:"全部",value:"all"},{label:"自定义",value:"自定义"},{label:"未归类",value:""}],Q=$e(()=>y.value.length>0),E=async()=>{V.value=!0;try{const t={page:x.value,pageSize:D.value};j.value&&(t.filename=j.value),F.value!=="all"&&(t.category=F.value);const e=await ul(t);e&&e.data&&e.data.list?(console.log("解析的数据(格式1):",JSON.stringify(e.data)),h.value=e.data.list.map(i=>({...i,selected:!1})),A.value=e.data.total):e&&e.total!==void 0&&e.list?(h.value=e.list.map(i=>({...i,selected:!1})),A.value=e.total):(m.error("获取附件列表失败: 数据格式不正确"),h.value=[],A.value=0)}catch(t){t instanceof Error?m.error(`获取附件列表失败: ${t.message}`):m.error("获取附件列表失败: 未知错误")}finally{V.value=!1}},ce=()=>{E()},W=t=>{D.value=t,x.value=1,E()},fe=t=>{x.value=t,E()},X=()=>{x.value=1,E()},me=()=>{m.info("功能尚未实现")},pe=t=>{y.value=t},ve=()=>{const t=h.value.filter(e=>e.selected);y.value=t},ge=()=>{Object.keys(n).forEach(t=>{t!=="storageEngine"&&(n[t]=t==="category"?"":null)}),n.storageEngine="local",I.value=[],k.value=!0},_e=()=>{if(y.value.length!==1){m.warning("请选择一个附件进行编辑");return}P(y.value[0])},P=async t=>{try{V.value=!0;const e=await cl(t.id);e&&e.data&&typeof e.data=="object"?(Object.assign(n,e.data),k.value=!0):e&&typeof e=="object"&&e.id?(Object.assign(n,e),k.value=!0):m.error("获取附件详情失败: 数据格式不正确")}catch(e){e instanceof Error?m.error(`获取附件详情失败: ${e.message}`):m.error("获取附件详情失败: 未知错误")}finally{V.value=!1}},be=t=>{if(I.value=[t],!n.id&&t&&t.name){const e=t.name;n.filename=e}},ye=async()=>{if(n.id)try{const t={category:n.category,filename:n.filename,metadata:n.metadata},e=await fl(n.id,t);m.success("更新附件成功"),k.value=!1,E()}catch(t){t instanceof Error?m.error(`更新附件失败: ${t.message}`):m.error("更新附件失败: 未知错误")}else{if(I.value.length===0){m.warning("请选择要上传的文件");return}try{const t=new FormData;t.append("file",I.value[0].raw),t.append("category",n.category||"未归类"),n.metadata&&t.append("metadata",n.metadata);const e=await ml(t);m.success("上传附件成功"),k.value=!1,E()}catch(t){t instanceof Error?m.error(`上传附件失败: ${t.message}`):m.error("上传附件失败: 未知错误")}}},we=()=>{if(y.value.length===0){m.warning("请选择要删除的附件");return}S.value=!0},Y=t=>{y.value=[t],S.value=!0},he=async()=>{try{V.value=!0;let t;if(y.value.length===1){const e=y.value[0].id;t=await pl(e)}else{const e=y.value.map(i=>i.id);t=await vl(e)}m.success("删除附件成功"),S.value=!1,E()}catch(t){t instanceof Error?m.error(`删除附件失败: ${t.message}`):m.error("删除附件失败: 未知错误")}finally{V.value=!1}},q=t=>t<1024?t+" B":t<1024*1024?(t/1024).toFixed(1)+" KB":t<1024*1024*1024?(t/(1024*1024)).toFixed(1)+" MB":(t/(1024*1024*1024)).toFixed(1)+" GB",C=t=>t?["jpg","jpeg","png","gif","bmp","webp","svg"].includes(t.toLowerCase()):!1,Z=t=>!t||!C(t.fileType)?0:h.value.filter(i=>C(i.fileType)).findIndex(i=>i.id===t.id),$=v(!1),w=v(null),ee=()=>{$.value=!0,setTimeout(()=>{const t=document.querySelector(".el-image-viewer__wrapper");t&&(w.value=t,document.addEventListener("click",M),document.addEventListener("keydown",N),t.addEventListener("blur",K,!0),t.setAttribute("tabindex","-1"),t.focus())},100)},le=()=>{$.value=!1,document.removeEventListener("click",M),document.removeEventListener("keydown",N),w.value&&(w.value.removeEventListener("blur",K,!0),w.value=null)},K=t=>{$.value&&setTimeout(()=>{const e=document.activeElement,i=w.value;if(i&&!i.contains(e)){const r=i.querySelector(".el-image-viewer__close");r&&r.click()}},100)},M=t=>{if($.value&&w.value&&!w.value.contains(t.target)){const e=w.value.querySelector(".el-image-viewer__close");e&&e.click()}},N=t=>{if(t.key==="Tab"&&$.value){t.preventDefault();const e=w.value.querySelectorAll("button, [tabindex]");if(e.length>0){const i=e[0],r=e[e.length-1];if(t.shiftKey){if(document.activeElement===i)r.focus();else for(let u=1;u<e.length;u++)if(document.activeElement===e[u]){e[u-1].focus();break}}else if(document.activeElement===r)i.focus();else for(let u=0;u<e.length-1;u++)if(document.activeElement===e[u]){e[u+1].focus();break}}}};return Le(()=>{E()}),Be(()=>{document.removeEventListener("click",M),document.removeEventListener("keydown",N),w.value&&w.value.removeEventListener("blur",K,!0)}),(t,e)=>{const i=Me,r=Ae,u=Fe,g=Ye,ke=Ze,te=el,Ee=Xe,ae=Ke,Ve=tl,Ce=ll,Te=al,L=nl,oe=ol,xe=Pe,Se=sl,_=rl,ze=il,ne=qe,De=We;return s(),z("div",gl,[d("div",_l,[(s(),z(B,null,O(ue,(a,p)=>d("div",{key:p,class:G(["tab",{active:F.value===a.value}]),onClick:Fl=>F.value=a.value},T(a.label),11,bl)),64))]),d("div",yl,[d("div",wl,[l(r,{class:"toolbar-button",type:"default",onClick:ce},{default:o(()=>[l(i,null,{default:o(()=>[l(c(Ne))]),_:1}),e[19]||(e[19]=b("刷新 "))]),_:1}),l(r,{class:"toolbar-button",type:"success",onClick:ge},{default:o(()=>[l(i,null,{default:o(()=>[l(c(Oe))]),_:1}),e[20]||(e[20]=b("添加 "))]),_:1}),l(r,{class:"toolbar-button",type:"primary",onClick:_e,disabled:!Q.value},{default:o(()=>[l(i,null,{default:o(()=>[l(c(J))]),_:1}),e[21]||(e[21]=b("编辑 "))]),_:1},8,["disabled"]),l(r,{class:"toolbar-button",type:"danger",onClick:we,disabled:!Q.value},{default:o(()=>[l(i,null,{default:o(()=>[l(c(H))]),_:1}),e[22]||(e[22]=b("删除 "))]),_:1},8,["disabled"])]),d("div",hl,[l(u,{modelValue:j.value,"onUpdate:modelValue":e[0]||(e[0]=a=>j.value=a),placeholder:"搜索附件名称",class:"search-input",onKeyup:je(X,["enter"])},null,8,["modelValue"]),l(r,{class:"search-button",type:"primary",onClick:X},{default:o(()=>[l(i,null,{default:o(()=>[l(c(Re))]),_:1})]),_:1}),h.value.length>0?(s(),f(r,{key:0,class:"toolbar-button export-button",type:"default",onClick:me},{default:o(()=>[l(i,null,{default:o(()=>[l(c(Ge))]),_:1}),e[23]||(e[23]=b("导出 "))]),_:1})):R("",!0),d("div",kl,[l(r,{class:G(["view-button",{active:U.value==="list"}]),onClick:e[1]||(e[1]=a=>U.value="list")},{default:o(()=>[l(i,null,{default:o(()=>[l(c(Je))]),_:1})]),_:1},8,["class"]),l(r,{class:G(["view-button",{active:U.value==="grid"}]),onClick:e[2]||(e[2]=a=>U.value="grid")},{default:o(()=>[l(i,null,{default:o(()=>[l(c(He))]),_:1})]),_:1},8,["class"])])])]),U.value==="list"?(s(),f(ae,{key:0,class:"table-card"},{default:o(()=>[d("div",El,[Qe((s(),f(Ee,{data:h.value,style:{width:"100%"},border:"",stripe:"","highlight-current-row":"",onSelectionChange:pe},{default:o(()=>[l(g,{type:"selection",width:"40",align:"center",fixed:"left"}),l(g,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),l(g,{prop:"category",label:"类别","min-width":"90",align:"center"},{default:o(a=>[l(ke,null,{default:o(()=>[b(T(a.row.category||"未归类"),1)]),_:2},1024)]),_:1}),l(g,{label:"预览","min-width":"150",align:"center"},{default:o(a=>[C(a.row.fileType)?(s(),f(te,{key:0,src:a.row.url,"preview-src-list":h.value.filter(p=>C(p.fileType)).map(p=>p.url),"initial-index":Z(a.row),fit:"contain",class:"preview-image","preview-teleported":"","hide-on-click-modal":"",onShow:ee,onClose:le,ref:"imageRef"},null,8,["src","preview-src-list","initial-index"])):(s(),f(i,{key:1,size:40,class:"file-icon"},{default:o(()=>[a.row.fileType==="pdf"?(s(),f(c(ie),{key:0})):a.row.fileType==="doc"||a.row.fileType==="docx"?(s(),f(c(se),{key:1})):a.row.fileType==="zip"||a.row.fileType==="rar"?(s(),f(c(re),{key:2})):(s(),f(c(de),{key:3}))]),_:2},1024))]),_:1}),l(g,{prop:"filename",label:"文件名","min-width":"180",align:"center"}),l(g,{prop:"fileSize",label:"文件大小","min-width":"100",align:"center"},{default:o(a=>[b(T(q(a.row.fileSize)),1)]),_:1}),l(g,{prop:"width",label:"宽度","min-width":"80",align:"center"}),l(g,{prop:"height",label:"高度","min-width":"80",align:"center"}),l(g,{prop:"fileType",label:"图片类型","min-width":"90",align:"center"}),l(g,{prop:"storageEngine",label:"存储引擎","min-width":"90",align:"center"}),l(g,{prop:"mimeType",label:"Mime类型","min-width":"120",align:"center"}),l(g,{prop:"uploadTime",label:"创建日期",width:"180",align:"center",sortable:""}),l(g,{label:"操作",width:"150",fixed:"right",align:"center"},{default:o(a=>[d("div",Vl,[l(r,{class:"operation-button icon-only",size:"small",type:"default",onClick:p=>P(a.row)},{default:o(()=>[l(i,null,{default:o(()=>[l(c(J))]),_:1})]),_:2},1032,["onClick"]),l(r,{type:"danger",size:"small",onClick:p=>Y(a.row),class:"operation-button icon-only"},{default:o(()=>[l(i,null,{default:o(()=>[l(c(H))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[De,V.value]])])]),_:1})):(s(),f(ae,{key:1,class:"grid-card"},{default:o(()=>[d("div",Cl,[l(Te,{gutter:16},{default:o(()=>[(s(!0),z(B,null,O(h.value,a=>(s(),f(Ce,{xs:12,sm:8,md:6,lg:4,key:a.id,class:"grid-item-col"},{default:o(()=>[d("div",Tl,[d("div",xl,[l(Ve,{modelValue:a.selected,"onUpdate:modelValue":p=>a.selected=p,onChange:ve},null,8,["modelValue","onUpdate:modelValue"])]),d("div",Sl,[C(a.fileType)?(s(),f(te,{key:0,src:a.url,"preview-src-list":h.value.filter(p=>C(p.fileType)).map(p=>p.url),"initial-index":Z(a),fit:"cover",class:"grid-preview-image","preview-teleported":"","hide-on-click-modal":"",onShow:ee,onClose:le,ref_for:!0,ref:"gridImageRef"},null,8,["src","preview-src-list","initial-index"])):(s(),f(i,{key:1,size:50,class:"grid-file-icon"},{default:o(()=>[a.fileType==="pdf"?(s(),f(c(ie),{key:0})):a.fileType==="doc"||a.fileType==="docx"?(s(),f(c(se),{key:1})):a.fileType==="zip"||a.fileType==="rar"?(s(),f(c(re),{key:2})):(s(),f(c(de),{key:3}))]),_:2},1024))]),d("div",zl,[d("div",Dl,T(a.filename),1),d("div",Ul,[d("span",null,T(q(a.fileSize)),1),d("span",null,T(a.fileType),1)])]),d("div",Il,[l(r,{class:"operation-button icon-only",size:"small",type:"default",onClick:p=>P(a)},{default:o(()=>[l(i,null,{default:o(()=>[l(c(J))]),_:1})]),_:2},1032,["onClick"]),l(r,{type:"danger",size:"small",onClick:p=>Y(a),class:"operation-button icon-only"},{default:o(()=>[l(i,null,{default:o(()=>[l(c(H))]),_:1})]),_:2},1032,["onClick"])])])]),_:2},1024))),128))]),_:1})])]),_:1})),d("div",$l,[l(xe,{"current-page":x.value,"onUpdate:currentPage":e[3]||(e[3]=a=>x.value=a),"page-size":D.value,"onUpdate:pageSize":e[4]||(e[4]=a=>D.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:A.value,onSizeChange:W,onCurrentChange:fe,"pager-count":7,background:""},{sizes:o(()=>[l(oe,{"model-value":D.value,onChange:W,class:"custom-page-size"},{default:o(()=>[(s(),z(B,null,O([10,20,50,100],a=>l(L,{key:a,value:a,label:`${a}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),l(ne,{modelValue:k.value,"onUpdate:modelValue":e[16]||(e[16]=a=>k.value=a),title:n.id?"编辑附件":"上传附件",width:"600px",center:"","close-on-click-modal":!1},{footer:o(()=>[d("span",Ll,[l(r,{type:"primary",onClick:ye},{default:o(()=>e[26]||(e[26]=[b("确定")])),_:1}),l(r,{onClick:e[15]||(e[15]=a=>k.value=!1)},{default:o(()=>e[27]||(e[27]=[b("取消")])),_:1})])]),default:o(()=>[l(ze,{ref:"editFormRef",model:n,"label-width":"120px","label-position":"right"},{default:o(()=>[n.id?(s(),f(_,{key:1,label:"文件ID"},{default:o(()=>[l(u,{modelValue:n.id,"onUpdate:modelValue":e[5]||(e[5]=a=>n.id=a),disabled:""},null,8,["modelValue"])]),_:1})):(s(),f(_,{key:0,label:"选择文件",prop:"file"},{default:o(()=>[l(Se,{class:"attachment-uploader",action:"#","auto-upload":!1,"on-change":be,limit:1,"file-list":I.value},{tip:o(()=>e[25]||(e[25]=[d("div",{class:"el-upload__tip"},"支持各种类型文件，图片、文档、压缩包等",-1)])),default:o(()=>[l(r,{type:"primary"},{default:o(()=>e[24]||(e[24]=[b("选择文件")])),_:1})]),_:1},8,["file-list"])]),_:1})),l(_,{label:"类别"},{default:o(()=>[l(oe,{modelValue:n.category,"onUpdate:modelValue":e[6]||(e[6]=a=>n.category=a),placeholder:"请选择类别",style:{width:"100%"}},{default:o(()=>[l(L,{label:"未归类",value:""}),l(L,{label:"自定义",value:"自定义"}),l(L,{label:"用户上传",value:"用户上传"}),l(L,{label:"系统文件",value:"系统文件"})]),_:1},8,["modelValue"])]),_:1}),l(_,{label:"文件名"},{default:o(()=>[l(u,{modelValue:n.filename,"onUpdate:modelValue":e[7]||(e[7]=a=>n.filename=a)},null,8,["modelValue"])]),_:1}),n.id?(s(),z(B,{key:2},[l(_,{label:"物理路径"},{default:o(()=>[l(u,{modelValue:n.filePath,"onUpdate:modelValue":e[8]||(e[8]=a=>n.filePath=a),disabled:""},null,8,["modelValue"])]),_:1}),l(_,{label:"文件大小"},{default:o(()=>[l(u,{value:q(n.fileSize),disabled:""},null,8,["value"])]),_:1}),l(_,{label:"Mime类型"},{default:o(()=>[l(u,{modelValue:n.mimeType,"onUpdate:modelValue":e[9]||(e[9]=a=>n.mimeType=a),disabled:""},null,8,["modelValue"])]),_:1}),l(_,{label:"存储引擎"},{default:o(()=>[l(u,{modelValue:n.storageEngine,"onUpdate:modelValue":e[10]||(e[10]=a=>n.storageEngine=a),disabled:""},null,8,["modelValue"])]),_:1}),l(_,{label:"上传时间"},{default:o(()=>[l(u,{modelValue:n.uploadTime,"onUpdate:modelValue":e[11]||(e[11]=a=>n.uploadTime=a),disabled:""},null,8,["modelValue"])]),_:1}),C(n.fileType)?(s(),z(B,{key:0},[l(_,{label:"宽度"},{default:o(()=>[l(u,{modelValue:n.width,"onUpdate:modelValue":e[12]||(e[12]=a=>n.width=a),disabled:""},null,8,["modelValue"])]),_:1}),l(_,{label:"高度"},{default:o(()=>[l(u,{modelValue:n.height,"onUpdate:modelValue":e[13]||(e[13]=a=>n.height=a),disabled:""},null,8,["modelValue"])]),_:1})],64)):R("",!0)],64)):R("",!0),l(_,{label:"透传数据"},{default:o(()=>[l(u,{modelValue:n.metadata,"onUpdate:modelValue":e[14]||(e[14]=a=>n.metadata=a),type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l(ne,{modelValue:S.value,"onUpdate:modelValue":e[18]||(e[18]=a=>S.value=a),title:"删除确认",width:"400px",center:"","close-on-click-modal":!0},{footer:o(()=>[d("span",Bl,[l(r,{type:"danger",onClick:he},{default:o(()=>e[28]||(e[28]=[b("删除")])),_:1}),l(r,{onClick:e[17]||(e[17]=a=>S.value=!1)},{default:o(()=>e[29]||(e[29]=[b("取消")])),_:1})])]),default:o(()=>[d("span",null,"确认要删除选中的"+T(y.value.length)+"个附件吗？此操作无法撤销。",1)]),_:1},8,["modelValue"])])}}}),ot=dl(Al,[["__scopeId","data-v-2305b9d1"]]);export{ot as default};
