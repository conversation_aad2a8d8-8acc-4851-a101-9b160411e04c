/**
 * KB支付订单状态检查定时任务
 */
const { Deposit, User, Transaction } = require('../models');
const { Op } = require('sequelize');
const { getKBPayService } = require('../services/kbPayService');
const balanceService = require('../services/balanceService');
const commissionService = require('../services/commissionService');
const sequelize = require('../config/database');

/**
 * 检查KB支付未完成订单
 * @returns {Promise<Object>} 检查结果
 */
async function checkKBPayOrders() {
  console.log('开始检查KB支付未完成订单...');

  try {
    // 1. 处理24小时内的pending订单（原有逻辑）
    await processRecentPendingOrders();

    // 2. 处理超过24小时的pending订单（新增逻辑）
    await processTimeoutOrders();

    return {
      success: true,
      message: 'KB支付订单检查完成'
    };
  } catch (error) {
    console.error('检查KB支付订单时出错:', error);
    return {
      success: false,
      message: error.message
    };
  }
}

/**
 * 处理24小时内的pending订单（原有逻辑）
 */
async function processRecentPendingOrders() {
  // 查找24小时内的KB支付待支付订单
  const pendingOrders = await Deposit.findAll({
    where: {
      payment_method: 'kbpay',
      status: 'pending',
      created_at: {
        // 只检查24小时内的订单
        [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000)
      }
    },
    include: [
      {
        model: User,
        as: 'user'
      }
    ]
  });

  console.log(`找到 ${pendingOrders.length} 个24小时内的待处理KB支付订单`);

  if (pendingOrders.length === 0) {
    return;
  }

  // 获取KB支付服务
  const kbPayService = await getKBPayService();

  // 处理每个订单
  let processed = 0;
  for (const order of pendingOrders) {
    try {
      console.log(`检查订单 ${order.order_number}...`);

      // 查询订单状态
      const queryResult = await kbPayService.queryOrder(order.order_number);

      if (!queryResult.success) {
        console.log(`订单 ${order.order_number} 查询失败: ${queryResult.message}`);
        continue;
      }

      const orderData = queryResult.data;

      // 检查支付状态
      if (parseInt(orderData.trade_status) === 1) {
        console.log(`订单 ${order.order_number} 已支付，更新状态...`);

        // 开启事务
        const transaction = await sequelize.transaction();

        try {
          // 更新订单状态
          order.status = 'completed';
          order.actual_amount = parseFloat(orderData.trade_amount);
          order.payment_time = new Date();
          order.completion_time = new Date();
          order.remark = `KB支付成功，平台订单号: ${orderData.trade_no}`;

          // 使用余额服务增加用户充值账户余额
          const result = await balanceService.adjustBalance(
            order.user_id,
            'deposit', // 充值账户
            order.actual_amount,
            'add',
            'deposit', // 交易类型为充值
            `充值 ${order.actual_amount}`,
            order.id,
            'deposit',
            transaction
          );

          // 更新充值订单关联的交易ID
          order.transaction_id = result.transactionId;

          // 处理充值返佣
          await commissionService.processDepositCommission(
            order.user_id,
            parseFloat(order.actual_amount),
            result.transactionId,
            transaction
          );

          await order.save({ transaction });

          // 提交事务
          await transaction.commit();

          // 触发统计数据更新（异步执行，不影响主流程）
          try {
            const statsUpdateService = require('../services/statsUpdateService');
            statsUpdateService.triggerFullStatsUpdate().catch(err => {
              console.error(`订单 ${order.order_number} 处理后更新统计数据失败:`, err);
            });
          } catch (error) {
            console.error('触发统计数据更新失败:', error);
          }

          processed++;
          console.log(`订单 ${order.order_number} 处理成功`);
        } catch (error) {
          // 回滚事务
          await transaction.rollback();
          console.error(`订单 ${order.order_number} 处理失败:`, error);
        }
      } else {
        console.log(`订单 ${order.order_number} 支付状态: ${orderData.trade_status}，暂不处理`);
      }
    } catch (error) {
      console.error(`处理订单 ${order.order_number} 时出错:`, error);
    }
  }
}

/**
 * 处理超过24小时的pending订单（新增逻辑）
 */
async function processTimeoutOrders() {
  // 查找超过24小时的KB支付待支付订单
  const timeoutOrders = await Deposit.findAll({
    where: {
      payment_method: 'kbpay',
      status: 'pending',
      created_at: {
        // 查找超过24小时的订单
        [Op.lt]: new Date(Date.now() - 24 * 60 * 60 * 1000)
      }
    },
    include: [
      {
        model: User,
        as: 'user'
      }
    ]
  });

  console.log(`找到 ${timeoutOrders.length} 个超过24小时的待处理KB支付订单`);

  if (timeoutOrders.length === 0) {
    return;
  }

  // 获取KB支付服务
  const kbPayService = await getKBPayService();

  // 处理每个超时订单
  let processed = 0;
  for (const order of timeoutOrders) {
    try {
      console.log(`处理超时订单 ${order.order_number}...`);

      // 最后一次查询确认订单状态
      const queryResult = await kbPayService.queryOrder(order.order_number);

      if (queryResult.success && parseInt(queryResult.data.trade_status) === 1) {
        // 如果查询发现已支付，按成功处理
        console.log(`超时订单 ${order.order_number} 发现已支付，按成功处理...`);
        await processSuccessfulOrder(order, queryResult.data);
        processed++;
      } else {
        // 确认未支付，标记为取消
        console.log(`超时订单 ${order.order_number} 确认未支付，标记为取消...`);
        await markOrderAsFailed(order);
        processed++;
      }
    } catch (error) {
      console.error(`处理超时订单 ${order.order_number} 时出错:`, error);
    }
  }

  console.log(`成功处理 ${processed} 个超时KB支付订单（标记为取消状态）`);
}

/**
 * 处理成功支付的订单
 */
async function processSuccessfulOrder(order, orderData) {
  const transaction = await sequelize.transaction();

  try {
    // 更新订单状态
    order.status = 'completed';
    order.actual_amount = parseFloat(orderData.trade_amount);
    order.payment_time = new Date();
    order.completion_time = new Date();
    order.remark = `KB支付成功，平台订单号: ${orderData.trade_no}`;

    // 使用余额服务增加用户充值账户余额
    const result = await balanceService.adjustBalance(
      order.user_id,
      'deposit', // 充值账户
      order.actual_amount,
      'add',
      'deposit', // 交易类型为充值
      `充值 ${order.actual_amount}`,
      order.id,
      'deposit',
      transaction
    );

    // 更新充值订单关联的交易ID
    order.transaction_id = result.transactionId;

    // 处理充值返佣
    await commissionService.processDepositCommission(
      order.user_id,
      parseFloat(order.actual_amount),
      result.transactionId,
      transaction
    );

    await order.save({ transaction });

    // 提交事务
    await transaction.commit();

    // 触发统计数据更新（异步执行，不影响主流程）
    try {
      const statsUpdateService = require('../services/statsUpdateService');
      statsUpdateService.triggerFullStatsUpdate().catch(err => {
        console.error(`订单 ${order.order_number} 处理后更新统计数据失败:`, err);
      });
    } catch (error) {
      console.error('触发统计数据更新失败:', error);
    }

    console.log(`订单 ${order.order_number} 成功处理`);
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    throw error;
  }
}

/**
 * 标记订单为取消状态
 */
async function markOrderAsFailed(order) {
  try {
    // 更新订单状态为已取消
    order.status = 'cancelled';
    order.remark = '订单超时未支付';
    order.updated_at = new Date();

    await order.save();

    console.log(`订单 ${order.order_number} 已标记为取消`);
  } catch (error) {
    console.error(`标记订单 ${order.order_number} 为取消时出错:`, error);
    throw error;
  }
}

module.exports = {
  checkKBPayOrders
};
