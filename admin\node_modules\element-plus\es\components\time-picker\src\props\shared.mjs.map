{"version": 3, "file": "shared.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/props/shared.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\nimport type {\n  GetDisabledHours,\n  GetDisabledMinutes,\n  GetDisabledSeconds,\n} from '../common/props'\n\nexport const disabledTimeListsProps = buildProps({\n  /**\n   * @description To specify the array of hours that cannot be selected\n   */\n  disabledHours: {\n    type: definePropType<GetDisabledHours>(Function),\n  },\n  /**\n   * @description To specify the array of minutes that cannot be selected\n   */\n  disabledMinutes: {\n    type: definePropType<GetDisabledMinutes>(Function),\n  },\n  /**\n   * @description To specify the array of seconds that cannot be selected\n   */\n  disabledSeconds: {\n    type: definePropType<GetDisabledSeconds>(Function),\n  },\n} as const)\n\nexport type DisabledTimeListsProps = ExtractPropTypes<\n  typeof disabledTimeListsProps\n>\n\nexport const timePanelSharedProps = buildProps({\n  visible: Boolean,\n  actualVisible: {\n    type: Boolean,\n    default: undefined,\n  },\n  format: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type TimePanelSharedProps = ExtractPropTypes<typeof timePanelSharedProps>\n"], "names": [], "mappings": ";;AACY,MAAC,sBAAsB,GAAG,UAAU,CAAC;AACjD,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}