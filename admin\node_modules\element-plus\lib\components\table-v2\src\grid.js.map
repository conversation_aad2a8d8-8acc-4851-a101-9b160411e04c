{"version": 3, "file": "grid.js", "sources": ["../../../../../../packages/components/table-v2/src/grid.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport {\n  virtualizedGridProps,\n  virtualizedListProps,\n} from '@element-plus/components/virtual-list'\nimport {\n  classType,\n  columns,\n  dataType,\n  fixedDataType,\n  requiredNumber,\n  styleType,\n} from './common'\nimport { tableV2HeaderProps } from './header'\nimport { tableV2RowProps } from './row'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { ItemSize } from '@element-plus/components/virtual-list'\n\nexport type onRowRenderedParams = {\n  rowCacheStart: number\n  rowCacheEnd: number\n  rowVisibleStart: number\n  rowVisibleEnd: number\n}\n\nexport const tableV2GridProps = buildProps({\n  columns,\n  data: dataType,\n  fixedData: fixedDataType,\n  estimatedRowHeight: tableV2RowProps.estimatedRowHeight,\n\n  /**\n   * Size related attributes\n   */\n  width: requiredNumber,\n  height: requiredNumber,\n\n  headerWidth: requiredNumber,\n  headerHeight: tableV2HeaderProps.headerHeight,\n\n  bodyWidth: requiredNumber,\n  rowHeight: requiredNumber,\n\n  /**\n   * Special attributes\n   */\n  cache: virtualizedListProps.cache,\n  useIsScrolling: Boolean,\n  scrollbarAlwaysOn: virtualizedGridProps.scrollbarAlwaysOn,\n  scrollbarStartGap: virtualizedGridProps.scrollbarStartGap,\n  scrollbarEndGap: virtualizedGridProps.scrollbarEndGap,\n\n  /**\n   * CSS attributes\n   */\n  class: classType,\n  style: styleType,\n  containerStyle: styleType,\n\n  getRowHeight: {\n    type: definePropType<ItemSize>(Function),\n    required: true,\n  },\n  rowKey: tableV2RowProps.rowKey,\n\n  /**\n   * Event handlers\n   */\n  onRowsRendered: {\n    type: definePropType<(params: onRowRenderedParams) => void>(Function),\n  },\n  onScroll: {\n    type: definePropType<(...args: any[]) => void>(Function),\n  },\n} as const)\n\nexport type TableV2GridProps = ExtractPropTypes<typeof tableV2GridProps>\n"], "names": ["buildProps", "columns", "dataType", "fixedDataType", "tableV2RowProps", "requiredNumber", "tableV2HeaderProps", "virtualizedListProps", "virtualizedGridProps", "classType", "styleType", "definePropType"], "mappings": ";;;;;;;;;;AAeY,MAAC,gBAAgB,GAAGA,kBAAU,CAAC;AAC3C,WAAEC,cAAO;AACT,EAAE,IAAI,EAAEC,eAAQ;AAChB,EAAE,SAAS,EAAEC,oBAAa;AAC1B,EAAE,kBAAkB,EAAEC,mBAAe,CAAC,kBAAkB;AACxD,EAAE,KAAK,EAAEC,qBAAc;AACvB,EAAE,MAAM,EAAEA,qBAAc;AACxB,EAAE,WAAW,EAAEA,qBAAc;AAC7B,EAAE,YAAY,EAAEC,yBAAkB,CAAC,YAAY;AAC/C,EAAE,SAAS,EAAED,qBAAc;AAC3B,EAAE,SAAS,EAAEA,qBAAc;AAC3B,EAAE,KAAK,EAAEE,0BAAoB,CAAC,KAAK;AACnC,EAAE,cAAc,EAAE,OAAO;AACzB,EAAE,iBAAiB,EAAEC,0BAAoB,CAAC,iBAAiB;AAC3D,EAAE,iBAAiB,EAAEA,0BAAoB,CAAC,iBAAiB;AAC3D,EAAE,eAAe,EAAEA,0BAAoB,CAAC,eAAe;AACvD,EAAE,KAAK,EAAEC,gBAAS;AAClB,EAAE,KAAK,EAAEC,gBAAS;AAClB,EAAE,cAAc,EAAEA,gBAAS;AAC3B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEC,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,MAAM,EAAEP,mBAAe,CAAC,MAAM;AAChC,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAEO,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC;;;;"}