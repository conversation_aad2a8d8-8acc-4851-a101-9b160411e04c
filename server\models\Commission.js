const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Commission = sequelize.define('Commission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '接收佣金的用户ID',
  },
  from_user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '产生佣金的用户ID',
  },
  investment_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '投资ID',
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '佣金级别：1=一级, 2=二级, 3=三级',
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '佣金金额',
  },
  rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    comment: '佣金比例(%)',
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态：pending=待发放, paid=已发放',
  },
  transaction_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '交易ID',
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '佣金类型',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'commissions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Commission.associate = (models) => {
  // 佣金与接收用户
  Commission.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });

  // 佣金与来源用户
  Commission.belongsTo(models.User, {
    foreignKey: 'from_user_id',
    as: 'from_user',
  });

  // 佣金与投资
  Commission.belongsTo(models.Investment, {
    foreignKey: 'investment_id',
    as: 'investment',
  });

  // 佣金与交易
  Commission.belongsTo(models.Transaction, {
    foreignKey: 'transaction_id',
    as: 'transaction',
  });
};

module.exports = Commission;
