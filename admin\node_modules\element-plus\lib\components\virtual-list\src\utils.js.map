{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/virtual-list/src/utils.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  BACKWARD,\n  FORWARD,\n  HORIZONTAL,\n  LTR,\n  PageKey,\n  RTL,\n  RTL_OFFSET_NAG,\n  RTL_OFFSET_POS_ASC,\n  RTL_OFFSET_POS_DESC,\n} from './defaults'\n\nimport type { CSSProperties } from 'vue'\nimport type { Direction, LayoutDirection, RTLOffsetType } from './types'\n\nexport const getScrollDir = (prev: number, cur: number) =>\n  prev < cur ? FORWARD : BACKWARD\n\nexport const isHorizontal = (dir: string) =>\n  dir === LTR || dir === RTL || dir === HORIZONTAL\n\nexport const isRTL = (dir: Direction) => dir === RTL\n\nlet cachedRTLResult: RTLOffsetType | null = null\n\nexport function getRTLOffsetType(recalculate = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div')\n    const outerStyle = outerDiv.style\n    outerStyle.width = '50px'\n    outerStyle.height = '50px'\n    outerStyle.overflow = 'scroll'\n    outerStyle.direction = 'rtl'\n\n    const innerDiv = document.createElement('div')\n    const innerStyle = innerDiv.style\n    innerStyle.width = '100px'\n    innerStyle.height = '100px'\n\n    outerDiv.appendChild(innerDiv)\n\n    document.body.appendChild(outerDiv)\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = RTL_OFFSET_POS_DESC\n    } else {\n      outerDiv.scrollLeft = 1\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = RTL_OFFSET_NAG\n      } else {\n        cachedRTLResult = RTL_OFFSET_POS_ASC\n      }\n    }\n\n    document.body.removeChild(outerDiv)\n\n    return cachedRTLResult\n  }\n\n  return cachedRTLResult\n}\n\nexport const getRelativePos = (\n  e: TouchEvent | MouseEvent,\n  layout: LayoutDirection\n) => {\n  return 'touches' in e ? e.touches[0][PageKey[layout]] : e[PageKey[layout]]\n}\n\ntype RenderThumbStyleParams = {\n  bar: {\n    size: 'height' | 'width'\n    axis: 'X' | 'Y'\n  }\n  size: string\n  move: number\n}\n\nexport function renderThumbStyle(\n  { move, size, bar }: RenderThumbStyleParams,\n  layout: string\n) {\n  const style: CSSProperties = {}\n  const translate = `translate${bar.axis}(${move}px)`\n\n  style[bar.size] = size\n  style.transform = translate\n  style.msTransform = translate\n  // polyfill\n  ;(style as any).webkitTransform = translate\n\n  if (layout === 'horizontal') {\n    style.height = '100%'\n  } else {\n    style.width = '100%'\n  }\n\n  return style\n}\n"], "names": ["FORWARD", "BACKWARD", "LTR", "RTL", "HORIZONTAL", "RTL_OFFSET_POS_DESC", "RTL_OFFSET_NAG", "RTL_OFFSET_POS_ASC", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAWY,MAAC,YAAY,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,GAAGA,gBAAO,GAAGC,kBAAS;AAC/D,MAAC,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,KAAKC,YAAG,IAAI,GAAG,KAAKC,YAAG,IAAI,GAAG,KAAKC,oBAAW;AAC1E,MAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,KAAKD,aAAI;AAC1C,IAAI,eAAe,GAAG,IAAI,CAAC;AACpB,SAAS,gBAAgB,CAAC,WAAW,GAAG,KAAK,EAAE;AACtD,EAAE,IAAI,eAAe,KAAK,IAAI,IAAI,WAAW,EAAE;AAC/C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;AACtC,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;AAC9B,IAAI,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B,IAAI,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACnC,IAAI,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC;AACjC,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC;AACtC,IAAI,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC;AAC/B,IAAI,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC,IAAI,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnC,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAI,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE;AACjC,MAAM,eAAe,GAAGE,4BAAmB,CAAC;AAC5C,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;AAC9B,MAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,CAAC,EAAE;AACrC,QAAQ,eAAe,GAAGC,uBAAc,CAAC;AACzC,OAAO,MAAM;AACb,QAAQ,eAAe,GAAGC,2BAAkB,CAAC;AAC7C,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAI,OAAO,eAAe,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,eAAe,CAAC;AACzB,CAAC;AACW,MAAC,cAAc,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK;AAC7C,EAAE,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAACC,gBAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAACA,gBAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7E,EAAE;AACK,SAAS,gBAAgB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE;AAC9D,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB,EAAE,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACtD,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACzB,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAC9B,EAAE,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC;AAChC,EAAE,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;AACpC,EAAE,IAAI,MAAM,KAAK,YAAY,EAAE;AAC/B,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC1B,GAAG,MAAM;AACT,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;AACzB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf;;;;;;;;;"}