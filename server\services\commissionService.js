/**
 * 佣金服务
 * 处理充值返佣、收益返佣等佣金相关业务逻辑
 */
const { User, UserRelation, Commission, Transaction, SystemParam, Investment, InvestmentProfit } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const balanceService = require('./balanceService');

/**
 * 处理充值返佣
 * @param {number} userId - 充值用户ID
 * @param {number} amount - 充值金额
 * @param {number} transactionId - 充值交易ID
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} - 返回处理结果
 */
exports.processDepositCommission = async (userId, amount, transactionId, transaction = null) => {
  try {
    // 创建新事务或使用传入的事务
    const t = transaction || await sequelize.transaction();

    try {
      // 获取用户的上级关系
      const userRelations = await UserRelation.findAll({
        where: {
          user_id: userId
        },
        order: [['level', 'ASC']], // 按层级排序
        transaction: t
      });

      if (!userRelations || userRelations.length === 0) {
        // 如果没有上级关系，直接返回
        if (!transaction) await t.commit();
        return {
          success: true,
          message: '用户没有上级关系，无需分配佣金',
          commissions: []
        };
      }

      // 获取充值返佣比例设置
      const commissionRates = await getCommissionRates(t);

      // 记录已处理的佣金
      const processedCommissions = [];

      // 为每个上级分配佣金
      for (const relation of userRelations) {
        const level = relation.level;
        const parentId = relation.parent_id;

        // 获取对应层级的返佣比例
        const rateKey = `[site.recharge_commission_rate_${level}]`;
        const rate = commissionRates[rateKey] || 0;

        // 如果返佣比例为0，跳过
        if (rate <= 0) continue;

        // 计算佣金金额
        const commissionAmount = (amount * rate / 100).toFixed(2);

        // 如果佣金金额为0，跳过
        if (parseFloat(commissionAmount) <= 0) continue;

        // 创建佣金记录
        const commission = await Commission.create({
          user_id: parentId,
          from_user_id: userId,
          investment_id: null, // 充值返佣没有关联的投资ID
          level,
          amount: commissionAmount,
          rate,
          status: 'pending', // 初始状态为待发放
          transaction_id: null, // 先设为null，发放后再更新
          type: `${level}级充值返佣` // 佣金类型
        }, { transaction: t });

        // 将佣金添加到用户的收入账户
        const result = await balanceService.adjustBalance(
          parentId,
          'income', // 佣金添加到收入账户
          commissionAmount,
          'add',
          'commission', // 交易类型为佣金
          `${level}级充值返佣 ${commissionAmount}`,
          null,
          'commission',
          t
        );

        // 更新佣金记录状态和关联的交易ID
        commission.status = 'paid';
        commission.transaction_id = result.transactionId;
        await commission.save({ transaction: t });

        // 添加到已处理佣金列表
        processedCommissions.push({
          id: commission.id,
          user_id: parentId,
          from_user_id: userId,
          level,
          amount: commissionAmount,
          rate,
          status: 'paid',
          transaction_id: result.transactionId,
          type: `${level}级充值返佣`
        });
      }

      // 如果使用的是新创建的事务，则提交事务
      if (!transaction) await t.commit();

      return {
        success: true,
        message: '佣金分配成功',
        commissions: processedCommissions
      };
    } catch (error) {
      // 如果使用的是新创建的事务，则回滚事务
      if (!transaction) await t.rollback();
      throw error;
    }
  } catch (error) {
    console.error('处理充值返佣错误:', error);
    throw error;
  }
};

/**
 * 处理收益返佣（仅下级用户第一个产品产生收益时触发）
 * @param {number} userId - 用户ID
 * @param {number} amount - 收益金额
 * @param {number} transactionId - 收益交易ID
 * @param {number} investmentId - 投资记录ID
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} - 返回处理结果
 */
exports.processIncomeCommission = async (userId, amount, transactionId, investmentId, transaction = null) => {
  try {
    // 获取当前投资记录
    const currentInvestment = await Investment.findByPk(investmentId, { transaction });
    if (!currentInvestment) {
      return {
        success: false,
        message: '投资记录不存在',
        commissions: []
      };
    }

    // 检查这是否是用户的第一个产品
    const userInvestments = await Investment.findAll({
      where: {
        user_id: userId,
        id: { [Op.ne]: investmentId } // 排除当前投资记录
      },
      order: [['created_at', 'ASC']], // 按创建时间排序
      transaction
    });

    // 如果用户有其他投资记录，且当前投资不是用户的第一个投资，则不返佣
    if (userInvestments.length > 0 &&
        userInvestments[0].created_at < currentInvestment.created_at) {
      return {
        success: true,
        message: '非用户第一个产品的收益，不分配佣金',
        commissions: []
      };
    }

    // 获取收益返佣比例设置
    const commissionRates = await getIncomeCommissionRates(transaction);

    // 记录已处理的佣金
    const processedCommissions = [];

    // 递归处理所有层级的上级关系
    // 传入原始用户ID作为最初产生收益的用户
    await processAllLevelCommissionsNoTransaction(userId, amount, investmentId, 1, commissionRates, processedCommissions, transaction, userId);

    return {
      success: true,
      message: '佣金分配成功',
      commissions: processedCommissions
    };
  } catch (error) {
    console.error('处理收益返佣错误:', error);
    // 返回错误但不抛出异常，避免中断收益发放流程
    return {
      success: false,
      message: `处理收益返佣错误: ${error.message}`,
      commissions: []
    };
  }
};

/**
 * 递归处理所有层级的上级佣金
 * @param {number} userId - 当前用户ID
 * @param {number} amount - 收益金额
 * @param {number} investmentId - 投资记录ID
 * @param {number} currentLevel - 当前处理的层级
 * @param {Object} commissionRates - 佣金比例设置
 * @param {Array} processedCommissions - 已处理的佣金列表
 * @param {Object} transaction - 事务对象
 * @param {number} originalUserId - 最初产生收益的用户ID
 * @returns {Promise<void>}
 */
async function processAllLevelCommissions(userId, amount, investmentId, currentLevel, commissionRates, processedCommissions, transaction, originalUserId) {
  // 获取当前用户的直接上级
  const parentRelation = await UserRelation.findOne({
    where: {
      user_id: userId,
      level: 1 // 只查询直接上级
    },
    transaction
  });

  // 如果没有上级，则结束递归
  if (!parentRelation) {
    return;
  }

  const parentId = parentRelation.parent_id;

  // 获取对应层级的返佣比例
  const rateKey = `[site.income_commission_rate_${currentLevel}]`;
  const rate = commissionRates[rateKey] || 0;

  // 如果返佣比例大于0，则处理佣金
  if (rate > 0) {
    // 计算佣金金额
    const commissionAmount = (amount * rate / 100).toFixed(2);

    // 如果佣金金额大于0，则创建佣金记录
    if (parseFloat(commissionAmount) > 0) {
      // 创建佣金记录
      const commission = await Commission.create({
        user_id: parentId,
        from_user_id: originalUserId, // 使用最初产生收益的用户ID
        investment_id: investmentId, // 关联投资ID
        level: currentLevel,
        amount: commissionAmount,
        rate,
        status: 'pending', // 初始状态为待发放
        transaction_id: null, // 先设为null，发放后再更新
        type: `${currentLevel}级收益返佣` // 佣金类型
      }, { transaction });

      // 将佣金添加到用户的收入账户
      const result = await balanceService.adjustBalance(
        parentId,
        'income', // 佣金添加到收入账户
        commissionAmount,
        'add',
        'commission', // 交易类型为佣金
        `${currentLevel}级收益返佣 ${commissionAmount}`,
        investmentId,
        'investment',
        transaction
      );

      // 更新佣金记录状态和关联的交易ID
      commission.status = 'paid';
      commission.transaction_id = result.transactionId;
      await commission.save({ transaction });

      // 添加到已处理佣金列表
      processedCommissions.push({
        id: commission.id,
        user_id: parentId,
        from_user_id: originalUserId, // 使用最初产生收益的用户ID
        level: currentLevel,
        amount: commissionAmount,
        rate,
        status: 'paid',
        transaction_id: result.transactionId,
        type: `${currentLevel}级收益返佣`
      });
    }
  }

  // 递归处理上级的上级，层级+1
  await processAllLevelCommissions(parentId, amount, investmentId, currentLevel + 1, commissionRates, processedCommissions, transaction, originalUserId);
}

/**
 * 递归处理所有层级的上级佣金（无事务模式）
 * @param {number} userId - 当前用户ID
 * @param {number} amount - 收益金额
 * @param {number} investmentId - 投资记录ID
 * @param {number} currentLevel - 当前处理的层级
 * @param {Object} commissionRates - 佣金比例设置
 * @param {Array} processedCommissions - 已处理的佣金列表
 * @param {Object} transaction - 事务对象（可选）
 * @param {number} originalUserId - 最初产生收益的用户ID
 * @returns {Promise<void>}
 */
async function processAllLevelCommissionsNoTransaction(userId, amount, investmentId, currentLevel, commissionRates, processedCommissions, transaction, originalUserId) {
  try {
    // 获取当前用户的直接上级
    const parentRelation = await UserRelation.findOne({
      where: {
        user_id: userId,
        level: 1 // 只查询直接上级
      },
      transaction
    });

    // 如果没有上级，则结束递归
    if (!parentRelation) {
      return;
    }

    const parentId = parentRelation.parent_id;

    // 获取对应层级的返佣比例
    const rateKey = `[site.income_commission_rate_${currentLevel}]`;
    const rate = commissionRates[rateKey] || 0;

    // 如果返佣比例大于0，则处理佣金
    if (rate > 0) {
      // 计算佣金金额
      const commissionAmount = (amount * rate / 100).toFixed(2);

      // 如果佣金金额大于0，则创建佣金记录
      if (parseFloat(commissionAmount) > 0) {
        try {
          // 创建佣金记录
          const commission = await Commission.create({
            user_id: parentId,
            from_user_id: originalUserId, // 使用最初产生收益的用户ID
            investment_id: investmentId, // 关联投资ID
            level: currentLevel,
            amount: commissionAmount,
            rate,
            status: 'pending', // 初始状态为待发放
            transaction_id: null, // 先设为null，发放后再更新
            type: `${currentLevel}级收益返佣` // 佣金类型
          }, { transaction });

          // 将佣金添加到用户的收入账户
          try {
            const result = await balanceService.adjustBalance(
              parentId,
              'income', // 佣金添加到收入账户
              commissionAmount,
              'add',
              'commission', // 交易类型为佣金
              `${currentLevel}级收益返佣 ${commissionAmount}`,
              investmentId,
              'investment',
              transaction
            );

            // 更新佣金记录状态和关联的交易ID
            try {
              commission.status = 'paid';
              commission.transaction_id = result.transactionId;
              await commission.save({ transaction });

              // 添加到已处理佣金列表
              processedCommissions.push({
                id: commission.id,
                user_id: parentId,
                from_user_id: originalUserId, // 使用最初产生收益的用户ID
                level: currentLevel,
                amount: commissionAmount,
                rate,
                status: 'paid',
                transaction_id: result.transactionId,
                type: `${currentLevel}级收益返佣`
              });
            } catch (saveError) {
              console.error(`更新佣金记录状态失败: ${saveError.message}`);
              // 继续执行，不中断流程
            }
          } catch (balanceError) {
            console.error(`调整用户余额失败: ${balanceError.message}`);
            // 继续执行，不中断流程
          }
        } catch (createError) {
          console.error(`创建佣金记录失败: ${createError.message}`);
          // 继续执行，不中断流程
        }
      }
    }

    // 递归处理上级的上级，层级+1
    try {
      await processAllLevelCommissionsNoTransaction(parentId, amount, investmentId, currentLevel + 1, commissionRates, processedCommissions, transaction, originalUserId);
    } catch (recursionError) {
      console.error(`处理上级佣金失败: ${recursionError.message}`);
      // 继续执行，不中断流程
    }
  } catch (error) {
    console.error(`处理${currentLevel}级佣金失败: ${error.message}`);
    // 捕获所有错误，不抛出异常，避免中断收益发放流程
  }
}

/**
 * 获取充值返佣比例设置
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} - 返回返佣比例设置
 */
async function getCommissionRates(transaction = null) {
  try {
    // 查询所有充值返佣比例参数
    const params = await SystemParam.findAll({
      where: {
        param_key: {
          [Op.like]: '[site.recharge_commission_rate_%'
        }
      },
      transaction
    });

    // 将参数转换为对象
    const rates = {};
    params.forEach(param => {
      rates[param.param_key] = parseFloat(param.param_value) || 0;
    });

    return rates;
  } catch (error) {
    console.error('获取充值返佣比例设置错误:', error);
    throw error;
  }
}

/**
 * 获取收益返佣比例设置
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} - 返回返佣比例设置
 */
async function getIncomeCommissionRates(transaction = null) {
  try {
    // 查询所有收益返佣比例参数
    const params = await SystemParam.findAll({
      where: {
        param_key: {
          [Op.like]: '[site.income_commission_rate_%'
        }
      },
      transaction
    });

    // 将参数转换为对象
    const rates = {};
    params.forEach(param => {
      rates[param.param_key] = parseFloat(param.param_value) || 0;
    });

    return rates;
  } catch (error) {
    console.error('获取收益返佣比例设置错误:', error);
    throw error;
  }
}
