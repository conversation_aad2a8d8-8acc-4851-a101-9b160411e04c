{"version": 3, "file": "anchor.mjs", "sources": ["../../../../../../packages/components/anchor/src/anchor.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Anchor from './anchor.vue'\n\nexport const anchorProps = buildProps({\n  /**\n   * @description scroll container\n   */\n  container: {\n    type: definePropType<string | HTMLElement | Window | null>([\n      String,\n      Object,\n    ]),\n  },\n  /**\n   * @description Set the offset of the anchor scroll\n   */\n  offset: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description The offset of the element starting to trigger the anchor\n   */\n  bound: {\n    type: Number,\n    default: 15,\n  },\n  /**\n   * @description Set the scroll duration of the container when the anchor is clicked, in milliseconds\n   */\n  duration: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description Whether to show the marker\n   */\n  marker: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Set Anchor type\n   */\n  type: {\n    type: definePropType<'default' | 'underline'>(String),\n    default: 'default',\n  },\n  /**\n   * @description Set Anchor direction\n   */\n  direction: {\n    type: definePropType<'vertical' | 'horizontal'>(String),\n    default: 'vertical',\n  },\n  /**\n   * @description Scroll whether link is selected at the top\n   */\n  selectScrollTop: {\n    type: Boolean,\n    default: false,\n  },\n})\n\nexport type AnchorProps = ExtractPropTypes<typeof anchorProps>\nexport type AnchorInstance = InstanceType<typeof Anchor> & unknown\n\nexport const anchorEmits = {\n  change: (href: string) => isString(href),\n  click: (e: MouseEvent, href?: string) =>\n    e instanceof MouseEvent && (isString(href) || isUndefined(href)),\n}\nexport type AnchorEmits = typeof anchorEmits\n"], "names": [], "mappings": ";;;;AAMY,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;AAC3B,EAAE,MAAM,EAAE,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC;AAClC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,YAAY,UAAU,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACtF;;;;"}