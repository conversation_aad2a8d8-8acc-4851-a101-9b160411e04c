<template>
  <view class="page-container">
    <!-- 顶部导航栏 (使用自定义样式) -->
    <view class="custom-header">
      <view class="back-button-wrapper">
        <back-button></back-button>
      </view>
      <text class="custom-header-title">My Investments</text>
      <view class="custom-header-placeholder"></view>
    </view>

    <!-- 投资记录列表 -->
    <view class="investment-list-wrapper">
      <scroll-view
        class="investment-list"
        scroll-y
        @scrolltolower="loadMore"
        lower-threshold="100"
      >
      <view v-if="loading && page === 1" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">Loading...</text>
      </view>

      <view v-else-if="investments.length === 0" class="empty-container">
        <image src="/static/images/empty.png" class="empty-image" mode="aspectFit"></image>
        <text class="empty-text">No investment records yet</text>
        <view class="empty-tips">
          <text class="empty-tips-text">You can select investment plans on the home page</text>
          <button class="go-home-btn" @click="goToHome">Go to Home</button>
        </view>

      </view>

      <view v-else>
        <view
          v-for="(item, index) in investments"
          :key="index"
          class="investment-item"
        >
          <view class="investment-info">
            <view class="investment-header">
              <text class="investment-name">{{ item.project && item.project.name ? item.project.name : 'Unknown Plan' }}</text>
              <text :class="['investment-status', getStatusClass(item.status)]">{{ getStatusText(item.status) }}</text>
            </view>

            <view class="investment-details">
              <view class="detail-row">
                <text class="detail-label">Investment Amount</text>
                <text class="detail-value">₱{{ formatNumber(item.amount) }}</text>
              </view>

              <view class="detail-row">
                <text class="detail-label">Return Rate</text>
                <text class="detail-value highlight">{{ item.profit_rate }}%</text>
              </view>

              <view class="detail-row">
                <text class="detail-label">Investment Date</text>
                <text class="detail-value">{{ formatDate(item.created_at) }}</text>
              </view>

              <view class="detail-row">
                <text class="detail-label">Next Earnings</text>
                <text :class="['detail-value', 'countdown', getCountdownClass(item)]">{{ getNextProfitTime(item) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading && page > 1" class="loading-more">
        <view class="loading-spinner"></view>
        <text class="loading-text">Loading more...</text>
      </view>

      <!-- 没有更多数据 -->
      <view v-if="!loading && !hasMore && investments.length > 0" class="no-more">
        <text class="no-more-text">No more data</text>
      </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import BackButton from '@/components/back-button.vue';

export default {
  components: {
    BackButton
  },
  data() {
    return {
      investments: [],
      page: 1,
      limit: 10, // 默认加载10条数据，确保有足够内容触发分页
      total: 0,
      loading: false,
      hasMore: true,
      countdownTimers: {}, // 存储每个投资项目的倒计时定时器
      countdownValues: {} // 存储每个投资项目的倒计时值
    }
  },
  onLoad() {
    // 检查用户登录状态
    const token = uni.getStorageSync('userToken');

    if (!token) {
      uni.showToast({
        title: 'Please login first',
        icon: 'none',
        duration: 2000
      });

      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/index/index'
        });
      }, 2000);
      return;
    }

    // 检查用户信息
    let userInfo = {};
    const userInfoStr = uni.getStorageSync('userInfo');

    if (userInfoStr) {
      try {
        // 尝试解析JSON字符串
        userInfo = JSON.parse(userInfoStr);
      } catch (parseError) {

        // 如果用户信息解析失败，尝试从Token中获取用户ID
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));

            // 使用Token中的用户信息
            userInfo = {
              id: payload.id,
              username: payload.username
            };

            // 更新本地存储
            uni.setStorageSync('userInfo', JSON.stringify(userInfo));
          }
        } catch (tokenError) {
        }
      }
    } else {

      // 尝试从Token中获取用户ID
      try {
        const tokenParts = token.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));

          // 使用Token中的用户信息
          userInfo = {
            id: payload.id,
            username: payload.username
          };

          // 更新本地存储
          uni.setStorageSync('userInfo', JSON.stringify(userInfo));
        }
      } catch (tokenError) {
      }
    }

    // 显示加载中
    uni.showLoading({
      title: 'Loading...',
      mask: true
    });

    // 延迟一点时间再加载数据，确保数据库事务已完成
    setTimeout(() => {
      this.loadInvestments();
    }, 500);
  },
  onShow() {
    // 导入刷新管理器
    import('../../utils/refreshManager.js').then(module => {
      const refreshManager = module.default;

      // 页面显示时的刷新机制
      if (refreshManager.canRefresh('investments')) {
        this.silentRefreshInvestments();
        refreshManager.markRefreshed('investments');
      }
    });
  },

  onUnload() {
    // 页面卸载时清除所有倒计时定时器
    this.clearAllCountdowns();
  },
  methods: {
    // 静默刷新投资数据
    async silentRefreshInvestments() {
      try {
        // 重置分页并刷新，不显示loading
        this.page = 1;
        this.investments = [];
        this.hasMore = true;
        await this.loadInvestments();
      } catch (error) {
        // 静默处理错误，不影响用户体验
      }
    },

    // 加载投资记录
    async loadInvestments() {
      if (this.loading) return;

      try {
        this.loading = true;

        // 导入API加载器
        const apiLoader = await import('../../services/api-loader.js');

        // 加载投资API模块
        let investmentApi;
        let getUserInvestments;

        try {
          // 首先尝试加载投资API模块
          investmentApi = await apiLoader.loadInvestmentApi();
          getUserInvestments = investmentApi.getUserInvestments;
        } catch (investmentError) {
          // 如果投资API模块加载失败，尝试加载项目API模块
          try {
            const projectApi = await apiLoader.loadProjectApi();
            getUserInvestments = projectApi.getUserInvestments;
          } catch (projectError) {
            throw new Error('无法加载任何API模块');
          }
        }

        if (!getUserInvestments) {
          throw new Error('无法加载投资API服务');
        }

        // 调用API获取投资记录
        const response = await getUserInvestments({
          page: this.page,
          limit: this.limit
        });

        // 隐藏加载中
        uni.hideLoading();

        if (response && response.code === 200 && response.data) {
          const { items, total } = response.data;



          // 正常处理API返回的数据
          this.total = total || 0;

          if (this.page === 1) {
            this.investments = items || [];
          } else {
            this.investments = [...this.investments, ...(items || [])];
          }

          // 简化分页逻辑：判断是否还有更多数据
          this.hasMore = this.investments.length < (total || 0);

          // 为每个投资项目启动倒计时
          this.investments.forEach(investment => {
            if (investment.status === 'active') {
              this.startCountdown(investment);
            } else {
              // 为非活跃状态的投资设置初始显示值
              const statusText = this.getStatusText(investment.status);
              this.$set(this.countdownValues, investment.id, statusText);
            }
          });
        } else {
          uni.showToast({
            title: response?.message || 'Failed to get investment records',
            icon: 'none'
          });
        }
      } catch (error) {
        // 隐藏加载中
        uni.hideLoading();

        // 检查是否是网络错误
        if (error.message && error.message.includes('network')) {
          uni.showToast({
            title: 'Network connection failed, please check network settings',
            icon: 'none',
            duration: 3000
          });
        } else if (error.message && error.message.includes('login')) {
          // 检查是否是登录错误
          uni.showToast({
            title: 'Login expired, please login again',
            icon: 'none',
            duration: 2000
          });

          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/index'
            });
          }, 2000);
        } else {
          // 其他错误
          uni.showToast({
            title: 'Failed to get investment records, please try again later',
            icon: 'none',
            duration: 2000
          });
        }
      } finally {
        this.loading = false;
      }
    },

    // 加载更多
    loadMore() {
      if (this.loading || !this.hasMore) {
        return;
      }

      this.page++;
      this.loadInvestments();
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 跳转到首页
    goToHome() {
      uni.switchTab({
        url: '/pages/home/<USER>'
      });
    },

    // 格式化数字
    formatNumber(num) {
      return parseFloat(num).toFixed(2);
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': 'Active',
        'completed': 'Completed',
        'cancelled': 'Cancelled',
        'pending': 'Pending'
      };

      return statusMap[status] || status;
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        'active': 'status-active',
        'completed': 'status-completed',
        'cancelled': 'status-cancelled',
        'pending': 'status-pending'
      };

      return classMap[status] || '';
    },

    // 计算下一次收益时间
    getNextProfitTime(investment) {
      if (!investment || !investment.id) {
        return 'Calculating...';
      }

      // 如果已经有缓存的倒计时值，直接返回
      if (this.countdownValues[investment.id]) {
        return this.countdownValues[investment.id];
      }

      // 如果投资状态不是活跃的，返回相应状态
      if (investment.status !== 'active') {
        if (investment.status === 'completed') {
          return 'Completed';
        } else if (investment.status === 'cancelled') {
          return 'Cancelled';
        } else if (investment.status === 'paused') {
          return 'Paused';
        } else if (investment.status === 'pending') {
          return 'Pending';
        } else {
          return 'Unknown Status';
        }
      }

      // 对于活跃状态的投资，返回默认值，倒计时会在数据加载时启动
      return 'Calculating...';
    },

    // 开始倒计时
    startCountdown(investment) {
      // 清除可能存在的旧定时器
      if (this.countdownTimers[investment.id]) {
        clearInterval(this.countdownTimers[investment.id]);
      }

      // 获取投资创建时间（购买成功时间）
      const createdAt = new Date(investment.created_at);

      // 获取投资开始时间（可能与创建时间不同）
      const startTime = investment.start_time ? new Date(investment.start_time) : createdAt;

      // 获取投资结束时间（如果有）
      const endTime = investment.end_time ? new Date(investment.end_time) : null;

      // 获取项目的收益时间和投资周期设置
      let profitTime = 1; // 初始化收益时间为1小时
      let profitCycle = 1; // 初始化投资周期为1天

      // 从项目数据中读取收益时间和投资周期设置
      if (investment.project) {
        // 记录项目数据，以便查看可用字段

        // 使用项目的收益时间设置（每隔多久产生一次收益）
        // 确保不使用默认值，如果项目中没有设置，则使用1小时
        profitTime = investment.project.profit_time ? parseInt(investment.project.profit_time) : 1;

        // 使用项目的投资周期设置（投资总时长）
        // 确保不使用默认值，如果项目中没有设置，则使用1天
        profitCycle = investment.project.duration ? parseInt(investment.project.duration) : 1;
      }

      // 如果投资记录中有收益周期字段，优先使用
      if (investment.profit_cycle) {
        // 投资记录中的profit_cycle是收益周期（小时），而不是投资周期（天）
        // 这里不应该覆盖profitCycle，而是应该更新profitTime
        profitTime = parseInt(investment.profit_cycle);
      }

      // 计算收益间隔的毫秒数（每隔多久产生一次收益）
      const profitIntervalMs = profitTime * 60 * 60 * 1000;

      // 计算投资周期的毫秒数（投资总时长）
      const cycleDurationMs = profitCycle * 24 * 60 * 60 * 1000;

      // 计算投资结束时间（如果没有明确设置）
      const calculatedEndTime = endTime || new Date(startTime.getTime() + cycleDurationMs);

      // 设置定时器，每秒更新一次倒计时
      this.countdownTimers[investment.id] = setInterval(() => {
        // 当前时间
        const now = new Date();

        // 检查投资是否已经结束
        if (now >= calculatedEndTime) {
          // 投资已结束，显示"Completed"
          this.$set(this.countdownValues, investment.id, 'Completed');

          // 清除定时器
          clearInterval(this.countdownTimers[investment.id]);
          delete this.countdownTimers[investment.id];
          return;
        }

        // 计算从开始时间到现在已经过去了多少个完整的收益周期
        const elapsedMs = now.getTime() - startTime.getTime();
        const completedProfitCycles = Math.floor(elapsedMs / profitIntervalMs);

        // 计算下一次收益的时间点
        const nextProfitTime = new Date(startTime.getTime() + (completedProfitCycles + 1) * profitIntervalMs);

        // 计算距离下一次收益的剩余时间
        const remainingToProfitMs = nextProfitTime.getTime() - now.getTime();

        // 计算距离投资结束的剩余时间
        const remainingToCycleEndMs = calculatedEndTime.getTime() - now.getTime();

        // 如果距离投资结束的时间小于距离下一次收益的时间，显示投资结束倒计时
        if (remainingToCycleEndMs < remainingToProfitMs) {
          // 计算剩余的天、小时、分钟和秒数
          const remainingDays = Math.floor(remainingToCycleEndMs / (24 * 60 * 60 * 1000));
          const remainingHours = Math.floor((remainingToCycleEndMs % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
          const remainingMinutes = Math.floor((remainingToCycleEndMs % (60 * 60 * 1000)) / (60 * 1000));
          const remainingSeconds = Math.floor((remainingToCycleEndMs % (60 * 1000)) / 1000);

          // 格式化剩余时间
          let formattedTime = '';

          if (remainingDays > 0) {
            // 如果有天数，显示"Cycle ends in X days HH:MM:SS"
            formattedTime = `Cycle ends in ${remainingDays}d ${String(remainingHours).padStart(2, '0')}:${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
          } else {
            // 否则只显示"Cycle ends in HH:MM:SS"
            formattedTime = `Cycle ends in ${String(remainingHours).padStart(2, '0')}:${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
          }

          // 更新倒计时值
          this.$set(this.countdownValues, investment.id, formattedTime);
        } else {
          // 显示距离下一次收益的倒计时
          // 计算剩余的天、小时、分钟和秒数
          const remainingDays = Math.floor(remainingToProfitMs / (24 * 60 * 60 * 1000));
          const remainingHours = Math.floor((remainingToProfitMs % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
          const remainingMinutes = Math.floor((remainingToProfitMs % (60 * 60 * 1000)) / (60 * 1000));
          const remainingSeconds = Math.floor((remainingToProfitMs % (60 * 1000)) / 1000);

          // 格式化剩余时间
          let formattedTime = '';

          if (remainingDays > 0) {
            // 如果有天数，显示"X days HH:MM:SS"
            formattedTime = `${remainingDays}d ${String(remainingHours).padStart(2, '0')}:${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
          } else {
            // 否则只显示"HH:MM:SS"
            formattedTime = `${String(remainingHours).padStart(2, '0')}:${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
          }

          // 更新倒计时值
          this.$set(this.countdownValues, investment.id, formattedTime);
        }
      }, 1000);
    },

    // 清除所有倒计时定时器
    clearAllCountdowns() {
      Object.keys(this.countdownTimers).forEach(id => {
        clearInterval(this.countdownTimers[id]);
      });
      this.countdownTimers = {};
    },

    // 获取倒计时样式类
    getCountdownClass(investment) {
      if (!investment || !investment.id) {
        return '';
      }

      // 如果投资状态不是活跃的，返回相应样式类
      if (investment.status !== 'active') {
        if (investment.status === 'completed') {
          return 'countdown-completed';
        } else if (investment.status === 'cancelled') {
          return 'countdown-cancelled';
        } else if (investment.status === 'paused') {
          return 'countdown-paused';
        } else {
          return '';
        }
      }

      // 如果没有倒计时值，返回空
      if (!this.countdownValues[investment.id]) {
        return '';
      }

      // 根据倒计时值的内容返回相应的样式类
      const countdownValue = this.countdownValues[investment.id];

      if (countdownValue === 'Completed') {
        return 'countdown-completed';
      } else if (countdownValue.includes('Cycle ends in')) {
        return 'countdown-ending';
      } else {
        return 'countdown-active';
      }
    }
  }
}
</script>

<style lang="scss">
/* 使用共享样式变量 */

/* 确保容器样式正确 */
.responsive-container {
  width: 100%;
  margin: 0 auto;
  position: relative;
}

/* 移动端不需要特殊样式，移除空规则集 */

/* 页面容器样式使用共享的page-container类 */
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0a0e1a;
  position: relative;
  padding-bottom: 40rpx; /* 底部留出空间 */
}

/* 自定义顶部导航栏样式 */
.custom-header {
  background-color: #0a0e1a;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative; /* 统一使用相对定位，在文档流中 */
  z-index: 1000;
  min-height: 120rpx;
}

.back-button-wrapper {
  position: relative;
  z-index: 10;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-header-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  color: #FF8C00;
  font-size: 38rpx;
  font-weight: 500;
  pointer-events: none;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-header-placeholder {
  width: 60rpx;
  height: 60rpx;
  visibility: hidden;
}

.investment-list-wrapper {
  flex: 1;
  padding: 20rpx 30rpx 0;
}

.investment-list {
  height: calc(100vh - 200rpx); /* 给scroll-view一个明确的高度 */
}

.investment-item {
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
  border-radius: 12rpx;
  padding: 25rpx 30rpx;
  margin-bottom: 20rpx;
  color: #FFFFFF;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 40rpx;
    height: 8rpx;
    background: #FF8C00;
  }

  &::before {
    top: 0;
    left: 0;
    border-radius: 12rpx 0 0 0;
  }

  &::after {
    top: 0;
    left: 40rpx;
    border-radius: 0 12rpx 12rpx 0;
  }
}

.investment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-right: 10rpx;
}

.investment-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}

.investment-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-active {
  background-color: #FF8C00;
  color: #0a0e1a;
}

.status-completed {
  background-color: #4caf50;
  color: #FFFFFF;
}

.status-cancelled {
  background-color: #f44336;
  color: #FFFFFF;
}

.status-pending {
  background-color: #ff9800;
  color: #FFFFFF;
}

.investment-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding-right: 10rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  font-size: 28rpx;
  color: #FFFFFF;
}

.highlight {
  color: #FF8C00;
  font-weight: 500;
}

.countdown {
  font-weight: 500;
  font-family: 'Courier New', monospace;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 26rpx;
}

/* 活跃状态的倒计时（显示距离下一次收益的时间） */
.countdown-active {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
  border-color: rgba(255, 152, 0, 0.3);
}

/* 即将结束的倒计时（显示距离投资周期结束的时间） */
.countdown-ending {
  color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
}

/* 已完成的倒计时 */
.countdown-completed {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

/* 已取消的倒计时 */
.countdown-cancelled {
  color: #9e9e9e;
  background-color: rgba(158, 158, 158, 0.1);
  border-color: rgba(158, 158, 158, 0.3);
}

/* 已暂停的倒计时 */
.countdown-paused {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  border-color: rgba(33, 150, 243, 0.3);
}

.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 229, 255, 0.3);
  border-top: 3px solid #00e5ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .empty-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 10px;
}

.empty-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.empty-tips-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 15px;
}

.go-home-btn {
  background-color: #FF8C00;
  color: #1c2431;
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 20px;
  border: none;
}



.loading-more, .no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.no-more-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
}

/* 隐藏滚动条 */
.investment-list::-webkit-scrollbar {
  display: none;
}

.investment-list {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* PC端响应式设计 - 统一宽度为700px */
@media screen and (min-width: 768px) {
  .investment-list-wrapper {
    max-width: 700px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    width: 700px !important;
  }

  .page-container {
    max-width: none !important;
  }
}
</style>
