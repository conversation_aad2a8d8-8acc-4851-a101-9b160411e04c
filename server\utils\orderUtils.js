/**
 * 订单工具函数
 */

/**
 * 生成订单号
 * @param {string} prefix - 订单前缀，例如：D(充值)，W(提现)，I(投资)
 * @returns {string} - 返回生成的订单号
 */
exports.generateOrderNumber = (prefix = 'D') => {
  // 获取当前日期
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  
  // 生成随机数
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  // 生成时间戳部分
  const timestamp = now.getTime().toString().slice(-6);
  
  // 组合订单号：前缀 + 年月日 + 随机数 + 时间戳
  return `${prefix}${year}${month}${day}${random}${timestamp}`;
};
