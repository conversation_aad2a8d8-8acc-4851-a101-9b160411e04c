/**
 * 检查users表的结构
 */
const { User } = require('./models');
const sequelize = require('./config/database');

async function checkUsersTable() {
  try {
    // 检查连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 获取users表的属性
    console.log('User模型的属性:');
    console.log(Object.keys(User.rawAttributes));
    
    // 查询数据库中users表的结构
    const [result] = await sequelize.query('DESCRIBE users');
    console.log('users表的结构:');
    result.forEach(field => {
      console.log(`${field.Field}: ${field.Type} ${field.Null === 'YES' ? '(可为空)' : '(不可为空)'}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('检查users表失败:', error);
    process.exit(1);
  }
}

checkUsersTable();
