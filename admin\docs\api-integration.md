# 前端 API 集成文档

## 概述
本文档详细描述了前端应用如何与后端 API 进行交互，包括请求方法、URL、请求参数、响应数据等信息，以及前端需要处理的各种情况。

## 基础信息
- 基础URL: `http://localhost:3000/api`
- 所有请求和响应均使用 JSON 格式
- 认证使用 JWT Token，在请求头中添加 `Authorization: Bearer {token}`
- 所有响应均包含 `code`、`message` 和 `data` 字段

## API 请求封装

前端使用 Axios 进行 API 请求，并进行了统一封装处理。以下是 API 请求的基本封装：

```javascript
// src/utils/request.js
import axios from 'axios';
import { ElMessage } from 'element-plus';
import router from '@/router';
import { useUserStore } from '@/stores/user';

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStore();
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`;
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 检查是否有新的 token
    const newToken = response.headers['x-new-token'];
    if (newToken) {
      const userStore = useUserStore();
      userStore.setToken(newToken);
      console.log('Token 已自动刷新');
    }

    const res = response.data;

    // 如果返回的状态码不是 200，则判断为错误
    if (res.code !== 200 && res.code !== 201) {
      ElMessage({
        message: res.message || '请求失败',
        type: 'error',
        duration: 5 * 1000
      });

      // 401: 未登录或 token 过期
      if (res.code === 401) {
        ElMessage({
          message: '登录已过期，请重新登录',
          type: 'error',
          duration: 5 * 1000
        });
        const userStore = useUserStore();
        userStore.logout();
        router.push('/login');
      }

      return Promise.reject(new Error(res.message || '请求失败'));
    } else {
      return res;
    }
  },
  error => {
    console.error('响应错误:', error);

    // 处理 HTTP 状态码错误
    if (error.response) {
      const { status, data } = error.response;

      // 处理 401 未授权错误
      if (status === 401) {
        ElMessage({
          message: '登录已过期，请重新登录',
          type: 'error',
          duration: 5 * 1000
        });
        const userStore = useUserStore();
        userStore.logout();
        router.push('/login');
      }
      // 处理 403 禁止访问错误
      else if (status === 403) {
        ElMessage({
          message: data.message || '权限不足，无法访问',
          type: 'error',
          duration: 5 * 1000
        });
      }
      // 处理 429 请求过多错误
      else if (status === 429) {
        ElMessage({
          message: data.message || '请求过于频繁，请稍后再试',
          type: 'error',
          duration: 5 * 1000
        });
      }
      // 处理其他错误
      else {
        ElMessage({
          message: data.message || `请求失败: ${status}`,
          type: 'error',
          duration: 5 * 1000
        });
      }
    } else if (error.message.includes('timeout')) {
      ElMessage({
        message: '请求超时，请检查网络连接',
        type: 'error',
        duration: 5 * 1000
      });
    } else {
      ElMessage({
        message: error.message || '请求失败，请稍后再试',
        type: 'error',
        duration: 5 * 1000
      });
    }

    return Promise.reject(error);
  }
);

export default service;
```

## API 模块化组织

前端将 API 请求按照功能模块进行组织，每个模块对应后端的一个或多个接口。

### 用户认证 API

```javascript
// src/api/auth.js
import request from '@/utils/request';

// 管理员登录
export function login(data) {
  return request({
    url: '/admin/auth/login',
    method: 'post',
    data
  });
}

// 获取管理员信息
export function getInfo() {
  return request({
    url: '/admin/auth/profile',
    method: 'get'
  });
}

// 管理员登出
export function logout() {
  return request({
    url: '/admin/auth/logout',
    method: 'post'
  });
}
```

### 管理员 API

```javascript
// src/api/admin.js
import request from '@/utils/request';

// 获取管理员列表
export function getAdmins(params) {
  return request({
    url: '/admin/admins',
    method: 'get',
    params
  });
}

// 创建管理员
export function createAdmin(data) {
  return request({
    url: '/admin/admins',
    method: 'post',
    data
  });
}

// 更新管理员
export function updateAdmin(id, data) {
  return request({
    url: `/admin/admins/${id}`,
    method: 'put',
    data
  });
}

// 删除管理员
export function deleteAdmin(id) {
  return request({
    url: `/admin/admins/${id}`,
    method: 'delete'
  });
}
```

### 角色 API

```javascript
// src/api/role.js
import request from '@/utils/request';

// 获取角色列表
export function getRoles(params) {
  return request({
    url: '/admin/roles',
    method: 'get',
    params
  });
}

// 获取角色详情
export function getRole(id) {
  return request({
    url: `/admin/roles/${id}`,
    method: 'get'
  });
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/admin/roles',
    method: 'post',
    data
  });
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/admin/roles/${id}`,
    method: 'put',
    data
  });
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/admin/roles/${id}`,
    method: 'delete'
  });
}
```

### 权限 API

```javascript
// src/api/permission.js
import request from '@/utils/request';

// 获取权限列表
export function getPermissions() {
  return request({
    url: '/admin/permissions',
    method: 'get'
  });
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/admin/permissions/tree',
    method: 'get'
  });
}
```

### 附件管理 API

```javascript
// src/api/attachment.js
import request from '@/utils/request';

// 获取附件列表
export function getAttachments(params) {
  return request({
    url: '/admin/attachments',
    method: 'get',
    params
  });
}

// 获取附件详情
export function getAttachment(id) {
  return request({
    url: `/admin/attachments/${id}`,
    method: 'get'
  });
}

// 上传附件
export function uploadAttachment(data) {
  return request({
    url: '/admin/attachments/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 更新附件信息
export function updateAttachment(id, data) {
  return request({
    url: `/admin/attachments/${id}`,
    method: 'put',
    data
  });
}

// 删除附件
export function deleteAttachment(id) {
  return request({
    url: `/admin/attachments/${id}`,
    method: 'delete'
  });
}

// 批量删除附件
export function batchDeleteAttachments(ids) {
  return request({
    url: '/admin/attachments/batch',
    method: 'delete',
    data: { ids }
  });
}

// 导出附件列表
export function exportAttachments(params) {
  return request({
    url: '/admin/attachments/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}
```

### 客服管理 API

```javascript
// src/api/customerService.js
import request from '@/utils/request';

// 获取客服列表
export function getCustomerServices(params) {
  return request({
    url: '/admin/customer-services',
    method: 'get',
    params
  });
}

// 获取客服详情
export function getCustomerService(id) {
  return request({
    url: `/admin/customer-services/${id}`,
    method: 'get'
  });
}

// 创建客服
export function createCustomerService(data) {
  return request({
    url: '/admin/customer-services',
    method: 'post',
    data
  });
}

// 更新客服信息
export function updateCustomerService(id, data) {
  return request({
    url: `/admin/customer-services/${id}`,
    method: 'put',
    data
  });
}

// 删除客服
export function deleteCustomerService(id) {
  return request({
    url: `/admin/customer-services/${id}`,
    method: 'delete'
  });
}

// 批量删除客服
export function batchDeleteCustomerServices(ids) {
  return request({
    url: '/admin/customer-services/batch',
    method: 'delete',
    data: { ids }
  });
}

// 导出客服列表
export function exportCustomerServices(params) {
  return request({
    url: '/admin/customer-services/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}
```

### 会员 API

```javascript
// src/api/member.js
import request from '@/utils/request';

// 获取会员列表
export function getMembers(params) {
  return request({
    url: '/admin/members',
    method: 'get',
    params
  });
}

// 获取会员详情
export function getMember(id) {
  return request({
    url: `/admin/members/${id}`,
    method: 'get'
  });
}

// 更新会员信息
export function updateMember(id, data) {
  return request({
    url: `/admin/members/${id}`,
    method: 'put',
    data
  });
}

// 赠送金额
export function giftMoney(id, data) {
  return request({
    url: `/admin/members/${id}/gift-money`,
    method: 'post',
    data
  });
}

// 赠送投资
export function giftInvestment(id, data) {
  return request({
    url: `/admin/members/${id}/gift-investment`,
    method: 'post',
    data
  });
}
```

### VIP等级和类型 API

```javascript
// src/api/vip.js
import request from '@/utils/request';

// 获取VIP等级列表
export function getVipLevels() {
  return request({
    url: '/api/admin/vip/levels',
    method: 'get'
  });
}

// 获取VIP类型列表
export function getVipTypes() {
  return request({
    url: '/api/admin/vip/types',
    method: 'get'
  });
}
```

### 配置 API

```javascript
// src/api/config.ts
import request from '@/utils/request'

// 获取所有配置数据
export const getAllConfigs = () => {
  return request({
    url: '/api/admin/config/all',
    method: 'get'
  })
}

// 获取项目类型列表
export const getProjectTypes = () => {
  return request({
    url: '/api/admin/config/project-types',
    method: 'get'
  })
}

// 获取项目分类列表
export const getProjectCategories = () => {
  return request({
    url: '/api/admin/config/project-categories',
    method: 'get'
  })
}

// 获取货币类型列表
export const getCurrencyTypes = () => {
  return request({
    url: '/api/admin/config/currency-types',
    method: 'get'
  })
}

// 获取价格类型列表
export const getPriceTypes = () => {
  return request({
    url: '/api/admin/config/price-types',
    method: 'get'
  })
}

// 获取支付方式列表
export const getPaymentMethods = () => {
  return request({
    url: '/api/admin/config/payment-methods',
    method: 'get'
  })
}

// 获取状态列表
export const getStatusOptions = () => {
  return request({
    url: '/api/admin/config/status-options',
    method: 'get'
  })
}

// 获取出售状态列表
export const getSellStatusOptions = () => {
  return request({
    url: '/api/admin/config/sell-status-options',
    method: 'get'
  })
}

// 获取每周收益日列表
export const getWeeklyProfitDays = () => {
  return request({
    url: '/api/admin/config/weekly-profit-days',
    method: 'get'
  })
}
```

## 状态管理与 API 集成

前端使用 Pinia 进行状态管理，并与 API 请求进行集成。以下是用户认证相关的状态管理示例：

```javascript
// src/stores/user.js
import { defineStore } from 'pinia';
import { login, logout, getInfo } from '@/api/auth';
import { setToken, getToken, removeToken } from '@/utils/auth';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  }),

  actions: {
    // 用户登录
    async login(userInfo) {
      const { username, password } = userInfo;
      try {
        const response = await login({ username: username.trim(), password });
        const { data } = response;
        this.token = data.token;
        setToken(data.token);
        return Promise.resolve(response);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    // 获取用户信息
    async getInfo() {
      try {
        const response = await getInfo();
        const { data } = response;

        if (!data) {
          return Promise.reject(new Error('验证失败，请重新登录'));
        }

        const { roles, name, avatar, permissions } = data;

        // 角色必须是非空数组
        if (!roles || roles.length <= 0) {
          return Promise.reject(new Error('用户没有角色权限'));
        }

        this.roles = roles;
        this.name = name;
        this.avatar = avatar;
        this.permissions = permissions;

        return Promise.resolve(data);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    // 用户登出
    async logout() {
      try {
        if (this.token) {
          await logout();
        }
      } catch (error) {
        console.error('登出错误:', error);
      } finally {
        this.token = '';
        this.roles = [];
        this.permissions = [];
        removeToken();
      }
    },

    // 重置 Token
    resetToken() {
      this.token = '';
      this.roles = [];
      this.permissions = [];
      removeToken();
    },

    // 设置 Token（用于自动刷新）
    setToken(token) {
      this.token = token;
      setToken(token);
    }
  }
});
```

## 组件中使用 API

在 Vue 组件中使用 API 的示例：

```javascript
<script setup>
import { ref, onMounted } from 'vue';
import { getMembers, updateMember } from '@/api/member';
import { ElMessage, ElMessageBox } from 'element-plus';

// 会员列表数据
const memberList = ref([]);
const total = ref(0);
const loading = ref(false);
const queryParams = ref({
  page: 1,
  limit: 10,
  keyword: '',
  status: ''
});

// 获取会员列表
const fetchMembers = async () => {
  loading.value = true;
  try {
    const res = await getMembers(queryParams.value);
    memberList.value = res.data.items;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取会员列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 编辑会员信息
const handleEdit = async (row) => {
  try {
    const { id, nickname, status } = row;
    await updateMember(id, { nickname, status });
    ElMessage.success('更新成功');
    fetchMembers();
  } catch (error) {
    console.error('更新会员信息失败:', error);
  }
};

// 删除会员
const handleDelete = (id) => {
  ElMessageBox.confirm('确认删除该会员?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteMember(id);
      ElMessage.success('删除成功');
      fetchMembers();
    } catch (error) {
      console.error('删除会员失败:', error);
    }
  }).catch(() => {
    // 取消删除操作
  });
};

// 搜索会员
const handleSearch = () => {
  queryParams.value.page = 1;
  fetchMembers();
};

// 重置搜索条件
const resetQuery = () => {
  queryParams.value = {
    page: 1,
    limit: 10,
    keyword: '',
    status: ''
  };
  fetchMembers();
};

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.value.page = page;
  fetchMembers();
};

// 每页条数变化
const handleSizeChange = (size) => {
  queryParams.value.limit = size;
  queryParams.value.page = 1;
  fetchMembers();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchMembers();
});
</script>
```

## 错误处理

前端对不同类型的错误进行了统一处理：

1. **401 未授权错误**：清除用户信息，跳转到登录页面
2. **403 禁止访问错误**：显示权限不足提示
3. **429 请求过多错误**：显示请求频繁提示
4. **网络错误**：显示网络连接错误提示
5. **超时错误**：显示请求超时提示

## 浏览器兼容性处理

由于后端限制只允许 Chrome 浏览器访问管理后台，前端在登录页面添加了浏览器检测：

```javascript
// src/utils/browser.js
export function isChrome() {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('chrome') && !userAgent.includes('edg') && !userAgent.includes('opr');
}
```

```javascript
// src/views/login/index.vue
import { isChrome } from '@/utils/browser';

// 检查浏览器兼容性
const checkBrowser = () => {
  if (!isChrome()) {
    ElMessage({
      message: '为了获得最佳体验，请使用 Chrome 浏览器访问管理后台',
      type: 'warning',
      duration: 5000
    });
  }
};

onMounted(() => {
  checkBrowser();
});
```

## Token 自动刷新机制

前端实现了 Token 自动刷新机制，当服务器返回新的 Token 时（在响应头的 `X-New-Token` 字段中），前端会自动更新存储的 Token：

1. 在响应拦截器中检查是否有 `X-New-Token` 头
2. 如果存在，则更新 Pinia store 中的 Token
3. 同时更新本地存储中的 Token

这样可以确保用户在使用过程中不会因为 Token 过期而被强制登出。

## 安全注意事项

1. **敏感信息处理**：
   - 不在前端存储敏感信息
   - 密码等敏感信息只在内存中临时保存，不写入本地存储

2. **XSS 防护**：
   - 使用 Vue 的模板语法自动转义内容
   - 对用户输入进行验证和过滤

3. **CSRF 防护**：
   - 使用 JWT Token 进行认证
   - 确保所有修改操作使用 POST/PUT/DELETE 方法

4. **限流处理**：
   - 对频繁请求进行友好提示
   - 实现请求节流和防抖

## 常见问题与解决方案

### 1. Token 过期处理

**问题**：用户 Token 过期后，API 请求返回 401 错误。

**解决方案**：
- 在响应拦截器中统一处理 401 错误
- 清除用户信息并跳转到登录页面
- 实现 Token 自动刷新机制，减少用户被强制登出的情况

### 2. 请求限流处理

**问题**：频繁请求导致服务器返回 429 错误。

**解决方案**：
- 在响应拦截器中统一处理 429 错误
- 显示友好的错误提示
- 实现请求节流和防抖，减少短时间内的重复请求

### 3. 浏览器兼容性问题

**问题**：非 Chrome 浏览器访问管理后台时可能出现兼容性问题。

**解决方案**：
- 在登录页面检测浏览器类型
- 对非 Chrome 浏览器显示友好提示
- 确保核心功能在 Chrome 浏览器中正常工作

### 4. 大数据量处理

**问题**：获取大量数据时前端性能下降。

**解决方案**：
- 实现分页加载
- 使用虚拟滚动技术
- 实现数据缓存，减少重复请求

## API 状态码参考

| 状态码 | 说明 |
|-------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过多 |
| 500 | 服务器内部错误 |
