import request from '@/utils/request'

/**
 * 获取订单列表
 * @param params 查询参数
 */
export function getOrders(params?: any) {
  return request({
    url: '/api/admin/orders',
    method: 'get',
    params
  })
}

/**
 * 获取订单详情
 * @param id 订单ID
 */
export function getOrder(id: number) {
  return request({
    url: `/api/admin/orders/${id}`,
    method: 'get'
  })
}

/**
 * 更新订单状态
 * @param id 订单ID
 * @param data 状态数据
 */
export function updateOrderStatus(id: number, data: { status: string, actual_return?: number }) {
  return request({
    url: `/api/admin/orders/${id}/status`,
    method: 'put',
    data
  })
}
