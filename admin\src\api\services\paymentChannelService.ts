import request from '@/utils/request'
import axios from 'axios'
import { PaymentChannel, PaymentChannelRequest, PaymentChannelQuery, PaymentChannelResponse } from '../models/paymentChannel'

/**
 * 支付通道服务
 */
export default {
  /**
   * 获取支付通道列表
   * @param params 查询参数
   * @returns 支付通道列表
   */
  getPaymentChannels(params?: PaymentChannelQuery): Promise<PaymentChannelResponse> {
    return request({
      url: '/api/admin/payment-channels',
      method: 'get',
      params
    })
  },

  /**
   * 获取支付通道详情
   * @param id 支付通道ID
   * @returns 支付通道详情
   */
  getPaymentChannel(id: number): Promise<{ code: number; message: string; data: PaymentChannel }> {
    return request({
      url: `/api/admin/payment-channels/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建支付通道
   * @param data 支付通道数据
   * @returns 创建结果
   */
  createPaymentChannel(data: PaymentChannelRequest): Promise<any> {
    return request({
      url: '/api/admin/payment-channels',
      method: 'post',
      data
    }).then(response => {
      // 确保返回完整的响应对象，包括code和message
      if (typeof response === 'object' && response !== null) {
        return response;
      } else {
        // 如果response不是对象，构造一个标准响应
        return {
          code: 200,
          message: '创建成功',
          data: response
        };
      }
    });
  },

  /**
   * 更新支付通道
   * @param id 支付通道ID
   * @param data 支付通道数据
   * @returns 更新结果
   */
  updatePaymentChannel(id: number, data: PaymentChannelRequest): Promise<any> {
    return request({
      url: `/api/admin/payment-channels/${id}`,
      method: 'put',
      data
    }).then(response => {
      // 确保返回完整的响应对象，包括code和message
      if (typeof response === 'object' && response !== null) {
        return response;
      } else {
        // 如果response不是对象，构造一个标准响应
        return {
          code: 200,
          message: '更新成功',
          data: response
        };
      }
    });
  },

  /**
   * 删除支付通道
   * @param id 支付通道ID
   * @returns 删除结果
   */
  deletePaymentChannel(id: number): Promise<any> {
    return request({
      url: `/api/admin/payment-channels/${id}`,
      method: 'delete'
    }).then(response => {
      // 确保返回完整的响应对象，包括code和message
      if (typeof response === 'object' && response !== null) {
        return response;
      } else {
        // 如果response不是对象，构造一个标准响应
        return {
          code: 200,
          message: '删除成功',
          data: null
        };
      }
    });
  },

  /**
   * 更新支付通道状态
   * @param id 支付通道ID
   * @param field 字段名
   * @param value 字段值
   * @returns 更新结果
   */
  updatePaymentChannelStatus(id: number, field: string, value: number): Promise<any> {
    // 使用PUT方法代替PATCH方法
    return request({
      url: `/api/admin/payment-channels/${id}/status`,
      method: 'put',
      data: {
        field,
        value
      }
    }).then(response => {
      // 确保返回完整的响应对象，包括code和message
      if (typeof response === 'object' && response !== null) {
        return response;
      } else {
        // 如果response不是对象，构造一个标准响应
        return {
          code: 200,
          message: '更新成功',
          data: response
        };
      }
    });
  },

  /**
   * 更新支付通道权重
   * @param id 支付通道ID
   * @param weight 权重值
   * @returns 更新结果
   */
  updatePaymentChannelWeight(id: number, weight: number): Promise<any> {
    // 使用PUT方法代替PATCH方法
    return request({
      url: `/api/admin/payment-channels/${id}/status`,
      method: 'put',
      data: {
        field: 'weight',
        value: weight
      }
    }).then(response => {
      // 确保返回完整的响应对象，包括code和message
      if (typeof response === 'object' && response !== null) {
        return response;
      } else {
        // 如果response不是对象，构造一个标准响应
        return {
          code: 200,
          message: '更新成功',
          data: response
        };
      }
    });
  },

  /**
   * 批量更新支付通道权重
   * @param data 权重更新数据数组 [{id, weight}]
   * @returns 更新结果
   */
  updatePaymentChannelWeights(data: {id: number, weight: number}[]): Promise<any> {
    return request({
      url: '/api/admin/payment-channels/batch-weights',
      method: 'post',
      data
    }).then(response => {
      // 确保返回完整的响应对象，包括code和message
      if (typeof response === 'object' && response !== null) {
        return response;
      } else {
        // 如果response不是对象，构造一个标准响应
        return {
          code: 200,
          message: '批量更新权重成功',
          data: response
        };
      }
    });
  }
}
