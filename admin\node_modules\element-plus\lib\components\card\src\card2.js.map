{"version": 3, "file": "card2.js", "sources": ["../../../../../../packages/components/card/src/card.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes, StyleValue } from 'vue'\n\nexport const cardProps = buildProps({\n  /**\n   * @description title of the card. Also accepts a DOM passed by `slot#header`\n   */\n  header: {\n    type: String,\n    default: '',\n  },\n  footer: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description CSS style of card body\n   */\n  bodyStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: '',\n  },\n  /**\n   * @description custom class name of card body\n   */\n  bodyClass: String,\n  /**\n   * @description when to show card shadows\n   */\n  shadow: {\n    type: String,\n    values: ['always', 'hover', 'never'],\n    default: 'always',\n  },\n} as const)\nexport type CardProps = ExtractPropTypes<typeof cardProps>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,SAAS,GAAGA,kBAAU,CAAC;AACpC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;AACxC,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,CAAC;;;;"}