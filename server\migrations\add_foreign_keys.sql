-- FOX投资平台添加外键约束脚本
-- 作者：FOX开发团队
-- 日期：2025-06-04

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 用户表外键
ALTER TABLE users_new
ADD CONSTRAINT fk_users_inviter
FOREIGN KEY (inviter_id) REFERENCES users_new(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_users_level
FOREIGN KEY (level_id) REFERENCES user_levels_new(id) ON DELETE SET NULL;

-- 2. 账户余额表外键
ALTER TABLE account_balances
ADD CONSTRAINT fk_account_balances_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE;

-- 3. 投资项目表外键
ALTER TABLE projects_new
ADD CONSTRAINT fk_projects_image
FOREIGN KEY (image_id) REFERENCES attachments_new(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_projects_video
FOREIGN KEY (video_id) REFERENCES attachments_new(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_projects_vip_level
FOREIGN KEY (vip_level_id) REFERENCES user_levels_new(id) ON DELETE SET NULL;

-- 4. 交易表外键
ALTER TABLE transactions_new
ADD CONSTRAINT fk_transactions_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE;

-- 5. 投资记录表外键
ALTER TABLE investments_new
ADD CONSTRAINT fk_investments_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_investments_project
FOREIGN KEY (project_id) REFERENCES projects_new(id) ON DELETE CASCADE;

-- 6. 投资收益表外键
ALTER TABLE investment_profits_new
ADD CONSTRAINT fk_investment_profits_investment
FOREIGN KEY (investment_id) REFERENCES investments_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_investment_profits_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_investment_profits_transaction
FOREIGN KEY (transaction_id) REFERENCES transactions_new(id) ON DELETE SET NULL;

-- 7. 佣金记录表外键
ALTER TABLE commissions_new
ADD CONSTRAINT fk_commissions_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_commissions_from_user
FOREIGN KEY (from_user_id) REFERENCES users_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_commissions_investment
FOREIGN KEY (investment_id) REFERENCES investments_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_commissions_transaction
FOREIGN KEY (transaction_id) REFERENCES transactions_new(id) ON DELETE SET NULL;

-- 8. 银行卡表外键
ALTER TABLE bank_cards
ADD CONSTRAINT fk_bank_cards_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE;

-- 9. 充值订单表外键
ALTER TABLE deposits_new
ADD CONSTRAINT fk_deposits_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_deposits_receiving_card
FOREIGN KEY (receiving_card_id) REFERENCES bank_cards(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_deposits_transaction
FOREIGN KEY (transaction_id) REFERENCES transactions_new(id) ON DELETE SET NULL;

-- 10. 提现记录表外键
ALTER TABLE withdrawals_new
ADD CONSTRAINT fk_withdrawals_user
FOREIGN KEY (user_id) REFERENCES users_new(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_withdrawals_bank_card
FOREIGN KEY (bank_card_id) REFERENCES bank_cards(id) ON DELETE CASCADE,
ADD CONSTRAINT fk_withdrawals_transaction
FOREIGN KEY (transaction_id) REFERENCES transactions_new(id) ON DELETE SET NULL;

-- 11. 用户级别表外键
ALTER TABLE user_levels_new
ADD CONSTRAINT fk_user_levels_image
FOREIGN KEY (image_id) REFERENCES attachments_new(id) ON DELETE SET NULL;

-- 12. 轮播图表外键
ALTER TABLE banners_new
ADD CONSTRAINT fk_banners_attachment
FOREIGN KEY (attachment_id) REFERENCES attachments_new(id) ON DELETE CASCADE;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
