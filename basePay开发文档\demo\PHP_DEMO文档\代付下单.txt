{
   <?php
include ('signapi.php');


$apply_date=$_POST["apply_date"];


$bank_code=$_POST["bank_code"];    

$mch_id=$_POST["mch_id"];  

$mch_transferId=$_POST["mch_transferId"];

$receive_account=$_POST["receive_account"];

$receive_name=$_POST["receive_name"];

$transfer_amount=$_POST["transfer_amount"];

$sign_type=$_POST["sign_type"];


$signStr = "";

    $signStr = $signStr."apply_date=".$apply_date."&";

    if($bank_code != ""){
        $signStr = $signStr . "bank_code=" . $bank_code . "&";
    }

    $signStr = $signStr."mch_id=".$mch_id."&";    

    $signStr = $signStr."mch_transferId=".$mch_transferId."&";    

    $signStr = $signStr."receive_account=".$receive_account."&";

    $signStr = $signStr."receive_name=".$receive_name."&";    

    $signStr = $signStr."transfer_amount=".$transfer_amount;


$reqUrl = "";

$merchant_key ="";


$signAPI = new signapi();

$sign = $signAPI->sign($signStr, $merchant_key);


$postdata=array('apply_date'=>$apply_date,
    'bank_code'=>$bank_code,
    'mch_id'=>$mch_id,
    'mch_transferId'=>$mch_transferId,
    'receive_account'=>$receive_account,
    'receive_name'=>$receive_name,
    'transfer_amount'=>$transfer_amount,
    'sign_type'=>$sign_type,
    'sign'=>$sign);

    //var_dump($postdata);



    $ch = curl_init();    
    curl_setopt($ch,CURLOPT_URL,$reqUrl);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postdata));  
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response=curl_exec($ch);


    curl_close($ch);



    echo $response;

    }
  }