START TRANSACTION;

UPDATE investments 
SET 
    start_time = CONVERT_TZ(start_time, '+08:00', '+00:00'),
    last_profit_time = CASE 
        WHEN last_profit_time IS NOT NULL 
        THEN CONVERT_TZ(last_profit_time, '+08:00', '+00:00')
        ELSE NULL 
    END,
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

UPDATE investment_profits 
SET 
    profit_time = CONVERT_TZ(profit_time, '+08:00', '+00:00'),
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

UPDATE users 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00'),
    last_login_time = CASE 
        WHEN last_login_time IS NOT NULL 
        THEN CONVERT_TZ(last_login_time, '+08:00', '+00:00')
        ELSE NULL 
    END;

UPDATE transactions 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00'),
    completed_at = CASE 
        WHEN completed_at IS NOT NULL 
        THEN CONVERT_TZ(completed_at, '+08:00', '+00:00')
        ELSE NULL 
    END;

UPDATE admin_users 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00'),
    last_login_time = CASE 
        WHEN last_login_time IS NOT NULL 
        THEN CONVERT_TZ(last_login_time, '+08:00', '+00:00')
        ELSE NULL 
    END;

UPDATE projects 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

UPDATE system_params 
SET 
    created_at = CONVERT_TZ(created_at, '+08:00', '+00:00'),
    updated_at = CONVERT_TZ(updated_at, '+08:00', '+00:00');

INSERT IGNORE INTO system_params (param_key, param_value, created_at, updated_at)
VALUES ('[site.timezone]', '+00:00', UTC_TIMESTAMP(), UTC_TIMESTAMP());

UPDATE system_params 
SET param_value = '+00:00', updated_at = UTC_TIMESTAMP()
WHERE param_key = '[site.timezone]';

COMMIT;
