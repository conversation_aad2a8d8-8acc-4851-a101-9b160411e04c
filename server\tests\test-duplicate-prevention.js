/**
 * 重复发放测试
 * 测试方案3.1的防重复机制
 */

const { Investment, Project, User, InvestmentProfit } = require('../models');
const sequelize = require('../config/database');
const profitSystem = require('../services/profitSystem');
const logger = require('../utils/logger');

/**
 * 测试1：检查现有重复记录
 */
const testCheckExistingDuplicates = async () => {
  console.log('\n=== 测试1：检查现有重复记录 ===');
  
  try {
    // 查询重复记录
    const duplicates = await sequelize.query(`
      SELECT investment_id, profit_time, COUNT(*) as count 
      FROM investment_profits 
      GROUP BY investment_id, profit_time 
      HAVING COUNT(*) > 1 
      LIMIT 10
    `, { type: sequelize.QueryTypes.SELECT });
    
    console.log(`📊 发现重复记录: ${duplicates.length} 组`);
    
    if (duplicates.length > 0) {
      console.log('重复记录详情:');
      duplicates.forEach((dup, index) => {
        console.log(`   ${index + 1}. 投资ID: ${dup.investment_id}, 时间: ${dup.profit_time}, 重复数: ${dup.count}`);
      });
      
      // 计算总重复记录数
      const totalDuplicateCount = await sequelize.query(`
        SELECT SUM(count - 1) as total_duplicates
        FROM (
          SELECT COUNT(*) as count 
          FROM investment_profits 
          GROUP BY investment_id, profit_time 
          HAVING COUNT(*) > 1
        ) as dup_counts
      `, { type: sequelize.QueryTypes.SELECT });
      
      console.log(`📈 总重复记录数: ${totalDuplicateCount[0].total_duplicates} 条`);
      console.log('⚠️  这证明了当前系统确实存在重复发放问题！');
      
      global.hasDuplicates = true;
      global.duplicateCount = duplicates.length;
    } else {
      console.log('✅ 没有发现重复记录');
      global.hasDuplicates = false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 检查重复记录失败:', error.message);
    return false;
  }
};

/**
 * 测试2：清理重复记录（保留最早的）
 */
const testCleanupDuplicates = async () => {
  console.log('\n=== 测试2：清理重复记录 ===');
  
  if (!global.hasDuplicates) {
    console.log('✅ 无需清理，没有重复记录');
    return true;
  }
  
  try {
    console.log('🧹 开始清理重复记录...');
    
    // 删除重复记录，保留ID最小的（最早创建的）
    const deleteResult = await sequelize.query(`
      DELETE p1 FROM investment_profits p1
      INNER JOIN investment_profits p2 
      WHERE p1.investment_id = p2.investment_id 
        AND p1.profit_time = p2.profit_time 
        AND p1.id > p2.id
    `);
    
    const deletedCount = deleteResult[0].affectedRows || 0;
    console.log(`✅ 清理完成，删除了 ${deletedCount} 条重复记录`);
    
    // 验证清理结果
    const remainingDuplicates = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM (
        SELECT investment_id, profit_time, COUNT(*) as cnt 
        FROM investment_profits 
        GROUP BY investment_id, profit_time 
        HAVING COUNT(*) > 1
      ) as dup_check
    `, { type: sequelize.QueryTypes.SELECT });
    
    if (remainingDuplicates[0].count === 0) {
      console.log('✅ 重复记录清理成功');
    } else {
      console.log(`⚠️  仍有 ${remainingDuplicates[0].count} 组重复记录`);
    }
    
    global.cleanedDuplicates = deletedCount;
    return true;
  } catch (error) {
    console.log('❌ 清理重复记录失败:', error.message);
    return false;
  }
};

/**
 * 测试3：添加唯一约束
 */
const testAddUniqueConstraint = async () => {
  console.log('\n=== 测试3：添加唯一约束 ===');
  
  try {
    // 检查约束是否已存在
    const existingConstraint = await sequelize.query(`
      SHOW INDEX FROM investment_profits WHERE Key_name = 'uk_investment_profit_time'
    `, { type: sequelize.QueryTypes.SELECT });
    
    if (existingConstraint.length > 0) {
      console.log('✅ 唯一约束已存在');
      global.constraintExists = true;
      return true;
    }
    
    // 添加唯一约束
    console.log('🔧 添加唯一约束...');
    await sequelize.query(`
      ALTER TABLE investment_profits 
      ADD UNIQUE INDEX uk_investment_profit_time (investment_id, profit_time)
    `);
    
    console.log('✅ 唯一约束添加成功');
    
    // 验证约束
    const newConstraint = await sequelize.query(`
      SHOW INDEX FROM investment_profits WHERE Key_name = 'uk_investment_profit_time'
    `, { type: sequelize.QueryTypes.SELECT });
    
    if (newConstraint.length > 0) {
      console.log('✅ 唯一约束验证成功');
      console.log(`   - 约束名: ${newConstraint[0].Key_name}`);
      console.log(`   - 字段: ${newConstraint.map(c => c.Column_name).join(', ')}`);
      global.constraintExists = true;
    } else {
      console.log('❌ 唯一约束验证失败');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 添加唯一约束失败:', error.message);
    return false;
  }
};

/**
 * 测试4：测试重复发放防护
 */
const testDuplicateProtection = async () => {
  console.log('\n=== 测试4：测试重复发放防护 ===');
  
  if (!global.constraintExists) {
    console.log('❌ 唯一约束不存在，无法测试');
    return false;
  }
  
  try {
    // 找到一个活跃投资
    const investment = await Investment.findOne({
      where: { status: 'active' },
      include: [{ model: Project, as: 'project' }]
    });
    
    if (!investment) {
      console.log('❌ 没有找到活跃投资');
      return false;
    }
    
    console.log(`✅ 使用投资ID: ${investment.id} 进行测试`);
    
    // 创建一个测试时间点
    const testProfitTime = new Date();
    console.log(`📅 测试时间点: ${testProfitTime.toISOString()}`);
    
    // 第一次创建收益记录（应该成功）
    console.log('🔄 第一次创建收益记录...');
    const firstResult = await profitSystem.createProfitRecord(investment, testProfitTime);
    
    if (firstResult) {
      console.log('✅ 第一次创建成功');
      console.log(`   - 收益ID: ${firstResult.id}`);
      console.log(`   - 金额: ${firstResult.amount}`);
      
      global.testProfitId = firstResult.id;
    } else {
      console.log('❌ 第一次创建失败');
      return false;
    }
    
    // 第二次创建相同时间的收益记录（应该被阻止）
    console.log('🔄 第二次创建相同时间的收益记录...');
    const secondResult = await profitSystem.createProfitRecord(investment, testProfitTime);
    
    if (secondResult === null) {
      console.log('✅ 第二次创建被正确阻止（返回null）');
      console.log('✅ 唯一约束防重复机制工作正常');
    } else if (secondResult === false) {
      console.log('✅ 第二次创建被正确阻止（返回false）');
      console.log('✅ 唯一约束防重复机制工作正常');
    } else {
      console.log('❌ 第二次创建没有被阻止，防重复机制失效');
      console.log(`   - 意外创建了收益ID: ${secondResult.id}`);
      return false;
    }
    
    return true;
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      console.log('✅ 捕获到唯一约束错误，防重复机制正常');
      console.log(`   - 错误信息: ${error.message}`);
      return true;
    } else {
      console.log('❌ 测试重复发放防护失败:', error.message);
      return false;
    }
  }
};

/**
 * 测试5：验证数据一致性
 */
const testDataConsistency = async () => {
  console.log('\n=== 测试5：验证数据一致性 ===');
  
  try {
    // 检查是否还有重复记录
    const duplicateCheck = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM (
        SELECT investment_id, profit_time, COUNT(*) as cnt 
        FROM investment_profits 
        GROUP BY investment_id, profit_time 
        HAVING COUNT(*) > 1
      ) as dup_check
    `, { type: sequelize.QueryTypes.SELECT });
    
    const duplicateCount = duplicateCheck[0].count;
    
    if (duplicateCount === 0) {
      console.log('✅ 数据一致性检查通过，无重复记录');
    } else {
      console.log(`❌ 数据一致性检查失败，仍有 ${duplicateCount} 组重复记录`);
      return false;
    }
    
    // 检查约束是否正常工作
    if (global.constraintExists) {
      console.log('✅ 唯一约束已正确安装');
    } else {
      console.log('❌ 唯一约束未正确安装');
      return false;
    }
    
    // 统计总收益记录数
    const totalProfits = await InvestmentProfit.count();
    console.log(`📊 当前总收益记录数: ${totalProfits}`);
    
    if (global.cleanedDuplicates) {
      console.log(`🧹 本次清理的重复记录: ${global.cleanedDuplicates} 条`);
    }
    
    return true;
  } catch (error) {
    console.log('❌ 验证数据一致性失败:', error.message);
    return false;
  }
};

/**
 * 清理测试数据
 */
const cleanupTestData = async () => {
  try {
    if (global.testProfitId) {
      await InvestmentProfit.destroy({ where: { id: global.testProfitId } });
      console.log('✅ 测试收益记录已清理');
    }
  } catch (error) {
    console.log('⚠️  清理测试数据时出错:', error.message);
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('开始执行重复发放测试...\n');
  
  let passedTests = 0;
  const totalTests = 5;
  
  try {
    // 执行测试
    if (await testCheckExistingDuplicates()) passedTests++;
    if (await testCleanupDuplicates()) passedTests++;
    if (await testAddUniqueConstraint()) passedTests++;
    if (await testDuplicateProtection()) passedTests++;
    if (await testDataConsistency()) passedTests++;
    
  } catch (error) {
    console.log('\n❌ 测试执行过程中出现错误:', error.message);
  } finally {
    // 清理测试数据
    await cleanupTestData();
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有重复发放测试通过！');
    console.log('✅ 方案3.1的防重复机制工作正常');
    console.log('✅ 数据库唯一约束已正确安装');
    return true;
  } else {
    console.log('⚠️  部分测试失败，请检查防重复机制');
    return false;
  }
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testCheckExistingDuplicates,
  testCleanupDuplicates,
  testAddUniqueConstraint,
  testDuplicateProtection,
  testDataConsistency,
  cleanupTestData
};
