{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/text/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Text from './src/text.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElText: SFCWithInstall<typeof Text> = withInstall(Text)\nexport default ElText\n\nexport * from './src/text'\n"], "names": ["withInstall", "Text"], "mappings": ";;;;;;;;AAEY,MAAC,MAAM,GAAGA,mBAAW,CAACC,iBAAI;;;;;;"}