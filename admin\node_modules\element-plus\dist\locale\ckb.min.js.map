{"version": 3, "file": "ckb.min.js", "sources": ["../../../../packages/locale/lang/ckb.ts"], "sourcesContent": ["export default {\n  name: 'ckb',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'باشە',\n      clear: 'پاککردنەوە',\n      defaultLabel: 'هەڵبژاردنی ڕەنگ',\n      description:\n        'ڕەنگی ئێستا {color}. ئینتەر دابگرە بۆ هەڵبژاردنی ڕەنگی نوێ.',\n    },\n    datepicker: {\n      now: 'ئێستا',\n      today: 'ئەمڕۆ',\n      cancel: 'پەشیمانبوونەوە',\n      clear: 'پاککردنەوە',\n      confirm: 'باشە',\n      dateTablePrompt:\n        'کلیلی ئاراستەکان بەکاربهێنەر بۆ هەڵبژاردنی ڕۆژی مانگەکە',\n      monthTablePrompt: 'کلیلی ئاراستەکان بەکاربهێنەر بۆ هەڵبژاردنی مانگ',\n      yearTablePrompt: 'کلیلی ئاراستەکان بەکاربهێنەر بۆ هەڵبژاردنی ساڵ',\n      selectedDate: 'بەرواری هەڵبژێردراو',\n      selectDate: 'هەڵبژاردنی بەروار',\n      selectTime: 'هەڵبژاردنی کات',\n      startDate: 'بەرواری دەستپێک',\n      startTime: 'کاتی دەستپێک',\n      endDate: 'بەرواری کۆتایی',\n      endTime: 'کاتی کۆتایی',\n      prevYear: 'ساڵی پێشوو',\n      nextYear: 'ساڵ داهاتوو',\n      prevMonth: 'مانگی پێشوو',\n      nextMonth: 'مانگی داهاتوو',\n      year: '',\n      month1: 'ڕێبەندان',\n      month2: 'ڕەشەمە',\n      month3: 'نەورۆز',\n      month4: 'گوڵان',\n      month5: 'جۆزەردان',\n      month6: 'پووشپەڕ',\n      month7: 'گەلاوێژ',\n      month8: 'خەرمانان',\n      month9: 'ڕەزبەر',\n      month10: 'گەڵاڕێزان',\n      month11: 'سەرماوەز',\n      month12: 'بەفرانبار',\n      week: 'هەفت',\n      weeks: {\n        sun: 'یەکشەممە',\n        mon: 'دووشەممە',\n        tue: 'سێشەممە',\n        wed: 'چوارشەممە',\n        thu: 'پێنجشەممە',\n        fri: 'هەینی',\n        sat: 'شەممە',\n      },\n      weeksFull: {\n        sun: 'یەکشەممە',\n        mon: 'دووشەممە',\n        tue: 'سێشەممە',\n        wed: 'چوارشەممە',\n        thu: 'پێنجشەممە',\n        fri: 'هەینی',\n        sat: 'شەممە',\n      },\n      months: {\n        jan: 'ڕێبەندان',\n        feb: 'ڕەشەمە',\n        mar: 'نەورۆز',\n        apr: 'گوڵان',\n        may: 'جۆزەردان',\n        jun: 'پووشپەڕ',\n        jul: 'گەلاوێژ',\n        aug: 'خەرمانان',\n        sep: 'ڕەزبەر',\n        oct: 'گەڵاڕێزان',\n        nov: 'سەرماوەز',\n        dec: 'بەفرانبار',\n      },\n    },\n    inputNumber: {\n      decrease: 'کەمکردنەوەی ژمارە',\n      increase: 'زیادکردنی ژمارە',\n    },\n    select: {\n      loading: 'بارکردن',\n      noMatch: 'هیچ داتایەکی هاوتا نیە',\n      noData: 'هیچ داتایەک نیە',\n      placeholder: 'هەڵبژاردن',\n    },\n    mention: {\n      loading: 'بارکردن',\n    },\n    dropdown: {\n      toggleDropdown: 'کردنەوەو داخستنی کشاو',\n    },\n    cascader: {\n      noMatch: 'هیچ داتایەکی هاوتا نیە',\n      loading: 'بارکردن',\n      placeholder: 'هەڵبژاردن',\n      noData: 'هیچ داتایەک نیە',\n    },\n    pagination: {\n      goto: 'بڕۆ بۆ',\n      pagesize: '/لاپەڕە',\n      total: 'کۆی گشتیی {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'بەکارهێنانی بەکارنەهێنراو دۆزراوەتەوە، تکایە بۆ وردەکاری زیاتر سەردانی بەڵگەنامەکانی el-pagination بکە',\n    },\n    dialog: {\n      close: 'داخستنی ئەم دیالۆگە',\n    },\n    drawer: {\n      close: 'داخستنی ئەم دیالۆگە',\n    },\n    messagebox: {\n      title: 'پەیام',\n      confirm: 'باشە',\n      cancel: 'پەشایمانبوونەوە',\n      error: 'داخلکردنی نایاسایی',\n      close: 'داخستنی ئەم دیالۆگە',\n    },\n    upload: {\n      deleteTip: 'فشار لەسەر سڕینەوە بکە بۆ لابردن',\n      delete: 'سڕینەوە',\n      preview: 'بینینەوە',\n      continue: 'بەردەوامبوون',\n    },\n    slider: {\n      defaultLabel: 'سلاید لە نێوان {min} و {max}',\n      defaultRangeStartLabel: 'بەهای دەستپێک هەلبژێرە',\n      defaultRangeEndLabel: 'بەهای کۆتایی هەلبژێرە',\n    },\n    table: {\n      emptyText: 'هیچ داتا نیە',\n      confirmFilter: 'دووپاتکردنەوە',\n      resetFilter: 'جێگیرکردنەوە',\n      clearFilter: 'هەموو',\n      sumText: 'کۆ',\n    },\n    tree: {\n      emptyText: 'هیچ داتا نیە',\n    },\n    transfer: {\n      noMatch: 'هیچ داتای هاوتا نیە',\n      noData: 'هیچ داتا نیە',\n      titles: ['لیستی 1', 'لیستی 2'], // to be translated\n      filterPlaceholder: 'کلیلەوشە داخڵ بکە', // to be translated\n      noCheckedFormat: '{total} دانە', // to be translated\n      hasCheckedFormat: '{checked}/{total} هەڵبژێردراوە', // to be translated\n    },\n    image: {\n      error: 'شکستی هێنا',\n    },\n    pageHeader: {\n      title: 'گەڕانەوە', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'بەڵێ',\n      cancelButtonText: 'نەخێر',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,8DAA8D,CAAC,YAAY,CAAC,uFAAuF,CAAC,WAAW,CAAC,+QAA+Q,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,sFAAsF,CAAC,KAAK,CAAC,8DAA8D,CAAC,OAAO,CAAC,0BAA0B,CAAC,eAAe,CAAC,8SAA8S,CAAC,gBAAgB,CAAC,mQAAmQ,CAAC,eAAe,CAAC,6PAA6P,CAAC,YAAY,CAAC,+GAA+G,CAAC,UAAU,CAAC,mGAAmG,CAAC,UAAU,CAAC,iFAAiF,CAAC,SAAS,CAAC,uFAAuF,CAAC,SAAS,CAAC,qEAAqE,CAAC,OAAO,CAAC,iFAAiF,CAAC,OAAO,CAAC,+DAA+D,CAAC,QAAQ,CAAC,yDAAyD,CAAC,QAAQ,CAAC,+DAA+D,CAAC,SAAS,CAAC,+DAA+D,CAAC,SAAS,CAAC,2EAA2E,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,sCAAsC,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,wDAAwD,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,mGAAmG,CAAC,QAAQ,CAAC,uFAAuF,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,uHAAuH,CAAC,MAAM,CAAC,kFAAkF,CAAC,WAAW,CAAC,wDAAwD,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,sHAAsH,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,uHAAuH,CAAC,OAAO,CAAC,4CAA4C,CAAC,WAAW,CAAC,wDAAwD,CAAC,MAAM,CAAC,kFAAkF,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,uCAAuC,CAAC,KAAK,CAAC,2DAA2D,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,mfAAmf,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,0GAA0G,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,0GAA0G,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,4FAA4F,CAAC,KAAK,CAAC,yGAAyG,CAAC,KAAK,CAAC,0GAA0G,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,yKAAyK,CAAC,MAAM,CAAC,4CAA4C,CAAC,OAAO,CAAC,kDAAkD,CAAC,QAAQ,CAAC,0EAA0E,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,+FAA+F,CAAC,sBAAsB,CAAC,4HAA4H,CAAC,oBAAoB,CAAC,sHAAsH,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,gEAAgE,CAAC,aAAa,CAAC,gFAAgF,CAAC,WAAW,CAAC,0EAA0E,CAAC,WAAW,CAAC,gCAAgC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gEAAgE,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,qGAAqG,CAAC,MAAM,CAAC,gEAAgE,CAAC,MAAM,CAAC,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC,iBAAiB,CAAC,8FAA8F,CAAC,eAAe,CAAC,kCAAkC,CAAC,gBAAgB,CAAC,4FAA4F,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}