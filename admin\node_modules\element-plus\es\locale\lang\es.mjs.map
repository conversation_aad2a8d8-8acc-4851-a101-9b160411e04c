{"version": 3, "file": "es.mjs", "sources": ["../../../../../packages/locale/lang/es.ts"], "sourcesContent": ["export default {\n  name: 'es',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Confirmar',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Ahora',\n      today: 'Hoy',\n      cancel: 'Cancelar',\n      clear: '<PERSON><PERSON>jar',\n      confirm: 'Confirmar',\n      selectDate: 'Seleccionar fecha',\n      selectTime: 'Seleccionar hora',\n      startDate: 'Fecha Incial',\n      startTime: 'Hora Inicial',\n      endDate: 'Fecha Final',\n      endTime: 'Hora Final',\n      prevYear: 'Año Anterior',\n      nextYear: 'Próximo Año',\n      prevMonth: 'Mes Anterior',\n      nextMonth: 'Próximo Me<PERSON>',\n      year: '',\n      month1: 'enero',\n      month2: 'febrero',\n      month3: 'marzo',\n      month4: 'abril',\n      month5: 'mayo',\n      month6: 'junio',\n      month7: 'julio',\n      month8: 'agosto',\n      month9: 'septiembre',\n      month10: 'octubre',\n      month11: 'noviembre',\n      month12: 'diciembre',\n      // week: 'semana',\n      weeks: {\n        sun: 'dom',\n        mon: 'lun',\n        tue: 'mar',\n        wed: 'mié',\n        thu: 'jue',\n        fri: 'vie',\n        sat: 'sáb',\n      },\n      months: {\n        jan: 'ene',\n        feb: 'feb',\n        mar: 'mar',\n        apr: 'abr',\n        may: 'may',\n        jun: 'jun',\n        jul: 'jul',\n        aug: 'ago',\n        sep: 'sep',\n        oct: 'oct',\n        nov: 'nov',\n        dec: 'dic',\n      },\n    },\n    select: {\n      loading: 'Cargando',\n      noMatch: 'No hay datos que coincidan',\n      noData: 'Sin datos',\n      placeholder: 'Seleccionar',\n    },\n    mention: {\n      loading: 'Cargando',\n    },\n    cascader: {\n      noMatch: 'No hay datos que coincidan',\n      loading: 'Cargando',\n      placeholder: 'Seleccionar',\n      noData: 'Sin datos',\n    },\n    pagination: {\n      goto: 'Ir a',\n      pagesize: '/página',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'Aceptar',\n      cancel: 'Cancelar',\n      error: 'Entrada inválida',\n    },\n    upload: {\n      deleteTip: 'Pulse Eliminar para retirar',\n      delete: 'Eliminar',\n      preview: 'Vista Previa',\n      continue: 'Continuar',\n    },\n    table: {\n      emptyText: 'Sin Datos',\n      confirmFilter: 'Confirmar',\n      resetFilter: 'Reiniciar',\n      clearFilter: 'Despejar',\n      sumText: 'Suma',\n    },\n    tree: {\n      emptyText: 'Sin Datos',\n    },\n    transfer: {\n      noMatch: 'No hay datos que coincidan',\n      noData: 'Sin datos',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Ingresar palabra clave',\n      noCheckedFormat: '{total} artículos',\n      hasCheckedFormat: '{checked}/{total} revisados',\n    },\n    image: {\n      error: 'HA FALLADO',\n    },\n    pageHeader: {\n      title: 'Volver',\n    },\n    popconfirm: {\n      confirmButtonText: 'Si',\n      cancelButtonText: 'No',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE,UAAU;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,UAAU,EAAE,kBAAkB;AACpC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,WAAW,EAAE,aAAa;AAChC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,UAAU;AACzB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,MAAM,EAAE,WAAW;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,qBAAqB;AAClC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6BAA6B;AAC9C,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,QAAQ,EAAE,WAAW;AAC3B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,aAAa,EAAE,WAAW;AAChC,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,WAAW;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,wBAAwB;AACjD,MAAM,eAAe,EAAE,sBAAsB;AAC7C,MAAM,gBAAgB,EAAE,6BAA6B;AACrD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}