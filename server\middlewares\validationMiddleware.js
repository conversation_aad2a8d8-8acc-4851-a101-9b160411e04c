const { body, param, query, validationResult } = require('express-validator');

/**
 * 验证结果处理中间件
 */
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      code: 400,
      message: 'Input validation failed',
      data: errors.array()
    });
  }
  next();
};

/**
 * 登录验证规则
 */
const loginValidation = [
  body('username')
    .isString().withMessage('Username must be a string')
    .trim()
    .isLength({ min: 3, max: 50 }).withMessage('Username must be between 3-50 characters')
    .escape(),
  body('password')
    .isString().withMessage('Password must be a string')
    .isLength({ min: 6, max: 100 }).withMessage('Password must be between 6-100 characters'),
  validate
];

/**
 * 创建管理员验证规则
 */
const createAdminValidation = [
  body('username')
    .isString().withMessage('Username must be a string')
    .trim()
    .isLength({ min: 3, max: 50 }).withMessage('Username must be between 3-50 characters')
    .matches(/^[a-zA-Z0-9_]+$/).withMessage('Username can only contain letters, numbers and underscores')
    .escape(),
  body('password')
    .isString().withMessage('Password must be a string')
    .isLength({ min: 6, max: 100 }).withMessage('Password must be between 6-100 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/).withMessage('Password must contain at least one uppercase letter, one lowercase letter and one number'),
  body('nickname')
    .isString().withMessage('Nickname must be a string')
    .trim()
    .isLength({ min: 1, max: 50 }).withMessage('Nickname must be between 1-50 characters')
    .escape(),
  body('is_super')
    .optional()
    .isBoolean().withMessage('is_super must be a boolean'),
  body('status')
    .optional()
    .isBoolean().withMessage('status must be a boolean'),
  body('roleIds')
    .optional()
    .isArray().withMessage('roleIds must be an array')
    .custom((value) => {
      if (!value.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('roleIds must be an array of positive integers');
      }
      return true;
    }),
  validate
];

/**
 * 更新管理员验证规则
 */
const updateAdminValidation = [
  param('id')
    .isInt({ min: 1 }).withMessage('ID must be a positive integer'),
  body('nickname')
    .optional()
    .isString().withMessage('Nickname must be a string')
    .trim()
    .isLength({ min: 1, max: 50 }).withMessage('Nickname must be between 1-50 characters')
    .escape(),
  body('status')
    .optional()
    .isBoolean().withMessage('status must be a boolean'),
  body('roleIds')
    .optional()
    .isArray().withMessage('roleIds must be an array')
    .custom((value) => {
      if (!value.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('roleIds must be an array of positive integers');
      }
      return true;
    }),
  validate
];

/**
 * 更新密码验证规则
 */
const updatePasswordValidation = [
  body('old_password')
    .isString().withMessage('Old password must be a string')
    .isLength({ min: 6, max: 100 }).withMessage('Old password must be between 6-100 characters'),
  body('new_password')
    .isString().withMessage('New password must be a string')
    .isLength({ min: 6, max: 100 }).withMessage('New password must be between 6-100 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/).withMessage('New password must contain at least one uppercase letter, one lowercase letter and one number')
    .custom((value, { req }) => {
      if (value === req.body.old_password) {
        throw new Error('New password cannot be the same as old password');
      }
      return true;
    }),
  validate
];

/**
 * ID参数验证规则
 */
const idParamValidation = [
  param('id')
    .isInt({ min: 1 }).withMessage('ID must be a positive integer'),
  validate
];

/**
 * 分页查询验证规则
 */
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 }).withMessage('Page number must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 }).withMessage('Limit must be an integer between 1-100'),
  validate
];

/**
 * 用户注册验证规则
 */
const userRegisterValidation = [
  body('username')
    .isString().withMessage('Username must be a string')
    .trim()
    .isLength({ min: 11, max: 11 }).withMessage('Username must be exactly 11 digits')
    .matches(/^[0-9]{11}$/).withMessage('Username must be exactly 11 digits')
    .escape(),
  body('password')
    .isString().withMessage('Password must be a string')
    .isLength({ min: 6, max: 100 }).withMessage('Password must be between 6-100 characters'),
  body('email')
    .optional({ checkFalsy: true })
    .isEmail().withMessage('Invalid email format')
    .normalizeEmail(),
  body('invite_code')
    .isString().withMessage('Invite code must be a string')
    .trim()
    .isLength({ min: 6, max: 20 }).withMessage('Invite code must be between 6-20 characters')
    .escape(),
  validate
];

/**
 * 用户登录验证规则
 */
const userLoginValidation = [
  body('username')
    .isString().withMessage('Username must be a string')
    .trim()
    .isLength({ min: 11, max: 11 }).withMessage('Username must be exactly 11 digits')
    .matches(/^[0-9]{11}$/).withMessage('Username must be exactly 11 digits')
    .escape(),
  body('password')
    .isString().withMessage('Password must be a string')
    .isLength({ min: 6, max: 100 }).withMessage('Password must be between 6-100 characters'),
  validate
];

module.exports = {
  validate,
  loginValidation,
  createAdminValidation,
  updateAdminValidation,
  updatePasswordValidation,
  idParamValidation,
  paginationValidation,
  userRegisterValidation,
  userLoginValidation
};
