/**
 * 时间冲突测试
 * 测试方案3.1在补发时间与正常发放时间冲突时的处理
 */

const { Investment, Project, User, InvestmentProfit } = require('../models');
const sequelize = require('../config/database');
const profitSystem = require('../services/profitSystem');
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const { BalanceTracker, TestDataManager, TestAssert } = require('./test-utils');

/**
 * 测试1：设置精确时间冲突场景
 */
const testSetupTimeConflictScenario = async () => {
  console.log('\n=== 测试1：设置精确时间冲突场景 ===');
  
  try {
    // 找到一个活跃投资
    const investment = await Investment.findOne({
      where: { status: 'active' },
      include: [
        { model: Project, as: 'project' },
        { model: User, as: 'user' }
      ]
    });
    
    if (!investment) {
      console.log('❌ 没有找到活跃投资');
      return false;
    }
    
    console.log(`✅ 找到测试投资: ${investment.id}`);
    console.log(`   - 用户: ${investment.user.username} (ID: ${investment.user.id})`);
    console.log(`   - 项目: ${investment.project.name} (收益周期: ${investment.project.profit_time}小时)`);
    
    // 记录原始状态
    global.testInvestmentConflict = investment;
    global.originalLastProfitTime = investment.last_profit_time;
    global.originalUserBalance = investment.user.balance;
    
    // 设置一个精确的时间点，模拟补发和正常发放在同一时间
    const now = new Date();
    const profitCycleMs = investment.project.profit_time * 60 * 60 * 1000;
    
    // 设置最后收益时间为1个周期前，这样下次正常收益应该是现在
    const conflictTime = new Date(now.getTime() - profitCycleMs);
    
    await Investment.update(
      { last_profit_time: conflictTime },
      { where: { id: investment.id } }
    );
    
    console.log(`✅ 时间冲突场景设置完成`);
    console.log(`   - 设置最后收益时间: ${conflictTime.toISOString()}`);
    console.log(`   - 当前时间: ${now.toISOString()}`);
    console.log(`   - 预期下次收益时间: ${now.toISOString()} (与当前时间冲突)`);
    
    global.conflictTime = conflictTime;
    global.expectedNextProfitTime = now;
    
    return true;
  } catch (error) {
    console.log('❌ 设置时间冲突场景失败:', error.message);
    return false;
  }
};

/**
 * 测试2：模拟Redis任务触发正常发放
 */
const testRedisNormalDistribution = async () => {
  console.log('\n=== 测试2：模拟Redis任务触发正常发放 ===');

  try {
    const investment = global.testInvestmentConflict;
    const expectedTime = global.expectedNextProfitTime;

    console.log('🔄 模拟Redis轮询触发正常收益发放...');

    // 使用标准化工具记录发放前状态
    const beforeSnapshot = await BalanceTracker.snapshot(
      investment.user.id,
      investment.id,
      '正常发放前'
    );

    // 执行正常收益发放
    const normalResult = await profitSystem.createProfitRecord(investment, expectedTime);

    if (normalResult) {
      console.log('✅ 正常收益发放成功');
      console.log(`   - 收益ID: ${normalResult.id}`);
      console.log(`   - 金额: ${normalResult.amount}`);
      console.log(`   - 时间: ${normalResult.profit_time.toISOString()}`);

      global.normalProfitId = normalResult.id;
      global.normalProfitTime = normalResult.profit_time;
      TestDataManager.recordProfitId(normalResult.id);

      // 记录发放后状态并验证
      const afterSnapshot = await BalanceTracker.snapshot(
        investment.user.id,
        investment.id,
        '正常发放后'
      );

      // 验证余额变化
      TestAssert.assertBalanceChange(beforeSnapshot, afterSnapshot, normalResult.amount, '正常发放');

    } else {
      console.log('❌ 正常收益发放失败');
      return false;
    }

    return true;
  } catch (error) {
    console.log('❌ 模拟Redis正常发放失败:', error.message);
    return false;
  }
};

/**
 * 测试3：模拟系统重启触发补发
 */
const testSystemRestartCompensation = async () => {
  console.log('\n=== 测试3：模拟系统重启触发补发 ===');

  try {
    const investment = global.testInvestmentConflict;

    console.log('🔄 模拟系统重启，触发自动补发...');

    // 重置投资的最后收益时间到冲突时间（模拟系统重启前的状态）
    await Investment.update(
      { last_profit_time: global.conflictTime },
      { where: { id: investment.id } }
    );

    console.log(`   - 重置最后收益时间: ${global.conflictTime.toISOString()}`);

    // 使用标准化工具记录补发前状态（在重置后）
    const beforeSnapshot = await BalanceTracker.snapshot(
      investment.user.id,
      investment.id,
      '补发前（重置后）'
    );

    // 执行Redis重建（会触发自动补发）
    const rebuildResult = await profitSystem.simpleRedisRebuild(investment, investment.project);

    console.log(`📊 Redis重建结果:`);
    console.log(`   - 成功: ${rebuildResult.success}`);
    console.log(`   - 补发数量: ${rebuildResult.compensated || 0}`);
    console.log(`   - 下次收益时间: ${rebuildResult.nextProfitTime}`);

    // 记录补发后状态
    const afterSnapshot = await BalanceTracker.snapshot(
      investment.user.id,
      investment.id,
      '补发后'
    );

    // 验证补发结果
    const validation = BalanceTracker.validateChange(beforeSnapshot, afterSnapshot, 10);

    global.compensationResult = {
      compensated: rebuildResult.compensated || 0,
      balanceChange: validation.balanceChange,
      profitChange: validation.profitChange,
      isValid: validation.isValid
    };

    // 如果有补发，验证余额变化
    if (rebuildResult.compensated > 0) {
      TestAssert.assertBalanceChange(beforeSnapshot, afterSnapshot, 10, '自动补发');
    } else {
      console.log('⚠️  没有补发（可能收益已存在）');
    }

    return true;
  } catch (error) {
    console.log('❌ 模拟系统重启补发失败:', error.message);
    return false;
  }
};

/**
 * 测试4：验证时间冲突处理
 */
const testTimeConflictHandling = async () => {
  console.log('\n=== 测试4：验证时间冲突处理 ===');
  
  try {
    const investment = global.testInvestmentConflict;
    const conflictTime = global.expectedNextProfitTime;
    
    console.log('🔍 检查时间冲突处理结果...');
    
    // 查询冲突时间点的收益记录
    const conflictProfits = await InvestmentProfit.findAll({
      where: {
        investment_id: investment.id,
        profit_time: conflictTime
      },
      order: [['created_at', 'ASC']]
    });
    
    console.log(`📊 冲突时间点 ${conflictTime.toISOString()} 的收益记录:`);
    console.log(`   - 记录数量: ${conflictProfits.length}`);
    
    if (conflictProfits.length === 0) {
      console.log('❌ 没有找到任何收益记录');
      return false;
    } else if (conflictProfits.length === 1) {
      console.log('✅ 只有一条收益记录（正确）');
      console.log(`   - 收益ID: ${conflictProfits[0].id}`);
      console.log(`   - 金额: ${conflictProfits[0].amount}`);
      console.log(`   - 创建时间: ${new Date(conflictProfits[0].created_at).toISOString()}`);

      // 判断这条记录是正常发放还是补发的
      if (global.normalProfitId && conflictProfits[0].id === global.normalProfitId) {
        console.log('✅ 这是正常发放的记录');
        console.log('✅ 补发机制正确跳过了已存在的收益');
      } else {
        console.log('✅ 这是补发的记录');
        console.log('✅ 正常发放机制正确跳过了已存在的收益');
      }
      
    } else {
      console.log(`❌ 发现重复记录 (${conflictProfits.length} 条)`);
      conflictProfits.forEach((profit, index) => {
        console.log(`   ${index + 1}. ID: ${profit.id}, 金额: ${profit.amount}, 创建时间: ${new Date(profit.created_at).toISOString()}`);
      });
      return false;
    }
    
    // 验证用户总收益是否正确
    // 注意：这里应该验证的是补发阶段的收益变化，而不是整个测试的收益变化
    // 因为正常发放已经增加了10，补发应该是0（因为收益已存在）
    const expectedCompensationIncrease = 0; // 补发阶段应该是0，因为收益已存在
    const actualCompensationIncrease = global.compensationResult.balanceChange;

    if (Math.abs(actualCompensationIncrease - expectedCompensationIncrease) < 0.01) {
      console.log('✅ 补发阶段收益变化正确，没有重复发放');
      console.log(`   - 预期补发增加: ${expectedCompensationIncrease}`);
      console.log(`   - 实际补发增加: ${actualCompensationIncrease}`);
    } else {
      console.log('❌ 补发阶段收益变化不正确');
      console.log(`   - 预期补发增加: ${expectedCompensationIncrease}`);
      console.log(`   - 实际补发增加: ${actualCompensationIncrease}`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 验证时间冲突处理失败:', error.message);
    return false;
  }
};

/**
 * 测试5：验证Redis任务状态
 */
const testRedisTaskState = async () => {
  console.log('\n=== 测试5：验证Redis任务状态 ===');
  
  try {
    const investment = global.testInvestmentConflict;
    
    // 检查Redis中的任务状态
    const allTasks = await redisClient.getAllProfitTasks();
    const investmentTask = allTasks.find(task => task.investment_id === investment.id);
    
    if (investmentTask) {
      console.log('✅ Redis任务状态正常');
      console.log(`   - 投资ID: ${investmentTask.investment_id}`);
      console.log(`   - 下次收益时间: ${investmentTask.next_profit_time}`);
      
      // 验证下次收益时间是否合理
      const nextProfitTime = new Date(investmentTask.next_profit_time);
      const lastProfitTime = global.expectedNextProfitTime;
      const profitCycleMs = investment.project.profit_time * 60 * 60 * 1000;
      const expectedNextTime = new Date(lastProfitTime.getTime() + profitCycleMs);
      
      console.log(`   - 预期下次时间: ${expectedNextTime.toISOString()}`);
      console.log(`   - 实际下次时间: ${nextProfitTime.toISOString()}`);
      
      const timeDiff = Math.abs(nextProfitTime.getTime() - expectedNextTime.getTime());
      if (timeDiff < 60000) { // 允许1分钟误差
        console.log('✅ 下次收益时间设置正确');
      } else {
        console.log('⚠️  下次收益时间可能不准确');
        console.log(`   - 时间差: ${timeDiff / 1000} 秒`);
      }
      
    } else {
      console.log('❌ 没有找到Redis任务');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 验证Redis任务状态失败:', error.message);
    return false;
  }
};

/**
 * 清理测试数据
 */
const cleanupTestData = async () => {
  try {
    const investment = global.testInvestmentConflict;

    if (investment && global.originalLastProfitTime !== undefined) {
      // 恢复原始的最后收益时间
      await Investment.update(
        { last_profit_time: global.originalLastProfitTime },
        { where: { id: investment.id } }
      );

      console.log('✅ 投资状态已恢复');
    }

    // 使用标准化工具清理测试数据
    await TestDataManager.cleanup();

  } catch (error) {
    console.log('⚠️  清理测试数据时出错:', error.message);
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('开始执行时间冲突测试...\n');
  
  let passedTests = 0;
  const totalTests = 5;
  
  try {
    // 执行测试
    if (await testSetupTimeConflictScenario()) passedTests++;
    if (await testRedisNormalDistribution()) passedTests++;
    if (await testSystemRestartCompensation()) passedTests++;
    if (await testTimeConflictHandling()) passedTests++;
    if (await testRedisTaskState()) passedTests++;
    
  } catch (error) {
    console.log('\n❌ 测试执行过程中出现错误:', error.message);
  } finally {
    // 清理测试数据
    await cleanupTestData();
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有时间冲突测试通过！');
    console.log('✅ 方案3.1的时间冲突处理机制工作正常');
    console.log('✅ 用户不会因为时间冲突丢失或重复收益');
    return true;
  } else {
    console.log('⚠️  部分测试失败，请检查时间冲突处理逻辑');
    return false;
  }
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testSetupTimeConflictScenario,
  testRedisNormalDistribution,
  testSystemRestartCompensation,
  testTimeConflictHandling,
  testRedisTaskState,
  cleanupTestData
};
