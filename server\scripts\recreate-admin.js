const sequelize = require('../config/database');
const Admin = require('../models/Admin');

async function recreateAdmin() {
  try {
    console.log('重新创建admin用户...');
    
    // 删除现有的admin用户
    await Admin.destroy({ where: { username: 'admin' } });
    console.log('已删除现有admin用户');
    
    // 创建新的admin用户
    const admin = await Admin.create({
      username: 'admin',
      password: 'admin123',  // 这会触发beforeCreate hook进行加密
      nickname: '超级管理员',
      is_super: true,
      status: true
    });
    
    console.log('新admin用户创建成功');
    console.log('用户名:', admin.username);
    console.log('加密后密码:', admin.password);
    
    // 验证密码
    const isValid = await admin.validatePassword('admin123');
    console.log('密码验证结果:', isValid ? '成功' : '失败');
    
    if (isValid) {
      console.log('✅ admin用户创建成功，密码验证正常！');
    } else {
      console.log('❌ 密码验证失败');
    }
    
  } catch (error) {
    console.error('重新创建admin用户时出错:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
recreateAdmin();
