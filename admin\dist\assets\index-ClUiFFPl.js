const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/vip-WC12GZ19.js","assets/request-Cd-6Wde0.js","assets/index-t--hEgTQ.js","assets/index-LncY9lAB.js","assets/index-D6_EvlvT.css","assets/project-DkiLnx-I.js"])))=>i.map(i=>d[i]);
import{d as Ll,r as b,a as Re,q as Sl,a6 as ke,o as jl,aJ as Nl,c as f,b as o,e as i,a8 as M,a9 as P,w as n,m as ql,f as Ql,i as Ol,aa as Al,ab as $l,ac as Fl,aK as be,p as V,v as Hl,y as g,n as v,x as zl,j as _,ad as Ee,ae as De,aB as Le,af as Se,ag as Yl,ai as Bl,aj as Wl,ak as Zl,V as w,al as Gl,an as Xl,aL as Kl,M as je,A as Ne,aM as Jl,aN as ee,a7 as Y,ao as et,aO as lt,aP as tt,aQ as it,ap as at,aq as st,at as ot,E as nt,ar as rt,as as ut,h as dt,aC as mt,aR as pt,aS as vt,aT as ft,aU as ct,aV as yt,aA as bt,az as gt,a0 as B,g as u,_ as Vt}from"./index-LncY9lAB.js";/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css                *//* empty css                    *//* empty css                          *//* empty css                        *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                        */import{A as wt}from"./AttachmentSelector-YIf-YS41.js";import{f as qe,g as xt}from"./urlConfig-DWnoqxuv.js";import{getProjects as Mt,createProject as Tt,updateProject as ge,deleteProject as Qe}from"./project-DkiLnx-I.js";import{s as Pt}from"./request-Cd-6Wde0.js";/* empty css                    *//* empty css                 */import"./attachments-CNgOoo0P.js";import"./index-t--hEgTQ.js";const _t=()=>Pt({url:"/api/admin/config/all",method:"get"}),ht={class:"projects-container"},Ut={class:"tabs"},Ct=["onClick"],It={class:"toolbar"},Rt={class:"toolbar-left"},kt={class:"toolbar-right"},Et={class:"table-wrapper"},Dt={class:"image-error"},Lt={key:1},St={class:"custom-value"},jt={class:"custom-value"},Nt={key:0,class:"status-hint"},qt={key:0},Qt={key:1},Ot={key:0},At={key:1},$t={key:0},Ft={key:1},Ht={class:"operation-buttons-container"},zt={class:"pagination-container"},Yt={class:"form-row"},Bt={class:"form-row"},Wt={class:"form-row"},Zt={class:"form-row"},Gt={class:"form-row"},Xt={class:"form-row"},Kt={class:"form-row"},Jt={class:"preset-buttons"},ei={class:"form-row"},li={class:"form-row"},ti={class:"form-row"},ii={key:0,class:"form-row"},ai={key:1,class:"form-row"},si={class:"form-row"},oi={class:"form-row"},ni={class:"form-row"},ri={key:0,class:"form-row"},ui={class:"editor-wrapper"},di={class:"image-upload-container"},mi={class:"image-preview-wrapper"},pi={class:"image-actions"},vi={class:"dialog-footer"},fi={key:0,class:"project-detail"},ci={class:"project-header"},yi={class:"project-info"},bi={class:"project-status"},gi={class:"project-image-container"},Vi=["src"],wi={class:"project-description"},xi={class:"project-stats"},Mi={class:"stat-item"},Ti={key:0,class:"stat-value"},Pi={class:"custom-value"},_i={key:1,class:"stat-value"},hi={class:"stat-item"},Ui={class:"stat-value"},Ci={class:"stat-item"},Ii={key:0,class:"stat-value"},Ri={class:"custom-value"},ki={key:1,class:"stat-value"},Ei={class:"stat-item"},Di={class:"stat-value"},Li={class:"stat-item"},Si={class:"stat-value"},ji={class:"stat-item"},Ni={class:"stat-value"},qi={class:"project-investments"},Qi={class:"dialog-footer"},Oi={class:"dialog-footer"},Ai={class:"filter-container"},$i={class:"filter-section"},Fi={class:"section-content"},Hi={class:"filter-grid"},zi={class:"filter-item"},Yi={class:"filter-item"},Bi={class:"filter-item"},Wi={class:"filter-item"},Zi={class:"filter-item"},Gi={class:"filter-section"},Xi={class:"section-content"},Ki={class:"filter-grid"},Ji={class:"filter-item"},ea={class:"range-inputs"},la={class:"filter-item"},ta={class:"range-inputs"},ia={class:"filter-item"},aa={class:"range-inputs"},sa={class:"filter-item"},oa={class:"range-inputs"},na={class:"filter-item"},ra={class:"filter-item"},ua={class:"filter-item"},da={class:"filter-section"},ma={class:"section-content"},pa={class:"filter-grid"},va={class:"filter-item"},fa={class:"range-inputs"},ca={class:"filter-item"},ya={class:"filter-item"},ba={class:"range-inputs"},ga={class:"filter-item"},Va={class:"range-inputs"},wa={class:"filter-section"},xa={class:"section-content"},Ma={class:"filter-grid"},Ta={class:"filter-item"},Pa={class:"range-inputs"},_a={class:"filter-item"},ha={class:"range-inputs"},Ua={class:"filter-item"},Ca={class:"range-inputs"},Ia={class:"filter-item"},Ra={class:"range-inputs"},ka={class:"filter-item"},Ea={class:"range-inputs"},Da={class:"filter-item"},La={class:"range-inputs"},Sa={class:"filter-section"},ja={class:"section-content"},Na={class:"filter-grid"},qa={class:"filter-item"},Qa={class:"range-inputs"},Oa={class:"filter-item"},Aa={class:"filter-item"},$a={class:"filter-item"},Fa={class:"filter-item"},Ha={class:"filter-item"},za={class:"filter-section"},Ya={class:"section-content"},Ba={class:"filter-grid"},Wa={class:"filter-item"},Za={class:"filter-item"},Ga={class:"filter-item"},Xa={class:"filter-item"},Ka={class:"range-inputs"},Ja={class:"filter-item"},es={class:"range-inputs"},ls={class:"filter-item"},ts={class:"range-inputs"},is={class:"filter-section"},as={class:"section-content"},ss={class:"filter-grid"},os={class:"filter-item"},ns={class:"filter-item"},rs={class:"filter-footer"},Oe="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMzAwIDIwMCI+PHJlY3Qgd2lkdGg9IjMwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNlZWUiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZmlsbD0iIzk5OSI+5peg5Zu+54mHPC90ZXh0Pjwvc3ZnPg==",Ae=40,us=Ll({__name:"index",setup(ds){const $e=[{label:"全部",value:"all"},{label:"正常",value:"normal"},{label:"下架",value:"offline"}],E=b("normal"),N=b(""),Ve=b(""),L=b(1),F=b(10),H=b(!1),we=b(0),le=b(!1),R=b([]),q=b(!1),Q=b(!1),O=b(!1),W=b(!1),A=b(null),te=b("basic"),ie=b(!1),Z=b(""),s=Re({id:0,name:"",type:"产品",category:"基础",description:"",expectedReturn:0,minInvestment:0,maxInvestment:0,price:0,sellPrice:0,priceType:"",paymentMethod:"",purchaseTime:"",purchaseTimeRange:[],duration:1,durationUnit:"天",profitTime:24,customReturnEnabled:0,customReturnHours:0,customReturnRate:0,quantity:0,actualQuantity:0,sortOrder:1,maxPurchaseTimes:0,simultaneousPurchases:0,maxProfitTimes:0,soldQuantity:0,vipLevel:"",vipLevelNumber:0,vipType:"",vipTypeOperator:"=",returnPrincipal:!0,isFree:!1,currency:"CNY",status:1,sellStatus:1,commissionEnabled:1,level1Commission:0,level2Commission:0,level3Commission:0,weeklyProfitDays:["1","2","3","4","5","6","7"],imageUrl:"",videoUrl:"",imageId:null,videoId:null}),t=Re({id:"",type:"",category:"",name:"",hasVideo:"",priceMin:void 0,priceMax:void 0,sellPriceMin:void 0,sellPriceMax:void 0,minInvestmentMin:void 0,minInvestmentMax:void 0,maxInvestmentMin:void 0,maxInvestmentMax:void 0,priceType:"",paymentMethod:"",purchaseTime:"",durationMin:void 0,durationMax:void 0,customReturnEnabled:"",expectedReturnMin:void 0,expectedReturnMax:void 0,profitTimeMin:void 0,profitTimeMax:void 0,quantityMin:void 0,quantityMax:void 0,actualQuantityMin:void 0,actualQuantityMax:void 0,maxPurchaseTimesMin:void 0,maxPurchaseTimesMax:void 0,simultaneousPurchasesMin:void 0,simultaneousPurchasesMax:void 0,maxProfitTimesMin:void 0,maxProfitTimesMax:void 0,soldQuantityMin:void 0,soldQuantityMax:void 0,vipLevelMin:void 0,vipLevelMax:void 0,vipType:"",vipTypeOperator:"",weeklyProfitDays:"",returnPrincipal:"",isFree:"",currency:"",status:"",sellStatus:"",commissionEnabled:"",level1CommissionMin:void 0,level1CommissionMax:void 0,level2CommissionMin:void 0,level2CommissionMax:void 0,level3CommissionMin:void 0,level3CommissionMax:void 0,createTimeRange:[],updateTimeRange:[]}),Fe=b([]),xe=b([{id:1,name:"VIP 0",level:0},{id:2,name:"VIP 1",level:1},{id:3,name:"VIP 2",level:2},{id:4,name:"VIP 3",level:3},{id:5,name:"VIP 4",level:4},{id:6,name:"VIP 5",level:5},{id:7,name:"VIP 6",level:6},{id:8,name:"VIP 7",level:7},{id:9,name:"VIP 8",level:8},{id:10,name:"VIP 9",level:9}]),ae=b([{id:1,name:"等于",value:"="},{id:2,name:"大于等于",value:">="},{id:3,name:"小于等于",value:"<="}]),se=b([{id:1,name:"产品",value:"产品"}]),oe=b([{id:1,name:"基础",value:"基础"}]),ne=b([{id:1,name:"法币",value:"CNY"}]),re=b([{id:1,name:"固定价格",value:"固定价格"}]),ue=b([{id:1,name:"余额支付",value:"余额支付"},{id:2,name:"银行卡支付",value:"银行卡支付"},{id:3,name:"混合支付",value:"混合支付"}]),de=b([{id:1,name:"正常",value:1},{id:0,name:"下架",value:0}]),me=b([{id:0,name:"待售",value:0},{id:1,name:"在售",value:1},{id:2,name:"售完",value:2}]),Me=b([{id:1,name:"周一",value:"1"},{id:2,name:"周二",value:"2"},{id:3,name:"周三",value:"3"},{id:4,name:"周四",value:"4"},{id:5,name:"周五",value:"5"},{id:6,name:"周六",value:"6"},{id:7,name:"周日",value:"7"}]),He=async()=>{try{const a=await be(()=>import("./vip-WC12GZ19.js"),__vite__mapDeps([0,1,2,3,4])).then(e=>e.getVipLevels());a&&a.data&&Array.isArray(a.data)&&(xe.value=a.data)}catch{}},ze=async()=>{try{const a=await be(()=>import("./vip-WC12GZ19.js"),__vite__mapDeps([0,1,2,3,4])).then(e=>e.getVipTypes());a&&a.data&&Array.isArray(a.data)&&(ae.value=a.data)}catch{}},I=b([]),Ye=b([]),Be=Sl(()=>{let a=I.value;if(E.value!=="all"&&(E.value==="normal"?a=a.filter(e=>e.status===1):E.value==="offline"&&(a=a.filter(e=>e.status===0))),t.id&&(a=a.filter(e=>String(e.id).includes(t.id))),t.type&&(a=a.filter(e=>e.type===t.type)),t.category&&(a=a.filter(e=>e.category===t.category)),t.name&&(a=a.filter(e=>e.name.toLowerCase().includes(t.name.toLowerCase()))),t.hasVideo==="true"?a=a.filter(e=>e.videoUrl&&e.videoUrl.trim()!==""):t.hasVideo==="false"&&(a=a.filter(e=>!e.videoUrl||e.videoUrl.trim()==="")),t.priceMin!==void 0&&(a=a.filter(e=>e.price>=t.priceMin)),t.priceMax!==void 0&&(a=a.filter(e=>e.price<=t.priceMax)),t.sellPriceMin!==void 0&&(a=a.filter(e=>e.sellPrice>=t.sellPriceMin)),t.sellPriceMax!==void 0&&(a=a.filter(e=>e.sellPrice<=t.sellPriceMax)),t.minInvestmentMin!==void 0&&(a=a.filter(e=>e.minInvestment>=t.minInvestmentMin)),t.minInvestmentMax!==void 0&&(a=a.filter(e=>e.minInvestment<=t.minInvestmentMax)),t.maxInvestmentMin!==void 0&&(a=a.filter(e=>e.maxInvestment>=t.maxInvestmentMin)),t.maxInvestmentMax!==void 0&&(a=a.filter(e=>e.maxInvestment<=t.maxInvestmentMax)),t.priceType&&(a=a.filter(e=>e.priceType===t.priceType)),t.paymentMethod&&(a=a.filter(e=>e.paymentMethod===t.paymentMethod)),t.purchaseTime&&(a=a.filter(e=>e.purchaseTime===t.purchaseTime)),t.durationMin!==void 0&&(a=a.filter(e=>e.duration>=t.durationMin)),t.durationMax!==void 0&&(a=a.filter(e=>e.duration<=t.durationMax)),t.customReturnEnabled==="1"?a=a.filter(e=>e.customReturnEnabled===1):t.customReturnEnabled==="0"&&(a=a.filter(e=>e.customReturnEnabled===0)),t.expectedReturnMin!==void 0&&(a=a.filter(e=>e.expectedReturn>=t.expectedReturnMin)),t.expectedReturnMax!==void 0&&(a=a.filter(e=>e.expectedReturn<=t.expectedReturnMax)),t.profitTimeMin!==void 0&&(a=a.filter(e=>e.profitTime>=t.profitTimeMin)),t.profitTimeMax!==void 0&&(a=a.filter(e=>e.profitTime<=t.profitTimeMax)),t.quantityMin!==void 0&&(a=a.filter(e=>e.quantity>=t.quantityMin)),t.quantityMax!==void 0&&(a=a.filter(e=>e.quantity<=t.quantityMax)),t.actualQuantityMin!==void 0&&(a=a.filter(e=>e.actualQuantity>=t.actualQuantityMin)),t.actualQuantityMax!==void 0&&(a=a.filter(e=>e.actualQuantity<=t.actualQuantityMax)),t.maxPurchaseTimesMin!==void 0&&(a=a.filter(e=>e.maxPurchaseTimes>=t.maxPurchaseTimesMin)),t.maxPurchaseTimesMax!==void 0&&(a=a.filter(e=>e.maxPurchaseTimes<=t.maxPurchaseTimesMax)),t.simultaneousPurchasesMin!==void 0&&(a=a.filter(e=>e.simultaneousPurchases>=t.simultaneousPurchasesMin)),t.simultaneousPurchasesMax!==void 0&&(a=a.filter(e=>e.simultaneousPurchases<=t.simultaneousPurchasesMax)),t.maxProfitTimesMin!==void 0&&(a=a.filter(e=>e.maxProfitTimes>=t.maxProfitTimesMin)),t.maxProfitTimesMax!==void 0&&(a=a.filter(e=>e.maxProfitTimes<=t.maxProfitTimesMax)),t.soldQuantityMin!==void 0&&(a=a.filter(e=>e.soldQuantity>=t.soldQuantityMin)),t.soldQuantityMax!==void 0&&(a=a.filter(e=>e.soldQuantity<=t.soldQuantityMax)),t.vipLevelMin!==void 0&&(a=a.filter(e=>(parseInt(e.vipLevel.replace("VIP",""))||0)>=t.vipLevelMin)),t.vipLevelMax!==void 0&&(a=a.filter(e=>(parseInt(e.vipLevel.replace("VIP",""))||0)<=t.vipLevelMax)),t.vipTypeOperator&&(t.vipTypeOperator==="="||(t.vipTypeOperator===">="?a=a.filter(e=>(parseInt(e.vipLevel.replace("VIP",""))||0)>=0):t.vipTypeOperator==="<="&&(a=a.filter(e=>(parseInt(e.vipLevel.replace("VIP",""))||0)<=10)))),t.returnPrincipal==="true"?a=a.filter(e=>e.returnPrincipal===!0):t.returnPrincipal==="false"&&(a=a.filter(e=>e.returnPrincipal===!1)),t.isFree==="true"?a=a.filter(e=>e.isFree===!0):t.isFree==="false"&&(a=a.filter(e=>e.isFree===!1)),t.currency&&(a=a.filter(e=>e.currency===t.currency)),t.status&&(a=a.filter(e=>String(e.status)===t.status)),t.sellStatus&&(a=a.filter(e=>String(e.sellStatus)===t.sellStatus)),t.commissionEnabled&&(a=a.filter(e=>String(e.commissionEnabled)===t.commissionEnabled)),t.level1CommissionMin!==void 0&&(a=a.filter(e=>e.level1Commission>=t.level1CommissionMin)),t.level1CommissionMax!==void 0&&(a=a.filter(e=>e.level1Commission<=t.level1CommissionMax)),t.level2CommissionMin!==void 0&&(a=a.filter(e=>e.level2Commission>=t.level2CommissionMin)),t.level2CommissionMax!==void 0&&(a=a.filter(e=>e.level2Commission<=t.level2CommissionMax)),t.level3CommissionMin!==void 0&&(a=a.filter(e=>e.level3Commission>=t.level3CommissionMin)),t.level3CommissionMax!==void 0&&(a=a.filter(e=>e.level3Commission<=t.level3CommissionMax)),t.createTimeRange&&t.createTimeRange.length===2){const e=new Date(t.createTimeRange[0]).getTime(),r=new Date(t.createTimeRange[1]).getTime();a=a.filter(c=>{const d=new Date(c.createTime).getTime();return d>=e&&d<=r})}if(t.updateTimeRange&&t.updateTimeRange.length===2){const e=new Date(t.updateTimeRange[0]).getTime(),r=new Date(t.updateTimeRange[1]).getTime();a=a.filter(c=>{const d=new Date(c.updateTime).getTime();return d>=e&&d<=r})}return a}),Te=a=>{switch(a){case"产品":return"success";default:return""}},pe=()=>{N.value!==Ve.value&&(Ve.value=N.value,L.value=1,D())},We=a=>{L.value=a,D()},Pe=a=>{F.value=a,L.value=1,D()},Ze=a=>{R.value=a},D=async()=>{le.value=!0;try{const a={page:L.value,limit:F.value};E.value!=="all"&&(E.value==="normal"?a.status=1:E.value==="offline"&&(a.status=0)),N.value&&(a.keyword=N.value),t.id&&(a.id=t.id),t.type&&(a.type=t.type),t.category&&(a.category=t.category),t.name&&(a.name=t.name),t.hasVideo==="true"&&(a.hasVideo=!0),t.hasVideo==="false"&&(a.hasVideo=!1),t.vipLevelMin!==void 0&&(a.vipLevelMin=t.vipLevelMin),t.vipLevelMax!==void 0&&(a.vipLevelMax=t.vipLevelMax),t.vipTypeOperator&&(a.vipTypeOperator=t.vipTypeOperator),t.weeklyProfitDays&&(a.weeklyProfitDays=t.weeklyProfitDays),t.returnPrincipal&&(a.returnPrincipal=t.returnPrincipal),t.isFree&&(a.isFree=t.isFree),t.currency&&(a.currency=t.currency),t.sellStatus&&(a.sellStatus=t.sellStatus),t.commissionEnabled&&(a.commissionEnabled=t.commissionEnabled),t.customReturnEnabled&&(a.customReturnEnabled=t.customReturnEnabled),t.createTimeRange&&t.createTimeRange.length===2&&(a.createTimeStart=t.createTimeRange[0],a.createTimeEnd=t.createTimeRange[1]),t.updateTimeRange&&t.updateTimeRange.length===2&&(a.updateTimeStart=t.updateTimeRange[0],a.updateTimeEnd=t.updateTimeRange[1]);const{total:e,items:r}=await Mt(a);I.value=r,we.value=e}catch(a){console.error("获取项目列表失败:",a),V.error(a.message||"获取项目列表失败")}finally{le.value=!1}},Ge=a=>{switch(a){case 0:return"待售";case 1:return"在售";case 2:return"售完";default:return"未知"}},Xe=a=>{switch(a){case 0:return"info";case 1:return"success";case 2:return"danger";default:return""}},Ke=a=>{switch(a){case"=":return"等于";case">=":return"大于等于";case"<=":return"小于等于";default:return a||"未设置"}},S=a=>isNaN(a)||a===void 0||a===null?"0.00":new Intl.NumberFormat("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(a),Je=()=>{W.value=!0,_e(),q.value=!0},ve=a=>{if(!a){if(R.value.length!==1){V.warning("请选择一条记录进行编辑");return}a=R.value[0]}W.value=!1,_e(),te.value="basic";const e=a;let r=[];e.purchaseTime&&e.purchaseTime.includes("-")?r=e.purchaseTime.split("-"):r=["00:00:00","23:59:59"],Object.assign(s,{id:e.id,weeklyProfitDays:e.weeklyProfitDays?e.weeklyProfitDays.split(","):["1","2","3","4","5","6","7"],name:e.name,type:"产品",category:"基础",description:e.description,expectedReturn:Number(e.expectedReturn),minInvestment:Number(e.minInvestment),maxInvestment:Number(e.maxInvestment),price:Number(e.price),sellPrice:Number(e.sellPrice),priceType:"固定价格",paymentMethod:e.paymentMethod,purchaseTime:e.purchaseTime,purchaseTimeRange:r,duration:Number(e.duration),durationUnit:e.durationUnit,profitTime:Number(e.profitTime),customReturnEnabled:e.customReturnEnabled===1?1:0,customReturnHours:Number(e.customReturnHours)||0,customReturnRate:Number(e.customReturnRate)||0,quantity:Number(e.quantity),actualQuantity:Number(e.actualQuantity),sortOrder:Number(e.sortOrder),maxPurchaseTimes:Number(e.maxPurchaseTimes),simultaneousPurchases:Number(e.simultaneousPurchases),maxProfitTimes:Number(e.maxProfitTimes),soldQuantity:Number(e.soldQuantity),vipLevel:e.vipLevel,vipLevelNumber:parseInt(e.vipLevel.replace("VIP",""))||0,vipTypeOperator:e.vipTypeOperator||"=",returnPrincipal:!!e.returnPrincipal,isFree:!!e.isFree,currency:e.currency,status:Number(e.status),sellStatus:Number(e.sellStatus),commissionEnabled:Number(e.commissionEnabled),level1Commission:Number(e.level1Commission),level2Commission:Number(e.level2Commission),level3Commission:Number(e.level3Commission),imageUrl:e.imageUrl,videoUrl:e.videoUrl,imageId:e.image_id,videoId:e.video_id}),q.value=!0},el=()=>{if(R.value.length===0){V.warning("请至少选择一条记录进行删除");return}B.confirm(`确定要删除选中的 ${R.value.length} 个项目吗？此操作不可逆。`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Q.value=!0}).catch(()=>{V.info("已取消删除")})},ll=a=>{H.value&&(H.value=!1),ve(a)},tl=a=>{B.confirm(`确定要删除项目 "${a.name}" 吗？此操作不可逆。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{A.value=a,Q.value=!0}).catch(()=>{V.info("已取消删除")})},_e=()=>{s.id=0,s.name="",s.type="产品",s.category="基础",s.description="",s.expectedReturn=5,s.minInvestment=100,s.maxInvestment=1e5,s.price=100,s.sellPrice=0,s.priceType="固定价格",s.paymentMethod="余额支付",s.purchaseTime="00:00-24:00",s.purchaseTimeRange=["00:00:00","23:59:59"],s.duration=600,s.profitTime=24,s.customReturnEnabled=0,s.customReturnHours=0,s.customReturnRate=0,s.quantity=0,s.actualQuantity=0,s.sortOrder=100,s.maxPurchaseTimes=0,s.simultaneousPurchases=0,s.maxProfitTimes=0,s.soldQuantity=0,s.vipLevel="",s.vipLevelNumber=0,s.vipTypeOperator="=",s.returnPrincipal=!1,s.isFree=!1,s.currency="CNY",s.status=1,s.sellStatus=1,s.commissionEnabled=0,s.level1Commission=3,s.level2Commission=1.5,s.level3Commission=.5,s.weeklyProfitDays=["1","2","3","4","5","6","7"],s.imageUrl="",s.videoUrl="",s.imageId=null,s.videoId=null,Fe.value=[]};ke(()=>s.vipLevelNumber,a=>{a!=null&&(s.vipLevel=a>0?`VIP${a}`:"")});const il=a=>{a>0&&(s.minInvestment=a)},al=a=>{a>0?s.actualQuantity=Math.max(0,a-s.soldQuantity):s.actualQuantity=0},G=a=>xt(a,Oe),sl=async a=>{if(a.imageUrl){const e=await qe(a.imageUrl);e&&e!==a.imageUrl&&(a.imageUrl=e,I.value=[...I.value])}},ol=()=>{s.weeklyProfitDays=["1","2","3","4","5"]},nl=()=>{s.weeklyProfitDays=["6","7"]},rl=()=>{s.weeklyProfitDays=["1","2","3","4","5","6","7"]},h=b(!1),ul=async()=>{var a;if(!h.value){h.value=!0;try{const e=[];if(s.name||e.push("项目名称"),s.type||e.push("项目类型"),s.price<=0&&e.push("价格"),s.sellPrice<0&&e.push("卖出价格"),(!s.purchaseTimeRange||s.purchaseTimeRange.length!==2)&&e.push("购买时间"),(!s.minInvestment||s.minInvestment<=0)&&e.push("最少投资金额"),(!s.maxInvestment||s.maxInvestment<=0)&&e.push("最多投资金额"),s.minInvestment>s.maxInvestment&&e.push("最少投资金额不能大于最多投资金额"),(!s.expectedReturn||s.expectedReturn<=0)&&e.push("收益率"),s.duration<=0&&e.push("投资周期"),(!s.profitTime||s.profitTime<=0)&&e.push("收益时间"),s.customReturnEnabled===1&&((!s.customReturnHours||s.customReturnHours<=0)&&e.push("自定义结算时间"),(!s.customReturnRate||s.customReturnRate<=0)&&e.push("自定义收益率")),s.maxProfitTimes<0&&e.push("最多收益次数"),(s.vipLevelNumber<0||s.vipLevelNumber>9)&&e.push("VIP级别"),s.quantity<0&&e.push("项目数量"),s.actualQuantity<0&&e.push("实际数量"),s.maxPurchaseTimes<0&&e.push("最多购买次数"),s.simultaneousPurchases<0&&e.push("同时购买数量"),s.commissionEnabled===1&&(s.level1Commission<0&&e.push("一级返佣"),s.level2Commission<0&&e.push("二级返佣"),s.level3Commission<0&&e.push("三级返佣")),e.length>0){V.error(`以下字段未正确填写：${e.join("、")}`),h.value=!1;return}if(s.quantity>0&&s.actualQuantity>s.quantity){V.error("实际数量不能大于项目总数量"),h.value=!1;return}if(s.soldQuantity>s.quantity&&s.quantity>0){V.error("已售数量不能大于项目总数量"),h.value=!1;return}if(s.description&&/<script|<iframe|javascript:/i.test(s.description)){V.error("项目描述包含不安全的内容"),h.value=!1;return}try{s.purchaseTimeRange&&s.purchaseTimeRange.length===2?s.purchaseTime=`${s.purchaseTimeRange[0]}-${s.purchaseTimeRange[1]}`:s.purchaseTime="00:00:00-23:59:59";const r={name:s.name,type:s.type,category:s.category,description:s.description,expected_return:s.expectedReturn,min_investment:s.minInvestment,max_investment:s.maxInvestment,price:s.price,sell_price:s.sellPrice,price_type:s.priceType,payment_method:s.paymentMethod,purchase_time:s.purchaseTime,duration:s.duration,profit_time:s.profitTime,quantity:s.quantity,actual_quantity:s.actualQuantity,sort_order:s.sortOrder,max_purchase_times:s.maxPurchaseTimes,simultaneous_purchases:s.simultaneousPurchases,max_profit_times:s.maxProfitTimes,vip_level_number:s.vipLevelNumber,vip_type_operator:s.vipTypeOperator,currency:s.currency,status:s.status===1,sell_status:s.sellStatus,weekly_profit_days:s.weeklyProfitDays.join(","),return_principal:s.returnPrincipal,is_free:s.isFree,commission_enabled:s.commissionEnabled===1,level1_commission:s.level1Commission,level2_commission:s.level2Commission,level3_commission:s.level3Commission,image_id:s.imageId,video_id:s.videoId,custom_return_enabled:s.customReturnEnabled,custom_return_hours:s.customReturnHours,custom_return_rate:s.customReturnRate};console.log("保存项目数据:",r);const c=((a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content"))||"",d=c?{"X-CSRF-TOKEN":c}:{};W.value?(await Tt(r),V.success("创建成功")):(await ge(s.id,r),V.success("保存成功")),q.value=!1,D()}catch(r){console.error("保存项目失败:",r),V.error(r.message||"保存失败")}finally{h.value=!1}}catch(e){console.error("表单验证失败:",e),V.error(e.message||"表单验证失败"),h.value=!1}}},dl=async()=>{if(!h.value){h.value=!0;try{if(A.value)await Qe(A.value.id),console.log(`操作日志: 管理员删除了项目 "${A.value.name}"，ID: ${A.value.id}，时间: ${new Date().toLocaleString()}`),A.value=null;else{const a=R.value.map(r=>r.id),e=R.value.map(r=>r.name).join(", ");await Promise.all(a.map(r=>Qe(r))),console.log(`操作日志: 管理员批量删除了项目，ID列表: ${a.join(", ")}，名称: ${e}，时间: ${new Date().toLocaleString()}`),R.value=[]}V.success("删除成功"),Q.value=!1,D()}catch(a){console.error("删除项目失败:",a),V.error(a.message||"删除失败")}finally{h.value=!1}}},ml=async(a,e)=>{B.confirm(`确定要${e===1?"开启":"关闭"}项目 "${a.name}" 吗？`,"状态变更确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:e===1?"success":"warning"}).then(async()=>{try{if(h.value)return;h.value=!0;const r={status:e===1};await ge(a.id,r),console.log(`操作日志: 管理员${e===1?"开启":"关闭"}了项目 "${a.name}"，ID: ${a.id}，时间: ${new Date().toLocaleString()}`),V.success(`项目状态已${e===1?"开启":"关闭"}`)}catch(r){console.error("更新项目状态失败:",r),V.error(r.message||"更新状态失败"),a.status=e===1?0:1}finally{h.value=!1}}).catch(()=>{a.status=e===1?0:1,V.info("已取消状态变更")})},pl=async(a,e)=>{B.confirm(`确定要${e===1?"开启":"关闭"}项目 "${a.name}" 的返佣功能吗？`,"返佣功能变更确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{if(h.value)return;h.value=!0;const r={commission_enabled:e===1};await ge(a.id,r),console.log(`操作日志: 管理员${e===1?"开启":"关闭"}了项目 "${a.name}" 的返佣功能，ID: ${a.id}，时间: ${new Date().toLocaleString()}`),V.success(`返佣功能已${e===1?"开启":"关闭"}`)}catch(r){console.error("更新返佣开关状态失败:",r),V.error(r.message||"更新返佣开关状态失败"),a.commissionEnabled=e===1?0:1}finally{h.value=!1}}).catch(()=>{a.commissionEnabled=e===1?0:1,V.info("已取消返佣功能变更")})},vl=()=>{V.info("功能尚未实现")},fl=()=>{O.value=!0},cl=()=>{t.id="",t.type="",t.category="",t.name="",t.hasVideo="",t.priceMin=void 0,t.priceMax=void 0,t.sellPriceMin=void 0,t.sellPriceMax=void 0,t.minInvestmentMin=void 0,t.minInvestmentMax=void 0,t.maxInvestmentMin=void 0,t.maxInvestmentMax=void 0,t.priceType="",t.paymentMethod="",t.purchaseTime="",t.durationMin=void 0,t.durationMax=void 0,t.customReturnEnabled="",t.expectedReturnMin=void 0,t.expectedReturnMax=void 0,t.profitTimeMin=void 0,t.profitTimeMax=void 0,t.quantityMin=void 0,t.quantityMax=void 0,t.actualQuantityMin=void 0,t.actualQuantityMax=void 0,t.maxPurchaseTimesMin=void 0,t.maxPurchaseTimesMax=void 0,t.simultaneousPurchasesMin=void 0,t.simultaneousPurchasesMax=void 0,t.maxProfitTimesMin=void 0,t.maxProfitTimesMax=void 0,t.soldQuantityMin=void 0,t.soldQuantityMax=void 0,t.vipLevelMin=void 0,t.vipLevelMax=void 0,t.vipType="",t.vipTypeOperator="",t.returnPrincipal="",t.isFree="",t.currency="",t.status="",t.sellStatus="",t.commissionEnabled="",t.level1CommissionMin=void 0,t.level1CommissionMax=void 0,t.level2CommissionMin=void 0,t.level2CommissionMax=void 0,t.level3CommissionMin=void 0,t.level3CommissionMax=void 0,t.createTimeRange=[],t.updateTimeRange=[]},yl=()=>{O.value=!1,L.value=1,D(),V.success("筛选条件已应用")},bl=a=>{Z.value=a,ie.value=!0},gl=a=>{console.log("选中附件:",a),Z.value==="image"?(s.imageUrl=a.url,s.imageId=a.id,console.log("已选择图片ID:",a.id,"图片URL:",a.url),V.success("已选择图片")):Z.value==="video"&&(s.videoUrl=a.url,s.videoId=a.id,console.log("已选择视频ID:",a.id,"视频URL:",a.url),V.success("已选择视频"))},Vl=a=>{a.videoUrl?B.alert(`<video controls style="width: 100%" src="${a.videoUrl}"></video>`,"视频预览",{dangerouslyUseHTMLString:!0,confirmButtonText:"关闭"}):V.info("该项目没有视频")},k=b(-1),j=b(null),X=b(!1),fe=b(0),wl=(a,e)=>{k.value=e,X.value=!0,fe.value=a.clientY,document.body.style.userSelect="none",document.addEventListener("mousemove",ce),document.addEventListener("mouseup",ye);const r=document.querySelectorAll(".el-table__row");r[k.value]&&(j.value=r[k.value],j.value.classList.add("row-dragging"))},ce=a=>{if(!X.value||!j.value)return;const e=a.clientY-fe.value;j.value.style.transform=`translateY(${e}px)`;const r=Math.round(e/Ae)+k.value;document.querySelectorAll(".el-table__row").forEach((d,m)=>{m===r&&m!==k.value?d.classList.add("row-drop-target"):d.classList.remove("row-drop-target")})},ye=async a=>{if(!X.value||k.value===-1)return;document.removeEventListener("mousemove",ce),document.removeEventListener("mouseup",ye),document.body.style.userSelect="",j.value&&(j.value.style.transform="",j.value.classList.remove("row-dragging"));const e=a.clientY-fe.value,r=Math.round(e/Ae)+k.value;if(r>=0&&r<I.value.length&&r!==k.value)try{V({message:"正在更新排序...",type:"info",duration:1e3});const d=I.value.splice(k.value,1)[0];I.value.splice(r,0,d),await xl(),V.success("排序已更新并保存")}catch(d){console.error("更新排序失败:",d),V.error("更新排序失败，请重试")}document.querySelectorAll(".el-table__row").forEach(d=>d.classList.remove("row-drop-target")),X.value=!1,k.value=-1,j.value=null},xl=async()=>{try{const a=Math.max(...I.value.map(c=>c.sortOrder)),e=I.value.map((c,d)=>{const m=a-d;return c.sortOrder=m,{id:c.id,sort_order:m}}),{updateProjectSortOrders:r}=await be(async()=>{const{updateProjectSortOrders:c}=await import("./project-DkiLnx-I.js");return{updateProjectSortOrders:c}},__vite__mapDeps([5,1,2,3,4]));await r(e),console.log("项目排序已保存到服务器")}catch(a){console.error("保存项目排序失败:",a),V.error("保存排序失败，请重试")}};ke(E,()=>{L.value=1,D()});const Ml=async()=>{try{const a=await _t();if(a&&a.data){const e=a.data;e.projectTypes&&Array.isArray(e.projectTypes)&&(se.value=e.projectTypes),e.projectCategories&&Array.isArray(e.projectCategories)&&(oe.value=e.projectCategories),e.currencyTypes&&Array.isArray(e.currencyTypes)&&(ne.value=e.currencyTypes),e.priceTypes&&Array.isArray(e.priceTypes)&&(re.value=e.priceTypes),e.paymentMethods&&Array.isArray(e.paymentMethods)&&(ue.value=e.paymentMethods),e.statusOptions&&Array.isArray(e.statusOptions)&&(de.value=e.statusOptions),e.sellStatusOptions&&Array.isArray(e.sellStatusOptions)&&(me.value=e.sellStatusOptions),e.weeklyProfitDays&&Array.isArray(e.weeklyProfitDays)&&(Me.value=e.weeklyProfitDays),console.log("配置数据加载成功:",e)}}catch(a){console.error("获取配置数据失败，使用默认数据:",a)}};return jl(async()=>{if(await D(),await He(),await ze(),await Ml(),I.value.length>0){for(const a of I.value)if(a.imageUrl){const e=await qe(a.imageUrl);e&&e!==a.imageUrl&&(a.imageUrl=e)}}}),Nl(()=>{document.removeEventListener("mousemove",ce),document.removeEventListener("mouseup",ye)}),(a,e)=>{const r=zl,c=ql,d=Ol,m=Gl,C=Xl,he=Kl,Tl=Ne("VideoPlay"),K=Jl,$=et,Pl=lt,Ue=at,_l=Al,p=ot,x=st,hl=$l,y=dt,T=mt,Ul=pt,Cl=ft,Il=vt,z=ut,Rl=Ne("quill-editor"),kl=rt,El=nt,J=Fl,Ce=yt,Ie=bt,Dl=Zl;return u(),f("div",ht,[o("div",Ut,[(u(),f(M,null,P($e,(l,U)=>o("div",{key:U,class:Hl(["tab",{active:E.value===l.value}]),onClick:ms=>E.value=l.value},g(l.label),11,Ct)),64))]),o("div",It,[o("div",Rt,[i(c,{class:"toolbar-button",type:"default",onClick:D},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Ee))]),_:1}),e[106]||(e[106]=v("刷新 "))]),_:1}),i(c,{class:"toolbar-button",type:"success",onClick:Je},{default:n(()=>[i(r,{class:"el-icon-plus"}),e[107]||(e[107]=v("添加 "))]),_:1}),i(c,{class:"toolbar-button",type:"default",disabled:R.value.length!==1,onClick:ve},{default:n(()=>[i(r,null,{default:n(()=>[i(_(De))]),_:1}),e[108]||(e[108]=v("编辑 "))]),_:1},8,["disabled"]),i(c,{class:"toolbar-button",type:"danger",disabled:R.value.length===0,onClick:el},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Le))]),_:1}),e[109]||(e[109]=v("删除 "))]),_:1},8,["disabled"])]),o("div",kt,[i(d,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=l=>N.value=l),placeholder:"搜索项目名称",class:"search-input",onKeyup:Ql(pe,["enter"]),onBlur:pe},null,8,["modelValue"]),i(c,{class:"search-button",type:"primary",onClick:pe},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Se))]),_:1})]),_:1}),i(c,{class:"toolbar-button filter-button",type:"default",onClick:fl},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Yl))]),_:1}),e[110]||(e[110]=v("筛选 "))]),_:1}),i(c,{class:"toolbar-button export-button",type:"default",onClick:vl},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Bl))]),_:1}),e[111]||(e[111]=v("导出 "))]),_:1})])]),i(_l,{class:"table-card"},{default:n(()=>[o("div",Et,[Wl((u(),w(Ue,{ref:"projectsTable",data:Be.value,border:"",stripe:"","highlight-current-row":"","row-key":"id",style:{width:"100%"},onSelectionChange:Ze,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:n(()=>[i(m,{type:"selection",width:"50",align:"center",fixed:"left"}),i(m,{prop:"id",label:"ID",width:"60",align:"center",fixed:"left"}),i(m,{prop:"type",label:"类型",width:"80",align:"center",fixed:"left"},{default:n(l=>[i(C,{type:Te(l.row.type)},{default:n(()=>[v(g(l.row.type),1)]),_:2},1032,["type"])]),_:1}),i(m,{prop:"category",label:"分类",width:"80",align:"center",fixed:"left"}),i(m,{prop:"name",label:"名称",width:"120",align:"center",fixed:"left"}),i(m,{prop:"imageUrl",label:"图片",width:"70",align:"center",fixed:"left"},{default:n(l=>[i(he,{style:{width:"40px",height:"40px"},src:G(l.row.imageUrl),"preview-src-list":l.row.imageUrl?[G(l.row.imageUrl)]:[],fit:"cover",onError:()=>sl(l.row)},{error:n(()=>[o("div",Dt,[i(r,null,{default:n(()=>[i(_(je))]),_:1})])]),_:2},1032,["src","preview-src-list","onError"])]),_:1}),i(m,{prop:"videoUrl",label:"视频",width:"70",align:"center",fixed:"left"},{default:n(l=>[l.row.videoUrl?(u(),w(c,{key:0,size:"small",type:"primary",onClick:U=>Vl(l.row)},{default:n(()=>[i(r,null,{default:n(()=>[i(Tl)]),_:1})]),_:2},1032,["onClick"])):(u(),f("span",Lt,"-"))]),_:1}),i(m,{prop:"price",label:"价格",width:"90",align:"center",fixed:"left"},{default:n(l=>[v(g(S(l.row.price)),1)]),_:1}),i(m,{prop:"sellPrice",label:"卖出价格",width:"90",align:"center",fixed:"left"},{default:n(l=>[v(g(S(l.row.sellPrice)),1)]),_:1}),i(m,{prop:"minInvestment",label:"最少投资金额",width:"110",align:"center",fixed:"left"},{default:n(l=>[v(g(S(l.row.minInvestment)),1)]),_:1}),i(m,{prop:"maxInvestment",label:"最多投资金额",width:"110",align:"center",fixed:"left"},{default:n(l=>[v(g(S(l.row.maxInvestment)),1)]),_:1}),i(m,{prop:"priceType",label:"价格类型","min-width":"100",align:"center"}),i(m,{prop:"paymentMethod",label:"支付方式","min-width":"100",align:"center"}),i(m,{prop:"purchaseTime",label:"购买时间","min-width":"150",align:"center"}),i(m,{prop:"duration",label:"投资周期(天)","min-width":"120",align:"center"},{default:n(l=>[v(g(l.row.duration),1)]),_:1}),i(m,{prop:"customReturnEnabled",label:"自定义收益率","min-width":"120",align:"center"},{default:n(l=>[l.row.customReturnEnabled===1?(u(),w(C,{key:0,type:"success"},{default:n(()=>e[112]||(e[112]=[v("是")])),_:1})):(u(),w(C,{key:1,type:"info"},{default:n(()=>e[113]||(e[113]=[v("否")])),_:1}))]),_:1}),i(m,{prop:"expectedReturn",label:"收益率(%)","min-width":"100",align:"center"},{default:n(l=>[l.row.customReturnEnabled===1?(u(),f(M,{key:0},[o("span",St,g(l.row.customReturnRate)+"%",1),i(K,{content:"自定义收益率",placement:"top"},{default:n(()=>[i(r,{class:"custom-icon"},{default:n(()=>[i(_(ee))]),_:1})]),_:1})],64)):(u(),f(M,{key:1},[v(g(l.row.expectedReturn)+"% ",1)],64))]),_:1}),i(m,{prop:"profitTime",label:"收益时间(小时)","min-width":"120",align:"center"},{default:n(l=>[l.row.customReturnEnabled===1?(u(),f(M,{key:0},[o("span",jt,g(l.row.customReturnHours),1),i(K,{content:"自定义结算时间",placement:"top"},{default:n(()=>[i(r,{class:"custom-icon"},{default:n(()=>[i(_(ee))]),_:1})]),_:1})],64)):(u(),f(M,{key:1},[v(g(l.row.profitTime),1)],64))]),_:1}),i(m,{prop:"quantity",label:"数量","min-width":"80",align:"center"}),i(m,{prop:"actualQuantity",label:"实际数量","min-width":"100",align:"center"}),i(m,{prop:"sortOrder",label:"排序","min-width":"80",align:"center"}),i(m,{prop:"maxPurchaseTimes",label:"最多购买次数","min-width":"120",align:"center"}),i(m,{prop:"simultaneousPurchases",label:"同时购买数量","min-width":"120",align:"center"}),i(m,{prop:"maxProfitTimes",label:"最多收益次数","min-width":"120",align:"center"}),i(m,{prop:"soldQuantity",label:"已售数量","min-width":"100",align:"center"}),i(m,{prop:"vipLevel",label:"VIP级别","min-width":"100",align:"center"},{default:n(l=>[i(C,{type:"info"},{default:n(()=>[v(g(l.row.vipLevel)+" ("+g(l.row.vipLevelNumber)+")",1)]),_:2},1024)]),_:1}),i(m,{prop:"vipTypeOperator",label:"VIP类型","min-width":"100",align:"center"},{default:n(l=>[i(C,{type:"info"},{default:n(()=>[v(g(Ke(l.row.vipTypeOperator)),1)]),_:2},1024)]),_:1}),i(m,{prop:"returnPrincipal",label:"到期退本","min-width":"100",align:"center"},{default:n(l=>[l.row.returnPrincipal?(u(),w(C,{key:0,type:"success"},{default:n(()=>e[114]||(e[114]=[v("是")])),_:1})):(u(),w(C,{key:1,type:"info"},{default:n(()=>e[115]||(e[115]=[v("否")])),_:1}))]),_:1}),i(m,{prop:"isFree",label:"免费","min-width":"80",align:"center"},{default:n(l=>[l.row.isFree?(u(),w(C,{key:0,type:"success"},{default:n(()=>e[116]||(e[116]=[v("是")])),_:1})):(u(),w(C,{key:1,type:"info"},{default:n(()=>e[117]||(e[117]=[v("否")])),_:1}))]),_:1}),i(m,{prop:"currency",label:"货币","min-width":"80",align:"center"}),i(m,{prop:"status",label:"状态","min-width":"80",align:"center"},{default:n(l=>[i($,{modelValue:l.row.status,"onUpdate:modelValue":U=>l.row.status=U,"active-value":1,"inactive-value":0,disabled:l.row.actualStatus===0&&l.row.status===1,onChange:U=>ml(l.row,U)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"]),l.row.actualStatus===0&&l.row.status===1?(u(),f("div",Nt,[i(Pl,{type:"warning",size:"small"},{default:n(()=>e[118]||(e[118]=[v("库存不足")])),_:1})])):Y("",!0)]),_:1}),i(m,{prop:"sellStatus",label:"出售状态","min-width":"100",align:"center"},{default:n(l=>[i(C,{type:Xe(l.row.sellStatus)},{default:n(()=>[v(g(Ge(l.row.sellStatus)),1)]),_:2},1032,["type"])]),_:1}),i(m,{prop:"commissionEnabled",label:"返佣开关","min-width":"100",align:"center"},{default:n(l=>[i($,{modelValue:l.row.commissionEnabled,"onUpdate:modelValue":U=>l.row.commissionEnabled=U,"active-value":1,"inactive-value":0,onChange:U=>pl(l.row,U)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),i(m,{prop:"level1Commission",label:"一级返佣","min-width":"100",align:"center"},{default:n(l=>[l.row.commissionEnabled===1?(u(),f("span",qt,g(l.row.level1Commission)+"%",1)):(u(),f("span",Qt,"-"))]),_:1}),i(m,{prop:"level2Commission",label:"二级返佣","min-width":"100",align:"center"},{default:n(l=>[l.row.commissionEnabled===1?(u(),f("span",Ot,g(l.row.level2Commission)+"%",1)):(u(),f("span",At,"-"))]),_:1}),i(m,{prop:"level3Commission",label:"三级返佣","min-width":"100",align:"center"},{default:n(l=>[l.row.commissionEnabled===1?(u(),f("span",$t,g(l.row.level3Commission)+"%",1)):(u(),f("span",Ft,"-"))]),_:1}),i(m,{prop:"settlementTime",label:"结算时间","min-width":"160",align:"center",sortable:""}),i(m,{prop:"createTime",label:"添加时间","min-width":"160",align:"center",sortable:""}),i(m,{prop:"updateTime",label:"更新时间","min-width":"160",align:"center",sortable:""}),i(m,{label:"操作","min-width":"160",align:"center",fixed:"right"},{default:n(l=>[o("div",Ht,[i(c,{class:"operation-button icon-only drag-handle",size:"small",type:"primary",onMousedown:tt(U=>wl(U,l.$index),["prevent"])},{default:n(()=>[i(r,null,{default:n(()=>[i(_(it))]),_:1})]),_:2},1032,["onMousedown"]),i(c,{class:"operation-button icon-only",size:"small",type:"default",onClick:U=>ve(l.row)},{default:n(()=>[i(r,null,{default:n(()=>[i(_(De))]),_:1})]),_:2},1032,["onClick"]),i(c,{type:"danger",size:"small",onClick:U=>tl(l.row),class:"operation-button icon-only"},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Le))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Dl,le.value]])])]),_:1}),o("div",zt,[i(hl,{"current-page":L.value,"onUpdate:currentPage":e[1]||(e[1]=l=>L.value=l),"page-size":F.value,"onUpdate:pageSize":e[2]||(e[2]=l=>F.value=l),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:we.value,onSizeChange:Pe,onCurrentChange:We,"pager-count":7,background:""},{sizes:n(()=>[i(x,{"model-value":F.value,onChange:Pe,class:"custom-page-size"},{default:n(()=>[(u(),f(M,null,P([10,20,50,100],l=>i(p,{key:l,value:l,label:`${l}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),i(J,{modelValue:q.value,"onUpdate:modelValue":e[43]||(e[43]=l=>q.value=l),title:W.value?"添加投资项目":"编辑投资项目",width:"850px",center:"",top:"5vh"},{footer:n(()=>[o("span",vi,[i(c,{type:"primary",onClick:ul},{default:n(()=>e[123]||(e[123]=[v("确定")])),_:1}),i(c,{onClick:e[42]||(e[42]=l=>q.value=!1)},{default:n(()=>e[124]||(e[124]=[v("取消")])),_:1})])]),default:n(()=>[i(El,{model:s,"label-position":"top","label-width":"100px"},{default:n(()=>[i(kl,{modelValue:te.value,"onUpdate:modelValue":e[41]||(e[41]=l=>te.value=l)},{default:n(()=>[i(z,{label:"基本信息",name:"basic"},{default:n(()=>[o("div",Yt,[i(y,{label:"项目名称",required:""},{default:n(()=>[i(d,{modelValue:s.name,"onUpdate:modelValue":e[3]||(e[3]=l=>s.name=l),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1}),i(y,{label:"项目类型",required:""},{default:n(()=>[i(x,{modelValue:s.type,"onUpdate:modelValue":e[4]||(e[4]=l=>s.type=l),placeholder:"请选择项目类型",style:{width:"100%"},disabled:""},{default:n(()=>[(u(!0),f(M,null,P(se.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"项目分类"},{default:n(()=>[i(x,{modelValue:s.category,"onUpdate:modelValue":e[5]||(e[5]=l=>s.category=l),placeholder:"请选择项目分类",style:{width:"100%"},disabled:""},{default:n(()=>[(u(!0),f(M,null,P(oe.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),o("div",Bt,[i(y,{label:"货币类型"},{default:n(()=>[i(x,{modelValue:s.currency,"onUpdate:modelValue":e[6]||(e[6]=l=>s.currency=l),placeholder:"请选择货币类型",style:{width:"100%"},disabled:""},{default:n(()=>[(u(!0),f(M,null,P(ne.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"价格类型"},{default:n(()=>[i(x,{modelValue:s.priceType,"onUpdate:modelValue":e[7]||(e[7]=l=>s.priceType=l),placeholder:"请选择价格类型",style:{width:"100%"},disabled:""},{default:n(()=>[(u(!0),f(M,null,P(re.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"支付方式"},{default:n(()=>[i(x,{modelValue:s.paymentMethod,"onUpdate:modelValue":e[8]||(e[8]=l=>s.paymentMethod=l),placeholder:"请选择支付方式",style:{width:"100%"},disabled:""},{default:n(()=>[(u(!0),f(M,null,P(ue.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),o("div",Wt,[i(y,{label:"价格",required:""},{default:n(()=>[i(T,{modelValue:s.price,"onUpdate:modelValue":e[9]||(e[9]=l=>s.price=l),min:0,precision:2,step:100,"controls-position":"right",style:{width:"100%"},onChange:il},null,8,["modelValue"])]),_:1}),i(y,{label:"卖出价格",required:""},{default:n(()=>[i(T,{modelValue:s.sellPrice,"onUpdate:modelValue":e[10]||(e[10]=l=>s.sellPrice=l),min:0,precision:2,step:100,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"购买时间",required:""},{default:n(()=>[i(Ul,{modelValue:s.purchaseTimeRange,"onUpdate:modelValue":e[11]||(e[11]=l=>s.purchaseTimeRange=l),"is-range":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围",format:"HH:mm:ss","value-format":"HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),o("div",Zt,[i(y,{label:"最少投资金额",required:""},{default:n(()=>[i(T,{modelValue:s.minInvestment,"onUpdate:modelValue":e[12]||(e[12]=l=>s.minInvestment=l),min:0,precision:2,step:100,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"最多投资金额",required:""},{default:n(()=>[i(T,{modelValue:s.maxInvestment,"onUpdate:modelValue":e[13]||(e[13]=l=>s.maxInvestment=l),min:0,precision:2,step:1e3,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"排序(从大到小)"},{default:n(()=>[i(T,{modelValue:s.sortOrder,"onUpdate:modelValue":e[14]||(e[14]=l=>s.sortOrder=l),min:1,max:100,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),o("div",Gt,[i(y,{label:"收益率(%)",required:""},{default:n(()=>[i(T,{modelValue:s.expectedReturn,"onUpdate:modelValue":e[15]||(e[15]=l=>s.expectedReturn=l),precision:0,step:1,min:0,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"投资周期(天)",required:""},{default:n(()=>[i(T,{modelValue:s.duration,"onUpdate:modelValue":e[16]||(e[16]=l=>s.duration=l),min:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),o("div",Xt,[i(y,{label:"收益时间(小时)",required:""},{default:n(()=>[i(T,{modelValue:s.profitTime,"onUpdate:modelValue":e[17]||(e[17]=l=>s.profitTime=l),min:1,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"最多收益次数",required:""},{default:n(()=>[i(T,{modelValue:s.maxProfitTimes,"onUpdate:modelValue":e[18]||(e[18]=l=>s.maxProfitTimes=l),min:0,step:1,"controls-position":"right",style:{width:"100%"},placeholder:"0表示不限制"},null,8,["modelValue"])]),_:1})]),o("div",Kt,[i(y,{label:"每周收益日"},{default:n(()=>[i(Il,{modelValue:s.weeklyProfitDays,"onUpdate:modelValue":e[19]||(e[19]=l=>s.weeklyProfitDays=l),class:"weekly-profit-days"},{default:n(()=>[(u(!0),f(M,null,P(Me.value,l=>(u(),w(Cl,{key:l.id,label:l.value},{default:n(()=>[v(g(l.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),o("div",Jt,[i(c,{size:"small",onClick:ol},{default:n(()=>e[119]||(e[119]=[v("工作日")])),_:1}),i(c,{size:"small",onClick:nl},{default:n(()=>e[120]||(e[120]=[v("周末")])),_:1}),i(c,{size:"small",onClick:rl},{default:n(()=>e[121]||(e[121]=[v("全周")])),_:1})])]),_:1})]),o("div",ei,[i(y,{label:"VIP级别",required:""},{default:n(()=>[i(x,{modelValue:s.vipLevelNumber,"onUpdate:modelValue":e[20]||(e[20]=l=>s.vipLevelNumber=l),placeholder:"请选择VIP级别",style:{width:"100%"}},{default:n(()=>[(u(!0),f(M,null,P(xe.value,l=>(u(),w(p,{key:l.level,label:`${l.name} (${l.level})`,value:l.level},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"VIP类型"},{default:n(()=>[i(x,{modelValue:s.vipTypeOperator,"onUpdate:modelValue":e[21]||(e[21]=l=>s.vipTypeOperator=l),placeholder:"请选择操作符",style:{width:"100%"}},{default:n(()=>[(u(!0),f(M,null,P(ae.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"状态"},{default:n(()=>[i(x,{modelValue:s.status,"onUpdate:modelValue":e[22]||(e[22]=l=>s.status=l),placeholder:"请选择状态",style:{width:"100%"}},{default:n(()=>[(u(!0),f(M,null,P(de.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),o("div",li,[i(y,{label:"出售状态"},{default:n(()=>[i(x,{modelValue:s.sellStatus,"onUpdate:modelValue":e[23]||(e[23]=l=>s.sellStatus=l),placeholder:"请选择出售状态",style:{width:"100%"}},{default:n(()=>[(u(!0),f(M,null,P(me.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(y,{label:"免费项目"},{default:n(()=>[i($,{modelValue:s.isFree,"onUpdate:modelValue":e[24]||(e[24]=l=>s.isFree=l),"active-value":!0,"inactive-value":!1,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1}),i(y,{label:"到期退本"},{default:n(()=>[i($,{modelValue:s.returnPrincipal,"onUpdate:modelValue":e[25]||(e[25]=l=>s.returnPrincipal=l),"active-value":!0,"inactive-value":!1,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})])]),_:1}),i(z,{label:"自定义收益设置",name:"profit",disabled:""},{default:n(()=>[o("div",ti,[i(y,{label:"启用自定义收益率"},{default:n(()=>[i($,{modelValue:s.customReturnEnabled,"onUpdate:modelValue":e[26]||(e[26]=l=>s.customReturnEnabled=l),"active-value":1,"inactive-value":0,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),s.customReturnEnabled===1?(u(),f("div",ii,[i(y,{label:"自定义结算时间(小时)",required:""},{default:n(()=>[i(T,{modelValue:s.customReturnHours,"onUpdate:modelValue":e[27]||(e[27]=l=>s.customReturnHours=l),min:1,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"自定义收益率(%)",required:""},{default:n(()=>[i(T,{modelValue:s.customReturnRate,"onUpdate:modelValue":e[28]||(e[28]=l=>s.customReturnRate=l),precision:0,step:1,min:0,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})])):Y("",!0),s.customReturnEnabled===1?(u(),f("div",ai)):Y("",!0)]),_:1}),i(z,{label:"数量设置",name:"quantity"},{default:n(()=>[o("div",si,[i(y,{label:"项目数量",required:""},{default:n(()=>[i(T,{modelValue:s.quantity,"onUpdate:modelValue":e[29]||(e[29]=l=>s.quantity=l),min:0,step:1,"controls-position":"right",style:{width:"100%"},onChange:al,placeholder:"0表示不限制"},null,8,["modelValue"])]),_:1}),i(y,{label:"实际数量",required:""},{default:n(()=>[i(T,{modelValue:s.actualQuantity,"onUpdate:modelValue":e[30]||(e[30]=l=>s.actualQuantity=l),min:0,step:1,"controls-position":"right",style:{width:"100%"},disabled:s.quantity>0,placeholder:"0表示不限制"},null,8,["modelValue","disabled"])]),_:1}),i(y,{label:"已售数量"},{default:n(()=>[i(T,{modelValue:s.soldQuantity,"onUpdate:modelValue":e[31]||(e[31]=l=>s.soldQuantity=l),min:0,step:1,"controls-position":"right",style:{width:"100%"},disabled:""},null,8,["modelValue"])]),_:1})]),o("div",oi,[i(y,{label:"最多购买次数",required:""},{default:n(()=>[i(T,{modelValue:s.maxPurchaseTimes,"onUpdate:modelValue":e[32]||(e[32]=l=>s.maxPurchaseTimes=l),min:0,step:1,"controls-position":"right",style:{width:"100%"},placeholder:"0表示不限制"},null,8,["modelValue"])]),_:1}),i(y,{label:"同时购买数量",required:""},{default:n(()=>[i(T,{modelValue:s.simultaneousPurchases,"onUpdate:modelValue":e[33]||(e[33]=l=>s.simultaneousPurchases=l),min:0,step:1,"controls-position":"right",style:{width:"100%"},placeholder:"0表示不限制"},null,8,["modelValue"])]),_:1})])]),_:1}),i(z,{label:"返佣设置",name:"commission",disabled:""},{default:n(()=>[o("div",ni,[i(y,{label:"返佣开关"},{default:n(()=>[i($,{modelValue:s.commissionEnabled,"onUpdate:modelValue":e[34]||(e[34]=l=>s.commissionEnabled=l),"active-value":1,"inactive-value":0,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),s.commissionEnabled===1?(u(),f("div",ri,[i(y,{label:"一级返佣(%)"},{default:n(()=>[i(T,{modelValue:s.level1Commission,"onUpdate:modelValue":e[35]||(e[35]=l=>s.level1Commission=l),precision:2,step:.1,min:0,max:50,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"二级返佣(%)"},{default:n(()=>[i(T,{modelValue:s.level2Commission,"onUpdate:modelValue":e[36]||(e[36]=l=>s.level2Commission=l),precision:2,step:.1,min:0,max:50,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),i(y,{label:"三级返佣(%)"},{default:n(()=>[i(T,{modelValue:s.level3Commission,"onUpdate:modelValue":e[37]||(e[37]=l=>s.level3Commission=l),precision:2,step:.1,min:0,max:50,style:{width:"100%"}},null,8,["modelValue"])]),_:1})])):Y("",!0)]),_:1}),i(z,{label:"媒体资源",name:"media"},{default:n(()=>[i(y,{label:"项目描述"},{default:n(()=>[o("div",ui,[i(Rl,{content:s.description,"onUpdate:content":e[38]||(e[38]=l=>s.description=l),contentType:"html",theme:"snow",toolbar:"full",placeholder:"请输入项目描述"},null,8,["content"])])]),_:1}),i(y,{label:"项目图片"},{default:n(()=>[o("div",di,[o("div",mi,[s.imageUrl?(u(),w(he,{key:0,src:G(s.imageUrl),class:"project-image","preview-src-list":[G(s.imageUrl)],"preview-teleported":"","hide-on-click-modal":"",onError:e[39]||(e[39]=()=>console.warn("编辑对话框图片加载失败:",s.imageUrl))},null,8,["src","preview-src-list"])):(u(),w(r,{key:1,class:"project-image-uploader-icon"},{default:n(()=>[i(_(je))]),_:1}))]),o("div",pi,[i(c,{type:"success",size:"small",onClick:e[40]||(e[40]=l=>bl("image"))},{default:n(()=>[i(r,null,{default:n(()=>[i(_(ct))]),_:1}),e[122]||(e[122]=v("从附件选择图片 "))]),_:1})])])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),i(J,{modelValue:H.value,"onUpdate:modelValue":e[46]||(e[46]=l=>H.value=l),title:"项目详情",width:"800px",center:""},{footer:n(()=>[o("span",Qi,[i(c,{type:"primary",onClick:e[44]||(e[44]=l=>ll(a.currentProject))},{default:n(()=>e[134]||(e[134]=[v("编辑")])),_:1}),i(c,{onClick:e[45]||(e[45]=l=>H.value=!1)},{default:n(()=>e[135]||(e[135]=[v("关闭")])),_:1})])]),default:n(()=>[a.currentProject?(u(),f("div",fi,[o("div",ci,[o("div",yi,[o("h2",null,g(a.currentProject.name),1),i(C,{type:Te(a.currentProject.type),class:"project-type"},{default:n(()=>[v(g(a.currentProject.type),1)]),_:1},8,["type"]),o("div",bi,[e[125]||(e[125]=v(" 状态: ")),i(C,{type:a.currentProject.status===1?"success":"danger"},{default:n(()=>[v(g(a.currentProject.status===1?"开启":"关闭"),1)]),_:1},8,["type"])])]),o("div",gi,[o("img",{src:a.currentProject.imageUrl||Oe,alt:"项目图片",class:"detail-image"},null,8,Vi)])]),i(Ce),o("div",wi,[e[126]||(e[126]=o("h3",null,"项目描述",-1)),o("p",null,g(a.currentProject.description),1)]),o("div",xi,[o("div",Mi,[e[127]||(e[127]=o("div",{class:"stat-label"},"预期收益率",-1)),a.currentProject.customReturnEnabled===1?(u(),f("div",Ti,[o("span",Pi,g(a.currentProject.customReturnRate)+"%",1),i(K,{content:"自定义收益率",placement:"top"},{default:n(()=>[i(r,{class:"custom-icon"},{default:n(()=>[i(_(ee))]),_:1})]),_:1})])):(u(),f("div",_i,g(a.currentProject.expectedReturn)+"%",1))]),o("div",hi,[e[128]||(e[128]=o("div",{class:"stat-label"},"投资期限",-1)),o("div",Ui,g(a.currentProject.duration)+" "+g(a.currentProject.durationUnit),1)]),o("div",Ci,[e[129]||(e[129]=o("div",{class:"stat-label"},"收益时间",-1)),a.currentProject.customReturnEnabled===1?(u(),f("div",Ii,[o("span",Ri,g(a.currentProject.customReturnHours)+" 小时",1),i(K,{content:"自定义结算时间",placement:"top"},{default:n(()=>[i(r,{class:"custom-icon"},{default:n(()=>[i(_(ee))]),_:1})]),_:1})])):(u(),f("div",ki,g(a.currentProject.profitTime)+" 小时",1))]),o("div",Ei,[e[130]||(e[130]=o("div",{class:"stat-label"},"投资额度",-1)),o("div",Di,g(S(a.currentProject.minInvestment))+" - "+g(S(a.currentProject.maxInvestment)),1)]),o("div",Li,[e[131]||(e[131]=o("div",{class:"stat-label"},"已投资人数",-1)),o("div",Si,g(a.currentProject.totalInvestors),1)]),o("div",ji,[e[132]||(e[132]=o("div",{class:"stat-label"},"已投资金额",-1)),o("div",Ni,g(S(a.currentProject.totalInvestment)),1)])]),i(Ce),o("div",qi,[e[133]||(e[133]=o("h3",null,"最近投资记录",-1)),i(Ue,{data:Ye.value,style:{width:"100%"},border:"",stripe:""},{default:n(()=>[i(m,{prop:"userId",label:"用户ID",width:"100"}),i(m,{prop:"username",label:"用户名",width:"150"}),i(m,{prop:"amount",label:"投资金额",width:"120"},{default:n(l=>[v(g(S(l.row.amount)),1)]),_:1}),i(m,{prop:"investTime",label:"投资时间",width:"160"}),i(m,{prop:"status",label:"状态",width:"100"},{default:n(l=>[i(C,{type:l.row.status==="进行中"?"success":"info"},{default:n(()=>[v(g(l.row.status),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])])])):Y("",!0)]),_:1},8,["modelValue"]),i(J,{modelValue:Q.value,"onUpdate:modelValue":e[48]||(e[48]=l=>Q.value=l),title:"确认删除",width:"400px",center:""},{footer:n(()=>[o("span",Oi,[i(c,{type:"danger",onClick:dl},{default:n(()=>e[136]||(e[136]=[v("确定删除")])),_:1}),i(c,{onClick:e[47]||(e[47]=l=>Q.value=!1)},{default:n(()=>e[137]||(e[137]=[v("取消")])),_:1})])]),default:n(()=>[e[138]||(e[138]=o("p",{class:"delete-confirm-text"},"确定要删除所选投资项目吗？此操作不可恢复。",-1))]),_:1},8,["modelValue"]),i(wt,{visible:ie.value,"onUpdate:visible":e[49]||(e[49]=l=>ie.value=l),fileType:Z.value,onSelect:gl},null,8,["visible","fileType"]),i(J,{modelValue:O.value,"onUpdate:modelValue":e[104]||(e[104]=l=>O.value=l),title:"筛选条件",width:"900px","close-on-click-modal":!0,onClose:e[105]||(e[105]=l=>O.value=!1),class:"filter-dialog"},{footer:n(()=>[o("div",rs,[i(c,{class:"filter-button",type:"primary",onClick:yl},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Se))]),_:1}),e[199]||(e[199]=v("搜索 "))]),_:1}),i(c,{class:"filter-button",onClick:cl},{default:n(()=>[i(r,null,{default:n(()=>[i(_(Ee))]),_:1}),e[200]||(e[200]=v("重置 "))]),_:1}),i(c,{class:"filter-button",onClick:e[103]||(e[103]=l=>O.value=!1)},{default:n(()=>[i(r,null,{default:n(()=>[i(_(gt))]),_:1}),e[201]||(e[201]=v("取消 "))]),_:1})])]),default:n(()=>[o("div",Ai,[o("div",$i,[e[144]||(e[144]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"基本信息")],-1)),o("div",Fi,[o("div",Hi,[o("div",zi,[e[139]||(e[139]=o("div",{class:"filter-label"},"ID",-1)),i(d,{modelValue:t.id,"onUpdate:modelValue":e[50]||(e[50]=l=>t.id=l),placeholder:"请输入ID"},null,8,["modelValue"])]),o("div",Yi,[e[140]||(e[140]=o("div",{class:"filter-label"},"类型",-1)),i(x,{modelValue:t.type,"onUpdate:modelValue":e[51]||(e[51]=l=>t.type=l),placeholder:"请选择类型",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(se.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",Bi,[e[141]||(e[141]=o("div",{class:"filter-label"},"分类",-1)),i(x,{modelValue:t.category,"onUpdate:modelValue":e[52]||(e[52]=l=>t.category=l),placeholder:"请选择分类",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(oe.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",Wi,[e[142]||(e[142]=o("div",{class:"filter-label"},"名称",-1)),i(d,{modelValue:t.name,"onUpdate:modelValue":e[53]||(e[53]=l=>t.name=l),placeholder:"请输入名称"},null,8,["modelValue"])]),o("div",Zi,[e[143]||(e[143]=o("div",{class:"filter-label"},"视频",-1)),i(x,{modelValue:t.hasVideo,"onUpdate:modelValue":e[54]||(e[54]=l=>t.hasVideo=l),placeholder:"请选择",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),i(p,{label:"有视频",value:"1"}),i(p,{label:"无视频",value:"0"})]),_:1},8,["modelValue"])])])])]),o("div",Gi,[e[156]||(e[156]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"价格信息")],-1)),o("div",Xi,[o("div",Ki,[o("div",Ji,[e[146]||(e[146]=o("div",{class:"filter-label"},"价格范围",-1)),o("div",ea,[i(d,{modelValue:t.priceMin,"onUpdate:modelValue":e[55]||(e[55]=l=>t.priceMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[145]||(e[145]=o("span",null,"至",-1)),i(d,{modelValue:t.priceMax,"onUpdate:modelValue":e[56]||(e[56]=l=>t.priceMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",la,[e[148]||(e[148]=o("div",{class:"filter-label"},"卖出价格范围",-1)),o("div",ta,[i(d,{modelValue:t.sellPriceMin,"onUpdate:modelValue":e[57]||(e[57]=l=>t.sellPriceMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[147]||(e[147]=o("span",null,"至",-1)),i(d,{modelValue:t.sellPriceMax,"onUpdate:modelValue":e[58]||(e[58]=l=>t.sellPriceMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",ia,[e[150]||(e[150]=o("div",{class:"filter-label"},"最少投资金额范围",-1)),o("div",aa,[i(d,{modelValue:t.minInvestmentMin,"onUpdate:modelValue":e[59]||(e[59]=l=>t.minInvestmentMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[149]||(e[149]=o("span",null,"至",-1)),i(d,{modelValue:t.minInvestmentMax,"onUpdate:modelValue":e[60]||(e[60]=l=>t.minInvestmentMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",sa,[e[152]||(e[152]=o("div",{class:"filter-label"},"最多投资金额范围",-1)),o("div",oa,[i(d,{modelValue:t.maxInvestmentMin,"onUpdate:modelValue":e[61]||(e[61]=l=>t.maxInvestmentMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[151]||(e[151]=o("span",null,"至",-1)),i(d,{modelValue:t.maxInvestmentMax,"onUpdate:modelValue":e[62]||(e[62]=l=>t.maxInvestmentMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",na,[e[153]||(e[153]=o("div",{class:"filter-label"},"价格类型",-1)),i(x,{modelValue:t.priceType,"onUpdate:modelValue":e[63]||(e[63]=l=>t.priceType=l),placeholder:"请选择价格类型",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(re.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",ra,[e[154]||(e[154]=o("div",{class:"filter-label"},"支付方式",-1)),i(x,{modelValue:t.paymentMethod,"onUpdate:modelValue":e[64]||(e[64]=l=>t.paymentMethod=l),placeholder:"请选择支付方式",style:{width:"100%"},disabled:""},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(ue.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",ua,[e[155]||(e[155]=o("div",{class:"filter-label"},"购买时间",-1)),i(x,{modelValue:t.purchaseTime,"onUpdate:modelValue":e[65]||(e[65]=l=>t.purchaseTime=l),placeholder:"请选择购买时间",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),i(p,{label:"不限",value:"不限"}),i(p,{label:"白天",value:"白天"}),i(p,{label:"夜间",value:"夜间"})]),_:1},8,["modelValue"])])])])]),o("div",da,[e[164]||(e[164]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"收益设置")],-1)),o("div",ma,[o("div",pa,[o("div",va,[e[158]||(e[158]=o("div",{class:"filter-label"},"投资周期(天)",-1)),o("div",fa,[i(d,{modelValue:t.durationMin,"onUpdate:modelValue":e[66]||(e[66]=l=>t.durationMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[157]||(e[157]=o("span",null,"至",-1)),i(d,{modelValue:t.durationMax,"onUpdate:modelValue":e[67]||(e[67]=l=>t.durationMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",ca,[e[159]||(e[159]=o("div",{class:"filter-label"},"自定义收益率",-1)),i(x,{modelValue:t.customReturnEnabled,"onUpdate:modelValue":e[68]||(e[68]=l=>t.customReturnEnabled=l),placeholder:"请选择",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),i(p,{label:"是",value:"1"}),i(p,{label:"否",value:"0"})]),_:1},8,["modelValue"])]),o("div",ya,[e[161]||(e[161]=o("div",{class:"filter-label"},"收益率范围(%)",-1)),o("div",ba,[i(d,{modelValue:t.expectedReturnMin,"onUpdate:modelValue":e[69]||(e[69]=l=>t.expectedReturnMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[160]||(e[160]=o("span",null,"至",-1)),i(d,{modelValue:t.expectedReturnMax,"onUpdate:modelValue":e[70]||(e[70]=l=>t.expectedReturnMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",ga,[e[163]||(e[163]=o("div",{class:"filter-label"},"收益时间(小时)",-1)),o("div",Va,[i(d,{modelValue:t.profitTimeMin,"onUpdate:modelValue":e[71]||(e[71]=l=>t.profitTimeMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[162]||(e[162]=o("span",null,"至",-1)),i(d,{modelValue:t.profitTimeMax,"onUpdate:modelValue":e[72]||(e[72]=l=>t.profitTimeMax=l),placeholder:"最大值"},null,8,["modelValue"])])])])])]),o("div",wa,[e[177]||(e[177]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"数量设置")],-1)),o("div",xa,[o("div",Ma,[o("div",Ta,[e[166]||(e[166]=o("div",{class:"filter-label"},"数量范围",-1)),o("div",Pa,[i(d,{modelValue:t.quantityMin,"onUpdate:modelValue":e[73]||(e[73]=l=>t.quantityMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[165]||(e[165]=o("span",null,"至",-1)),i(d,{modelValue:t.quantityMax,"onUpdate:modelValue":e[74]||(e[74]=l=>t.quantityMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",_a,[e[168]||(e[168]=o("div",{class:"filter-label"},"实际数量范围",-1)),o("div",ha,[i(d,{modelValue:t.actualQuantityMin,"onUpdate:modelValue":e[75]||(e[75]=l=>t.actualQuantityMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[167]||(e[167]=o("span",null,"至",-1)),i(d,{modelValue:t.actualQuantityMax,"onUpdate:modelValue":e[76]||(e[76]=l=>t.actualQuantityMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",Ua,[e[170]||(e[170]=o("div",{class:"filter-label"},"最多购买次数范围",-1)),o("div",Ca,[i(d,{modelValue:t.maxPurchaseTimesMin,"onUpdate:modelValue":e[77]||(e[77]=l=>t.maxPurchaseTimesMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[169]||(e[169]=o("span",null,"至",-1)),i(d,{modelValue:t.maxPurchaseTimesMax,"onUpdate:modelValue":e[78]||(e[78]=l=>t.maxPurchaseTimesMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",Ia,[e[172]||(e[172]=o("div",{class:"filter-label"},"同时购买数量范围",-1)),o("div",Ra,[i(d,{modelValue:t.simultaneousPurchasesMin,"onUpdate:modelValue":e[79]||(e[79]=l=>t.simultaneousPurchasesMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[171]||(e[171]=o("span",null,"至",-1)),i(d,{modelValue:t.simultaneousPurchasesMax,"onUpdate:modelValue":e[80]||(e[80]=l=>t.simultaneousPurchasesMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",ka,[e[174]||(e[174]=o("div",{class:"filter-label"},"最多收益次数范围",-1)),o("div",Ea,[i(d,{modelValue:t.maxProfitTimesMin,"onUpdate:modelValue":e[81]||(e[81]=l=>t.maxProfitTimesMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[173]||(e[173]=o("span",null,"至",-1)),i(d,{modelValue:t.maxProfitTimesMax,"onUpdate:modelValue":e[82]||(e[82]=l=>t.maxProfitTimesMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",Da,[e[176]||(e[176]=o("div",{class:"filter-label"},"已售数量范围",-1)),o("div",La,[i(d,{modelValue:t.soldQuantityMin,"onUpdate:modelValue":e[83]||(e[83]=l=>t.soldQuantityMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[175]||(e[175]=o("span",null,"至",-1)),i(d,{modelValue:t.soldQuantityMax,"onUpdate:modelValue":e[84]||(e[84]=l=>t.soldQuantityMax=l),placeholder:"最大值"},null,8,["modelValue"])])])])])]),o("div",Sa,[e[185]||(e[185]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"VIP设置")],-1)),o("div",ja,[o("div",Na,[o("div",qa,[e[179]||(e[179]=o("div",{class:"filter-label"},"VIP级别",-1)),o("div",Qa,[i(T,{modelValue:t.vipLevelMin,"onUpdate:modelValue":e[85]||(e[85]=l=>t.vipLevelMin=l),min:0,max:10,step:1,"controls-position":"right",placeholder:"最小值"},null,8,["modelValue"]),e[178]||(e[178]=o("span",null,"至",-1)),i(T,{modelValue:t.vipLevelMax,"onUpdate:modelValue":e[86]||(e[86]=l=>t.vipLevelMax=l),min:0,max:10,step:1,"controls-position":"right",placeholder:"最大值"},null,8,["modelValue"])])]),o("div",Oa,[e[180]||(e[180]=o("div",{class:"filter-label"},"VIP类型",-1)),i(x,{modelValue:t.vipTypeOperator,"onUpdate:modelValue":e[87]||(e[87]=l=>t.vipTypeOperator=l),placeholder:"请选择操作符",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(ae.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",Aa,[e[181]||(e[181]=o("div",{class:"filter-label"},"每周收益日",-1)),i(x,{modelValue:t.weeklyProfitDays,"onUpdate:modelValue":e[88]||(e[88]=l=>t.weeklyProfitDays=l),placeholder:"请选择收益日",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),i(p,{label:"工作日",value:"1,2,3,4,5"}),i(p,{label:"周末",value:"6,7"}),i(p,{label:"全周",value:"1,2,3,4,5,6,7"})]),_:1},8,["modelValue"])]),o("div",$a,[e[182]||(e[182]=o("div",{class:"filter-label"},"到期退本",-1)),i(x,{modelValue:t.returnPrincipal,"onUpdate:modelValue":e[89]||(e[89]=l=>t.returnPrincipal=l),placeholder:"请选择",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),i(p,{label:"是",value:"1"}),i(p,{label:"否",value:"0"})]),_:1},8,["modelValue"])]),o("div",Fa,[e[183]||(e[183]=o("div",{class:"filter-label"},"免费",-1)),i(x,{modelValue:t.isFree,"onUpdate:modelValue":e[90]||(e[90]=l=>t.isFree=l),placeholder:"请选择",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),i(p,{label:"是",value:"1"}),i(p,{label:"否",value:"0"})]),_:1},8,["modelValue"])]),o("div",Ha,[e[184]||(e[184]=o("div",{class:"filter-label"},"货币",-1)),i(x,{modelValue:t.currency,"onUpdate:modelValue":e[91]||(e[91]=l=>t.currency=l),placeholder:"请选择货币",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(ne.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])])]),o("div",za,[e[195]||(e[195]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"状态设置")],-1)),o("div",Ya,[o("div",Ba,[o("div",Wa,[e[186]||(e[186]=o("div",{class:"filter-label"},"状态",-1)),i(x,{modelValue:t.status,"onUpdate:modelValue":e[92]||(e[92]=l=>t.status=l),placeholder:"请选择状态",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(de.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",Za,[e[187]||(e[187]=o("div",{class:"filter-label"},"出售状态",-1)),i(x,{modelValue:t.sellStatus,"onUpdate:modelValue":e[93]||(e[93]=l=>t.sellStatus=l),placeholder:"请选择出售状态",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),(u(!0),f(M,null,P(me.value,l=>(u(),w(p,{key:l.id,label:l.name,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),o("div",Ga,[e[188]||(e[188]=o("div",{class:"filter-label"},"返佣开关",-1)),i(x,{modelValue:t.commissionEnabled,"onUpdate:modelValue":e[94]||(e[94]=l=>t.commissionEnabled=l),placeholder:"请选择",style:{width:"100%"}},{default:n(()=>[i(p,{label:"全部",value:""}),i(p,{label:"开启",value:1}),i(p,{label:"关闭",value:0})]),_:1},8,["modelValue"])]),o("div",Xa,[e[190]||(e[190]=o("div",{class:"filter-label"},"一级返佣范围(%)",-1)),o("div",Ka,[i(d,{modelValue:t.level1CommissionMin,"onUpdate:modelValue":e[95]||(e[95]=l=>t.level1CommissionMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[189]||(e[189]=o("span",null,"至",-1)),i(d,{modelValue:t.level1CommissionMax,"onUpdate:modelValue":e[96]||(e[96]=l=>t.level1CommissionMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",Ja,[e[192]||(e[192]=o("div",{class:"filter-label"},"二级返佣范围(%)",-1)),o("div",es,[i(d,{modelValue:t.level2CommissionMin,"onUpdate:modelValue":e[97]||(e[97]=l=>t.level2CommissionMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[191]||(e[191]=o("span",null,"至",-1)),i(d,{modelValue:t.level2CommissionMax,"onUpdate:modelValue":e[98]||(e[98]=l=>t.level2CommissionMax=l),placeholder:"最大值"},null,8,["modelValue"])])]),o("div",ls,[e[194]||(e[194]=o("div",{class:"filter-label"},"三级返佣范围(%)",-1)),o("div",ts,[i(d,{modelValue:t.level3CommissionMin,"onUpdate:modelValue":e[99]||(e[99]=l=>t.level3CommissionMin=l),placeholder:"最小值"},null,8,["modelValue"]),e[193]||(e[193]=o("span",null,"至",-1)),i(d,{modelValue:t.level3CommissionMax,"onUpdate:modelValue":e[100]||(e[100]=l=>t.level3CommissionMax=l),placeholder:"最大值"},null,8,["modelValue"])])])])])]),o("div",is,[e[198]||(e[198]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"时间范围")],-1)),o("div",as,[o("div",ss,[o("div",os,[e[196]||(e[196]=o("div",{class:"filter-label"},"添加时间",-1)),i(Ie,{modelValue:t.createTimeRange,"onUpdate:modelValue":e[101]||(e[101]=l=>t.createTimeRange=l),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),o("div",ns,[e[197]||(e[197]=o("div",{class:"filter-label"},"更新时间",-1)),i(Ie,{modelValue:t.updateTimeRange,"onUpdate:modelValue":e[102]||(e[102]=l=>t.updateTimeRange=l),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])])])])])]),_:1},8,["modelValue"])])}}}),As=Vt(us,[["__scopeId","data-v-cfc688bf"]]);export{As as default};
