# FOX 用户端

## 简介
FOX用户端基于uni-app框架开发，支持多端部署（H5、Android、iOS等），为用户提供投资理财、账户管理等功能。

## 技术栈
- 开发框架：uni-app
- 开发工具：HBuilderX
- UI框架：uView
- 状态管理：Vuex
- 网络请求：uni.request

## 目录结构
```
mobile/
├── components/     # 公共组件
├── pages/          # 页面
├── static/         # 静态资源
├── store/          # 状态管理
├── utils/          # 工具函数
└── services/       # 服务和API
```

## 功能模块
- 用户注册与登录
- 首页与项目展示
- 投资管理
- 个人中心
- 资金管理
- 在线客服
- APP下载

## 开发指南

### 环境准备
1. 安装HBuilderX
2. 安装uni-app相关插件
3. 导入项目

### 开发运行
1. 在HBuilderX中打开项目
2. 点击"运行"菜单，选择运行到浏览器或模拟器

### 发布构建
1. 点击"发行"菜单
2. 根据需要选择发布平台（H5、Android、iOS等）
3. 按照提示完成发布流程

## 开发规范
- 页面命名使用小写字母，多个单词用连字符连接
- 组件命名使用PascalCase
- CSS采用BEM命名规范
- 保持代码简洁，避免过度嵌套

## 当前开发状态（2024年6月更新）

目前用户端已完成以下页面的开发：

1. **在线客服页面**
   - 实现客服列表展示
   - 支持多平台客服渠道（WhatsApp、Telegram等）
   - 集成二维码展示和链接跳转
   - 顶部轮播图展示

2. **APP下载页面**
   - 二维码下载展示
   - Android下载按钮
   - 下载说明指引

### 下一步开发计划

1. 完成用户注册和登录功能
2. 开发首页和项目列表
3. 实现个人中心页面
4. 开发资金管理相关功能
