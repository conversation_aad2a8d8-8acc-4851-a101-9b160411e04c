/*! Element Plus v2.9.7 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleCs = factory());
})(this, (function () { 'use strict';

  var cs = {
    name: "cs",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "OK",
        clear: "Vymazat"
      },
      datepicker: {
        now: "Te\u010F",
        today: "Dnes",
        cancel: "Zru\u0161it",
        clear: "Vymazat",
        confirm: "OK",
        selectDate: "Vybrat datum",
        selectTime: "Vybrat \u010Das",
        startDate: "Datum za\u010D\xE1tku",
        startTime: "\u010Cas za\u010D\xE1tku",
        endDate: "Datum konce",
        endTime: "\u010Cas konce",
        prevYear: "P\u0159edchoz\xED rok",
        nextYear: "P\u0159\xED\u0161t\xED rok",
        prevMonth: "P\u0159edchoz\xED m\u011Bs\xEDc",
        nextMonth: "P\u0159\xED\u0161t\xED m\u011Bs\xEDc",
        day: "Den",
        week: "T\xFDden",
        month: "M\u011Bs\xEDc",
        year: "Rok",
        month1: "Leden",
        month2: "\xDAnor",
        month3: "B\u0159ezen",
        month4: "Duben",
        month5: "Kv\u011Bten",
        month6: "\u010Cerven",
        month7: "\u010Cervenec",
        month8: "Srpen",
        month9: "Z\xE1\u0159\xED",
        month10: "\u0158\xEDjen",
        month11: "Listopad",
        month12: "Prosinec",
        weeks: {
          sun: "Ne",
          mon: "Po",
          tue: "\xDAt",
          wed: "St",
          thu: "\u010Ct",
          fri: "P\xE1",
          sat: "So"
        },
        months: {
          jan: "Led",
          feb: "\xDAno",
          mar: "B\u0159e",
          apr: "Dub",
          may: "Kv\u011B",
          jun: "\u010Cer",
          jul: "\u010Cvc",
          aug: "Srp",
          sep: "Z\xE1\u0159",
          oct: "\u0158\xEDj",
          nov: "Lis",
          dec: "Pro"
        }
      },
      select: {
        loading: "Na\u010D\xEDt\xE1n\xED",
        noMatch: "\u017D\xE1dn\xE1 shoda",
        noData: "\u017D\xE1dn\xE1 data",
        placeholder: "Vybrat"
      },
      mention: {
        loading: "Na\u010D\xEDt\xE1n\xED"
      },
      cascader: {
        noMatch: "\u017D\xE1dn\xE1 shoda",
        loading: "Na\u010D\xEDt\xE1n\xED",
        placeholder: "Vybrat",
        noData: "\u017D\xE1dn\xE1 data"
      },
      pagination: {
        goto: "J\xEDt na",
        pagesize: "na stranu",
        total: "Celkem {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "Zpr\xE1va",
        confirm: "OK",
        cancel: "Zru\u0161it",
        error: "Neplatn\xFD vstup"
      },
      upload: {
        deleteTip: "Stisknout pro smaz\xE1n\xED",
        delete: "Vymazat",
        preview: "N\xE1hled",
        continue: "Pokra\u010Dovat"
      },
      table: {
        emptyText: "\u017D\xE1dn\xE1 data",
        confirmFilter: "Potvrdit",
        resetFilter: "Resetovat",
        clearFilter: "V\u0161e",
        sumText: "Celkem"
      },
      tree: {
        emptyText: "\u017D\xE1dn\xE1 data"
      },
      transfer: {
        noMatch: "\u017D\xE1dn\xE1 shoda",
        noData: "\u017D\xE1dn\xE1 data",
        titles: ["Seznam 1", "Seznam 2"],
        filterPlaceholder: "Kl\xED\u010Dov\xE9 slovo",
        noCheckedFormat: "{total} polo\u017Eek",
        hasCheckedFormat: "{checked}/{total} vybr\xE1no"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return cs;

}));
