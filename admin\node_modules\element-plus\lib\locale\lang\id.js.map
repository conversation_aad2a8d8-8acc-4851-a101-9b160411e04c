{"version": 3, "file": "id.js", "sources": ["../../../../../packages/locale/lang/id.ts"], "sourcesContent": ["export default {\n  name: 'id',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON><PERSON>',\n      clear: 'Kosongkan',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: '<PERSON> ini',\n      cancel: '<PERSON><PERSON>',\n      clear: 'Kosong<PERSON>',\n      confirm: 'Ya',\n      selectDate: '<PERSON><PERSON>h tanggal',\n      selectTime: 'Pilih waktu',\n      startDate: 'Tanggal Mulai',\n      startTime: '<PERSON><PERSON>tu <PERSON>',\n      endDate: 'Tanggal Selesai',\n      endTime: '<PERSON><PERSON><PERSON>',\n      prevYear: 'Tahun Sebelumnya',\n      nextYear: 'Tahun Selanjutnya',\n      prevMonth: 'Bulan Sebelumnya',\n      nextMonth: '<PERSON><PERSON><PERSON>',\n      year: '<PERSON>hun',\n      month1: 'Januari',\n      month2: 'Februari',\n      month3: 'Maret',\n      month4: 'April',\n      month5: 'Mei',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: '<PERSON><PERSON><PERSON>',\n      month9: 'September',\n      month10: 'Okto<PERSON>',\n      month11: 'November',\n      month12: 'Desember',\n      week: '<PERSON><PERSON>',\n      weeks: {\n        sun: 'Min',\n        mon: 'Sen',\n        tue: 'Sel',\n        wed: 'Rab',\n        thu: 'Kam',\n        fri: 'Jum',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mei',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Agu',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Memuat',\n      noMatch: 'Tidak ada data yg cocok',\n      noData: 'Tidak ada data',\n      placeholder: 'Pilih',\n    },\n    mention: {\n      loading: 'Memuat',\n    },\n    cascader: {\n      noMatch: 'Tidak ada data yg cocok',\n      loading: 'Memuat',\n      placeholder: 'Pilih',\n      noData: 'Tidak ada data',\n    },\n    pagination: {\n      goto: 'Pergi ke',\n      pagesize: '/halaman',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'Penggunaan yang tidak akan digunakan lagi terdeteksi, silakan lihat dokumentasi el-pagination untuk lebih jelasnya',\n    },\n    messagebox: {\n      title: 'Pesan',\n      confirm: 'Ya',\n      cancel: 'Batal',\n      error: 'Masukan ilegal',\n    },\n    upload: {\n      deleteTip: 'Tekan hapus untuk melanjutkan',\n      delete: 'Hapus',\n      preview: 'Pratinjau',\n      continue: 'Lanjutkan',\n    },\n    table: {\n      emptyText: 'Tidak ada data',\n      confirmFilter: 'Konfirmasi',\n      resetFilter: 'Atur ulang',\n      clearFilter: 'Semua',\n      sumText: 'Jumlah',\n    },\n    tree: {\n      emptyText: 'Tidak ada data',\n    },\n    transfer: {\n      noMatch: 'Tidak ada data yg cocok',\n      noData: 'Tidak ada data',\n      titles: ['Daftar 1', 'Daftar 2'],\n      filterPlaceholder: 'Masukan kata kunci',\n      noCheckedFormat: '{total} item',\n      hasCheckedFormat: '{checked}/{total} terpilih',\n    },\n    image: {\n      error: 'GAGAL',\n    },\n    pageHeader: {\n      title: 'Kembali',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ya',\n      cancelButtonText: 'Tidak',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,KAAK,EAAE,WAAW;AACxB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,WAAW,EAAE,OAAO;AAC1B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,MAAM,EAAE,gBAAgB;AAC9B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,kBAAkB,EAAE,oHAAoH;AAC9I,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,+BAA+B;AAChD,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,WAAW;AAC3B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,aAAa,EAAE,YAAY;AACjC,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gBAAgB;AACjC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AACtC,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,eAAe,EAAE,cAAc;AACrC,MAAM,gBAAgB,EAAE,4BAA4B;AACpD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,OAAO;AACpB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,gBAAgB,EAAE,OAAO;AAC/B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}