import{a as t}from"./index-t--hEgTQ.js";import{p as n,a0 as c}from"./index-LncY9lAB.js";const s=t.create({baseURL:"",timeout:6e4});s.interceptors.request.use(e=>{const i=localStorage.getItem("token");return i&&(e.headers.Authorization=`Bearer ${i}`),e},e=>Promise.reject(e));s.interceptors.response.use(e=>{const i=e.headers["x-new-token"];i&&localStorage.setItem("token",i);const a=e.data;return a.code!==void 0&&a.code!==200&&a.code!==201?(a.message&&(a.message.includes("账号已存在")||a.message.includes("用户名已存在")||a.message.includes("already exists"))?n.warning(a.message||"账号已存在，请使用其他账号"):n.error(a.message||"请求失败"),a.code===401&&c.confirm("登录状态已过期，请重新登录","系统提示",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.removeItem("token"),window.location.reload()}),Promise.reject(new Error(a.message||"请求失败"))):e.config.url&&(e.config.url.includes("/api/admin/customer-services")||e.config.url.includes("/api/mobile/customer-services")||e.config.url.includes("/api/admin/customer-service-images")||e.config.url.includes("/api/mobile/customer-service-images")||e.config.url.includes("/api/admin/payment-channels")||e.config.url.includes("/api/admin/bank-mappings")||e.config.url.includes("/api/admin/banks")||e.config.url.includes("/api/admin/deposits")||e.config.url.includes("/api/admin/withdrawals"))?a:a.data!==void 0?a.data:a},e=>{let i=e.message;if(e.code==="ECONNABORTED"&&e.message.includes("timeout")){if(e.config&&e.config.url&&e.config.url.includes("/api/admin/admins")&&e.config.method==="post")return Promise.resolve({code:200,message:"创建成功",data:null});if(e.config&&e.config.url&&e.config.url.includes("/api/admin/attachments/upload")&&e.config.method==="post")return n.info("上传请求超时，请刷新页面查看结果"),Promise.resolve({code:200,message:"上传可能已成功，请刷新页面查看",data:null})}if(e.response)switch(e.response.status){case 400:i="请求错误";break;case 401:i="未授权，请重新登录",localStorage.removeItem("token"),setTimeout(()=>{window.location.href="/login"},1e3);break;case 403:i="拒绝访问";break;case 404:i=`请求地址出错: ${e.response.config.url}`;break;case 408:i="请求超时";break;case 409:if(e.config&&e.config.url&&e.config.url.includes("/api/admin/admins")&&e.config.method==="post")return n.warning("账号已存在，请使用其他账号"),Promise.reject(e);i="账号已存在，请使用其他账号";break;case 500:i="服务器内部错误";break;case 501:i="服务未实现";break;case 502:i="网关错误";break;case 503:i="服务不可用";break;case 504:i="网关超时";break;case 505:i="HTTP版本不受支持";break;default:i=`连接错误${e.response.status}`}else e.code==="ECONNABORTED"&&e.message.includes("timeout")?i="请求超时，请刷新页面查看结果":e.code==="ERR_NETWORK"&&(i="网络错误，请检查网络连接");return n.error(i),Promise.reject(e)});function d(e,i){return s.get(e,{params:i})}function m(e,i){return s.post(e,i)}function g(e,i){return s.put(e,i)}function f(e,i){return s.delete(e,{data:i})}export{m as a,f as d,d as g,g as p,s};
