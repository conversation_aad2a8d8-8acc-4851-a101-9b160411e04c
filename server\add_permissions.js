const mysql = require('mysql2/promise');

async function main() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'MySQL3352~!',
      database: 'fox_db'
    });

    // 添加会员列表页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('添加会员', 'members:add', '添加新会员', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('编辑会员', 'members:edit', '编辑会员信息', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('删除会员', 'members:delete', '删除会员', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看会员详情', 'members:view', '查看会员详情', 'operation')");

    // 添加充值订单页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('审核充值', 'deposits:approve', '审核充值订单', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('拒绝充值', 'deposits:reject', '拒绝充值订单', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看充值详情', 'deposits:view', '查看充值详情', 'operation')");

    // 添加用户投资页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看投资详情', 'investments:view', '查看投资详情', 'operation')");

    // 添加提现记录页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('审核提现', 'withdrawals:approve', '审核提现', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('拒绝提现', 'withdrawals:reject', '拒绝提现', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看提现详情', 'withdrawals:view', '查看提现详情', 'operation')");

    // 添加用户交易页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看交易详情', 'transactions:view', '查看交易详情', 'operation')");

    // 添加佣金记录页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看佣金详情', 'commissions:view', '查看佣金详情', 'operation')");

    // 添加用户银行卡页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看银行卡详情', 'user-cards:view', '查看银行卡详情', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('删除银行卡', 'user-cards:delete', '删除银行卡', 'operation')");

    // 添加收款银行卡页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('添加收款卡', 'receiving-cards:add', '添加收款卡', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('编辑收款卡', 'receiving-cards:edit', '编辑收款卡', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('删除收款卡', 'receiving-cards:delete', '删除收款卡', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看收款卡详情', 'receiving-cards:view', '查看收款卡详情', 'operation')");

    // 添加代理管理页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('添加代理', 'agents:add', '添加代理', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('编辑代理', 'agents:edit', '编辑代理', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('删除代理', 'agents:delete', '删除代理', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看代理详情', 'agents:view', '查看代理详情', 'operation')");

    // 添加系统设置页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('修改系统设置', 'settings:edit', '修改系统设置', 'operation')");

    // 添加通知消息页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('添加通知', 'notifications:add', '添加通知', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('编辑通知', 'notifications:edit', '编辑通知', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('删除通知', 'notifications:delete', '删除通知', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看通知详情', 'notifications:view', '查看通知详情', 'operation')");

    // 添加管理员设置页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('添加管理员', 'admins:add', '添加管理员', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('编辑管理员', 'admins:edit', '编辑管理员', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('删除管理员', 'admins:delete', '删除管理员', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看管理员详情', 'admins:view', '查看管理员详情', 'operation')");

    // 添加角色管理页面的操作权限
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('添加角色', 'roles:add', '添加角色', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('编辑角色', 'roles:edit', '编辑角色', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('删除角色', 'roles:delete', '删除角色', 'operation')");
    await connection.execute("INSERT INTO permissions (name, code, description, type) VALUES ('查看角色详情', 'roles:view', '查看角色详情', 'operation')");

    console.log('所有权限添加成功');
    await connection.end();
  } catch (error) {
    console.error('添加权限失败:', error);
  }
}

main();
