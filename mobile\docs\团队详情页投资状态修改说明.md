# 团队详情页投资状态修改说明

## 🎯 **修改概述**

将团队详情页面的状态分类从"充值状态"改为"投资状态"，更符合收益返佣的业务逻辑。

## 🔄 **主要变化**

### **1. 后端API修改 (server/controllers/inviteController.js)**

#### **新增投资状态查询**
```javascript
// 查询这些用户是否有投资记录
const investmentUsers = [];
if (userIds.length > 0) {
  const { Investment } = require('../models');
  const investments = await Investment.findAll({
    attributes: ['user_id'],
    where: {
      user_id: userIds,
      status: ['active', 'completed'] // 有效的投资状态
    },
    group: ['user_id']
  });

  // 将有投资记录的用户ID添加到数组中
  investmentUsers.push(...investments.map(i => i.user_id));
}
```

#### **返回数据新增投资状态字段**
```javascript
// 格式化数据
let invitees = rows.map(relation => ({
  id: relation.user.id,
  username: relation.user.username,
  name: relation.user.name || relation.user.username,
  avatar: relation.user.avatar,
  level: relation.level,
  created_at: relation.createdAt,
  recharged: depositUsers.includes(relation.user.id),
  invested: investmentUsers.includes(relation.user.id), // ⭐ 新增投资状态
  balance: relation.user.balance || 0
}));
```

#### **新增投资状态筛选**
```javascript
// 根据类型筛选
if (type === 'recharged') {
  invitees = invitees.filter(user => user.recharged);
} else if (type === 'not-recharged') {
  invitees = invitees.filter(user => !user.recharged);
} else if (type === 'invested') {          // ⭐ 新增
  invitees = invitees.filter(user => user.invested);
} else if (type === 'not-invested') {     // ⭐ 新增
  invitees = invitees.filter(user => !user.invested);
}
```

### **2. 前端页面修改 (mobile/pages/invite/team-detail.vue)**

#### **类型标签修改**
```javascript
// 修改前
types: [
  { id: 'total', name: 'Total' },
  { id: 'recharged', name: 'With Deposit' },
  { id: 'not-recharged', name: 'No Deposit' }
]

// 修改后
types: [
  { id: 'total', name: 'Total' },
  { id: 'invested', name: 'With Investment' },      // ⭐ 修改
  { id: 'not-invested', name: 'No Investment' }     // ⭐ 修改
]
```

#### **筛选逻辑修改**
```javascript
// 修改前
if (this.currentType === 'recharged') {
  result = result.filter(member => member.recharged);
} else if (this.currentType === 'not-recharged') {
  result = result.filter(member => !member.recharged);
}

// 修改后
if (this.currentType === 'invested') {              // ⭐ 修改
  result = result.filter(member => member.invested);
} else if (this.currentType === 'not-invested') {   // ⭐ 修改
  result = result.filter(member => !member.invested);
}
```

#### **API请求参数修改**
```javascript
// 修改前
if (this.currentType === 'recharged') {
  params.type = 'recharged';
} else if (this.currentType === 'not-recharged') {
  params.type = 'not-recharged';
}

// 修改后
if (this.currentType === 'invested') {              // ⭐ 修改
  params.type = 'invested';
} else if (this.currentType === 'not-invested') {   // ⭐ 修改
  params.type = 'not-invested';
}
```

#### **数据格式化修改**
```javascript
// 格式化数据
const formattedMembers = items.map(item => ({
  id: item.id,
  username: item.username,
  name: item.name || item.username,
  joinDate: this.formatDate(item.created_at),
  recharged: item.recharged || false, // 充值状态（保留）
  invested: item.invested || false,   // ⭐ 新增投资状态
  level: this.currentLevel
}));
```

#### **模板显示修改**
```html
<!-- 修改前 -->
<text class="member-status" :class="{ 'recharged': member.recharged }">
  {{ member.recharged ? 'With Deposit' : 'No Deposit' }}
</text>

<!-- 修改后 -->
<text class="member-status" :class="{ 'invested': member.invested }">
  {{ member.invested ? 'With Investment' : 'No Investment' }}
</text>
```

#### **CSS样式修改**
```scss
// 修改前
.member-status {
  font-size: 28rpx;
  color: #ff6b6b;

  &.recharged {
    color: #00e5ff;
  }
}

// 修改后
.member-status {
  font-size: 28rpx;
  color: #ff6b6b;

  &.invested {    // ⭐ 修改类名
    color: #00e5ff;
  }
}
```

## 📊 **新的状态分类**

### **投资状态分类**
- **Total**: 显示所有成员
- **With Investment**: 显示已购买产品的成员
- **No Investment**: 显示未购买产品的成员

### **状态判断逻辑**
- **With Investment**: 用户在 `investments` 表中有状态为 `active` 或 `completed` 的记录
- **No Investment**: 用户在 `investments` 表中没有有效的投资记录

### **显示效果**
- **With Investment**: 青色文字 (#00e5ff)
- **No Investment**: 红色文字 (#ff6b6b)

## 🎯 **业务价值**

### **1. 更符合收益返佣逻辑**
- 收益返佣只有在用户购买产品并产生收益时才会触发
- 关注投资状态比充值状态更有意义

### **2. 提供更有价值的数据**
- **With Investment**: 活跃用户，已经开始投资
- **No Investment**: 潜在用户，可以引导其购买产品

### **3. 便于分析转化率**
- 可以分析推荐用户的投资转化率
- 帮助优化推荐策略

## 🔧 **技术实现**

### **数据库查询**
- 查询 `investments` 表获取有效投资记录
- 使用 `GROUP BY user_id` 避免重复统计
- 只统计 `active` 和 `completed` 状态的投资

### **API兼容性**
- 保留原有的 `recharged` 字段，确保向后兼容
- 新增 `invested` 字段，提供投资状态信息
- 支持新的筛选参数 `invested` 和 `not-invested`

### **前端处理**
- 使用计算属性进行客户端筛选
- 支持服务端筛选以提高性能
- 保持原有的分页和加载更多功能

## 🎯 **级别动态化修改**

### **3. 级别切换动态化**

#### **获取收益返佣比例配置**
```javascript
// 获取收益返佣比例配置
async fetchCommissionRates() {
  try {
    // 获取1-3级收益返佣比例
    const promises = [
      getSystemParam('[site.income_commission_rate_1]'),
      getSystemParam('[site.income_commission_rate_2]'),
      getSystemParam('[site.income_commission_rate_3]')
    ];

    const responses = await Promise.all(promises);

    // 处理返回的数据
    responses.forEach((response, index) => {
      const level = index + 1;
      if (response && response.code === 200 && response.data) {
        const rate = parseFloat(response.data.param_value) || 0;
        this.commissionRates[level] = rate;
      }
    });
  } catch (error) {
    console.error('获取收益返佣比例失败:', error);
  }
}
```

#### **动态生成有效级别**
```javascript
// 计算有效的级别（返佣比例大于0的级别）
activeLevels() {
  const activeLevels = [];

  // 遍历1-3级，找出返佣比例大于0的级别
  for (let level = 1; level <= 3; level++) {
    const rate = this.commissionRates[level];
    if (rate && rate > 0) {
      activeLevels.push({
        id: level,
        name: `Level ${level}`
      });
    }
  }

  return activeLevels;
}
```

#### **级别验证和默认选择**
```javascript
// 验证当前级别是否有效
validateCurrentLevel() {
  const validLevels = this.activeLevels.map(level => level.id);

  // 如果当前级别无效，选择第一个有效级别
  if (validLevels.length > 0 && !validLevels.includes(this.currentLevel)) {
    this.currentLevel = validLevels[0];
  }

  // 如果没有有效级别，跳转回团队主页
  if (validLevels.length === 0) {
    uni.showToast({
      title: 'No active commission levels',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
}
```

#### **模板动态渲染**
```html
<!-- 级别切换标签 -->
<view class="level-tabs" v-if="activeLevels.length > 1">
  <view
    v-for="level in activeLevels"
    :key="level.id"
    class="level-tab"
    :class="{ 'active': currentLevel === level.id }"
    @click="setCurrentLevel(level.id)"
  >
    Level {{ level.id }}
  </view>
</view>
```

#### **级别切换验证**
```javascript
setCurrentLevel(level) {
  // 验证级别是否有效
  const validLevels = this.activeLevels.map(l => l.id);
  if (!validLevels.includes(level)) {
    uni.showToast({
      title: 'Invalid level',
      icon: 'none'
    });
    return;
  }

  if (this.currentLevel !== level) {
    this.currentLevel = level;
    this.page = 1;
    this.teamMembers = [];
    this.fetchTeamMembers();
  }
}
```

### **级别显示逻辑**

#### **场景1：只有1个有效级别**
- 不显示级别切换标签
- 直接显示该级别的成员列表

#### **场景2：有多个有效级别**
- 显示级别切换标签
- 只显示有效级别的标签

#### **场景3：没有有效级别**
- 显示提示信息
- 自动跳转回团队主页

### **示例效果**

#### **配置：Level 1=10%, Level 2=0%, Level 3=5%**
```
┌─────────────────────────────────┐
│     [Level 1] [Level 3]         │  ← 只显示有效级别
└─────────────────────────────────┘
```

#### **配置：Level 1=10%, Level 2=0%, Level 3=0%**
```
┌─────────────────────────────────┐
│        (无级别切换标签)           │  ← 只有1个级别，不显示切换
└─────────────────────────────────┘
```

#### **配置：Level 1=0%, Level 2=0%, Level 3=0%**
```
┌─────────────────────────────────┐
│   No active commission levels   │  ← 显示提示并跳转
└─────────────────────────────────┘
```

## ✅ **完整修改完成**

现在团队详情页面已经完全实现了动态返佣逻辑：

1. ✅ **投资状态分类**：显示投资状态而不是充值状态
2. ✅ **动态级别切换**：只显示有效的返佣级别
3. ✅ **级别验证**：确保所有操作都在有效级别范围内
4. ✅ **自动处理**：无效级别时自动选择或跳转

团队详情页面现在完全符合收益返佣的业务逻辑，提供了更准确和有用的数据展示。
