require('dotenv').config();
const mysql = require('mysql2/promise');

async function resetDatabase() {
  try {
    console.log('开始重置数据库...');

    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USERNAME || 'root',
      password: 'MySQL3352~!',
      database: process.env.DB_DATABASE || 'fox_db'
    });

    // 1. 删除现有的表
    console.log('删除现有的表...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    
    // 获取所有表名
    const [tables] = await connection.execute('SHOW TABLES');
    for (const tableObj of tables) {
      const tableName = Object.values(tableObj)[0];
      await connection.execute(`DROP TABLE IF EXISTS \`${tableName}\``);
      console.log(`已删除表: ${tableName}`);
    }
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    console.log('所有表已删除');

    // 2. 创建新的表结构
    console.log('创建新的表结构...');

    // 创建管理员表
    await connection.execute(`
      CREATE TABLE admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        nickname VARCHAR(50) NOT NULL,
        is_super BOOLEAN NOT NULL DEFAULT FALSE,
        status BOOLEAN NOT NULL DEFAULT TRUE,
        last_login DATETIME,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('已创建管理员表');

    // 创建角色表
    await connection.execute(`
      CREATE TABLE roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description VARCHAR(255),
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('已创建角色表');

    // 创建权限表
    await connection.execute(`
      CREATE TABLE permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        code VARCHAR(50) NOT NULL UNIQUE,
        description VARCHAR(255),
        type VARCHAR(20) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('已创建权限表');

    // 创建角色权限关联表
    await connection.execute(`
      CREATE TABLE role_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        role_id INT NOT NULL,
        permission_id INT NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_role_permission (role_id, permission_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
      )
    `);
    console.log('已创建角色权限关联表');

    // 创建管理员角色关联表
    await connection.execute(`
      CREATE TABLE admin_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        role_id INT NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_admin_role (admin_id, role_id),
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
      )
    `);
    console.log('已创建管理员角色关联表');

    // 3. 创建基础数据
    console.log('创建基础数据...');

    // 创建角色
    await connection.execute(`
      INSERT INTO roles (id, name, description) VALUES
      (1, '超级管理员组', '拥有所有权限的超级管理员角色'),
      (2, '管理员组', '普通管理员角色')
    `);
    console.log('已创建角色数据');

    // 创建权限
    await connection.execute(`
      INSERT INTO permissions (id, name, code, description, type) VALUES
      (1, '首页', 'dashboard', '访问系统首页', 'menu'),
      (2, '会员列表', 'members', '访问会员列表页面', 'menu'),
      (3, '充值订单', 'deposits', '访问充值订单页面', 'menu'),
      (4, '用户投资', 'investments', '访问用户投资页面', 'menu'),
      (5, '提现记录', 'withdrawals', '访问提现记录页面', 'menu'),
      (6, '用户交易', 'transactions', '访问用户交易页面', 'menu'),
      (7, '佣金记录', 'commissions', '访问佣金记录页面', 'menu'),
      (8, '用户银行卡', 'user-cards', '访问用户银行卡页面', 'menu'),
      (9, '收款银行卡', 'receiving-cards', '访问收款银行卡页面', 'menu'),
      (10, '代理管理', 'agents', '访问代理管理页面', 'menu'),
      (11, '系统设置', 'settings', '访问系统设置页面', 'menu'),
      (12, '通知消息', 'notifications', '访问通知消息页面', 'menu'),
      (13, '管理员设置', 'admins', '访问管理员设置页面', 'menu'),
      (14, '角色管理', 'roles', '访问角色管理页面', 'menu')
    `);
    console.log('已创建权限数据');

    // 为角色分配权限
    // 超级管理员拥有所有权限
    const [permissions] = await connection.execute('SELECT id FROM permissions');
    for (const perm of permissions) {
      await connection.execute(`
        INSERT INTO role_permissions (role_id, permission_id) VALUES (1, ${perm.id})
      `);
    }
    
    // 管理员组拥有除了管理员设置和角色管理之外的所有权限
    for (const perm of permissions) {
      if (perm.id !== 13 && perm.id !== 14) {
        await connection.execute(`
          INSERT INTO role_permissions (role_id, permission_id) VALUES (2, ${perm.id})
        `);
      }
    }
    console.log('已为角色分配权限');

    // 创建超级管理员账号
    // 密码: admin123 (实际应用中应该使用bcrypt等加密)
    await connection.execute(`
      INSERT INTO admins (username, password, nickname, is_super, status) VALUES
      ('admin', 'admin123', '超级管理员', TRUE, TRUE)
    `);
    console.log('已创建超级管理员账号');

    // 为超级管理员分配角色
    await connection.execute(`
      INSERT INTO admin_roles (admin_id, role_id) VALUES (1, 1)
    `);
    console.log('已为超级管理员分配角色');

    console.log('数据库重置完成');
    await connection.end();
    process.exit(0);
  } catch (error) {
    console.error('重置数据库失败:', error);
    process.exit(1);
  }
}

resetDatabase();
