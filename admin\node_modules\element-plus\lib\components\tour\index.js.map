{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/tour/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\nimport Tour from './src/tour.vue'\nimport TourStep from './src/step.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTour: SFCWithInstall<typeof Tour> & {\n  TourStep: typeof TourStep\n} = withInstall(Tour, {\n  TourStep,\n})\nexport const ElTourStep: SFCWithInstall<typeof TourStep> =\n  withNoopInstall(TourStep)\nexport default ElTour\n\nexport * from './src/tour'\nexport * from './src/step'\nexport * from './src/content'\nexport type { TourMask, TourGap, TourBtnProps } from './src/types'\n"], "names": ["withInstall", "Tour", "TourStep", "withNoopInstall"], "mappings": ";;;;;;;;;;;AAGY,MAAC,MAAM,GAAGA,mBAAW,CAACC,iBAAI,EAAE;AACxC,YAAEC,iBAAQ;AACV,CAAC,EAAE;AACS,MAAC,UAAU,GAAGC,uBAAe,CAACD,iBAAQ;;;;;;;;;;;;;;"}