{"version": 3, "file": "da.js", "sources": ["../../../../../packages/locale/lang/da.ts"], "sourcesContent": ["export default {\n  name: 'da',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON>yd',\n    },\n    datepicker: {\n      now: 'Nu',\n      today: 'I dag',\n      cancel: 'Ann<PERSON>er',\n      clear: '<PERSON>yd',\n      confirm: 'OK',\n      selectDate: 'Vælg dato',\n      selectTime: 'Vælg tidspunkt',\n      startDate: 'Startdato',\n      startTime: 'Starttidspunkt',\n      endDate: 'Slutdato',\n      endTime: 'Sluttidspunkt',\n      prevYear: 'Forrige år',\n      nextYear: 'Næste år',\n      prevMonth: 'Forrige måned',\n      nextMonth: 'Næste måned',\n      year: '',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'Marts',\n      month4: 'April',\n      month5: 'Maj',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'December',\n      week: 'uge',\n      weeks: {\n        sun: '<PERSON>øn',\n        mon: 'Man',\n        tue: 'Tir',\n        wed: 'Ons',\n        thu: 'Tor',\n        fri: 'Fre',\n        sat: 'Lør',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Henter',\n      noMatch: 'Ingen matchende data',\n      noData: 'Ingen data',\n      placeholder: 'Vælg',\n    },\n    mention: {\n      loading: 'Henter',\n    },\n    cascader: {\n      noMatch: 'Ingen matchende data',\n      loading: 'Henter',\n      placeholder: 'Vælg',\n      noData: 'Ingen data',\n    },\n    pagination: {\n      goto: 'Gå til',\n      pagesize: '/side',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Annuller',\n      error: 'Ugyldig input',\n    },\n    upload: {\n      deleteTip: 'tryk slet for at fjerne',\n      delete: 'Slet',\n      preview: 'Forhåndsvisning',\n      continue: 'Fortsæt',\n    },\n    table: {\n      emptyText: 'Ingen data',\n      confirmFilter: 'Bekræft',\n      resetFilter: 'Nulstil',\n      clearFilter: 'Alle',\n      sumText: 'Sum',\n    },\n    tree: {\n      emptyText: 'Ingen data',\n    },\n    transfer: {\n      noMatch: 'Ingen matchende data',\n      noData: 'Ingen data',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Indtast søgeord',\n      noCheckedFormat: '{total} emner',\n      hasCheckedFormat: '{checked}/{total} valgt',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,KAAK;AAClB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,IAAI;AACf,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,SAAS;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,YAAY;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,eAAe;AAC5B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,yBAAyB;AAC1C,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,QAAQ,EAAE,YAAY;AAC5B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,YAAY;AACjC,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,oBAAoB;AAC7C,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,yBAAyB;AACjD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}