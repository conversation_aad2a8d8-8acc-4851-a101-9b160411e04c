/**
 * CPU 分析脚本
 * 用于监控 Node.js 应用程序的 CPU 使用情况
 */
const v8Profiler = require('v8-profiler-next');
const fs = require('fs');
const path = require('path');

// 设置分析器标题
const title = 'fox-app-cpu-profile';

/**
 * 开始 CPU 分析
 * @param {number} duration - 分析持续时间（毫秒）
 * @returns {Promise<string>} - 分析文件路径
 */
function startCpuProfiling(duration = 30000) {
  return new Promise((resolve, reject) => {
    try {
      console.log(`开始 CPU 分析，持续 ${duration / 1000} 秒...`);
      
      // 开始分析
      v8Profiler.startProfiling(title, true);
      
      // 设置定时器，在指定时间后停止分析
      setTimeout(() => {
        // 停止分析并获取分析结果
        const profile = v8Profiler.stopProfiling(title);
        
        // 创建输出目录
        const dir = path.join(__dirname, '../logs/profiles');
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        
        // 生成文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${title}-${timestamp}.cpuprofile`;
        const filepath = path.join(dir, filename);
        
        // 将分析结果导出到文件
        profile.export()
          .pipe(fs.createWriteStream(filepath))
          .on('finish', () => {
            profile.delete();
            console.log(`CPU 分析完成，结果保存在: ${filepath}`);
            console.log('您可以在 Chrome DevTools 的 Performance 面板中加载此文件进行分析');
            resolve(filepath);
          });
      }, duration);
    } catch (error) {
      console.error('CPU 分析失败:', error);
      reject(error);
    }
  });
}

// 如果作为独立脚本运行，则开始分析
if (require.main === module) {
  // 检查是否安装了 v8-profiler-next
  try {
    require('v8-profiler-next');
  } catch (error) {
    console.error('请先安装 v8-profiler-next 包:');
    console.error('npm install v8-profiler-next --save-dev');
    process.exit(1);
  }
  
  // 开始分析
  startCpuProfiling()
    .catch(error => {
      console.error('分析过程中出错:', error);
      process.exit(1);
    });
}

module.exports = { startCpuProfiling };
