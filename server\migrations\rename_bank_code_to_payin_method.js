const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

// 直接执行迁移
async function runMigration() {
  try {
    console.log('开始重命名 bank_code 字段为 payin_method...');

    // 检查表是否存在
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'bank_channel_mappings'");
    if (tables.length === 0) {
      console.log('bank_channel_mappings 表不存在，跳过迁移');
      return;
    }

    // 检查字段是否存在
    const [columns] = await sequelize.query("SHOW COLUMNS FROM bank_channel_mappings");
    const columnNames = columns.map(column => column.Field);

    if (columnNames.includes('bank_code') && !columnNames.includes('payin_method')) {
      // 重命名 bank_code 为 payin_method，并修改类型为 INT
      await sequelize.query(`
        ALTER TABLE bank_channel_mappings
        CHANGE COLUMN bank_code payin_method INT NULL COMMENT '代收方式编号'
      `);
      console.log('成功将 bank_code 字段重命名为 payin_method');
    } else if (columnNames.includes('payin_method')) {
      console.log('payin_method 字段已存在，跳过重命名');
    } else {
      console.log('bank_code 字段不存在，无法重命名');
    }

    await sequelize.close();
    console.log('迁移完成');
  } catch (error) {
    console.error('迁移失败:', error);
    await sequelize.close();
    process.exit(1);
  }
}

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  runMigration();
}

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log('开始重命名 bank_code 字段为 payin_method...');

      // 检查表是否存在
      const [tables] = await queryInterface.sequelize.query("SHOW TABLES LIKE 'bank_channel_mappings'");
      if (tables.length === 0) {
        console.log('bank_channel_mappings 表不存在，跳过迁移');
        return;
      }

      // 检查字段是否存在
      const [columns] = await queryInterface.sequelize.query("SHOW COLUMNS FROM bank_channel_mappings");
      const columnNames = columns.map(column => column.Field);

      if (columnNames.includes('bank_code') && !columnNames.includes('payin_method')) {
        // 重命名 bank_code 为 payin_method，并修改类型为 INT
        await queryInterface.sequelize.query(`
          ALTER TABLE bank_channel_mappings
          CHANGE COLUMN bank_code payin_method INT NULL COMMENT '代收方式编号'
        `);
        console.log('成功将 bank_code 字段重命名为 payin_method');
      } else if (columnNames.includes('payin_method')) {
        console.log('payin_method 字段已存在，跳过重命名');
      } else {
        console.log('bank_code 字段不存在，无法重命名');
      }

    } catch (error) {
      console.error('重命名字段失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      console.log('开始回滚：重命名 payin_method 字段为 bank_code...');

      // 检查字段是否存在
      const [columns] = await queryInterface.sequelize.query("SHOW COLUMNS FROM bank_channel_mappings");
      const columnNames = columns.map(column => column.Field);

      if (columnNames.includes('payin_method') && !columnNames.includes('bank_code')) {
        // 重命名 payin_method 为 bank_code，并修改类型为 VARCHAR(100)
        await queryInterface.sequelize.query(`
          ALTER TABLE bank_channel_mappings
          CHANGE COLUMN payin_method bank_code VARCHAR(100) NULL COMMENT '银行编码'
        `);
        console.log('成功将 payin_method 字段回滚为 bank_code');
      } else {
        console.log('无法回滚，字段状态不符合预期');
      }

    } catch (error) {
      console.error('回滚失败:', error);
      throw error;
    }
  }
};
