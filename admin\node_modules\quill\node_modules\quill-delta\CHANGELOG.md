## v3.6.3

- Performance optimization for `compose`

## v3.6.2

- Documentation fixes

## v3.6.1

- Stop using `=>` because of IE11

## v3.6.0

- Add experimental method `changeLength()`


## v3.5.0

- Add counter and early return to `eachLine()`


## v3.4.0

- Support index suggestion in `diff()`


## v3.3.0

- Add `partition()`


## v3.2.0

- Add `eachLine()`, `map()`, `reduce()`, `filter()`, `forEach()`


## v3.1.0

- Pull out quilljs/delta from ottypes/rich-text


## v3.0.0

#### Breaking Changes
- Deep copy and compare attributes and deltas


## v2.1.0

- Add `concat()` method for document Deltas


## v2.0.0

#### Breaking Changes
- `compose()` returns a new Delta instead of self-modifying

#### Features
- Support embed being any non-string type
