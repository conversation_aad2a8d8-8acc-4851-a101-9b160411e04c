/**
 * 充值订单路由
 */
const express = require('express');
const router = express.Router();
const depositController = require('../controllers/depositController');
const { verifyAdminToken, verifyUserToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 管理员端路由
const adminRouter = express.Router();

// 所有管理员端路由都需要验证token
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/deposits:
 *   get:
 *     summary: 获取充值订单列表
 *     tags: [充值管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         description: 用户ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 订单状态
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', depositController.getDeposits);

/**
 * @swagger
 * /api/admin/deposits/{id}:
 *   get:
 *     summary: 获取充值订单详情
 *     tags: [充值管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 充值订单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 充值订单不存在
 */
adminRouter.get('/:id', depositController.getDeposit);

/**
 * @swagger
 * /api/admin/deposits:
 *   post:
 *     summary: 创建充值订单
 *     tags: [充值管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - amount
 *               - payment_method
 *             properties:
 *               user_id:
 *                 type: integer
 *                 description: 用户ID
 *               amount:
 *                 type: number
 *                 description: 充值金额
 *               payment_method:
 *                 type: string
 *                 description: 支付方式
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 用户不存在
 */
adminRouter.post('/', depositController.createDeposit);

/**
 * @swagger
 * /api/admin/deposits/{id}/approve:
 *   put:
 *     summary: 审核充值订单
 *     tags: [充值管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 充值订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [completed, cancelled]
 *                 description: 订单状态
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 审核成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 充值订单不存在
 */
adminRouter.put('/:id/approve', depositController.approveDeposit);

/**
 * @swagger
 * /api/admin/deposits/{id}/mock-callback:
 *   post:
 *     summary: 模拟支付回调
 *     tags: [充值管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 充值订单ID
 *     responses:
 *       200:
 *         description: 模拟回调成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 充值订单不存在
 */
adminRouter.post('/:id/mock-callback', depositController.mockCallback);

/**
 * @swagger
 * /api/admin/deposits/{id}/payment-platform-order-no:
 *   put:
 *     summary: 更新支付平台订单号
 *     tags: [充值管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 充值订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - payment_platform_order_no
 *             properties:
 *               payment_platform_order_no:
 *                 type: string
 *                 description: 支付平台订单号
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 充值订单不存在
 */
adminRouter.put('/:id/payment-platform-order-no', depositController.updatePaymentPlatformOrderNo);

module.exports = {
  admin: adminRouter
};
