/**
 * 用户相关 API 服务
 */
import request from '../request';

// 定义请求方法
const get = (url, options = {}) => request({ url, method: 'GET', ...options });
const post = (url, data, options = {}) => request({ url, method: 'POST', data, ...options });
const put = (url, data, options = {}) => request({ url, method: 'PUT', data, ...options });
const del = (url, options = {}) => request({ url, method: 'DELETE', ...options });

/**
 * 用户注册
 * @param {Object} data - 注册数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.name - 姓名
 * @param {string} data.phone - 手机号
 * @param {string} data.email - 邮箱（可选）
 * @param {string} data.invite_code - 邀请码
 * @returns {Promise} - 返回注册结果
 */
export function register(data) {
  return post('/api/user/register', data);
}

/**
 * 用户登录
 * @param {Object} data - 登录数据
 * @param {string} data.username - 用户名/手机号/邮箱
 * @param {string} data.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export function login(data) {
  return post('/api/user/login', data);
}

/**
 * 用户登出
 * @returns {Promise} - 返回登出结果
 */
export function logout() {
  return post('/api/user/logout');
}

/**
 * 获取用户个人信息
 * @returns {Promise} - 返回用户信息
 */
export function getProfile() {
  return get('/api/user/profile');
}

/**
 * 更新用户密码
 * @param {Object} data - 密码数据
 * @param {string} data.old_password - 旧密码
 * @param {string} data.new_password - 新密码
 * @returns {Promise} - 返回更新结果
 */
export function updatePassword(data) {
  return put('/api/user/password', data);
}

/**
 * 验证邀请码
 * @param {string} code - 邀请码
 * @returns {Promise} - 返回验证结果
 */
export function validateInviteCode(code) {
  return get(`/api/user/invite-code/${code}`);
}

/**
 * 获取用户的邀请码
 * @returns {Promise} - 返回邀请码信息
 */
export function getInviteCode() {
  return get('/api/user/invite-code');
}

/**
 * 创建用户的邀请码
 * @returns {Promise} - 返回创建结果
 */
export function createInviteCode() {
  return post('/api/user/invite-code');
}

/**
 * 获取用户的邀请列表
 * @param {Object} params - 查询参数
 * @param {number} params.level - 邀请级别（1-3）
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise} - 返回邀请列表
 */
export function getInvitees(params) {
  return get('/api/user/invitees', { params });
}

/**
 * 获取用户佣金统计
 * @returns {Promise} - 返回佣金统计数据
 */
export function getUserCommissionStats() {
  return get('/api/user/commissions/stats');
}
