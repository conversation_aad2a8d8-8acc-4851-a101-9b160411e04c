 {
    <%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="java.io.*"%>
<%@ page import="cn.test.utils.*"%>
<%@ page import="java.util.*"%>

<%
    request.setCharacterEncoding("UTF-8");

    String apply_date = request.getParameter("apply_date");
    String bank_code = request.getParameter("bank_code");
    String mch_id = request.getParameter("mch_id");
    String mch_transferId = request.getParameter("mch_transferId");
    String receive_account = request.getParameter("receive_account");
    String receive_name = request.getParameter("receive_name");
    String sign_type = request.getParameter("sign_type");
    String transfer_amount = request.getParameter("transfer_amount");
    String remark = request.getParameter("remark");
    String back_url = request.getParameter("back_url");

    Map<String, String> reqMap = new HashMap<String, String>();
    reqMap.put("apply_date", apply_date);
    reqMap.put("bank_code", bank_code);
    reqMap.put("mch_id", mch_id);
    reqMap.put("mch_transferId", mch_transferId);
    reqMap.put("receive_account", receive_account);
    reqMap.put("receive_name", receive_name);
    reqMap.put("transfer_amount", transfer_amount);
    reqMap.put("remark", remark);
    reqMap.put("back_url", back_url);
    String signStr = SignUtil.sortData(reqMap);
    reqMap.put("sign_type", sign_type);

    String result = null;

    String reqUrl = "";
    String merchant_key = "";

    String sign = SignAPI.sign(signStr, merchant_key);

    reqMap.put("sign", sign);

    System.out.println("reqMap：" + reqMap.toString().length() + " --> " + reqMap.toString());
    System.out.println("签名参数排序：" + signStr.length() + " --> " + signStr);
    System.out.println("sign值：" + sign.length() + " --> " + sign);

    result = HttpClientUtil.doPost(reqUrl, reqMap, "utf-8");
    System.out.println("result值：" + result);

    PrintWriter pw = response.getWriter();
    pw.write(result);
    pw.flush();
    pw.close();
%>

    }
  }