const logger = require('../utils/logger');
const tokenService = require('../services/tokenService');

/**
 * Token刷新中间件
 * 如果token即将过期（小于刷新阈值），则自动刷新token
 */
const tokenRefreshMiddleware = async (req, res, next) => {
  // 如果没有认证头，直接跳过
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return next();
  }

  try {
    // 获取token
    const token = authHeader.split(' ')[1];

    // 如果用户已认证（req.admin存在），尝试刷新token
    if (req.admin && req.token) {
      // 尝试刷新token
      const refreshResult = await tokenService.refreshAdminToken(token);

      // 如果刷新成功，在响应头中设置新token
      if (refreshResult.success) {
        res.setHeader('X-New-Token', refreshResult.token);
        logger.info(`Token refreshed for admin ID: ${req.admin.id}`);
      }
    }
  } catch (error) {
    // 如果出错，记录错误但不中断请求
    logger.error('Token refresh error:', error);
  }

  next();
};

module.exports = tokenRefreshMiddleware;
