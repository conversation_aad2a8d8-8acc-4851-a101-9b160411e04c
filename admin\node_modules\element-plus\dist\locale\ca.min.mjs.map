{"version": 3, "file": "ca.min.mjs", "sources": ["../../../../packages/locale/lang/ca.ts"], "sourcesContent": ["export default {\n  name: 'ca',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Confirmar',\n      clear: 'Netejar',\n    },\n    datepicker: {\n      now: 'Ara',\n      today: 'Avui',\n      cancel: 'Cancel·lar',\n      clear: 'Netejar',\n      confirm: 'Confirmar',\n      selectDate: 'Seleccionar data',\n      selectTime: 'Seleccionar hora',\n      startDate: 'Data Inici',\n      startTime: 'Hora Inici',\n      endDate: 'Data Final',\n      endTime: 'Hora Final',\n      prevYear: 'Any anterior',\n      nextYear: 'Pròxim Any',\n      prevMonth: 'Mes anterior',\n      nextMonth: 'Pròxim Mes',\n      year: '',\n      month1: 'Gener',\n      month2: 'Febrer',\n      month3: 'Març',\n      month4: 'Abril',\n      month5: 'Maig',\n      month6: 'Juny',\n      month7: 'Juliol',\n      month8: 'Agost',\n      month9: 'Setembre',\n      month10: 'Octubre',\n      month11: 'Novembre',\n      month12: 'Desembre',\n      // week: 'setmana',\n      weeks: {\n        sun: 'Dg',\n        mon: 'Dl',\n        tue: 'Dt',\n        wed: 'Dc',\n        thu: 'Dj',\n        fri: 'Dv',\n        sat: 'Ds',\n      },\n      months: {\n        jan: 'Gen',\n        feb: 'Febr',\n        mar: 'Març',\n        apr: 'Abr',\n        may: 'Maig',\n        jun: 'Juny',\n        jul: 'Jul',\n        aug: 'Ag',\n        sep: 'Set',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Carregant',\n      noMatch: 'No hi ha dades que coincideixin',\n      noData: 'Sense Dades',\n      placeholder: 'Seleccionar',\n    },\n    mention: {\n      loading: 'Carregant',\n    },\n    cascader: {\n      noMatch: 'No hi ha dades que coincideixin',\n      loading: 'Carregant',\n      placeholder: 'Seleccionar',\n      noData: 'Sense Dades',\n    },\n    pagination: {\n      goto: 'Anar a',\n      pagesize: '/pàgina',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'Acceptar',\n      cancel: 'Cancel·lar',\n      error: 'Entrada invàlida',\n    },\n    upload: {\n      deleteTip: 'premi eliminar per descartar',\n      delete: 'Eliminar',\n      preview: 'Vista Prèvia',\n      continue: 'Continuar',\n    },\n    table: {\n      emptyText: 'Sense Dades',\n      confirmFilter: 'Confirmar',\n      resetFilter: 'Netejar',\n      clearFilter: 'Tot',\n      sumText: 'Tot',\n    },\n    tree: {\n      emptyText: 'Sense Dades',\n    },\n    transfer: {\n      noMatch: 'No hi ha dades que coincideixin',\n      noData: 'Sense Dades',\n      titles: ['Llista 1', 'Llista 2'],\n      filterPlaceholder: 'Introdueix la paraula clau',\n      noCheckedFormat: '{total} ítems',\n      hasCheckedFormat: '{checked}/{total} seleccionats',\n    },\n    image: {\n      error: 'HA FALLAT',\n    },\n    pageHeader: {\n      title: 'Tornar',\n    },\n    popconfirm: {\n      confirmButtonText: 'Sí',\n      cancelButtonText: 'No',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,kBAAkB,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,iCAAiC,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}