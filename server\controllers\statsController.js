/**
 * 统计数据控制器
 */
const { User, Transaction, UserRelation, Project, Order } = require('../models');
const { Op, Sequelize } = require('sequelize');
const dateUtils = require('../utils/dateUtils');
const moment = require('moment');

/**
 * 获取首页统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getHomeStats = async (req, res) => {
  try {
    const user = req.user;

    // 获取今天的开始和结束时间
    const todayStart = dateUtils.getTodayStart();
    const todayEnd = dateUtils.getTodayEnd();

    // 查询今日收益（包含投资收益和佣金收入）
    const todayEarnings = await Transaction.sum('amount', {
      where: {
        user_id: user.id,
        type: {
          [Op.in]: ['profit', 'commission'] // 同时包含投资收益和佣金收入
        },
        status: 'success',
        created_at: {
          [Op.between]: [todayStart, todayEnd]
        }
      }
    }) || 0;

    // 查询今日邀请人数
    const todayInvites = await UserRelation.count({
      where: {
        parent_id: user.id,
        level: 1,
        created_at: {
          [Op.between]: [todayStart, todayEnd]
        }
      }
    });

    // 查询总收益（包含投资收益和佣金收入）
    const totalEarnings = await Transaction.sum('amount', {
      where: {
        user_id: user.id,
        type: {
          [Op.in]: ['profit', 'commission'] // 同时包含投资收益和佣金收入
        },
        status: 'success'
      }
    }) || 0;

    // 查询总邀请人数
    const totalInvites = await UserRelation.count({
      where: {
        parent_id: user.id,
        level: 1
      }
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        todayEarnings: parseFloat(todayEarnings).toFixed(2),
        todayInvites,
        totalEarnings: parseFloat(totalEarnings).toFixed(2),
        totalInvites
      }
    });
  } catch (error) {
    console.error('获取首页统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 获取账户统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAccountStats = async (req, res) => {
  try {
    const user = req.user;
    const { period = 'today' } = req.query;

    // 获取时间范围
    const { start, end } = dateUtils.getPeriodRange(period);

    // 查询收益（包含投资收益和佣金收入）
    const income = await Transaction.sum('amount', {
      where: {
        user_id: user.id,
        type: {
          [Op.in]: ['profit', 'commission'] // 同时包含投资收益和佣金收入
        },
        status: 'success',
        created_at: {
          [Op.between]: [start, end]
        }
      }
    }) || 0;

    // 查询团队人数
    const members = await UserRelation.count({
      where: {
        parent_id: user.id,
        created_at: {
          [Op.between]: [start, end]
        }
      }
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        income: parseFloat(income).toFixed(2),
        members
      }
    });
  } catch (error) {
    console.error('获取账户统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 获取管理端首页统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAdminDashboardStats = async (req, res) => {
  try {
    // 引入统计服务
    const statisticsService = require('../services/statisticsService');
    const dateUtils = require('../utils/dateUtils');
    const moment = require('moment');
    const { User, Transaction, Deposit } = require('../models');
    const { Op } = require('sequelize');

    // 获取查询参数
    const {
      load_charts = 'true',      // 是否加载图表数据
      load_lists = 'true',       // 是否加载列表数据
      chart_days = 30            // 图表数据天数
    } = req.query;

    // 获取当前服务器时间
    const currentTime = new Date();
    const formattedCurrentTime = dateUtils.formatDateTime(currentTime);

    // 基础统计数据（总是需要的）
    const [todayStats, yesterdayStats, totalStats] = await Promise.all([
      statisticsService.getTodayStatistics(),
      statisticsService.getYesterdayStatistics(),
      statisticsService.getTotalStatistics()
    ]);

    // 初始化可选数据
    let recentStats = [];
    let recentDeposits = [];
    let recentUsers = [];

    // 按需加载图表数据
    if (load_charts === 'true') {
      recentStats = await statisticsService.getRecentDaysStatistics(parseInt(chart_days));
    }

    // 按需加载列表数据
    if (load_lists === 'true') {
      [recentDeposits, recentUsers] = await Promise.all([
        Transaction.findAll({
          where: {
            type: 'deposit',
            status: 'completed'
          },
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'username', 'user_id']
            }
          ],
          order: [['created_at', 'DESC']],
          limit: 5
        }),
        User.findAll({
          attributes: ['id', 'username', 'user_id', 'created_at'],
          order: [['created_at', 'DESC']],
          limit: 5
        })
      ]);
    }

    // 格式化充值记录
    const formattedDeposits = recentDeposits.map(deposit => ({
      id: deposit.id,
      user_id: deposit.user ? deposit.user.user_id : '',
      username: deposit.user ? deposit.user.username : '',
      amount: parseFloat(deposit.amount).toFixed(2),
      created_at: dateUtils.formatDateTime(deposit.created_at)
    }));

    // 格式化用户记录
    const formattedUsers = recentUsers.map(user => ({
      id: user.id,
      user_id: user.user_id,
      username: user.username,
      created_at: dateUtils.formatDateTime(user.created_at)
    }));

    // 初始化图表数据
    let chartDates = [];
    let chartData = {
      registerCounts: [],
      registerDepositCounts: [],
      totalDepositCounts: []
    };
    let moneyChartData = {
      investmentAmounts: [],
      depositAmounts: [],
      withdrawalAmounts: []
    };

    // 只在需要时处理图表数据
    if (load_charts === 'true' && recentStats.length > 0) {
      recentStats.forEach(stat => {
        const formattedDate = moment(stat.date).format('YYYY-MM-DD');
        chartDates.push(formattedDate);

        // 注册人数
        chartData.registerCounts.push(stat.new_user_count);

        // 注册并充值人数
        chartData.registerDepositCounts.push(stat.register_deposit_count);

        // 充值人数
        chartData.totalDepositCounts.push(stat.deposit_user_count);

        // 投资金额
        moneyChartData.investmentAmounts.push(parseFloat(stat.investment_amount).toFixed(2));

        // 充值金额
        moneyChartData.depositAmounts.push(parseFloat(stat.deposit_amount).toFixed(2));

        // 取款金额
        moneyChartData.withdrawalAmounts.push(parseFloat(stat.withdrawal_amount).toFixed(2));
      });
    }

    // 计算平台利润
    const todayPlatformProfit = parseFloat(todayStats.deposit_amount) - parseFloat(todayStats.withdrawal_amount);
    const yesterdayPlatformProfit = parseFloat(yesterdayStats.deposit_amount) - parseFloat(yesterdayStats.withdrawal_amount);
    const totalPlatformProfit = parseFloat(totalStats.total_deposit_amount) - parseFloat(totalStats.total_withdrawal_amount);

    // 构建响应数据
    const responseData = {
      currentTime: formattedCurrentTime,
      users: {
        today: todayStats.new_user_count,
        yesterday: yesterdayStats.new_user_count,
        total: totalStats.total_user_count
      },
      deposits: {
        today: parseFloat(todayStats.deposit_amount).toFixed(2),
        yesterday: parseFloat(yesterdayStats.deposit_amount).toFixed(2),
        total: parseFloat(totalStats.total_deposit_amount).toFixed(2)
      },
      depositCounts: {
        today: todayStats.deposit_count,
        yesterday: yesterdayStats.deposit_count,
        total: totalStats.total_deposit_count
      },
      depositUserCounts: {
        today: todayStats.deposit_user_count,
        yesterday: yesterdayStats.deposit_user_count,
        total: totalStats.total_deposit_user_count
      },
      withdrawals: {
        today: parseFloat(todayStats.withdrawal_amount).toFixed(2),
        yesterday: parseFloat(yesterdayStats.withdrawal_amount).toFixed(2),
        total: parseFloat(totalStats.total_withdrawal_amount).toFixed(2)
      },
      withdrawalCounts: {
        today: todayStats.withdrawal_count,
        yesterday: yesterdayStats.withdrawal_count,
        total: totalStats.total_withdrawal_count
      },
      investments: {
        today: parseFloat(todayStats.investment_amount).toFixed(2),
        yesterday: parseFloat(yesterdayStats.investment_amount).toFixed(2),
        total: parseFloat(totalStats.total_investment_amount).toFixed(2)
      },
      platformProfits: {
        today: parseFloat(todayPlatformProfit).toFixed(2),
        yesterday: parseFloat(yesterdayPlatformProfit).toFixed(2),
        total: parseFloat(totalPlatformProfit).toFixed(2)
      }
    };

    // 按需添加列表数据
    if (load_lists === 'true') {
      responseData.recentDeposits = formattedDeposits;
      responseData.recentUsers = formattedUsers;
    }

    // 按需添加图表数据
    if (load_charts === 'true') {
      responseData.chartData = {
        dates: chartDates,
        registerCounts: chartData.registerCounts,
        registerDepositCounts: chartData.registerDepositCounts,
        totalDepositCounts: chartData.totalDepositCounts
      };
      responseData.moneyChartData = {
        dates: chartDates,
        investmentAmounts: moneyChartData.investmentAmounts,
        depositAmounts: moneyChartData.depositAmounts,
        withdrawalAmounts: moneyChartData.withdrawalAmounts
      };
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: responseData
    });
  } catch (error) {
    console.error('获取管理端首页统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 获取最近充值记录（分页）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRecentDeposits = async (req, res) => {
  try {
    const { User, Transaction } = require('../models');
    const dateUtils = require('../utils/dateUtils');

    // 获取分页参数
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 查询充值记录
    const { count, rows: deposits } = await Transaction.findAndCountAll({
      where: {
        type: 'deposit',
        status: 'completed'
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'user_id']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    // 格式化数据
    const formattedDeposits = deposits.map(deposit => ({
      id: deposit.id,
      user_id: deposit.user ? deposit.user.user_id : '',
      username: deposit.user ? deposit.user.username : '',
      amount: parseFloat(deposit.amount).toFixed(2),
      created_at: dateUtils.formatDateTime(deposit.created_at)
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        deposits: formattedDeposits,
        pagination: {
          current_page: page,
          per_page: limit,
          total: count,
          total_pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取最近充值记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 获取最近注册用户（分页）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRecentUsers = async (req, res) => {
  try {
    const { User } = require('../models');
    const dateUtils = require('../utils/dateUtils');

    // 获取分页参数
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 查询用户记录
    const { count, rows: users } = await User.findAndCountAll({
      attributes: ['id', 'username', 'user_id', 'created_at'],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    // 格式化数据
    const formattedUsers = users.map(user => ({
      id: user.id,
      user_id: user.user_id,
      username: user.username,
      created_at: dateUtils.formatDateTime(user.created_at)
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        users: formattedUsers,
        pagination: {
          current_page: page,
          per_page: limit,
          total: count,
          total_pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取最近注册用户失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
