/* empty css             *//* empty css                  *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                 */import{d as b,r as x,o as g,c as y,e as s,b as V,w as i,al as E,V as p,a7 as B,i as U,a8 as T,a9 as C,at as N,aq as P,ap as S,n as v,m as $,p as u,g as n,a0 as I,_ as j}from"./index-LncY9lAB.js";const F={class:"params-form"},M={class:"form-actions"},O=b({__name:"UserPanel",setup(z){const m=x([{title:"超级登录密码",value:"",key:"[site.super_login_password]",type:"text"},{title:"邀请注册奖励",value:"0",key:"[site.invite_give]",type:"text"},{title:"邀请注册奖励上限",value:"0",key:"[site.invite_limit]",type:"text"},{title:"邀请注册奖励说明",value:"",key:"[site.invite_text]",type:"textarea"},{title:"邀请码标识",value:"promo",key:"[site.promo_code_url]",type:"text"},{title:"邀请码规则",value:"1",key:"[site.promo_code_rule]",type:"select",options:[{label:"数字和英文字母",value:"1"},{label:"纯数字",value:"2"},{label:"纯英文字母",value:"3"}]},{title:"邀请码长度",value:"8",key:"[site.promo_code_length]",type:"text"},{title:"注册奖励类型",value:"income",key:"[site.reg_bonus_type]",type:"select",options:[{label:"收入账户",value:"income"},{label:"充值账户",value:"deposit"}]},{title:"注册奖励金额",value:"20",key:"[site.reg_bonus_amount]",type:"text"}]),w=()=>{I.confirm("确定要重置所有设置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{d(),u.success("表单已重置")}).catch(()=>{})},f=async()=>{try{const a=m.value.map(l=>({param_key:l.key,param_value:l.value,param_type:l.type,group_name:"user"})),t=await fetch("/api/admin/system-params",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"},body:JSON.stringify({params:a})});if(!t.ok)throw new Error("保存失败");const o=await t.json();o.code===200?u.success("设置已保存"):u.error(o.message||"保存失败")}catch(a){console.error("保存会员配置参数失败:",a),u.error("保存失败，请稍后重试")}},d=async()=>{try{const a=await fetch("/api/admin/system-params/group/user",{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(!a.ok)throw new Error("获取失败");const t=await a.json();t.code===200&&t.data&&t.data.items&&t.data.items.forEach(o=>{const l=m.value.find(c=>c.key===o.param_key);l&&(l.value=o.param_value)})}catch(a){console.error("获取会员配置参数失败:",a),u.error("获取参数失败，使用默认值")}};return g(()=>{d()}),(a,t)=>{const o=E,l=U,c=N,k=P,h=S,_=$;return n(),y("div",F,[s(h,{data:m.value,class:"param-table"},{default:i(()=>[s(o,{prop:"title",label:"变量标题",width:"150"}),s(o,{prop:"value",label:"变量值"},{default:i(e=>[e.row.type==="text"?(n(),p(l,{key:0,modelValue:e.row.value,"onUpdate:modelValue":r=>e.row.value=r,placeholder:`请输入${e.row.title}`},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.row.type==="textarea"?(n(),p(l,{key:1,modelValue:e.row.value,"onUpdate:modelValue":r=>e.row.value=r,type:"textarea",rows:3,placeholder:`请输入${e.row.title}`},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.row.type==="select"?(n(),p(k,{key:2,modelValue:e.row.value,"onUpdate:modelValue":r=>e.row.value=r,placeholder:`请选择${e.row.title}`,style:{width:"100%"}},{default:i(()=>[(n(!0),y(T,null,C(e.row.options,r=>(n(),p(c,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):B("",!0)]),_:1}),s(o,{prop:"key",label:"变量名",width:"200"})]),_:1},8,["data"]),V("div",M,[s(_,{onClick:w},{default:i(()=>t[0]||(t[0]=[v("重置")])),_:1}),s(_,{type:"primary",onClick:f},{default:i(()=>t[1]||(t[1]=[v("保存")])),_:1})])])}}}),R=j(O,[["__scopeId","data-v-26ef39e8"]]);export{R as default};
