{"version": 3, "file": "cell.mjs", "sources": ["../../../../../../packages/components/table-v2/src/cell.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { column } from './common'\n\nimport type { ExtractPropTypes, StyleValue } from 'vue'\n\nexport const tableV2CellProps = buildProps({\n  class: String,\n  cellData: {\n    type: definePropType<any>([String, Boolean, Number, Object]),\n  },\n  column,\n  columnIndex: Number,\n  style: {\n    type: definePropType<StyleValue>([String, Array, Object]),\n  },\n  rowData: {\n    type: definePropType<any>(Object),\n  },\n  rowIndex: Number,\n} as const)\n\nexport type TableV2CellProps = ExtractPropTypes<typeof tableV2CellProps>\n"], "names": [], "mappings": ";;;AAEY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,MAAM;AACR,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,QAAQ,EAAE,MAAM;AAClB,CAAC;;;;"}