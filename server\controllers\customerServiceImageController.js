const { CustomerServiceImage, Attachment } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 获取客服图片列表
exports.getCustomerServiceImages = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      sort = 'id',
      order = 'desc'
    } = req.query;

    // 构建查询条件
    const where = {};

    if (status !== undefined && status !== '') {
      where.status = status === 'true' || status === '1' || status === true ? true : false;
    }

    // 构建排序条件
    const orderBy = [];
    if (sort) {
      orderBy.push([sort, order.toUpperCase()]);
    }

    // 分页参数
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const pageSize = parseInt(limit);

    // 查询数据
    const result = await sequelize.query(`
      SELECT
        csi.id,
        csi.attachment_id,
        csi.file_name,
        csi.file_path,
        csi.file_size,
        csi.status,
        csi.created_at,
        csi.updated_at,
        a.id as attachment_id,
        a.filename as attachment_filename,
        a.file_path as attachment_file_path,
        a.file_size as attachment_file_size,
        a.file_type as attachment_file_type,
        a.mime_type as attachment_mime_type
      FROM
        customer_service_images csi
      LEFT JOIN
        attachments a ON csi.attachment_id = a.id
      ORDER BY
        csi.${sort} ${order}
      LIMIT ${pageSize} OFFSET ${offset}
    `, { type: sequelize.QueryTypes.SELECT });

    // 获取总数
    const countResult = await sequelize.query(`
      SELECT COUNT(*) as total FROM customer_service_images
    `, { type: sequelize.QueryTypes.SELECT });

    const count = countResult[0].total;
    const rows = result.map(item => {
      return {
        id: item.id,
        attachment_id: item.attachment_id,
        file_name: item.file_name,
        file_path: item.file_path,
        file_size: item.file_size,
        status: item.status,
        created_at: item.created_at,
        updated_at: item.updated_at,
        attachment: {
          id: item.attachment_id,
          filename: item.attachment_filename,
          file_path: item.attachment_file_path,
          file_size: item.attachment_file_size,
          file_type: item.attachment_file_type,
          mime_type: item.attachment_mime_type
        }
      };
    });

    // 格式化响应数据
    const items = rows;

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: pageSize,
        items
      }
    });
  } catch (error) {
    console.error('获取客服图片列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取客服图片详情
exports.getCustomerServiceImage = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await sequelize.query(`
      SELECT
        csi.id,
        csi.attachment_id,
        csi.file_name,
        csi.file_path,
        csi.file_size,
        csi.status,
        csi.created_at,
        csi.updated_at,
        a.id as attachment_id,
        a.filename as attachment_filename,
        a.file_path as attachment_file_path,
        a.file_size as attachment_file_size,
        a.file_type as attachment_file_type,
        a.mime_type as attachment_mime_type
      FROM
        customer_service_images csi
      LEFT JOIN
        attachments a ON csi.attachment_id = a.id
      WHERE
        csi.id = :id
    `, {
      replacements: { id },
      type: sequelize.QueryTypes.SELECT
    });

    if (!result || result.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '客服图片不存在',
        data: null
      });
    }

    const item = result[0];
    const customerServiceImage = {
      id: item.id,
      attachment_id: item.attachment_id,
      file_name: item.file_name,
      file_path: item.file_path,
      file_size: item.file_size,
      status: item.status,
      created_at: item.created_at,
      updated_at: item.updated_at,
      attachment: {
        id: item.attachment_id,
        filename: item.attachment_filename,
        file_path: item.attachment_file_path,
        file_size: item.attachment_file_size,
        file_type: item.attachment_file_type,
        mime_type: item.attachment_mime_type
      }
    };



    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: customerServiceImage
    });
  } catch (error) {
    console.error('获取客服图片详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 创建客服图片
exports.createCustomerServiceImage = async (req, res) => {
  try {
    const { attachment_id, file_name, file_path, file_size, status } = req.body;

    // 验证必填字段
    if (!attachment_id) {
      return res.status(400).json({
        code: 400,
        message: '附件ID为必填项',
        data: null
      });
    }

    // 获取附件信息
    const attachment = await Attachment.findByPk(attachment_id);
    if (!attachment) {
      return res.status(404).json({
        code: 404,
        message: '附件不存在',
        data: null
      });
    }

    // 创建客服图片
    const customerServiceImage = await sequelize.query(`
      INSERT INTO customer_service_images
      (attachment_id, file_name, file_path, file_size, status, created_at, updated_at)
      VALUES
      (:attachment_id, :file_name, :file_path, :file_size, :status, NOW(), NOW())
    `, {
      replacements: {
        attachment_id,
        file_name: file_name || attachment.filename,
        file_path: file_path || attachment.file_path,
        file_size: file_size || attachment.file_size,
        status: status !== undefined ? status : true
      },
      type: sequelize.QueryTypes.INSERT
    });

    const insertId = customerServiceImage[0];

    // 获取创建的客服图片
    const result = await sequelize.query(`
      SELECT
        csi.id,
        csi.attachment_id,
        csi.file_name,
        csi.file_path,
        csi.file_size,
        csi.status,
        csi.created_at,
        csi.updated_at,
        a.id as attachment_id,
        a.filename as attachment_filename,
        a.file_path as attachment_file_path,
        a.file_size as attachment_file_size,
        a.file_type as attachment_file_type,
        a.mime_type as attachment_mime_type
      FROM
        customer_service_images csi
      LEFT JOIN
        attachments a ON csi.attachment_id = a.id
      WHERE
        csi.id = :id
    `, {
      replacements: { id: insertId },
      type: sequelize.QueryTypes.SELECT
    });

    if (result.length === 0) {
      return res.status(500).json({
        code: 500,
        message: '创建客服图片失败',
        data: null
      });
    }

    const item = result[0];
    const customerServiceImageData = {
      id: item.id,
      attachment_id: item.attachment_id,
      file_name: item.file_name,
      file_path: item.file_path,
      file_size: item.file_size,
      status: item.status,
      created_at: item.created_at,
      updated_at: item.updated_at,
      attachment: {
        id: item.attachment_id,
        filename: item.attachment_filename,
        file_path: item.attachment_file_path,
        file_size: item.attachment_file_size,
        file_type: item.attachment_file_type,
        mime_type: item.attachment_mime_type
      }
    };

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: customerServiceImageData
    });
  } catch (error) {
    console.error('创建客服图片错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新客服图片
exports.updateCustomerServiceImage = async (req, res) => {
  try {
    const { id } = req.params;
    const { attachment_id, file_name, file_path, file_size, status } = req.body;

    const customerServiceImage = await CustomerServiceImage.findByPk(id);

    if (!customerServiceImage) {
      return res.status(404).json({
        code: 404,
        message: '客服图片不存在',
        data: null
      });
    }

    // 如果更新了附件ID，检查附件是否存在
    if (attachment_id && attachment_id !== customerServiceImage.attachment_id) {
      const attachment = await Attachment.findByPk(attachment_id);
      if (!attachment) {
        return res.status(404).json({
          code: 404,
          message: '附件不存在',
          data: null
        });
      }
    }

    // 确保状态值是布尔值或0/1
    let statusValue = customerServiceImage.status;
    if (status !== undefined) {
      // 将状态转换为布尔值或0/1
      if (status === true || status === 1 || status === '1') {
        statusValue = true;
      } else if (status === false || status === 0 || status === '0') {
        statusValue = false;
      }
    }

    // 更新客服图片
    await customerServiceImage.update({
      attachment_id: attachment_id || customerServiceImage.attachment_id,
      file_name: file_name || customerServiceImage.file_name,
      file_path: file_path || customerServiceImage.file_path,
      file_size: file_size || customerServiceImage.file_size,
      status: statusValue
    });

    // 获取更新后的数据
    const updatedCustomerServiceImage = await CustomerServiceImage.findByPk(id, {
      include: [
        {
          model: Attachment,
          as: 'attachment',
          attributes: ['id', 'filename', 'file_path', 'file_size', 'file_type', 'mime_type']
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: updatedCustomerServiceImage
    });
  } catch (error) {
    console.error('更新客服图片错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除客服图片
exports.deleteCustomerServiceImage = async (req, res) => {
  try {
    console.log('=== 删除客服图片 - 开始 ===');
    console.log('删除客服图片 - 请求方法:', req.method);
    console.log('删除客服图片 - 请求URL:', req.originalUrl);
    console.log('删除客服图片 - 请求参数:', req.params);

    const { id } = req.params;
    console.log('删除客服图片 - ID:', id, '类型:', typeof id);

    // 验证ID是否有效
    if (!id || isNaN(parseInt(id))) {
      console.log('删除客服图片 - 无效的ID参数:', id);
      return res.status(400).json({
        code: 400,
        message: '无效的ID参数',
        data: null
      });
    }

    const numericId = parseInt(id);
    console.log('删除客服图片 - 转换后的ID:', numericId, '类型:', typeof numericId);

    // 使用 Sequelize 模型直接删除
    const customerServiceImage = await CustomerServiceImage.findByPk(numericId);
    console.log('删除客服图片 - 查询结果:', customerServiceImage ? '找到记录' : '未找到记录');

    if (!customerServiceImage) {
      console.log('删除客服图片 - 客服图片不存在:', numericId);
      return res.status(404).json({
        code: 404,
        message: '客服图片不存在',
        data: null
      });
    }

    await customerServiceImage.destroy();
    console.log('删除客服图片 - 删除成功:', numericId);
    console.log('=== 删除客服图片 - 结束 ===');

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除客服图片错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 批量删除客服图片
exports.batchDeleteCustomerServiceImages = async (req, res) => {
  try {
    console.log('=== 批量删除客服图片 - 开始 ===');
    console.log('批量删除客服图片 - 请求方法:', req.method);
    console.log('批量删除客服图片 - 请求URL:', req.originalUrl);
    console.log('批量删除客服图片 - 请求头:', JSON.stringify(req.headers, null, 2));
    console.log('批量删除客服图片 - 请求体类型:', typeof req.body);
    console.log('批量删除客服图片 - 请求体:', JSON.stringify(req.body, null, 2));
    console.log('批量删除客服图片 - 原始请求体:', req.body);

    // 检查请求体是否为空
    if (!req.body || Object.keys(req.body).length === 0) {
      console.log('批量删除客服图片 - 请求体为空');
      return res.status(400).json({
        code: 400,
        message: '请求体为空',
        data: null
      });
    }

    const { ids } = req.body;
    console.log('批量删除客服图片 - IDs:', ids);
    console.log('批量删除客服图片 - IDs类型:', typeof ids);
    console.log('批量删除客服图片 - IDs是否为数组:', Array.isArray(ids));

    if (ids) {
      console.log('批量删除客服图片 - IDs长度:', ids.length);
      console.log('批量删除客服图片 - IDs内容:', JSON.stringify(ids));

      // 检查每个ID的类型
      if (Array.isArray(ids)) {
        ids.forEach((id, index) => {
          console.log(`批量删除客服图片 - ID[${index}]:`, id, typeof id);
        });
      }
    }

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      console.log('批量删除客服图片 - 无效的ID数组:', ids);
      return res.status(400).json({
        code: 400,
        message: '请提供有效的ID数组',
        data: null
      });
    }

    // 验证所有ID是否有效
    const validIds = ids.filter(id => !isNaN(parseInt(id))).map(id => parseInt(id));
    console.log('批量删除客服图片 - 有效的IDs:', validIds);
    console.log('批量删除客服图片 - 有效的IDs长度:', validIds.length);

    // 检查哪些ID无效
    const invalidIds = ids.filter(id => isNaN(parseInt(id)));
    console.log('批量删除客服图片 - 无效的IDs:', invalidIds);
    console.log('批量删除客服图片 - 无效的IDs长度:', invalidIds.length);

    if (validIds.length === 0) {
      console.log('批量删除客服图片 - 没有有效的ID');
      return res.status(400).json({
        code: 400,
        message: '请提供有效的ID数组',
        data: null
      });
    }

    // 使用 Sequelize 模型直接删除
    const result = await CustomerServiceImage.destroy({
      where: {
        id: {
          [Op.in]: validIds
        }
      }
    });

    console.log('批量删除客服图片 - 删除结果:', result);
    console.log('=== 批量删除客服图片 - 结束 ===');

    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: {
        deleted_count: result || 0
      }
    });
  } catch (error) {
    console.error('批量删除客服图片错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};



// 获取移动端客服图片列表
exports.getMobileCustomerServiceImages = async (req, res) => {
  try {
    // 只获取状态为启用的客服图片
    const result = await sequelize.query(`
      SELECT
        csi.id,
        csi.attachment_id,
        csi.file_name,
        csi.file_path,
        csi.file_size,
        csi.status,
        csi.created_at,
        csi.updated_at,
        a.id as attachment_id,
        a.filename as attachment_filename,
        a.file_path as attachment_file_path,
        a.file_size as attachment_file_size,
        a.file_type as attachment_file_type,
        a.mime_type as attachment_mime_type
      FROM
        customer_service_images csi
      LEFT JOIN
        attachments a ON csi.attachment_id = a.id
      WHERE
        csi.status = true
      ORDER BY
        csi.id DESC
    `, { type: sequelize.QueryTypes.SELECT });

    const customerServiceImages = result.map(item => {
      return {
        id: item.id,
        file_name: item.file_name,
        file_path: item.file_path,
        file_size: item.file_size,
        attachment: {
          id: item.attachment_id,
          filename: item.attachment_filename,
          file_path: item.attachment_file_path,
          file_size: item.attachment_file_size,
          file_type: item.attachment_file_type,
          mime_type: item.attachment_mime_type
        }
      };
    });

    // 格式化响应数据
    const items = customerServiceImages;

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: items
    });
  } catch (error) {
    console.error('获取移动端客服图片列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
