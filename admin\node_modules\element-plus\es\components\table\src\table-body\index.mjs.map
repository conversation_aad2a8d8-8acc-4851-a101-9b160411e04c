{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../packages/components/table/src/table-body/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  defineComponent,\n  getCurrentInstance,\n  h,\n  inject,\n  onUnmounted,\n  watch,\n} from 'vue'\nimport { addClass, isClient, rAF, removeClass } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport useLayoutObserver from '../layout-observer'\nimport { removePopper } from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport useRender from './render-helper'\nimport defaultProps from './defaults'\n\nimport type { VNode } from 'vue'\n\nexport default defineComponent({\n  name: 'ElTableBody',\n  props: defaultProps,\n  setup(props) {\n    const instance = getCurrentInstance()\n    const parent = inject(TABLE_INJECTION_KEY)\n    const ns = useNamespace('table')\n    const { wrappedRowRender, tooltipContent, tooltipTrigger } =\n      useRender(props)\n    const { onColumnsChange, onScrollableChange } = useLayoutObserver(parent!)\n\n    const hoveredCellList = []\n    watch(props.store.states.hoverRow, (newVal: any, oldVal: any) => {\n      const el = instance?.vnode.el as HTMLElement\n      const rows = Array.from(el?.children || []).filter((e) =>\n        e?.classList.contains(`${ns.e('row')}`)\n      )\n\n      // hover rowSpan > 1 choose the whole row\n      let rowNum = newVal\n      const childNodes = rows[rowNum]?.childNodes\n      if (childNodes?.length) {\n        let control = 0\n        const indexes = Array.from(childNodes).reduce((acc, item, index) => {\n          // drop colsSpan\n          if (childNodes[index]?.colSpan > 1) {\n            control = childNodes[index]?.colSpan\n          }\n          if (item.nodeName !== 'TD' && control === 0) {\n            acc.push(index)\n          }\n          control > 0 && control--\n          return acc\n        }, [])\n\n        indexes.forEach((rowIndex) => {\n          rowNum = newVal\n          while (rowNum > 0) {\n            // find from previous\n            const preChildNodes = rows[rowNum - 1]?.childNodes\n            if (\n              preChildNodes[rowIndex] &&\n              preChildNodes[rowIndex].nodeName === 'TD' &&\n              preChildNodes[rowIndex].rowSpan > 1\n            ) {\n              addClass(preChildNodes[rowIndex], 'hover-cell')\n              hoveredCellList.push(preChildNodes[rowIndex])\n              break\n            }\n            rowNum--\n          }\n        })\n      } else {\n        hoveredCellList.forEach((item) => removeClass(item, 'hover-cell'))\n        hoveredCellList.length = 0\n      }\n      if (!props.store.states.isComplex.value || !isClient) return\n\n      rAF(() => {\n        // just get first level children; fix #9723\n        const oldRow = rows[oldVal]\n        const newRow = rows[newVal]\n        // when there is fixed row, hover on rowSpan > 1 should not clear the class\n        if (oldRow && !oldRow.classList.contains('hover-fixed-row')) {\n          removeClass(oldRow, 'hover-row')\n        }\n        if (newRow) {\n          addClass(newRow, 'hover-row')\n        }\n      })\n    })\n\n    onUnmounted(() => {\n      removePopper?.()\n    })\n\n    return {\n      ns,\n      onColumnsChange,\n      onScrollableChange,\n      wrappedRowRender,\n      tooltipContent,\n      tooltipTrigger,\n    }\n  },\n  render() {\n    const { wrappedRowRender, store } = this\n    const data = store.states.data.value || []\n    // Why do we need tabIndex: -1 ?\n    // If you set the tabindex attribute on an element ,\n    // then its child content cannot be scrolled with the arrow keys,\n    // unless you set tabindex on the content too\n    // See https://github.com/facebook/react/issues/25462#issuecomment-1274775248 or https://developer.mozilla.org/zh-CN/docs/Web/HTML/Global_attributes/tabindex\n    return h('tbody', { tabIndex: -1 }, [\n      data.reduce((acc: VNode[], row) => {\n        return acc.concat(wrappedRowRender(row, acc.length))\n      }, []),\n    ])\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;AAeA,gBAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,KAAK,CAAC,KAAK,EAAE;AACf,IAAI,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AAC1C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC/C,IAAI,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACrC,IAAI,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAClF,IAAI,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC9E,IAAI,MAAM,eAAe,GAAG,EAAE,CAAC;AAC/B,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK;AAC3D,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,EAAE,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;AAC/D,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpJ,MAAM,IAAI,MAAM,GAAG,MAAM,CAAC;AAC1B,MAAM,MAAM,UAAU,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;AAC9E,MAAM,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE;AAC3D,QAAQ,IAAI,OAAO,GAAG,CAAC,CAAC;AACxB,QAAQ,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,KAAK;AAC5E,UAAU,IAAI,GAAG,EAAE,EAAE,CAAC;AACtB,UAAU,IAAI,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE;AAC9E,YAAY,OAAO,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;AAC7E,WAAW;AACX,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,KAAK,CAAC,EAAE;AACvD,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,WAAW;AACX,UAAU,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC;AACnC,UAAU,OAAO,GAAG,CAAC;AACrB,SAAS,EAAE,EAAE,CAAC,CAAC;AACf,QAAQ,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACtC,UAAU,IAAI,GAAG,CAAC;AAClB,UAAU,MAAM,GAAG,MAAM,CAAC;AAC1B,UAAU,OAAO,MAAM,GAAG,CAAC,EAAE;AAC7B,YAAY,MAAM,aAAa,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC;AAC7F,YAAY,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,KAAK,IAAI,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC,EAAE;AAC7H,cAAc,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,YAAY,CAAC,CAAC;AAC9D,cAAc,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC5D,cAAc,MAAM;AACpB,aAAa;AACb,YAAY,MAAM,EAAE,CAAC;AACrB,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO,MAAM;AACb,QAAQ,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;AAC3E,QAAQ,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,QAAQ;AAC1D,QAAQ,OAAO;AACf,MAAM,GAAG,CAAC,MAAM;AAChB,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,QAAQ,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AACrE,UAAU,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC3C,SAAS;AACT,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,QAAQ,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACxC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC;AACP,IAAI,WAAW,CAAC,MAAM;AACtB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,CAAC,EAAE,GAAG,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC;AAClD,KAAK,CAAC,CAAC;AACP,IAAI,OAAO;AACX,MAAM,EAAE;AACR,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,MAAM,gBAAgB;AACtB,MAAM,cAAc;AACpB,MAAM,cAAc;AACpB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,EAAE,gBAAgB,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7C,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC/C,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;AACxC,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK;AAChC,QAAQ,OAAO,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAC7D,OAAO,EAAE,EAAE,CAAC;AACZ,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC,CAAC;;;;"}