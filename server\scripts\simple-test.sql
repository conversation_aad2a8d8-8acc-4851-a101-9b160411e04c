SELECT @@session.time_zone as session_timezone;
SELECT @@system_time_zone as system_timezone;
SELECT NOW() as current_time;
SELECT UTC_TIMESTAMP() as utc_time;
SELECT COUNT(*) as investment_count FROM investments;
SELECT COUNT(*) as profit_count FROM investment_profits;
SELECT id, start_time, last_profit_time FROM investments ORDER BY id DESC LIMIT 3;
SELECT id, profit_time FROM investment_profits ORDER BY id DESC LIMIT 3;
