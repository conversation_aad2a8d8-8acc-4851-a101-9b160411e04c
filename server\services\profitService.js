/**
 * 投资收益服务
 * 处理投资收益计算和发放
 */
const { Investment, InvestmentProfit, Project, User, Transaction } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const balanceService = require('./balanceService');
const commissionService = require('./commissionService');
const dateUtils = require('../utils/dateUtils');
const { generateOrderNumberByType } = require('../utils/orderNumberGenerator');
const moment = require('moment');
const profitLogger = require('../utils/profitLogger');

/**
 * 计算单个投资的收益
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @returns {Object} - 返回收益信息
 */
const calculateProfit = (investment, project) => {
  // 获取投资金额
  const amount = parseFloat(investment.amount);

  // 获取收益率
  const profitRate = parseFloat(investment.profit_rate);

  // 计算收益金额 (投资金额 * 收益率百分比)
  const profitAmount = amount * profitRate / 100;

  return {
    amount: profitAmount.toFixed(2),
    rate: profitRate
  };
};

/**
 * 检查今天是否是收益日
 * @param {Object} project - 项目信息
 * @returns {Boolean} - 是否是收益日
 */
const isWeeklyProfitDay = (project) => {
  // 获取当前时间
  const now = new Date();

  // 获取当前是星期几 (0-6, 0表示星期日)
  const dayOfWeek = now.getDay();

  // 将星期日(0)转换为7，与数据库中的格式保持一致
  const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;

  // 获取项目的每周收益天数
  const weeklyProfitDays = project.weekly_profit_days.split(',').map(day => parseInt(day.trim()));

  // 检查今天是否在收益天数中
  return weeklyProfitDays.includes(adjustedDayOfWeek);
};

/**
 * 检查是否达到收益发放时间
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @returns {Boolean} - 是否达到收益发放时间
 */
const isTimeToDistributeProfit = (investment, project) => {
  // 方案5.1：获取当前时间（UTC）
  const now = new Date();

  // 获取上次收益时间或投资开始时间（数据库中已是UTC时间）
  const lastProfitTime = investment.last_profit_time
    ? new Date(investment.last_profit_time)
    : new Date(investment.start_time);

  // 如果没有上次收益时间，使用投资开始时间
  if (!lastProfitTime || isNaN(lastProfitTime.getTime())) {
    profitLogger.profitDist(investment.id, `无法获取上次收益时间或投资开始时间`, { warning: true });
    return false;
  }

  // 计算下一次理论收益时间（UTC）
  const nextProfitTime = new Date(lastProfitTime.getTime() + project.profit_time * 60 * 60 * 1000);

  // 计算距离上次收益的小时数
  const hoursSinceLastProfit = (now - lastProfitTime) / (1000 * 60 * 60);

  // 方案5.1：添加详细日志（使用UTC时间）
  profitLogger.profitDist(investment.id, `收益发放检查`, {
    projectId: project.id,
    currentTimeUTC: now.toISOString(),
    lastProfitTimeUTC: lastProfitTime.toISOString(),
    nextProfitTimeUTC: nextProfitTime.toISOString(),
    hoursSinceLastProfit: hoursSinceLastProfit.toFixed(2),
    profitCycle: project.profit_time
  });

  // 严格检查当前时间是否已经达到或超过下一次理论收益时间
  const isTimeReached = now >= nextProfitTime;
  profitLogger.profitDist(investment.id, `是否达到收益发放条件: ${isTimeReached}`);

  return isTimeReached;
};

/**
 * 检查是否达到最大收益次数
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @returns {Boolean} - 是否达到最大收益次数
 */
const hasReachedMaxProfitTimes = (investment, project) => {
  // 如果项目没有设置最大收益次数（值为0），则表示不限制
  if (project.max_profit_times === 0) {
    return false;
  }

  // 检查当前收益次数是否达到最大收益次数
  return investment.profit_count >= project.max_profit_times;
};

/**
 * 处理单个投资的收益发放
 * @param {Object} investment - 投资记录
 * @param {Date} [specificProfitTime] - 指定的收益时间，用于补发时设置准确的收益时间
 * @returns {Promise<Object>} - 返回处理结果
 */
exports.processInvestmentProfit = async (investment, specificProfitTime = null) => {
  try {
    // 创建事务
    const transaction = await sequelize.transaction();

    try {
      // 获取项目信息
      const project = await Project.findByPk(investment.project_id, { transaction });
      if (!project) {
        await transaction.rollback();
        return {
          success: false,
          message: '项目不存在',
          investment_id: investment.id
        };
      }

      // 获取用户信息
      const user = await User.findByPk(investment.user_id, { transaction });
      if (!user) {
        await transaction.rollback();
        return {
          success: false,
          message: '用户不存在',
          investment_id: investment.id
        };
      }

      // 检查投资状态是否为活跃
      if (investment.status !== 'active') {
        await transaction.rollback();
        return {
          success: false,
          message: '投资状态不是活跃状态',
          investment_id: investment.id
        };
      }

      // 检查是否是收益日
      const isProfitDay = await isWeeklyProfitDay(project);
      if (!isProfitDay) {
        await transaction.rollback();
        return {
          success: false,
          message: '今天不是收益日',
          investment_id: investment.id
        };
      }

      // 如果是指定收益时间的补发，跳过时间检查；否则检查是否达到收益发放时间
      if (!specificProfitTime) {
        // 检查是否是首次收益
        const isFirstProfit = !investment.last_profit_time;

        if (isFirstProfit) {
          // 对于首次收益，计算理论收益时间
          const startTime = new Date(investment.start_time || investment.created_at);
          const theoreticalFirstProfitTime = new Date(startTime.getTime() + project.profit_time * 60 * 60 * 1000);
          const now = new Date();

          profitLogger.firstProfit(investment.id, `首次收益时间检查`, {
            startTime: dateUtils.formatDateTime(startTime),
            theoreticalFirstProfitTime: dateUtils.formatDateTime(theoreticalFirstProfitTime),
            currentTime: dateUtils.formatDateTime(now)
          });

          // 检查当前时间是否已经达到或超过理论首次收益时间
          const isTimeReached = now >= theoreticalFirstProfitTime;
          profitLogger.firstProfit(investment.id, `是否达到首次收益时间: ${isTimeReached}`);

          if (!isTimeReached) {
            await transaction.rollback();
            return {
              success: false,
              message: '未达到首次收益时间',
              investment_id: investment.id
            };
          }

          profitLogger.firstProfit(investment.id, `已达到首次收益时间，准备发放首次收益`);
        } else {
          // 对于非首次收益，使用常规检查
          const isTimeToProfit = await isTimeToDistributeProfit(investment, project);
          if (!isTimeToProfit) {
            await transaction.rollback();
            return {
              success: false,
              message: '未达到收益发放时间',
              investment_id: investment.id
            };
          }
        }
      } else {
        profitLogger.profitComp(investment.id, `使用指定收益时间: ${dateUtils.formatDateTime(specificProfitTime)}, 跳过时间检查`);
      }

      // 检查是否达到最大收益次数
      if (hasReachedMaxProfitTimes(investment, project)) {
        // 如果达到最大收益次数，将投资状态更新为已完成
        investment.status = 'completed';
        await investment.save({ transaction });

        await transaction.rollback();
        return {
          success: false,
          message: '已达到最大收益次数',
          investment_id: investment.id
        };
      }

      // 检查是否已经存在相同投资ID和相同收益时间的收益记录
      const profitTime = specificProfitTime || new Date();

      // 设置时间容差范围（1分钟）
      const startTime = new Date(profitTime.getTime() - 60000); // 前1分钟
      const endTime = new Date(profitTime.getTime() + 60000);   // 后1分钟

      const existingProfit = await InvestmentProfit.findOne({
        where: {
          investment_id: investment.id,
          profit_time: {
            [Op.between]: [startTime, endTime]
          }
        },
        transaction
      });

      if (existingProfit) {
        profitLogger.profitDist(investment.id, `已存在相同时间的收益记录，跳过发放`, {
          existingProfitId: existingProfit.id,
          existingProfitTime: dateUtils.formatDateTime(existingProfit.profit_time),
          currentProfitTime: dateUtils.formatDateTime(profitTime)
        });

        await transaction.rollback();
        return {
          success: false,
          message: '已存在相同时间的收益记录',
          investment_id: investment.id,
          existing_profit_id: existingProfit.id
        };
      }

      // 计算收益
      const profit = calculateProfit(investment, project);

      // 使用前面已定义的profitTime变量
      const profitRecord = await InvestmentProfit.create({
        investment_id: investment.id,
        user_id: investment.user_id,
        amount: profit.amount,
        profit_time: profitTime,
        status: 'pending'
      }, { transaction });

      // 生成订单号
      const orderNumber = generateOrderNumberByType('profit');

      // 将收益添加到用户的收入账户
      const result = await balanceService.adjustBalance(
        investment.user_id,
        'income', // 收益添加到收入账户
        profit.amount,
        'add',
        'profit', // 交易类型为收益
        `投资收益 ${profit.amount}`,
        investment.id,
        'investment',
        transaction
      );

      // 更新收益记录状态和关联的交易ID
      profitRecord.status = 'paid';
      profitRecord.transaction_id = result.transactionId;
      await profitRecord.save({ transaction });

      // 更新投资记录，使用指定的收益时间或当前时间
      const currentTime = new Date();
      const lastProfitTime = specificProfitTime || currentTime;
      profitLogger.profitDist(investment.id, `更新收益时间`, {
        profitTime: dateUtils.formatDateTime(lastProfitTime),
        currentTime: dateUtils.formatDateTime(currentTime)
      });

      // 计算新的总收益和收益次数
      const newProfitCount = investment.profit_count + 1;
      const newTotalProfit = parseFloat(investment.total_profit) + parseFloat(profit.amount);

      // 使用原始 SQL 更新 last_profit_time 字段
      await sequelize.query(
        'UPDATE investments SET last_profit_time = ?, profit_count = ?, total_profit = ?, updated_at = ? WHERE id = ?',
        {
          replacements: [
            lastProfitTime,
            newProfitCount,
            newTotalProfit,
            currentTime,
            investment.id
          ],
          type: sequelize.QueryTypes.UPDATE,
          transaction
        }
      );

      // 记录详细日志
      profitLogger.profitDist(investment.id, `收益发放成功`, {
        lastProfitTime: dateUtils.formatDateTime(lastProfitTime),
        profitCount: newProfitCount,
        totalProfit: newTotalProfit,
        profitAmount: profit.amount
      });

      // 重新加载投资记录
      await investment.reload({ transaction });

      // 处理收益返佣（仅下级用户第一个产品产生收益时触发）
      await commissionService.processIncomeCommission(
        investment.user_id,
        profit.amount,
        result.transactionId,
        investment.id,
        transaction
      );

      // 提交事务
      await transaction.commit();

      // 收益发放成功后，安排下一次收益任务
      try {
        const queueService = require('./queueService');
        // 检查是否达到最大收益次数
        if (!hasReachedMaxProfitTimes(investment, project)) {
          // 只有在投资状态为活跃时才安排下一次任务
          if (investment.status === 'active') {
            const taskResult = await queueService.scheduleNextProfitTask(investment);
            profitLogger.profitTask(investment.id, `安排下一次收益任务成功`, { taskResult });
          }
        }
      } catch (taskError) {
        // 记录错误但不影响当前收益发放的结果
        profitLogger.profitTask(investment.id, `安排下一次收益任务失败`, {
          error: taskError.message,
          stack: taskError.stack
        });
      }

      return {
        success: true,
        message: '收益发放成功',
        investment_id: investment.id,
        profit_id: profitRecord.id,
        amount: profit.amount
      };
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('处理投资收益错误:', error);
    return {
      success: false,
      message: '处理投资收益错误: ' + error.message,
      investment_id: investment.id
    };
  }
};

/**
 * 检查投资是否应该在当前时间窗口内发放收益
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @param {Date} windowStart - 时间窗口开始时间
 * @param {Date} windowEnd - 时间窗口结束时间
 * @returns {Boolean} - 是否应该在当前时间窗口内发放收益
 */
const shouldDistributeProfitInTimeWindow = (investment, project, windowStart, windowEnd) => {
  // 检查是否达到最大收益次数
  if (hasReachedMaxProfitTimes(investment, project)) {
    return false;
  }

  // 检查今天是否是收益日
  const isProfitDay = isWeeklyProfitDay(project);
  if (!isProfitDay) {
    return false;
  }

  // 获取上次收益时间或投资开始时间
  const lastProfitTime = investment.last_profit_time
    ? new Date(investment.last_profit_time)
    : new Date(investment.start_time);

  if (!lastProfitTime || isNaN(lastProfitTime.getTime())) {
    return false;
  }

  // 计算下一次应该发放收益的时间
  const nextProfitTime = new Date(lastProfitTime.getTime() + project.profit_time * 60 * 60 * 1000);

  // 获取当前时间
  const now = new Date();

  // 确保当前时间已经达到或超过理论收益时间
  // 如果当前时间小于理论收益时间，即使在时间窗口内也不应该发放收益
  if (now < nextProfitTime) {
    console.log(`[收益发放] 投资ID: ${investment.id}, 当前时间(${dateUtils.formatDateTime(now)})小于理论收益时间(${dateUtils.formatDateTime(nextProfitTime)}), 不应提前发放收益`);
    return false;
  }

  // 对于长周期产品（24小时及以上），使用更大的时间窗口
  if (project.profit_time >= 24) {
    // 使用±2小时的扩展窗口，而不是传入的标准窗口
    const extendedWindowStart = new Date(now.getTime() - 2 * 60 * 60 * 1000);
    const extendedWindowEnd = new Date(now.getTime() + 2 * 60 * 60 * 1000);

    // 检查下一次收益时间是否在扩展窗口内
    const isInExtendedWindow = nextProfitTime >= extendedWindowStart && nextProfitTime <= extendedWindowEnd;

    if (isInExtendedWindow) {
      console.log(`[收益发放] 长周期产品(${project.profit_time}小时) 投资ID: ${investment.id}, 使用扩展时间窗口(±2小时)检查`);
    }

    return isInExtendedWindow;
  }

  // 对于短周期产品，使用传入的标准窗口
  return nextProfitTime >= windowStart && nextProfitTime <= windowEnd;
};

/**
 * 处理当前时间窗口内应该发放收益的投资记录
 * @returns {Promise<Object>} - 返回处理结果
 */
exports.processInvestmentProfitsInTimeWindow = async () => {
  try {
    console.log('开始处理当前时间窗口内的投资收益发放...');

    // 获取当前时间
    const now = new Date();

    // 定义时间窗口（当前时间的前后15分钟）
    const windowStart = new Date(now.getTime() - 15 * 60 * 1000);
    const windowEnd = new Date(now.getTime() + 15 * 60 * 1000);

    console.log(`时间窗口: ${dateUtils.formatDateTime(windowStart)} 到 ${dateUtils.formatDateTime(windowEnd)}`);

    // 获取所有活跃的投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      }
    });

    console.log(`找到 ${activeInvestments.length} 条活跃投资记录`);

    // 处理结果统计
    const results = {
      total: activeInvestments.length,
      eligible: 0,
      success: 0,
      failed: 0,
      details: []
    };

    // 逐个检查投资记录是否应该在当前时间窗口内发放收益
    for (const investment of activeInvestments) {
      // 获取项目信息
      const project = await Project.findByPk(investment.project_id);
      if (!project) {
        results.details.push({
          success: false,
          message: '项目不存在',
          investment_id: investment.id
        });
        continue;
      }

      // 检查是否应该在当前时间窗口内发放收益
      const shouldDistributeInWindow = await shouldDistributeProfitInTimeWindow(
        investment,
        project,
        windowStart,
        windowEnd
      );

      if (!shouldDistributeInWindow) {
        // 不在当前时间窗口内，跳过
        continue;
      }

      // 在当前时间窗口内应该发放收益
      results.eligible += 1;

      // 处理收益发放
      const result = await exports.processInvestmentProfit(investment);

      if (result.success) {
        results.success += 1;
      } else {
        results.failed += 1;
      }

      results.details.push(result);
    }

    console.log(`收益发放处理完成: 符合条件 ${results.eligible}, 成功 ${results.success}, 失败 ${results.failed}`);

    return results;
  } catch (error) {
    console.error('处理当前时间窗口内的投资收益错误:', error);
    throw error;
  }
};

/**
 * 检查长周期产品（24小时及以上）的收益发放
 * @returns {Promise<Object>} - 返回处理结果
 */
exports.checkLongCycleProductProfits = async () => {
  try {
    console.log('开始检查长周期产品的收益发放...');

    // 获取当前时间
    const now = new Date();

    // 定义扩展时间窗口（当前时间的前后2小时）
    const windowStart = new Date(now.getTime() - 2 * 60 * 60 * 1000);
    const windowEnd = new Date(now.getTime() + 2 * 60 * 60 * 1000);

    console.log(`扩展时间窗口: ${dateUtils.formatDateTime(windowStart)} 到 ${dateUtils.formatDateTime(windowEnd)}`);

    // 获取所有活跃的长周期投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      },
      include: [
        {
          model: Project,
          as: 'project',
          where: {
            profit_time: {
              [Op.gte]: 24 // 24小时及以上的产品
            }
          }
        }
      ]
    });

    console.log(`找到 ${activeInvestments.length} 条活跃的长周期投资记录`);

    // 处理结果统计
    const results = {
      total: activeInvestments.length,
      eligible: 0,
      success: 0,
      failed: 0,
      details: []
    };

    // 逐个检查投资记录
    for (const investment of activeInvestments) {
      const project = investment.project;

      // 获取上次收益时间或投资开始时间
      const lastProfitTime = investment.last_profit_time
        ? new Date(investment.last_profit_time)
        : new Date(investment.start_time);

      // 计算下一次应该发放收益的时间
      const nextProfitTime = new Date(lastProfitTime.getTime() + project.profit_time * 60 * 60 * 1000);

      // 检查是否达到收益条件
      const isTimeToProfit = now >= nextProfitTime;
      const isProfitDay = isWeeklyProfitDay(project);
      const hasReachedMax = hasReachedMaxProfitTimes(investment, project);

      console.log(`[长周期检查] 投资ID: ${investment.id}, 项目ID: ${project.id}`);
      console.log(`[长周期检查] 上次收益时间: ${dateUtils.formatDateTime(lastProfitTime)}`);
      console.log(`[长周期检查] 下一次理论收益时间: ${dateUtils.formatDateTime(nextProfitTime)}`);
      console.log(`[长周期检查] 是否达到时间: ${isTimeToProfit}, 是否是收益日: ${isProfitDay}, 是否达到最大次数: ${hasReachedMax}`);

      // 如果满足所有条件，发放收益
      if (isTimeToProfit && isProfitDay && !hasReachedMax) {
        results.eligible += 1;

        // 处理收益发放
        const result = await exports.processInvestmentProfit(investment);

        if (result.success) {
          results.success += 1;
        } else {
          results.failed += 1;
        }

        results.details.push(result);
      }
    }

    console.log(`长周期产品收益检查完成: 总计 ${results.total}, 符合条件 ${results.eligible}, 成功 ${results.success}, 失败 ${results.failed}`);

    return results;
  } catch (error) {
    console.error('检查长周期产品收益失败:', error);
    throw error;
  }
};

/**
 * 处理所有活跃投资的收益发放（用于手动触发）
 * @returns {Promise<Object>} - 返回处理结果
 */
exports.processAllInvestmentProfits = async () => {
  try {
    console.log('开始处理所有活跃投资的收益发放...');

    // 获取所有活跃的投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      }
    });

    console.log(`找到 ${activeInvestments.length} 条活跃投资记录`);

    // 处理结果统计
    const results = {
      total: activeInvestments.length,
      success: 0,
      failed: 0,
      details: []
    };

    // 逐个处理投资收益
    for (const investment of activeInvestments) {
      const result = await exports.processInvestmentProfit(investment);

      if (result.success) {
        results.success += 1;
      } else {
        results.failed += 1;
      }

      results.details.push(result);
    }

    console.log(`收益发放处理完成: 成功 ${results.success}, 失败 ${results.failed}`);

    return results;
  } catch (error) {
    console.error('处理所有投资收益错误:', error);
    throw error;
  }
};
