/**
 * 收益处理器
 * 负责处理投资收益发放
 */
const sequelize = require('../config/database');
const { Investment, Project, User, InvestmentProfit } = require('../models');
const balanceService = require('./balanceService');
const commissionService = require('./commissionService');
const profitScheduler = require('./profitScheduler');
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');

/**
 * 检查今天是否是收益日
 * @param {Object} project - 项目信息
 * @returns {boolean} - 是否是收益日
 */
const isWeeklyProfitDay = (project) => {
  // 如果项目没有设置weekly_profit_days或者设置为空，默认所有日期都是收益日
  if (!project.weekly_profit_days || project.weekly_profit_days.trim() === '') {
    return true;
  }

  // 获取当前是星期几
  const dayOfWeek = new Date().getDay();
  const adjustedDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;

  // 解析weekly_profit_days
  const weeklyProfitDays = project.weekly_profit_days.split(',').map(day => parseInt(day.trim()));

  // 检查今天是否在收益天数中
  return weeklyProfitDays.includes(adjustedDayOfWeek);
};

/**
 * 处理投资收益
 * @param {number} investmentId - 投资ID
 * @returns {Promise<Object>} - 处理结果
 */
const processInvestmentProfit = async (investmentId) => {
  let transaction;

  try {
    // 创建事务
    transaction = await sequelize.transaction();

    logger.info(`开始处理投资ID ${investmentId} 的收益...`);

    // 获取投资记录
    const investment = await Investment.findByPk(investmentId, {
      include: [
        {
          model: Project,
          as: 'project'
        },
        {
          model: User,
          as: 'user'
        }
      ],
      transaction
    });

    if (!investment) {
      throw new Error(`投资记录不存在: ${investmentId}`);
    }

    // 检查投资状态
    if (investment.status !== 'active') {
      logger.info(`投资ID ${investmentId} 不是活跃状态，跳过收益发放`);
      await transaction.commit();
      return {
        success: false,
        message: '投资不是活跃状态',
        investment_id: investmentId
      };
    }

    // 检查今天是否是收益日
    const isProfitDay = isWeeklyProfitDay(investment.project);

    if (!isProfitDay) {
      // 如果今天不是收益日，跳过收益发放，但仍然安排下一次任务
      await transaction.commit();

      // 安排下一次收益任务
      await profitScheduler.scheduleNextProfitTask(investment, investment.project);

      logger.info(`投资ID ${investmentId} 今天不是收益日，跳过收益发放`);
      return {
        success: false,
        message: '今天不是收益日',
        investment_id: investmentId
      };
    }

    // 检查是否达到最大收益次数
    if (investment.project.max_profit_times > 0 && investment.profit_count >= investment.project.max_profit_times) {
      // 如果达到最大收益次数，将投资状态更新为已完成
      investment.status = 'completed';
      await investment.save({ transaction });

      await transaction.commit();

      logger.info(`投资ID ${investmentId} 已达到最大收益次数，状态更新为已完成`);
      return {
        success: false,
        message: '已达到最大收益次数',
        investment_id: investmentId
      };
    }

    // 计算收益
    const profitAmount = (investment.amount * investment.profit_rate / 100).toFixed(2);

    // 创建收益记录
    const profitRecord = await InvestmentProfit.create({
      investment_id: investment.id,
      user_id: investment.user_id,
      amount: profitAmount,
      profit_time: new Date(), // 使用当前时间作为收益时间
      status: 'pending'
    }, { transaction });

    // 将收益添加到用户的收入账户
    const balanceResult = await balanceService.adjustBalance(
      investment.user_id,
      'income', // 收益添加到收入账户
      profitAmount,
      'add',
      'profit', // 交易类型为收益
      `投资收益 ${investment.project.name}`,
      investment.id,
      'investment',
      transaction
    );

    // 更新收益记录状态和关联的交易ID
    profitRecord.status = 'paid';
    profitRecord.transaction_id = balanceResult.transactionId;
    await profitRecord.save({ transaction });

    // 更新投资记录
    const now = new Date();

    // 更新投资记录
    investment.last_profit_time = now;
    investment.profit_count += 1;
    investment.total_profit = (parseFloat(investment.total_profit || 0) + parseFloat(profitAmount)).toFixed(2);

    await investment.save({ transaction });

    // 处理收益返佣
    await commissionService.processIncomeCommission(
      investment.user_id,
      profitAmount,
      balanceResult.transactionId,
      investment.id,
      transaction
    );

    await transaction.commit();

    // 安排下一次收益任务
    await profitScheduler.scheduleNextProfitTask(investment, investment.project);

    logger.info(`投资ID ${investmentId} 的收益发放成功，金额: ${profitAmount}`);

    return {
      success: true,
      message: '收益发放成功',
      investment_id: investment.id,
      profit_id: profitRecord.id,
      amount: profitAmount
    };
  } catch (error) {
    // 只有当事务存在时才回滚
    if (transaction) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        logger.error(`回滚事务失败:`, rollbackError);
      }
    }

    logger.error(`处理投资ID ${investmentId} 的收益失败:`, error);
    return {
      success: false,
      message: `收益发放失败: ${error.message}`,
      investment_id: investmentId
    };
  }
};

module.exports = {
  processInvestmentProfit,
  isWeeklyProfitDay
};
