/**
 * 内存优化工具类
 * 用于优化应用程序的内存使用
 */

/**
 * 强制执行垃圾回收
 * 注意：这只是建议垃圾回收器运行，不保证立即执行
 */
function forceGarbageCollection() {
  if (global.gc) {
    global.gc();
    return true;
  }
  return false;
}

/**
 * 获取当前内存使用情况
 * @returns {Object} 内存使用情况
 */
function getMemoryUsage() {
  const memoryData = process.memoryUsage();
  
  return {
    rss: formatMemoryUsage(memoryData.rss),
    heapTotal: formatMemoryUsage(memoryData.heapTotal),
    heapUsed: formatMemoryUsage(memoryData.heapUsed),
    external: formatMemoryUsage(memoryData.external),
    arrayBuffers: formatMemoryUsage(memoryData.arrayBuffers || 0)
  };
}

/**
 * 格式化内存使用量
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的内存使用量
 */
function formatMemoryUsage(bytes) {
  return `${Math.round(bytes / 1024 / 1024 * 100) / 100} MB`;
}

/**
 * 检查内存使用情况，如果超过阈值则尝试回收内存
 * @param {number} threshold - 堆内存使用阈值（MB）
 * @returns {boolean} 是否尝试回收内存
 */
function checkAndOptimizeMemory(threshold = 100) {
  const memoryData = process.memoryUsage();
  const heapUsedMB = memoryData.heapUsed / 1024 / 1024;
  
  if (heapUsedMB > threshold) {
    return forceGarbageCollection();
  }
  
  return false;
}

/**
 * 创建内存使用监控器
 * @param {number} interval - 检查间隔（毫秒）
 * @param {number} threshold - 堆内存使用阈值（MB）
 * @param {Function} callback - 回调函数，接收内存使用情况作为参数
 * @returns {Object} 监控器对象
 */
function createMemoryMonitor(interval = 60000, threshold = 100, callback = null) {
  let timer = null;
  
  const start = () => {
    if (timer) return;
    
    timer = setInterval(() => {
      const memoryUsage = getMemoryUsage();
      const memoryData = process.memoryUsage();
      const heapUsedMB = memoryData.heapUsed / 1024 / 1024;
      
      // 如果超过阈值，尝试回收内存
      if (heapUsedMB > threshold) {
        forceGarbageCollection();
      }
      
      // 如果提供了回调函数，调用它
      if (typeof callback === 'function') {
        callback(memoryUsage, heapUsedMB > threshold);
      }
    }, interval);
    
    return true;
  };
  
  const stop = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
      return true;
    }
    return false;
  };
  
  return {
    start,
    stop,
    getMemoryUsage
  };
}

/**
 * 对象池
 * 用于重用对象，减少垃圾回收压力
 */
class ObjectPool {
  /**
   * 创建对象池
   * @param {Function} factory - 创建对象的工厂函数
   * @param {Function} reset - 重置对象的函数
   * @param {number} initialSize - 初始池大小
   */
  constructor(factory, reset, initialSize = 10) {
    this.factory = factory;
    this.reset = reset;
    this.pool = [];
    
    // 初始化对象池
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.factory());
    }
  }
  
  /**
   * 从池中获取对象
   * @returns {Object} 对象
   */
  acquire() {
    if (this.pool.length > 0) {
      return this.pool.pop();
    }
    
    return this.factory();
  }
  
  /**
   * 将对象归还到池中
   * @param {Object} obj - 对象
   */
  release(obj) {
    if (obj) {
      this.reset(obj);
      this.pool.push(obj);
    }
  }
  
  /**
   * 获取池大小
   * @returns {number} 池大小
   */
  size() {
    return this.pool.length;
  }
  
  /**
   * 清空池
   */
  clear() {
    this.pool = [];
  }
}

module.exports = {
  forceGarbageCollection,
  getMemoryUsage,
  formatMemoryUsage,
  checkAndOptimizeMemory,
  createMemoryMonitor,
  ObjectPool
};
