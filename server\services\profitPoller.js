/**
 * 收益任务轮询器
 * 负责轮询Redis获取到期的收益任务并处理
 */
const redisClient = require('../utils/redisClient');
const { Investment, Project } = require('../models');
const logger = require('../utils/logger');

/**
 * 轮询Redis获取到期的任务
 * 每秒执行一次
 */
const pollRedisTasks = async () => {
  try {
    // 获取当前时间戳
    const now = Date.now();
    const currentDate = new Date();

    // 方案5.1：记录当前UTC时间，便于调试
    logger.debug(`轮询器执行，当前UTC时间: ${currentDate.toISOString()}, 时间戳: ${now}`);

    // 获取所有到期的任务（分数小于等于当前时间戳的成员）
    const investmentIds = await redisClient.getDueProfitTasks(now);

    if (investmentIds.length === 0) {
      return;
    }

    logger.info(`发现 ${investmentIds.length} 个到期的收益任务`);

    // 处理到期的任务
    for (const investmentId of investmentIds) {
      try {
        // 从Redis中移除任务
        await redisClient.removeProfitTask(investmentId);

        // 查询投资记录
        const investment = await Investment.findByPk(investmentId, {
          include: [{ model: Project, as: 'project' }]
        });

        if (!investment) {
          logger.warn(`投资ID ${investmentId} 不存在，跳过处理`);
          continue;
        }

        try {
          // 使用统一服务处理收益
          const profitUnifiedService = require('./profitUnifiedService');
          await profitUnifiedService.processInvestmentProfit(investment, null, false);
        } catch (processingError) {
          logger.error(`处理投资ID ${investmentId} 的收益失败:`, processingError);
        }
      } catch (error) {
        logger.error(`处理投资ID ${investmentId} 的收益失败:`, error);
      }
    }
  } catch (error) {
    logger.error('轮询Redis任务失败:', error);
  }
};

// 轮询器实例
let pollerInterval = null;

/**
 * 启动轮询器
 */
const startPoller = () => {
  // 如果已经启动，先停止
  if (pollerInterval) {
    clearInterval(pollerInterval);
  }

  // 每秒执行一次轮询
  pollerInterval = setInterval(async () => {
    try {
      await pollRedisTasks();
    } catch (error) {
      logger.error('轮询任务执行失败:', error);
    }
  }, 1000);
  logger.info('收益任务轮询器已启动');
};

/**
 * 停止轮询器
 */
const stopPoller = () => {
  if (pollerInterval) {
    clearInterval(pollerInterval);
    pollerInterval = null;
    logger.info('收益任务轮询器已停止');
  }
};

module.exports = {
  pollRedisTasks,
  startPoller,
  stopPoller
};
