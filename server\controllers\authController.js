const jwt = require('jsonwebtoken');
const { Admin, Role } = require('../models');
const { addToBlacklist } = require('../utils/jwtBlacklist');

// 管理员登录
exports.adminLogin = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证请求数据
    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空',
        data: null
      });
    }

    // 查找管理员
    const admin = await Admin.findOne({ where: { username } });
    if (!admin) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误',
        data: null
      });
    }

    // 验证密码
    const isPasswordValid = await admin.validatePassword(password);

    if (!isPasswordValid) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误',
        data: null
      });
    }

    // 检查管理员状态
    if (!admin.status) {
      return res.status(403).json({
        code: 403,
        message: '账号已被禁用',
        data: null
      });
    }

    // 更新最后登录时间
    admin.last_login = new Date();
    await admin.save();

    // 使用token服务生成JWT令牌
    const tokenService = require('../services/tokenService');
    const token = await tokenService.generateAdminToken(admin);

    // 获取管理员角色
    const adminWithRoles = await Admin.findByPk(admin.id, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ]
    });

    // 返回响应
    return res.status(200).json({
      code: 200,
      message: '登录成功',
      data: {
        token,
        admin: {
          id: admin.id,
          username: admin.username,
          nickname: admin.nickname,
          is_super: admin.is_super,
          status: admin.status,
          last_login: admin.last_login,
          created_at: admin.createdAt,
          roles: adminWithRoles.roles
        }
      }
    });
  } catch (error) {
    console.error('管理员登录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};



// 获取当前管理员信息
exports.getAdminProfile = async (req, res) => {
  try {
    const adminId = req.admin.id;

    // 获取管理员及其角色信息
    const admin = await Admin.findByPk(adminId, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ]
    });

    if (!admin) {
      return res.status(404).json({
        code: 404,
        message: '管理员不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        id: admin.id,
        username: admin.username,
        nickname: admin.nickname,
        is_super: admin.is_super,
        status: admin.status,
        last_login: admin.last_login,
        created_at: admin.createdAt,
        roles: admin.roles
      }
    });
  } catch (error) {
    console.error('获取管理员信息错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};



// 更新管理员密码
exports.updateAdminPassword = async (req, res) => {
  try {
    const { old_password, new_password } = req.body;
    const admin = req.admin;

    // 验证请求数据
    if (!old_password || !new_password) {
      return res.status(400).json({
        code: 400,
        message: '旧密码和新密码不能为空',
        data: null
      });
    }

    // 验证旧密码
    const isPasswordValid = await admin.validatePassword(old_password);
    if (!isPasswordValid) {
      return res.status(401).json({
        code: 401,
        message: '旧密码错误',
        data: null
      });
    }

    // 获取当前请求的token
    const authHeader = req.headers.authorization;
    const token = authHeader.split(' ')[1];

    // 使用token服务使token失效
    const tokenService = require('../services/tokenService');
    await tokenService.invalidateAdminToken(token);

    // 更新密码
    admin.password = new_password;
    await admin.save();

    return res.status(200).json({
      code: 200,
      message: '密码更新成功，请重新登录',
      data: null
    });
  } catch (error) {
    console.error('更新管理员密码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};


// 管理员登出
exports.adminLogout = async (req, res) => {
  try {
    // 获取当前请求的token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(400).json({
        code: 400,
        message: '未提供有效的认证令牌',
        data: null
      });
    }

    const token = authHeader.split(' ')[1];

    // 使用token服务使token失效
    const tokenService = require('../services/tokenService');
    const result = await tokenService.invalidateAdminToken(token);

    if (!result) {
      return res.status(400).json({
        code: 400,
        message: '登出失败，令牌无效',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '登出成功',
      data: null
    });
  } catch (error) {
    console.error('管理员登出错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};


