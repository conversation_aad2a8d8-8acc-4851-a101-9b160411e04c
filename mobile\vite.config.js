import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './'),
      '@api': resolve(__dirname, './services/api'),  // 使用新的路径前缀
      '@utils': resolve(__dirname, './utils'),
      '@components': resolve(__dirname, './components'),
      '@pages': resolve(__dirname, './pages')
    }
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:3000', // 使用IPv4地址而不是localhost
        changeOrigin: true,
        secure: false,
        timeout: 60000, // 增加超时时间到60秒
        proxyTimeout: 60000, // 增加代理超时时间到60秒
        rewrite: (path) => {
          return path;
        },
        configure: (proxy, _options) => {
          // 代理错误事件
          proxy.on('error', (err, req, res) => {
            // 如果响应尚未发送，返回一个错误响应
            if (!res.headersSent) {
              res.writeHead(500, {
                'Content-Type': 'application/json'
              });
              res.end(JSON.stringify({
                code: 500,
                message: '代理服务器错误: ' + (err.message || '未知错误'),
                data: null
              }));
            }
          });
        }
      },
      '/uploads': {
        target: 'http://127.0.0.1:3000', // 使用IPv4地址而不是localhost
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          return path;
        }
      }
    },
    // 添加中间件，处理直接访问/register的情况
    configureServer(server) {
      server.middlewares.use((req, res, next) => {
        // 检查是否是/register路径
        if (req.url.startsWith('/register')) {
          // 提取code参数
          const codeMatch = req.url.match(/code=([^&]+)/);
          const code = codeMatch ? codeMatch[1] : '';

          // 重定向到正确的uni-app路由
          res.writeHead(302, {
            'Location': `/#/pages/register/index${code ? `?code=${code}` : ''}`
          });
          res.end();
          return;
        }
        next();
      });
    },
    fs: {
      // 确保不将API路径解析为静态文件
      strict: true,
      allow: ['../']
    }
  },
  build: {
    // 确保正确处理静态资源
    assetsInlineLimit: 4096,
    // 输出目录
    outDir: 'dist',
    // 生成sourcemap
    sourcemap: process.env.NODE_ENV === 'development'
  }
});
