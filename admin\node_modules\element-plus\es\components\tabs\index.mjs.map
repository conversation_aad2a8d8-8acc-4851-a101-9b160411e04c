{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/tabs/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON>oopInstall } from '@element-plus/utils'\nimport Tabs from './src/tabs'\nimport TabPane from './src/tab-pane.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTabs: SFCWithInstall<typeof Tabs> & {\n  TabPane: typeof TabPane\n} = withInstall(Tabs, {\n  TabPane,\n})\nexport const ElTabPane: SFCWithInstall<typeof TabPane> =\n  withNoopInstall(TabPane)\nexport default ElTabs\n\nexport * from './src/tabs'\nexport * from './src/tab-bar'\nexport * from './src/tab-nav'\nexport * from './src/tab-pane'\nexport * from './src/constants'\n"], "names": [], "mappings": ";;;;;;;;;AAGY,MAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE;AACxC,EAAE,OAAO;AACT,CAAC,EAAE;AACS,MAAC,SAAS,GAAG,eAAe,CAAC,OAAO;;;;"}