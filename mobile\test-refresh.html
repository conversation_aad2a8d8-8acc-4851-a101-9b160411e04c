<!DOCTYPE html>
<html>
<head>
    <title>缓存刷新功能测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>缓存刷新功能测试</h1>
    
    <div class="test-section">
        <h2>测试1: 应用启动刷新</h2>
        <p class="info">测试步骤：</p>
        <ol>
            <li>点击"模拟应用启动"按钮</li>
            <li>检查localStorage中是否设置了appJustLaunched标记</li>
            <li>模拟页面onShow，检查是否触发刷新</li>
        </ol>
        <button onclick="simulateAppLaunch()">模拟应用启动</button>
        <button onclick="simulatePageShow()">模拟页面显示</button>
        <button onclick="checkStorage()">检查存储</button>
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: TabBar双击刷新</h2>
        <p class="info">测试步骤：</p>
        <ol>
            <li>快速双击"模拟TabBar点击"按钮</li>
            <li>检查是否触发双击刷新逻辑</li>
        </ol>
        <button onclick="simulateTabClick()">模拟TabBar点击</button>
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: API缓存控制</h2>
        <p class="info">测试API请求是否包含防缓存参数：</p>
        <button onclick="testApiCall()">测试API调用</button>
        <div id="test3-result"></div>
    </div>

    <script>
        let lastTabClickTime = 0;
        let clickCount = 0;
        
        // 模拟应用启动
        function simulateAppLaunch() {
            localStorage.setItem('appJustLaunched', 'true');
            document.getElementById('test1-result').innerHTML = 
                '<p class="success">✅ 应用启动标记已设置</p>';
        }
        
        // 模拟页面显示
        function simulatePageShow() {
            const appJustLaunched = localStorage.getItem('appJustLaunched');
            
            if (appJustLaunched) {
                // 模拟刷新逻辑
                localStorage.removeItem('appJustLaunched');
                document.getElementById('test1-result').innerHTML += 
                    '<p class="success">✅ 检测到应用启动，触发数据刷新</p>' +
                    '<p class="success">✅ 启动标记已清除</p>';
            } else {
                document.getElementById('test1-result').innerHTML += 
                    '<p class="info">ℹ️ 未检测到应用启动标记，不触发刷新</p>';
            }
        }
        
        // 检查存储
        function checkStorage() {
            const appJustLaunched = localStorage.getItem('appJustLaunched');
            document.getElementById('test1-result').innerHTML += 
                `<p>当前appJustLaunched值: ${appJustLaunched || 'null'}</p>`;
        }
        
        // 模拟TabBar点击
        function simulateTabClick() {
            const now = Date.now();
            const timeDiff = now - lastTabClickTime;
            
            if (timeDiff < 500) {
                clickCount++;
                
                if (clickCount >= 2) {
                    document.getElementById('test2-result').innerHTML = 
                        '<p class="success">✅ 检测到双击，触发刷新！</p>';
                    clickCount = 0;
                } else {
                    document.getElementById('test2-result').innerHTML = 
                        `<p class="info">点击次数: ${clickCount}</p>`;
                }
            } else {
                clickCount = 1;
                document.getElementById('test2-result').innerHTML = 
                    '<p class="info">第一次点击</p>';
            }
            
            lastTabClickTime = now;
        }
        
        // 测试API调用
        function testApiCall() {
            const timestamp = Date.now();
            const apiUrl = `http://localhost:3000/api/mobile/projects?_t=${timestamp}`;
            
            document.getElementById('test3-result').innerHTML = 
                `<p class="success">✅ API URL包含防缓存参数:</p>` +
                `<p><code>${apiUrl}</code></p>`;
        }
    </script>
</body>
</html>
