/**
 * 任务队列服务
 * 使用Bull和Redis实现任务队列系统
 */
const moment = require('moment');
const { sequelize, Investment, Project, User, InvestmentProfit } = require('../models');
const { Op } = require('sequelize');
const balanceService = require('./balanceService');
const commissionService = require('./commissionService');
// 使用延迟加载避免循环依赖
let profitService = null;
const getProfitService = () => {
  if (!profitService) {
    profitService = require('./profitService');
  }
  return profitService;
};
const timezoneUtils = require('../utils/timezoneUtils');
const dateUtils = require('../utils/dateUtils');
const logger = require('../utils/logger');
const profitLogger = require('../utils/profitLogger');
const redisConfig = require('../config/redis');

// 检查Redis是否可用
let profitQueue = null;
let redisAvailable = false;

try {
  const Bull = require('bull');
  // 创建收益发放队列
  profitQueue = new Bull('investment-profits', {
    redis: redisConfig,
    defaultJobOptions: {
      attempts: 3, // 任务失败后重试3次
      backoff: {
        type: 'exponential',
        delay: 5000, // 初始延迟5秒
      },
      removeOnComplete: true, // 完成后删除任务
      removeOnFail: false, // 失败后不删除任务，便于排查问题
    }
  });

  // 设置Redis可用标志
  redisAvailable = true;

  logger.info('Redis队列服务初始化成功');
} catch (error) {
  logger.error('Redis队列服务初始化失败:', error);
  logger.warn('系统将在没有队列服务的情况下运行，收益发放功能可能受到影响');
}

/**
 * 初始化任务队列
 */
exports.initQueues = async () => {
  try {
    logger.info('正在初始化任务队列...');

    // 检查Redis是否可用
    if (!redisAvailable || !profitQueue) {
      logger.warn('Redis不可用，跳过任务队列初始化');
      return {
        success: false,
        message: 'Redis不可用，跳过任务队列初始化'
      };
    }

    // 清空现有队列中的所有任务
    await profitQueue.empty();

    // 注册处理器
    profitQueue.process(async (job) => {
      return await processInvestmentProfitNoTransaction(job.data);
    });

    // 注册事件监听器
    profitQueue.on('completed', (job, result) => {
      logger.info(`收益发放任务完成: ${job.id}`, { result });
    });

    profitQueue.on('failed', (job, error) => {
      logger.error(`收益发放任务失败: ${job.id}`, { error: error.message });
    });

    // 加载所有活跃投资的收益任务
    await loadAllActiveInvestmentTasks();

    logger.info('任务队列初始化完成');

    return {
      success: true,
      message: '任务队列初始化成功'
    };
  } catch (error) {
    logger.error('初始化任务队列失败:', error);
    return {
      success: false,
      message: '初始化任务队列失败: ' + error.message
    };
  }
};

/**
 * 加载所有活跃投资的收益任务
 */
const loadAllActiveInvestmentTasks = async () => {
  try {
    logger.info('正在加载所有活跃投资的收益任务...');

    // 检查Redis是否可用
    if (!redisAvailable || !profitQueue) {
      logger.warn('Redis不可用，跳过加载活跃投资收益任务');
      return {
        success: false,
        message: 'Redis不可用，跳过加载活跃投资收益任务',
        count: 0
      };
    }

    // 获取所有活跃的投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      },
      include: [
        {
          model: Project,
          as: 'project'
        },
        {
          model: User,
          as: 'user'
        }
      ]
    });

    logger.info(`找到 ${activeInvestments.length} 条活跃投资记录`);

    // 为每个投资记录创建收益任务
    for (const investment of activeInvestments) {
      await scheduleNextProfitTask(investment);
    }

    logger.info('所有活跃投资的收益任务加载完成');

    return {
      success: true,
      count: activeInvestments.length
    };
  } catch (error) {
    logger.error('加载活跃投资收益任务失败:', error);
    return {
      success: false,
      message: '加载活跃投资收益任务失败: ' + error.message,
      count: 0
    };
  }
};

/**
 * 为投资记录安排下一次收益任务
 * @param {Object} investment - 投资记录
 * @returns {Promise<Object>} - 返回任务信息
 */
exports.scheduleNextProfitTask = async (investment) => {
  return await scheduleNextProfitTask(investment);
};

/**
 * 为投资记录安排下一次收益任务
 * @param {Object} investment - 投资记录
 * @returns {Promise<Object>} - 返回任务信息
 */
const scheduleNextProfitTask = async (investment) => {
  try {
    // 检查Redis是否可用
    if (!redisAvailable || !profitQueue) {
      logger.warn('Redis不可用，跳过创建收益任务');
      return {
        success: false,
        message: 'Redis不可用，跳过创建收益任务',
        investment_id: investment?.id || 'unknown'
      };
    }

    // 检查投资记录是否存在
    if (!investment) {
      return {
        success: false,
        message: '投资记录不存在',
        investment_id: 'unknown'
      };
    }

    // 如果投资记录不是活跃状态，不创建任务
    if (!investment.status || investment.status !== 'active') {
      return {
        success: false,
        message: '投资记录不是活跃状态',
        investment_id: investment.id || 'unknown'
      };
    }

    // 获取项目信息
    let project = null;
    if (investment.project) {
      project = investment.project;
    } else if (investment.project_id) {
      project = await Project.findByPk(investment.project_id);
    }

    if (!project) {
      return {
        success: false,
        message: '项目不存在',
        investment_id: investment.id
      };
    }

    // 检查是否达到最大收益次数
    if (project.max_profit_times > 0 && investment.profit_count >= project.max_profit_times) {
      return {
        success: false,
        message: '已达到最大收益次数',
        investment_id: investment.id
      };
    }

    // 计算下一次收益时间
    const nextProfitTime = await calculateNextProfitTime(investment, project);
    if (!nextProfitTime) {
      return {
        success: false,
        message: '无法计算下一次收益时间',
        investment_id: investment.id
      };
    }

    // 计算延迟时间（毫秒） - 直接使用系统时间，不进行时区转换
    const now = moment();

    // 确保nextProfitTime是有效的moment对象
    if (!nextProfitTime || !nextProfitTime.isValid()) {
      logger.error(`投资ID ${investment.id} 的下一次收益时间无效: ${nextProfitTime}`);
      return {
        success: false,
        message: '下一次收益时间无效',
        investment_id: investment.id
      };
    }

    // 计算延迟时间
    const delayMs = nextProfitTime.diff(now);

    // 如果延迟时间小于0，表示已经过了收益时间，立即执行
    const actualDelayMs = Math.max(delayMs, 0);



    // 检查是否是首次收益
    const isFirstProfit = !investment.last_profit_time;
    if (isFirstProfit) {
      profitLogger.firstProfit(investment.id, `首次收益任务已安排，将在 ${nextProfitTime.format('YYYY-MM-DD HH:mm:ss')} 执行`);
    }

    // 创建任务数据
    const jobData = {
      investment_id: investment.id,
      project_id: project.id,
      user_id: investment.user_id,
      scheduled_time: nextProfitTime.format('YYYY-MM-DD HH:mm:ss'),
      profit_time: project.profit_time,
      profit_rate: investment.profit_rate,
      is_first_profit: isFirstProfit
    };

    try {
      // 检查是否已存在相同的任务
      const existingJobs = await profitQueue.getJobs(['delayed', 'waiting']);
      const duplicateJob = existingJobs.find(job =>
        job.data.investment_id === investment.id &&
        job.data.scheduled_time === jobData.scheduled_time
      );

      if (duplicateJob) {
        return {
          success: true,
          message: '收益任务已存在',
          job_id: duplicateJob.id,
          investment_id: investment.id,
          scheduled_time: jobData.scheduled_time
        };
      }

      // 添加到队列
      const job = await profitQueue.add(jobData, {
        delay: actualDelayMs,
        jobId: `profit:${investment.id}:${nextProfitTime.valueOf()}`
      });



      return {
        success: true,
        message: '收益任务创建成功',
        job_id: job.id,
        investment_id: investment.id,
        scheduled_time: jobData.scheduled_time
      };
    } catch (queueError) {
      logger.error(`为投资ID ${investment.id} 创建收益任务失败:`, queueError);
      return {
        success: false,
        message: '创建收益任务失败: ' + queueError.message,
        investment_id: investment.id
      };
    }
  } catch (error) {
    const investmentId = investment && investment.id ? investment.id : 'unknown';
    logger.error(`为投资ID ${investmentId} 创建收益任务失败:`, error);
    return {
      success: false,
      message: '创建收益任务失败: ' + error.message,
      investment_id: investmentId
    };
  }
};

/**
 * 计算下一次收益时间
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @returns {Promise<Object>} - 返回下一次收益时间（moment对象）
 */
const calculateNextProfitTime = async (investment, project) => {
  try {
    // 获取上次收益时间或投资开始时间 - 直接使用原始时间，不进行时区转换
    let baseTime;
    let isFirstProfit = false;

    if (investment.last_profit_time) {
      // 确保使用正确的格式解析日期字符串
      baseTime = moment(investment.last_profit_time, 'YYYY-MM-DD HH:mm:ss');

    } else {
      // 确保使用正确的格式解析日期字符串
      const startTimeStr = investment.start_time || investment.created_at;
      baseTime = moment(startTimeStr, 'YYYY-MM-DD HH:mm:ss');

      // 记录原始字符串和解析后的时间，以便调试
      profitLogger.firstProfit(investment.id, `使用开始时间作为基准`, {
        originalTimeString: startTimeStr,
        parsedTime: baseTime.format('YYYY-MM-DD HH:mm:ss')
      });

      isFirstProfit = true;
    }

    // 如果没有基准时间，使用当前时间（这种情况应该很少发生）
    if (!baseTime || !baseTime.isValid()) {
      const now = moment();
      baseTime = now;
      profitLogger.profitCalc(investment.id, `没有有效的基准时间，使用当前时间作为基准: ${now.format('YYYY-MM-DD HH:mm:ss')}`);
    }

    // 计算下一次收益时间，始终基于上次收益时间或投资开始时间
    const nextProfitTime = moment(baseTime).add(project.profit_time, 'hours');

    // 获取当前时间
    const now = moment();

    // 如果计算出的下一次收益时间已经过去，则需要重新计算
    // 这种情况可能发生在系统长时间停机后重启，或者补偿机制执行后
    if (nextProfitTime < now) {


      // 计算从基准时间到现在应该经过的收益周期数
      const hoursSinceBase = now.diff(baseTime, 'hours');
      const cyclesPassed = Math.floor(hoursSinceBase / project.profit_time);

      // 计算最近一次应该发放收益的时间点
      const lastTheoreticalProfitTime = moment(baseTime).add(cyclesPassed * project.profit_time, 'hours');

      // 计算下一次应该发放收益的时间点
      const recalculatedNextProfitTime = moment(lastTheoreticalProfitTime).add(project.profit_time, 'hours');



      return recalculatedNextProfitTime;
    }



    // 对于首次收益，额外记录日志
    if (isFirstProfit) {
      profitLogger.firstProfit(investment.id, `首次收益时间计算完成，将在 ${nextProfitTime.format('YYYY-MM-DD HH:mm:ss')} 发放首次收益`);
    }

    return nextProfitTime;
  } catch (error) {
    logger.error('计算下一次收益时间错误:', error);
    return null;
  }
};

/**
 * 处理投资收益（无事务模式）
 * @param {Object} data - 任务数据
 * @returns {Promise<Object>} - 返回处理结果
 */
const processInvestmentProfitNoTransaction = async (data) => {
  try {
    const investmentId = data.investment_id;
    profitLogger.profitDist(investmentId, `开始处理收益（无事务模式）`, { taskData: data });

    // 解析特定收益时间（如果有）
    let specificProfitTime = null;
    if (data.specific_profit_time) {
      specificProfitTime = new Date(data.specific_profit_time);
    }

    // 检查是否是补偿调用
    const isCompensation = data.is_compensation === true;

    // 获取投资记录
    const investment = await Investment.findByPk(investmentId, {
      include: [
        {
          model: Project,
          as: 'project'
        }
      ]
    });

    if (!investment) {
      throw new Error(`投资记录不存在: ${investmentId}`);
    }

    // 检查投资状态
    if (investment.status !== 'active') {
      return {
        success: false,
        message: `投资记录不是活跃状态: ${investmentId}`,
        investment_id: investmentId
      };
    }

    // 获取项目信息
    const project = investment.project;
    if (!project) {
      return {
        success: false,
        message: `项目不存在: ${investment.project_id}`,
        investment_id: investmentId
      };
    }

    // 计算理论收益时间（用户应该获得收益的时间）
    const theoreticalProfitTime = calculateTheoreticalProfitTime(investment, project, specificProfitTime);

    // 实际发放时间是当前时间
    const distributionTime = new Date();

    // 记录两个时间点
    logger.info(`投资ID ${investment.id} 的理论收益时间: ${dateUtils.formatDateTime(theoreticalProfitTime)}, 实际发放时间: ${dateUtils.formatDateTime(distributionTime)}`);

    // 检查当前时间是否已经达到或超过理论收益时间
    // 只有在补偿模式下或当前时间已经达到理论收益时间时才发放收益
    if (!isCompensation && distributionTime < theoreticalProfitTime) {
      logger.warn(`投资ID ${investment.id} 的理论收益时间(${dateUtils.formatDateTime(theoreticalProfitTime)})尚未到达，当前时间为(${dateUtils.formatDateTime(distributionTime)})，跳过发放`);

      // 重新安排任务，确保在理论收益时间发放
      const delayMs = theoreticalProfitTime.getTime() - distributionTime.getTime();

      // 创建新任务
      try {
        await profitQueue.add({
          ...data,
          scheduled_time: dateUtils.formatDateTime(theoreticalProfitTime)
        }, {
          delay: delayMs,
          jobId: `profit:${investment.id}:${theoreticalProfitTime.getTime()}`
        });

        logger.info(`投资ID ${investment.id} 的收益任务已重新安排，将在 ${dateUtils.formatDateTime(theoreticalProfitTime)} 执行，延迟 ${delayMs} 毫秒`);
      } catch (error) {
        logger.error(`重新安排投资ID ${investment.id} 的收益任务失败:`, error);
      }

      return {
        success: false,
        message: '理论收益时间尚未到达，已重新安排任务',
        investment_id: investment.id,
        theoretical_profit_time: theoreticalProfitTime,
        distribution_time: distributionTime
      };
    }

    // 检查是否已存在相同理论收益时间的收益记录（允许1分钟的误差）
    const existingProfit = await InvestmentProfit.findOne({
      where: {
        investment_id: investment.id
      }
    });

    if (existingProfit) {
      // 检查是否有相同理论收益时间的记录
      const existingProfitTime = new Date(existingProfit.profit_time);
      const timeDiff = Math.abs(existingProfitTime.getTime() - theoreticalProfitTime.getTime());

      if (timeDiff < 60000) { // 1分钟内的误差
        logger.warn(`投资ID ${investment.id} 在理论收益时间 ${dateUtils.formatDateTime(theoreticalProfitTime)} 附近已有收益记录，跳过发放`);
        return {
          success: false,
          message: '该投资在此理论收益时间附近已有收益记录',
          investment_id: investment.id,
          existing_profit_id: existingProfit.id
        };
      }
    }

    // 检查是否达到最大收益次数
    if (project.max_profit_times > 0 && investment.profit_count >= project.max_profit_times) {
      try {
        // 如果达到最大收益次数，将投资状态更新为已完成
        investment.status = 'completed';
        await investment.save();

        logger.info(`投资ID ${investment.id} 已达到最大收益次数，状态更新为已完成`);
        return {
          success: false,
          message: '已达到最大收益次数',
          investment_id: investment.id
        };
      } catch (error) {
        logger.error(`更新投资ID ${investment.id} 状态失败:`, error);
        return {
          success: false,
          message: `更新投资状态失败: ${error.message}`,
          investment_id: investment.id
        };
      }
    }

    // 计算收益金额
    const profitAmount = (investment.amount * investment.profit_rate / 100).toFixed(2);

    // 创建收益记录
    let profitRecord;
    try {
      profitRecord = await InvestmentProfit.create({
        investment_id: investment.id,
        user_id: investment.user_id,
        amount: profitAmount,
        profit_time: theoreticalProfitTime, // 理论收益时间（用户应该获得收益的时间）
        distribution_time: distributionTime, // 实际发放时间（系统执行发放操作的时间）
        status: 'pending'
      });
    } catch (error) {
      logger.error(`创建投资ID ${investment.id} 的收益记录失败:`, error);
      return {
        success: false,
        message: `创建收益记录失败: ${error.message}`,
        investment_id: investment.id
      };
    }

    // 将收益添加到用户的收入账户
    let balanceResult;
    try {
      balanceResult = await balanceService.adjustBalance(
        investment.user_id,
        'income', // 收益添加到收入账户
        profitAmount,
        'add',
        'profit', // 交易类型为收益
        `投资收益 ${project.name}`, // 统一描述格式
        investment.id,
        'investment'
      );
    } catch (error) {
      logger.error(`调整投资ID ${investment.id} 用户余额失败:`, error);

      // 如果余额调整失败，将收益记录标记为失败
      try {
        profitRecord.status = 'failed';
        await profitRecord.save();
      } catch (saveError) {
        logger.error(`更新收益记录状态为失败时出错:`, saveError);
      }

      return {
        success: false,
        message: `调整用户余额失败: ${error.message}`,
        investment_id: investment.id
      };
    }

    // 更新收益记录状态和关联的交易ID
    try {
      profitRecord.status = 'paid';
      profitRecord.transaction_id = balanceResult.transactionId;
      await profitRecord.save();
    } catch (error) {
      logger.error(`更新投资ID ${investment.id} 的收益记录状态失败:`, error);
      // 这里不返回失败，因为收益已经发放成功，只是更新状态失败
    }

    // 更新投资记录
    try {
      // 使用理论收益时间作为last_profit_time，而不是实际发放时间
      investment.last_profit_time = theoreticalProfitTime;
      investment.profit_count += 1;
      investment.total_profit = (parseFloat(investment.total_profit || 0) + parseFloat(profitAmount)).toFixed(2);
      await investment.save();
    } catch (error) {
      logger.error(`更新投资ID ${investment.id} 记录失败:`, error);
      // 这里不返回失败，因为收益已经发放成功，只是更新投资记录失败
    }

    // 处理收益返佣
    try {
      await commissionService.processIncomeCommission(
        investment.user_id,
        profitAmount,
        balanceResult.transactionId,
        investment.id
      );
    } catch (error) {
      logger.error(`处理投资ID ${investment.id} 的收益返佣失败:`, error);
      // 返佣失败不影响收益发放，继续执行
    }

    // 安排下一次收益任务
    try {
      await scheduleNextProfitTask(investment);
    } catch (error) {
      logger.error(`安排投资ID ${investment.id} 的下一次收益任务失败:`, error);
    }

    return {
      success: true,
      message: '收益发放成功',
      investment_id: investment.id,
      profit_id: profitRecord.id,
      amount: profitAmount,
      last_profit_time: theoreticalProfitTime // 返回理论收益时间，用于更新临时投资对象
    };
  } catch (error) {
    logger.error(`处理投资收益失败:`, error);
    return {
      success: false,
      message: `处理收益失败: ${error.message}`,
      investment_id: data.investment_id
    };
  }
};

/**
 * 计算理论收益时间
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @param {Date} [specificProfitTime] - 指定的收益时间（用于补偿）
 * @returns {Date} - 理论收益时间
 */
function calculateTheoreticalProfitTime(investment, project, specificProfitTime = null) {
  try {
    // 如果指定了收益时间（用于补偿），直接使用
    if (specificProfitTime) {
      const date = new Date(specificProfitTime);
      logger.info(`投资ID ${investment.id} 使用指定的收益时间: ${dateUtils.formatDateTime(date)}`);
      return date;
    }

    // 如果是首次收益
    if (!investment.last_profit_time) {
      // 确保正确解析日期字符串
      const startTimeStr = investment.start_time || investment.created_at;

      // 记录原始字符串，以便调试
      logger.info(`投资ID ${investment.id} 首次收益，原始开始时间字符串: ${startTimeStr}`);

      // 使用moment正确解析日期，然后转换为Date对象
      const baseTime = moment(startTimeStr, 'YYYY-MM-DD HH:mm:ss').toDate();
      const firstProfitTime = new Date(baseTime.getTime() + project.profit_time * 60 * 60 * 1000);

      logger.info(`投资ID ${investment.id} 首次收益，基于开始时间 ${dateUtils.formatDateTime(baseTime)} 计算理论收益时间: ${dateUtils.formatDateTime(firstProfitTime)}`);
      return firstProfitTime;
    }

    // 如果不是首次收益，基于上次收益时间计算
    // 确保正确解析日期字符串
    const lastProfitTimeStr = investment.last_profit_time;

    // 记录原始字符串，以便调试
    logger.info(`投资ID ${investment.id} 非首次收益，原始上次收益时间字符串: ${lastProfitTimeStr}`);

    // 使用moment正确解析日期，然后转换为Date对象
    const lastProfitTime = moment(lastProfitTimeStr, 'YYYY-MM-DD HH:mm:ss').toDate();
    const nextProfitTime = new Date(lastProfitTime.getTime() + project.profit_time * 60 * 60 * 1000);

    logger.info(`投资ID ${investment.id} 非首次收益，基于上次收益时间 ${dateUtils.formatDateTime(lastProfitTime)} 计算理论收益时间: ${dateUtils.formatDateTime(nextProfitTime)}`);
    return nextProfitTime;
  } catch (error) {
    // 如果计算过程中出现错误，记录错误并返回当前时间作为理论收益时间
    logger.error(`计算投资ID ${investment.id} 的理论收益时间失败: ${error.message}`, error);
    const now = new Date();
    logger.warn(`使用当前时间 ${dateUtils.formatDateTime(now)} 作为理论收益时间`);
    return now;
  }
}

/**
 * 获取队列统计信息
 * @returns {Promise<Object>} - 返回队列统计信息
 */
exports.getQueueStats = async () => {
  try {
    const counts = await profitQueue.getJobCounts();

    return {
      success: true,
      counts,
      queue_name: 'investment-profits'
    };
  } catch (error) {
    logger.error('获取队列统计信息失败:', error);
    return {
      success: false,
      message: '获取队列统计信息失败: ' + error.message
    };
  }
};

/**
 * 手动触发补偿机制
 * 检查是否有错过的收益发放，并创建相应的任务
 * @returns {Promise<Object>} - 返回补偿结果
 */
exports.runCompensationCheck = async () => {
  try {
    logger.info('开始执行收益发放补偿检查...');

    // 获取所有活跃的投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      },
      include: [
        {
          model: Project,
          as: 'project'
        }
      ]
    });

    logger.info(`找到 ${activeInvestments.length} 条活跃投资记录`);

    const results = {
      total: activeInvestments.length,
      compensated: 0,
      skipped: 0,
      failed: 0,
      details: []
    };

    // 获取当前时间 - 直接使用系统时间，不进行时区转换
    const now = moment();

    // 检查每个投资记录
    for (const investment of activeInvestments) {
      try {
        // 获取项目信息
        const project = investment.project;
        if (!project) {
          results.skipped++;
          results.details.push({
            success: false,
            message: '项目不存在',
            investment_id: investment.id
          });
          continue;
        }

        // 检查是否达到最大收益次数
        if (project.max_profit_times > 0 && investment.profit_count >= project.max_profit_times) {
          results.skipped++;
          results.details.push({
            success: false,
            message: '已达到最大收益次数',
            investment_id: investment.id
          });
          continue;
        }

        // 获取上次收益时间或投资开始时间 - 直接使用原始时间，不进行时区转换
        let lastProfitTime;
        if (investment.last_profit_time) {
          lastProfitTime = moment(investment.last_profit_time);
        } else {
          lastProfitTime = moment(investment.start_time || investment.created_at);
        }

        // 计算应该发放的收益次数
        const hoursSinceLastProfit = now.diff(lastProfitTime, 'hours');
        const missedProfitCount = Math.floor(hoursSinceLastProfit / project.profit_time);

        // 对于长周期产品（24小时及以上），即使没有错过收益也进行检查
        const isLongCycleProduct = project.profit_time >= 24;

        // 记录产品类型信息
        logger.info(`投资ID ${investment.id} 是${isLongCycleProduct ? '长' : '短'}周期产品(${project.profit_time}小时)，距离上次收益 ${hoursSinceLastProfit.toFixed(2)} 小时，错过 ${missedProfitCount} 次收益`);

        if (missedProfitCount <= 0) {
          // 没有错过的收益，检查是否已有计划任务
          const existingJobs = await profitQueue.getJobs(['delayed', 'waiting']);
          const hasJob = existingJobs.some(job => job.data.investment_id === investment.id);

          if (!hasJob) {
            // 没有计划任务，创建一个
            const result = await scheduleNextProfitTask(investment);
            results.details.push(result);

            if (result.success) {
              results.compensated++;
            } else {
              results.failed++;
            }
          } else {
            results.skipped++;
            results.details.push({
              success: true,
              message: '已有计划任务',
              investment_id: investment.id,
              product_type: isLongCycleProduct ? 'long-cycle' : 'short-cycle',
              profit_time: project.profit_time
            });
          }
        } else {
          // 有错过的收益，循环补发所有错过的收益
          logger.info(`投资ID ${investment.id} 错过了 ${missedProfitCount} 次收益发放，开始循环补偿`);

          // 获取投资记录在特定时间范围内的所有收益记录
          const existingProfits = await InvestmentProfit.findAll({
            where: {
              investment_id: investment.id,
              profit_time: {
                [Op.between]: [lastProfitTime.toDate(), now.toDate()]
              }
            },
            attributes: ['profit_time'],
            raw: true
          });

          // 将已存在的收益时间点转换为时间戳，便于比较
          const existingProfitTimestamps = existingProfits.map(profit =>
            moment(profit.profit_time).valueOf()
          );

          logger.info(`投资ID ${investment.id} 在时间范围内已有 ${existingProfits.length} 条收益记录`);

          // 允许1分钟的误差，避免因为毫秒级差异导致的重复补发
          const TIME_TOLERANCE_MS = 60000; // 1分钟

          // 检查时间点是否已存在收益记录的函数
          const isTimePointAlreadyProcessed = (targetTime, existingTimes) => {
            const targetTimestamp = moment(targetTime).valueOf();
            return existingTimes.some(timestamp =>
              Math.abs(timestamp - targetTimestamp) < TIME_TOLERANCE_MS
            );
          };

          let successCount = 0;
          let failedCount = 0;
          let skippedCount = 0;
          let currentLastProfitTime = moment(lastProfitTime);

          // 循环处理每一次错过的收益
          for (let i = 0; i < missedProfitCount; i++) {
            // 计算理论上应该发放收益的时间点
            const theoreticalProfitTime = moment(currentLastProfitTime).add(project.profit_time, 'hours').toDate();
            const theoreticalProfitTimestamp = moment(theoreticalProfitTime).valueOf();

            // 检查理论收益时间是否已经过去（当前时间是否已经达到或超过理论收益时间）
            const now = moment();
            if (now < moment(theoreticalProfitTime)) {
              logger.info(`投资ID ${investment.id} 的理论收益时间 ${dateUtils.formatDateTime(theoreticalProfitTime)} 尚未到达，当前时间: ${dateUtils.formatDateTime(now.toDate())}，跳过补发`);
              // 不再继续处理后续的收益，因为它们的理论时间点更晚
              break;
            }

            // 检查这个时间点是否已经有收益记录
            const alreadyProcessed = isTimePointAlreadyProcessed(theoreticalProfitTime, existingProfitTimestamps);

            if (alreadyProcessed) {
              logger.info(`投资ID ${investment.id} 的理论收益时间 ${dateUtils.formatDateTime(theoreticalProfitTime)} 已有收益记录，跳过补发`);
              // 更新当前的最后收益时间，用于计算下一次理论收益时间
              currentLastProfitTime = moment(theoreticalProfitTime);
              skippedCount++;
              continue;
            }

            logger.info(`投资ID ${investment.id} 补发第 ${i+1}/${missedProfitCount} 次收益，理论收益时间: ${dateUtils.formatDateTime(theoreticalProfitTime)}`);

            // 使用理论收益时间进行补发
            let result;
            try {
              const profitUnifiedService = require('./profitUnifiedService');
              result = await profitUnifiedService.processInvestmentProfit(investment, theoreticalProfitTime, true);
            } catch (error) {
              logger.error(`投资ID ${investment.id} 第 ${i+1} 次补发失败:`, error);
              failedCount++;
              continue;
            }

            if (result && result.success) {
              successCount++;
              // 将新补发的收益时间点添加到已存在的时间点列表中，避免在同一次补偿检查中重复补发
              existingProfitTimestamps.push(theoreticalProfitTimestamp);
              // 更新当前的最后收益时间，用于计算下一次理论收益时间
              currentLastProfitTime = moment(theoreticalProfitTime);
              // 重新加载投资记录，确保后续计算使用最新数据
              await investment.reload();
            } else {
              failedCount++;
              logger.error(`投资ID ${investment.id} 第 ${i+1} 次补发失败: ${result.message}`);
              break; // 如果补发失败，停止后续补发
            }
          }

          // 记录补发结果
          const isLongCycleProduct = project.profit_time >= 24;
          results.details.push({
            success: successCount > 0,
            message: `补发了 ${successCount}/${missedProfitCount} 次收益，跳过 ${skippedCount} 次`,
            investment_id: investment.id,
            success_count: successCount,
            failed_count: failedCount,
            skipped_count: skippedCount,
            product_type: isLongCycleProduct ? 'long-cycle' : 'short-cycle',
            profit_time: project.profit_time
          });

          if (successCount > 0) {
            results.compensated++;
          } else {
            results.failed++;
          }

          // 补发完成后，安排下一次收益任务
          if (successCount > 0 || isLongCycleProduct) {
            // 对于长周期产品，即使没有成功补发也尝试安排下一次任务
            await scheduleNextProfitTask(investment);
          }
        }
      } catch (error) {
        logger.error(`投资ID ${investment.id} 补偿检查失败:`, error);
        results.failed++;
        results.details.push({
          success: false,
          message: '补偿检查失败: ' + error.message,
          investment_id: investment.id
        });
      }
    }

    // 计算总共跳过的收益次数
    const totalSkippedProfits = results.details.reduce((total, detail) => total + (detail.skipped_count || 0), 0);
    logger.info(`补偿检查完成: 总计 ${results.total}, 补偿 ${results.compensated}, 跳过 ${results.skipped}, 失败 ${results.failed}, 跳过的收益次数 ${totalSkippedProfits}`);

    return {
      success: true,
      message: '补偿检查完成',
      results
    };
  } catch (error) {
    logger.error('执行补偿检查失败:', error);
    return {
      success: false,
      message: '执行补偿检查失败: ' + error.message
    };
  }
};

// 导出队列对象，便于其他模块使用
exports.profitQueue = profitQueue;
