/**
 * 移动端提现路由
 */
const express = require('express');
const router = express.Router();
const mobileWithdrawalController = require('../controllers/mobileWithdrawalController');
const { verifyUserToken } = require('../middlewares/authMiddleware');

// 使用用户认证中间件
router.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/withdrawals:
 *   post:
 *     summary: 创建提现订单
 *     tags: [移动端提现]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - bank_card_id
 *             properties:
 *               amount:
 *                 type: number
 *                 description: 提现金额
 *               bank_card_id:
 *                 type: integer
 *                 description: 银行卡ID
 *               password:
 *                 type: string
 *                 description: 支付密码
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       500:
 *         description: 服务器错误
 */
router.post('/', mobileWithdrawalController.createWithdrawal);

/**
 * @swagger
 * /api/mobile/withdrawals:
 *   get:
 *     summary: 获取用户提现记录
 *     tags: [移动端提现]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 订单状态
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       500:
 *         description: 服务器错误
 */
router.get('/', mobileWithdrawalController.getUserWithdrawals);

module.exports = router;
