-- 添加收益记录唯一约束，防止重复发放
-- 执行前请先备份数据库

-- 检查是否存在重复记录
SELECT 
    investment_id, 
    profit_time, 
    COUNT(*) as count 
FROM investment_profits 
GROUP BY investment_id, profit_time 
HAVING COUNT(*) > 1;

-- 如果存在重复记录，请先手动处理
-- 然后执行以下约束添加

ALTER TABLE investment_profits 
ADD UNIQUE INDEX uk_investment_profit_time (investment_id, profit_time);

-- 验证约束是否添加成功
SHOW INDEX FROM investment_profits WHERE Key_name = 'uk_investment_profit_time';
