/**
 * 内容安全策略中间件
 * 设置CSP头部，防止XSS攻击和数据注入
 */
const contentSecurityPolicy = (req, res, next) => {
  // 设置CSP头部
  res.setHeader('Content-Security-Policy', `
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com;
    style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://unpkg.com;
    img-src 'self' data: https://cdn.jsdelivr.net https://unpkg.com;
    font-src 'self' https://cdn.jsdelivr.net https://fonts.gstatic.com https://unpkg.com;
    connect-src 'self' https://api.example.com;
    media-src 'self';
    object-src 'none';
    frame-src 'self';
    worker-src 'self' blob:;
    manifest-src 'self';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'self';
    upgrade-insecure-requests;
  `.replace(/\s+/g, ' ').trim());

  next();
};

module.exports = contentSecurityPolicy;
