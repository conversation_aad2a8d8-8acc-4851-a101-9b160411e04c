/**
 * 检查错误日志
 * 用于检查收益系统的错误日志
 */
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 日志文件路径
const logFilePath = path.join(__dirname, 'logs', 'error.log');

// 创建可读流
const fileStream = fs.createReadStream(logFilePath);

// 创建接口
const rl = readline.createInterface({
  input: fileStream,
  crlfDelay: Infinity
});

// 检查日志
console.log(`开始检查错误日志文件: ${logFilePath}`);

// 读取现有日志
let errorCount = 0;
let transactionErrors = 0;

rl.on('line', (line) => {
  // 只输出包含"Cannot read properties of undefined (reading 'transaction')"的行
  if (line.includes('Cannot read properties of undefined (reading \'transaction\')')) {
    transactionErrors++;
    console.log(line);
  }
  
  // 计算总错误数
  if (line.includes('error')) {
    errorCount++;
  }
});

rl.on('close', () => {
  console.log(`总错误数: ${errorCount}`);
  console.log(`事务错误数: ${transactionErrors}`);
  
  if (transactionErrors === 0) {
    console.log('没有发现事务错误，修复成功！');
  } else {
    console.log('仍然存在事务错误，修复失败！');
  }
});
