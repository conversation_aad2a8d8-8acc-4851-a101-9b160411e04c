/**
 * 清理重复的收益记录
 * 只保留每个投资每天的最新一条记录
 */
const mysql = require('mysql2/promise');
require('dotenv').config();

async function cleanDuplicateProfits() {
  console.log('开始清理重复的收益记录...');
  
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'MySQL3352~!',
    database: process.env.DB_NAME || 'fox_db'
  });
  
  try {
    // 查找重复的收益记录
    console.log('查找重复的收益记录...');
    const [duplicateGroups] = await connection.query(`
      SELECT 
        investment_id, 
        DATE(profit_time) as profit_date, 
        COUNT(*) as count
      FROM 
        investment_profits
      GROUP BY 
        investment_id, DATE(profit_time)
      HAVING 
        COUNT(*) > 1
      ORDER BY 
        investment_id, profit_date
    `);
    
    console.log(`找到 ${duplicateGroups.length} 组重复收益记录`);
    
    let totalDeleted = 0;
    
    // 处理每组重复记录
    for (const group of duplicateGroups) {
      // 查询这组收益记录
      const [profits] = await connection.query(
        'SELECT id, profit_time FROM investment_profits WHERE investment_id = ? AND DATE(profit_time) = ? ORDER BY profit_time DESC',
        [group.investment_id, group.profit_date]
      );
      
      // 保留最新的一条记录，删除其他记录
      const keepId = profits[0].id;
      const deleteIds = profits.slice(1).map(p => p.id);
      
      if (deleteIds.length > 0) {
        console.log(`投资ID ${group.investment_id} 在 ${group.profit_date} 保留收益ID ${keepId}，删除 ${deleteIds.length} 条记录`);
        
        // 删除重复记录
        const [deleteResult] = await connection.query(
          'DELETE FROM investment_profits WHERE id IN (?)',
          [deleteIds]
        );
        
        totalDeleted += deleteResult.affectedRows;
      }
    }
    
    console.log(`清理完成，共删除 ${totalDeleted} 条重复记录`);
    return { deleted: totalDeleted };
  } catch (error) {
    console.error('清理失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

// 执行清理
cleanDuplicateProfits()
  .then(result => {
    console.log('清理结果:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('清理过程出错:', error);
    process.exit(1);
  });
