{"version": 3, "file": "compareNodes.js", "sourceRoot": "", "sources": ["../../../src/rules/prefer-optional-chain-utils/compareNodes.ts"], "names": [], "mappings": ";;;AACA,oDAA0D;AAC1D,kEAA8D;AAW9D,SAAS,aAAa,CACpB,MAAiB,EACjB,MAAiB;IAEjB,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;QACpC,oDAAoC;IACtC,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACvC,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,OAAO,GAAG,KAAK,GAAG,CAAC;QACrB,CAAC;QACD,OAAO,oBAAoB,CAAC,GAAG,EAAE,GAAG,CAAC,6CAA+B,CAAC;IACvE,CAAC,CAAC,CAAC;IACH,IAAI,MAAM,EAAE,CAAC;QACX,gDAAkC;IACpC,CAAC;IACD,oDAAoC;AACtC,CAAC;AAED,SAAS,WAAW,CAAC,CAAU;IAC7B,OAAO,CACL,OAAO,CAAC,KAAK,QAAQ;QACrB,CAAC,IAAI,IAAI;QACT,MAAM,IAAI,CAAC;QACX,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAC3B,CAAC;AACJ,CAAC;AACD,SAAS,mCAAmC,CAC1C,IAAmB;IAEnB,OAAO,CACL,CAAC,CACC,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QACrD,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAC5B;QACD,CAAC,CACC,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,cAAc;YACnD,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAC5B;QACD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAC7C,CAAC;AACJ,CAAC;AACD,SAAS,oBAAoB,CAC3B,MAAe,EACf,MAAe;IAEf,2FAA2F;IAC3F,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACrC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,oDAAoC;QACtC,CAAC;QACD,gDAAkC;IACpC,CAAC;IAED,2FAA2F;IAC3F,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC;QACjD,oDAAoC;IACtC,CAAC;IAED,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACtC,CAAC;AACD,SAAS,iBAAiB,CACxB,KAAoB,EACpB,KAAoB;IAEpB,MAAM,kBAAkB,GAAG,0BAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnD,2FAA2F;IAC3F,IAAI,kBAAkB,IAAI,IAAI,EAAE,CAAC;QAC/B,wGAAwG;QACxG,oDAAoC;IACtC,CAAC;IAED,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACpC,8DAA8D;QAC9D,gDAAkC;IACpC,CAAC;IAED,KAAK,MAAM,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACrC,kDAAkD;QAClD,MAAM,oBAAoB,GAAG,KAAK,CAAC,GAAG,CAAY,CAAC;QACnD,kDAAkD;QAClD,MAAM,oBAAoB,GAAG,KAAK,CAAC,GAAG,CAAY,CAAC;QAEnD,IAAI,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,oBAAiC,CAAC;YACjD,MAAM,MAAM,GAAG,oBAAiC,CAAC;YAEjD,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC7C,IAAI,MAAM,6CAA+B,EAAE,CAAC;gBAC1C,oDAAoC;YACtC,CAAC;YACD,qDAAqD;QACvD,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,GAAG,oBAAoB,CACjC,oBAAoB,EACpB,oBAAoB,CACrB,CAAC;YACF,IAAI,MAAM,6CAA+B,EAAE,CAAC;gBAC1C,oDAAoC;YACtC,CAAC;YACD,qDAAqD;QACvD,CAAC;IACH,CAAC;IAED,gDAAkC;AACpC,CAAC;AAED,SAAS,oBAAoB,CAC3B,KAAoB,EACpB,KAAoB;IAEpB,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;QAC9B,wDAAwD;QAExD,wEAAwE;QACxE,iDAAiD;QACjD,EAAE;QACF,gBAAgB;QAChB,yCAAyC;QACzC,iCAAiC;QACjC,EAAE;QACF,oCAAoC;QACpC,oBAAoB;QACpB,0DAA0D;QAC1D,IAAI,mCAAmC,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,mCAAmC,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC;QAED,4EAA4E;QAC5E,oDAAoD;QACpD,EAAE;QACF,gBAAgB;QAChB,2BAA2B;QAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;YACtD,OAAO,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;YACtD,OAAO,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC;QAED,4EAA4E;QAC5E,8DAA8D;QAC9D,EAAE;QACF,WAAW;QACX,sBAAsB;QACtB,WAAW;QACX,sBAAsB;QACtB,eAAe;QACf,0BAA0B;QAC1B,eAAe;QACf,0BAA0B;QAC1B,+BAA+B;QAC/B,0CAA0C;QAC1C,IACE,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;YAC5C,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;YACxC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;YAC9C,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAC1C,CAAC;YACD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;wBAC7D,oEAAoE;wBACpE,gGAAgG;wBAChG,oDAAoC;oBACtC,CAAC;oBACD,IACE,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,iDAAiC,EAClE,CAAC;wBACD,kDAAmC;oBACrC,CAAC;oBACD,oDAAoC;gBAEtC,KAAK,sBAAc,CAAC,cAAc;oBAChC,IACE,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,iDAAiC,EAClE,CAAC;wBACD,kDAAmC;oBACrC,CAAC;oBACD,oDAAoC;gBAEtC;oBACE,oDAAoC;YACxC,CAAC;QACH,CAAC;QAED,oDAAoC;IACtC,CAAC;IAED,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,gGAAgG;QAChG,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,uBAAuB,CAAC;QAC5C,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,gBAAgB;YAClC,oDAAoC;QAEtC,2GAA2G;QAC3G,KAAK,sBAAc,CAAC,oBAAoB;YACtC,oDAAoC;QAEtC,KAAK,sBAAc,CAAC,cAAc,CAAC,CAAC,CAAC;YACnC,MAAM,SAAS,GAAG,KAAqB,CAAC;YAExC,uBAAuB;YACvB,sBAAsB;YACtB,cAAc;YACd,4BAA4B;YAC5B,oDAAoD;YACpD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YACzD,IAAI,UAAU,iDAAiC,EAAE,CAAC;gBAChD,kDAAmC;YACrC,CAAC;YAED,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YACnE,IAAI,aAAa,6CAA+B,EAAE,CAAC;gBACjD,oDAAoC;YACtC,CAAC;YAED,oEAAoE;YACpE,iCAAiC;YACjC,KAAK;YACL,iCAAiC;YACjC,mCAAmC;YAEnC,MAAM,eAAe,GAAG,aAAa,CACnC,KAAK,CAAC,SAAS,EACf,SAAS,CAAC,SAAS,CACpB,CAAC;YACF,IAAI,eAAe,6CAA+B,EAAE,CAAC;gBACnD,oDAAoC;YACtC,CAAC;YAED,MAAM,gBAAgB,GAAG,YAAY,CACnC,KAAK,CAAC,aAAa,EACnB,SAAS,CAAC,aAAa,CACxB,CAAC;YACF,IAAI,gBAAgB,6CAA+B,EAAE,CAAC;gBACpD,gDAAkC;YACpC,CAAC;YAED,oDAAoC;QACtC,CAAC;QAED,KAAK,sBAAc,CAAC,eAAe;YACjC,gFAAgF;YAChF,OAAO,YAAY,CAAC,KAAK,EAAG,KAAsB,CAAC,UAAU,CAAC,CAAC;QAEjE,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,iBAAiB;YACnC,IAAI,KAAK,CAAC,IAAI,KAAM,KAAsB,CAAC,IAAI,EAAE,CAAC;gBAChD,gDAAkC;YACpC,CAAC;YACD,oDAAoC;QAEtC,KAAK,sBAAc,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5B,MAAM,YAAY,GAAG,KAAqB,CAAC;YAC3C,IACE,KAAK,CAAC,GAAG,KAAK,YAAY,CAAC,GAAG;gBAC9B,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK,EAClC,CAAC;gBACD,gDAAkC;YACpC,CAAC;YACD,oDAAoC;QACtC,CAAC;QAED,KAAK,sBAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACrC,MAAM,WAAW,GAAG,KAAqB,CAAC;YAE1C,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBACnE,oEAAoE;gBACpE,gGAAgG;gBAChG,oDAAoC;YACtC,CAAC;YAED,uBAAuB;YACvB,yBAAyB;YACzB,gBAAgB;YAChB,+BAA+B;YAC/B,mBAAmB;YACnB,EAAE;YACF,6BAA6B;YAC7B,gBAAgB;YAChB,mCAAmC;YACnC,oBAAoB;YACpB,EAAE;YACF,mDAAmD;YACnD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,UAAU,iDAAiC,EAAE,CAAC;gBAChD,kDAAmC;YACrC,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC5C,oDAAoC;YACtC,CAAC;YAED,oEAAoE;YACpE,2BAA2B;YAC3B,KAAK;YACL,2BAA2B;YAC3B,mCAAmC;YAEnC,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YACrE,IAAI,aAAa,6CAA+B,EAAE,CAAC;gBACjD,oDAAoC;YACtC,CAAC;YAED,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,KAAK,sBAAc,CAAC,qBAAqB,CAAC;QAC1C,KAAK,sBAAc,CAAC,eAAe,CAAC,CAAC,CAAC;YACpC,MAAM,aAAa,GAAG,KAAqB,CAAC;YAC5C,MAAM,cAAc,GAClB,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,CAAC,MAAM;gBACnD,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;oBAC9B,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACtC,OAAO,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;gBAC/C,CAAC,CAAC,CAAC;YACL,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,oDAAoC;YACtC,CAAC;YAED,gDAAkC;QACpC,CAAC;QAED,KAAK,sBAAc,CAAC,eAAe,CAAC,CAAC,CAAC;YACpC,MAAM,YAAY,GAAG,KAAqB,CAAC;YAC3C,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACrD,gDAAkC;YACpC,CAAC;YACD,oDAAoC;QACtC,CAAC;QAED,2CAA2C;QAC3C,0JAA0J;QAC1J,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,aAAa;YAC/B,0BAA0B;YAC1B,oDAAoC;QAEtC,sHAAsH;QACtH,KAAK,sBAAc,CAAC,gBAAgB;YAClC,oDAAoC;QAEtC,yIAAyI;QACzI,KAAK,sBAAc,CAAC,eAAe;YACjC,oDAAoC;QAEtC,yEAAyE;QACzE,0EAA0E;QAC1E,sBAAsB;QACtB,EAAE;QACF,0EAA0E;QAC1E,2DAA2D;QAC3D;YACE,OAAO,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AACD,MAAM,mBAAmB,GAAG,IAAI,OAAO,EAGpC,CAAC;AACJ;;GAEG;AACH,SAAgB,YAAY,CAC1B,KAA2B,EAC3B,KAA2B;IAE3B,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;YACpB,oDAAoC;QACtC,CAAC;QACD,gDAAkC;IACpC,CAAC;IAED,MAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1D,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,MAAM,GAAG,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAClD,IAAI,IAAI,GAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;QACrB,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IACD,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACxB,OAAO,MAAM,CAAC;AAChB,CAAC;AAxBD,oCAwBC"}