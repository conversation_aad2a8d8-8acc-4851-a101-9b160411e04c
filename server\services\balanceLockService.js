/**
 * 余额锁定服务
 * 使用Redis实现临时锁定用户余额，防止并发操作导致的问题
 */
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');

// 锁定时间（秒）
const LOCK_EXPIRE_SECONDS = 30;

// 锁定键前缀
const LOCK_KEY_PREFIX = 'balance:lock:';

/**
 * 锁定用户余额
 * @param {number} userId - 用户ID
 * @param {number} amount - 锁定金额
 * @param {string} [reason='purchase'] - 锁定原因
 * @returns {Promise<{success: boolean, lockId: string|null, message: string}>} - 返回锁定结果
 */
exports.lockBalance = async (userId, amount, reason = 'purchase') => {
  try {
    if (!userId || !amount) {
      return { success: false, lockId: null, message: '用户ID和金额不能为空' };
    }

    // 生成锁ID
    const lockId = `${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
    const lockKey = `${LOCK_KEY_PREFIX}${userId}`;

    // 检查是否已有锁
    const existingLock = await redisClient.get(lockKey);
    if (existingLock) {
      return { 
        success: false, 
        lockId: null, 
        message: '用户余额已被锁定，请稍后再试',
        existingLock: JSON.parse(existingLock)
      };
    }

    // 创建锁
    const lockData = {
      userId,
      amount,
      reason,
      lockId,
      createdAt: new Date().toISOString()
    };

    // 设置锁，带过期时间
    await redisClient.set(lockKey, lockData, LOCK_EXPIRE_SECONDS);

    logger.debug(`用户 ${userId} 的余额已锁定 ${amount}，锁ID: ${lockId}`);

    return { success: true, lockId, message: '余额锁定成功' };
  } catch (error) {
    logger.error(`锁定用户 ${userId} 余额失败:`, error);
    return { success: false, lockId: null, message: '锁定余额失败: ' + error.message };
  }
};

/**
 * 解锁用户余额
 * @param {number} userId - 用户ID
 * @param {string} lockId - 锁ID
 * @returns {Promise<{success: boolean, message: string}>} - 返回解锁结果
 */
exports.unlockBalance = async (userId, lockId) => {
  try {
    if (!userId || !lockId) {
      return { success: false, message: '用户ID和锁ID不能为空' };
    }

    const lockKey = `${LOCK_KEY_PREFIX}${userId}`;

    // 检查锁是否存在
    const existingLock = await redisClient.get(lockKey);
    if (!existingLock) {
      return { success: true, message: '锁已不存在' };
    }

    // 验证锁ID
    const lockData = JSON.parse(existingLock);
    if (lockData.lockId !== lockId) {
      return { success: false, message: '锁ID不匹配，无法解锁' };
    }

    // 删除锁
    await redisClient.del(lockKey);

    logger.debug(`用户 ${userId} 的余额锁定已解除，锁ID: ${lockId}`);

    return { success: true, message: '余额解锁成功' };
  } catch (error) {
    logger.error(`解锁用户 ${userId} 余额失败:`, error);
    return { success: false, message: '解锁余额失败: ' + error.message };
  }
};

/**
 * 检查用户余额是否被锁定
 * @param {number} userId - 用户ID
 * @returns {Promise<{locked: boolean, lockData: Object|null}>} - 返回锁定状态
 */
exports.checkLock = async (userId) => {
  try {
    if (!userId) {
      return { locked: false, lockData: null };
    }

    const lockKey = `${LOCK_KEY_PREFIX}${userId}`;
    const existingLock = await redisClient.get(lockKey);

    if (existingLock) {
      return { locked: true, lockData: JSON.parse(existingLock) };
    }

    return { locked: false, lockData: null };
  } catch (error) {
    logger.error(`检查用户 ${userId} 余额锁定状态失败:`, error);
    return { locked: false, lockData: null };
  }
};
