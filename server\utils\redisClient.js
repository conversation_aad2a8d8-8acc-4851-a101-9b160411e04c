/**
 * Redis客户端工具
 * 提供Redis连接和常用操作
 */
const Redis = require('ioredis');
const redisConfig = require('../config/redis');
const logger = require('./logger');

// 创建Redis客户端实例
const redisClient = new Redis(redisConfig);

// 监听连接事件
redisClient.on('connect', () => {
  logger.info('Redis连接成功');
});

redisClient.on('error', (err) => {
  logger.error('Redis连接错误:', err);
});

redisClient.on('close', () => {
  logger.warn('Redis连接关闭');
});

// Redis键名
const PROFIT_TASKS_KEY = 'investment:profit:tasks';

/**
 * 设置键值对
 * @param {string} key - 键
 * @param {string|object} value - 值
 * @param {number} [expireSeconds] - 过期时间（秒）
 * @returns {Promise<string>} - 返回操作结果
 */
const set = async (key, value, expireSeconds) => {
  try {
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : value;

    if (expireSeconds) {
      return await redisClient.set(key, stringValue, 'EX', expireSeconds);
    } else {
      return await redisClient.set(key, stringValue);
    }
  } catch (error) {
    logger.error(`Redis设置键值对失败: ${key}`, error);
    throw error;
  }
};

/**
 * 获取键值
 * @param {string} key - 键
 * @returns {Promise<string|object|null>} - 返回值
 */
const get = async (key) => {
  try {
    const value = await redisClient.get(key);

    if (!value) return null;

    // 尝试解析JSON
    try {
      return JSON.parse(value);
    } catch (e) {
      return value;
    }
  } catch (error) {
    logger.error(`Redis获取键值失败: ${key}`, error);
    throw error;
  }
};

/**
 * 删除键
 * @param {string} key - 键
 * @returns {Promise<number>} - 返回删除的键数量
 */
const del = async (key) => {
  try {
    return await redisClient.del(key);
  } catch (error) {
    logger.error(`Redis删除键失败: ${key}`, error);
    throw error;
  }
};

/**
 * 检查键是否存在
 * @param {string} key - 键
 * @returns {Promise<boolean>} - 返回是否存在
 */
const exists = async (key) => {
  try {
    const result = await redisClient.exists(key);
    return result === 1;
  } catch (error) {
    logger.error(`Redis检查键是否存在失败: ${key}`, error);
    throw error;
  }
};

/**
 * 设置键的过期时间
 * @param {string} key - 键
 * @param {number} seconds - 过期时间（秒）
 * @returns {Promise<number>} - 返回操作结果
 */
const expire = async (key, seconds) => {
  try {
    return await redisClient.expire(key, seconds);
  } catch (error) {
    logger.error(`Redis设置过期时间失败: ${key}`, error);
    throw error;
  }
};

/**
 * 获取键的剩余过期时间
 * @param {string} key - 键
 * @returns {Promise<number>} - 返回剩余时间（秒）
 */
const ttl = async (key) => {
  try {
    return await redisClient.ttl(key);
  } catch (error) {
    logger.error(`Redis获取过期时间失败: ${key}`, error);
    throw error;
  }
};

/**
 * 添加任务到收益队列
 * @param {number} investmentId - 投资ID
 * @param {Date} nextProfitTime - 下一次收益时间
 * @returns {Promise<Object>} - 结果
 */
const addProfitTask = async (investmentId, nextProfitTime) => {
  try {
    // 确保使用正确的时间戳
    const timestamp = new Date(nextProfitTime).getTime();
    const member = `investment:${investmentId}`;

    // 记录当前时间和计划时间，便于调试
    const now = new Date();
    const localTime = new Date(timestamp);
    logger.debug(`添加收益任务，当前时间: ${now.toLocaleString()}, 计划时间: ${localTime.toLocaleString()}, 时间戳: ${timestamp}`);

    await redisClient.zadd(PROFIT_TASKS_KEY, timestamp, member);

    logger.debug(`投资ID ${investmentId} 的收益任务已添加到队列，计划时间: ${localTime.toLocaleString()}`);

    return {
      success: true,
      investment_id: investmentId,
      next_profit_time: localTime
    };
  } catch (error) {
    logger.error(`添加投资ID ${investmentId} 的收益任务失败:`, error);
    throw error;
  }
};

/**
 * 获取到期的收益任务
 * @param {number} [endTime=Date.now()] - 结束时间戳
 * @returns {Promise<Array<number>>} - 投资ID数组
 */
const getDueProfitTasks = async (endTime = Date.now()) => {
  try {
    // 记录当前时间，便于调试
    const now = new Date();
    logger.debug(`获取到期任务，当前时间: ${now.toLocaleString()}, 时间戳: ${now.getTime()}, 参数时间戳: ${endTime}`);

    const tasks = await redisClient.zrangebyscore(PROFIT_TASKS_KEY, 0, endTime);

    if (tasks.length > 0) {
      logger.info(`发现 ${tasks.length} 个到期的收益任务，当前时间: ${now.toLocaleString()}`);
    }

    // 解析任务ID
    const investmentIds = tasks.map(task => {
      const match = task.match(/investment:(\d+)/);
      return match ? parseInt(match[1]) : null;
    }).filter(id => id !== null);

    return investmentIds;
  } catch (error) {
    logger.error('获取到期的收益任务失败:', error);
    throw error;
  }
};

/**
 * 移除收益任务
 * @param {number} investmentId - 投资ID
 * @returns {Promise<Object>} - 结果
 */
const removeProfitTask = async (investmentId) => {
  try {
    const member = `investment:${investmentId}`;

    await redisClient.zrem(PROFIT_TASKS_KEY, member);

    logger.debug(`投资ID ${investmentId} 的收益任务已从队列中移除`);

    return {
      success: true,
      investment_id: investmentId
    };
  } catch (error) {
    logger.error(`移除投资ID ${investmentId} 的收益任务失败:`, error);
    throw error;
  }
};

/**
 * 获取所有收益任务
 * @returns {Promise<Array<Object>>} - 任务数组
 */
const getAllProfitTasks = async () => {
  try {
    const tasks = await redisClient.zrange(PROFIT_TASKS_KEY, 0, -1, 'WITHSCORES');

    // 解析结果
    const result = [];
    for (let i = 0; i < tasks.length; i += 2) {
      const task = tasks[i];
      const timestamp = parseInt(tasks[i + 1]);

      const match = task.match(/investment:(\d+)/);
      if (match) {
        result.push({
          investment_id: parseInt(match[1]),
          next_profit_time: new Date(timestamp)
        });
      }
    }

    return result;
  } catch (error) {
    logger.error('获取所有收益任务失败:', error);
    throw error;
  }
};

module.exports = {
  client: redisClient,
  set,
  get,
  del,
  exists,
  expire,
  ttl,
  addProfitTask,
  getDueProfitTasks,
  removeProfitTask,
  getAllProfitTasks
};
