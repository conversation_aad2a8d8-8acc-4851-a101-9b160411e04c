/**
 * 安全HTTP头部中间件
 * 设置各种安全相关的HTTP头部
 */
const securityHeadersMiddleware = (req, res, next) => {
  // 防止点击劫持
  res.setHeader('X-Frame-Options', 'DENY');
  
  // 启用XSS保护
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // 防止MIME类型嗅探
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // 限制引用来源
  res.setHeader('Referrer-Policy', 'same-origin');
  
  // 设置严格传输安全（仅在生产环境启用）
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  
  // 设置特性策略
  res.setHeader('Feature-Policy', `
    geolocation 'none';
    microphone 'none';
    camera 'none';
    payment 'none';
    usb 'none';
    accelerometer 'none';
    gyroscope 'none';
    magnetometer 'none';
  `.replace(/\s+/g, ' ').trim());
  
  // 设置权限策略
  res.setHeader('Permissions-Policy', `
    geolocation=(),
    microphone=(),
    camera=(),
    payment=(),
    usb=(),
    accelerometer=(),
    gyroscope=(),
    magnetometer=()
  `.replace(/\s+/g, ' ').trim());
  
  next();
};

module.exports = securityHeadersMiddleware;
