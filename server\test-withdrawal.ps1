# Login to get token
$loginBody = @{
    username = "user"
    password = "xt147258"
} | ConvertTo-<PERSON>son

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/user/login" -Method Post -Body $loginBody -ContentType "application/json"
    $token = ($loginResponse.Content | ConvertFrom-Json).data.token
    
    Write-Host "Login successful, got token: $token"
    
    # Use token to test withdrawal
    $headers = @{
        Authorization = "Bearer $token"
    }
    
    $withdrawalBody = @{
        amount = 100
        bank_card_id = 2
    } | ConvertTo-Json
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/mobile/withdrawals" -Method Post -Headers $headers -Body $withdrawalBody -ContentType "application/json" -ErrorAction Stop
        Write-Host "Response status code: $($response.StatusCode)"
        Write-Host "Response content: $($response.Content)"
    } catch {
        Write-Host "Error status code: $($_.Exception.Response.StatusCode.value__)"
        
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $reader.BaseStream.Position = 0
            $reader.DiscardBufferedData()
            $responseBody = $reader.ReadToEnd()
            Write-Host "Error response: $responseBody"
            
            # Parse JSON response to display message
            $errorJson = $responseBody | ConvertFrom-Json
            Write-Host "Error message: $($errorJson.message)"
        } catch {
            Write-Host "Unable to read error response: $_"
        }
    }
}
catch {
    Write-Host "Login failed: $_"
}
