/**
 * 交易记录相关 API 服务
 */
import { get } from '../../utils/request';

/**
 * 获取用户交易记录
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=10] - 每页数量
 * @param {string|Array} [params.type] - 交易类型，可选值："all"(全部)、"deposit"(存款)、"withdrawal"(取款)、"investment"(购买)、"profit"(收益)、"commission"(佣金)、"bonus"(系统赠送)、"investment_gift"(系统赠送)、"deduction"(扣除)，也可以是类型数组
 * @param {string} [params.start_date] - 开始日期，格式：YYYY-MM-DD
 * @param {string} [params.end_date] - 结束日期，格式：YYYY-MM-DD
 * @returns {Promise<Object>} 交易记录数据
 */
export function getTransactions(params = {}) {
  return get('/account/transactions', {
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      ...params
    }
  });
}
