/*
 * FOX应用头部导航栏样式
 * 此文件定义了移动端通用的头部导航栏样式
 * 确保所有页面在视觉上保持一致
 */

@import './variables.scss';

/* 通用顶部导航栏 */
.fox-header {
  background-color: #0a0e1a;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  /* 固定在顶部 */
  position: relative; /* 使用相对定位，让标题可以绝对定位居中 */
  top: 0;
  z-index: 100;
  /* 确保在iOS设备上正确显示 */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  /* 确保高度足够 */
  min-height: 100rpx;
}

.fox-header-title {
  color: #FF8C00;
  font-size: 36rpx;
  font-weight: normal;
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  /* 确保标题不会与左右两侧的按钮重叠 */
  padding: 0 60rpx;
  /* 确保文本溢出时显示省略号 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  z-index: 1; /* 确保标题在按钮下面 */
}

.fox-header-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2; /* 确保按钮在标题之上，可以正常点击 */
}

.fox-header-placeholder {
  width: 60rpx;
  height: 60rpx;
  position: relative;
  z-index: 2; /* 确保占位元素在标题之上 */
}
