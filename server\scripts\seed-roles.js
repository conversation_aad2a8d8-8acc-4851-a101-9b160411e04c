require('dotenv').config();
const sequelize = require('../config/database');
const { Role, Permission, RolePermission, Admin, AdminRole } = require('../models');
const bcrypt = require('bcryptjs');

async function seedRoles() {
  try {
    // 清空角色表
    await sequelize.query('DELETE FROM `roles`');
    console.log('角色表已清空');

    // 清空角色权限关联表
    await sequelize.query('DELETE FROM `role_permissions`');
    console.log('角色权限关联表已清空');

    // 清空管理员角色关联表
    await sequelize.query('DELETE FROM `admin_roles`');
    console.log('管理员角色关联表已清空');

    // 创建超级管理员角色
    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
    await sequelize.query(`INSERT INTO roles (id, name, description, parent_id, status, created_at, updated_at) VALUES (1, '超级管理员组', '拥有所有权限的超级管理员角色', 0, 1, '${now}', '${now}')`);

    // 创建管理员角色
    await sequelize.query(`INSERT INTO roles (id, name, description, parent_id, status, created_at, updated_at) VALUES (2, '管理员组', '普通管理员角色', 1, 1, '${now}', '${now}')`);

    console.log('角色创建成功');

    // 获取所有权限
    const [permissions] = await sequelize.query('SELECT * FROM permissions');

    // 为超级管理员角色分配所有权限
    for (const permission of permissions) {
      await sequelize.query(`INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at) VALUES (1, ${permission.id}, '${now}', '${now}')`);
    }

    // 为管理员角色分配除了角色管理和管理员设置之外的所有权限
    for (const permission of permissions) {
      if (!['roles', 'admins'].includes(permission.module)) {
        await sequelize.query(`INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at) VALUES (2, ${permission.id}, '${now}', '${now}')`);
      }
    }

    console.log('角色权限分配成功');

    // 创建超级管理员账号
    const adminPassword = await bcrypt.hash('admin123', 10);

    const [superAdmins] = await sequelize.query("SELECT * FROM admins WHERE username = 'admin'");
    const superAdmin = superAdmins[0];

    if (!superAdmin) {
      const [result] = await sequelize.query(`INSERT INTO admins (username, password, name, role, status, created_at, updated_at) VALUES ('admin', '${adminPassword}', '超级管理员', 'super', 'active', '${now}', '${now}')`);

      const adminId = result.insertId;

      // 关联超级管理员角色
      await sequelize.query(`INSERT INTO admin_roles (admin_id, role_id, created_at, updated_at) VALUES (${adminId}, 1, '${now}', '${now}')`);

      console.log('超级管理员账号创建成功');
    } else {
      // 关联超级管理员角色
      await sequelize.query(`INSERT INTO admin_roles (admin_id, role_id, created_at, updated_at) VALUES (${superAdmin.id}, 1, '${now}', '${now}')`);

      console.log('超级管理员账号已存在，已关联角色');
    }

    process.exit(0);
  } catch (error) {
    console.error('初始化角色数据失败:', error);
    process.exit(1);
  }
}

seedRoles();
