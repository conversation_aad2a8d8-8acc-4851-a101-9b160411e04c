const { PaymentChannel } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 获取支付通道列表
exports.getPaymentChannels = async (req, res) => {
  try {
    const { keyword, status } = req.query;

    // 构建查询条件
    const where = {};

    if (keyword) {
      where[Op.or] = [
        { name: { [Op.like]: `%${keyword}%` } },
        { code: { [Op.like]: `%${keyword}%` } }
      ];
    }

    if (status !== undefined) {
      where.status = status === 'true';
    }

    // 查询支付通道
    const paymentChannels = await PaymentChannel.findAll({
      where,
      order: [
        ['weight', 'DESC'],
        ['id', 'ASC']
      ]
    });

    // 转换字段名称为驼峰命名
    const formattedChannels = paymentChannels.map(channel => ({
      id: channel.id,
      name: channel.name,
      code: channel.code,
      countryCode: channel.country_code,
      depositEnabled: channel.deposit_enabled ? 1 : 0,
      withdrawEnabled: channel.withdraw_enabled ? 1 : 0,
      isDefault: channel.is_default ? 1 : 0,
      weight: channel.weight,
      // 余额字段已移除
      config: channel.config,
      createTime: channel.created_at,
      updateTime: channel.updated_at
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: formattedChannels.length,
        items: formattedChannels
      }
    });
  } catch (error) {
    console.error('获取支付通道列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取支付通道详情
exports.getPaymentChannel = async (req, res) => {
  try {
    const { id } = req.params;

    // 查询支付通道
    const paymentChannel = await PaymentChannel.findByPk(id);

    if (!paymentChannel) {
      return res.status(404).json({
        code: 404,
        message: '支付通道不存在',
        data: null
      });
    }

    // 转换字段名称为驼峰命名
    const formattedChannel = {
      id: paymentChannel.id,
      name: paymentChannel.name,
      code: paymentChannel.code,
      countryCode: paymentChannel.country_code,
      depositEnabled: paymentChannel.deposit_enabled ? 1 : 0,
      withdrawEnabled: paymentChannel.withdraw_enabled ? 1 : 0,
      isDefault: paymentChannel.is_default ? 1 : 0,
      weight: paymentChannel.weight,
      // 余额字段已移除
      config: paymentChannel.config,
      createTime: paymentChannel.created_at,
      updateTime: paymentChannel.updated_at
    };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: formattedChannel
    });
  } catch (error) {
    console.error('获取支付通道详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 创建支付通道
exports.createPaymentChannel = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { name, code, countryCode, depositEnabled, withdrawEnabled, isDefault, weight, config } = req.body;

    // 验证请求数据
    if (!name || !code) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '名称和代码不能为空',
        data: null
      });
    }

    // 验证代码格式
    if (!/^[a-zA-Z0-9_-]+$/.test(code)) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '通道代码只能包含字母、数字、下划线和连字符',
        data: null
      });
    }

    // 验证权重
    if (weight !== undefined && (isNaN(parseInt(weight)) || parseInt(weight) < 0)) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '权重必须是非负整数',
        data: null
      });
    }

    // 检查代码是否已存在
    const existingChannel = await PaymentChannel.findOne({
      where: { code }
    });

    if (existingChannel) {
      await transaction.rollback();
      return res.status(409).json({
        code: 409,
        message: '支付通道代码已存在',
        data: null
      });
    }

    // 处理默认通道逻辑
    let shouldBeDefault = isDefault === 1;

    // 检查是否有其他默认通道
    const defaultChannelsCount = await PaymentChannel.count({
      where: { is_default: true }
    });

    // 如果没有默认通道，则强制设置为默认通道
    if (defaultChannelsCount === 0) {
      shouldBeDefault = true;
    }

    // 如果设置为默认通道，则将其他通道设置为非默认
    if (shouldBeDefault) {
      await PaymentChannel.update(
        { is_default: false },
        { where: {}, transaction }
      );
    }

    // 创建支付通道
    const paymentChannel = await PaymentChannel.create({
      name,
      code,
      country_code: countryCode || 'CN',
      deposit_enabled: depositEnabled === 1,
      withdraw_enabled: withdrawEnabled === 1,
      is_default: shouldBeDefault, // 使用处理后的默认通道状态
      weight: weight || 0,
      // 余额字段已移除
      config: config || null
    }, { transaction });

    await transaction.commit();

    // 转换字段名称为驼峰命名
    const formattedChannel = {
      id: paymentChannel.id,
      name: paymentChannel.name,
      code: paymentChannel.code,
      countryCode: paymentChannel.country_code,
      depositEnabled: paymentChannel.deposit_enabled ? 1 : 0,
      withdrawEnabled: paymentChannel.withdraw_enabled ? 1 : 0,
      isDefault: paymentChannel.is_default ? 1 : 0,
      weight: paymentChannel.weight,
      // 余额字段已移除
      config: paymentChannel.config,
      createTime: paymentChannel.created_at,
      updateTime: paymentChannel.updated_at
    };

    return res.status(200).json({
      code: 200,
      message: '创建成功',
      data: formattedChannel
    });
  } catch (error) {
    await transaction.rollback();
    console.error('创建支付通道错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新支付通道
exports.updatePaymentChannel = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const { name, code, countryCode, depositEnabled, withdrawEnabled, isDefault, weight, config } = req.body;

    // 基本数据验证
    if (!name || !code) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '名称和代码不能为空',
        data: null
      });
    }

    // 验证代码格式
    if (!/^[a-zA-Z0-9_-]+$/.test(code)) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '通道代码只能包含字母、数字、下划线和连字符',
        data: null
      });
    }

    // 验证权重
    if (weight !== undefined && (isNaN(parseInt(weight)) || parseInt(weight) < 0)) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '权重必须是非负整数',
        data: null
      });
    }

    // 查询支付通道
    const paymentChannel = await PaymentChannel.findByPk(id);

    if (!paymentChannel) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '支付通道不存在',
        data: null
      });
    }

    // 如果更改了代码，检查是否已存在
    if (code !== paymentChannel.code) {
      const existingChannel = await PaymentChannel.findOne({
        where: { code }
      });

      if (existingChannel) {
        await transaction.rollback();
        return res.status(409).json({
          code: 409,
          message: '支付通道代码已存在',
          data: null
        });
      }
    }

    // 处理默认通道逻辑
    if (isDefault !== undefined) {
      if (isDefault === 1) {
        // 如果设置为默认通道，则将其他通道设置为非默认
        await PaymentChannel.update(
          { is_default: false },
          { where: { id: { [Op.ne]: id } }, transaction }
        );
      } else if (paymentChannel.is_default) {
        // 如果尝试取消默认通道，检查是否有其他默认通道
        const defaultChannelsCount = await PaymentChannel.count({
          where: {
            is_default: true,
            id: { [Op.ne]: id }
          }
        });

        // 如果没有其他默认通道，不允许取消当前默认通道
        if (defaultChannelsCount === 0) {
          await transaction.rollback();
          return res.status(400).json({
            code: 400,
            message: '必须保留至少一个默认通道，请先设置其他通道为默认',
            data: null
          });
        }
      }
    }

    // 更新支付通道
    await paymentChannel.update({
      name: name || paymentChannel.name,
      code: code || paymentChannel.code,
      country_code: countryCode !== undefined ? countryCode : paymentChannel.country_code,
      deposit_enabled: depositEnabled !== undefined ? depositEnabled === 1 : paymentChannel.deposit_enabled,
      withdraw_enabled: withdrawEnabled !== undefined ? withdrawEnabled === 1 : paymentChannel.withdraw_enabled,
      is_default: isDefault !== undefined ? isDefault === 1 : paymentChannel.is_default,
      weight: weight !== undefined ? weight : paymentChannel.weight,
      // 余额字段已移除
      config: config !== undefined ? config : paymentChannel.config
    }, { transaction });

    await transaction.commit();

    // 获取更新后的支付通道
    const updatedChannel = await PaymentChannel.findByPk(id);

    // 转换字段名称为驼峰命名
    const formattedChannel = {
      id: updatedChannel.id,
      name: updatedChannel.name,
      code: updatedChannel.code,
      countryCode: updatedChannel.country_code,
      depositEnabled: updatedChannel.deposit_enabled ? 1 : 0,
      withdrawEnabled: updatedChannel.withdraw_enabled ? 1 : 0,
      isDefault: updatedChannel.is_default ? 1 : 0,
      weight: updatedChannel.weight,
      // 余额字段已移除
      config: updatedChannel.config,
      createTime: updatedChannel.created_at,
      updateTime: updatedChannel.updated_at
    };

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: formattedChannel
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新支付通道错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除支付通道
exports.deletePaymentChannel = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    // 查询支付通道
    const paymentChannel = await PaymentChannel.findByPk(id);

    if (!paymentChannel) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '支付通道不存在',
        data: null
      });
    }

    // 删除支付通道
    await paymentChannel.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('删除支付通道错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新支付通道状态
exports.updatePaymentChannelStatus = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const { field, value } = req.body;

    // 验证请求数据
    if (!field || value === undefined) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '字段名和值不能为空',
        data: null
      });
    }

    // 查询支付通道
    const paymentChannel = await PaymentChannel.findByPk(id);

    if (!paymentChannel) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '支付通道不存在',
        data: null
      });
    }

    // 根据字段名更新状态
    const updateData = {};

    switch (field) {
      case 'depositEnabled':
        updateData.deposit_enabled = value === 1;
        break;
      case 'withdrawEnabled':
        updateData.withdraw_enabled = value === 1;
        break;
      case 'isDefault':
        // 如果设置为默认通道，则将其他通道设置为非默认
        if (value === 1) {
          await PaymentChannel.update(
            { is_default: false },
            { where: { id: { [Op.ne]: id } }, transaction }
          );
          updateData.is_default = true;
        } else {
          // 检查是否有其他默认通道
          const defaultChannelsCount = await PaymentChannel.count({
            where: {
              is_default: true,
              id: { [Op.ne]: id }
            }
          });

          // 如果没有其他默认通道，不允许取消当前默认通道
          if (defaultChannelsCount === 0) {
            await transaction.rollback();
            return res.status(400).json({
              code: 400,
              message: '必须保留至少一个默认通道，请先设置其他通道为默认',
              data: null
            });
          }

          updateData.is_default = false;
        }
        break;
      case 'weight':
        // 验证权重值
        if (isNaN(parseInt(value))) {
          await transaction.rollback();
          return res.status(400).json({
            code: 400,
            message: '权重值必须是数字',
            data: null
          });
        }
        updateData.weight = parseInt(value);
        break;
      default:
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '无效的字段名',
          data: null
        });
    }

    // 更新支付通道
    await paymentChannel.update(updateData, { transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新支付通道状态错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 批量更新支付通道权重
exports.updatePaymentChannelWeights = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const updates = req.body;

    if (!Array.isArray(updates) || updates.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '请提供有效的权重更新数据',
        data: null
      });
    }

    // 批量更新权重
    const updatePromises = updates.map(async (item) => {
      const { id, weight } = item;

      if (!id || weight === undefined || isNaN(parseInt(weight))) {
        return null;
      }

      const paymentChannel = await PaymentChannel.findByPk(id);

      if (!paymentChannel) {
        return null;
      }

      return paymentChannel.update({
        weight: parseInt(weight)
      }, { transaction });
    });

    await Promise.all(updatePromises.filter(Boolean));

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '批量更新权重成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('批量更新支付通道权重错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
