const express = require('express');
const userController = require('../controllers/userController');
const { verifyAdminToken, verifyUserToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 管理员路由
const adminRouter = express.Router();

// 所有路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: 获取用户列表
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, blocked]
 *         description: 状态筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', userController.getUsers);

/**
 * @swagger
 * /api/admin/users/{id}:
 *   get:
 *     summary: 获取用户详情
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 用户不存在
 */
adminRouter.get('/:id', userController.getUser);

/**
 * @swagger
 * /api/admin/users:
 *   post:
 *     summary: 创建用户
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *               - name
 *               - phone
 *             properties:
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               id_card:
 *                 type: string
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 *       409:
 *         description: 用户名、手机号或邮箱已存在
 */
adminRouter.post('/', userController.createUser);

/**
 * @swagger
 * /api/admin/users/{id}:
 *   put:
 *     summary: 更新用户
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               id_card:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, inactive, blocked]
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 用户不存在
 *       409:
 *         description: 手机号或邮箱已存在
 */
adminRouter.put('/:id', userController.updateUser);

/**
 * @swagger
 * /api/admin/users/{id}/password:
 *   put:
 *     summary: 重置用户密码
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: 密码重置成功
 *       404:
 *         description: 用户不存在
 */
adminRouter.put('/:id/password', userController.resetPassword);

/**
 * @swagger
 * /api/admin/users/{id}/balance:
 *   put:
 *     summary: 调整用户余额
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - type
 *             properties:
 *               amount:
 *                 type: number
 *                 description: 金额
 *               type:
 *                 type: string
 *                 enum: [add, subtract]
 *                 description: 操作类型
 *               account_type:
 *                 type: string
 *                 enum: [income, deposit]
 *                 default: deposit
 *                 description: 账户类型
 *               currency:
 *                 type: string
 *                 enum: [CNY, USDT]
 *                 default: CNY
 *                 description: 货币类型
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 余额调整成功
 *       404:
 *         description: 用户不存在
 */
adminRouter.put('/:id/balance', checkAdminRole(['super', 'admin']), userController.adjustBalance);

/**
 * @swagger
 * /api/admin/users/{id}/investments:
 *   get:
 *     summary: 获取用户投资列表
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, paused, completed]
 *         description: 状态筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 用户不存在
 */
adminRouter.get('/:id/investments', userController.getUserInvestments);

/**
 * @swagger
 * /api/admin/users/{id}/transactions:
 *   get:
 *     summary: 获取用户交易列表
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [deposit, withdrawal, investment, profit, commission, bonus, deduction]
 *         description: 交易类型
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 用户不存在
 */
adminRouter.get('/:id/transactions', userController.getUserTransactions);

/**
 * @swagger
 * /api/admin/users/{id}/subordinates:
 *   get:
 *     summary: 获取用户下级会员
 *     tags: [用户管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: level
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: 下级级别
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 用户不存在
 */
adminRouter.get('/:id/subordinates', userController.getUserSubordinates);

// 移动端路由
const mobileRouter = express.Router();

// 所有路由都需要用户认证
mobileRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/user/profile:
 *   put:
 *     summary: 更新用户个人信息
 *     tags: [用户]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               avatar:
 *                 type: string
 *     responses:
 *       200:
 *         description: 更新成功
 *       409:
 *         description: 邮箱已存在
 */
mobileRouter.put('/profile', userController.updateProfile);

module.exports = {
  admin: adminRouter,
  mobile: mobileRouter
};
