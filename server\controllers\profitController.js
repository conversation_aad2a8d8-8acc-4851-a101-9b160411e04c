/**
 * 收益控制器
 * 提供收益相关的API接口
 */
const profitSystem = require('../services/profitSystem');
const profitCompensator = require('../services/profitCompensator');
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');

/**
 * 启动收益系统
 */
exports.startSystem = async (req, res) => {
  try {
    const result = await profitSystem.startProfitSystem();
    
    return res.json(result);
  } catch (error) {
    logger.error('启动收益系统失败:', error);
    return res.status(500).json({
      success: false,
      message: '启动收益系统失败: ' + error.message
    });
  }
};

/**
 * 停止收益系统
 */
exports.stopSystem = (req, res) => {
  try {
    const result = profitSystem.stopProfitSystem();
    
    return res.json(result);
  } catch (error) {
    logger.error('停止收益系统失败:', error);
    return res.status(500).json({
      success: false,
      message: '停止收益系统失败: ' + error.message
    });
  }
};

/**
 * 执行补偿检查
 */
exports.runCompensation = async (req, res) => {
  try {
    const result = await profitCompensator.runCompensationCheck();
    
    return res.json(result);
  } catch (error) {
    logger.error('执行补偿检查失败:', error);
    return res.status(500).json({
      success: false,
      message: '执行补偿检查失败: ' + error.message
    });
  }
};

/**
 * 获取所有收益任务
 */
exports.getAllTasks = async (req, res) => {
  try {
    const tasks = await redisClient.getAllProfitTasks();
    
    return res.json({
      success: true,
      count: tasks.length,
      tasks
    });
  } catch (error) {
    logger.error('获取所有收益任务失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取所有收益任务失败: ' + error.message
    });
  }
};

/**
 * 重新初始化所有收益任务
 */
exports.reinitializeTasks = async (req, res) => {
  try {
    const result = await profitSystem.initializeAllInvestmentTasks();
    
    return res.json(result);
  } catch (error) {
    logger.error('重新初始化所有收益任务失败:', error);
    return res.status(500).json({
      success: false,
      message: '重新初始化所有收益任务失败: ' + error.message
    });
  }
};
