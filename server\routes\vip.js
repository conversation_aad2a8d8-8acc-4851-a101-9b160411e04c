const express = require('express');
const router = express.Router();
const vipController = require('../controllers/vipController');
const { verifyAdminToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 所有路由都需要验证token和管理员权限
router.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/vip/levels:
 *   get:
 *     summary: 获取VIP等级列表
 *     tags: [VIP管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/levels', vipController.getVipLevels);

/**
 * @swagger
 * /api/admin/vip/types:
 *   get:
 *     summary: 获取VIP类型列表
 *     tags: [VIP管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/types', vipController.getVipTypes);

/**
 * @swagger
 * /api/admin/vip/levels/{id}:
 *   get:
 *     summary: 获取VIP等级详情
 *     tags: [VIP管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: VIP等级ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: VIP等级不存在
 */
router.get('/levels/:id', vipController.getVipLevel);

/**
 * @swagger
 * /api/admin/vip/levels:
 *   post:
 *     summary: 创建VIP等级
 *     tags: [VIP管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               level:
 *                 type: integer
 *               upgradeUsers:
 *                 type: integer
 *               upgradeAmount:
 *                 type: number
 *               returnRate:
 *                 type: number
 *               upgradeBonus:
 *                 type: number
 *               imageId:
 *                 type: integer
 *               content:
 *                 type: string
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       409:
 *         description: 级别已存在
 */
router.post('/levels', checkAdminRole(['super']), vipController.createVipLevel);

/**
 * @swagger
 * /api/admin/vip/levels/{id}:
 *   put:
 *     summary: 更新VIP等级
 *     tags: [VIP管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: VIP等级ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               level:
 *                 type: integer
 *               upgradeUsers:
 *                 type: integer
 *               upgradeAmount:
 *                 type: number
 *               returnRate:
 *                 type: number
 *               upgradeBonus:
 *                 type: number
 *               imageId:
 *                 type: integer
 *               content:
 *                 type: string
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: VIP等级不存在
 *       409:
 *         description: 级别已存在
 */
router.put('/levels/:id', checkAdminRole(['super']), vipController.updateVipLevel);

/**
 * @swagger
 * /api/admin/vip/levels/{id}:
 *   delete:
 *     summary: 删除VIP等级
 *     tags: [VIP管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: VIP等级ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 无法删除
 *       404:
 *         description: VIP等级不存在
 */
router.delete('/levels/:id', checkAdminRole(['super']), vipController.deleteVipLevel);

module.exports = router;
