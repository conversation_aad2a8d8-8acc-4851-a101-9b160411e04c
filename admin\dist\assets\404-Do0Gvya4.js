/* empty css             *//* empty css                  */import{d as r,c as a,b as o,e as u,w as l,m as p,n as d,u as _,g as i,_ as m}from"./index-LncY9lAB.js";const c={class:"not-found"},f={class:"not-found-content"},x=r({__name:"404",setup(B){const e=_(),s=()=>{e.push("/")};return(b,t)=>{const n=p;return i(),a("div",c,[o("div",f,[t[1]||(t[1]=o("h1",null,"404",-1)),t[2]||(t[2]=o("h2",null,"页面未找到",-1)),t[3]||(t[3]=o("p",null,"抱歉，访问的页面不存在或已被删除。",-1)),u(n,{type:"primary",onClick:s},{default:l(()=>t[0]||(t[0]=[d("返回首页")])),_:1})])])}}}),N=m(x,[["__scopeId","data-v-08fbe7e0"]]);export{N as default};
