/**
 * 定时任务调度器
 * 管理系统中的所有定时任务
 */
const cron = require('node-cron');
const profitService = require('../services/profitService');
const statisticsService = require('../services/statisticsService');
const timezoneUtils = require('../utils/timezoneUtils');
const kbPayOrderChecker = require('./kbPayOrderChecker');

// 定时任务列表
const schedulers = {};

/**
 * 初始化定时任务
 */
exports.initSchedulers = async () => {
  try {
    console.log('正在初始化定时任务...');

    // 不再使用时区设置，直接使用系统默认时区
    console.log('使用系统默认时区，不进行时区转换');

    // 使用默认时区选项
    const cronOptions = {};

    // 初始化任务队列
    try {
      const queueService = require('../services/queueService');
      await queueService.initQueues();
      console.log('任务队列初始化成功');

      // 服务器启动时自动执行补偿检查
      console.log('服务器启动，开始执行收益发放补偿检查...');
      try {
        const results = await queueService.runCompensationCheck();
        console.log('服务器启动时收益发放补偿检查执行完成:', results);
      } catch (compensationError) {
        console.error('服务器启动时收益发放补偿检查执行失败:', compensationError);
      }
    } catch (error) {
      console.error('任务队列初始化失败:', error);
    }

    // 每天凌晨3点执行补偿检查
    schedulers.compensationCheck = cron.schedule('0 3 * * *', async () => {
      console.log('开始执行收益发放补偿检查...');
      try {
        const queueService = require('../services/queueService');
        const results = await queueService.runCompensationCheck();
        console.log('收益发放补偿检查执行完成:', results);
      } catch (error) {
        console.error('收益发放补偿检查执行失败:', error);
      }
    }, cronOptions);

    // 每小时检查一次长周期产品（24小时及以上）
    schedulers.longCycleCheck = cron.schedule('0 * * * *', async () => {
      console.log('开始检查长周期产品收益...');
      try {
        const results = await profitService.checkLongCycleProductProfits();
        console.log('长周期产品收益检查完成:', results);
      } catch (error) {
        console.error('长周期产品收益检查失败:', error);
      }
    }, cronOptions);

    // 每5分钟检查一次KB支付未完成订单
    schedulers.kbPayOrderCheck = cron.schedule('*/5 * * * *', async () => {
      console.log('开始检查KB支付未完成订单...');
      try {
        const results = await kbPayOrderChecker.checkKBPayOrders();
        console.log('KB支付未完成订单检查完成:', results);
      } catch (error) {
        console.error('KB支付未完成订单检查失败:', error);
      }
    }, cronOptions);

    // 每天凌晨1点更新前一天的统计数据
    schedulers.dailyStatisticsUpdate = cron.schedule('0 1 * * *', async () => {
      console.log('开始更新前一天的统计数据...');
      try {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const formattedDate = yesterday.toISOString().split('T')[0];

        const results = await statisticsService.updateDailyStatistics(formattedDate);
        console.log(`${formattedDate} 的统计数据更新完成`);
      } catch (error) {
        console.error('统计数据更新失败:', error);
      }
    }, cronOptions);

    console.log('定时任务初始化完成');

    return {
      success: true,
      message: '定时任务初始化成功',
      schedulers: Object.keys(schedulers)
    };
  } catch (error) {
    console.error('初始化定时任务失败:', error);
    return {
      success: false,
      message: '初始化定时任务失败: ' + error.message
    };
  }
};

/**
 * 停止所有定时任务
 */
exports.stopAllSchedulers = () => {
  try {
    console.log('正在停止所有定时任务...');

    // 停止所有定时任务
    Object.values(schedulers).forEach(scheduler => {
      if (scheduler && typeof scheduler.stop === 'function') {
        scheduler.stop();
      }
    });

    console.log('所有定时任务已停止');

    return {
      success: true,
      message: '所有定时任务已停止'
    };
  } catch (error) {
    console.error('停止定时任务失败:', error);
    return {
      success: false,
      message: '停止定时任务失败: ' + error.message
    };
  }
};

// 手动触发收益发放的方法已被移除，因为它们可能导致重复收益问题

/**
 * 手动触发收益发放补偿检查
 */
exports.triggerCompensationCheck = async () => {
  try {
    console.log('手动触发收益发放补偿检查...');
    const profitCompensator = require('../services/profitCompensator');
    const results = await profitCompensator.runCompensationCheck();
    console.log('收益发放补偿检查执行完成:', results);
    return {
      success: true,
      message: '收益发放补偿检查执行成功',
      results
    };
  } catch (error) {
    console.error('手动触发收益发放补偿检查失败:', error);
    return {
      success: false,
      message: '手动触发收益发放补偿检查失败: ' + error.message
    };
  }
};

/**
 * 手动触发深度补偿检查
 */
exports.triggerDeepCompensationCheck = async () => {
  try {
    console.log('手动触发深度补偿检查...');
    const profitCompensator = require('../services/profitCompensator');
    const results = await profitCompensator.runDeepCompensationCheck();
    console.log('深度补偿检查执行完成:', results);
    return {
      success: true,
      message: '深度补偿检查执行成功',
      results
    };
  } catch (error) {
    console.error('手动触发深度补偿检查失败:', error);
    return {
      success: false,
      message: '手动触发深度补偿检查失败: ' + error.message
    };
  }
};

/**
 * 手动触发长周期产品收益检查
 */
exports.triggerLongCycleCheck = async () => {
  try {
    console.log('手动触发长周期产品收益检查...');
    const results = await profitService.checkLongCycleProductProfits();
    console.log('长周期产品收益检查执行完成:', results);
    return {
      success: true,
      message: '长周期产品收益检查执行成功',
      results
    };
  } catch (error) {
    console.error('手动触发长周期产品收益检查失败:', error);
    return {
      success: false,
      message: '手动触发长周期产品收益检查失败: ' + error.message
    };
  }
};

/**
 * 获取任务队列统计信息
 */
exports.getQueueStats = async () => {
  try {
    console.log('获取任务队列统计信息...');
    const queueService = require('../services/queueService');
    const results = await queueService.getQueueStats();
    console.log('获取任务队列统计信息完成:', results);
    return {
      success: true,
      message: '获取任务队列统计信息成功',
      results
    };
  } catch (error) {
    console.error('获取任务队列统计信息失败:', error);
    return {
      success: false,
      message: '获取任务队列统计信息失败: ' + error.message
    };
  }
};

/**
 * 手动触发KB支付订单检查
 */
exports.triggerKBPayOrderCheck = async () => {
  try {
    console.log('手动触发KB支付订单检查...');
    const results = await kbPayOrderChecker.checkKBPayOrders();
    console.log('KB支付订单检查执行完成:', results);
    return {
      success: true,
      message: 'KB支付订单检查执行成功',
      results
    };
  } catch (error) {
    console.error('手动触发KB支付订单检查失败:', error);
    return {
      success: false,
      message: '手动触发KB支付订单检查失败: ' + error.message
    };
  }
};

/**
 * 手动更新今日统计数据
 */
exports.updateTodayStatistics = async () => {
  try {
    console.log('手动更新今日统计数据...');
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];

    const results = await statisticsService.updateDailyStatistics(formattedDate);
    console.log(`${formattedDate} 的统计数据更新完成`);

    return {
      success: true,
      message: `今日(${formattedDate})统计数据更新成功`
    };
  } catch (error) {
    console.error('手动更新今日统计数据失败:', error);
    return {
      success: false,
      message: '手动更新今日统计数据失败: ' + error.message
    };
  }
};
