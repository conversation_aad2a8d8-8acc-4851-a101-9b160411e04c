@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

$slider-height: () !default;
$slider-height: map.merge(
  (
    'large': 40px,
    'default': 32px,
    'small': 24px,
  ),
  $slider-height
);

@include b(slider) {
  @include set-component-css-var('slider', $slider);
}

@include b(slider) {
  width: 100%;
  height: map.get($slider-height, 'default');
  display: flex;
  align-items: center;

  @include e(runway) {
    flex: 1;
    height: getCssVar('slider-height');
    background-color: getCssVar('slider-runway-bg-color');
    border-radius: getCssVar('slider-border-radius');
    position: relative;
    cursor: pointer;

    &.show-input {
      margin-right: 30px;
      width: auto;
    }

    &.is-disabled {
      cursor: default;

      .#{$namespace}-slider__bar {
        background-color: getCssVar('slider-disabled-color');
      }

      .#{$namespace}-slider__button {
        border-color: getCssVar('slider-disabled-color');
      }

      .#{$namespace}-slider__button-wrapper {
        &:hover,
        &.hover {
          cursor: not-allowed;
        }

        &.dragging {
          cursor: not-allowed;
        }
      }

      .#{$namespace}-slider__button {
        &:hover,
        &.hover,
        &.dragging {
          transform: scale(1);
        }

        &:hover,
        &.hover {
          cursor: not-allowed;
        }

        &.dragging {
          cursor: not-allowed;
        }
      }
    }
  }

  @include e(input) {
    flex-shrink: 0;
    width: 130px;
  }

  @include e(bar) {
    height: getCssVar('slider-height');
    background-color: getCssVar('slider-main-bg-color');
    border-top-left-radius: getCssVar('slider-border-radius');
    border-bottom-left-radius: getCssVar('slider-border-radius');
    position: absolute;
  }

  @include e(button-wrapper) {
    height: getCssVar('slider-button-wrapper-size');
    width: getCssVar('slider-button-wrapper-size');
    position: absolute;
    z-index: 1;
    top: getCssVar('slider-button-wrapper-offset');
    transform: translateX(-50%);
    background-color: transparent;
    text-align: center;
    user-select: none;
    line-height: normal;
    outline: none;
    @include utils-vertical-center;

    &:hover,
    &.hover {
      cursor: grab;
    }

    &.dragging {
      cursor: grabbing;
    }
  }

  @include e(button) {
    display: inline-block;
    width: getCssVar('slider-button-size');
    height: getCssVar('slider-button-size');
    vertical-align: middle;
    border: solid 2px getCssVar('slider-main-bg-color');
    background-color: getCssVar('color-white');
    border-radius: 50%;
    box-sizing: border-box;
    transition: getCssVar('transition-duration-fast');
    user-select: none;

    &:hover,
    &.hover,
    &.dragging {
      transform: scale(1.2);
    }

    &:hover,
    &.hover {
      cursor: grab;
    }

    &.dragging {
      cursor: grabbing;
    }
  }

  @include e(stop) {
    position: absolute;
    height: getCssVar('slider-height');
    width: getCssVar('slider-height');
    border-radius: getCssVar('border-radius-circle');
    background-color: getCssVar('slider-stop-bg-color');
    transform: translateX(-50%);
  }

  @include e(marks) {
    top: 0;
    left: 12px;
    width: 18px;
    height: 100%;

    @include e(marks-text) {
      position: absolute;
      transform: translateX(-50%);
      font-size: 14px;
      color: getCssVar('color-info');
      margin-top: 15px;
      white-space: pre;
    }
  }

  @include when(vertical) {
    position: relative;
    display: inline-flex;
    width: auto;
    height: 100%;
    flex: 0;

    .#{$namespace}-slider__runway {
      width: getCssVar('slider-height');
      height: 100%;
      margin: 0 16px;
    }
    .#{$namespace}-slider__bar {
      width: getCssVar('slider-height');
      height: auto;
      border-radius: 0 0 3px 3px;
    }
    .#{$namespace}-slider__button-wrapper {
      top: auto;
      left: getCssVar('slider-button-wrapper-offset');
      transform: translateY(50%);
    }
    .#{$namespace}-slider__stop {
      transform: translateY(50%);
    }

    @include e(marks-text) {
      margin-top: 0;
      left: 15px;
      transform: translateY(50%);
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      height: map.get($slider-height, $size);
      @include e(runway) {
      }
    }
  }
}
