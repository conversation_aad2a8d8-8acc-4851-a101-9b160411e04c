/**
 * 取款订单控制器
 * 处理取款订单的获取、审核、完成等操作
 */
const { Withdrawal, User, BankCard, Transaction, PaymentChannel } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const balanceService = require('../services/balanceService');
const { getKBPayService } = require('../services/kbPayService');
const { getBasePayService } = require('../services/basePayService');

// 管理员端 - 获取取款订单列表
exports.getWithdrawals = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, user_id, user_id_str, start_date, end_date, keyword } = req.query;

    // 构建查询条件
    const where = {};

    // 构建用户关联查询条件
    const userWhere = {};
    let userRequired = false;

    // 根据状态筛选
    if (status) {
      where.status = status;
    }

    // 根据用户ID筛选
    if (user_id) {
      where.user_id = user_id;
    }

    // 根据用户ID字符串（user_id字段）筛选
    if (user_id_str) {
      userWhere.user_id = user_id_str;
      userRequired = true;
    }

    // 根据日期范围筛选
    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // 关键词搜索
    if (keyword) {
      // 尝试将关键词转换为数字，用于匹配ID
      const keywordAsNumber = !isNaN(keyword) ? Number(keyword) : null;

      where[Op.or] = [
        { order_number: { [Op.like]: `%${keyword}%` } },
        ...(keywordAsNumber ? [{ id: keywordAsNumber }] : [])
      ];

      // 如果需要搜索用户名，添加用户关联条件
      userWhere[Op.or] = [
        { username: { [Op.like]: `%${keyword}%` } },
        { name: { [Op.like]: `%${keyword}%` } },
        { user_id: { [Op.like]: `%${keyword}%` } }
      ];
      userRequired = true;
    }

    // 构建查询选项
    const options = {
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email'],
          where: userWhere,
          required: userRequired
        },
        {
          model: BankCard,
          as: 'bank_card',
          attributes: ['id', 'bank_name', 'card_number', 'card_holder', 'bank_id']
        }
      ],
      order: [['created_at', 'DESC']],
      offset: (page - 1) * limit,
      limit: parseInt(limit)
    };

    const { count, rows } = await Withdrawal.findAndCountAll(options);

    // 批量获取支付通道信息和银行映射信息
    const { PaymentChannel, BankChannelMapping } = require('../models');

    // 获取所有需要的支付通道ID
    const channelIds = [...new Set(rows.map(row => row.payment_channel_id).filter(id => id))];
    const channelMap = new Map();

    if (channelIds.length > 0) {
      const channels = await PaymentChannel.findAll({
        where: { id: { [Op.in]: channelIds } },
        attributes: ['id', 'name']
      });
      channels.forEach(channel => {
        channelMap.set(channel.id, channel.name);
      });
    }

    // 获取所有需要的银行映射信息
    const bankMappingKeys = new Set();
    rows.forEach(row => {
      if (row.bank_card?.bank_id && row.payment_channel_id) {
        bankMappingKeys.add(`${row.bank_card.bank_id}-${row.payment_channel_id}`);
      }
    });

    const bankMappingMap = new Map();
    if (bankMappingKeys.size > 0) {
      // 构建批量查询条件 - 去重后的条件
      const uniqueConditions = Array.from(bankMappingKeys).map(key => {
        const [bank_id, payment_channel_id] = key.split('-');
        return {
          bank_id: parseInt(bank_id),
          payment_channel_id: parseInt(payment_channel_id),
          status: true
        };
      });

      const mappings = await BankChannelMapping.findAll({
        where: {
          [Op.or]: uniqueConditions
        },
        attributes: ['bank_id', 'payment_channel_id', 'payout_method']
      });

      mappings.forEach(mapping => {
        const key = `${mapping.bank_id}-${mapping.payment_channel_id}`;
        bankMappingMap.set(key, mapping.payout_method);
      });
    }

    // 处理返回数据，填充真实数据
    const processedItems = rows.map((item) => {
      const withdrawal = item.toJSON();

      // 获取支付通道信息
      const paymentChannelName = withdrawal.payment_channel_id ?
        (channelMap.get(withdrawal.payment_channel_id) || '') : '';

      // 处理银行编码 - 直接使用payout_method字段
      let bankCode = '-';
      if (withdrawal.bank_card?.bank_id && withdrawal.payment_channel_id) {
        const mappingKey = `${withdrawal.bank_card.bank_id}-${withdrawal.payment_channel_id}`;
        const payoutMethod = bankMappingMap.get(mappingKey);
        if (payoutMethod) {
          bankCode = payoutMethod.toString();
        }
      }

      // 处理支付状态
      let paymentStatus = '';
      if (withdrawal.status === 'completed') {
        paymentStatus = '已支付';
      } else if (withdrawal.status === 'rejected') {
        paymentStatus = '支付失败';
      } else if (withdrawal.status === 'processing') {
        paymentStatus = '处理中';
      } else {
        // pending 状态是未支付
        paymentStatus = '未支付';
      }

      // 处理支付时间
      let paymentTime = '';
      if (withdrawal.completion_time) {
        paymentTime = withdrawal.completion_time;
      } else if (withdrawal.approval_time) {
        paymentTime = withdrawal.approval_time;
      }

      // 处理支付平台订单号
      let paymentOrderNumber = '';
      if (withdrawal.payment_platform_order_no) {
        paymentOrderNumber = withdrawal.payment_platform_order_no;
      } else if (withdrawal.payment_channel_order_id) {
        paymentOrderNumber = withdrawal.payment_channel_order_id;
      }

      // 处理回调状态
      let callbackStatus = '';
      if (withdrawal.callback_status === 'mock_callback') {
        callbackStatus = '模拟回调';
      } else if (withdrawal.callback_status === 'channel_callback') {
        callbackStatus = '通道回调';
      } else {
        callbackStatus = '未回调';
      }

      // 处理取款状态
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'rejected': '已退回',
        'failed': '失败'
      };
      const withdrawalStatus = statusMap[withdrawal.status] || withdrawal.status;

      return {
        ...withdrawal,
        bank_code: bankCode,
        payment_channel: withdrawal.callback_status === 'mock_callback' ? '-' : paymentChannelName,
        payment_status: paymentStatus,
        payment_time: paymentTime,
        payment_order_number: paymentOrderNumber,
        callback_status: callbackStatus,
        withdrawal_status: withdrawalStatus
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: processedItems
      }
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取取款订单详情
exports.getWithdrawal = async (req, res) => {
  try {
    const { id } = req.params;

    const withdrawal = await Withdrawal.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email']
        },
        {
          model: BankCard,
          as: 'bank_card',
          attributes: ['id', 'bank_name', 'card_number', 'card_holder']
        },
        {
          model: Transaction,
          as: 'transaction',
          attributes: ['id', 'type', 'amount', 'status', 'created_at']
        }
      ]
    });

    if (!withdrawal) {
      return res.status(404).json({
        code: 404,
        message: '取款订单不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: withdrawal
    });
  } catch (error) {
    console.error('获取取款订单详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 审核取款订单
exports.approveWithdrawal = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, remark, payment_channel_id } = req.body;

    // 验证请求数据
    if (!status) {
      return res.status(400).json({
        code: 400,
        message: '状态不能为空',
        data: null
      });
    }

    if (!['completed', 'rejected'].includes(status)) {
      return res.status(400).json({
        code: 400,
        message: '状态只能是completed或rejected',
        data: null
      });
    }

    // 查找取款订单
    const withdrawal = await Withdrawal.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user'
        },
        {
          model: BankCard,
          as: 'bank_card'
        }
      ]
    });

    if (!withdrawal) {
      return res.status(404).json({
        code: 404,
        message: '取款订单不存在',
        data: null
      });
    }

    // 检查订单状态
    if (withdrawal.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: '订单状态不正确',
        data: null
      });
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 如果状态为已拒绝，更新状态并退回金额
      if (status === 'rejected') {
        console.log('=== 开始处理取款拒绝 ===');
        console.log('取款订单信息:', {
          id: withdrawal.id,
          user_id: withdrawal.user_id,
          amount: withdrawal.amount,
          status: withdrawal.status
        });

        withdrawal.status = status;
        withdrawal.remark = remark || withdrawal.remark;
        withdrawal.completion_time = new Date();

        try {
          console.log('=== 调用余额服务退回金额 ===');
          // 使用余额服务将金额退回到用户收入账户
          const result = await balanceService.adjustBalance(
            withdrawal.user_id,
            'income', // 收入账户
            withdrawal.amount, // 正数表示增加
            'add',
            'increase', // 交易类型为增加（取款拒绝退回）
            `提现退回 ${withdrawal.amount}`,
            withdrawal.id,
            'withdrawal_rejected',
            transaction
          );

          console.log('余额调整结果:', result);

          // 更新取款订单关联的退款交易ID
          withdrawal.refund_transaction_id = result.transactionId;

          await withdrawal.save({ transaction });
          await transaction.commit();

          console.log('=== 取款拒绝处理完成 ===');

          return res.status(200).json({
            code: 200,
            message: '取款已拒绝，金额已退回',
            data: withdrawal
          });
        } catch (balanceError) {
          console.error('=== 余额调整失败 ===');
          console.error('余额调整错误:', balanceError);
          console.error('错误堆栈:', balanceError.stack);
          throw balanceError;
        }
      }

      // 如果状态为已完成，则处理取款
      if (status === 'completed') {

        // 检查是否提供了支付通道ID
        let channelId = payment_channel_id;

        // 如果没有提供支付通道ID，查询默认支付通道
        if (!channelId) {

          const defaultChannel = await PaymentChannel.findOne({
            where: { is_default: true }
          });
          if (!defaultChannel) {
            await transaction.rollback();
            return res.status(400).json({
              code: 400,
              message: '未找到默认支付通道，请联系管理员配置',
              data: null
            });
          }

          channelId = defaultChannel.id;
        }

        // 获取银行卡信息
        if (!withdrawal.bank_card) {
          await transaction.rollback();
          return res.status(400).json({
            code: 400,
            message: '未找到关联的银行卡信息',
            data: null
          });
        }

        // 用户在提交提现请求时已经扣减了余额，这里不需要再次扣减
        // 只需要记录交易状态变更
        console.log(`提现订单 ${withdrawal.id} 状态变更为 ${status}，无需再次扣减余额`);

        // 获取支付通道信息以确定使用哪个支付服务
        const paymentChannel = await PaymentChannel.findByPk(channelId);

        if (!paymentChannel) {
          await transaction.rollback();
          return res.status(400).json({
            code: 400,
            message: '支付通道不存在',
            data: null
          });
        }

        // 根据支付通道类型调用相应的代付API
        try {


          let payoutResult;

          // 根据支付通道代码选择相应的支付服务
          if (paymentChannel.code === 'kbpay') {
            const kbPayService = await getKBPayService(channelId);
            payoutResult = await kbPayService.createPayout(withdrawal, withdrawal.bank_card);
          } else if (paymentChannel.code === 'basepay') {
            const basePayService = await getBasePayService(channelId);
            payoutResult = await basePayService.createPayout(withdrawal, withdrawal.bank_card);
          } else {
            throw new Error(`不支持的支付通道类型: ${paymentChannel.code}`);
          }

          // 无论代付是否成功，都设置支付通道ID
          withdrawal.payment_channel_id = channelId;

          if (payoutResult.success) {
            // 代付请求成功，设置为处理中状态，等待第三方回调
            withdrawal.status = 'processing';
            withdrawal.remark = remark || '代付处理中';
            withdrawal.payment_channel_order_id = payoutResult.platformOrderNo || payoutResult.data?.order_id || '';
            withdrawal.payment_channel_data = JSON.stringify(payoutResult);
            withdrawal.callback_status = 'none'; // 还未收到回调
          } else {
            // 代付请求失败，设置为已退回状态并退回金额
            withdrawal.status = 'rejected';
            withdrawal.remark = `代付请求失败: ${payoutResult.message}`;
            withdrawal.completion_time = new Date();
            withdrawal.callback_status = 'channel_callback'; // 第三方已回复失败

            // 退回金额到用户收入账户
            const balanceService = require('../services/balanceService');
            const refundResult = await balanceService.adjustBalance(
              withdrawal.user_id,
              'income', // 收入账户
              withdrawal.amount, // 正数表示增加
              'add',
              'increase', // 交易类型为增加（代付失败退回）
              `代付失败退回 ${withdrawal.amount}`,
              withdrawal.id,
              'withdrawal_payout_failed',
              transaction
            );
            withdrawal.refund_transaction_id = refundResult.transactionId;
          }
        } catch (payError) {
          console.error('调用代付API错误:', payError);
          // 代付API调用出错，设置为已退回状态并退回金额
          // 无论如何都设置支付通道ID
          withdrawal.payment_channel_id = channelId;
          withdrawal.status = 'rejected';
          withdrawal.remark = `代付API调用错误: ${payError.message}`;
          withdrawal.completion_time = new Date();
          withdrawal.callback_status = 'none'; // 未回调

          // 退回金额到用户收入账户
          const balanceService = require('../services/balanceService');
          const refundResult = await balanceService.adjustBalance(
            withdrawal.user_id,
            'income', // 收入账户
            withdrawal.amount, // 正数表示增加
            'add',
            'increase', // 交易类型为增加（API调用错误退回）
            `代付API错误退回 ${withdrawal.amount}`,
            withdrawal.id,
            'withdrawal_api_error',
            transaction
          );
          withdrawal.refund_transaction_id = refundResult.transactionId;
        }

        await withdrawal.save({ transaction });
        await transaction.commit();

        // 触发统计数据更新（异步执行，不影响主流程）
        try {
          const statsUpdateService = require('../services/statsUpdateService');
          statsUpdateService.triggerFullStatsUpdate().catch(err => {
            console.error('取款完成后更新统计数据失败:', err);
          });
        } catch (error) {
          console.error('触发统计数据更新失败:', error);
        }

        return res.status(200).json({
          code: 200,
          message: withdrawal.status === 'processing' ? '取款已发送第三方处理' : '取款处理失败',
          data: withdrawal
        });
      }
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('审核取款订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 处理支付失败
exports.handlePaymentFailure = async (req, res) => {
  try {
    const { id } = req.params;
    const { remark } = req.body;

    // 查找取款订单
    const withdrawal = await Withdrawal.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user'
        }
      ]
    });

    if (!withdrawal) {
      return res.status(404).json({
        code: 404,
        message: '取款订单不存在',
        data: null
      });
    }

    // 检查订单状态
    if (withdrawal.status !== 'processing') {
      return res.status(400).json({
        code: 400,
        message: `订单状态不正确，当前状态: ${withdrawal.status}，只能处理处理中的订单`,
        data: null
      });
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      withdrawal.status = 'rejected';
      withdrawal.completion_time = new Date();
      withdrawal.remark = remark || '支付失败，金额已退回';

      // 使用余额服务将金额退回到用户收入账户
      const result = await balanceService.adjustBalance(
        withdrawal.user_id,
        'income', // 收入账户
        withdrawal.amount, // 正数表示增加
        'add',
        'increase', // 交易类型为增加（取款失败退回）
        `提现失败退回 ${withdrawal.amount}`,
        withdrawal.id,
        'withdrawal_failed',
        transaction
      );

      // 更新取款订单关联的退款交易ID
      withdrawal.refund_transaction_id = result.transactionId;

      await withdrawal.save({ transaction });

      // 提交事务
      await transaction.commit();

      return res.status(200).json({
        code: 200,
        message: '支付失败处理成功，金额已退回',
        data: withdrawal
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 模拟取款完成
exports.mockWithdrawal = async (req, res) => {
  try {
    const { id } = req.params;

    // 查找取款订单
    const withdrawal = await Withdrawal.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user'
        }
      ]
    });

    if (!withdrawal) {
      return res.status(404).json({
        code: 404,
        message: '取款订单不存在',
        data: null
      });
    }

    // 检查订单状态
    if (withdrawal.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: `订单状态不正确，当前状态: ${withdrawal.status}`,
        data: null
      });
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      withdrawal.status = 'completed';
      withdrawal.completion_time = new Date();
      withdrawal.remark = '模拟取款完成';
      withdrawal.callback_status = 'mock_callback'; // 设置为模拟回调

      // 用户在提交提现请求时已经扣减了余额，这里不需要再次扣减
      // 只需要记录交易状态变更
      console.log(`模拟通过提现订单 ${withdrawal.id}，无需再次扣减余额`);

      await withdrawal.save({ transaction });

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          console.error('模拟取款完成后更新统计数据失败:', err);
        });
      } catch (error) {
        console.error('触发统计数据更新失败:', error);
      }

      return res.status(200).json({
        code: 200,
        message: '模拟取款完成成功',
        data: withdrawal
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('模拟取款完成错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
