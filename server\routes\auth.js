const express = require('express');
const authController = require('../controllers/authController');
const { verifyAdminToken } = require('../middlewares/authMiddleware');

// 管理员路由
const adminRouter = express.Router();

/**
 * @swagger
 * /api/admin/auth/login:
 *   post:
 *     summary: 管理员登录
 *     tags: [管理员认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: 登录成功
 *       401:
 *         description: 用户名或密码错误
 */
adminRouter.post('/login', authController.adminLogin);

/**
 * @swagger
 * /api/admin/auth/profile:
 *   get:
 *     summary: 获取当前管理员信息
 *     tags: [管理员认证]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/profile', verifyAdminToken, authController.getAdminProfile);

/**
 * @swagger
 * /api/admin/auth/password:
 *   put:
 *     summary: 更新管理员密码
 *     tags: [管理员认证]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - old_password
 *               - new_password
 *             properties:
 *               old_password:
 *                 type: string
 *               new_password:
 *                 type: string
 *     responses:
 *       200:
 *         description: 更新成功
 *       401:
 *         description: 旧密码错误
 */
adminRouter.put('/password', verifyAdminToken, authController.updateAdminPassword);

/**
 * @swagger
 * /api/admin/auth/logout:
 *   post:
 *     summary: 管理员登出
 *     tags: [管理员认证]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 登出成功
 *       401:
 *         description: 未认证
 */
adminRouter.post('/logout', verifyAdminToken, authController.adminLogout);


module.exports = {
  admin: adminRouter
};
