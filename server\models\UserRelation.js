const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UserRelation = sequelize.define('UserRelation', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
    comment: '用户ID',
  },
  parent_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
    comment: '上级用户ID',
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关系级别：1-一级关系，2-二级关系，3-三级关系',
  },
  invite_code_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'invite_codes',
      key: 'id',
    },
    comment: '邀请码ID，仅一级关系有值',
  },
}, {
  tableName: 'user_relations',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'parent_id', 'level'],
    },
    {
      fields: ['user_id'],
    },
    {
      fields: ['parent_id'],
    },
    {
      fields: ['level'],
    },
  ],
});

module.exports = UserRelation;
