const { sequelize } = require('../config/database');
const { Op } = require('sequelize');
const timezoneUtils = require('../utils/timezoneUtils');

// 获取项目类型列表
exports.getProjectTypes = async (req, res) => {
  try {
    // 项目类型是固定的，不需要从数据库获取
    const types = [
      { id: 1, name: '产品', value: '产品' }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: types
    });
  } catch (error) {
    console.error('获取项目类型列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取项目分类列表
exports.getProjectCategories = async (req, res) => {
  try {
    // 项目分类是固定的，不需要从数据库获取
    const categories = [
      { id: 1, name: '基础', value: '基础' }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: categories
    });
  } catch (error) {
    console.error('获取项目分类列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取货币类型列表
exports.getCurrencyTypes = async (req, res) => {
  try {
    // 货币类型是固定的，不需要从数据库获取
    const currencies = [
      { id: 1, name: '法币', value: 'CNY' }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: currencies
    });
  } catch (error) {
    console.error('获取货币类型列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取价格类型列表
exports.getPriceTypes = async (req, res) => {
  try {
    // 价格类型是固定的，不需要从数据库获取
    const priceTypes = [
      { id: 1, name: '固定价格', value: '固定价格' }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: priceTypes
    });
  } catch (error) {
    console.error('获取价格类型列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取支付方式列表
exports.getPaymentMethods = async (req, res) => {
  try {
    // 支付方式是固定的，不需要从数据库获取
    const paymentMethods = [
      { id: 1, name: '余额支付', value: '余额支付' },
      { id: 2, name: '银行卡支付', value: '银行卡支付' },
      { id: 3, name: '混合支付', value: '混合支付' }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: paymentMethods
    });
  } catch (error) {
    console.error('获取支付方式列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取状态列表
exports.getStatusOptions = async (req, res) => {
  try {
    // 状态是固定的，不需要从数据库获取
    const statusOptions = [
      { id: 1, name: '正常', value: 1 },
      { id: 0, name: '下架', value: 0 }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: statusOptions
    });
  } catch (error) {
    console.error('获取状态列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取出售状态列表
exports.getSellStatusOptions = async (req, res) => {
  try {
    // 出售状态是固定的，不需要从数据库获取
    const sellStatusOptions = [
      { id: 0, name: '待售', value: 0 },
      { id: 1, name: '在售', value: 1 },
      { id: 2, name: '售完', value: 2 }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: sellStatusOptions
    });
  } catch (error) {
    console.error('获取出售状态列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取每周收益日列表
exports.getWeeklyProfitDays = async (req, res) => {
  try {
    // 每周收益日是固定的，不需要从数据库获取
    const weeklyProfitDays = [
      { id: 1, name: '周一', value: '1' },
      { id: 2, name: '周二', value: '2' },
      { id: 3, name: '周三', value: '3' },
      { id: 4, name: '周四', value: '4' },
      { id: 5, name: '周五', value: '5' },
      { id: 6, name: '周六', value: '6' },
      { id: 7, name: '周日', value: '7' }
    ];

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: weeklyProfitDays
    });
  } catch (error) {
    console.error('获取每周收益日列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取时区列表
exports.getTimezones = async (req, res) => {
  try {
    // 常用时区列表
    const commonTimezones = [
      { id: 'UTC+0', name: '(UTC+00:00) 协调世界时', value: '+00:00' },
      { id: 'UTC+8', name: '(UTC+08:00) 中国标准时间', value: '+08:00' },
      { id: 'UTC-5', name: '(UTC-05:00) 东部标准时间', value: '-05:00' },
      { id: 'UTC-6', name: '(UTC-06:00) 中部标准时间', value: '-06:00' },
      { id: 'UTC-7', name: '(UTC-07:00) 山地标准时间', value: '-07:00' },
      { id: 'UTC-8', name: '(UTC-08:00) 太平洋标准时间', value: '-08:00' }
    ];

    // 所有时区列表
    const allTimezones = [];

    // 生成从 UTC-12 到 UTC+14 的时区列表
    for (let i = -12; i <= 14; i++) {
      const sign = i >= 0 ? '+' : '-';
      const hours = Math.abs(i).toString().padStart(2, '0');
      const value = `${sign}${hours}:00`;
      const name = `(UTC${sign}${hours}:00) UTC${sign}${hours}`;
      const id = `UTC${sign}${hours}`;

      // 避免重复添加常用时区
      if (!commonTimezones.some(tz => tz.value === value)) {
        allTimezones.push({ id, name, value });
      }
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        common: commonTimezones,
        all: allTimezones
      }
    });
  } catch (error) {
    console.error('获取时区列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取所有配置数据
exports.getAllConfigs = async (req, res) => {
  try {
    // 获取所有配置数据
    const projectTypes = [{ id: 1, name: '产品', value: '产品' }];
    const projectCategories = [{ id: 1, name: '基础', value: '基础' }];
    const currencyTypes = [{ id: 1, name: '法币', value: 'CNY' }];
    const priceTypes = [{ id: 1, name: '固定价格', value: '固定价格' }];
    const paymentMethods = [
      { id: 1, name: '余额支付', value: '余额支付' },
      { id: 2, name: '银行卡支付', value: '银行卡支付' },
      { id: 3, name: '混合支付', value: '混合支付' }
    ];
    const statusOptions = [
      { id: 1, name: '正常', value: 1 },
      { id: 0, name: '下架', value: 0 }
    ];
    const sellStatusOptions = [
      { id: 0, name: '待售', value: 0 },
      { id: 1, name: '在售', value: 1 },
      { id: 2, name: '售完', value: 2 }
    ];
    const weeklyProfitDays = [
      { id: 1, name: '周一', value: '1' },
      { id: 2, name: '周二', value: '2' },
      { id: 3, name: '周三', value: '3' },
      { id: 4, name: '周四', value: '4' },
      { id: 5, name: '周五', value: '5' },
      { id: 6, name: '周六', value: '6' },
      { id: 7, name: '周日', value: '7' }
    ];

    // 获取时区列表
    const commonTimezones = [
      { id: 'UTC+0', name: '(UTC+00:00) 协调世界时', value: '+00:00' },
      { id: 'UTC+8', name: '(UTC+08:00) 中国标准时间', value: '+08:00' },
      { id: 'UTC-5', name: '(UTC-05:00) 东部标准时间', value: '-05:00' },
      { id: 'UTC-6', name: '(UTC-06:00) 中部标准时间', value: '-06:00' },
      { id: 'UTC-7', name: '(UTC-07:00) 山地标准时间', value: '-07:00' },
      { id: 'UTC-8', name: '(UTC-08:00) 太平洋标准时间', value: '-08:00' }
    ];

    // 所有时区列表
    const allTimezones = [];

    // 生成从 UTC-12 到 UTC+14 的时区列表
    for (let i = -12; i <= 14; i++) {
      const sign = i >= 0 ? '+' : '-';
      const hours = Math.abs(i).toString().padStart(2, '0');
      const value = `${sign}${hours}:00`;
      const name = `(UTC${sign}${hours}:00) UTC${sign}${hours}`;
      const id = `UTC${sign}${hours}`;

      // 避免重复添加常用时区
      if (!commonTimezones.some(tz => tz.value === value)) {
        allTimezones.push({ id, name, value });
      }
    }

    const timezones = {
      common: commonTimezones,
      all: allTimezones
    };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        projectTypes,
        projectCategories,
        currencyTypes,
        priceTypes,
        paymentMethods,
        statusOptions,
        sellStatusOptions,
        weeklyProfitDays,
        timezones
      }
    });
  } catch (error) {
    console.error('获取所有配置数据错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
