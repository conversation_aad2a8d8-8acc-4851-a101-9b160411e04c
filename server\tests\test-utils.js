/**
 * 测试工具类
 * 提供标准化的余额验证和数据快照功能
 */

const { User, InvestmentProfit } = require('../models');

/**
 * 余额跟踪器
 */
const BalanceTracker = {
  /**
   * 创建数据快照
   */
  async snapshot(userId, investmentId, description = '') {
    const user = await User.findByPk(userId);
    const profitCount = await InvestmentProfit.count({ 
      where: { investment_id: investmentId } 
    });
    
    const snapshot = {
      userId,
      investmentId,
      balance: parseFloat(user.balance),
      profitCount,
      timestamp: new Date(),
      description
    };
    
    console.log(`📸 快照 [${description}]: 余额=${snapshot.balance}, 收益记录=${snapshot.profitCount}`);
    return snapshot;
  },

  /**
   * 验证两个快照之间的变化
   */
  validateChange(beforeSnapshot, afterSnapshot, expectedProfitAmount) {
    const balanceChange = afterSnapshot.balance - beforeSnapshot.balance;
    const profitChange = afterSnapshot.profitCount - beforeSnapshot.profitCount;
    const expectedBalanceChange = profitChange * expectedProfitAmount;
    
    const result = {
      balanceChange: parseFloat(balanceChange.toFixed(2)),
      profitChange,
      expectedBalanceChange: parseFloat(expectedBalanceChange.toFixed(2)),
      isValid: Math.abs(balanceChange - expectedBalanceChange) < 0.01,
      timeDiff: afterSnapshot.timestamp - beforeSnapshot.timestamp
    };
    
    console.log(`🔍 变化验证:`);
    console.log(`   - 余额变化: ${result.balanceChange} (预期: ${result.expectedBalanceChange})`);
    console.log(`   - 收益记录变化: ${result.profitChange}`);
    console.log(`   - 验证结果: ${result.isValid ? '✅ 通过' : '❌ 失败'}`);
    
    return result;
  },

  /**
   * 比较多个快照
   */
  compareSnapshots(snapshots) {
    console.log(`\n📊 快照对比 (${snapshots.length}个快照):`);
    
    for (let i = 0; i < snapshots.length; i++) {
      const snapshot = snapshots[i];
      console.log(`   ${i + 1}. [${snapshot.description}] 余额=${snapshot.balance}, 记录=${snapshot.profitCount}, 时间=${snapshot.timestamp.toISOString()}`);
      
      if (i > 0) {
        const prev = snapshots[i - 1];
        const balanceChange = snapshot.balance - prev.balance;
        const profitChange = snapshot.profitCount - prev.profitCount;
        console.log(`      ↳ 变化: 余额+${balanceChange}, 记录+${profitChange}`);
      }
    }
  }
};

/**
 * 测试数据管理器
 */
const TestDataManager = {
  /**
   * 记录测试创建的数据，用于清理
   */
  testData: {
    profitIds: [],
    originalStates: {}
  },

  /**
   * 记录创建的收益ID
   */
  recordProfitId(profitId) {
    this.testData.profitIds.push(profitId);
  },

  /**
   * 记录原始状态
   */
  recordOriginalState(key, value) {
    this.testData.originalStates[key] = value;
  },

  /**
   * 清理所有测试数据
   */
  async cleanup() {
    try {
      // 清理收益记录
      if (this.testData.profitIds.length > 0) {
        await InvestmentProfit.destroy({ 
          where: { id: this.testData.profitIds } 
        });
        console.log(`✅ 清理了 ${this.testData.profitIds.length} 条测试收益记录`);
      }

      // 恢复原始状态
      for (const [key, value] of Object.entries(this.testData.originalStates)) {
        console.log(`✅ 恢复原始状态: ${key}`);
      }

      // 重置数据
      this.testData.profitIds = [];
      this.testData.originalStates = {};
      
    } catch (error) {
      console.log('⚠️  清理测试数据时出错:', error.message);
    }
  }
};

/**
 * 时间工具
 */
const TimeUtils = {
  /**
   * 格式化时间差
   */
  formatTimeDiff(startTime, endTime) {
    const diffMs = endTime - startTime;
    const diffSeconds = Math.round(diffMs / 1000);
    const diffMinutes = Math.round(diffMs / (1000 * 60));
    const diffHours = Math.round(diffMs / (1000 * 60 * 60));
    
    if (diffMs < 1000) {
      return `${diffMs}毫秒`;
    } else if (diffMs < 60000) {
      return `${diffSeconds}秒`;
    } else if (diffMs < 3600000) {
      return `${diffMinutes}分钟`;
    } else {
      return `${diffHours}小时`;
    }
  },

  /**
   * 创建测试时间点
   */
  createTestTimes(baseTime, cycleHours, count) {
    const times = [];
    const cycleMs = cycleHours * 60 * 60 * 1000;
    
    for (let i = 1; i <= count; i++) {
      times.push(new Date(baseTime.getTime() + i * cycleMs));
    }
    
    return times;
  }
};

/**
 * 测试断言工具
 */
const TestAssert = {
  /**
   * 断言余额变化正确
   */
  assertBalanceChange(beforeSnapshot, afterSnapshot, expectedAmount, description = '') {
    const validation = BalanceTracker.validateChange(beforeSnapshot, afterSnapshot, expectedAmount);
    
    if (!validation.isValid) {
      throw new Error(`余额变化断言失败 [${description}]: 预期${validation.expectedBalanceChange}, 实际${validation.balanceChange}`);
    }
    
    console.log(`✅ 余额变化断言通过 [${description}]`);
    return validation;
  },

  /**
   * 断言收益记录数量
   */
  assertProfitCount(beforeSnapshot, afterSnapshot, expectedIncrease, description = '') {
    const actualIncrease = afterSnapshot.profitCount - beforeSnapshot.profitCount;
    
    if (actualIncrease !== expectedIncrease) {
      throw new Error(`收益记录数量断言失败 [${description}]: 预期增加${expectedIncrease}, 实际增加${actualIncrease}`);
    }
    
    console.log(`✅ 收益记录数量断言通过 [${description}]: 增加${actualIncrease}条`);
    return actualIncrease;
  },

  /**
   * 断言无变化
   */
  assertNoChange(beforeSnapshot, afterSnapshot, description = '') {
    const balanceChange = afterSnapshot.balance - beforeSnapshot.balance;
    const profitChange = afterSnapshot.profitCount - beforeSnapshot.profitCount;
    
    if (Math.abs(balanceChange) > 0.01 || profitChange !== 0) {
      throw new Error(`无变化断言失败 [${description}]: 余额变化${balanceChange}, 记录变化${profitChange}`);
    }
    
    console.log(`✅ 无变化断言通过 [${description}]`);
    return true;
  }
};

module.exports = {
  BalanceTracker,
  TestDataManager,
  TimeUtils,
  TestAssert
};
