-- 添加 user_id 字段到 users 表
ALTER TABLE users ADD COLUMN user_id VARCHAR(20) NULL COMMENT '用户唯一标识';

-- 为每个用户生成唯一的 user_id
UPDATE users SET user_id = CONCAT('U', LPAD(id, 8, '0'));

-- 设置 user_id 为唯一索引
ALTER TABLE users ADD UNIQUE INDEX idx_user_id (user_id);

-- 添加 user_id 字段到 investments 表
ALTER TABLE investments ADD COLUMN user_id_ref VARCHAR(20) NULL COMMENT '关联到users表的user_id';

-- 更新 investments 表的 user_id_ref 字段
UPDATE investments i
JOIN users u ON i.user_id = u.id
SET i.user_id_ref = u.user_id;

-- 添加 investments 表的 user_id_ref 索引
ALTER TABLE investments ADD INDEX idx_user_id_ref (user_id_ref);
