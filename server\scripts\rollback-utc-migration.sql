-- =====================================================
-- 方案5.1：UTC迁移回滚脚本
-- =====================================================
-- 
-- 重要提醒：
-- 1. 仅在UTC迁移出现问题时使用！
-- 2. 执行前请确认需要回滚到+08:00时区
-- 3. 建议优先使用数据库备份恢复
-- 4. 此脚本将UTC时间转换回+08:00时区
-- 
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 记录回滚开始时间
SELECT NOW() as '回滚开始时间', 'UTC迁移回滚脚本开始执行' as '状态';

-- =====================================================
-- 1. 回滚 investments 表
-- =====================================================
SELECT '正在回滚 investments 表...' as '状态';

UPDATE investments 
SET 
    start_time = CONVERT_TZ(start_time, '+00:00', '+08:00'),
    last_profit_time = CASE 
        WHEN last_profit_time IS NOT NULL 
        THEN CONVERT_TZ(last_profit_time, '+00:00', '+08:00')
        ELSE NULL 
    END,
    created_at = CONVERT_TZ(created_at, '+00:00', '+08:00'),
    updated_at = CONVERT_TZ(updated_at, '+00:00', '+08:00');

SELECT ROW_COUNT() as '已回滚investments记录数';

-- =====================================================
-- 2. 回滚 investment_profits 表
-- =====================================================
SELECT '正在回滚 investment_profits 表...' as '状态';

UPDATE investment_profits 
SET 
    profit_time = CONVERT_TZ(profit_time, '+00:00', '+08:00'),
    created_at = CONVERT_TZ(created_at, '+00:00', '+08:00'),
    updated_at = CONVERT_TZ(updated_at, '+00:00', '+08:00');

SELECT ROW_COUNT() as '已回滚investment_profits记录数';

-- =====================================================
-- 3. 回滚 users 表
-- =====================================================
SELECT '正在回滚 users 表...' as '状态';

UPDATE users 
SET 
    created_at = CONVERT_TZ(created_at, '+00:00', '+08:00'),
    updated_at = CONVERT_TZ(updated_at, '+00:00', '+08:00'),
    last_login_time = CASE 
        WHEN last_login_time IS NOT NULL 
        THEN CONVERT_TZ(last_login_time, '+00:00', '+08:00')
        ELSE NULL 
    END;

SELECT ROW_COUNT() as '已回滚users记录数';

-- =====================================================
-- 4. 回滚 transactions 表
-- =====================================================
SELECT '正在回滚 transactions 表...' as '状态';

UPDATE transactions 
SET 
    created_at = CONVERT_TZ(created_at, '+00:00', '+08:00'),
    updated_at = CONVERT_TZ(updated_at, '+00:00', '+08:00'),
    completed_at = CASE 
        WHEN completed_at IS NOT NULL 
        THEN CONVERT_TZ(completed_at, '+00:00', '+08:00')
        ELSE NULL 
    END;

SELECT ROW_COUNT() as '已回滚transactions记录数';

-- =====================================================
-- 5. 回滚 admin_users 表
-- =====================================================
SELECT '正在回滚 admin_users 表...' as '状态';

UPDATE admin_users 
SET 
    created_at = CONVERT_TZ(created_at, '+00:00', '+08:00'),
    updated_at = CONVERT_TZ(updated_at, '+00:00', '+08:00'),
    last_login_time = CASE 
        WHEN last_login_time IS NOT NULL 
        THEN CONVERT_TZ(last_login_time, '+00:00', '+08:00')
        ELSE NULL 
    END;

SELECT ROW_COUNT() as '已回滚admin_users记录数';

-- =====================================================
-- 6. 回滚 projects 表
-- =====================================================
SELECT '正在回滚 projects 表...' as '状态';

UPDATE projects 
SET 
    created_at = CONVERT_TZ(created_at, '+00:00', '+08:00'),
    updated_at = CONVERT_TZ(updated_at, '+00:00', '+08:00');

SELECT ROW_COUNT() as '已回滚projects记录数';

-- =====================================================
-- 7. 回滚 system_params 表
-- =====================================================
SELECT '正在回滚 system_params 表...' as '状态';

UPDATE system_params 
SET 
    created_at = CONVERT_TZ(created_at, '+00:00', '+08:00'),
    updated_at = CONVERT_TZ(updated_at, '+00:00', '+08:00');

-- 恢复时区配置为+08:00
UPDATE system_params 
SET param_value = '+08:00', updated_at = NOW()
WHERE param_key = '[site.timezone]';

SELECT ROW_COUNT() as '已回滚system_params记录数';

-- =====================================================
-- 8. 验证回滚结果
-- =====================================================
SELECT '正在验证回滚结果...' as '状态';

-- 检查investments表的时间范围
SELECT 
    'investments' as '表名',
    MIN(start_time) as '最早开始时间',
    MAX(start_time) as '最晚开始时间',
    MIN(last_profit_time) as '最早收益时间',
    MAX(last_profit_time) as '最晚收益时间'
FROM investments;

-- 检查investment_profits表的时间范围
SELECT 
    'investment_profits' as '表名',
    MIN(profit_time) as '最早收益时间',
    MAX(profit_time) as '最晚收益时间',
    COUNT(*) as '总记录数'
FROM investment_profits;

-- 检查时区配置
SELECT param_key, param_value 
FROM system_params 
WHERE param_key = '[site.timezone]';

-- =====================================================
-- 完成回滚
-- =====================================================
SELECT NOW() as '回滚完成时间', 'UTC迁移回滚脚本执行完成' as '状态';

-- 提交事务
COMMIT;

-- =====================================================
-- 回滚后操作清单
-- =====================================================
-- 
-- 请在回滚完成后执行以下操作：
-- 
-- 1. 恢复代码：
--    - 回滚到UTC迁移前的代码版本
--    - 确保database.js中时区设置为+08:00
--    - 确保timezoneConfig.js中硬编码时区为+08:00
-- 
-- 2. 重启服务：
--    - 重启应用服务器
--    - 重启数据库连接
--    - 清除Redis缓存
-- 
-- 3. 验证系统：
--    - 检查收益发放是否正常
--    - 验证时间显示是否正确
--    - 测试用户注册和登录
-- 
-- 4. 监控系统：
--    - 观察系统日志
--    - 检查用户反馈
--    - 监控收益发放
-- 
-- 5. 分析问题：
--    - 记录回滚原因
--    - 分析UTC迁移失败的原因
--    - 制定改进方案
-- 
-- =====================================================
