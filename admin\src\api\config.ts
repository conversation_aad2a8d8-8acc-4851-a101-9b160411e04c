import request from '@/utils/request'

// 获取所有配置数据
export const getAllConfigs = () => {
  return request({
    url: '/api/admin/config/all',
    method: 'get'
  })
}

// 获取项目类型列表
export const getProjectTypes = () => {
  return request({
    url: '/api/admin/config/project-types',
    method: 'get'
  })
}

// 获取项目分类列表
export const getProjectCategories = () => {
  return request({
    url: '/api/admin/config/project-categories',
    method: 'get'
  })
}

// 获取货币类型列表
export const getCurrencyTypes = () => {
  return request({
    url: '/api/admin/config/currency-types',
    method: 'get'
  })
}

// 获取价格类型列表
export const getPriceTypes = () => {
  return request({
    url: '/api/admin/config/price-types',
    method: 'get'
  })
}

// 获取支付方式列表
export const getPaymentMethods = () => {
  return request({
    url: '/api/admin/config/payment-methods',
    method: 'get'
  })
}

// 获取状态列表
export const getStatusOptions = () => {
  return request({
    url: '/api/admin/config/status-options',
    method: 'get'
  })
}

// 获取出售状态列表
export const getSellStatusOptions = () => {
  return request({
    url: '/api/admin/config/sell-status-options',
    method: 'get'
  })
}

// 获取每周收益日列表
export const getWeeklyProfitDays = () => {
  return request({
    url: '/api/admin/config/weekly-profit-days',
    method: 'get'
  })
}
