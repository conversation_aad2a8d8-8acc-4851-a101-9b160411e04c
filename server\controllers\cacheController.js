/**
 * 缓存控制器
 * 提供缓存管理相关的API
 */
const cacheService = require('../services/cacheService');
const logger = require('../utils/logger');

/**
 * 获取缓存统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getStats = async (req, res) => {
  try {
    const result = await cacheService.getStats();
    
    if (result.success) {
      return res.json({
        code: 200,
        message: '获取缓存统计信息成功',
        data: result.stats
      });
    } else {
      return res.status(500).json({
        code: 500,
        message: result.message,
        data: null
      });
    }
  } catch (error) {
    logger.error('获取缓存统计信息失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取缓存统计信息失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 清除指定前缀的缓存
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.clearByPrefix = async (req, res) => {
  try {
    const { prefix } = req.body;
    
    if (!prefix) {
      return res.status(400).json({
        code: 400,
        message: '缓存前缀不能为空',
        data: null
      });
    }
    
    const result = await cacheService.clearByPrefix(prefix);
    
    if (result) {
      return res.json({
        code: 200,
        message: `清除前缀为 ${prefix} 的缓存成功`,
        data: null
      });
    } else {
      return res.status(500).json({
        code: 500,
        message: `清除前缀为 ${prefix} 的缓存失败`,
        data: null
      });
    }
  } catch (error) {
    logger.error('清除缓存失败:', error);
    return res.status(500).json({
      code: 500,
      message: '清除缓存失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 清除所有缓存
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.clearAll = async (req, res) => {
  try {
    // 使用Redis的FLUSHDB命令清除所有缓存
    const redisClient = require('../utils/redisClient');
    await redisClient.client.flushdb();
    
    return res.json({
      code: 200,
      message: '清除所有缓存成功',
      data: null
    });
  } catch (error) {
    logger.error('清除所有缓存失败:', error);
    return res.status(500).json({
      code: 500,
      message: '清除所有缓存失败: ' + error.message,
      data: null
    });
  }
};
