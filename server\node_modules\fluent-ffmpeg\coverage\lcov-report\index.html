
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85.96% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1017/1183</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">93.51% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>202/216</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.25% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>51/62</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85.96% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>1017/1183</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input oninput="onInput()" type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="83.89" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.89" class="pct high">83.89%</td>
	<td data-value="677" class="abs high">568/677</td>
	<td data-value="87.93" class="pct high">87.93%</td>
	<td data-value="116" class="abs high">102/116</td>
	<td data-value="84.84" class="pct high">84.84%</td>
	<td data-value="33" class="abs high">28/33</td>
	<td data-value="83.89" class="pct high">83.89%</td>
	<td data-value="677" class="abs high">568/677</td>
	</tr>

<tr>
	<td class="file high" data-value="src/utils"><a href="src/utils/index.html">src/utils</a></td>
	<td data-value="87.52" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 87%"></div><div class="cover-empty" style="width: 13%"></div></div>
	</td>
	<td data-value="87.52" class="pct high">87.52%</td>
	<td data-value="457" class="abs high">400/457</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="92" class="abs high">92/92</td>
	<td data-value="82.6" class="pct high">82.6%</td>
	<td data-value="23" class="abs high">19/23</td>
	<td data-value="87.52" class="pct high">87.52%</td>
	<td data-value="457" class="abs high">400/457</td>
	</tr>

<tr>
	<td class="file high" data-value="tests/acceptance"><a href="tests/acceptance/index.html">tests/acceptance</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file high" data-value="tests/helpers"><a href="tests/helpers/index.html">tests/helpers</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="46" class="abs high">46/46</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="46" class="abs high">46/46</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2023-11-17T09:02:42.005Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    