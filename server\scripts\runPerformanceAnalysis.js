/**
 * 性能分析启动脚本
 * 用于运行所有性能分析工具
 */
const { logSystemInfo } = require('./analyzePerformance');
const { logMemoryUsage } = require('./memoryMonitor');
const { analyzeCommonQueries, analyzeTableStructure } = require('./databaseAnalyzer');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 创建命令行交互界面
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 创建日志目录
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 主菜单
function showMainMenu() {
  console.clear();
  console.log('=== Node.js 性能分析工具 ===');
  console.log('1. 系统信息');
  console.log('2. 内存使用情况');
  console.log('3. 数据库查询分析');
  console.log('4. 表结构和索引分析');
  console.log('5. 运行所有分析');
  console.log('0. 退出');
  console.log('========================');
  
  rl.question('请选择操作 (0-5): ', async (answer) => {
    switch (answer) {
      case '1':
        await runSystemInfo();
        break;
      case '2':
        await runMemoryAnalysis();
        break;
      case '3':
        await runDatabaseQueryAnalysis();
        break;
      case '4':
        await runTableStructureAnalysis();
        break;
      case '5':
        await runAllAnalysis();
        break;
      case '0':
        console.log('退出程序');
        rl.close();
        return;
      default:
        console.log('无效的选择，请重试');
        setTimeout(showMainMenu, 1000);
        return;
    }
    
    // 等待用户按任意键继续
    rl.question('\n按 Enter 键返回主菜单...', () => {
      showMainMenu();
    });
  });
}

// 运行系统信息分析
async function runSystemInfo() {
  console.clear();
  console.log('=== 系统信息 ===');
  logSystemInfo();
}

// 运行内存分析
async function runMemoryAnalysis() {
  console.clear();
  console.log('=== 内存使用情况 ===');
  logMemoryUsage();
  
  console.log('\n监控内存使用情况 (按 Ctrl+C 停止)...');
  
  // 每秒记录一次内存使用情况
  const interval = setInterval(logMemoryUsage, 1000);
  
  // 等待用户按 Ctrl+C 停止
  await new Promise(resolve => {
    const onSigInt = () => {
      clearInterval(interval);
      console.log('\n停止内存监控');
      process.off('SIGINT', onSigInt);
      resolve();
    };
    
    process.on('SIGINT', onSigInt);
  });
}

// 运行数据库查询分析
async function runDatabaseQueryAnalysis() {
  console.clear();
  console.log('=== 数据库查询分析 ===');
  await analyzeCommonQueries();
}

// 运行表结构和索引分析
async function runTableStructureAnalysis() {
  console.clear();
  console.log('=== 表结构和索引分析 ===');
  await analyzeTableStructure();
}

// 运行所有分析
async function runAllAnalysis() {
  console.clear();
  console.log('=== 运行所有分析 ===');
  
  console.log('\n1. 系统信息');
  logSystemInfo();
  
  console.log('\n2. 内存使用情况');
  logMemoryUsage();
  
  console.log('\n3. 数据库查询分析');
  await analyzeCommonQueries();
  
  console.log('\n4. 表结构和索引分析');
  await analyzeTableStructure();
  
  console.log('\n所有分析完成!');
}

// 主函数
async function main() {
  try {
    // 显示主菜单
    showMainMenu();
  } catch (error) {
    console.error('性能分析过程中出错:', error);
    rl.close();
    process.exit(1);
  }
}

// 如果作为独立脚本运行，则执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  runSystemInfo,
  runMemoryAnalysis,
  runDatabaseQueryAnalysis,
  runTableStructureAnalysis,
  runAllAnalysis
};
