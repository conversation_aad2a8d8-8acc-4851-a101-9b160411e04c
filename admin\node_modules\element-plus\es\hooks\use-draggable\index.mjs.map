{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-draggable/index.ts"], "sourcesContent": ["import { onBeforeUnmount, onMounted, watchEffect } from 'vue'\nimport { addUnit } from '@element-plus/utils'\nimport type { ComputedRef, Ref } from 'vue'\n\nexport const useDraggable = (\n  targetRef: Ref<HTMLElement | undefined>,\n  dragRef: Ref<HTMLElement | undefined>,\n  draggable: ComputedRef<boolean>,\n  overflow?: ComputedRef<boolean>\n) => {\n  let transform = {\n    offsetX: 0,\n    offsetY: 0,\n  }\n\n  const onMousedown = (e: MouseEvent) => {\n    const downX = e.clientX\n    const downY = e.clientY\n    const { offsetX, offsetY } = transform\n\n    const targetRect = targetRef.value!.getBoundingClientRect()\n    const targetLeft = targetRect.left\n    const targetTop = targetRect.top\n    const targetWidth = targetRect.width\n    const targetHeight = targetRect.height\n\n    const clientWidth = document.documentElement.clientWidth\n    const clientHeight = document.documentElement.clientHeight\n\n    const minLeft = -targetLeft + offsetX\n    const minTop = -targetTop + offsetY\n    const maxLeft = clientWidth - targetLeft - targetWidth + offsetX\n    const maxTop = clientHeight - targetTop - targetHeight + offsetY\n\n    const onMousemove = (e: MouseEvent) => {\n      let moveX = offsetX + e.clientX - downX\n      let moveY = offsetY + e.clientY - downY\n\n      if (!overflow?.value) {\n        moveX = Math.min(Math.max(moveX, minLeft), maxLeft)\n        moveY = Math.min(Math.max(moveY, minTop), maxTop)\n      }\n\n      transform = {\n        offsetX: moveX,\n        offsetY: moveY,\n      }\n\n      if (targetRef.value) {\n        targetRef.value.style.transform = `translate(${addUnit(\n          moveX\n        )}, ${addUnit(moveY)})`\n      }\n    }\n\n    const onMouseup = () => {\n      document.removeEventListener('mousemove', onMousemove)\n      document.removeEventListener('mouseup', onMouseup)\n    }\n\n    document.addEventListener('mousemove', onMousemove)\n    document.addEventListener('mouseup', onMouseup)\n  }\n\n  const onDraggable = () => {\n    if (dragRef.value && targetRef.value) {\n      dragRef.value.addEventListener('mousedown', onMousedown)\n    }\n  }\n\n  const offDraggable = () => {\n    if (dragRef.value && targetRef.value) {\n      dragRef.value.removeEventListener('mousedown', onMousedown)\n    }\n  }\n\n  const resetPosition = () => {\n    transform = {\n      offsetX: 0,\n      offsetY: 0,\n    }\n    if (targetRef.value) {\n      targetRef.value.style.transform = 'none'\n    }\n  }\n\n  onMounted(() => {\n    watchEffect(() => {\n      if (draggable.value) {\n        onDraggable()\n      } else {\n        offDraggable()\n      }\n    })\n  })\n\n  onBeforeUnmount(() => {\n    offDraggable()\n  })\n\n  return {\n    resetPosition,\n  }\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,YAAY,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,KAAK;AACzE,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC;AAC5B,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC;AAC5B,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;AAC3C,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;AAC/D,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;AACvC,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;AACrC,IAAI,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;AACzC,IAAI,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;AAC3C,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC;AAC7D,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC;AAC/D,IAAI,MAAM,OAAO,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC;AAC1C,IAAI,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;AACxC,IAAI,MAAM,OAAO,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,GAAG,OAAO,CAAC;AACrE,IAAI,MAAM,MAAM,GAAG,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,OAAO,CAAC;AACrE,IAAI,MAAM,WAAW,GAAG,CAAC,EAAE,KAAK;AAChC,MAAM,IAAI,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC;AAC/C,MAAM,IAAI,KAAK,GAAG,OAAO,GAAG,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC;AAC/C,MAAM,IAAI,EAAE,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzD,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5D,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1D,OAAO;AACP,MAAM,SAAS,GAAG;AAClB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO,EAAE,KAAK;AACtB,OAAO,CAAC;AACR,MAAM,IAAI,SAAS,CAAC,KAAK,EAAE;AAC3B,QAAQ,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,MAAM;AAC5B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC7D,MAAM,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACzD,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACxD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACpD,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,EAAE;AAC1C,MAAM,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC/D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,EAAE;AAC1C,MAAM,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAClE,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,SAAS,GAAG;AAChB,MAAM,OAAO,EAAE,CAAC;AAChB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK,CAAC;AACN,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AACzB,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;AAC/C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,WAAW,CAAC,MAAM;AACtB,MAAM,IAAI,SAAS,CAAC,KAAK,EAAE;AAC3B,QAAQ,WAAW,EAAE,CAAC;AACtB,OAAO,MAAM;AACb,QAAQ,YAAY,EAAE,CAAC;AACvB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,CAAC,MAAM;AACxB,IAAI,YAAY,EAAE,CAAC;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ;;;;"}