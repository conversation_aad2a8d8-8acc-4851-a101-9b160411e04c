{"version": 3, "file": "sr.min.js", "sources": ["../../../../packages/locale/lang/sr.ts"], "sourcesContent": ["export default {\n  name: 'sr',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Поништ<PERSON>',\n    },\n    datepicker: {\n      now: 'Сада',\n      today: 'Дана<PERSON>',\n      cancel: 'От<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Бриш<PERSON>',\n      confirm: 'OK',\n      selectDate: 'Изабери датум',\n      selectTime: 'Изабери време',\n      startDate: 'Датум почетка',\n      startTime: 'Време почетка',\n      endDate: 'Датум завршетка',\n      endTime: 'Време завршетка',\n      prevYear: 'Претходна година',\n      nextYear: 'Следећа година',\n      prevMonth: 'Претходни месец',\n      nextMonth: 'Следећи месец',\n      year: 'година',\n      month1: 'јануар',\n      month2: 'фебруар',\n      month3: 'март',\n      month4: 'април',\n      month5: 'мај',\n      month6: 'јун',\n      month7: 'јул',\n      month8: 'август',\n      month9: 'септембар',\n      month10: 'октобар',\n      month11: 'новембар',\n      month12: 'децембар',\n      week: 'седмица',\n      weeks: {\n        sun: 'Нед',\n        mon: 'Пон',\n        tue: 'Уто',\n        wed: 'Сре',\n        thu: 'Чет',\n        fri: 'Пет',\n        sat: 'Суб',\n      },\n      months: {\n        jan: 'јан',\n        feb: 'феб',\n        mar: 'мар',\n        apr: 'апр',\n        may: 'мај',\n        jun: 'јун',\n        jul: 'јул',\n        aug: 'авг',\n        sep: 'сеп',\n        oct: 'окт',\n        nov: 'нов',\n        dec: 'дец',\n      },\n    },\n    select: {\n      loading: 'Учитавање',\n      noMatch: 'Нема резултата',\n      noData: 'Нема података',\n      placeholder: 'Изабери',\n    },\n    mention: {\n      loading: 'Учитавање',\n    },\n    cascader: {\n      noMatch: 'Нема резултата',\n      loading: 'Учитавање',\n      placeholder: 'Изабери',\n      noData: 'Нема података',\n    },\n    pagination: {\n      goto: 'Иди на',\n      pagesize: '/страни',\n      total: 'Укупно {total}',\n      pageClassifier: '',\n      page: 'Страна',\n      prev: 'Иди на претходну страну',\n      next: 'Иди на следећу страну',\n      currentPage: 'страна {pager}',\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Порука',\n      confirm: 'OK',\n      cancel: 'Откажи',\n      error: 'Неисправан унос',\n    },\n    upload: {\n      deleteTip: 'притисни БРИШИ да обришеш',\n      delete: 'Бриши',\n      preview: 'Види',\n      continue: 'Настави',\n    },\n    table: {\n      emptyText: 'Нема података',\n      confirmFilter: 'Потврди',\n      resetFilter: 'Ресетуј',\n      clearFilter: 'Све',\n      sumText: 'Збир',\n    },\n    tree: {\n      emptyText: 'Нема података',\n    },\n    transfer: {\n      noMatch: 'Нема резултата',\n      noData: 'Нема података',\n      titles: ['Листа 1', 'Листа 2'], // to be translated\n      filterPlaceholder: 'Унеси кључну реч',\n      noCheckedFormat: '{total} ставки',\n      hasCheckedFormat: '{checked}/{total} обележених',\n    },\n    image: {\n      error: 'НЕУСПЕШНО',\n    },\n    pageHeader: {\n      title: 'Назад',\n    },\n    popconfirm: {\n      confirmButtonText: 'Да',\n      cancelButtonText: 'Не',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,2EAA2E,CAAC,UAAU,CAAC,2EAA2E,CAAC,SAAS,CAAC,2EAA2E,CAAC,SAAS,CAAC,2EAA2E,CAAC,OAAO,CAAC,uFAAuF,CAAC,OAAO,CAAC,uFAAuF,CAAC,QAAQ,CAAC,6FAA6F,CAAC,QAAQ,CAAC,iFAAiF,CAAC,SAAS,CAAC,uFAAuF,CAAC,SAAS,CAAC,2EAA2E,CAAC,IAAI,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,wDAAwD,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,kDAAkD,CAAC,IAAI,CAAC,4CAA4C,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,iFAAiF,CAAC,MAAM,CAAC,2EAA2E,CAAC,WAAW,CAAC,4CAA4C,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,iFAAiF,CAAC,OAAO,CAAC,wDAAwD,CAAC,WAAW,CAAC,4CAA4C,CAAC,MAAM,CAAC,2EAA2E,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,uCAAuC,CAAC,KAAK,CAAC,8CAA8C,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,sCAAsC,CAAC,IAAI,CAAC,6HAA6H,CAAC,IAAI,CAAC,iHAAiH,CAAC,WAAW,CAAC,8CAA8C,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,uFAAuF,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,yIAAyI,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,0BAA0B,CAAC,QAAQ,CAAC,4CAA4C,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,2EAA2E,CAAC,aAAa,CAAC,4CAA4C,CAAC,WAAW,CAAC,4CAA4C,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,2EAA2E,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,iFAAiF,CAAC,MAAM,CAAC,2EAA2E,CAAC,MAAM,CAAC,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC,iBAAiB,CAAC,wFAAwF,CAAC,eAAe,CAAC,8CAA8C,CAAC,gBAAgB,CAAC,gFAAgF,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}