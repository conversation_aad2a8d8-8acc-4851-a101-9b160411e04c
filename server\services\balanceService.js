/**
 * 余额管理服务
 * 统一处理用户余额相关的操作，确保数据一致性
 */
const { User, AccountBalance, Transaction } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

/**
 * 获取用户的总余额（充值账户和收入账户的总和）
 * @param {number} userId - 用户ID
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<number>} - 返回用户总余额
 */
exports.getTotalBalance = async function(userId, transaction = null) {
  try {
    const options = transaction ? { transaction } : {};

    // 查询用户的所有账户余额
    const accountBalances = await AccountBalance.findAll({
      where: {
        user_id: userId
      },
      ...options
    });

    // 计算总余额
    let totalBalance = 0;
    accountBalances.forEach(account => {
      totalBalance += parseFloat(account.balance);
    });

    return totalBalance;
  } catch (error) {
    console.error('获取用户总余额错误:', error);
    throw error;
  }
}

/**
 * 获取用户余额信息
 * @param {number} userId - 用户ID
 * @param {Object} transaction - 事务对象（可选）
 * @returns {Promise<Object>} - 返回用户余额信息
 */
exports.getUserBalances = async (userId, transaction = null) => {
  try {
    const options = transaction ? { transaction } : {};

    // 查询用户信息
    const user = await User.findByPk(userId, {
      ...options,
      attributes: ['id', 'balance']
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 查询用户账户余额
    const accountBalances = await AccountBalance.findAll({
      where: { user_id: userId },
      ...options
    });

    // 提取收入账户和充值账户余额
    let incomeBalance = 0;
    let depositBalance = 0;

    accountBalances.forEach(account => {
      if (account.account_type === 'income') {
        incomeBalance = parseFloat(account.balance);
      } else if (account.account_type === 'deposit') {
        depositBalance = parseFloat(account.balance);
      }
    });

    // 计算总余额（使用统一的方法）
    const totalBalance = await exports.getTotalBalance(userId, transaction);

    return {
      userId,
      mainBalance: parseFloat(user.balance),
      incomeBalance,
      depositBalance,
      totalBalance,
      accountBalances
    };
  } catch (error) {
    console.error('获取用户余额信息错误:', error);
    throw error;
  }
};

/**
 * 调整用户余额
 * @param {number} userId - 用户ID
 * @param {string} accountType - 账户类型 ('income' 或 'deposit')
 * @param {string} currency - 货币类型 (默认 'CNY')
 * @param {number} amount - 调整金额 (正数为增加，负数为减少)
 * @param {string} type - 操作类型 ('add' 或 'subtract')
 * @param {string} transactionType - 交易类型
 * @param {string} description - 交易描述
 * @param {number|null} referenceId - 关联记录ID (可选)
 * @param {string|null} referenceType - 关联记录类型 (可选)
 * @param {Object} transaction - 事务对象 (可选)
 * @returns {Promise<Object>} - 返回调整结果
 */
exports.adjustBalance = async (
  userId,
  accountType,
  amount,
  type,
  transactionType,
  description,
  referenceId = null,
  referenceType = null,
  transaction = null
) => {
  // 验证参数
  if (!userId || !accountType || !amount || !type || !transactionType) {
    throw new Error('缺少必要参数');
  }

  if (!['income', 'deposit'].includes(accountType)) {
    throw new Error('账户类型无效');
  }

  if (!['add', 'subtract'].includes(type)) {
    throw new Error('操作类型无效');
  }

  const numAmount = Math.abs(parseFloat(amount));
  if (isNaN(numAmount) || numAmount <= 0) {
    throw new Error('金额必须是大于0的数字');
  }

  // 创建新事务或使用传入的事务
  const t = transaction || await sequelize.transaction();

  try {
    // 查找用户
    const user = await User.findByPk(userId, { transaction: t, lock: true });
    if (!user) {
      if (!transaction) await t.rollback();
      throw new Error('用户不存在');
    }

    // 查找或创建账户余额记录
    let accountBalance = await AccountBalance.findOne({
      where: {
        user_id: userId,
        account_type: accountType
      },
      transaction: t,
      lock: true
    });

    if (!accountBalance) {
      accountBalance = await AccountBalance.create({
        user_id: userId,
        account_type: accountType,
        balance: 0
      }, { transaction: t });
    }

    // 保存交易前余额
    const oldBalance = parseFloat(accountBalance.balance);

    // 获取用户的总余额（交易前）
    const beforeTotalBalance = await exports.getTotalBalance(userId, t);

    // 计算新余额
    let newBalance = 0;
    if (type === 'add') {
      newBalance = oldBalance + numAmount;
    } else {
      if (oldBalance < numAmount) {
        if (!transaction) await t.rollback();
        console.error(`余额不足: 用户ID=${userId}, 账户类型=${accountType}, 当前余额=${oldBalance}, 需要金额=${numAmount}`);
        throw new Error(`用户余额不足: 当前余额${oldBalance}, 需要${numAmount}`);
      }
      newBalance = oldBalance - numAmount;
    }

    // 更新账户余额
    accountBalance.balance = newBalance;
    await accountBalance.save({ transaction: t });

    // 更新用户主余额（如果是充值账户）
    if (accountType === 'deposit') {
      user.balance = newBalance;
      await user.save({ transaction: t });
    }

    // 获取用户的总余额（交易后）- 在更新账户余额之后获取
    const afterTotalBalance = await exports.getTotalBalance(userId, t);

    // 导入订单号生成工具
    const { generateOrderNumber } = require('../utils/orderUtils');

    // 生成订单号
    const orderNumber = generateOrderNumber(transactionType.substring(0, 2).toUpperCase());

    // 创建交易记录 - 使用实际的交易前余额和交易后余额
    const transactionRecord = await Transaction.create({
      user_id: userId,
      order_number: orderNumber,
      type: transactionType,
      amount: type === 'add' ? numAmount : -numAmount, // 扣除类型的金额为负数
      before_balance: beforeTotalBalance,
      balance: afterTotalBalance, // 使用从数据库获取的实际交易后余额
      currency: 'CNY',
      status: 'success',
      reference_id: referenceId,
      reference_type: referenceType,
      description: description || `${type === 'add' ? '增加' : '减少'}余额 ${numAmount}`
    }, { transaction: t });

    // 如果使用的是新创建的事务，则提交事务
    if (!transaction) await t.commit();

    return {
      success: true,
      userId,
      accountType,
      oldBalance,
      newBalance,
      amount: numAmount,
      transactionId: transactionRecord.id
    };
  } catch (error) {
    // 如果使用的是新创建的事务，则回滚事务
    if (!transaction) {
      try {
        await t.rollback();
        console.log('事务已回滚');
      } catch (rollbackError) {
        console.error('事务回滚失败:', rollbackError);
      }
    }
    console.error('调整用户余额错误:', error);
    console.error('错误详情:', {
      userId,
      accountType,
      amount: numAmount, // 使用numAmount而不是amount
      type,
      transactionType,
      errorMessage: error.message,
      errorStack: error.stack
    });
    throw error;
  }
};
