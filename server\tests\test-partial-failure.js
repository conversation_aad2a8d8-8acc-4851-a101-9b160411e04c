/**
 * 部分失败测试
 * 测试方案3.1在部分补发失败时的处理机制
 */

const { Investment, Project, User, InvestmentProfit } = require('../models');
const sequelize = require('../config/database');
const profitSystem = require('../services/profitSystem');
const logger = require('../utils/logger');

/**
 * 模拟数据库连接失败
 */
const simulateDatabaseFailure = () => {
  const originalQuery = sequelize.query;
  let failureCount = 0;
  const maxFailures = 2; // 前2次查询失败
  
  sequelize.query = function(...args) {
    failureCount++;
    if (failureCount <= maxFailures && args[0].includes('INSERT INTO `investment_profits`')) {
      throw new Error('模拟数据库连接失败');
    }
    return originalQuery.apply(this, args);
  };
  
  return () => {
    sequelize.query = originalQuery; // 恢复原始函数
  };
};

/**
 * 模拟用户余额不足（通过临时修改余额）
 */
const simulateInsufficientBalance = async (userId) => {
  const user = await User.findByPk(userId);
  const originalBalance = user.balance;
  
  // 临时设置余额为负数，模拟余额不足
  await User.update({ balance: -1000 }, { where: { id: userId } });
  
  return async () => {
    // 恢复原始余额
    await User.update({ balance: originalBalance }, { where: { id: userId } });
  };
};

/**
 * 测试1：设置多笔遗漏收益场景
 */
const testSetupMultipleMissedProfits = async () => {
  console.log('\n=== 测试1：设置多笔遗漏收益场景 ===');
  
  try {
    // 找到一个活跃投资
    const investment = await Investment.findOne({
      where: { status: 'active' },
      include: [
        { model: Project, as: 'project' },
        { model: User, as: 'user' }
      ]
    });
    
    if (!investment) {
      console.log('❌ 没有找到活跃投资');
      return false;
    }
    
    console.log(`✅ 找到测试投资: ${investment.id}`);
    console.log(`   - 用户: ${investment.user.username} (ID: ${investment.user.id})`);
    console.log(`   - 项目: ${investment.project.name} (收益周期: ${investment.project.profit_time}小时)`);
    
    // 记录原始状态
    global.testInvestmentFailure = investment;
    global.originalLastProfitTime = investment.last_profit_time;
    global.originalUserBalance = investment.user.balance;
    
    // 设置最后收益时间为5个周期前，模拟遗漏4笔收益
    const now = new Date();
    const profitCycleMs = investment.project.profit_time * 60 * 60 * 1000;
    const missedProfitTime = new Date(now.getTime() - 5 * profitCycleMs);
    
    await Investment.update(
      { last_profit_time: missedProfitTime },
      { where: { id: investment.id } }
    );
    
    console.log(`✅ 多笔遗漏收益场景设置完成`);
    console.log(`   - 设置最后收益时间: ${missedProfitTime.toISOString()}`);
    console.log(`   - 当前时间: ${now.toISOString()}`);
    console.log(`   - 预期遗漏收益: 4笔`);
    
    global.missedProfitTime = missedProfitTime;
    global.expectedMissedCount = 4;
    
    return true;
  } catch (error) {
    console.log('❌ 设置多笔遗漏收益场景失败:', error.message);
    return false;
  }
};

/**
 * 测试2：记录补发前状态
 */
const testRecordBeforeState = async () => {
  console.log('\n=== 测试2：记录补发前状态 ===');
  
  try {
    const investment = global.testInvestmentFailure;
    
    // 记录用户当前余额
    const userBefore = await User.findByPk(investment.user.id);
    global.userBalanceBefore = userBefore.balance;
    
    // 记录当前收益记录数量
    const profitCountBefore = await InvestmentProfit.count({
      where: { investment_id: investment.id }
    });
    global.profitCountBefore = profitCountBefore;
    
    console.log(`✅ 补发前状态记录完成`);
    console.log(`   - 用户余额: ${userBefore.balance}`);
    console.log(`   - 收益记录数量: ${profitCountBefore}`);
    console.log(`   - 预期补发: ${global.expectedMissedCount} 笔`);
    
    return true;
  } catch (error) {
    console.log('❌ 记录补发前状态失败:', error.message);
    return false;
  }
};

/**
 * 测试3：模拟部分补发失败
 */
const testPartialCompensationFailure = async () => {
  console.log('\n=== 测试3：模拟部分补发失败 ===');
  
  try {
    const investment = global.testInvestmentFailure;
    
    console.log('🔄 模拟部分补发失败场景...');
    
    // 模拟数据库连接失败（前2次INSERT失败）
    const restoreDatabase = simulateDatabaseFailure();
    
    console.log('   - 已设置数据库失败模拟（前2次INSERT失败）');
    
    // 执行Redis重建（会触发自动补发）
    const rebuildResult = await profitSystem.simpleRedisRebuild(investment, investment.project);
    
    // 恢复数据库函数
    restoreDatabase();
    
    console.log(`📊 Redis重建结果:`);
    console.log(`   - 成功: ${rebuildResult.success}`);
    console.log(`   - 补发数量: ${rebuildResult.compensated || 0}`);
    console.log(`   - 下次收益时间: ${rebuildResult.nextProfitTime}`);
    
    // 验证部分补发结果
    const userAfter = await User.findByPk(investment.user.id);
    const profitCountAfter = await InvestmentProfit.count({
      where: { investment_id: investment.id }
    });
    
    const balanceChange = userAfter.balance - global.userBalanceBefore;
    const profitChange = profitCountAfter - global.profitCountBefore;
    
    console.log(`📈 部分补发结果:`);
    console.log(`   - 用户余额变化: ${balanceChange}`);
    console.log(`   - 收益记录变化: ${profitChange}`);
    console.log(`   - 成功补发: ${rebuildResult.compensated || 0} 笔`);
    console.log(`   - 失败补发: ${global.expectedMissedCount - (rebuildResult.compensated || 0)} 笔`);
    
    global.partialResult = {
      compensated: rebuildResult.compensated || 0,
      failed: global.expectedMissedCount - (rebuildResult.compensated || 0),
      balanceChange,
      profitChange
    };
    
    // 验证部分成功的逻辑
    if (global.partialResult.compensated > 0 && global.partialResult.failed > 0) {
      console.log('✅ 部分补发成功，部分失败（符合预期）');
    } else if (global.partialResult.compensated === global.expectedMissedCount) {
      console.log('⚠️  所有补发都成功了（可能模拟失败没有生效）');
    } else if (global.partialResult.compensated === 0) {
      console.log('⚠️  所有补发都失败了');
    }
    
    return true;
  } catch (error) {
    console.log('❌ 模拟部分补发失败测试失败:', error.message);
    return false;
  }
};

/**
 * 测试4：验证数据一致性
 */
const testDataConsistency = async () => {
  console.log('\n=== 测试4：验证数据一致性 ===');
  
  try {
    const investment = global.testInvestmentFailure;
    
    console.log('🔍 验证数据一致性...');
    
    // 检查用户余额与收益记录的一致性
    const userCurrent = await User.findByPk(investment.user.id);
    const profitRecords = await InvestmentProfit.findAll({
      where: { investment_id: investment.id },
      order: [['profit_time', 'ASC']]
    });
    
    // 计算理论余额增加
    const actualProfitIncrease = global.partialResult.profitChange;
    const expectedBalanceIncrease = actualProfitIncrease * 10; // 每笔收益10元
    const actualBalanceIncrease = global.partialResult.balanceChange;
    
    console.log(`📊 数据一致性检查:`);
    console.log(`   - 实际收益记录增加: ${actualProfitIncrease} 笔`);
    console.log(`   - 预期余额增加: ${expectedBalanceIncrease}`);
    console.log(`   - 实际余额增加: ${actualBalanceIncrease}`);
    
    if (Math.abs(actualBalanceIncrease - expectedBalanceIncrease) < 0.01) {
      console.log('✅ 用户余额与收益记录一致');
    } else {
      console.log('❌ 用户余额与收益记录不一致');
      return false;
    }
    
    // 检查投资的最后收益时间是否正确更新
    const updatedInvestment = await Investment.findByPk(investment.id);
    const lastProfitTime = new Date(updatedInvestment.last_profit_time);
    const originalTime = new Date(global.missedProfitTime);
    
    if (lastProfitTime > originalTime) {
      console.log('✅ 投资的最后收益时间已正确更新');
      console.log(`   - 更新后时间: ${lastProfitTime.toISOString()}`);
    } else {
      console.log('❌ 投资的最后收益时间未正确更新');
      return false;
    }
    
    // 检查是否有重复的收益记录
    const duplicateCheck = await sequelize.query(`
      SELECT investment_id, profit_time, COUNT(*) as count 
      FROM investment_profits 
      WHERE investment_id = ${investment.id}
      GROUP BY investment_id, profit_time 
      HAVING COUNT(*) > 1
    `, { type: sequelize.QueryTypes.SELECT });
    
    if (duplicateCheck.length === 0) {
      console.log('✅ 没有重复的收益记录');
    } else {
      console.log(`❌ 发现 ${duplicateCheck.length} 组重复收益记录`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 验证数据一致性失败:', error.message);
    return false;
  }
};

/**
 * 测试5：测试错误恢复机制
 */
const testErrorRecovery = async () => {
  console.log('\n=== 测试5：测试错误恢复机制 ===');
  
  try {
    const investment = global.testInvestmentFailure;
    
    console.log('🔄 测试错误恢复机制...');
    
    // 再次执行Redis重建，看是否能补发之前失败的收益
    console.log('   - 再次执行Redis重建（无故障模拟）');
    
    const userBefore = await User.findByPk(investment.user.id);
    const profitCountBefore = await InvestmentProfit.count({
      where: { investment_id: investment.id }
    });
    
    const recoveryResult = await profitSystem.simpleRedisRebuild(investment, investment.project);
    
    const userAfter = await User.findByPk(investment.user.id);
    const profitCountAfter = await InvestmentProfit.count({
      where: { investment_id: investment.id }
    });
    
    const recoveryBalanceChange = userAfter.balance - userBefore.balance;
    const recoveryProfitChange = profitCountAfter - profitCountBefore;
    
    console.log(`📊 错误恢复结果:`);
    console.log(`   - 成功: ${recoveryResult.success}`);
    console.log(`   - 补发数量: ${recoveryResult.compensated || 0}`);
    console.log(`   - 用户余额变化: ${recoveryBalanceChange}`);
    console.log(`   - 收益记录变化: ${recoveryProfitChange}`);
    
    // 计算总补发情况
    const totalCompensated = global.partialResult.compensated + (recoveryResult.compensated || 0);
    const totalFailed = global.expectedMissedCount - totalCompensated;
    
    console.log(`📈 总体补发情况:`);
    console.log(`   - 预期补发: ${global.expectedMissedCount} 笔`);
    console.log(`   - 实际补发: ${totalCompensated} 笔`);
    console.log(`   - 仍然失败: ${totalFailed} 笔`);
    
    if (totalCompensated >= global.expectedMissedCount) {
      console.log('✅ 错误恢复成功，所有遗漏收益已补发');
    } else if (totalCompensated > global.partialResult.compensated) {
      console.log('✅ 错误恢复部分成功，补发了部分遗漏收益');
    } else {
      console.log('⚠️  错误恢复未产生额外补发');
    }
    
    global.totalCompensated = totalCompensated;
    
    return true;
  } catch (error) {
    console.log('❌ 测试错误恢复机制失败:', error.message);
    return false;
  }
};

/**
 * 清理测试数据
 */
const cleanupTestData = async () => {
  try {
    const investment = global.testInvestmentFailure;
    
    if (investment && global.originalLastProfitTime !== undefined) {
      // 恢复原始的最后收益时间
      await Investment.update(
        { last_profit_time: global.originalLastProfitTime },
        { where: { id: investment.id } }
      );
      
      console.log('✅ 投资状态已恢复');
    }
    
    // 清理测试期间创建的收益记录
    if (global.totalCompensated > 0) {
      const testProfits = await InvestmentProfit.findAll({
        where: {
          investment_id: investment.id,
          profit_time: {
            [sequelize.Op.gte]: global.missedProfitTime,
            [sequelize.Op.lt]: new Date()
          }
        },
        order: [['created_at', 'DESC']],
        limit: global.totalCompensated
      });
      
      if (testProfits.length > 0) {
        const profitIds = testProfits.map(p => p.id);
        await InvestmentProfit.destroy({ where: { id: profitIds } });
        console.log(`✅ 清理了 ${testProfits.length} 条测试收益记录`);
      }
    }
    
  } catch (error) {
    console.log('⚠️  清理测试数据时出错:', error.message);
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('开始执行部分失败测试...\n');
  
  let passedTests = 0;
  const totalTests = 5;
  
  try {
    // 执行测试
    if (await testSetupMultipleMissedProfits()) passedTests++;
    if (await testRecordBeforeState()) passedTests++;
    if (await testPartialCompensationFailure()) passedTests++;
    if (await testDataConsistency()) passedTests++;
    if (await testErrorRecovery()) passedTests++;
    
  } catch (error) {
    console.log('\n❌ 测试执行过程中出现错误:', error.message);
  } finally {
    // 清理测试数据
    await cleanupTestData();
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有部分失败测试通过！');
    console.log('✅ 方案3.1的错误处理机制工作正常');
    console.log('✅ 部分失败不影响其他补发操作');
    return true;
  } else {
    console.log('⚠️  部分测试失败，请检查错误处理逻辑');
    return false;
  }
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testSetupMultipleMissedProfits,
  testRecordBeforeState,
  testPartialCompensationFailure,
  testDataConsistency,
  testErrorRecovery,
  cleanupTestData
};
