'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查索引是否已存在
      const [indexes] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investments WHERE column_name = 'last_profit_time'"
      );

      if (indexes.length === 0) {
        // 添加索引到investments表的last_profit_time字段
        await queryInterface.addIndex('investments', ['last_profit_time'], {
          name: 'investments_last_profit_time_idx'
        });
        console.log('添加索引到investments表的last_profit_time字段');
      } else {
        console.log('索引investments_last_profit_time_idx已存在，跳过');
      }

      // 检查investment_profits表的索引
      const [profitIndexes1] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investment_profits WHERE column_name = 'investment_id'"
      );

      if (profitIndexes1.length === 0) {
        // 添加索引到investment_profits表
        await queryInterface.addIndex('investment_profits', ['investment_id'], {
          name: 'investment_profits_investment_id_idx'
        });
        console.log('添加索引到investment_profits表的investment_id字段');
      } else {
        console.log('索引investment_profits_investment_id_idx已存在，跳过');
      }

      const [profitIndexes2] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investment_profits WHERE column_name = 'user_id'"
      );

      if (profitIndexes2.length === 0) {
        await queryInterface.addIndex('investment_profits', ['user_id'], {
          name: 'investment_profits_user_id_idx'
        });
        console.log('添加索引到investment_profits表的user_id字段');
      } else {
        console.log('索引investment_profits_user_id_idx已存在，跳过');
      }

      const [profitIndexes3] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investment_profits WHERE column_name = 'created_at'"
      );

      if (profitIndexes3.length === 0) {
        await queryInterface.addIndex('investment_profits', ['created_at'], {
          name: 'investment_profits_created_at_idx'
        });
        console.log('添加索引到investment_profits表的created_at字段');
      } else {
        console.log('索引investment_profits_created_at_idx已存在，跳过');
      }

      console.log('索引添加成功');
      return Promise.resolve();
    } catch (error) {
      console.error('添加索引失败:', error);
      return Promise.reject(error);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查索引是否存在，然后删除
      const [indexes] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investments WHERE column_name = 'last_profit_time'"
      );

      if (indexes.length > 0) {
        // 删除investments表的索引
        await queryInterface.removeIndex('investments', 'investments_last_profit_time_idx');
        console.log('删除索引investments_last_profit_time_idx');
      }

      // 检查investment_profits表的索引
      const [profitIndexes1] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investment_profits WHERE column_name = 'investment_id'"
      );

      if (profitIndexes1.length > 0) {
        await queryInterface.removeIndex('investment_profits', 'investment_profits_investment_id_idx');
        console.log('删除索引investment_profits_investment_id_idx');
      }

      const [profitIndexes2] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investment_profits WHERE column_name = 'user_id'"
      );

      if (profitIndexes2.length > 0) {
        await queryInterface.removeIndex('investment_profits', 'investment_profits_user_id_idx');
        console.log('删除索引investment_profits_user_id_idx');
      }

      const [profitIndexes3] = await queryInterface.sequelize.query(
        "SHOW INDEXES FROM investment_profits WHERE column_name = 'created_at'"
      );

      if (profitIndexes3.length > 0) {
        await queryInterface.removeIndex('investment_profits', 'investment_profits_created_at_idx');
        console.log('删除索引investment_profits_created_at_idx');
      }

      console.log('索引删除成功');
      return Promise.resolve();
    } catch (error) {
      console.error('删除索引失败:', error);
      return Promise.reject(error);
    }
  }
};
