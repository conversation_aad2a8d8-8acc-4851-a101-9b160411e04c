/**
 * 统计数据相关API
 */
import request from '../request';

/**
 * 获取首页统计数据
 * @returns {Promise<Object>} 首页统计数据
 */
export function getHomeStats() {
  return request({
    url: '/api/mobile/stats/home',
    method: 'GET'
  });
}

/**
 * 获取账户统计数据
 * @param {Object} params 查询参数
 * @param {string} [params.period='today'] 统计周期，可选值："today"(今天)、"week"(本周)、"month"(本月)、"all"(全部)
 * @returns {Promise<Object>} 账户统计数据
 */
export function getAccountStats(params = {}) {
  return request({
    url: '/api/mobile/stats/account',
    method: 'GET',
    params
  });
}
