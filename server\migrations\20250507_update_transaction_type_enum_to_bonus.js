'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 修改 type 列的类型，确保使用 bonus 而不是 increase
    await queryInterface.sequelize.query(`
      ALTER TABLE transactions
      MODIFY COLUMN type ENUM('deposit', 'withdrawal', 'investment', 'investment_gift', 'investment_purchase', 'profit', 'commission', 'bonus', 'deduction')
      NOT NULL
      COMMENT '交易类型：deposit=充值, withdrawal=提现, investment=投资, investment_gift=赠送投资, investment_purchase=购买投资, profit=收益, commission=佣金, bonus=赠金, deduction=扣除'
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // 不需要回滚操作，因为我们只是确保使用正确的枚举类型
  }
};
