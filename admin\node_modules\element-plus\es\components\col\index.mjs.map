{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/col/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Col from './src/col.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCol: SFCWithInstall<typeof Col> = withInstall(Col)\nexport default ElCol\n\nexport * from './src/col'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,KAAK,GAAG,WAAW,CAAC,GAAG;;;;"}