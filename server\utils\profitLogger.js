/**
 * 收益日志工具
 * 提供专门的收益日志记录接口
 */
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// 确保日志目录存在
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 创建日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// 创建控制台输出格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ level, message, timestamp, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
      if (meta.stack) {
        metaStr = `\n${meta.stack}`;
      } else {
        try {
          metaStr = `\n${JSON.stringify(meta, null, 2)}`;
        } catch (e) {
          metaStr = `\n[无法序列化的元数据]`;
        }
      }
    }
    return `${timestamp} ${level}: ${message}${metaStr}`;
  })
);

// 创建收益日志记录器
const profitLogger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'warn',
  format: logFormat,
  defaultMeta: { service: 'fox-profit' },
  transports: [
    // 写入所有收益日志到 profit.log
    new winston.transports.File({ 
      filename: path.join(logDir, 'profit.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
    }),
    // 写入错误日志到 profit_error.log
    new winston.transports.File({ 
      filename: path.join(logDir, 'profit_error.log'), 
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// 非生产环境下，同时输出到控制台
if (process.env.NODE_ENV !== 'production') {
  profitLogger.add(new winston.transports.Console({
    format: consoleFormat,
  }));
}

// 添加自定义日志方法
profitLogger.firstProfit = function(investmentId, message, meta = {}) {
  this.info(`[首次收益] 投资ID ${investmentId}: ${message}`, { ...meta, logType: 'first_profit', investmentId });
};

profitLogger.profitTask = function(investmentId, message, meta = {}) {
  this.info(`[收益任务] 投资ID ${investmentId}: ${message}`, { ...meta, logType: 'profit_task', investmentId });
};

profitLogger.profitCalc = function(investmentId, message, meta = {}) {
  this.info(`[收益计算] 投资ID ${investmentId}: ${message}`, { ...meta, logType: 'profit_calc', investmentId });
};

profitLogger.profitDist = function(investmentId, message, meta = {}) {
  this.info(`[收益发放] 投资ID ${investmentId}: ${message}`, { ...meta, logType: 'profit_dist', investmentId });
};

profitLogger.profitComp = function(investmentId, message, meta = {}) {
  this.info(`[收益补偿] 投资ID ${investmentId}: ${message}`, { ...meta, logType: 'profit_comp', investmentId });
};

module.exports = profitLogger;
