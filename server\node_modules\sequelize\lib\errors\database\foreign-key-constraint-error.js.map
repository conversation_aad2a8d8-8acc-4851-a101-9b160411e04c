{"version": 3, "sources": ["../../../src/errors/database/foreign-key-constraint-error.ts"], "sourcesContent": ["import DatabaseError, { DatabaseErrorSubclassOptions } from '../database-error';\n\nexport enum RelationshipType {\n  parent = 'parent',\n  child = 'child',\n}\n\ninterface ForeignKeyConstraintErrorOptions {\n  table?: string;\n  fields?: { [field: string]: string };\n  value?: unknown;\n  index?: string;\n  reltype?: RelationshipType;\n}\n\n/**\n * Thrown when a foreign key constraint is violated in the database\n */\nclass ForeignKeyConstraintError extends DatabaseError {\n  table: string | undefined;\n  fields: { [field: string]: string } | undefined;\n  value: unknown;\n  index: string | undefined;\n  reltype: RelationshipType | undefined;\n\n  constructor(\n    options: ForeignKeyConstraintErrorOptions & DatabaseErrorSubclassOptions\n  ) {\n    options = options || {};\n    options.parent = options.parent || { sql: '', name: '', message: '' };\n\n    super(options.parent, { stack: options.stack });\n    this.name = 'SequelizeForeignKeyConstraintError';\n\n    this.message =\n      options.message || options.parent.message || 'Database Error';\n    this.fields = options.fields;\n    this.table = options.table;\n    this.value = options.value;\n    this.index = options.index;\n    this.reltype = options.reltype;\n  }\n}\n\nexport default ForeignKeyConstraintError;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA,4BAA4D;AAErD,IAAK,mBAAL,kBAAK,sBAAL;AACL,gCAAS;AACT,+BAAQ;AAFE;AAAA;AAgBZ,wCAAwC,8BAAc;AAAA,EAOpD,YACE,SACA;AACA,cAAU,WAAW;AACrB,YAAQ,SAAS,QAAQ,UAAU,EAAE,KAAK,IAAI,MAAM,IAAI,SAAS;AAEjE,UAAM,QAAQ,QAAQ,EAAE,OAAO,QAAQ;AAZzC;AACA;AACA;AACA;AACA;AASE,SAAK,OAAO;AAEZ,SAAK,UACH,QAAQ,WAAW,QAAQ,OAAO,WAAW;AAC/C,SAAK,SAAS,QAAQ;AACtB,SAAK,QAAQ,QAAQ;AACrB,SAAK,QAAQ,QAAQ;AACrB,SAAK,QAAQ,QAAQ;AACrB,SAAK,UAAU,QAAQ;AAAA;AAAA;AAI3B,IAAO,uCAAQ;", "names": []}