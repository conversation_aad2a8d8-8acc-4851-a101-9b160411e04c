{
    <%@page import="cn.test.utils.SignUtil"%>
<%@page import="java.util.Map"%>
<%@page import="java.util.LinkedHashMap"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="java.io.*"%>
<%@ page import="cn.test.utils.SignAPI"%>

<%
    String merchant_key = "";

    request.setCharacterEncoding("UTF-8");
    String tradeResult = (String) request.getParameter("tradeResult");
    String merTransferId = (String) request.getParameter("merTransferId");
    String merNo = (String) request.getParameter("merNo");
    String tradeNo = (String) request.getParameter("tradeNo");
    String transferAmount = (String) request.getParameter("transferAmount");
    String sign = (String) request.getParameter("sign");
    String signType = (String) request.getParameter("signType");
    String applyDate = (String) request.getParameter("applyDate");
    String version = (String) request.getParameter("version");
    String respCode = (String) request.getParameter("respCode");

    System.out.println("tradeResult = " + tradeResult + "\n" + "merTransferId = " + merTransferId + "\n"
            + "merNo = " + merNo + "\n" + "tradeNo = " + tradeNo + "\n" + "transferAmount = " + transferAmount
            + "\n" + "sign = " + sign + "\n" + "signType = " + signType + "\n" + "applyDate = " + applyDate
            + "\n" + "signType = " + signType + "\n" + "version = " + version + "\n" + "respCode = " + respCode
            + "\n");

    Map<String, String> signStr = new LinkedHashMap<String, String>();
    signStr.put("tradeResult", tradeResult);
    signStr.put("merTransferId", merTransferId);
    signStr.put("merNo", merNo);
    signStr.put("tradeNo", tradeNo);
    signStr.put("transferAmount", transferAmount);
    signStr.put("applyDate", applyDate);
    signStr.put("version", version);
    signStr.put("respCode", respCode);

    String signInfo = SignUtil.sortData(signStr);
    System.out.println(signInfo.length() + " -->" + signInfo);
    System.out.println(sign.length() + " -->" + sign);
    boolean result = false;

    if ("MD5".equals(signType)) {
        result = SignAPI.validateSignByKey(signInfo, merchant_key, sign);
    }

    PrintWriter pw = response.getWriter();
    if (result) {
        pw.print("success");
        pw.flush();
        pw.close(); // 验签成功，响应SUCCESS 
        System.out.println("验签结果result的值：" + result + " -->SUCCESS");
    } else {
        pw.print("Signature Error");
        pw.flush();
        pw.close(); // 验签失败，业务结束  
        System.out.println("验签结果result的值：" + result + " -->Signature Error");
    }
    System.out.println(
            "---------------------------------------------------------------------------------------------------------------------------------------------");
%>

    }
  }