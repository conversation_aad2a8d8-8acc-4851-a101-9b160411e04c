# Login to get token
$loginBody = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/admin/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    $token = ($loginResponse.Content | ConvertFrom-Json).data.token

    Write-Host "Login successful, got token: $token"

    # Use token to trigger compensation check
    $headers = @{
        Authorization = "Bearer $token"
    }

    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/admin/schedulers/compensation-check" -Method Post -Headers $headers
    Write-Host "Compensation check triggered successfully"
    Write-Host "Response status code: $($response.StatusCode)"
    Write-Host "Response content: $($response.Content)"
}
catch {
    Write-Host "Error: $_"
    if ($_.Exception.Response) {
        Write-Host "Status code: $($_.Exception.Response.StatusCode.value__)"
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $reader.BaseStream.Position = 0
            $reader.DiscardBufferedData()
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response content: $responseBody"
        }
        catch {
            Write-Host "Unable to read response content"
        }
    }
}
