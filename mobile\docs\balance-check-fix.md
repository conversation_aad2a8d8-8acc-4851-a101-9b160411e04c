# 余额检查功能修复记录

## 修复概述

修复移动端产品购买时余额检查功能的两个关键问题：API函数名错误和余额不足错误识别失败。

## 修复时间
2025-05-25

## 🔍 问题分析

### 问题1：getUserProfile函数不存在
**错误信息**：
```
TypeError: getUserProfile is not a function
```

**问题原因**：
- 在`mobile/pages/product/detail.vue`中使用了错误的API函数名
- 正确的函数名应该是`getProfile`而不是`getUserProfile`

### 问题2：余额不足错误识别失败
**错误现象**：
- 后端返回`{code: 400, message: '余额不足', data: null}`
- 前端没有正确识别为余额不足错误
- 显示了通用的"Please check your input and try again"错误提示

**问题原因**：
- `request.js`中的400错误处理逻辑过于简单
- 没有区分输入验证失败和余额不足等不同的业务错误
- 错误对象结构不完整，缺少响应数据

## 🔧 修复内容

### 1. 修复API函数名错误

#### 文件：`mobile/pages/product/detail.vue`

**修复前**：
```javascript
const { getUserProfile } = await import('../../services/api/user.js');
const userProfile = await getUserProfile();
```

**修复后**：
```javascript
const { getProfile } = await import('../../services/api/user.js');
const userProfile = await getProfile();
```

**同时优化了余额字段处理**：
```javascript
this.userBalance = {
  total: parseFloat(userProfile.data.total_balance) || parseFloat(userProfile.data.balance) || 0,
  deposit: parseFloat(userProfile.data.deposit_balance) || 0,
  income: parseFloat(userProfile.data.income_balance) || 0
};
```

### 2. 增强余额不足错误识别

#### 文件：`mobile/pages/product/detail.vue`

**修复前**：
```javascript
isInsufficientBalanceError(error) {
  if (error.message && (error.message.includes('余额不足') || error.message.includes('Insufficient balance'))) {
    return true;
  }
  if (error.response && error.response.data && error.response.data.message) {
    const message = error.response.data.message;
    return message.includes('余额不足') || message.includes('Insufficient balance');
  }
  return false;
}
```

**修复后**：
```javascript
isInsufficientBalanceError(error) {
  // 检查错误消息
  if (error.message && (error.message.includes('余额不足') || error.message.includes('Insufficient balance'))) {
    return true;
  }
  
  // 检查响应数据中的消息
  if (error.response && error.response.data && error.response.data.message) {
    const message = error.response.data.message;
    return message.includes('余额不足') || message.includes('Insufficient balance');
  }
  
  // 检查原始错误对象（从request.js抛出的错误）
  if (error.originalError && error.originalError.response && error.originalError.response.data) {
    const message = error.originalError.response.data.message;
    return message && (message.includes('余额不足') || message.includes('Insufficient balance'));
  }
  
  return false;
}
```

### 3. 优化request.js错误处理

#### 文件：`mobile/utils/request.js`

**修复前**：
```javascript
// 处理400错误（输入验证失败）
if (statusCode === 400) {
  let errorMessage = 'Please check your input and try again';
  
  // 如果是输入验证失败，提供更友好的提示
  if (data?.message && data.message.includes('validation')) {
    errorMessage = 'Please enter valid username and password';
  }
  
  console.error('输入验证错误:', statusCode, data);
  
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  });

  return Promise.reject(new Error(errorMessage));
}
```

**修复后**：
```javascript
// 处理400错误（输入验证失败）
if (statusCode === 400) {
  let errorMessage = 'Please check your input and try again';
  
  // 如果是输入验证失败，提供更友好的提示
  if (data?.message && data.message.includes('validation')) {
    errorMessage = 'Please enter valid username and password';
  }
  // 如果是余额不足，直接使用原始错误信息，不显示Toast（让业务逻辑处理）
  else if (data?.message && (data.message.includes('余额不足') || data.message.includes('Insufficient balance'))) {
    console.error('余额不足错误:', statusCode, data);
    const error = new Error(data.message);
    error.response = { data: data, status: statusCode };
    return Promise.reject(error);
  }
  
  console.error('输入验证错误:', statusCode, data);
  
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  });

  return Promise.reject(new Error(errorMessage));
}
```

## 🎯 修复效果

### 修复前的问题
1. **API调用失败**：`getUserProfile is not a function`错误
2. **余额检查失败**：无法获取用户余额信息
3. **错误识别失败**：余额不足时显示通用错误提示
4. **用户体验差**：无法触发充值引导功能

### 修复后的效果
1. **✅ API调用成功**：正确调用`getProfile`函数获取用户信息
2. **✅ 余额检查正常**：成功获取用户的总余额、充值余额、收入余额
3. **✅ 错误识别准确**：正确识别"余额不足"错误
4. **✅ 充值引导触发**：余额不足时正确显示充值引导弹窗
5. **✅ 用户体验提升**：完整的余额不足处理流程

## 🔍 错误处理流程

### 新的错误处理流程
```
用户点击购买
    ↓
检查用户余额（getProfile API）
    ↓
发起购买请求
    ↓
后端返回400错误：{code: 400, message: '余额不足'}
    ↓
request.js识别为余额不足错误
    ↓
创建包含响应数据的错误对象
    ↓
产品页面的isInsufficientBalanceError识别成功
    ↓
显示充值引导弹窗
    ↓
用户选择充值 → 跳转充值页面
```

## 🔒 数据兼容性

### 余额字段兼容性处理
```javascript
// 支持多种余额字段格式
this.userBalance = {
  total: parseFloat(userProfile.data.total_balance) || parseFloat(userProfile.data.balance) || 0,
  deposit: parseFloat(userProfile.data.deposit_balance) || 0,
  income: parseFloat(userProfile.data.income_balance) || 0
};
```

### 错误消息兼容性
- 支持中文错误消息：`余额不足`
- 支持英文错误消息：`Insufficient balance`
- 支持多种错误对象结构

## 📋 测试验证

### 测试场景
1. **余额充足购买**：正常购买流程
2. **余额不足购买**：触发充值引导
3. **网络错误**：显示网络错误提示
4. **其他400错误**：显示相应的错误提示

### 验证结果
- ✅ API函数调用正常
- ✅ 余额信息获取成功
- ✅ 余额不足错误正确识别
- ✅ 充值引导弹窗正常显示
- ✅ 错误处理逻辑完善

## 🌍 国际化支持

### 错误消息处理
- **中文后端**：支持"余额不足"错误消息
- **英文前端**：显示英文充值引导界面
- **双语兼容**：同时支持中英文错误消息识别

## 总结

本次修复解决了余额检查功能的核心问题：

1. **✅ API函数名修复**：使用正确的`getProfile`函数
2. **✅ 余额字段兼容**：支持多种余额字段格式
3. **✅ 错误识别增强**：准确识别余额不足错误
4. **✅ 错误处理优化**：区分不同类型的400错误
5. **✅ 用户体验完善**：完整的余额不足处理流程

现在用户在购买产品时，如果余额不足，系统会：
1. 正确检查用户余额
2. 识别余额不足错误
3. 显示专业的充值引导弹窗
4. 提供完整的充值回跳流程

这大大提升了用户的购买体验和系统的可用性。
