/**
 * 日期工具类
 * 用于处理日期时间格式化和计算
 */
const moment = require('moment');

/**
 * 格式化日期时间
 * @param {Date|string} dateTime 日期时间
 * @param {string} format 格式
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(dateTime, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateTime) return '';
  // 方案5.1：直接格式化UTC时间，不进行本地转换
  return moment.utc(dateTime).format(format);
}

/**
 * 格式化日期
 * @param {Date|string} dateTime 日期时间
 * @returns {string} 格式化后的日期
 */
function formatDate(dateTime) {
  return formatDateTime(dateTime, 'YYYY-MM-DD');
}

/**
 * 格式化时间
 * @param {Date|string} dateTime 日期时间
 * @returns {string} 格式化后的时间
 */
function formatTime(dateTime) {
  return formatDateTime(dateTime, 'HH:mm:ss');
}

/**
 * 获取当前时间
 * @returns {Date} 当前时间
 */
function getCurrentTime() {
  return new Date(); // 保持不变，但存储时会转为UTC
}

/**
 * 获取当前UTC时间
 * @returns {Date} 当前UTC时间
 */
function getCurrentUTCTime() {
  return moment.utc().toDate();
}

/**
 * 获取今天的开始时间（UTC时区）
 * @returns {Date} 今天的开始时间
 */
function getTodayStart() {
  return moment.utc().startOf('day').toDate();
}

/**
 * 获取今天的结束时间（UTC时区）
 * @returns {Date} 今天的结束时间
 */
function getTodayEnd() {
  return moment.utc().endOf('day').toDate();
}

/**
 * 获取本周的开始时间（周一开始，UTC时区）
 * @returns {Date} 本周的开始时间
 */
function getWeekStart() {
  return moment.utc().startOf('isoWeek').toDate();
}

/**
 * 获取本周的结束时间（周日结束，UTC时区）
 * @returns {Date} 本周的结束时间
 */
function getWeekEnd() {
  return moment.utc().endOf('isoWeek').toDate();
}

/**
 * 获取本月的开始时间（UTC时区）
 * @returns {Date} 本月的开始时间
 */
function getMonthStart() {
  return moment.utc().startOf('month').toDate();
}

/**
 * 获取本月的结束时间（UTC时区）
 * @returns {Date} 本月的结束时间
 */
function getMonthEnd() {
  return moment.utc().endOf('month').toDate();
}

/**
 * 计算两个日期之间的差值
 * @param {Date|string} date1 日期1
 * @param {Date|string} date2 日期2
 * @param {string} unit 单位，可选值：years, months, weeks, days, hours, minutes, seconds
 * @returns {number} 差值
 */
function dateDiff(date1, date2, unit = 'days') {
  return moment(date1).diff(moment(date2), unit);
}

/**
 * 检查日期是否在指定范围内
 * @param {Date|string} date 日期
 * @param {Date|string} start 开始日期
 * @param {Date|string} end 结束日期
 * @returns {boolean} 是否在范围内
 */
function isDateInRange(date, start, end) {
  const m = moment(date);
  return m.isBetween(moment(start), moment(end), null, '[]');
}

/**
 * 获取指定时间段的开始和结束时间
 * @param {string} period 时间段，可选值：today, week, month, all
 * @returns {{start: Date, end: Date}} 开始和结束时间
 */
function getPeriodRange(period) {
  switch (period) {
    case 'today':
      return {
        start: getTodayStart(),
        end: getTodayEnd()
      };
    case 'week':
      return {
        start: getWeekStart(),
        end: getWeekEnd()
      };
    case 'month':
      return {
        start: getMonthStart(),
        end: getMonthEnd()
      };
    case 'all':
    default:
      return {
        start: new Date(0), // 1970-01-01
        end: new Date()
      };
  }
}

/**
 * 将日期添加指定时间
 * @param {Date|string} date 日期
 * @param {number} amount 数量
 * @param {string} unit 单位，可选值：years, months, weeks, days, hours, minutes, seconds
 * @returns {Date} 新日期
 */
function addTime(date, amount, unit = 'days') {
  return moment(date).add(amount, unit).toDate();
}

/**
 * 将日期减去指定时间
 * @param {Date|string} date 日期
 * @param {number} amount 数量
 * @param {string} unit 单位，可选值：years, months, weeks, days, hours, minutes, seconds
 * @returns {Date} 新日期
 */
function subtractTime(date, amount, unit = 'days') {
  return moment(date).subtract(amount, unit).toDate();
}

module.exports = {
  formatDateTime,
  formatDate,
  formatTime,
  getCurrentTime,
  getCurrentUTCTime, // 新增UTC时间获取方法
  getTodayStart,
  getTodayEnd,
  getWeekStart,
  getWeekEnd,
  getMonthStart,
  getMonthEnd,
  dateDiff,
  isDateInRange,
  getPeriodRange,
  addTime,
  subtractTime
};
