-- 添加订单号字段到交易表
ALTER TABLE transactions
ADD COLUMN order_number VARCHAR(50) AFTER user_id,
ADD UNIQUE INDEX idx_order_number (order_number);

-- 更新现有交易记录的订单号
-- 注意：这只是一个示例，实际执行时可能需要根据交易类型设置不同的前缀
UPDATE transactions
SET order_number = CONCAT(
  CASE 
    WHEN type = 'deposit' THEN 'RE'
    WHEN type = 'withdrawal' THEN 'WM'
    WHEN type = 'investment' THEN 'IV'
    WHEN type = 'profit' THEN 'PF'
    WHEN type = 'commission' THEN 'CM'
    WHEN type = 'bonus' THEN 'BN'
    WHEN type = 'deduction' THEN 'DD'
    ELSE ''
  END,
  DATE_FORMAT(created_at, '%Y%m%d%H%i'),
  LPAD(FLOOR(RAND() * 900000 + 100000), 6, '0')
)
WHERE order_number IS NULL;
