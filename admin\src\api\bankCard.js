import request from '@/utils/request';

// 获取银行卡列表
export function getBankCards(params) {
  return request({
    url: '/api/admin/bank-cards',
    method: 'get',
    params
  });
}

// 获取银行卡详情
export function getBankCard(id) {
  return request({
    url: `/api/admin/bank-cards/${id}`,
    method: 'get'
  });
}

// 创建银行卡
export function createBankCard(data) {
  return request({
    url: '/api/admin/bank-cards',
    method: 'post',
    data
  });
}

// 更新银行卡
export function updateBankCard(id, data) {
  return request({
    url: `/api/admin/bank-cards/${id}`,
    method: 'put',
    data
  });
}

// 删除银行卡
export function deleteBankCard(id) {
  return request({
    url: `/api/admin/bank-cards/${id}`,
    method: 'delete'
  });
}

// 批量删除银行卡
export function batchDeleteBankCards(ids) {
  return request({
    url: '/api/admin/bank-cards/batch',
    method: 'delete',
    data: { ids }
  });
}
