代收查询请求地址：https://pay.aiffpay.com/query/order
请求方式
POST
代付在代付成功未收到后台通知可调用此接口进行代付支付状态查询，
一般发起时间在代付发起之后的2-3分钟之后进行查询，频率为5分钟一次请求
Header：
参数名	必选	类型	说明
Content-Type	是	string	application/x-www-form-urlencoded
代收查询请求参数
参数值	参数名	是否必填	说明
mch_id	商户号	Y	平台分配唯一
mch_order_no	商家订单号	Y	支付使用的商家订单号
sign_type	签名方式	Y	固定值 MD5不参与签名
sign	签名	Y	不参与签名
请求参数签名串(如果非必填参数，只要提交的也要参与签名):
mch_id=123123666&mch_order_no=20190701170443&key=xxxxx
代收查询同步响应（返回 json 数据）
参数值	参数名	类型	是否必填	说明
respCode	响应状态	String	Y	SUCCESS：响应成功 FAIL:响应失败
tradeMsg	错误信息	String	Y
以下参数只有响应成功才有值
tradeResult	业务结果	String	C	0：未支付 1：支付成功 2：支付失败
mchId	商户号	String	C
mchOrderNo	商家订单号	String	C
oriAmount	原始订单金额	String	C	商家上传的订单金额
tradeAmount	交易金额	String	C	实际支付金额
orderDate	订单时间	String	C
orderNo	平台订单号	String	C
signType	签名方式	String	C	固定值 MD5，不参与签名
sign	签名	String	C	不参与签名