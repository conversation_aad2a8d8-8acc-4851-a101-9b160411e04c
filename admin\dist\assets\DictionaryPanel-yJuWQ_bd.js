/* empty css             *//* empty css                  *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                 */import{d as h,r as w,a6 as b,c as p,b as o,a8 as C,a9 as x,e as l,i as B,g as m,_ as g,o as E,w as d,al as I,ap as T,n as V,m as U,a0 as $,p as f}from"./index-LncY9lAB.js";const P={class:"key-value-table-wrapper"},D={class:"key-value-table"},F={class:"key-value-item"},M={class:"action-buttons"},N=["onClick"],z=["onClick"],K=h({__name:"KeyValueTable",props:{modelValue:{},title:{}},emits:["update:modelValue"],setup(k,{emit:_}){const r=k,y=_,t=w([...r.modelValue]);b(()=>r.modelValue,e=>{t.value=[...e]},{deep:!0}),b(t,e=>{y("update:modelValue",[...e])},{deep:!0});const v=e=>{const s={key:"",value:""};e!==void 0?t.value.splice(e+1,0,s):t.value.push(s)},n=e=>{t.value.length>1&&t.value.splice(e,1)};return(e,s)=>{const u=B;return m(),p("div",P,[o("table",D,[(m(!0),p(C,null,x(t.value,(a,c)=>(m(),p("tr",{key:c},[o("td",null,[o("div",F,[l(u,{modelValue:a.key,"onUpdate:modelValue":i=>a.key=i,placeholder:"键",size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue"]),l(u,{modelValue:a.value,"onUpdate:modelValue":i=>a.value=i,placeholder:"值",size:"small",style:{width:"80px",margin:"0 20px"}},null,8,["modelValue","onUpdate:modelValue"]),o("div",M,[o("button",{class:"btn-icon btn-delete",onClick:i=>n(c)},"×",8,N),o("button",{class:"btn-icon btn-add",onClick:i=>v(c)},"+",8,z)])])])]))),128))]),o("button",{class:"btn-new",onClick:s[0]||(s[0]=a=>v())},"新增")])}}}),L=g(K,[["__scopeId","data-v-0c805cb6"]]),j={class:"params-form"},q={class:"form-actions"},A=h({__name:"DictionaryPanel",setup(k){const _=w([{title:"分类类型",value:[{key:"default",value:"默认"},{key:"page",value:"单页"},{key:"article",value:"文章"}],key:"[site.categorytype]",type:"keyValue"},{title:"配置分组",value:[{key:"basic",value:"基础配置"},{key:"email",value:"邮件配置"},{key:"dictionary",value:"字典配置"},{key:"user",value:"会员配置"},{key:"rate",value:"资金配置"},{key:"other",value:"其他配置"}],key:"[site.configgroup]",type:"keyValue"},{title:"附件类别",value:[{key:"custom",value:"自定义"}],key:"[site.attachmentcategory]",type:"keyValue"}]),r=()=>{$.confirm("确定要重置所有设置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{t(),f.success("表单已重置")}).catch(()=>{})},y=()=>{f.success("设置已保存")},t=()=>{console.log("获取字典配置参数")};return E(()=>{t()}),(v,n)=>{const e=I,s=T,u=U;return m(),p("div",j,[l(s,{data:_.value,class:"param-table"},{default:d(()=>[l(e,{prop:"title",label:"变量标题",width:"150"}),l(e,{prop:"value",label:"变量值"},{default:d(a=>[l(L,{modelValue:a.row.value,"onUpdate:modelValue":c=>a.row.value=c,title:a.row.title},null,8,["modelValue","onUpdate:modelValue","title"])]),_:1}),l(e,{prop:"key",label:"变量名",width:"200"})]),_:1},8,["data"]),o("div",q,[l(u,{onClick:r},{default:d(()=>n[0]||(n[0]=[V("重置")])),_:1}),l(u,{type:"primary",onClick:y},{default:d(()=>n[1]||(n[1]=[V("保存")])),_:1})])])}}}),X=g(A,[["__scopeId","data-v-93c15e7d"]]);export{X as default};
