const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const BankChannelMapping = sequelize.define('BankChannelMapping', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  bank_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '银行ID',
  },
  payment_channel_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '支付通道ID',
  },
  payin_method: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '代收方式编号',
  },
  payout_method: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '代付方式编号',
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '状态：true=启用, false=禁用',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'bank_channel_mappings',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

module.exports = BankChannelMapping;
