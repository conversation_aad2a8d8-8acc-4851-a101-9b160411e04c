/**
 * 支付回调路由
 */
const express = require('express');
const router = express.Router();
const paymentCallbackController = require('../controllers/paymentCallbackController');

// KB支付回调 - 不需要验证token，因为是第三方支付平台回调
router.post('/kbpay', paymentCallbackController.handleKBPayCallback);

// KB支付代付回调 - 不需要验证token，因为是第三方支付平台回调
router.post('/kbpay-payout', paymentCallbackController.handleKBPayPayoutCallback);

// Base支付回调 - 不需要验证token，因为是第三方支付平台回调
router.post('/basepay', paymentCallbackController.handleBasePayCallback);

// Base支付代付回调 - 不需要验证token，因为是第三方支付平台回调
router.post('/basepay-payout', paymentCallbackController.handleBasePayPayoutCallback);

module.exports = router;
