/**
 * 支付回调控制器
 */
const { Deposit, User, Transaction, PaymentChannel, Withdrawal } = require('../models');
const { getKBPayService } = require('../services/kbPayService');
const { getBasePayService } = require('../services/basePayService');
const balanceService = require('../services/balanceService');
const commissionService = require('../services/commissionService');
const sequelize = require('../config/database');

/**
 * 处理KB支付回调
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 响应结果
 */
exports.handleKBPayCallback = async (req, res) => {
  try {
    console.log('收到KB支付回调:', req.body);

    // 获取KB支付服务
    const kbPayService = await getKBPayService();

    // 验证签名
    if (!kbPayService.verifyCallbackSignature(req.body)) {
      console.error('KB支付回调签名验证失败');
      return res.status(400).send('签名验证失败');
    }

    // 提取回调数据
    const {
      merchant_no,
      order_no,
      order_amount,
      trade_amount,
      trade_status,
      trade_no
    } = req.body;

    // 查找订单
    const deposit = await Deposit.findOne({
      where: { order_number: order_no },
      include: [
        {
          model: User,
          as: 'user'
        }
      ]
    });

    if (!deposit) {
      console.error(`KB支付回调: 订单 ${order_no} 不存在`);
      return res.status(404).send('订单不存在');
    }

    // 检查订单状态
    if (deposit.status !== 'pending') {
      console.log(`KB支付回调: 订单 ${order_no} 已处理，当前状态: ${deposit.status}`);
      return res.send('success'); // 返回成功，避免重复回调
    }

    // 检查支付状态
    if (parseInt(trade_status) !== 1) {
      console.log(`KB支付回调: 订单 ${order_no} 支付未完成，状态: ${trade_status}`);
      return res.send('success'); // 返回成功，等待后续回调
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      deposit.status = 'completed';
      deposit.actual_amount = parseFloat(trade_amount);
      deposit.payment_time = new Date();
      deposit.completion_time = new Date();
      deposit.payment_platform_order_no = trade_no;
      deposit.callback_status = 'channel_callback'; // 设置回调状态为通道回调
      deposit.remark = `KB支付成功，平台订单号: ${trade_no}`;

      // 使用余额服务增加用户充值账户余额
      const result = await balanceService.adjustBalance(
        deposit.user_id,
        'deposit', // 充值账户
        deposit.actual_amount,
        'add',
        'deposit', // 交易类型为充值
        `充值 ${deposit.actual_amount}`,
        deposit.id,
        'deposit',
        transaction
      );

      // 更新充值订单关联的交易ID
      deposit.transaction_id = result.transactionId;

      // 处理充值返佣
      await commissionService.processDepositCommission(
        deposit.user_id,
        parseFloat(deposit.actual_amount),
        result.transactionId,
        transaction
      );

      await deposit.save({ transaction });

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          console.error('KB支付回调后更新统计数据失败:', err);
        });
      } catch (error) {
        console.error('触发统计数据更新失败:', error);
      }

      console.log(`KB支付回调: 订单 ${order_no} 处理成功`);
      return res.send('success');
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      console.error('KB支付回调处理错误:', error);
      return res.status(500).send('处理失败');
    }
  } catch (error) {
    console.error('KB支付回调异常:', error);
    return res.status(500).send('服务器内部错误');
  }
};

/**
 * 处理KB支付代付回调
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 响应结果
 */
exports.handleKBPayPayoutCallback = async (req, res) => {
  try {
    console.log('收到KB支付代付回调:', req.body);

    // 提取回调数据
    const {
      merchant_no,
      order_no,
      order_amount,
      trade_status,
      trade_no,
      fail_msg
    } = req.body;

    // 查找提现订单
    const withdrawal = await Withdrawal.findOne({
      where: { order_number: order_no },
      include: [
        {
          model: User,
          as: 'user'
        }
      ]
    });

    if (!withdrawal) {
      console.error(`KB支付代付回调: 订单 ${order_no} 不存在`);
      return res.status(404).send('订单不存在');
    }

    // 检查订单状态
    if (withdrawal.status !== 'processing' && withdrawal.status !== 'pending') {
      console.log(`KB支付代付回调: 订单 ${order_no} 已处理，当前状态: ${withdrawal.status}`);
      return res.send('success'); // 返回成功，避免重复回调
    }

    // 获取KB支付服务
    const kbPayService = await getKBPayService(withdrawal.payment_channel_id || 1);

    // 验证签名
    if (!kbPayService.verifyCallbackSignature(req.body, 'payout_key')) {
      console.error('KB支付代付回调签名验证失败');
      return res.status(400).send('签名验证失败');
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 根据交易状态更新订单
      if (trade_status === 'success') {
        // 代付成功
        withdrawal.status = 'completed';
        withdrawal.completion_time = new Date();
        withdrawal.payment_platform_order_no = trade_no;
        withdrawal.callback_status = 'channel_callback';
        withdrawal.remark = `KB支付代付成功，平台订单号: ${trade_no}`;
      } else if (trade_status === 'fail') {
        // 代付失败
        withdrawal.status = 'failed';
        withdrawal.remark = `KB支付代付失败: ${fail_msg || '未知原因'}`;
      } else {
        // 其他状态，保持处理中
        console.log(`KB支付代付回调: 订单 ${order_no} 状态未知: ${trade_status}`);
        await transaction.rollback();
        return res.send('success');
      }

      // 保存更新
      await withdrawal.save({ transaction });

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          console.error('KB支付代付回调后更新统计数据失败:', err);
        });
      } catch (error) {
        console.error('触发统计数据更新失败:', error);
      }

      console.log(`KB支付代付回调: 订单 ${order_no} 处理成功，状态: ${withdrawal.status}`);
      return res.send('success');
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      console.error('KB支付代付回调处理错误:', error);
      return res.status(500).send('处理失败');
    }
  } catch (error) {
    console.error('KB支付代付回调异常:', error);
    return res.status(500).send('服务器内部错误');
  }
};

/**
 * 处理Base支付回调
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 响应结果
 */
exports.handleBasePayCallback = async (req, res) => {
  try {
    console.log('收到Base支付回调:', req.body);

    // 提取回调数据
    const {
      tradeResult,
      mchId,
      mchOrderNo,
      oriAmount,
      amount,
      orderDate,
      orderNo,
      merRetMsg,
      signType,
      sign
    } = req.body;

    // 查找订单
    const deposit = await Deposit.findOne({
      where: { order_number: mchOrderNo },
      include: [
        {
          model: User,
          as: 'user'
        }
      ]
    });

    if (!deposit) {
      console.error(`Base支付回调: 订单 ${mchOrderNo} 不存在`);
      return res.status(404).send('订单不存在');
    }

    // 获取Base支付服务
    const basePayService = await getBasePayService(deposit.payment_channel_id || 1);

    // 验证签名
    if (!basePayService.verifyCallbackSignature(req.body)) {
      console.error('Base支付回调签名验证失败');
      return res.status(400).send('签名验证失败');
    }

    // 检查订单状态
    if (deposit.status !== 'pending') {
      console.log(`Base支付回调: 订单 ${mchOrderNo} 已处理，当前状态: ${deposit.status}`);
      return res.send('success'); // 返回成功，避免重复回调
    }

    // 检查支付状态
    if (tradeResult !== '1') {
      console.log(`Base支付回调: 订单 ${mchOrderNo} 支付未完成，状态: ${tradeResult}`);
      return res.send('success'); // 返回成功，等待后续回调
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      deposit.status = 'completed';
      deposit.actual_amount = parseFloat(amount);
      deposit.payment_time = new Date();
      deposit.completion_time = new Date();
      deposit.payment_platform_order_no = orderNo;
      deposit.callback_status = 'channel_callback'; // 设置回调状态为通道回调
      deposit.remark = `Base支付成功，平台订单号: ${orderNo}`;

      // 使用余额服务增加用户充值账户余额
      const result = await balanceService.adjustBalance(
        deposit.user_id,
        'deposit', // 充值账户
        deposit.actual_amount,
        'add',
        'deposit', // 交易类型为充值
        `充值 ${deposit.actual_amount}`,
        deposit.id,
        'deposit',
        transaction
      );

      // 更新充值订单关联的交易ID
      deposit.transaction_id = result.transactionId;

      // 处理充值返佣
      await commissionService.processDepositCommission(
        deposit.user_id,
        parseFloat(deposit.actual_amount),
        result.transactionId,
        transaction
      );

      await deposit.save({ transaction });

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          console.error('Base支付回调后更新统计数据失败:', err);
        });
      } catch (error) {
        console.error('触发统计数据更新失败:', error);
      }

      console.log(`Base支付回调: 订单 ${mchOrderNo} 处理成功`);
      return res.send('success');
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      console.error('Base支付回调处理错误:', error);
      return res.status(500).send('处理失败');
    }
  } catch (error) {
    console.error('Base支付回调异常:', error);
    return res.status(500).send('服务器内部错误');
  }
};

/**
 * 处理Base支付代付回调
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 响应结果
 */
exports.handleBasePayPayoutCallback = async (req, res) => {
  try {
    console.log('收到Base支付代付回调:', req.body);

    // 提取回调数据
    const {
      tradeResult,
      merTransferId,
      merNo,
      tradeNo,
      transferAmount,
      applyDate,
      version,
      respCode,
      sign,
      signType
    } = req.body;

    // 查找提现订单
    const withdrawal = await Withdrawal.findOne({
      where: { order_number: merTransferId },
      include: [
        {
          model: User,
          as: 'user'
        }
      ]
    });

    if (!withdrawal) {
      console.error(`Base支付代付回调: 订单 ${merTransferId} 不存在`);
      return res.status(404).send('订单不存在');
    }

    // 检查订单状态
    if (withdrawal.status !== 'processing' && withdrawal.status !== 'pending') {
      console.log(`Base支付代付回调: 订单 ${merTransferId} 已处理，当前状态: ${withdrawal.status}`);
      return res.send('success'); // 返回成功，避免重复回调
    }

    // 获取Base支付服务
    const basePayService = await getBasePayService(withdrawal.payment_channel_id || 1);

    // 验证签名
    if (!basePayService.verifyCallbackSignature(req.body, 'payout_key')) {
      console.error('Base支付代付回调签名验证失败');
      return res.status(400).send('签名验证失败');
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 根据交易状态更新订单
      if (tradeResult === '1') {
        // 代付成功
        withdrawal.status = 'completed';
        withdrawal.completion_time = new Date();
        withdrawal.payment_platform_order_no = tradeNo;
        withdrawal.callback_status = 'channel_callback';
        withdrawal.remark = `Base支付代付成功，平台订单号: ${tradeNo}`;
      } else if (tradeResult === '2') {
        // 代付失败
        withdrawal.status = 'failed';
        withdrawal.remark = `Base支付代付失败`;
      } else {
        // 其他状态（0:申请成功, 3:转账拒绝, 4:转账处理中），保持处理中
        console.log(`Base支付代付回调: 订单 ${merTransferId} 状态: ${tradeResult}`);
        await transaction.rollback();
        return res.send('success');
      }

      // 保存更新
      await withdrawal.save({ transaction });

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          console.error('Base支付代付回调后更新统计数据失败:', err);
        });
      } catch (error) {
        console.error('触发统计数据更新失败:', error);
      }

      console.log(`Base支付代付回调: 订单 ${merTransferId} 处理成功，状态: ${withdrawal.status}`);
      return res.send('success');
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      console.error('Base支付代付回调处理错误:', error);
      return res.status(500).send('处理失败');
    }
  } catch (error) {
    console.error('Base支付代付回调异常:', error);
    return res.status(500).send('服务器内部错误');
  }
};
