<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员筛选功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .filter-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
        }
        .filter-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #f56c6c;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .filter-tags-container {
            margin-bottom: 20px;
            padding: 12px 16px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e4e7ed;
        }
        .filter-tags-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .filter-tags-title {
            font-size: 13px;
            font-weight: 500;
            color: #606266;
        }
        .clear-all-btn {
            font-size: 12px;
            color: #409eff;
            background: none;
            border: none;
            cursor: pointer;
        }
        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .filter-tag {
            font-size: 12px;
            background-color: #ecf5ff;
            border: 1px solid #b3d8ff;
            color: #409eff;
            padding: 4px 8px;
            border-radius: 4px;
            position: relative;
            padding-right: 20px;
        }
        .filter-tag .close {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            font-size: 10px;
        }
        .filter-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .filter-dialog.show {
            display: flex;
        }
        .filter-content {
            background: white;
            width: 900px;
            max-height: 80vh;
            border-radius: 8px;
            overflow: hidden;
        }
        .filter-header {
            padding: 16px 20px;
            background: #f0f5ff;
            border-bottom: 1px solid #e4e7ed;
            font-weight: 600;
        }
        .filter-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .filter-section {
            margin-bottom: 20px;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 20px;
        }
        .section-title {
            font-size: 15px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 16px;
            padding-left: 12px;
            position: relative;
        }
        .section-title:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background-color: #409EFF;
            border-radius: 2px;
        }
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }
        .filter-item {
            display: flex;
            flex-direction: column;
        }
        .filter-label {
            font-size: 13px;
            margin-bottom: 8px;
            color: #303133;
            font-weight: 500;
        }
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
        }
        .range-inputs {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .range-inputs input {
            flex: 1;
        }
        .filter-footer {
            padding: 16px 20px;
            background: #f0f5ff;
            border-top: 1px solid #e4e7ed;
            display: flex;
            justify-content: center;
            gap: 16px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            min-width: 100px;
        }
        .btn-primary {
            background: #409eff;
            color: white;
        }
        .btn-default {
            background: #f5f7fa;
            color: #606266;
            border: 1px solid #dcdfe6;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .demo-table th,
        .demo-table td {
            border: 1px solid #ebeef5;
            padding: 12px;
            text-align: left;
        }
        .demo-table th {
            background: #f5f7fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>会员列表筛选功能演示</h1>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <div>
                <button class="filter-button" onclick="showFilterDialog()">
                    筛选
                    <span class="filter-badge" id="filterBadge" style="display: none;">0</span>
                </button>
            </div>
        </div>

        <!-- 筛选条件显示 -->
        <div class="filter-tags-container" id="filterTagsContainer" style="display: none;">
            <div class="filter-tags-header">
                <span class="filter-tags-title">当前筛选条件：</span>
                <button class="clear-all-btn" onclick="clearAllFilters()">清除所有筛选</button>
            </div>
            <div class="filter-tags" id="filterTags"></div>
        </div>

        <!-- 演示表格 -->
        <table class="demo-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>等级</th>
                    <th>总资产</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>user001</td>
                    <td><EMAIL></td>
                    <td>VIP1</td>
                    <td>1000.00</td>
                    <td>正常</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>user002</td>
                    <td><EMAIL></td>
                    <td>VIP2</td>
                    <td>5000.00</td>
                    <td>正常</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>user003</td>
                    <td><EMAIL></td>
                    <td>VIP1</td>
                    <td>2000.00</td>
                    <td>禁用</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 筛选对话框 -->
    <div class="filter-dialog" id="filterDialog">
        <div class="filter-content">
            <div class="filter-header">
                筛选条件
            </div>
            <div class="filter-body">
                <!-- 基本信息区域 -->
                <div class="filter-section">
                    <div class="section-title">基本信息</div>
                    <div class="filter-grid">
                        <div class="filter-item">
                            <div class="filter-label">ID</div>
                            <input type="text" class="filter-input" id="filterId" placeholder="请输入ID">
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">用户名</div>
                            <input type="text" class="filter-input" id="filterUsername" placeholder="请输入用户名">
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">邮箱</div>
                            <input type="text" class="filter-input" id="filterEmail" placeholder="请输入邮箱">
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">状态</div>
                            <select class="filter-input" id="filterStatus">
                                <option value="">请选择</option>
                                <option value="正常">正常</option>
                                <option value="禁用">禁用</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 资产信息区域 -->
                <div class="filter-section">
                    <div class="section-title">资产信息</div>
                    <div class="filter-grid">
                        <div class="filter-item">
                            <div class="filter-label">总资产</div>
                            <div class="range-inputs">
                                <input type="number" class="filter-input" id="filterAssetMin" placeholder="最小值">
                                <span>-</span>
                                <input type="number" class="filter-input" id="filterAssetMax" placeholder="最大值">
                            </div>
                        </div>
                        <div class="filter-item">
                            <div class="filter-label">等级</div>
                            <div class="range-inputs">
                                <input type="number" class="filter-input" id="filterLevelMin" placeholder="最小值">
                                <span>-</span>
                                <input type="number" class="filter-input" id="filterLevelMax" placeholder="最大值">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="filter-footer">
                <button class="btn btn-primary" onclick="applyFilter()">搜索</button>
                <button class="btn btn-default" onclick="resetFilter()">重置</button>
                <button class="btn btn-default" onclick="hideFilterDialog()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let activeFilters = {};

        function showFilterDialog() {
            document.getElementById('filterDialog').classList.add('show');
        }

        function hideFilterDialog() {
            document.getElementById('filterDialog').classList.remove('show');
        }

        function applyFilter() {
            // 收集筛选条件
            const filters = {};
            
            const id = document.getElementById('filterId').value;
            const username = document.getElementById('filterUsername').value;
            const email = document.getElementById('filterEmail').value;
            const status = document.getElementById('filterStatus').value;
            const assetMin = document.getElementById('filterAssetMin').value;
            const assetMax = document.getElementById('filterAssetMax').value;
            const levelMin = document.getElementById('filterLevelMin').value;
            const levelMax = document.getElementById('filterLevelMax').value;

            if (id) filters.id = { label: `ID: ${id}`, value: id };
            if (username) filters.username = { label: `用户名: ${username}`, value: username };
            if (email) filters.email = { label: `邮箱: ${email}`, value: email };
            if (status) filters.status = { label: `状态: ${status}`, value: status };
            if (assetMin || assetMax) {
                const min = assetMin || '不限';
                const max = assetMax || '不限';
                filters.asset = { label: `总资产: ${min} - ${max}`, value: { min: assetMin, max: assetMax } };
            }
            if (levelMin || levelMax) {
                const min = levelMin || '不限';
                const max = levelMax || '不限';
                filters.level = { label: `等级: ${min} - ${max}`, value: { min: levelMin, max: levelMax } };
            }

            activeFilters = filters;
            updateFilterDisplay();
            hideFilterDialog();
            
            alert(`已应用 ${Object.keys(filters).length} 个筛选条件`);
        }

        function resetFilter() {
            document.getElementById('filterId').value = '';
            document.getElementById('filterUsername').value = '';
            document.getElementById('filterEmail').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterAssetMin').value = '';
            document.getElementById('filterAssetMax').value = '';
            document.getElementById('filterLevelMin').value = '';
            document.getElementById('filterLevelMax').value = '';
        }

        function clearAllFilters() {
            activeFilters = {};
            resetFilter();
            updateFilterDisplay();
            alert('已清除所有筛选条件');
        }

        function removeFilter(key) {
            delete activeFilters[key];
            updateFilterDisplay();
            alert(`已移除筛选条件: ${key}`);
        }

        function updateFilterDisplay() {
            const count = Object.keys(activeFilters).length;
            const badge = document.getElementById('filterBadge');
            const container = document.getElementById('filterTagsContainer');
            const tagsContainer = document.getElementById('filterTags');

            if (count > 0) {
                badge.style.display = 'flex';
                badge.textContent = count;
                container.style.display = 'block';
                
                tagsContainer.innerHTML = '';
                Object.keys(activeFilters).forEach(key => {
                    const filter = activeFilters[key];
                    const tag = document.createElement('div');
                    tag.className = 'filter-tag';
                    tag.innerHTML = `
                        ${filter.label}
                        <span class="close" onclick="removeFilter('${key}')">×</span>
                    `;
                    tagsContainer.appendChild(tag);
                });
            } else {
                badge.style.display = 'none';
                container.style.display = 'none';
            }
        }

        // 点击对话框外部关闭
        document.getElementById('filterDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                hideFilterDialog();
            }
        });
    </script>
</body>
</html>
