{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-cursor/index.ts"], "sourcesContent": ["import type { ShallowRef } from 'vue'\n\ninterface SelectionInfo {\n  selectionStart?: number\n  selectionEnd?: number\n  value?: string\n  beforeTxt?: string\n  afterTxt?: string\n}\n\n// Keep input cursor in the correct position when we use formatter.\nexport function useCursor(\n  input: ShallowRef<HTMLInputElement | undefined>\n): [() => void, () => void] {\n  let selectionInfo: SelectionInfo\n  function recordCursor() {\n    if (input.value == undefined) return\n\n    const { selectionStart, selectionEnd, value } = input.value\n\n    if (selectionStart == null || selectionEnd == null) return\n\n    const beforeTxt = value.slice(0, Math.max(0, selectionStart))\n    const afterTxt = value.slice(Math.max(0, selectionEnd))\n\n    selectionInfo = {\n      selectionStart,\n      selectionEnd,\n      value,\n      beforeTxt,\n      afterTxt,\n    }\n  }\n  function setCursor() {\n    if (input.value == undefined || selectionInfo == undefined) return\n\n    const { value } = input.value\n    const { beforeTxt, afterTxt, selectionStart } = selectionInfo\n\n    if (\n      beforeTxt == undefined ||\n      afterTxt == undefined ||\n      selectionStart == undefined\n    )\n      return\n\n    let startPos = value.length\n\n    if (value.endsWith(afterTxt)) {\n      startPos = value.length - afterTxt.length\n    } else if (value.startsWith(beforeTxt)) {\n      startPos = beforeTxt.length\n    } else {\n      const beforeLastChar = beforeTxt[selectionStart - 1]\n      const newIndex = value.indexOf(beforeLastChar, selectionStart - 1)\n      if (newIndex !== -1) {\n        startPos = newIndex + 1\n      }\n    }\n\n    input.value.setSelectionRange(startPos, startPos)\n  }\n\n  return [recordCursor, setCursor]\n}\n"], "names": [], "mappings": "AAAO,SAAS,SAAS,CAAC,KAAK,EAAE;AACjC,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC;AAC7B,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC;AAChE,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI;AACtD,MAAM,OAAO;AACb,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;AAClE,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AAC5D,IAAI,aAAa,GAAG;AACpB,MAAM,cAAc;AACpB,MAAM,YAAY;AAClB,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,aAAa,IAAI,KAAK,CAAC;AACxD,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC;AAClC,IAAI,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC;AAClE,IAAI,IAAI,SAAS,IAAI,KAAK,CAAC,IAAI,QAAQ,IAAI,KAAK,CAAC,IAAI,cAAc,IAAI,KAAK,CAAC;AAC7E,MAAM,OAAO;AACb,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;AAChC,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAChD,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AAC5C,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;AAClC,KAAK,MAAM;AACX,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;AAC3D,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;AACzE,MAAM,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;AAC3B,QAAQ,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;AAChC,OAAO;AACP,KAAK;AACL,IAAI,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;AACnC;;;;"}