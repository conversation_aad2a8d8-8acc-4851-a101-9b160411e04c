const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Transaction = sequelize.define('Transaction', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
  },
  order_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true,
    comment: '订单号',
  },
  type: {
    type: DataTypes.ENUM('deposit', 'withdrawal', 'investment', 'investment_gift', 'investment_purchase', 'profit', 'commission', 'bonus', 'deduction'),
    allowNull: false,
    comment: '交易类型：deposit=存款, withdrawal=取款, investment=购买, investment_gift=系统赠送, investment_purchase=购买, profit=收益, commission=佣金, bonus=系统赠送, deduction=扣除',
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '交易金额',
  },
  before_balance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '交易前余额',
  },
  balance: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '交易后余额',
  },
  currency: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: 'CNY',
    comment: '货币类型',
  },
  status: {
    type: DataTypes.ENUM('pending', 'success', 'failed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '交易状态：pending=处理中, success=成功, failed=失败',
  },
  reference_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联记录ID',
  },
  reference_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '关联记录类型',
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '交易描述',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'transactions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Transaction.associate = (models) => {
  Transaction.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });

  // 交易与充值订单
  Transaction.hasOne(models.Deposit, {
    foreignKey: 'transaction_id',
    as: 'deposit',
  });

  // 交易与提现记录
  Transaction.hasOne(models.Withdrawal, {
    foreignKey: 'transaction_id',
    as: 'withdrawal',
  });

  // 交易与投资收益
  Transaction.hasOne(models.InvestmentProfit, {
    foreignKey: 'transaction_id',
    as: 'investment_profit',
  });

  // 交易与佣金记录
  Transaction.hasOne(models.Commission, {
    foreignKey: 'transaction_id',
    as: 'commission',
  });
};

module.exports = Transaction;
