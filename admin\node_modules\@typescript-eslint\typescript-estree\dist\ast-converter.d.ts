import type { SourceFile } from 'typescript';
import type { ASTMaps } from './convert';
import type { ParseSettings } from './parseSettings';
import type { TSESTree } from './ts-estree';
export declare function astConverter(ast: SourceFile, parseSettings: ParseSettings, shouldPreserveNodeMaps: boolean): {
    estree: TSESTree.Program;
    astMaps: ASTMaps;
};
//# sourceMappingURL=ast-converter.d.ts.map