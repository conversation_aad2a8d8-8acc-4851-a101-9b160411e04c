<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';

onLaunch(() => {

  // 检查用户是否已登录
  const token = uni.getStorageSync('userToken');
  const userInfo = uni.getStorageSync('userInfo');

  // 如果没有登录，跳转到登录页面
  if (!token || !userInfo) {
    uni.reLaunch({
      url: '/pages/login/index'
    });
  } else {
    uni.switchTab({
      url: '/pages/index/index'
    });
  }
});

onShow(() => {
});

onHide(() => {
});
</script>

<style>
/* 全局样式 */
page {
  background-color: #1c2431;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 重置样式 */
view, text, input, button {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 全局文本颜色 */
text {
  color: #ffffff;
}

/* 全局按钮样式 */
button {
  background: #E31837;
  color: #FFFFFF;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
}

button:active {
  opacity: 0.8;
}

/* 全局输入框样式 */
input {
  background-color: transparent;
  border: 1px solid #E5E5E5;
  border-radius: 12rpx;
  color: #333333;
  font-size: 32rpx;
}

/* 全局链接样式 */
.link {
  color: #E31837;
  font-size: 28rpx;
}
</style>
