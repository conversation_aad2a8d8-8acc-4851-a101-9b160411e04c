/**
 * 检查统计表是否创建成功
 */
const { DailyStatistic, TotalStatistic } = require('./models');
const sequelize = require('./config/database');

async function checkTables() {
  try {
    // 检查连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 检查表是否存在
    try {
      await DailyStatistic.findOne();
      console.log('daily_statistics表存在');
    } catch (error) {
      console.error('daily_statistics表不存在:', error.message);
    }
    
    try {
      await TotalStatistic.findOne();
      console.log('total_statistics表存在');
    } catch (error) {
      console.error('total_statistics表不存在:', error.message);
    }
    
    // 查询所有表
    const [tables] = await sequelize.query('SHOW TABLES');
    console.log('所有表:', tables);
    
    process.exit(0);
  } catch (error) {
    console.error('检查表失败:', error);
    process.exit(1);
  }
}

checkTables();
