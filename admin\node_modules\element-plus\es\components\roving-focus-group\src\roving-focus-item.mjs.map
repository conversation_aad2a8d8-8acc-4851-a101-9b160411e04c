{"version": 3, "file": "roving-focus-item.mjs", "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-item.vue"], "sourcesContent": ["<template>\n  <el-roving-focus-collection-item\n    :id=\"id\"\n    :focusable=\"focusable\"\n    :active=\"active\"\n  >\n    <slot />\n  </el-roving-focus-collection-item>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  provide,\n  ref,\n  unref,\n} from 'vue'\nimport { useId } from '@element-plus/hooks'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport {\n  ElCollectionItem as ElRovingFocusCollectionItem,\n  ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n} from './roving-focus-group'\nimport {\n  ROVING_FOCUS_GROUP_INJECTION_KEY,\n  ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY,\n} from './tokens'\nimport { focusFirst, getFocusIntent, reorderArray } from './utils'\n\nexport default defineComponent({\n  components: {\n    ElRovingFocusCollectionItem,\n  },\n  props: {\n    focusable: {\n      type: Boolean,\n      default: true,\n    },\n    active: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  emits: ['mousedown', 'focus', 'keydown'],\n  setup(props, { emit }) {\n    const { currentTabbedId, loop, onItemFocus, onItemShiftTab } = inject(\n      ROVING_FOCUS_GROUP_INJECTION_KEY,\n      undefined\n    )!\n\n    const { getItems } = inject(\n      ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n      undefined\n    )!\n\n    const id = useId()\n    const rovingFocusGroupItemRef = ref<HTMLElement>()\n\n    const handleMousedown = composeEventHandlers(\n      (e: Event) => {\n        emit('mousedown', e)\n      },\n      (e) => {\n        if (!props.focusable) {\n          e.preventDefault()\n        } else {\n          onItemFocus(unref(id))\n        }\n      }\n    )\n\n    const handleFocus = composeEventHandlers(\n      (e: Event) => {\n        emit('focus', e)\n      },\n      () => {\n        onItemFocus(unref(id))\n      }\n    )\n\n    const handleKeydown = composeEventHandlers(\n      (e: Event) => {\n        emit('keydown', e)\n      },\n      (e) => {\n        const { code, shiftKey, target, currentTarget } = e as KeyboardEvent\n        if (code === EVENT_CODE.tab && shiftKey) {\n          onItemShiftTab()\n          return\n        }\n        if (target !== currentTarget) return\n        const focusIntent = getFocusIntent(e as KeyboardEvent)\n\n        if (focusIntent) {\n          e.preventDefault()\n          const items = getItems<typeof props>().filter(\n            (item) => item.focusable\n          )\n\n          let elements = items.map((item) => item.ref!)\n\n          switch (focusIntent) {\n            case 'last': {\n              elements.reverse()\n              break\n            }\n            case 'prev':\n            case 'next': {\n              if (focusIntent === 'prev') {\n                elements.reverse()\n              }\n              const currentIdx = elements.indexOf(currentTarget as HTMLElement)\n              elements = loop.value\n                ? reorderArray(elements, currentIdx + 1)\n                : elements.slice(currentIdx + 1)\n              break\n            }\n            default: {\n              break\n            }\n          }\n\n          nextTick(() => {\n            focusFirst(elements)\n          })\n        }\n      }\n    )\n\n    const isCurrentTab = computed(() => currentTabbedId.value === unref(id))\n\n    provide(ROVING_FOCUS_GROUP_ITEM_INJECTION_KEY, {\n      rovingFocusGroupItemRef,\n      tabIndex: computed(() => (unref(isCurrentTab) ? 0 : -1)),\n      handleMousedown,\n      handleFocus,\n      handleKeydown,\n    })\n\n    return {\n      id,\n      handleKeydown,\n      handleFocus,\n      handleMousedown,\n    }\n  },\n})\n</script>\n"], "names": ["ElRovingFocusCollectionItem", "ROVING_FOCUS_COLLECTION_INJECTION_KEY", "_resolveComponent", "_createBlock", "_withCtx", "_renderSlot"], "mappings": ";;;;;;;;;AAiCA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,UAAY,EAAA;AAAA,iCACVA,gBAAA;AAAA,GACF;AAAA,EACA,KAAO,EAAA;AAAA,IACL,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,KAAA;AAAA,KACX;AAAA,GACF;AAAA,EACA,KAAO,EAAA,CAAC,WAAa,EAAA,OAAA,EAAS,SAAS,CAAA;AAAA,EACvC,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAA,MAAM,EAAE,eAAA,EAAiB,IAAM,EAAA,WAAA,EAAa,gBAAmB,GAAA,MAAA,CAAA,gCAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAC7D,MAAA,EAAA,QAAA,EAAA,GAAA,MAAA,CAAAC,wBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,GAAA,KAAA,EAAA,CAAA;AAAA,IACF,MAAA,uBAAA,GAAA,GAAA,EAAA,CAAA;AAEA,IAAM,MAAA,eAAe,GAAA,oBAAA,CAAA,CAAA,CAAA,KAAA;AAAA,MACnB,IAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KACA,EAAA,CAAA,CAAA,KAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,SAAA,EAAA;AAEA,QAAA,CAAA,CAAM,cAAW,EAAA,CAAA;AACjB,OAAA;AAEA,QAAA,WAAwB,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,OACrB;AACC,KAAA,CAAA,CAAA;AAAmB,IACrB,MAAA,WAAA,GAAA,oBAAA,CAAA,CAAA,CAAA,KAAA;AAAA,MACA,IAAO,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;AACL,KAAI,EAAA,MAAC;AACH,MAAA,WAAiB,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAEjB,IAAY,MAAA,aAAA,GAAA,oBAAS,CAAA,CAAA,CAAA,KAAA;AAAA,MACvB,IAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KACF,EAAA,CAAA,CAAA,KAAA;AAAA,MACF,MAAA,EAAA,IAAA,EAAA,QAAA,EAAA,MAAA,EAAA,aAAA,EAAA,GAAA,CAAA,CAAA;AAEA,MAAA,IAAM,IAAc,KAAA,UAAA,CAAA,GAAA,IAAA,QAAA,EAAA;AAAA,QACJ,cAAA,EAAA,CAAA;AACZ,QAAA;AAAe,OACjB;AAAA,MACA,IAAM,MAAA,KAAA,aAAA;AACJ,QAAY,OAAA;AAAS,MACvB,MAAA,WAAA,GAAA,cAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACF,IAAA,WAAA,EAAA;AAEA,QAAA,CAAA,CAAM,cAAgB,EAAA,CAAA;AAAA,QACN,MAAA,KAAA,GAAA,QAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AACZ,QAAA,IAAA,gBAAiB,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAAA,QACnB,QAAA,WAAA;AAAA,UACO,KAAA,MAAA,EAAA;AACL,YAAA,QAAQ,CAAA,OAAgB,EAAA,CAAA;AACxB,YAAI,MAAA;AACF,WAAe;AACf,UAAA,KAAA,MAAA,CAAA;AAAA,UACF,KAAA,MAAA,EAAA;AACA,YAAI,eAA0B,KAAA,MAAA,EAAA;AAC9B,cAAM,QAAA,CAAA;AAEN,aAAiB;AACf,YAAE,MAAe,UAAA,GAAA,QAAA,CAAA,OAAA,CAAA,aAAA,CAAA,CAAA;AACjB,YAAM,QAAA,GAAA,UAA+B,GAAE,YAAA,CAAA,QAAA,EAAA,UAAA,GAAA,CAAA,CAAA,GAAA,QAAA,CAAA,KAAA,CAAA,UAAA,GAAA,CAAA,CAAA,CAAA;AAAA,YACrC;AAAe,WACjB;AAMI,SAAA;AACA,QAAA,QAAA,CAAA,MAAA;AAAA,UACF,UAAA,CAAA,QAAA,CAAA,CAAA;AAAA,SAAA,CAAA,CAAA;AACK,OAAA;AAEH,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,YAAA,GAAS,QAAQ,CAAA,MAAA,eAAA,CAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,IACnB,OAAA,CAAA,qCAAA,EAAA;AACA,MAAM,uBAAA;AACN,MAAW,QAAA,EAAA,QAAA,CAAA,MACP,KAAA,CAAA,YAAA,CAAA,GAAuB,CAAA,GAAA,CAAA,CAAA,CAAA;AAE3B,MAAA,eAAA;AAAA,MACF,WAAA;AAAA,MAAA,aACS;AACP,KAAA,CAAA,CAAA;AAAA,IACF,OAAA;AAAA,MACF,EAAA;AAEA,MAAA,aAAS;AACP,MAAA,WAAA;AAAmB,MAAA,eACpB;AAAA,KACH,CAAA;AAAA,GACF;AAAA,CACF,CAAA,CAAA;AAIA,SAAA,WAA+C,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EAC7C,MAAA,0CAAA,GAAAC,gBAAA,CAAA,iCAAA,CAAA,CAAA;AAAA,EAAA,gBACmB,EAAA,EAAAC,sDAAoC,EAAA;AAAA,IACvD,EAAA,EAAA,IAAA,CAAA,EAAA;AAAA,IACA,SAAA,EAAA,IAAA,CAAA,SAAA;AAAA,IACA,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,GAAA,EACD;AAED,IAAO,OAAA,EAAAC,OAAA,CAAA,MAAA;AAAA,MACLC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,KACA,CAAA;AAAA,IACA,CAAA,EAAA,CAAA;AAAA,GACA,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,CAAA;AAAA,CACF;AAEJ,wBAAC,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,uBAAA,CAAA,CAAA,CAAA;;;;"}