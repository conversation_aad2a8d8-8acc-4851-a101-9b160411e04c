/**
 * 添加failed状态到deposits表的status字段
 */
const { up } = require('../migrations/20250530-add-failed-status-to-deposits');
const sequelize = require('../config/database');

async function runMigration() {
  try {
    console.log('开始执行迁移：添加failed状态到deposits表...');
    
    // 创建queryInterface对象
    const queryInterface = sequelize.getQueryInterface();
    
    // 执行迁移
    await up(queryInterface, sequelize.Sequelize);
    
    console.log('迁移执行成功！');
    
    // 验证迁移结果
    const [columns] = await sequelize.query(`
      SHOW COLUMNS FROM deposits WHERE Field = 'status'
    `);
    
    if (columns.length > 0) {
      console.log('验证结果 - status字段类型:', columns[0].Type);
      
      if (columns[0].Type.includes('failed')) {
        console.log('✅ 验证成功：failed状态已添加到ENUM中');
      } else {
        console.log('❌ 验证失败：failed状态未添加到ENUM中');
      }
    }
    
  } catch (error) {
    console.error('迁移执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行迁移
runMigration();
