const { User, InviteCode, UserRelation } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const crypto = require('crypto');

// 生成随机邀请码
exports.generateInviteCode = async (length = null, rule = null) => {
  // 获取邀请码规则和长度的系统参数
  const { SystemParam } = require('../models');

  try {
    // 获取邀请码长度
    if (!length) {
      const lengthParam = await SystemParam.findOne({
        where: { param_key: '[site.promo_code_length]' }
      });

      if (lengthParam && lengthParam.param_value) {
        length = parseInt(lengthParam.param_value) || 6;
      } else {
        length = 6; // 默认长度
      }
    }

    // 获取邀请码规则
    if (!rule) {
      const ruleParam = await SystemParam.findOne({
        where: { param_key: '[site.promo_code_rule]' }
      });

      if (ruleParam && ruleParam.param_value) {
        rule = ruleParam.param_value;
      } else {
        rule = '1'; // 默认规则：数字和英文字母混合
      }
    }

    console.log(`生成邀请码，长度: ${length}, 规则: ${rule}`);

    // 确保长度至少为4
    length = Math.max(4, length);

    let code = '';

    // 根据规则生成邀请码
    switch (rule) {
      case '2': // 纯数字
        // 生成指定长度的随机数字
        for (let i = 0; i < length; i++) {
          code += Math.floor(Math.random() * 10).toString();
        }
        break;

      case '3': // 纯英文字母
        // 生成指定长度的随机字母
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        for (let i = 0; i < length; i++) {
          code += letters.charAt(Math.floor(Math.random() * letters.length));
        }
        break;

      default: // 数字和英文字母混合
        // 计算需要生成的字节数（每个字节可以生成2个十六进制字符）
        const bytesNeeded = Math.ceil(length / 2);
        code = crypto.randomBytes(bytesNeeded).toString('hex').toUpperCase().substring(0, length);
        break;
    }

    return code;
  } catch (error) {
    console.error('生成邀请码错误:', error);
    // 如果出错，使用默认方法生成
    return crypto.randomBytes(3).toString('hex').toUpperCase();
  }
};

// 创建用户的邀请码
exports.createInviteCode = async (req, res) => {
  try {
    const userId = req.user.id;

    // 检查用户是否已有邀请码
    const existingCode = await InviteCode.findOne({
      where: { user_id: userId, status: true }
    });

    if (existingCode) {
      return res.status(200).json({
        code: 200,
        message: '已有邀请码',
        data: {
          invite_code: existingCode.code
        }
      });
    }

    // 生成新的邀请码
    let code;
    let codeExists = true;

    // 确保生成的邀请码是唯一的
    while (codeExists) {
      code = await exports.generateInviteCode();
      const existingCode = await InviteCode.findOne({ where: { code } });
      codeExists = !!existingCode;
    }

    // 创建邀请码
    const inviteCode = await InviteCode.create({
      code,
      user_id: userId,
      used_count: 0,
      max_uses: 0, // 不限制使用次数
      status: true
    });

    // 更新用户的邀请码
    await User.update({ invite_code: code }, { where: { id: userId } });

    return res.status(201).json({
      code: 201,
      message: '邀请码创建成功',
      data: {
        invite_code: inviteCode.code
      }
    });
  } catch (error) {
    console.error('创建邀请码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取用户的邀请码
exports.getInviteCode = async (req, res) => {
  try {
    const userId = req.user.id;

    // 查询用户的邀请码
    const inviteCode = await InviteCode.findOne({
      where: { user_id: userId, status: true }
    });

    if (!inviteCode) {
      return res.status(404).json({
        code: 404,
        message: '邀请码不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        invite_code: inviteCode.code,
        used_count: inviteCode.used_count
      }
    });
  } catch (error) {
    console.error('获取邀请码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取用户的邀请列表
exports.getInvitees = async (req, res) => {
  try {
    const userId = req.user.id;
    const { level = 1, page = 1, limit = 10, type } = req.query;

    // 构建查询条件
    const where = {
      parent_id: userId,
      level: parseInt(level)
    };

    // 分页查询
    const offset = (page - 1) * limit;

    // 查询用户关系
    const { count, rows } = await UserRelation.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'name', 'avatar', 'created_at', 'balance']
        }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 获取所有用户ID
    const userIds = rows.map(relation => relation.user.id);

    // 查询这些用户是否有充值记录
    const depositUsers = [];
    if (userIds.length > 0) {
      const { Transaction } = require('../models');
      const deposits = await Transaction.findAll({
        attributes: ['user_id'],
        where: {
          user_id: userIds,
          type: 'deposit',
          status: 'completed'
        },
        group: ['user_id']
      });

      // 将有充值记录的用户ID添加到数组中
      depositUsers.push(...deposits.map(d => d.user_id));
    }

    // 查询这些用户是否有投资记录
    const investmentUsers = [];
    if (userIds.length > 0) {
      const { Investment } = require('../models');
      const investments = await Investment.findAll({
        attributes: ['user_id'],
        where: {
          user_id: userIds,
          status: ['active', 'completed'] // 有效的投资状态
        },
        group: ['user_id']
      });

      // 将有投资记录的用户ID添加到数组中
      investmentUsers.push(...investments.map(i => i.user_id));
    }

    // 查询这些用户为当前用户产生的返佣金额
    const commissionData = {};
    if (userIds.length > 0) {
      const { Commission } = require('../models');
      const commissions = await Commission.findAll({
        attributes: [
          'from_user_id',
          [sequelize.fn('SUM', sequelize.col('amount')), 'total_commission']
        ],
        where: {
          from_user_id: userIds,
          user_id: userId, // 当前用户ID（接收佣金的用户）
          status: 'paid'
        },
        group: ['from_user_id']
      });

      // 将返佣数据存储到对象中
      commissions.forEach(comm => {
        commissionData[comm.from_user_id] = parseFloat(comm.dataValues.total_commission || 0);
      });
    }

    // 格式化数据
    let invitees = rows.map(relation => ({
      id: relation.user.id,
      username: relation.user.username,
      name: relation.user.name || relation.user.username,
      avatar: relation.user.avatar,
      level: relation.level,
      created_at: relation.createdAt,
      recharged: depositUsers.includes(relation.user.id),
      invested: investmentUsers.includes(relation.user.id), // 投资状态
      commission_earned: commissionData[relation.user.id] || 0, // 新增返佣金额
      balance: relation.user.balance || 0
    }));

    // 根据类型筛选
    if (type === 'recharged') {
      invitees = invitees.filter(user => user.recharged);
    } else if (type === 'not-recharged') {
      invitees = invitees.filter(user => !user.recharged);
    } else if (type === 'invested') {
      invitees = invitees.filter(user => user.invested);
    } else if (type === 'not-invested') {
      invitees = invitees.filter(user => !user.invested);
    }

    // 计算筛选后的总数
    const filteredCount = type ? invitees.length : count;

    // 如果是按类型筛选，需要手动分页
    if (type) {
      const startIndex = offset;
      const endIndex = Math.min(startIndex + parseInt(limit), invitees.length);
      invitees = invitees.slice(startIndex, endIndex);
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: filteredCount,
        page: parseInt(page),
        limit: parseInt(limit),
        items: invitees
      }
    });
  } catch (error) {
    console.error('获取邀请列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 验证邀请码
exports.validateInviteCode = async (req, res) => {
  try {
    const { code } = req.params;

    if (!code) {
      return res.status(400).json({
        code: 400,
        message: '邀请码不能为空',
        data: null
      });
    }

    // 查询邀请码
    const inviteCode = await InviteCode.findOne({
      where: { code, status: true },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'name']
        }
      ]
    });

    if (!inviteCode) {
      return res.status(404).json({
        code: 404,
        message: '邀请码无效',
        data: null
      });
    }

    // 检查邀请码是否超过使用次数限制
    if (inviteCode.max_uses > 0 && inviteCode.used_count >= inviteCode.max_uses) {
      return res.status(400).json({
        code: 400,
        message: '邀请码已达到最大使用次数',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '邀请码有效',
      data: {
        invite_code: inviteCode.code,
        inviter: {
          id: inviteCode.user.id,
          username: inviteCode.user.username,
          name: inviteCode.user.name
        }
      }
    });
  } catch (error) {
    console.error('验证邀请码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

module.exports = exports;
