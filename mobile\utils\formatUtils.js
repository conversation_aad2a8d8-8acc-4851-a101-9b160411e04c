/**
 * 格式化工具函数
 * 提供各种数据格式化功能
 */

/**
 * 格式化金额为千分位+小数点后两位
 * @param {number|string} amount - 金额
 * @param {string} currency - 货币符号，默认为₱
 * @param {boolean} showCurrency - 是否显示货币符号，默认true
 * @returns {string} 格式化后的金额
 */
export const formatAmount = (amount, currency = '₱', showCurrency = true) => {
  // 处理空值或无效值
  if (amount === null || amount === undefined || amount === '' || isNaN(amount)) {
    return showCurrency ? `${currency}0.00` : '0.00';
  }

  // 转换为数字
  const numAmount = parseFloat(amount);
  
  // 格式化为千分位+小数点后两位
  const formatted = numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  // 返回带或不带货币符号的格式
  return showCurrency ? `${currency}${formatted}` : formatted;
};

/**
 * 格式化金额（不带货币符号）
 * @param {number|string} amount - 金额
 * @returns {string} 格式化后的金额
 */
export const formatAmountOnly = (amount) => {
  return formatAmount(amount, '', false);
};

/**
 * 格式化百分比
 * @param {number|string} percentage - 百分比
 * @param {number} decimals - 小数位数，默认2位
 * @returns {string} 格式化后的百分比
 */
export const formatPercentage = (percentage, decimals = 2) => {
  if (percentage === null || percentage === undefined || percentage === '' || isNaN(percentage)) {
    return '0.00%';
  }

  const numPercentage = parseFloat(percentage);
  return `${numPercentage.toFixed(decimals)}%`;
};

/**
 * 格式化数字（千分位）
 * @param {number|string} number - 数字
 * @param {number} decimals - 小数位数，默认0位
 * @returns {string} 格式化后的数字
 */
export const formatNumber = (number, decimals = 0) => {
  if (number === null || number === undefined || number === '' || isNaN(number)) {
    return '0';
  }

  const numValue = parseFloat(number);
  return numValue.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

/**
 * 格式化日期时间
 * @param {string|Date} dateTime - 日期时间
 * @param {string} format - 格式类型：'date', 'datetime', 'time'
 * @returns {string} 格式化后的日期时间
 */
export const formatDateTime = (dateTime, format = 'datetime') => {
  if (!dateTime) return '';

  const date = new Date(dateTime);
  
  if (isNaN(date.getTime())) return '';

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  switch (format) {
    case 'date':
      return `${year}-${month}-${day}`;
    case 'time':
      return `${hours}:${minutes}`;
    case 'datetime':
    default:
      return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
};

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化手机号（隐藏中间部分）
 * @param {string} phone - 手机号
 * @returns {string} 格式化后的手机号
 */
export const formatPhone = (phone) => {
  if (!phone || phone.length < 7) return phone;

  const start = phone.substring(0, 3);
  const end = phone.substring(phone.length - 4);
  const middle = '*'.repeat(phone.length - 7);

  return `${start}${middle}${end}`;
};

/**
 * 格式化银行卡号（隐藏中间部分）
 * @param {string} cardNumber - 银行卡号
 * @returns {string} 格式化后的银行卡号
 */
export const formatBankCard = (cardNumber) => {
  if (!cardNumber || cardNumber.length < 8) return cardNumber;

  const start = cardNumber.substring(0, 4);
  const end = cardNumber.substring(cardNumber.length - 4);
  const middle = '*'.repeat(cardNumber.length - 8);

  return `${start}${middle}${end}`;
};

// 默认导出所有格式化函数
export default {
  formatAmount,
  formatAmountOnly,
  formatPercentage,
  formatNumber,
  formatDateTime,
  formatFileSize,
  formatPhone,
  formatBankCard
};
