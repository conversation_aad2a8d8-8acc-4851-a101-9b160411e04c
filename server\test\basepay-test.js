/**
 * Base支付通道测试脚本
 * 用于测试Base支付服务的基本功能
 */

const { BasePayService, getBasePayService } = require('../services/basePayService');

// 测试配置
const testConfig = {
  merchant_no: '*********',
  payin_key: '1c9f2952e09a4b3d9e5b8de0a185b11f',
  payout_key: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  pay_type: '1720',
  bank_code: 'IDPT0001',
  payin_url: 'https://pay.aiffpay.com/pay/web',
  payout_url: 'https://pay.aiffpay.com/pay/transfer'
};

// 测试充值订单数据
const testDepositOrder = {
  order_number: 'RE' + Date.now(),
  amount: 100.00,
  user_id: 1
};

// 测试提现订单数据
const testWithdrawalOrder = {
  order_number: 'WM' + Date.now(),
  amount: 50.00,
  actual_amount: 50.00,
  user_id: 1
};

// 测试银行卡数据
const testBankCard = {
  card_holder: 'Test User',
  card_number: '*********',
  bank_id: 1
};

/**
 * 测试签名生成
 */
function testSignatureGeneration() {
  console.log('=== 测试签名生成 ===');
  
  const basePayService = new BasePayService(testConfig);
  
  // 测试代收签名（不包含sign_type）
  const payinParams = {
    version: '1.0',
    mch_id: testConfig.merchant_no,
    notify_url: 'https://example.com/callback',
    mch_order_no: 'TEST123',
    pay_type: testConfig.pay_type,
    trade_amount: '100.00',
    order_date: '2024-01-01 12:00:00',
    goods_name: 'Test'
  };

  const payinSignature = basePayService.generateSignature(payinParams, testConfig.payin_key);
  console.log('代收签名:', payinSignature);

  // 测试代付签名（不包含sign_type）
  const payoutParams = {
    mch_id: testConfig.merchant_no,
    mch_transferId: 'TEST456',
    transfer_amount: '50.00',
    apply_date: '2024-01-01 12:00:00',
    bank_code: testConfig.bank_code,
    receive_name: 'Test User',
    receive_account: '*********'
  };

  const payoutSignature = basePayService.generateSignature(payoutParams, testConfig.payout_key);
  console.log('代付签名:', payoutSignature);
}

/**
 * 测试代收回调签名验证
 */
function testPayinCallbackVerification() {
  console.log('\n=== 测试代收回调签名验证 ===');

  const basePayService = new BasePayService(testConfig);

  // 模拟回调数据（根据Base支付文档的实际字段名）
  const callbackData = {
    tradeResult: '1',
    mchId: testConfig.merchant_no,
    mchOrderNo: 'TEST123',
    oriAmount: '100.00',
    amount: '100.00',
    orderDate: '2024-01-01 12:00:00',
    orderNo: 'PLATFORM123',
    merRetMsg: 'test'
  };

  // 生成正确的签名（不包含signType）
  const correctSign = basePayService.generateSignature(callbackData, testConfig.payin_key);
  callbackData.sign = correctSign;
  callbackData.signType = 'MD5'; // 添加signType但不参与签名

  // 验证签名
  const isValid = basePayService.verifyCallbackSignature(callbackData, 'payin_key');
  console.log('代收回调签名验证结果:', isValid ? '通过' : '失败');

  // 测试错误签名
  callbackData.sign = 'wrong_signature';
  const isInvalid = basePayService.verifyCallbackSignature(callbackData, 'payin_key');
  console.log('错误签名验证结果:', isInvalid ? '通过（异常）' : '失败（正常）');
}

/**
 * 测试代付回调签名验证
 */
function testPayoutCallbackVerification() {
  console.log('\n=== 测试代付回调签名验证 ===');

  const basePayService = new BasePayService(testConfig);

  // 模拟代付回调数据（根据Base支付文档的实际字段名）
  const callbackData = {
    tradeResult: '1',
    merTransferId: 'TEST456',
    merNo: testConfig.merchant_no,
    tradeNo: 'PLATFORM456',
    transferAmount: '50.00',
    applyDate: '2024-01-01 12:00:00',
    version: '1.0',
    respCode: 'SUCCESS'
  };

  // 生成正确的签名（不包含signType）
  const correctSign = basePayService.generateSignature(callbackData, testConfig.payout_key);
  callbackData.sign = correctSign;
  callbackData.signType = 'MD5'; // 添加signType但不参与签名

  // 验证签名
  const isValid = basePayService.verifyCallbackSignature(callbackData, 'payout_key');
  console.log('代付回调签名验证结果:', isValid ? '通过' : '失败');
}

/**
 * 测试代收订单创建（不发送真实请求）
 */
function testPayinOrderCreation() {
  console.log('\n=== 测试代收订单创建 ===');
  
  const basePayService = new BasePayService(testConfig);
  
  // 构建代收请求参数（不包含sign_type）
  const orderDate = new Date().toISOString().slice(0, 19).replace('T', ' ');
  const params = {
    version: '1.0',
    mch_id: testConfig.merchant_no,
    notify_url: 'https://m.ohyeah012.xyz/api/payment-callbacks/basepay',
    page_url: 'https://m.ohyeah012.xyz/recharge/success',
    mch_order_no: testDepositOrder.order_number,
    pay_type: testConfig.pay_type,
    trade_amount: testDepositOrder.amount.toFixed(2),
    order_date: orderDate,
    goods_name: 'Top Up',
    mch_return_msg: `user_id:${testDepositOrder.user_id}`
  };

  // 生成签名
  const signature = basePayService.generateSignature(params, testConfig.payin_key);

  // 添加不参与签名的字段
  params.sign_type = 'MD5';
  params.sign = signature;
  
  console.log('代收请求参数:');
  console.log(JSON.stringify(params, null, 2));
}

/**
 * 测试代付订单创建（不发送真实请求）
 */
function testPayoutOrderCreation() {
  console.log('\n=== 测试代付订单创建 ===');
  
  const basePayService = new BasePayService(testConfig);
  
  // 构建代付请求参数（不包含sign_type）
  const applyDate = new Date().toISOString().slice(0, 19).replace('T', ' ');
  const params = {
    mch_id: testConfig.merchant_no,
    mch_transferId: testWithdrawalOrder.order_number,
    transfer_amount: testWithdrawalOrder.actual_amount.toFixed(2),
    apply_date: applyDate,
    bank_code: testConfig.bank_code,
    receive_name: testBankCard.card_holder,
    receive_account: testBankCard.card_number,
    remark: `Withdrawal for user ${testWithdrawalOrder.user_id}`,
    back_url: 'https://m.ohyeah012.xyz/api/payment-callbacks/basepay-payout'
  };

  // 生成签名
  const signature = basePayService.generateSignature(params, testConfig.payout_key);

  // 添加不参与签名的字段
  params.sign_type = 'MD5';
  params.sign = signature;
  
  console.log('代付请求参数:');
  console.log(JSON.stringify(params, null, 2));
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('Base支付通道测试开始...\n');
  
  try {
    testSignatureGeneration();
    testPayinCallbackVerification();
    testPayoutCallbackVerification();
    testPayinOrderCreation();
    testPayoutOrderCreation();
    
    console.log('\n=== 测试完成 ===');
    console.log('所有测试已完成，请检查输出结果');
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 如果直接运行此脚本，则执行测试
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testSignatureGeneration,
  testPayinCallbackVerification,
  testPayoutCallbackVerification,
  testPayinOrderCreation,
  testPayoutOrderCreation,
  runAllTests
};
