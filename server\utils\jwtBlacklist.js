const jwt = require('jsonwebtoken');

// 内存存储用于JWT黑名单
const memoryBlacklist = new Map();

// 清理过期的黑名单条目（每小时运行一次）
setInterval(() => {
  const now = Math.floor(Date.now() / 1000);
  for (const [key, expiry] of memoryBlacklist.entries()) {
    if (expiry <= now) {
      memoryBlacklist.delete(key);
    }
  }
}, 60 * 60 * 1000); // 每小时运行一次

/**
 * 将token添加到黑名单
 * @param {string} token JWT令牌
 * @returns {Promise<boolean>} 是否成功添加
 */
async function addToBlacklist(token) {
  try {
    // 解码token以获取过期时间
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) {
      return false;
    }

    // 将token添加到黑名单，并存储过期时间
    memoryBlacklist.set(token, decoded.exp);

    return true;
  } catch (error) {
    console.error('添加token到黑名单失败:', error);
    return false;
  }
}

/**
 * 检查token是否在黑名单中
 * @param {string} token JWT令牌
 * @returns {Promise<boolean>} 是否在黑名单中
 */
async function isBlacklisted(token) {
  try {
    // 检查token是否在黑名单中
    return memoryBlacklist.has(token);
  } catch (error) {
    console.error('检查token是否在黑名单中失败:', error);
    return false;
  }
}

module.exports = {
  addToBlacklist,
  isBlacklisted
};