"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ono = void 0;
const constructor_1 = require("./constructor");
const singleton = ono;
exports.ono = singleton;
ono.error = new constructor_1.Ono(Error);
ono.eval = new constructor_1.Ono(EvalError);
ono.range = new constructor_1.Ono(RangeError);
ono.reference = new constructor_1.Ono(ReferenceError);
ono.syntax = new constructor_1.Ono(SyntaxError);
ono.type = new constructor_1.Ono(TypeError);
ono.uri = new constructor_1.Ono(URIError);
const onoMap = ono;
/**
 * Creates a new error with the specified message, properties, and/or inner error.
 * If an inner error is provided, then the new error will match its type, if possible.
 */
function ono(...args) {
    let originalError = args[0];
    // Is the first argument an Error-like object?
    if (typeof originalError === "object" && typeof originalError.name === "string") {
        // Try to find an Ono singleton method that matches this error type
        for (let typedOno of Object.values(onoMap)) {
            if (typeof typedOno === "function" && typedOno.name === "ono") {
                let species = typedOno[Symbol.species];
                if (species && species !== Error && (originalError instanceof species || originalError.name === species.name)) {
                    // Create an error of the same type
                    return typedOno.apply(undefined, args);
                }
            }
        }
    }
    // By default, create a base Error object
    return ono.error.apply(undefined, args);
}
//# sourceMappingURL=singleton.js.map