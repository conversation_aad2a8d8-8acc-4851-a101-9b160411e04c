import request from '@/utils/request';

// 获取用户银行卡列表
export function getBankCards() {
  return request({
    url: '/api/account/bank-cards',
    method: 'get'
  });
}

// 添加银行卡
export function addBankCard(data) {
  return request({
    url: '/api/account/bank-cards',
    method: 'post',
    data
  });
}

// 更新银行卡
export function updateBankCard(id, data) {
  return request({
    url: `/api/account/bank-cards/${id}`,
    method: 'put',
    data
  });
}

// 删除银行卡
export function deleteBankCard(id) {
  return request({
    url: `/api/account/bank-cards/${id}`,
    method: 'delete'
  });
}

// 获取银行列表
export function getBanks() {
  return request({
    url: '/api/account/bank-cards/banks',
    method: 'get'
  });
}
