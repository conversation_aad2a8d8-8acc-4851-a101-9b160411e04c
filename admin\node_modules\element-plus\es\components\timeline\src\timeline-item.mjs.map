{"version": 3, "file": "timeline-item.mjs", "sources": ["../../../../../../packages/components/timeline/src/timeline-item.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type TimelineItem from './timeline-item.vue'\n\nexport const timelineItemProps = buildProps({\n  /**\n   * @description timestamp content\n   */\n  timestamp: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether to show timestamp\n   */\n  hideTimestamp: Boolean,\n  /**\n   * @description whether vertically centered\n   */\n  center: Boolean,\n  /**\n   * @description position of timestamp\n   */\n  placement: {\n    type: String,\n    values: ['top', 'bottom'],\n    default: 'bottom',\n  },\n  /**\n   * @description node type\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'warning', 'danger', 'info'],\n    default: '',\n  },\n  /**\n   * @description background color of node\n   */\n  color: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description node size\n   */\n  size: {\n    type: String,\n    values: ['normal', 'large'],\n    default: 'normal',\n  },\n  /**\n   * @description icon component\n   */\n  icon: {\n    type: iconPropType,\n  },\n  /**\n   * @description icon is hollow\n   */\n  hollow: Boolean,\n} as const)\nexport type TimelineItemProps = ExtractPropTypes<typeof timelineItemProps>\n\nexport type TimelineItemInstance = InstanceType<typeof TimelineItem> & unknown\n"], "names": [], "mappings": ";;;AACY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC7B,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC/D,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;AAC/B,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,CAAC;;;;"}