/**
 * 日志工具
 * 提供统一的日志记录接口
 * 优化版：减少不必要的日志输出，提高性能
 */
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// 确保日志目录存在
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 创建日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// 创建控制台输出格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ level, message, timestamp, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
      if (meta.stack) {
        metaStr = `\n${meta.stack}`;
      } else {
        try {
          metaStr = `\n${JSON.stringify(meta, null, 2)}`;
        } catch (e) {
          metaStr = `\n[无法序列化的元数据]`;
        }
      }
    }
    return `${timestamp} ${level}: ${message}${metaStr}`;
  })
);

// 日志过滤器
const logFilter = winston.format((info, opts) => {
  // 过滤掉一些不必要的日志
  const filters = [
    '时区转换',
    'SELECT 1',
    'Headers:',
    'Body:',
  ];

  // 检查是否包含过滤词
  for (const filter of filters) {
    if (info.message && info.message.includes(filter)) {
      return false;
    }
  }

  return info;
});

// 创建日志记录器
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'warn',
  format: winston.format.combine(
    logFilter(),
    logFormat
  ),
  defaultMeta: { service: 'fox-server' },
  transports: [
    // 写入所有日志到 combined.log
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 写入错误日志到 error.log
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 写入投资收益相关日志到 profit.log
    new winston.transports.File({
      filename: path.join(logDir, 'profit.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// 非生产环境下，同时输出到控制台
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
  }));
}

// 添加自定义日志方法
logger.profit = function(message, meta) {
  this.info(message, { ...meta, logType: 'profit' });
};

// 添加性能日志方法
logger.performance = function(operation, startTime, metadata = {}) {
  const duration = Date.now() - startTime;
  const message = `${operation} - ${duration}ms`;

  // 只记录耗时超过100ms的操作
  if (duration > 100) {
    this.warn(message, { ...metadata, duration, logType: 'performance' });
  } else if (duration > 50) {
    this.info(message, { ...metadata, duration, logType: 'performance' });
  } else {
    this.debug(message, { ...metadata, duration, logType: 'performance' });
  }
};

// 添加请求日志中间件
logger.requestLogger = function(req, res, next) {
  const start = Date.now();

  // 请求结束时记录日志
  res.on('finish', () => {
    const duration = Date.now() - start;

    // 只记录耗时超过50ms的请求或非成功状态的请求
    if (duration > 50 || res.statusCode >= 400) {
      const logData = {
        method: req.method,
        url: req.originalUrl || req.url,
        status: res.statusCode,
        duration: `${duration}ms`,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('user-agent') || '',
      };

      // 添加用户信息（如果有）
      if (req.user) {
        logData.userId = req.user.id;
        logData.username = req.user.username;
      }

      // 根据状态码决定日志级别
      if (res.statusCode >= 500) {
        logger.error('请求错误', logData);
      } else if (res.statusCode >= 400) {
        logger.warn('请求警告', logData);
      } else if (duration > 200) {
        logger.warn('请求缓慢', logData);
      } else {
        logger.info('请求完成', logData);
      }
    }
  });

  next();
};

module.exports = logger;
