/**
 * 缓存服务
 * 使用Redis实现数据缓存
 */
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');

// 默认缓存过期时间（秒）
const DEFAULT_CACHE_EXPIRE = 3600; // 1小时

/**
 * 设置缓存
 * @param {string} key - 缓存键
 * @param {any} data - 缓存数据
 * @param {number} [expireSeconds] - 过期时间（秒）
 * @returns {Promise<boolean>} - 返回是否成功
 */
exports.set = async (key, data, expireSeconds = DEFAULT_CACHE_EXPIRE) => {
  try {
    await redisClient.set(key, data, expireSeconds);
    return true;
  } catch (error) {
    logger.error(`设置缓存失败: ${key}`, error);
    return false;
  }
};

/**
 * 获取缓存
 * @param {string} key - 缓存键
 * @returns {Promise<any>} - 返回缓存数据
 */
exports.get = async (key) => {
  try {
    return await redisClient.get(key);
  } catch (error) {
    logger.error(`获取缓存失败: ${key}`, error);
    return null;
  }
};

/**
 * 删除缓存
 * @param {string} key - 缓存键
 * @returns {Promise<boolean>} - 返回是否成功
 */
exports.del = async (key) => {
  try {
    await redisClient.del(key);
    return true;
  } catch (error) {
    logger.error(`删除缓存失败: ${key}`, error);
    return false;
  }
};

/**
 * 获取或设置缓存
 * 如果缓存存在，返回缓存数据
 * 如果缓存不存在，执行回调函数获取数据，并设置缓存
 * @param {string} key - 缓存键
 * @param {Function} callback - 回调函数，用于获取数据
 * @param {number} [expireSeconds] - 过期时间（秒）
 * @returns {Promise<any>} - 返回数据
 */
exports.getOrSet = async (key, callback, expireSeconds = DEFAULT_CACHE_EXPIRE) => {
  try {
    // 尝试从缓存获取数据
    const cachedData = await redisClient.get(key);
    
    // 如果缓存存在，直接返回
    if (cachedData !== null) {
      return cachedData;
    }
    
    // 缓存不存在，执行回调函数获取数据
    const data = await callback();
    
    // 设置缓存
    if (data !== null && data !== undefined) {
      await redisClient.set(key, data, expireSeconds);
    }
    
    return data;
  } catch (error) {
    logger.error(`获取或设置缓存失败: ${key}`, error);
    
    // 如果缓存操作失败，直接执行回调函数获取数据
    try {
      return await callback();
    } catch (callbackError) {
      logger.error(`回调函数执行失败: ${key}`, callbackError);
      throw callbackError;
    }
  }
};

/**
 * 清除指定前缀的所有缓存
 * @param {string} prefix - 缓存键前缀
 * @returns {Promise<boolean>} - 返回是否成功
 */
exports.clearByPrefix = async (prefix) => {
  try {
    // 使用SCAN命令查找匹配的键
    let cursor = '0';
    let keys = [];
    
    do {
      // 使用SCAN命令获取匹配的键
      const result = await redisClient.client.scan(
        cursor,
        'MATCH',
        `${prefix}*`,
        'COUNT',
        100
      );
      
      cursor = result[0];
      keys = keys.concat(result[1]);
      
    } while (cursor !== '0');
    
    // 如果有匹配的键，删除它们
    if (keys.length > 0) {
      await redisClient.client.del(...keys);
      logger.info(`已清除前缀为 ${prefix} 的 ${keys.length} 个缓存`);
    }
    
    return true;
  } catch (error) {
    logger.error(`清除前缀缓存失败: ${prefix}`, error);
    return false;
  }
};

/**
 * 获取系统缓存统计信息
 * @returns {Promise<Object>} - 返回缓存统计信息
 */
exports.getStats = async () => {
  try {
    // 获取Redis信息
    const info = await redisClient.client.info();
    
    // 解析信息
    const stats = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line && !line.startsWith('#')) {
        const parts = line.split(':');
        if (parts.length === 2) {
          stats[parts[0]] = parts[1];
        }
      }
    }
    
    return {
      success: true,
      stats: {
        usedMemory: stats['used_memory_human'],
        usedMemoryPeak: stats['used_memory_peak_human'],
        connectedClients: stats['connected_clients'],
        uptime: stats['uptime_in_seconds'],
        hitRate: stats['keyspace_hits'] && stats['keyspace_misses']
          ? (parseInt(stats['keyspace_hits']) / (parseInt(stats['keyspace_hits']) + parseInt(stats['keyspace_misses'])) * 100).toFixed(2) + '%'
          : '0%'
      }
    };
  } catch (error) {
    logger.error('获取缓存统计信息失败', error);
    return {
      success: false,
      message: '获取缓存统计信息失败: ' + error.message
    };
  }
};
