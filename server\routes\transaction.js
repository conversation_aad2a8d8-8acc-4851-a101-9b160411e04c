const express = require('express');
const transactionController = require('../controllers/transactionController');
const { verifyAdminToken, verifyUserToken } = require('../middlewares/authMiddleware');

// 创建路由器
const router = express.Router();

// 管理员端路由
const adminRouter = express.Router();

// 所有管理员端路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/transactions:
 *   get:
 *     summary: 获取所有交易记录
 *     tags: [交易管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         description: 用户ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [deposit, withdrawal, investment, profit, commission, bonus, deduction]
 *         description: 交易类型
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, success, failed]
 *         description: 交易状态
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 关键词搜索
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', transactionController.getAllTransactions);

/**
 * @swagger
 * /api/admin/transactions/{id}:
 *   get:
 *     summary: 获取交易记录详情
 *     tags: [交易管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 交易记录ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 交易记录不存在
 */
adminRouter.get('/:id', transactionController.getTransactionById);

// 用户端路由
const userRouter = express.Router();

// 所有用户端路由都需要用户认证
userRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/account/transactions:
 *   get:
 *     summary: 获取当前用户的交易记录
 *     tags: [用户账户]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, deposit, withdrawal, investment, profit, commission, bonus, deduction]
 *         description: 交易类型，all表示所有类型
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
userRouter.get('/', transactionController.getUserTransactions);

module.exports = {
  admin: adminRouter,
  user: userRouter
};
