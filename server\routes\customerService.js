/**
 * 客服管理路由
 */
const express = require('express');
const customerServiceController = require('../controllers/customerServiceController');
const { verifyAdminToken, verifyUserToken } = require('../middlewares/authMiddleware');

// 管理端路由
const adminRouter = express.Router();

// 所有管理端路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/customer-services/batch-weights:
 *   put:
 *     summary: 批量更新客服权重
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               required:
 *                 - id
 *                 - weight
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: 客服ID
 *                 weight:
 *                   type: integer
 *                   description: 权重值
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求数据错误
 */
// 注意：这个路由必须放在参数路由之前，否则 Express 会将 'batch-weights' 解析为 ID
adminRouter.put('/batch-weights', customerServiceController.updateCustomerServiceWeights);

/**
 * @swagger
 * /api/admin/customer-services:
 *   get:
 *     summary: 获取客服列表
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: 客服类型
 *       - in: query
 *         name: title
 *         schema:
 *           type: string
 *         description: 客服标题
 *       - in: query
 *         name: status
 *         schema:
 *           type: boolean
 *         description: 状态
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', customerServiceController.getCustomerServices);

/**
 * @swagger
 * /api/admin/customer-services/{id}:
 *   get:
 *     summary: 获取客服详情
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 客服不存在
 */
adminRouter.get('/:id', customerServiceController.getCustomerServiceById);

/**
 * @swagger
 * /api/admin/customer-services:
 *   post:
 *     summary: 创建客服
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *               - url
 *             properties:
 *               type:
 *                 type: string
 *                 description: 客服类型
 *               title:
 *                 type: string
 *                 description: 客服标题
 *               url:
 *                 type: string
 *                 description: 客服链接
 *               status:
 *                 type: boolean
 *                 description: 状态
 *               icon:
 *                 type: string
 *                 description: 图标
 *               weight:
 *                 type: integer
 *                 description: 权重
 *               description:
 *                 type: string
 *                 description: 描述
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 */
adminRouter.post('/', customerServiceController.createCustomerService);

/**
 * @swagger
 * /api/admin/customer-services/{id}:
 *   put:
 *     summary: 更新客服
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 description: 客服类型
 *               title:
 *                 type: string
 *                 description: 客服标题
 *               url:
 *                 type: string
 *                 description: 客服链接
 *               status:
 *                 type: boolean
 *                 description: 状态
 *               icon:
 *                 type: string
 *                 description: 图标
 *               weight:
 *                 type: integer
 *                 description: 权重
 *               description:
 *                 type: string
 *                 description: 描述
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 客服不存在
 */
adminRouter.put('/:id', customerServiceController.updateCustomerService);

/**
 * @swagger
 * /api/admin/customer-services/batch-delete:
 *   post:
 *     summary: 批量删除客服
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 客服ID数组
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 请求数据错误
 */
adminRouter.post('/batch-delete', customerServiceController.batchDeleteCustomerServices);

/**
 * @swagger
 * /api/admin/customer-services/{id}:
 *   delete:
 *     summary: 删除客服
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 客服不存在
 */
adminRouter.delete('/:id', customerServiceController.deleteCustomerService);

/**
 * @swagger
 * /api/admin/customer-services/{id}/qrcode:
 *   get:
 *     summary: 获取客服二维码
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 客服不存在
 */
adminRouter.get('/:id/qrcode', customerServiceController.getCustomerServiceQRCode);

/**
 * @swagger
 * /api/admin/customer-services/batch-update-weights:
 *   post:
 *     summary: 批量更新客服权重
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               required:
 *                 - id
 *                 - weight
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: 客服ID
 *                 weight:
 *                   type: integer
 *                   description: 权重值
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求数据错误
 */
// 使用POST方法而不是PUT，避免与参数路由冲突
adminRouter.post('/batch-update-weights', customerServiceController.updateCustomerServiceWeights);

/**
 * @swagger
 * /api/admin/customer-services/{id}/weight:
 *   put:
 *     summary: 更新客服权重
 *     tags: [客服管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - weight
 *             properties:
 *               weight:
 *                 type: integer
 *                 description: 权重值
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 客服不存在
 */
adminRouter.put('/:id/weight', customerServiceController.updateCustomerServiceWeight);

// 移动端路由
const mobileRouter = express.Router();

// 移除用户认证中间件，使客服API公开访问
// mobileRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/customer-services:
 *   get:
 *     summary: 获取客服列表
 *     tags: [客服]
 *     responses:
 *       200:
 *         description: 获取成功
 */
mobileRouter.get('/', customerServiceController.getMobileCustomerServices);

module.exports = {
  admin: adminRouter,
  mobile: mobileRouter
};
