<template>
  <div class="banners-container">
    <!-- 状态切换标签页 -->
    <div class="tabs">
      <div
        v-for="(tab, index) in statusTabs"
        :key="index"
        :class="['tab', { active: statusFilter === tab.value }]"
        @click="handleTabClick(tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button class="toolbar-button" type="default" @click="fetchData">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button class="toolbar-button" type="success" @click="handleAdd">
          <el-icon><Plus /></el-icon>添加
        </el-button>
        <el-button class="toolbar-button" type="primary" :disabled="selectedRows.length !== 1" @click="handleEdit()">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
        <el-button class="toolbar-button" type="danger" :disabled="selectedRows.length === 0" @click="handleDelete">
          <el-icon><Delete /></el-icon>删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchInput"
          placeholder="搜索轮播图标题"
          class="search-input"
          @keyup.enter="handleSearch"
        />
        <el-button class="search-button" type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
        </el-button>
        <el-button class="toolbar-button export-button" type="default" @click="handleExport">
          <el-icon><Download /></el-icon>导出
        </el-button>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-card class="table-card">
      <div class="table-wrapper">
        <el-table
          ref="bannersTable"
          v-loading="loading"
          :data="tableData"
          border
          stripe
          highlight-current-row
          row-key="id"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          :cell-style="{ padding: '0', height: '44px', lineHeight: '44px' }"
          :header-cell-style="{ background: '#fafafa', color: '#606266', fontWeight: 'bold', padding: '0', height: '44px' }"
        >
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="title" label="标题" min-width="120" align="center" />
          <el-table-column label="图片" width="100" align="center">
            <template #default="scope">
              <div class="image-preview-cell">
                <el-image
                  v-if="scope.row.imageUrl"
                  :src="getFullImageUrl(scope.row.imageUrl)"
                  :preview-src-list="[]"
                  fit="cover"
                  class="banner-image"
                  style="width: 40px; height: 40px; border-radius: 4px;"
                  @error="handleImageError(scope.row)"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div v-else class="no-image">
                  <el-icon><Picture /></el-icon>
                  <span>无图片</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="sort_order" label="权重" width="100" align="center" sortable />
          <el-table-column prop="position" label="物理路径" min-width="180" align="center">
            <template #default="scope">
              <el-tooltip :content="scope.row.position" placement="top" :show-after="500">
                <span class="path-text">{{ scope.row.position }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 || scope.row.status === true ? 'success' : 'danger'">
                {{ scope.row.status === 1 || scope.row.status === true ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180" align="center" sortable>
            <template #default="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" align="center" fixed="right">
            <template #default="scope">
              <div class="operation-buttons-container">
                <el-button
                  class="operation-button icon-only drag-handle"
                  size="small"
                  type="primary"
                  @mousedown.prevent="handleDragStart($event, scope.$index)"
                >
                  <el-icon><Rank /></el-icon>
                </el-button>
                <el-button
                  class="operation-button icon-only"
                  size="small"
                  type="default"
                  @click="handleEdit(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDeleteItem(scope.row)"
                  class="operation-button icon-only"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
    </el-card>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="7"
        background
      >
        <template #sizes>
          <el-select
            :model-value="pageSize"
            @change="handleSizeChange"
            class="custom-page-size"
          >
            <el-option
              v-for="item in [10, 20, 50, 100]"
              :key="item"
              :value="item"
              :label="`${item}/页`"
            />
          </el-select>
        </template>
      </el-pagination>
    </div>

    <!-- 添加/编辑轮播图对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isAddMode ? '添加轮播图' : '编辑轮播图'"
      width="500px"
      center
    >
      <el-form :model="form" label-position="top" label-width="100px" :rules="rules" ref="formRef">
        <el-form-item label="标题">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>

        <el-form-item label="图片" prop="attachment_id" required>
          <div class="image-upload-container">
            <div class="image-preview-wrapper" @click="showAttachmentSelector">
              <div class="image-container" v-if="form.imageUrl">
                <el-image
                  :src="getFullImageUrl(form.imageUrl)"
                  class="banner-image-preview"
                  :preview-src-list="[getFullImageUrl(form.imageUrl)]"
                  fit="cover"
                  style="width: 100px; height: 100px; border-radius: 4px;"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="image-overlay">
                  <el-button
                    type="primary"
                    circle
                    size="small"
                    class="preview-button"
                    @click.stop="previewImage"
                  >
                    <el-icon><View /></el-icon>
                  </el-button>
                </div>
              </div>
              <div v-else class="empty-image-container">
                <el-icon class="image-uploader-icon"><Picture /></el-icon>
                <span class="upload-text">点击选择图片</span>
              </div>
            </div>
            <div class="image-actions">
              <el-button type="primary" size="small" @click="showAttachmentSelector">
                <el-icon><Select /></el-icon>从附件选择图片
              </el-button>
              <div class="form-tip" v-if="form.imageUrl">鼠标移至图片上可预览</div>
            </div>
          </div>
        </el-form-item>



        <el-form-item label="权重" prop="sort_order" required>
          <el-input-number
            v-model="form.sort_order"
            :min="1"
            :max="100"
            :step="1"
            placeholder="请输入权重(1-100)"
          />
          <div class="form-tip">权重范围1-100，1为最高优先级，100为最低优先级</div>
        </el-form-item>

        <el-form-item label="位置">
          <el-input v-model="form.position" placeholder="图片的物理路径，自动生成，无需填写" disabled />
          <div class="form-tip">图片的物理路径，自动生成</div>
        </el-form-item>

        <el-form-item label="状态">
          <el-switch
            v-model="form.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer banner-dialog-footer">
          <el-button type="primary" @click="submitForm">确定</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附件选择器 -->
    <AttachmentSelector
      v-model:visible="attachmentSelectorVisible"
      :fileType="'image'"
      @select="handleAttachmentSelect"
    />

    <!-- 筛选对话框 -->
    <el-dialog
      v-model="filterDialogVisible"
      title="筛选条件"
      width="800px"
      :close-on-click-modal="true"
      @close="filterDialogVisible = false"
      class="filter-dialog"
    >
      <div class="filter-container">
        <!-- 基本信息区域 -->
        <div class="filter-section">
          <div class="section-header">
            <div class="section-title">基本信息</div>
          </div>
          <div class="section-content">
            <div class="filter-grid">
              <div class="filter-item">
                <div class="filter-label">ID</div>
                <el-input v-model="filterForm.id" placeholder="请输入ID" />
              </div>
              <div class="filter-item">
                <div class="filter-label">标题</div>
                <el-input v-model="filterForm.title" placeholder="请输入标题" />
              </div>
              <div class="filter-item">
                <div class="filter-label">URL链接</div>
                <el-input v-model="filterForm.url" placeholder="请输入URL链接" />
              </div>
              <div class="filter-item">
                <div class="filter-label">状态</div>
                <el-select v-model="filterForm.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="全部" value="" />
                  <el-option label="正常" :value="1" />
                  <el-option label="隐藏" :value="0" />
                </el-select>
              </div>
            </div>
          </div>
        </div>

        <!-- 时间区域 -->
        <div class="filter-section">
          <div class="section-header">
            <div class="section-title">时间信息</div>
          </div>
          <div class="section-content">
            <div class="filter-grid">
              <div class="filter-item">
                <div class="filter-label">创建时间</div>
                <el-date-picker
                  v-model="filterForm.createTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                />
              </div>
              <div class="filter-item">
                <div class="filter-label">更新时间</div>
                <el-date-picker
                  v-model="filterForm.updateTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                />
              </div>
              <div class="filter-item">
                <div class="filter-label">权重范围</div>
                <div class="range-inputs">
                  <el-input v-model="filterForm.weightMin" placeholder="最小值" />
                  <span>至</span>
                  <el-input v-model="filterForm.weightMax" placeholder="最大值" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="filter-footer">
          <el-button class="filter-button" type="primary" @click="applyFilter">
            <el-icon><Search /></el-icon>搜索
          </el-button>
          <el-button class="filter-button" @click="resetFilter">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
          <el-button class="filter-button" @click="filterDialogVisible = false">
            <el-icon><Close /></el-icon>取消
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style>
/* 全局样式 - 必须放在style标签内不加scoped */
/* 所有样式现在由JavaScript动态处理 */
.el-card__body {
  padding: 5px !important;
}

.el-table__row {
  height: 44px !important;
}

.el-table__row.hover-row {
  height: 44px !important;
}
</style>

<style scoped>
.banners-container {
  padding: 5px 8px;
  min-height: calc(100vh - 60px);
  background-color: #f5f7fa;
}

/* 标签页样式 */
.tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.tab {
  padding: 8px 16px;
  cursor: pointer;
  margin-right: 8px;
  border-radius: 4px 4px 0 0;
}

.tab.active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.toolbar-left, .toolbar-right {
  display: flex;
  align-items: center;
}

.toolbar-button {
  margin-right: 8px;
}

.search-input {
  width: 200px;
  margin-right: 8px;
}

/* 表格样式 */
.table-card {
  margin-bottom: 5px;
}

:deep(.el-card.is-always-shadow.table-card) {
  margin-bottom: 5px;
}

.table-wrapper {
  margin-bottom: 16px;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;

  th {
    font-weight: bold;
    background-color: #fafafa;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebeef5;
  }

  td {
    height: auto;
    min-height: 40px;
    line-height: 1.5;
    padding: 8px 0;
    border-bottom: 1px solid #ebeef5;
  }

  .el-table__row {
    &:nth-child(odd) {
      background-color: #f9f9f9;
    }
  }

  .el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: #fafafa;
  }

  .el-table__body tr.hover-row > td.el-table__cell,
  .el-table__body tr:hover > td.el-table__cell {
    background-color: #f0f7ff;
  }

  .cell {
    white-space: normal;
    word-break: break-word;
    padding: 0 8px;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    display: none;
  }

  .row-dragging {
    background-color: #e6f7ff !important;
    opacity: 0.8;
    z-index: 10;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
    transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;
  }

  .row-drop-target {
    background-color: #f0f9ff !important;
    border-top: 2px dashed #1890ff;
    animation: pulse-border 1.5s infinite;
  }

  @keyframes pulse-border {
    0% {
      border-top-color: rgba(24, 144, 255, 0.5);
    }
    50% {
      border-top-color: rgba(24, 144, 255, 1);
    }
    100% {
      border-top-color: rgba(24, 144, 255, 0.5);
    }
  }
}

.banner-image {
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.image-preview-cell {
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
}

.no-image {
  width: 40px;
  height: 40px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}

.no-image .el-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.no-image span {
  font-size: 12px;
}

.image-error {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
}

.image-error .el-icon {
  font-size: 24px;
}

.url-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.url-text, .path-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
  display: inline-block;
}

/* 权重样式 - 已移除权重控制按钮 */

.operation-buttons-container {
  display: flex;
  justify-content: center;
  gap: 5px;
}

/* 基本操作按钮样式 */
.operation-button {
  margin: 0 !important;
  padding: 5px 8px !important;
  flex: 0 0 auto;

  &:first-child {
    margin-left: 0 !important;
  }

  &:last-child {
    margin-right: 0 !important;
  }

  &.icon-only {
    padding: 5px !important;
    min-width: 28px;
    height: 28px;
  }
}

/* 图标按钮样式 */
.operation-button .el-icon {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 拖动按钮样式 */
.drag-handle {
  cursor: move !important;
  color: #409eff;

  &:hover {
    color: #1890ff;
    background-color: #f0f9ff;
  }
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding: 0;
}

:deep(.el-pagination.is-background) {
  padding: 0;
}

.custom-page-size {
  width: 100px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 轮播图对话框底部按钮居中 */
.banner-dialog-footer {
  display: flex;
  justify-content: center !important;
  width: 100%;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-preview-wrapper {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  overflow: hidden;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.banner-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.empty-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.upload-text {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.image-container {
  position: relative;
  width: 100px;
  height: 100px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.preview-button {
  background-color: rgba(255, 255, 255, 0.8);
  color: #409EFF;
  border: none;
}

.image-actions {
  display: flex;
  justify-content: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 筛选对话框样式 */
.filter-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.filter-container {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
  background-color: #fff;
}

.filter-section {
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.filter-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-header {
  padding: 12px 20px;
  background-color: #f9fbff;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #409EFF;
  border-radius: 2px;
}

.section-content {
  padding: 16px 20px;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

@media (max-width: 1200px) {
  .filter-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .filter-grid {
    grid-template-columns: 1fr;
  }
}

.filter-item .filter-label {
  font-size: 13px;
  margin-bottom: 8px;
  color: #303133;
  font-weight: 500;
  position: relative;
  padding-left: 10px;
}

.filter-item .filter-label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #409EFF;
}

.filter-item .el-input,
.filter-item .el-select,
.filter-item .el-date-picker {
  width: 100%;
}

.range-inputs {
  display: flex;
  align-items: center;
}

.range-inputs span {
  padding: 0 8px;
  color: #909399;
  flex-shrink: 0;
}

.range-inputs .el-input {
  flex: 1;
}

.filter-footer {
  display: flex;
  justify-content: center;
  padding: 0;
  gap: 16px;
}
</style>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Edit, Delete, Refresh, Search, CopyDocument, ArrowUp, ArrowDown,
  Picture, Select, Filter, Download, Close, View, Sort as Rank
} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import AttachmentSelector from '@/components/AttachmentSelector.vue'
// 导入全局URL配置和处理工具
import { getFullImageUrl as getImageUrl, findWorkingImageUrl } from '@/utils/urlConfig'
import { getBanners, getBanner, createBanner, updateBanner, deleteBanners, updateBannerSortOrder } from '@/api/banner'
import { getAttachmentDetail } from '@/api/attachments'

// 定义轮播图类型
interface Banner {
  id: number
  title?: string
  attachment_id: number
  imageUrl?: string
  position?: string
  sort_order: number
  status: number
  start_time?: string
  end_time?: string
  created_at?: string
  updated_at?: string
}

// 表格引用
const bannersTable = ref<any>(null)
const formRef = ref<FormInstance>()

// 状态和加载
const loading = ref(false)
const dialogVisible = ref(false)
const isAddMode = ref(true)
const searchInput = ref('')
const selectedRows = ref<Banner[]>([])
const statusFilter = ref<string | number>('all')
const attachmentSelectorVisible = ref(false)

// 表格数据
const tableData = ref<Banner[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 已移除日期范围

// 筛选表单
const filterForm = ref({
  id: '',
  title: '',
  url: '',
  status: '',
  position: '',
  weightMin: '',
  weightMax: '',
  createTimeRange: [],
  updateTimeRange: []
})

// 筛选对话框可见性
const filterDialogVisible = ref(false)

// 表单数据
const form = ref<Partial<Banner>>({
  title: '',
  attachment_id: undefined,
  imageUrl: '',
  position: '',
  sort_order: 1,
  status: 1
})

// 表单验证规则
const rules = {
  attachment_id: [
    { required: true, message: '请选择图片', trigger: 'change' }
  ],
  sort_order: [
    { required: true, message: '请输入权重', trigger: 'blur' }
  ]
}

// 状态选项卡
const statusTabs = [
  { label: '全部', value: 'all' },
  { label: '启用', value: '1' },
  { label: '禁用', value: '0' }
]

// 格式化日期时间
const formatDateTime = (dateTimeStr: string | undefined) => {
  if (!dateTimeStr) return '-'
  return new Date(dateTimeStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 复制URL到剪贴板
const copyToClipboard = (text: string) => {
  if (!text) {
    ElMessage.warning('没有可复制的内容')
    return
  }

  navigator.clipboard.writeText(text)
    .then(() => {
      ElMessage.success('复制成功')
    })
    .catch(() => {
      ElMessage.error('复制失败')
    })
}

// 处理选择变化
const handleSelectionChange = (rows: Banner[]) => {
  selectedRows.value = rows
}

// 处理行点击
const handleRowClick = (row: Banner, column: any, event: MouseEvent) => {
  // 如果点击的是复选框列或操作列，不做额外处理
  if (column.type === 'selection' || column.label === '操作') {
    return
  }

  // 模拟点击行的复选框
  const table = bannersTable.value
  if (table) {
    table.toggleRowSelection(row)
  }
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchData()
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchData()
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 处理标签页点击
const handleTabClick = (value: string | number) => {
  statusFilter.value = value
  currentPage.value = 1
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      status: statusFilter.value !== 'all' ? statusFilter.value : undefined,
      search: searchInput.value || undefined,
      position: filterForm?.value?.position || undefined,
      sort_order_min: filterForm?.value?.weightMin || undefined,
      sort_order_max: filterForm?.value?.weightMax || undefined,
      sort: 'sort_order', // 按权重字段排序
      order: 'asc' // 升序排列，权重小的排在前面
    }

    const response = await getBanners(params)
    // response 已经是 data 字段的内容了
    const { items, total: totalCount } = response

    // 处理图片URL
    const banners = await Promise.all(items.map(async (item: any) => {
      let imageUrl = ''
      if (item.attachment_id) {
        try {
          const attachment = await getAttachmentDetail(item.attachment_id)
          // attachment 已经是附件详情数据了
          if (attachment && attachment.file_path) {
            imageUrl = attachment.file_path
          } else if (attachment && attachment.filePath) {
            // 有些附件可能使用 filePath 而不是 file_path
            imageUrl = attachment.filePath
          } else {
            console.warn('附件没有有效的文件路径:', attachment)
            // 尝试使用 position 作为备用
            imageUrl = item.position || ''
          }
        } catch (error) {
          console.error('获取附件详情失败:', error)
          // 如果获取附件详情失败，使用 position 作为图片URL
          imageUrl = item.position || ''
        }
      } else if (item.position) {
        // 如果没有附件ID但有位置，使用位置作为图片URL
        imageUrl = item.position
      }

      // 处理状态字段，将布尔值转换为数字
      const status = item.status === true ? 1 : 0;

      // 确保图片URL使用正确的服务器地址
      const finalImageUrl = imageUrl ? getFullImageUrl(imageUrl) : '';

      return {
        ...item,
        imageUrl: finalImageUrl,
        status
      }
    }))

    tableData.value = banners
    total.value = totalCount
  } catch (error) {
    console.error('获取轮播图数据失败:', error)
    ElMessage.error('获取数据失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 根据附件ID获取图片URL
const getImageUrl = async (attachmentId: number) => {
  if (!attachmentId) return ''

  try {
    // response 已经是附件详情数据了
    const attachment = await getAttachmentDetail(attachmentId)
    return attachment.file_path || ''
  } catch (error) {
    console.error('获取图片URL失败:', error)
  }
  return ''
}

// 处理权重变更 - 已移除权重控制按钮

// 更新轮播图排序值
const updateBannerSortOrders = async () => {
  try {
    // 假设表格数据按sort_order升序排列，数值越小排序越靠前
    // 为每个轮播图分配新的权重值，从10开始，间隔为10
    const updatePromises = tableData.value.map((banner, index) => {
      const newWeight = (index + 1) * 10 // 10, 20, 30, 40...

      // 如果权重值已经改变，则更新
      if (banner.sort_order !== newWeight) {
        console.log(`更新轮播图 ${banner.id} 的权重: ${banner.sort_order} -> ${newWeight}`)
        return updateBannerSortOrder(banner.id, newWeight)
      }

      return Promise.resolve()
    })

    // 等待所有更新完成
    await Promise.all(updatePromises)

    console.log('所有轮播图权重值已更新')
    return true
  } catch (error) {
    console.error('更新轮播图排序值失败:', error)
    throw error
  }
}

// 重新计算所有轮播图的权重值，确保唯一性
const recalculateSortOrders = async () => {
  try {
    // 获取所有轮播图数据
    const response = await getBanners({
      limit: 1000,
      sort: 'sort_order',
      order: 'asc'
    }) // 获取所有轮播图，按权重升序排序
    const banners = response.items

    if (!banners || banners.length === 0) return

    // 重新分配权重值，从10开始，间隔为10
    const updatePromises = banners.map((banner, index) => {
      const newWeight = (index + 1) * 10 // 10, 20, 30, 40...

      // 如果权重值已经改变，则更新
      if (banner.sort_order !== newWeight) {
        console.log(`重新计算轮播图 ${banner.id} 的权重: ${banner.sort_order} -> ${newWeight}`)
        return updateBannerSortOrder(banner.id, newWeight)
      }

      return Promise.resolve()
    })

    // 等待所有更新完成
    await Promise.all(updatePromises)

    console.log('所有轮播图权重值已重新计算')

    // 重新加载数据，确保按照权重排序显示
    await fetchData()
  } catch (error) {
    console.error('重新计算权重值失败:', error)
    throw error
  }
}

// 拖拽相关变量
const draggedItem = ref<any>(null)
const draggedIndex = ref<number>(-1)
const dragOverIndex = ref<number>(-1)
const isDragging = ref(false)

// 处理拖拽开始
const handleDragStart = (event: MouseEvent, index: number) => {
  // 记录被拖拽的项和索引
  draggedIndex.value = index
  draggedItem.value = tableData.value[index]
  isDragging.value = true

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)

  // 添加拖拽样式
  const rows = document.querySelectorAll('.el-table__row')
  const row = rows[index]
  if (row) {
    // 添加动画类
    row.classList.add('row-dragging')

    // 为其他行添加淡化效果
    rows.forEach((r, i) => {
      if (i !== index) {
        r.style.opacity = '0.6'
        r.style.transition = 'opacity 0.3s ease'
      }
    })

    // 添加拖动反馈
    const dragFeedback = document.createElement('div')
    dragFeedback.textContent = '正在移动...'
    dragFeedback.style.position = 'fixed'
    dragFeedback.style.top = '10px'
    dragFeedback.style.left = '50%'
    dragFeedback.style.transform = 'translateX(-50%)'
    dragFeedback.style.padding = '8px 16px'
    dragFeedback.style.backgroundColor = '#409eff'
    dragFeedback.style.color = 'white'
    dragFeedback.style.borderRadius = '4px'
    dragFeedback.style.zIndex = '9999'
    dragFeedback.style.boxShadow = '0 2px 12px rgba(0, 0, 0, 0.2)'
    dragFeedback.style.opacity = '0'
    dragFeedback.style.transition = 'opacity 0.3s ease'
    dragFeedback.id = 'drag-feedback'
    document.body.appendChild(dragFeedback)

    // 显示反馈
    setTimeout(() => {
      dragFeedback.style.opacity = '1'
    }, 100)
  }
}

// 处理拖拽移动
const handleDragMove = (event: MouseEvent) => {
  if (!isDragging.value) return

  // 获取鼠标位置下的元素
  const element = document.elementFromPoint(event.clientX, event.clientY)
  if (!element) return

  // 查找最近的表格行
  const row = element.closest('.el-table__row')
  if (!row) return

  // 获取行索引
  const rows = Array.from(document.querySelectorAll('.el-table__row'))
  const currentIndex = rows.indexOf(row as HTMLElement)

  if (currentIndex !== -1 && currentIndex !== draggedIndex.value) {
    // 移除之前的目标样式
    rows.forEach(r => r.classList.remove('row-drop-target'))

    // 添加新的目标样式
    row.classList.add('row-drop-target')

    // 更新拖动反馈信息
    const dragFeedback = document.getElementById('drag-feedback')
    if (dragFeedback) {
      const targetItem = tableData.value[currentIndex]
      dragFeedback.textContent = `正在移动到 "${targetItem.title || '未命名'}" 之前...`
    }

    // 如果目标索引变化，添加一个小动画效果
    if (dragOverIndex.value !== currentIndex) {
      const targetRow = rows[currentIndex]
      targetRow.animate([
        { transform: 'translateY(-3px)' },
        { transform: 'translateY(0)' }
      ], {
        duration: 300,
        easing: 'ease-out'
      })
    }

    dragOverIndex.value = currentIndex
  }
}

// 处理拖拽结束
const handleDragEnd = async () => {
  // 移除事件监听
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)

  // 移除拖动反馈
  const dragFeedback = document.getElementById('drag-feedback')
  if (dragFeedback) {
    dragFeedback.style.opacity = '0'
    // 等待淡出动画完成后移除元素
    setTimeout(() => {
      dragFeedback.remove()
    }, 300)
  }

  // 恢复所有行的透明度
  const rows = document.querySelectorAll('.el-table__row')
  rows.forEach(row => {
    row.style.opacity = '1'
  })

  // 如果有有效的拖放目标
  if (isDragging.value && draggedItem.value && dragOverIndex.value !== -1 && dragOverIndex.value !== draggedIndex.value) {
    try {
      // 显示成功消息
      ElMessage({
        message: '正在更新排序...',
        type: 'info',
        duration: 1000
      })

      // 移动数据（先在本地更新顺序）
      const item = tableData.value.splice(draggedIndex.value, 1)[0]
      tableData.value.splice(dragOverIndex.value, 0, item)

      // 更新排序值
      await updateBannerSortOrders()

      // 显示成功消息
      ElMessage({
        message: '更新排序成功，已重新计算所有权重',
        type: 'success',
        duration: 2000
      })

      // 刷新数据，确保按照权重排序
      await fetchData()
    } catch (error) {
      console.error('更新排序失败:', error)
      ElMessage.error('更新排序失败，请检查网络连接')

      // 移除所有样式
      rows.forEach(row => {
        row.classList.remove('row-dragging')
        row.classList.remove('row-drop-target')
      })
    }
  } else {
    // 如果没有有效的拖放目标，只移除样式
    rows.forEach(row => {
      row.classList.remove('row-dragging')
      row.classList.remove('row-drop-target')
    })
  }

  // 重置拖拽状态
  isDragging.value = false
  draggedItem.value = null
  draggedIndex.value = -1
  dragOverIndex.value = -1
}

// 处理添加
const handleAdd = () => {
  isAddMode.value = true
  form.value = {
    title: '',
    attachment_id: undefined,
    imageUrl: '',
    position: '',
    sort_order: 1,
    status: 1 // 1表示启用
  }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row?: Banner) => {
  if (!row) {
    if (selectedRows.value.length !== 1) {
      ElMessage.warning('请选择一条记录')
      return
    }
    row = selectedRows.value[0]
  }

  isAddMode.value = false

  // 重置表单数据，然后复制选中行的数据
  // 确保状态字段是数字类型（1或0）
  console.log('编辑轮播图，原始状态值:', row.status, typeof row.status);
  const status = row.status === true || row.status === 1 ? 1 : 0;
  console.log('编辑轮播图，转换后状态值:', status, typeof status);

  form.value = {
    id: row.id,
    title: row.title,
    attachment_id: row.attachment_id,
    imageUrl: row.imageUrl,
    position: row.position,
    sort_order: row.sort_order,
    status: status
  }

  // 已移除日期范围处理

  dialogVisible.value = true
}

// 处理删除
const handleDelete = () => {
  const banners = selectedRows.value

  if (banners.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }

  const bannerIds = banners.map(item => item.id)
  const message = banners.length > 1
    ? `确定要删除选中的 ${banners.length} 条轮播图记录吗？`
    : '确定要删除该轮播图记录吗？'

  ElMessageBox.confirm(message, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteBanners(bannerIds)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      console.error('删除轮播图失败:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}

// 处理删除单项
const handleDeleteItem = (row: Banner) => {
  ElMessageBox.confirm('确定要删除该轮播图记录吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteBanners([row.id])
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      console.error('删除轮播图失败:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}

// 显示筛选对话框
const handleFilter = () => {
  filterDialogVisible.value = true
}

// 处理导出
const handleExport = () => {
  ElMessage.info('功能尚未实现')
}

// 默认图片
const defaultImage = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgMjQwIj48cmVjdCB3aWR0aD0iMjQwIiBoZWlnaHQ9IjI0MCIgZmlsbD0iI2YyZjJmMiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBhbGlnbm1lbnQtYmFzZWxpbmU9Im1pZGRsZSIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSwgc2Fucy1zZXJpZiIgZmlsbD0iI2NjY2NjYyI+5Zu+54mH5peg5rOV5Yqg6L29PC90ZXh0Pjwvc3ZnPg==';

// 获取完整的图片URL
const getFullImageUrl = (url: string | null | undefined): string => {
  return getImageUrl(url, defaultImage);
}

// 处理图片加载错误
const handleImageError = async (row: Banner) => {
  console.error('图片加载失败:', row.imageUrl);

  // 使用全局工具函数查找可用的URL
  if (row.imageUrl) {
    const workingUrl = await findWorkingImageUrl(row.imageUrl, row.position);
    if (workingUrl && workingUrl !== row.imageUrl) {
      // 如果找到可用的URL，更新行数据
      console.log(`找到可用的URL: ${workingUrl}，更新图片地址`);
      row.imageUrl = workingUrl;
      // 强制重新渲染
      tableData.value = [...tableData.value];
    }
  }

  // 显示错误提示
  ElMessage({
    message: `图片加载失败: ${row.title || '未命名轮播图'}`,
    type: 'warning',
    duration: 2000
  });
}

// 重置筛选表单
const resetFilter = () => {
  filterForm.value = {
    id: '',
    title: '',
    url: '',
    status: '',
    weightMin: '',
    weightMax: '',
    createTimeRange: [],
    updateTimeRange: []
  }
}

// 应用筛选条件
const applyFilter = () => {
  filterDialogVisible.value = false
  currentPage.value = 1
  fetchData()
  ElMessage.success('筛选条件已应用')
}

// 显示附件选择器
const showAttachmentSelector = () => {
  attachmentSelectorVisible.value = true
}

// 预览图片
const previewImage = () => {
  if (form.value.imageUrl) {
    // 使用Element Plus的图片预览组件
    const url = getFullImageUrl(form.value.imageUrl);

    // 创建一个临时的图片预览器
    const imageViewer = document.createElement('div');
    imageViewer.className = 'el-image-viewer__wrapper';
    imageViewer.style.zIndex = '2000';

    // 创建遮罩层
    const mask = document.createElement('div');
    mask.className = 'el-image-viewer__mask';
    imageViewer.appendChild(mask);

    // 创建图片容器
    const container = document.createElement('div');
    container.className = 'el-image-viewer__container';
    imageViewer.appendChild(container);

    // 创建图片元素
    const img = document.createElement('img');
    img.src = form.value.imageUrl;
    img.className = 'el-image-viewer__img';
    img.style.maxWidth = '100%';
    img.style.maxHeight = '100%';
    container.appendChild(img);

    // 创建关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.className = 'el-image-viewer__close';
    closeBtn.innerHTML = '<i class="el-icon-close"></i>';
    closeBtn.onclick = () => {
      document.body.removeChild(imageViewer);
    };
    imageViewer.appendChild(closeBtn);

    // 点击遮罩层关闭预览
    mask.onclick = () => {
      document.body.removeChild(imageViewer);
    };

    // 添加到body
    document.body.appendChild(imageViewer);

    // 显示成功消息
    ElMessage.success('图片预览已打开');
  }
}

// 处理附件选择
const handleAttachmentSelect = (attachment: any) => {
  if (attachment) {
    console.log('选中的附件:', attachment);
    form.value.attachment_id = attachment.id

    // 使用 url 属性作为图片URL
    form.value.imageUrl = attachment.url || attachment.file_path

    // 使用 file_path 作为位置（图片的物理路径）
    // 如果是完整URL，则提取相对路径部分
    if (attachment.file_path && attachment.file_path.includes('/uploads/')) {
      // 提取 /uploads/ 开始的部分
      form.value.position = attachment.file_path.substring(attachment.file_path.indexOf('/uploads/'));
    } else {
      form.value.position = attachment.file_path;
    }

    // 显示成功消息
    ElMessage.success('图片选择成功')

    // 关闭附件选择器
    nextTick(() => {
      attachmentSelectorVisible.value = false
    })
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      ElMessage.error('请完善表单信息')
      return
    }

    // 已移除日期范围处理
    form.value.start_time = undefined
    form.value.end_time = undefined

    try {
      let response

      // 准备提交的数据，移除前端专用字段
      const submitData = { ...form.value };
      delete submitData.imageUrl; // 移除前端显示用的图片URL
      delete submitData.url; // 移除已废弃的url字段

      // 确保position字段正确
      if (submitData.position && submitData.position.includes('http')) {
        // 如果position是完整URL，则提取相对路径部分
        if (submitData.position.includes('/uploads/')) {
          submitData.position = submitData.position.substring(submitData.position.indexOf('/uploads/'));
        }
      } else if (!submitData.position && submitData.attachment_id && form.value.imageUrl) {
        // 如果没有position但有attachment_id和imageUrl，从imageUrl提取相对路径
        if (form.value.imageUrl.includes('/uploads/')) {
          submitData.position = form.value.imageUrl.substring(form.value.imageUrl.indexOf('/uploads/'));
        } else {
          submitData.position = form.value.imageUrl;
        }
      }

      // 确保状态字段正确
      if (submitData.status === 1 || submitData.status === '1') {
        submitData.status = true;
      } else if (submitData.status === 0 || submitData.status === '0') {
        submitData.status = false;
      }

      if (isAddMode.value) {
        // 添加模式
        response = await createBanner(submitData)
      } else {
        // 编辑模式
        const { id, ...updateData } = submitData
        response = await updateBanner(id!, updateData)
      }

      // 如果代码执行到这里，说明请求已经成功
      // 因为request.ts中的响应拦截器会在失败时抛出异常
      ElMessage.success(isAddMode.value ? '添加成功' : '更新成功')
      dialogVisible.value = false
      fetchData()
    } catch (error) {
      console.error(isAddMode.value ? '添加轮播图失败:' : '更新轮播图失败:', error)
      ElMessage.error(isAddMode.value ? '添加失败' : '更新失败')
    }
  })
}

// 初始加载
onMounted(async () => {
  await fetchData()

  // 页面加载时重新计算权重，确保唯一性
  try {
    await recalculateSortOrders()
    // 重新获取数据，显示更新后的权重值
    await fetchData()
  } catch (error) {
    console.error('初始化权重计算失败:', error)
  }
})
</script>