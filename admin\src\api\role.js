import request from '@/utils/request'

// 将响应数据转换为标准格式
function formatResponse(response) {
  // 如果原始响应中已经有code和message，则保留这些值
  const code = response.data && response.data.code ? response.data.code : 200;
  const message = response.data && response.data.message ? response.data.message : 'success';

  return {
    code: code,
    message: message,
    data: response.data && response.data.data ? response.data.data : response
  }
}

// 获取角色列表
export function getRoles(params) {
  return request({
    url: '/api/admin/roles',
    method: 'get',
    params
  }).then(response => formatResponse(response))
}

// 获取角色详情
export function getRole(id) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'get'
  }).then(response => formatResponse(response))
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/api/admin/roles',
    method: 'post',
    data
  }).then(response => formatResponse(response))
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'put',
    data
  }).then(response => formatResponse(response))
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'delete'
  }).then(response => formatResponse(response))
}

// 获取所有权限
export function getAllPermissions() {
  return request({
    url: '/api/admin/roles/permissions/all',
    method: 'get'
  }).then(response => formatResponse(response))
}
