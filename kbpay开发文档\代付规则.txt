代付下单（温馨提示：代付每秒最多5笔，批量代付请注意设置sleep间隔，Form表单提交参数）
测试地址：
https://api.kbpay.io/debug/payout/submit
正式地址：
https://api.kbpay.io/online/payout/submit
请求方式：
POST
Header：
'Content-Type': 'application/x-www-form-urlencoded'
♥ 请求参数
参数	示例值	参数名	类型	是否必填	说明
merchant_no	6035002	商户号	String	Y	平台分配唯一
order_no	***************	商户单号	String	Y	保证每笔订单唯一
order_amount	873.00	交易金额【单位：元】	Float	Y	交易金额【单位：元，float类型, 保留2位小数】
notify_url	http://192.168.0.195:8082/demo/repay/notice	通知地址	String	Y	通知代付的请求地址（说明：该地址请勿携带【?、=、*、.】等特殊符号，切记！切记！切记！）
payout_method	10142	代付编码	Integer	Y	请查阅商户后台【开发资料】->【代付方式】->【代付编码】
account_name	诸葛瑾	姓名	String	Y	收款账号持有者名称
account_no	*****************	账号或卡号	String	Y	账号或卡号
timestamp	**********	秒级时间戳【10位】	Integer	Y	秒级时间戳【10位】
order_attach	repay:6035002	商户自定义参数	String	N	商户订单附加信息，如有填写，通知代付中原样返回
bank_code	-	IFSC	String	N	开户银行编码，印度网银代付必填IFSC编码
bank_sub	-	分行	String	N	分行(日本代付必须填银行分店编码，斯里兰卡代付必须传分行名称)
account_type	CPF	账号类型	String	N	收款账号类型：巴西PIX代付，需要填写，分为CPF, CNPJ, PHONE, EMAIL, EVP五种。智利的帐户类型 Chequera Electrónica、Cuenta Corriente、Cuenta RUT、Cuenta Ahorro
account_attach	-	附加信息	String	N	收款账号附加信息（秘鲁代付，需要填写秘鲁CCI）
document_type	-	证件类型	String	N	证件类型
document_no	476465	证件编号	String	N	证件编号：加纳银行代付填写身份证号码， 智利必须填写税号
mobile_no	**********	真实手机号	String	N	真实手机号
sign	64be6b8d199591bd7f1aef00818d01e4	签名	String	Y	不参与签名，使用【代付密钥->MD5密钥】对query_string&key=payout_key进行md5运算得到sign的值
♥ 成功响应示例
{
  "code": 0, //0为成功，其他数字表示异常，异常描述在message字段中
  "data": {
    "merchant_no": "6035002", //商户号
    "order_amount": 873.00, //下单金额（float）
    "order_no": "***************", //商户单号
    "trade_no": "R**********9996992" //平台单号
  }, //返回数据
  "message": "success" //描述信息
}
♥ 失败响应示例
{
  "code": 1000, //0为成功，其他数字表示异常，异常描述在message字段中
  "message": "银行代码错误" //描述信息
}
♥ 代付下单go DEMO
package main

import (
	"api/util"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
)

func main() {
	//代付密钥
	payout_key := "ertwekmd923423kle90wdlsld23"
	params := url.Values{}
	params.Add("merchant_no", "6011567")
	params.Add("order_no", "**********")
	// params.Add("order_amount", 100.15) //金额（float）
        params.Add("order_amount", fmt.Sprintf("%.2f", 200.00)) //金额（float）
	params.Add("notify_url", "https://www.xxx1111.com")
	params.Add("timestamp", **********)
	params.Add("account_no", "**********")
	params.Add("account_name", "zhangsan")
	params.Add("bank_code", "UBIN0534579")
	params.Add("payout_method", 1066)
	unescape, _ := url.QueryUnescape(params.Encode())
	sign_str := unescape + "&key=" + payout_key
	sign := util.Md5(sign_str)
	params.Add("sign", sign)
	//使用post form 方式提交到支付网址，请自行替换为真实支付网址
	response, rsp_err := http.PostForm("https://api.kbpay.io/debug/payout/submit", params)
	if rsp_err != nil {
		fmt.Println(rsp_err.Error())
	}
	response_body, _ := ioutil.ReadAll(response.Body)
	fmt.Println("pay submit rs body is:", string(response_body))
}
♥ 代付下单 php DEMO
<?php
// 定义curl的post请求方法
function post_form($url, $data) {
   //初使化init方法
   $ch = curl_init();
   //指定URL
   curl_setopt($ch, CURLOPT_URL, $url);
   //设定请求后返回结果
   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
   //声明使用POST方式来进行发送
   curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
   //发送的数据
   curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
   //忽略证书
   curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
   curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
   //不需要响应的header头信息
   curl_setopt($ch, CURLOPT_HEADER, false);
   //设置请求的header头信息
   curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/x-www-form-urlencoded",   
   ));
   //设置超时时间
   curl_setopt($ch, CURLOPT_TIMEOUT, 10);
   //发送请求
   $object = new stdClass ();
   $object->response = curl_exec ( $ch );
   $object->info = curl_getinfo ( $ch );
   $object->error_code = curl_errno ( $ch );
   $object->error = curl_error ( $ch );
   //关闭curl
   curl_close($ch);
   //返回数据
   return $object;
}
//代付密钥
$payout_key ="43CQVasnjric0f1srdX8mQ8MEV1LYdMl";  
$data["merchant_no"]="6056003";
$data["order_no"]="**************";
// $data["order_amount"]= 120.38; //金额（float）
$data["order_amount"]= sprintf("%.2f", 200); 金额（float）
$data["notify_url"]="http://www.test111.com";
$data["bank_code"]="UBIN0534579";
$data["payout_method"]= 1041;
$data["account_no"]="*********";
$data["account_name"]="zhangsan";
$data["timestamp"]= time();
//排序
ksort($data);
//拼接字符串
$unescape = urldecode(http_build_query($data));
$sign_str = $unescape . "&key=" . $payout_key;
//签名
$data["sign"] = md5($sign_str);
//发送post的form请求
$url = "https://api.kbpay.io/debug/payout/submit";
$rs = post_form($url, $data);
var_export($rs);

代付通知（返回Form表单提交的通知数据）
请求地址：
代付请求参数中的notify_url（说明：该地址请勿携带【?、=、*、.】等特殊符号，切记！切记！切记！）
请求方式：
POST
Header：
'Content-Type': 'application/x-www-form-urlencoded'
说明：当请求notify_url地址返回http【状态码为200】【输出文本内容为"success"】即表示通知已送达。否则服务器将尝试3次请求后停止发送通知。
♥ 请求参数（Form表单）
参数	示例值	参数名	类型	是否必填	说明
merchant_no	6035002	商户号	String	Y	平台分配唯一
order_no	***************	商户单号	String	Y	保证每笔订单唯一
trade_no	R**********9996992	平台单号	String	Y	平台分配唯一
order_amount	873.00	订单金额【单位：元】	Float	Y	订单金额【单位：元，float类型, 保留2位小数】
trade_amount	45.00	实际支付金额【单位：元】	Float	Y	实际支付金额【单位：元，float类型, 保留2位小数】
trade_status	2	代付状态	Integer	Y	代付状态【0 下单成功】【1 处理中】【2 代付成功】【4 已取消】
timestamp	1655990955	秒级时间戳【10位】	Integer	Y	秒级时间戳【10位】
order_attach	-	商户自定义参数	String	N	商户自定义参数
cancel_message	-	取消原因	String	N	取消原因
sign	c8390187ec86a0db17c1e3f2b3f23aea	签名	String	Y	不参与签名，使用【代付密钥->MD5钥】对query_string&key=payout_key进行md5运算得到sign的值
代付查询（Form表单提交查询数据）
测试地址：
https://api.kbpay.io/debug/payout/query
正式地址：
https://api.kbpay.io/online/payout/query
请求方式：
POST
Header：
'Content-Type': 'application/x-www-form-urlencoded'
♥ 请求参数
参数	示例值	参数名	类型	是否必填	说明
merchant_no	6035002	商户号	String	Y	平台分配唯一
order_no	16559900261120728	商户单号	String	Y	商户提供的唯一单号
timestamp	1655990739	秒级时间戳【10位】	Integer	Y	秒级时间戳【10位】
sign	60c95d9d35684b21956675fc5fad2487	签名	String	Y	不参与签名，使用【代付密钥->MD5密钥】对query_string&key=payout_key进行md5运算得到sign的值
♥ 成功响应示例
{
  "code": 0, //0为成功，其他数字表示异常，异常描述在message字段中
  "data": {
    "merchant_no": "6035002", //商户号
    "order_amount": 873.00, //下单金额【单位：元，float类型，保留2位小数】
    "order_no": "***************", //商户单号
    "trade_amount": 873.00, //支付金额【单位：元，float类型，保留2位小数】
    "trade_status": 0, //代付状态[0下单成功][1处理中][2代付成功][4已取消]
    "trade_no": "R**********9996992", //平台单号
    "trade_status_format": "wait"  // 交易状态格式化
  }, //返回数据
  "message": "success" //描述信息
}
♥ 失败响应示例
{
  "code": 1000, //0为成功，其他数字表示异常，异常描述在message字段中
  "message": "订单不存在" //描述信息
}
♥ 返回参数
参数名	参数类型	参数描述
merchant_no	String	商户号
order_no	String	商户单号
order_amount	Float	下单金额【单位：元，float类型，保留2位小数】
trade_amount	Float	实际支付金额【单位：元，float类型，保留2位小数】
trade_status	Integer	代付状态【0 下单成功】【1 处理中】【2 代付成功】【4 已取消】
trade_no	String	平台单号
♥ 代付查询 go DEMO
package main

import (
	"api/util"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
)

func main() {
	//代付密钥
	payout_key := "ertwekmd923423kle90wdlsld23"
	params := url.Values{}
	params.Add("merchant_no", "6011567")
	params.Add("order_no", "R45634533234")
	params.Add("timestamp", **********)
	unescape, _ := url.QueryUnescape(params.Encode())
	sign_str := unescape + "&key=" + payout_key
	sign := util.Md5(sign_str)
	params.Add("sign", sign)
	//使用post form 方式提交到支付网址，请自行替换为真实支付网址
	response, rsp_err := http.PostForm("https://api.kbpay.io/debug/payout/query", params)
	if rsp_err != nil {
		fmt.Println(rsp_err.Error())
	}
	response_body, _ := ioutil.ReadAll(response.Body)
	fmt.Println("payout submit rs body is:", string(response_body))
}
♥ 代付查询php DEMO
<?php
/定义curl的post请求方法
function post_form($url, $data) {
   //初使化init方法
   $ch = curl_init();
   //指定URL
   curl_setopt($ch, CURLOPT_URL, $url);
   //设定请求后返回结果
   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
   //声明使用POST方式来进行发送
   curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
   //发送的数据
   curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
   //忽略证书
   curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
   curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
   //不需要响应的header头信息
   curl_setopt($ch, CURLOPT_HEADER, false);
   //设置请求的header头信息
   curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/x-www-form-urlencoded",   
   ));
   //设置超时时间
   curl_setopt($ch, CURLOPT_TIMEOUT, 10);
   //发送请求
   $object = new stdClass ();
   $object->response = curl_exec ( $ch );
   $object->info = curl_getinfo ( $ch );
   $object->error_code = curl_errno ( $ch );
   $object->error = curl_error ( $ch );
   //关闭curl
   curl_close($ch);
   //返回数据
   return $object;
}
//代付密钥
$payout_key ="43CQVasnjric0f1srdX8mQ8MEV1LYdMl";  
$data["merchant_no"]="6056003";
$data["order_no"]="P234345324234";
$data["timestamp"]= time();
//排序
ksort($data);
//拼接字符串
$unescape = urldecode(http_build_query($data));
$sign_str = $unescape . "&key=" . $payout_key;
//签名
$data["sign"] = md5($sign_str);
//发送post的form请求
$url = "https://api.kbpay.io/debug/payout/query";
$rs = post_form($url, $data);
var_export($rs);

余额查询（Form表单提交查询数据）
正式地址：
https://api.kbpay.io/balance
请求方式：
POST
Header：
'Content-Type': 'application/x-www-form-urlencoded'
♥ 请求参数
参数	示例值	参数名	类型	是否必填	说明
merchant_no	6035002	商户号	String	Y	平台分配唯一
timestamp	1655990842	秒级时间戳【10位】	Integer	Y	秒级时间戳【10位】
sign	a737815b856d427ff0ee2a5a25bd174e	签名	String	Y	不参与签名，使用【代付密钥->MD5密钥】对query_string&key=payout_key进行md5运算得到sign的值
♥ 成功响应示例
{
  "code": 0, //0为成功，其他数字表示异常，异常描述在message字段中
  "data": {
    "available_amount": 711.66, //可用资金，单位元，float类型，保留2位小数
    "currency_name": "BRL", //货币名称
    "currency_tag": "R$", //货币标识符
    "frozen_amount": 1952.28, //冻结金额，单位元，float类型，保留2位小数
    "merchant_no": "6035002", //商户号
    "balance_amount": 2663.94 //总金额，单位元，float类型，保留2位小数
  }, //返回数据
  "message": "success" //描述信息
}
♥ 失败响应示例
{
  "code": 1000, //0为成功，其他数字表示异常，异常描述在message字段中
  "message": "字段:MerchantNo 传值:16554536362345 校验:商户号错误" //描述信息
}
♥ 返回参数
参数名	参数类型	参数描述
merchant_no	String	商户号
available_amount	Float	可用资金【单位：元，float类型, 保留2位小数】
currency_name	String	货币名称
currency_tag	String	货币标识符
frozen_amount	Float	冻结金额【单位：元，float类型, 保留2位小数】
balance_amount	Float	总金额【单位：元，float类型, 保留2位小数】
