// 简单的服务器测试文件
require('dotenv').config();
const express = require('express');

console.log('开始启动测试服务器...');

const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());

// 简单的测试路由
app.get('/test', (req, res) => {
  res.json({ message: '服务器运行正常' });
});

// 测试手续费服务
app.get('/test-fee', async (req, res) => {
  try {
    console.log('测试手续费计算服务...');
    const withdrawalFeeService = require('./services/withdrawalFeeService');
    
    const result = await withdrawalFeeService.calculateWithdrawalFee(100);
    console.log('手续费计算结果:', result);
    
    res.json({
      message: '手续费计算成功',
      data: result
    });
  } catch (error) {
    console.error('手续费计算失败:', error);
    res.status(500).json({
      message: '手续费计算失败',
      error: error.message
    });
  }
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`测试服务器运行在 http://localhost:${PORT}`);
  console.log('测试路由: /test');
  console.log('手续费测试路由: /test-fee');
});
