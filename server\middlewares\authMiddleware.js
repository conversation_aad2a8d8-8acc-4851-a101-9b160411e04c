const jwt = require('jsonwebtoken');
const { Admin, Role, User } = require('../models');
const { isBlacklisted } = require('../utils/jwtBlacklist');

// 验证管理员JWT令牌
exports.verifyAdminToken = async (req, res, next) => {
  try {
    // 从请求头获取令牌
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        code: 401,
        message: '未提供认证令牌',
        data: null
      });
    }

    const token = authHeader.split(' ')[1];

    // 使用token服务验证令牌
    const tokenService = require('../services/tokenService');
    const verifyResult = await tokenService.verifyAdminToken(token);

    if (!verifyResult.valid) {
      return res.status(401).json({
        code: 401,
        message: verifyResult.message,
        data: null
      });
    }

    const { decoded } = verifyResult;

    // 查找管理员
    const admin = await Admin.findByPk(decoded.id);
    if (!admin) {
      return res.status(404).json({
        code: 404,
        message: '管理员不存在',
        data: null
      });
    }

    // 检查管理员状态
    if (!admin.status) {
      return res.status(403).json({
        code: 403,
        message: '管理员账号已被禁用',
        data: null
      });
    }

    // 将管理员信息添加到请求对象
    req.admin = admin;
    req.token = token;
    next();
  } catch (error) {
    console.error('认证中间件错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};



// 验证用户JWT令牌
exports.verifyUserToken = async (req, res, next) => {
  try {
    // 从请求头获取令牌
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        code: 401,
        message: 'Authentication token not provided',
        data: null
      });
    }

    const token = authHeader.split(' ')[1];

    // 检查token是否在黑名单中
    const tokenBlacklisted = await isBlacklisted(token);
    if (tokenBlacklisted) {
      return res.status(401).json({
        code: 401,
        message: 'Token has expired, please login again',
        data: null
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: [process.env.JWT_ALGORITHM || 'HS512', 'HS256'] // 支持新旧算法，确保兼容性
    });

    // 检查令牌类型
    if (decoded.type !== 'user') {
      return res.status(403).json({
        code: 403,
        message: 'Invalid token type',
        data: null
      });
    }

    // 查找用户
    const user = await User.findByPk(decoded.id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(403).json({
        code: 403,
        message: '用户账号已被禁用',
        data: null
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;

    // 尝试刷新用户token
    try {
      const tokenService = require('../services/tokenService');
      const refreshResult = await tokenService.refreshUserToken(token);

      // 如果刷新成功，在响应头中设置新token
      if (refreshResult.success) {
        res.setHeader('X-New-Token', refreshResult.token);
        console.log(`User token refreshed for user ID: ${user.id}`);
      }
    } catch (error) {
      // 如果刷新出错，记录错误但不中断请求
      console.error('User token refresh error:', error);
    }

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        code: 401,
        message: '令牌已过期',
        data: null
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        code: 401,
        message: '无效的令牌',
        data: null
      });
    }

    console.error('用户认证中间件错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 检查管理员权限
exports.checkAdminRole = (roleIds = []) => {
  return async (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({
        code: 401,
        message: '未认证',
        data: null
      });
    }

    // 超级管理员拥有所有权限
    if (req.admin.is_super) {
      return next();
    }

    // 获取管理员角色
    const admin = await Admin.findByPk(req.admin.id, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id'],
          through: { attributes: [] }
        }
      ]
    });

    // 检查是否有权限
    if (roleIds.length === 0 || admin.roles.some(role => roleIds.includes(role.id))) {
      return next();
    }

    return res.status(403).json({
      code: 403,
      message: '权限不足',
      data: null
    });
  };
};
