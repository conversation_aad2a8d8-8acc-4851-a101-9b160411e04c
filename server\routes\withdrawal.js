/**
 * 取款订单路由
 */
const express = require('express');
const withdrawalController = require('../controllers/withdrawalController');
const mobileWithdrawalController = require('../controllers/mobileWithdrawalController');
const { verifyAdminToken, verifyUserToken } = require('../middlewares/authMiddleware');

// 管理员路由
const adminRouter = express.Router();

// 所有管理员路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/withdrawals:
 *   get:
 *     summary: 获取取款订单列表
 *     tags: [取款管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 订单状态
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         description: 用户ID
 *       - in: query
 *         name: user_id_str
 *         schema:
 *           type: string
 *         description: 用户ID字符串
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 关键词
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', withdrawalController.getWithdrawals);

/**
 * @swagger
 * /api/admin/withdrawals/{id}:
 *   get:
 *     summary: 获取取款订单详情
 *     tags: [取款管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 取款订单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 取款订单不存在
 */
adminRouter.get('/:id', withdrawalController.getWithdrawal);

/**
 * @swagger
 * /api/admin/withdrawals/{id}/approve:
 *   put:
 *     summary: 审核取款订单
 *     tags: [取款管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 取款订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [completed, rejected]
 *                 description: 订单状态
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 审核成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 取款订单不存在
 */
adminRouter.put('/:id/approve', withdrawalController.approveWithdrawal);

/**
 * @swagger
 * /api/admin/withdrawals/{id}/mock:
 *   post:
 *     summary: 模拟取款完成
 *     tags: [取款管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 取款订单ID
 *     responses:
 *       200:
 *         description: 模拟取款完成成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 取款订单不存在
 */
adminRouter.post('/:id/mock', withdrawalController.mockWithdrawal);

/**
 * @swagger
 * /api/admin/withdrawals/{id}/payment-failure:
 *   post:
 *     summary: 处理支付失败
 *     tags: [取款管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 取款订单ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               remark:
 *                 type: string
 *                 description: 备注信息
 *     responses:
 *       200:
 *         description: 处理支付失败成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 取款订单不存在
 */
adminRouter.post('/:id/payment-failure', withdrawalController.handlePaymentFailure);

// 移动端路由
const mobileRouter = express.Router();

// 所有移动端路由都需要用户认证
mobileRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/withdrawals:
 *   post:
 *     summary: 创建取款订单
 *     tags: [移动端取款]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *                 description: 取款金额
 *               bank_card_id:
 *                 type: integer
 *                 description: 银行卡ID
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 *       401:
 *         description: 未认证
 */
mobileRouter.post('/', mobileWithdrawalController.createWithdrawal);

/**
 * @swagger
 * /api/mobile/withdrawals:
 *   get:
 *     summary: 获取用户取款记录
 *     tags: [移动端取款]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 订单状态
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
mobileRouter.get('/', mobileWithdrawalController.getUserWithdrawals);

module.exports = {
  admin: adminRouter,
  mobile: mobileRouter
};
