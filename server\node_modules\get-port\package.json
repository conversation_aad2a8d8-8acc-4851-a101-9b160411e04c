{"name": "get-port", "version": "5.1.1", "description": "Get an available port", "license": "MIT", "repository": "sindresorhus/get-port", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["port", "find", "finder", "portfinder", "free", "available", "connection", "connect", "open", "net", "tcp", "scan", "random", "preferred", "chosen"], "devDependencies": {"@types/node": "^12.12.21", "ava": "^2.4.0", "tsd": "^0.11.0", "xo": "^0.25.3"}}