'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查索引是否存在
      const [indexes] = await queryInterface.sequelize.query(
        "SHOW INDEX FROM account_balances WHERE Key_name = 'account_balances_user_id_account_type_currency_unique'"
      );

      // 1. 如果索引存在，则删除
      if (indexes.length > 0) {
        console.log('删除唯一索引 account_balances_user_id_account_type_currency_unique...');
        await queryInterface.removeIndex(
          'account_balances',
          'account_balances_user_id_account_type_currency_unique'
        );
      } else {
        console.log('索引 account_balances_user_id_account_type_currency_unique 不存在，跳过删除');
      }

      // 检查新索引是否存在
      const [newIndexes] = await queryInterface.sequelize.query(
        "SHOW INDEX FROM account_balances WHERE Key_name = 'account_balances_user_id_account_type_unique'"
      );

      // 2. 如果新索引不存在，则创建
      if (newIndexes.length === 0) {
        console.log('创建新的唯一索引 account_balances_user_id_account_type_unique...');
        await queryInterface.addIndex(
          'account_balances',
          ['user_id', 'account_type'],
          {
            unique: true,
            name: 'account_balances_user_id_account_type_unique'
          }
        );
      } else {
        console.log('索引 account_balances_user_id_account_type_unique 已存在，跳过创建');
      }

      // 检查字段是否存在
      const tableInfo = await queryInterface.describeTable('account_balances');

      // 3. 如果currency字段存在，则删除
      if (tableInfo.currency) {
        console.log('删除currency字段...');
        await queryInterface.removeColumn('account_balances', 'currency');
      } else {
        console.log('currency字段不存在，跳过删除');
      }

      console.log('成功处理account_balances表');
      return Promise.resolve();
    } catch (error) {
      console.error('处理account_balances表失败:', error);
      return Promise.reject(error);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 1. 删除唯一索引
      await queryInterface.removeIndex(
        'account_balances',
        'account_balances_user_id_account_type_unique'
      );

      // 2. 添加currency字段
      await queryInterface.addColumn('account_balances', 'currency', {
        type: Sequelize.ENUM('CNY', 'USDT'),
        allowNull: false,
        defaultValue: 'CNY',
        comment: '货币类型'
      });

      // 3. 创建新的唯一索引（包含currency字段）
      await queryInterface.addIndex(
        'account_balances',
        ['user_id', 'account_type', 'currency'],
        {
          unique: true,
          name: 'account_balances_user_id_account_type_currency_unique'
        }
      );

      console.log('成功恢复account_balances表中的currency字段');
      return Promise.resolve();
    } catch (error) {
      console.error('恢复account_balances表中的currency字段失败:', error);
      return Promise.reject(error);
    }
  }
};
