@mixin utils-clearfix {
  $selector: &;

  @at-root {
    #{$selector}::before,
    #{$selector}::after {
      display: table;
      content: '';
    }
    #{$selector}::after {
      clear: both;
    }
  }
}

@mixin utils-vertical-center {
  $selector: &;

  @at-root {
    #{$selector}::after {
      display: inline-block;
      content: '';
      height: 100%;
      vertical-align: middle;
    }
  }
}

@mixin utils-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin utils-inline-flex-center {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
