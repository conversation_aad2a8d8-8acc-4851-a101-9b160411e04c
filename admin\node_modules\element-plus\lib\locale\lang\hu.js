'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var hu = {
  name: "hu",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "T\xF6rl\xE9s"
    },
    datepicker: {
      now: "Most",
      today: "Ma",
      cancel: "M\xE9gse",
      clear: "T\xF6rl\xE9s",
      confirm: "OK",
      selectDate: "D\xE1tum",
      selectTime: "Id\u0151pont",
      startDate: "D\xE1tum-t\xF3l",
      startTime: "Id\u0151pont-t\xF3l",
      endDate: "D\xE1tum-ig",
      endTime: "Id\u0151pont-ig",
      prevYear: "El\u0151z\u0151 \xE9v",
      nextYear: "K\xF6vetkez\u0151 \xE9v",
      prevMonth: "El\u0151z\u0151 h\xF3nap",
      nextMonth: "K\xF6vetkez\u0151 h\xF3nap",
      year: "",
      month1: "Janu\xE1r",
      month2: "Febru\xE1r",
      month3: "M\xE1rcius",
      month4: "\xC1prilis",
      month5: "M\xE1jus",
      month6: "J\xFAnius",
      month7: "J\xFAlius",
      month8: "Augusztus",
      month9: "Szeptember",
      month10: "Okt\xF3ber",
      month11: "November",
      month12: "December",
      weeks: {
        sun: "Vas",
        mon: "H\xE9t",
        tue: "Ked",
        wed: "Sze",
        thu: "Cs\xFC",
        fri: "P\xE9n",
        sat: "Szo"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "M\xE1r",
        apr: "\xC1pr",
        may: "M\xE1j",
        jun: "J\xFAn",
        jul: "J\xFAl",
        aug: "Aug",
        sep: "Szep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "Bet\xF6lt\xE9s",
      noMatch: "Nincs tal\xE1lat",
      noData: "Nincs adat",
      placeholder: "V\xE1lassz"
    },
    mention: {
      loading: "Bet\xF6lt\xE9s"
    },
    cascader: {
      noMatch: "Nincs tal\xE1lat",
      loading: "Bet\xF6lt\xE9s",
      placeholder: "V\xE1lassz",
      noData: "Nincs adat"
    },
    pagination: {
      goto: "Ugr\xE1s",
      pagesize: "/oldal",
      total: "\xD6ssz {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "\xDCzenet",
      confirm: "OK",
      cancel: "M\xE9gse",
      error: "Hib\xE1s adat"
    },
    upload: {
      deleteTip: "kattints a t\xF6rl\xE9shez",
      delete: "T\xF6rl\xE9s",
      preview: "El\u0151n\xE9zet",
      continue: "Tov\xE1bb"
    },
    table: {
      emptyText: "Nincs adat",
      confirmFilter: "Meger\u0151s\xEDt",
      resetFilter: "Alaphelyet",
      clearFilter: "Mind",
      sumText: "\xD6sszeg"
    },
    tree: {
      emptyText: "Nincs adat"
    },
    transfer: {
      noMatch: "Nincs tal\xE1lat",
      noData: "Nincs adat",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Kulcssz\xF3",
      noCheckedFormat: "{total} elem",
      hasCheckedFormat: "{checked}/{total} kiv\xE1lasztva"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

exports["default"] = hu;
//# sourceMappingURL=hu.js.map
