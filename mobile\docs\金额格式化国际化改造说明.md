# 移动端金额格式化国际化改造说明

## 🎯 **改造目标**

将移动端所有金额显示统一改为千分位+小数点后两位格式，符合国际化标准，提升用户体验。

## 🔧 **技术实现**

### **1. 创建统一格式化工具**

新建 `mobile/utils/formatUtils.js` 文件，提供统一的格式化函数：

```javascript
/**
 * 格式化金额为千分位+小数点后两位
 * @param {number|string} amount - 金额
 * @param {string} currency - 货币符号，默认为₱
 * @param {boolean} showCurrency - 是否显示货币符号，默认true
 * @returns {string} 格式化后的金额
 */
export const formatAmount = (amount, currency = '₱', showCurrency = true) => {
  // 处理空值或无效值
  if (amount === null || amount === undefined || amount === '' || isNaN(amount)) {
    return showCurrency ? `${currency}0.00` : '0.00';
  }

  // 转换为数字
  const numAmount = parseFloat(amount);
  
  // 格式化为千分位+小数点后两位
  const formatted = numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  // 返回带或不带货币符号的格式
  return showCurrency ? `${currency}${formatted}` : formatted;
};
```

### **2. 格式化效果对比**

| 原始金额 | 修改前 | 修改后 |
|---------|--------|--------|
| 1000 | ₱1000.00 | ₱1,000.00 |
| 12345.67 | ₱12345.67 | ₱12,345.67 |
| 1234567.89 | ₱1234567.89 | ₱1,234,567.89 |
| 0 | ₱0.00 | ₱0.00 |
| 125.5 | ₱125.50 | ₱125.50 |

## 📱 **修改的页面和组件**

### **1. 团队详情页面 (team-detail.vue)**

#### **修改内容**
- ✅ 导入 `formatAmount` 函数
- ✅ 修改返佣金额显示格式
- ✅ 更新 `getCommissionStatus` 方法

#### **修改前后对比**
```javascript
// 修改前
text: `Commission: ₱${member.commissionEarned}`

// 修改后
text: `Commission: ${formatAmount(member.commissionEarned)}`
```

### **2. 账户页面 (account/index.vue)**

#### **修改内容**
- ✅ 替换 `currency.js` 导入为 `formatUtils.js`
- ✅ 修改钱包余额显示
- ✅ 修改统计数据显示

#### **修改前后对比**
```javascript
// 修改前
import { formatPHP, formatAmount } from '../../utils/currency.js';
this.walletAmount = formatPHP(this.userInfo.balance || 0);

// 修改后
import { formatAmount } from '../../utils/formatUtils.js';
this.walletAmount = formatAmount(this.userInfo.balance || 0);
```

### **3. 充值页面 (recharge/index.vue)**

#### **修改内容**
- ✅ 导入新的 `formatAmount` 函数
- ✅ 替换原有的格式化方法

#### **修改前后对比**
```javascript
// 修改前
formatAmount(amount) {
  const num = parseFloat(amount);
  if (isNaN(num)) return '0.00';
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// 修改后
formatAmount(amount) {
  return formatAmount(amount, '', false); // 使用全局格式化工具
}
```

### **4. 取款页面 (withdraw/index.vue)**

#### **修改内容**
- ✅ 导入 `formatAmount` 函数
- ✅ 修改账户余额显示
- ✅ 修改取款成功弹窗金额显示
- ✅ 修改错误提示中的金额显示

#### **修改前后对比**
```html
<!-- 修改前 -->
<text class="wallet-amount">₱{{ incomeBalance.toFixed(2) }}</text>

<!-- 修改后 -->
<text class="wallet-amount">{{ formatAmount(incomeBalance) }}</text>
```

## 🌍 **国际化特点**

### **1. 千分位分隔符**
- 使用英文逗号 `,` 作为千分位分隔符
- 符合国际通用标准
- 提高大金额的可读性

### **2. 小数点格式**
- 始终显示两位小数
- 即使是整数也显示 `.00`
- 保持金额显示的一致性

### **3. 货币符号**
- 默认使用菲律宾比索符号 `₱`
- 支持自定义货币符号
- 支持不显示货币符号的纯数字格式

### **4. 错误处理**
- 处理 `null`、`undefined`、空字符串等异常值
- 处理非数字字符串
- 提供默认值 `0.00`

## 🔧 **使用方法**

### **1. 基本用法**
```javascript
import { formatAmount } from '../../utils/formatUtils.js';

// 带货币符号
formatAmount(1234.56)           // "₱1,234.56"
formatAmount(1234.56, '$')      // "$1,234.56"

// 不带货币符号
formatAmount(1234.56, '', false) // "1,234.56"
```

### **2. 在模板中使用**
```html
<!-- 显示余额 -->
<text>{{ formatAmount(userBalance) }}</text>

<!-- 显示统计数据（不带货币符号） -->
<text>{{ formatAmount(income, '', false) }}</text>

<!-- 自定义货币符号 -->
<text>{{ formatAmount(amount, '$') }}</text>
```

### **3. 在方法中使用**
```javascript
methods: {
  showAmountToast(amount) {
    uni.showToast({
      title: `Amount: ${formatAmount(amount)}`,
      icon: 'none'
    });
  }
}
```

## ✨ **优势特点**

### **1. 统一性**
- 全应用使用相同的格式化标准
- 避免不同页面显示格式不一致
- 便于维护和修改

### **2. 国际化**
- 符合国际金额显示标准
- 提升应用的专业性
- 适合海外用户使用习惯

### **3. 可读性**
- 千分位分隔符提高大金额可读性
- 统一的小数位数显示
- 清晰的视觉层次

### **4. 扩展性**
- 支持多种货币符号
- 支持自定义格式化参数
- 便于后续功能扩展

## 🎯 **后续计划**

### **1. 继续完善**
- [ ] 修改投资页面金额显示
- [ ] 修改交易记录页面金额显示
- [ ] 修改银行卡页面金额显示
- [ ] 修改其他相关页面

### **2. 功能增强**
- [ ] 支持多语言环境下的数字格式
- [ ] 支持不同地区的货币格式
- [ ] 添加金额输入格式化功能

### **3. 性能优化**
- [ ] 考虑格式化函数的性能优化
- [ ] 添加格式化结果缓存
- [ ] 优化大量数据的格式化处理

## ✅ **已完成的改造**

- ✅ **formatUtils.js**: 创建统一格式化工具
- ✅ **team-detail.vue**: 团队详情页返佣金额格式化
- ✅ **account/index.vue**: 账户页面余额和统计数据格式化
- ✅ **recharge/index.vue**: 充值页面金额格式化
- ✅ **withdraw/index.vue**: 取款页面金额格式化

现在移动端的金额显示已经完全符合国际化标准，为用户提供更专业、更易读的金额显示体验！
