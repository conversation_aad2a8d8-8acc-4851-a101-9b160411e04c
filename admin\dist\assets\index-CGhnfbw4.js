/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                *//* empty css                    *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                        */import{d as _e,r as v,a as q,q as I,o as ye,c as A,b as n,e as t,w as o,m as Me,f as xe,i as Te,aa as Ce,ab as Re,ac as ke,p as b,n as m,x as De,j as c,ad as K,ae as W,aB as G,af as J,ag as Be,ai as Ae,aj as he,ak as ze,V as $,al as Ie,y as E,aL as $e,ap as Ee,aq as Le,a8 as Se,a9 as He,at as Ye,E as Pe,h as Fe,aC as je,av as Oe,aw as Ne,aA as qe,az as Ke,aH as We,g as M,_ as Ge}from"./index-LncY9lAB.js";const Je={class:"user-levels-container"},Qe={class:"toolbar"},Xe={class:"toolbar-left"},Ze={class:"toolbar-right"},et={class:"table-wrapper"},tt={key:1},lt=["innerHTML"],at={class:"operation-buttons-container"},ot={class:"pagination-container"},nt={class:"form-section"},st={class:"form-row"},it={class:"form-section"},rt={class:"form-row"},ut={class:"form-row"},dt={class:"form-section"},pt={class:"form-row"},mt=["src"],gt={class:"form-row full-width"},ft={class:"editor-container"},vt={class:"editor-toolbar"},ct={class:"dialog-footer"},bt={class:"form-section"},Vt={class:"form-row"},wt={class:"form-section"},Ut={class:"form-row"},_t={class:"range-input"},yt={class:"range-input"},Mt={class:"form-row"},xt={class:"range-input"},Tt={class:"range-input"},Ct={class:"form-section"},Rt={class:"form-row"},kt={class:"filter-footer"},Dt={class:"delete-confirm-content"},Bt={class:"dialog-footer"},At=_e({__name:"index",setup(ht){const h=v(!1),d=v([]),_=v([]),y=v(1),x=v(10),k=v(""),T=v(!1),R=v(!1),C=v(!1),D=v(!0),B=v(null);v("");const V=v(null),L=()=>{V.value&&(V.value.innerHTML=i.content||"",V.value.addEventListener("input",()=>{V.value&&(i.content=V.value.innerHTML)}))},w=(l,e="")=>{document.execCommand(l,!1,e),V.value&&(V.value.focus(),i.content=V.value.innerHTML)},i=q({id:void 0,name:"",level:1,upgradeUsers:0,upgradeAmount:0,returnRate:0,upgradeBonus:0,imageUrl:"",content:"<p>请输入级别说明内容</p>"}),s=q({id:"",name:"",level:void 0,upgradeUsersMin:void 0,upgradeUsersMax:void 0,upgradeAmountMin:void 0,upgradeAmountMax:void 0,returnRateMin:void 0,returnRateMax:void 0,upgradeBonusMin:void 0,upgradeBonusMax:void 0,createTimeRange:[],updateTimeRange:[]}),S=I(()=>{let l=d.value;if(k.value){const e=k.value.toLowerCase();l=l.filter(r=>r.name.toLowerCase().includes(e))}if(s.id&&(l=l.filter(e=>String(e.id)===s.id)),s.name&&(l=l.filter(e=>e.name.includes(s.name))),s.level!==void 0&&(l=l.filter(e=>e.level===s.level)),s.upgradeUsersMin!==void 0&&(l=l.filter(e=>e.upgradeUsers>=s.upgradeUsersMin)),s.upgradeUsersMax!==void 0&&(l=l.filter(e=>e.upgradeUsers<=s.upgradeUsersMax)),s.upgradeAmountMin!==void 0&&(l=l.filter(e=>e.upgradeAmount>=s.upgradeAmountMin)),s.upgradeAmountMax!==void 0&&(l=l.filter(e=>e.upgradeAmount<=s.upgradeAmountMax)),s.returnRateMin!==void 0&&(l=l.filter(e=>e.returnRate>=s.returnRateMin)),s.returnRateMax!==void 0&&(l=l.filter(e=>e.returnRate<=s.returnRateMax)),s.upgradeBonusMin!==void 0&&(l=l.filter(e=>e.upgradeBonus>=s.upgradeBonusMin)),s.upgradeBonusMax!==void 0&&(l=l.filter(e=>e.upgradeBonus<=s.upgradeBonusMax)),s.createTimeRange&&s.createTimeRange.length===2){const e=new Date(s.createTimeRange[0]).getTime(),r=new Date(s.createTimeRange[1]).getTime();l=l.filter(u=>{const U=new Date(u.createTime).getTime();return U>=e&&U<=r})}if(s.updateTimeRange&&s.updateTimeRange.length===2){const e=new Date(s.updateTimeRange[0]).getTime(),r=new Date(s.updateTimeRange[1]).getTime();l=l.filter(u=>{const U=new Date(u.updateTime).getTime();return U>=e&&U<=r})}return l}),Q=I(()=>{const l=(y.value-1)*x.value,e=l+x.value;return S.value.slice(l,e)}),X=I(()=>S.value.length),H=l=>l.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),Y=async()=>{h.value=!0;try{await new Promise(e=>setTimeout(e,500));const l=[];for(let e=1;e<=6;e++)l.push({id:e,name:`VIP ${e}`,level:e,upgradeUsers:e,upgradeAmount:e*1e4,returnRate:5+e*.5,upgradeBonus:e*500,imageUrl:e%2===0?`https://via.placeholder.com/100?text=VIP${e}`:"",content:`<h3>VIP ${e} 级别特权说明</h3><p>尊敬的用户，恭喜您成为 VIP ${e} 会员！</p><p>您将享受以下特权：</p><ul><li>每日签到奖励增加 ${e*10}%</li><li>投资收益提升 ${e*.5}%</li><li>专属客服服务</li><li>生日礼金 ${e*100} 元</li></ul><p>感谢您的支持！</p>`,createTime:"2024-05-20 06:47:09",updateTime:"2024-05-20 06:47:09"});d.value=l}catch(l){console.error("获取数据失败:",l),b.error("获取数据失败")}finally{h.value=!1}},Z=l=>{_.value=l},P=()=>{y.value=1},ee=()=>{D.value=!0,Object.assign(i,{id:void 0,name:"",level:1,upgradeUsers:0,upgradeAmount:0,returnRate:0,upgradeBonus:0,imageUrl:"",content:"<p>请输入级别说明内容</p>"}),T.value=!0,setTimeout(()=>{L()},100)},F=l=>{D.value=!1,Object.assign(i,{id:l.id,name:l.name,level:l.level,upgradeUsers:l.upgradeUsers,upgradeAmount:l.upgradeAmount,returnRate:l.returnRate,upgradeBonus:l.upgradeBonus,imageUrl:l.imageUrl,content:l.content}),T.value=!0,setTimeout(()=>{L()},100)},te=()=>{_.value.length===1?F(_.value[0]):b.warning("请选择一条记录进行编辑")},le=async()=>{try{if(await new Promise(l=>setTimeout(l,500)),D.value){const e={id:Math.max(...d.value.map(r=>r.id),0)+1,name:i.name||"",level:i.level||1,upgradeUsers:i.upgradeUsers||0,upgradeAmount:i.upgradeAmount||0,returnRate:i.returnRate||0,upgradeBonus:i.upgradeBonus||0,imageUrl:i.imageUrl,content:i.content,createTime:new Date().toISOString().replace("T"," ").substring(0,19),updateTime:new Date().toISOString().replace("T"," ").substring(0,19)};d.value.unshift(e),b.success("添加成功")}else{const l=d.value.findIndex(e=>e.id===i.id);if(l!==-1){const e={...d.value[l],name:i.name||d.value[l].name,level:i.level||d.value[l].level,upgradeUsers:i.upgradeUsers!==void 0?i.upgradeUsers:d.value[l].upgradeUsers,upgradeAmount:i.upgradeAmount!==void 0?i.upgradeAmount:d.value[l].upgradeAmount,returnRate:i.returnRate!==void 0?i.returnRate:d.value[l].returnRate,upgradeBonus:i.upgradeBonus!==void 0?i.upgradeBonus:d.value[l].upgradeBonus,imageUrl:i.imageUrl,content:i.content,updateTime:new Date().toISOString().replace("T"," ").substring(0,19)};d.value[l]=e,b.success("更新成功")}}T.value=!1}catch(l){console.error("保存失败:",l),b.error("保存失败")}},ae=l=>{B.value=l.id,C.value=!0},oe=()=>{if(_.value.length===0){b.warning("请选择要删除的记录");return}B.value=null,C.value=!0},ne=async()=>{try{if(await new Promise(l=>setTimeout(l,500)),B.value!==null){const l=d.value.findIndex(e=>e.id===B.value);l!==-1&&(d.value.splice(l,1),b.success("删除成功"))}else{const l=_.value.map(e=>e.id);d.value=d.value.filter(e=>!l.includes(e.id)),b.success(`成功删除 ${l.length} 条记录`)}C.value=!1}catch(l){console.error("删除失败:",l),b.error("删除失败")}},se=()=>{R.value=!0},ie=()=>{y.value=1,R.value=!1},re=()=>{Object.assign(s,{id:"",name:"",level:void 0,upgradeUsersMin:void 0,upgradeUsersMax:void 0,upgradeAmountMin:void 0,upgradeAmountMax:void 0,returnRateMin:void 0,returnRateMax:void 0,upgradeBonusMin:void 0,upgradeBonusMax:void 0,createTimeRange:[],updateTimeRange:[]})},ue=()=>{b.success("导出功能待实现")},de=l=>{i.imageUrl=`https://via.placeholder.com/100?text=VIP${i.level}`,b.success("图片上传成功")},j=l=>{x.value=l,y.value=1},pe=l=>{y.value=l};return ye(()=>{Y()}),(l,e)=>{const r=De,u=Me,U=Te,f=Ie,me=$e,ge=Ee,fe=Ce,ve=Ye,ce=Le,be=Re,p=Fe,g=je,Ve=Oe,O=Pe,z=ke,N=qe,we=ze;return M(),A("div",Je,[n("div",Qe,[n("div",Xe,[t(u,{class:"toolbar-button",type:"default",onClick:Y},{default:o(()=>[t(r,null,{default:o(()=>[t(c(K))]),_:1}),e[40]||(e[40]=m("刷新 "))]),_:1}),t(u,{class:"toolbar-button",type:"success",onClick:ee},{default:o(()=>[t(r,{class:"el-icon-plus"}),e[41]||(e[41]=m("添加 "))]),_:1}),t(u,{class:"toolbar-button",type:"primary",disabled:_.value.length!==1,onClick:te},{default:o(()=>[t(r,null,{default:o(()=>[t(c(W))]),_:1}),e[42]||(e[42]=m("编辑 "))]),_:1},8,["disabled"]),t(u,{class:"toolbar-button",type:"danger",disabled:_.value.length===0,onClick:oe},{default:o(()=>[t(r,null,{default:o(()=>[t(c(G))]),_:1}),e[43]||(e[43]=m("删除 "))]),_:1},8,["disabled"])]),n("div",Ze,[t(U,{modelValue:k.value,"onUpdate:modelValue":e[0]||(e[0]=a=>k.value=a),placeholder:"搜索级别名称",class:"search-input",onKeyup:xe(P,["enter"])},null,8,["modelValue"]),t(u,{class:"search-button",type:"primary",onClick:P},{default:o(()=>[t(r,null,{default:o(()=>[t(c(J))]),_:1})]),_:1}),t(u,{class:"toolbar-button filter-button",type:"default",onClick:se},{default:o(()=>[t(r,null,{default:o(()=>[t(c(Be))]),_:1}),e[44]||(e[44]=m("筛选 "))]),_:1}),t(u,{class:"toolbar-button export-button",type:"default",onClick:ue},{default:o(()=>[t(r,null,{default:o(()=>[t(c(Ae))]),_:1}),e[45]||(e[45]=m("导出 "))]),_:1})])]),t(fe,{class:"table-card"},{default:o(()=>[n("div",et,[he((M(),$(ge,{ref:"userLevelsTable",data:Q.value,border:"",stripe:"","highlight-current-row":"","row-key":"id",style:{width:"100%"},onSelectionChange:Z,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:o(()=>[t(f,{type:"selection",width:"40",align:"center",fixed:"left"}),t(f,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),t(f,{prop:"name",label:"级别名称",width:"120",align:"center",fixed:"left"}),t(f,{prop:"level",label:"级别",width:"80",align:"center"}),t(f,{prop:"upgradeUsers",label:"升级人数",width:"100",align:"center"}),t(f,{prop:"upgradeAmount",label:"升级金额",width:"120",align:"center"},{default:o(a=>[m(E(H(a.row.upgradeAmount)),1)]),_:1}),t(f,{prop:"returnRate",label:"收益率(%)",width:"100",align:"center"},{default:o(a=>[m(E(a.row.returnRate)+"% ",1)]),_:1}),t(f,{prop:"upgradeBonus",label:"升级奖金",width:"100",align:"center"},{default:o(a=>[m(E(H(a.row.upgradeBonus)),1)]),_:1}),t(f,{prop:"imageUrl",label:"图片",width:"100",align:"center"},{default:o(a=>[a.row.imageUrl?(M(),$(me,{key:0,style:{width:"40px",height:"40px"},src:a.row.imageUrl,"preview-src-list":[a.row.imageUrl],fit:"cover"},null,8,["src","preview-src-list"])):(M(),A("span",tt,"-"))]),_:1}),t(f,{prop:"content",label:"内容","min-width":"200",align:"center"},{default:o(a=>[n("div",{class:"content-preview",innerHTML:a.row.content},null,8,lt)]),_:1}),t(f,{prop:"createTime",label:"添加时间",width:"160",align:"center",sortable:""}),t(f,{prop:"updateTime",label:"更新时间",width:"160",align:"center",sortable:""}),t(f,{label:"操作",width:"160",align:"center",fixed:"right"},{default:o(a=>[n("div",at,[t(u,{class:"operation-button icon-only",size:"small",type:"default",onClick:Ue=>F(a.row)},{default:o(()=>[t(r,null,{default:o(()=>[t(c(W))]),_:1})]),_:2},1032,["onClick"]),t(u,{type:"danger",size:"small",onClick:Ue=>ae(a.row),class:"operation-button icon-only"},{default:o(()=>[t(r,null,{default:o(()=>[t(c(G))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[we,h.value]])])]),_:1}),n("div",ot,[t(be,{"current-page":y.value,"onUpdate:currentPage":e[1]||(e[1]=a=>y.value=a),"page-size":x.value,"onUpdate:pageSize":e[2]||(e[2]=a=>x.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:X.value,onSizeChange:j,onCurrentChange:pe,"pager-count":7,background:""},{sizes:o(()=>[t(ce,{"model-value":x.value,onChange:j,class:"custom-page-size"},{default:o(()=>[(M(),A(Se,null,He([10,20,50,100],a=>t(ve,{key:a,value:a,label:`${a}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),t(z,{modelValue:T.value,"onUpdate:modelValue":e[21]||(e[21]=a=>T.value=a),title:D.value?"添加用户级别":"编辑用户级别",width:"700px",center:"",top:"5vh",class:"edit-dialog",onClosed:e[22]||(e[22]=a=>V.value=null)},{footer:o(()=>[n("span",ct,[t(u,{type:"primary",onClick:le},{default:o(()=>e[54]||(e[54]=[m("确定")])),_:1}),t(u,{onClick:e[20]||(e[20]=a=>T.value=!1)},{default:o(()=>e[55]||(e[55]=[m("取消")])),_:1})])]),default:o(()=>[t(O,{model:i,"label-position":"top","label-width":"100px"},{default:o(()=>[n("div",nt,[e[46]||(e[46]=n("h3",{class:"form-section-title"},"基本信息",-1)),n("div",st,[t(p,{label:"级别名称",required:""},{default:o(()=>[t(U,{modelValue:i.name,"onUpdate:modelValue":e[3]||(e[3]=a=>i.name=a),placeholder:"请输入级别名称"},null,8,["modelValue"])]),_:1}),t(p,{label:"级别",required:""},{default:o(()=>[t(g,{modelValue:i.level,"onUpdate:modelValue":e[4]||(e[4]=a=>i.level=a),min:1,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])]),n("div",it,[e[47]||(e[47]=n("h3",{class:"form-section-title"},"升级设置",-1)),n("div",rt,[t(p,{label:"升级人数"},{default:o(()=>[t(g,{modelValue:i.upgradeUsers,"onUpdate:modelValue":e[5]||(e[5]=a=>i.upgradeUsers=a),min:0,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(p,{label:"升级金额"},{default:o(()=>[t(g,{modelValue:i.upgradeAmount,"onUpdate:modelValue":e[6]||(e[6]=a=>i.upgradeAmount=a),min:0,step:1e3,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),n("div",ut,[t(p,{label:"升级奖金"},{default:o(()=>[t(g,{modelValue:i.upgradeBonus,"onUpdate:modelValue":e[7]||(e[7]=a=>i.upgradeBonus=a),min:0,step:100,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(p,{label:"收益率(%)"},{default:o(()=>[t(g,{modelValue:i.returnRate,"onUpdate:modelValue":e[8]||(e[8]=a=>i.returnRate=a),precision:2,step:.1,min:0,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})])]),n("div",dt,[e[53]||(e[53]=n("h3",{class:"form-section-title"},"媒体资源",-1)),n("div",pt,[t(p,{label:"级别图片"},{default:o(()=>[t(Ve,{class:"level-image-uploader",action:"https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15","show-file-list":!1,"on-success":de},{default:o(()=>[i.imageUrl?(M(),A("img",{key:0,src:i.imageUrl,class:"level-image"},null,8,mt)):(M(),$(r,{key:1,class:"level-image-uploader-icon"},{default:o(()=>[t(c(Ne))]),_:1}))]),_:1}),e[48]||(e[48]=n("div",{class:"form-tip"},"点击上传级别图片，建议尺寸 100x100 像素",-1))]),_:1})]),n("div",gt,[t(p,{label:"内容",class:"rich-text-form-item"},{default:o(()=>[n("div",ft,[n("div",vt,[n("button",{type:"button",class:"toolbar-button",onClick:e[9]||(e[9]=a=>w("bold"))},e[49]||(e[49]=[n("strong",null,"B",-1)])),n("button",{type:"button",class:"toolbar-button",onClick:e[10]||(e[10]=a=>w("italic"))},e[50]||(e[50]=[n("em",null,"I",-1)])),n("button",{type:"button",class:"toolbar-button",onClick:e[11]||(e[11]=a=>w("underline"))},e[51]||(e[51]=[n("u",null,"U",-1)])),n("button",{type:"button",class:"toolbar-button",onClick:e[12]||(e[12]=a=>w("formatBlock","<h3>"))},"H3"),n("button",{type:"button",class:"toolbar-button",onClick:e[13]||(e[13]=a=>w("formatBlock","<p>"))},"P"),n("button",{type:"button",class:"toolbar-button",onClick:e[14]||(e[14]=a=>w("insertUnorderedList"))},"UL"),n("button",{type:"button",class:"toolbar-button",onClick:e[15]||(e[15]=a=>w("insertOrderedList"))},"OL"),n("button",{type:"button",class:"toolbar-button",onClick:e[16]||(e[16]=a=>w("justifyLeft"))},"Left"),n("button",{type:"button",class:"toolbar-button",onClick:e[17]||(e[17]=a=>w("justifyCenter"))},"Center"),n("button",{type:"button",class:"toolbar-button",onClick:e[18]||(e[18]=a=>w("justifyRight"))},"Right")]),n("div",{ref_key:"editorRef",ref:V,class:"rich-editor",contenteditable:"true",onInput:e[19]||(e[19]=a=>i.content=a.target.innerHTML)},null,544),e[52]||(e[52]=n("div",{class:"form-tip"},"点击工具栏按钮格式化文本，或直接输入内容",-1))])]),_:1})])])]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(z,{modelValue:R.value,"onUpdate:modelValue":e[37]||(e[37]=a=>R.value=a),title:"筛选条件",width:"800px",center:"",top:"5vh",class:"filter-dialog","append-to-body":!0,"destroy-on-close":!0,"close-on-click-modal":!1},{footer:o(()=>[n("div",kt,[t(u,{class:"filter-button",type:"primary",onClick:ie},{default:o(()=>[t(r,null,{default:o(()=>[t(c(J))]),_:1}),e[63]||(e[63]=m("搜索 "))]),_:1}),t(u,{class:"filter-button",onClick:re},{default:o(()=>[t(r,null,{default:o(()=>[t(c(K))]),_:1}),e[64]||(e[64]=m("重置 "))]),_:1}),t(u,{class:"filter-button",onClick:e[36]||(e[36]=a=>R.value=!1)},{default:o(()=>[t(r,null,{default:o(()=>[t(c(Ke))]),_:1}),e[65]||(e[65]=m("取消 "))]),_:1})])]),default:o(()=>[t(O,{model:s,"label-position":"top","label-width":"100px"},{default:o(()=>[n("div",bt,[e[56]||(e[56]=n("h3",{class:"form-section-title"},"基本信息",-1)),n("div",Vt,[t(p,{label:"ID"},{default:o(()=>[t(U,{modelValue:s.id,"onUpdate:modelValue":e[23]||(e[23]=a=>s.id=a),placeholder:"请输入ID",size:"small"},null,8,["modelValue"])]),_:1}),t(p,{label:"级别名称"},{default:o(()=>[t(U,{modelValue:s.name,"onUpdate:modelValue":e[24]||(e[24]=a=>s.name=a),placeholder:"请输入级别名称",size:"small"},null,8,["modelValue"])]),_:1}),t(p,{label:"级别"},{default:o(()=>[t(g,{modelValue:s.level,"onUpdate:modelValue":e[25]||(e[25]=a=>s.level=a),min:1,step:1,"controls-position":"right",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1})])]),n("div",wt,[e[61]||(e[61]=n("h3",{class:"form-section-title"},"升级设置",-1)),n("div",Ut,[t(p,{label:"升级人数范围"},{default:o(()=>[n("div",_t,[t(g,{modelValue:s.upgradeUsersMin,"onUpdate:modelValue":e[26]||(e[26]=a=>s.upgradeUsersMin=a),min:0,step:1,"controls-position":"right",placeholder:"最小值",size:"small"},null,8,["modelValue"]),e[57]||(e[57]=n("span",{class:"range-separator"},"至",-1)),t(g,{modelValue:s.upgradeUsersMax,"onUpdate:modelValue":e[27]||(e[27]=a=>s.upgradeUsersMax=a),min:0,step:1,"controls-position":"right",placeholder:"最大值",size:"small"},null,8,["modelValue"])])]),_:1}),t(p,{label:"升级金额范围"},{default:o(()=>[n("div",yt,[t(g,{modelValue:s.upgradeAmountMin,"onUpdate:modelValue":e[28]||(e[28]=a=>s.upgradeAmountMin=a),min:0,step:1e3,"controls-position":"right",placeholder:"最小值",size:"small"},null,8,["modelValue"]),e[58]||(e[58]=n("span",{class:"range-separator"},"至",-1)),t(g,{modelValue:s.upgradeAmountMax,"onUpdate:modelValue":e[29]||(e[29]=a=>s.upgradeAmountMax=a),min:0,step:1e3,"controls-position":"right",placeholder:"最大值",size:"small"},null,8,["modelValue"])])]),_:1})]),n("div",Mt,[t(p,{label:"升级奖金范围"},{default:o(()=>[n("div",xt,[t(g,{modelValue:s.upgradeBonusMin,"onUpdate:modelValue":e[30]||(e[30]=a=>s.upgradeBonusMin=a),min:0,step:100,"controls-position":"right",placeholder:"最小值",size:"small"},null,8,["modelValue"]),e[59]||(e[59]=n("span",{class:"range-separator"},"至",-1)),t(g,{modelValue:s.upgradeBonusMax,"onUpdate:modelValue":e[31]||(e[31]=a=>s.upgradeBonusMax=a),min:0,step:100,"controls-position":"right",placeholder:"最大值",size:"small"},null,8,["modelValue"])])]),_:1}),t(p,{label:"收益率范围(%)"},{default:o(()=>[n("div",Tt,[t(g,{modelValue:s.returnRateMin,"onUpdate:modelValue":e[32]||(e[32]=a=>s.returnRateMin=a),precision:2,step:.1,min:0,max:100,placeholder:"最小值",size:"small"},null,8,["modelValue"]),e[60]||(e[60]=n("span",{class:"range-separator"},"至",-1)),t(g,{modelValue:s.returnRateMax,"onUpdate:modelValue":e[33]||(e[33]=a=>s.returnRateMax=a),precision:2,step:.1,min:0,max:100,placeholder:"最大值",size:"small"},null,8,["modelValue"])])]),_:1})])]),n("div",Ct,[e[62]||(e[62]=n("h3",{class:"form-section-title"},"时间范围",-1)),n("div",Rt,[t(p,{label:"添加时间"},{default:o(()=>[t(N,{modelValue:s.createTimeRange,"onUpdate:modelValue":e[34]||(e[34]=a=>s.createTimeRange=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),t(p,{label:"更新时间"},{default:o(()=>[t(N,{modelValue:s.updateTimeRange,"onUpdate:modelValue":e[35]||(e[35]=a=>s.updateTimeRange=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(z,{modelValue:C.value,"onUpdate:modelValue":e[39]||(e[39]=a=>C.value=a),title:"确认删除",width:"400px",center:"",class:"delete-dialog"},{footer:o(()=>[n("span",Bt,[t(u,{type:"danger",onClick:ne},{default:o(()=>e[67]||(e[67]=[m("确定删除")])),_:1}),t(u,{onClick:e[38]||(e[38]=a=>C.value=!1)},{default:o(()=>e[68]||(e[68]=[m("取消")])),_:1})])]),default:o(()=>[n("div",Dt,[t(r,{class:"delete-icon"},{default:o(()=>[t(c(We))]),_:1}),e[66]||(e[66]=n("p",null,"确定要删除选中的用户级别吗？此操作不可恢复。",-1))])]),_:1},8,["modelValue"])])}}}),Qt=Ge(At,[["__scopeId","data-v-53486063"]]);export{Qt as default};
