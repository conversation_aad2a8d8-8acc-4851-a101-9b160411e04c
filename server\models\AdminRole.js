const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AdminRole = sequelize.define('AdminRole', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  admin_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
}, {
  tableName: 'admin_roles',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['admin_id', 'role_id']
    }
  ]
});

module.exports = AdminRole;
