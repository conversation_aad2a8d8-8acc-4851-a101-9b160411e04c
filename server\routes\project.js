const express = require('express');
const projectController = require('../controllers/projectController');
const { verifyAdminToken, verifyUserToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 管理员路由
const adminRouter = express.Router();

// 所有路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/projects:
 *   get:
 *     summary: 获取项目列表
 *     tags: [项目管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, pending, active, completed, cancelled]
 *         description: 状态筛选
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [fixed, flexible]
 *         description: 类型筛选
 *       - in: query
 *         name: risk_level
 *         schema:
 *           type: string
 *           enum: [low, medium, high]
 *         description: 风险等级筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', projectController.getProjects);

/**
 * @swagger
 * /api/admin/projects/{id}:
 *   get:
 *     summary: 获取项目详情
 *     tags: [项目管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 项目ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 项目不存在
 */
adminRouter.get('/:id', projectController.getProject);

/**
 * @swagger
 * /api/admin/projects:
 *   post:
 *     summary: 创建项目
 *     tags: [项目管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - expected_return
 *               - duration
 *               - total_amount
 *               - start_time
 *               - end_time
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               cover_image:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [fixed, flexible]
 *               expected_return:
 *                 type: number
 *               min_investment:
 *                 type: number
 *               max_investment:
 *                 type: number
 *               duration:
 *                 type: integer
 *               total_amount:
 *                 type: number
 *               start_time:
 *                 type: string
 *                 format: date-time
 *               end_time:
 *                 type: string
 *                 format: date-time
 *               risk_level:
 *                 type: string
 *                 enum: [low, medium, high]
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求数据错误
 */
adminRouter.post('/', projectController.createProject);

/**
 * @swagger
 * /api/admin/projects/{id}:
 *   put:
 *     summary: 更新项目
 *     tags: [项目管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 项目ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               cover_image:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [fixed, flexible]
 *               expected_return:
 *                 type: number
 *               min_investment:
 *                 type: number
 *               max_investment:
 *                 type: number
 *               duration:
 *                 type: integer
 *               total_amount:
 *                 type: number
 *               start_time:
 *                 type: string
 *                 format: date-time
 *               end_time:
 *                 type: string
 *                 format: date-time
 *               status:
 *                 type: string
 *                 enum: [draft, pending, active, completed, cancelled]
 *               risk_level:
 *                 type: string
 *                 enum: [low, medium, high]
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 项目不存在
 */
adminRouter.put('/:id', projectController.updateProject);

/**
 * @swagger
 * /api/admin/projects/{id}:
 *   delete:
 *     summary: 删除项目
 *     tags: [项目管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 项目ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 项目不存在
 */
adminRouter.delete('/:id', projectController.deleteProject);

/**
 * @swagger
 * /api/admin/projects/{id}/orders:
 *   get:
 *     summary: 获取项目订单列表
 *     tags: [项目管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 项目ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, paid, completed, cancelled, refunded]
 *         description: 状态筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 项目不存在
 */
adminRouter.get('/:id/orders', projectController.getProjectOrders);

/**
 * @swagger
 * /api/admin/projects/{id}/sort:
 *   put:
 *     summary: 更新项目排序
 *     tags: [项目]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 项目ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sort_order
 *             properties:
 *               sort_order:
 *                 type: integer
 *                 description: 排序值
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 项目不存在
 */
adminRouter.put('/:id/sort', projectController.updateProjectSortOrder);

/**
 * @swagger
 * /api/admin/projects/sort/batch:
 *   put:
 *     summary: 批量更新项目排序
 *     tags: [项目]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               required:
 *                 - id
 *                 - sort_order
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: 项目ID
 *                 sort_order:
 *                   type: integer
 *                   description: 排序值
 *     responses:
 *       200:
 *         description: 批量更新成功
 *       400:
 *         description: 请求参数错误
 */
adminRouter.put('/sort/batch', projectController.updateProjectSortOrders);

// 移动端路由
const mobileRouter = express.Router();

/**
 * @swagger
 * /api/mobile/projects:
 *   get:
 *     summary: 获取项目列表
 *     tags: [项目]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [fixed, flexible]
 *         description: 类型筛选
 *       - in: query
 *         name: risk_level
 *         schema:
 *           type: string
 *           enum: [low, medium, high]
 *         description: 风险等级筛选
 *     responses:
 *       200:
 *         description: 获取成功
 */
mobileRouter.get('/', projectController.getMobileProjects);

/**
 * @swagger
 * /api/mobile/projects/{id}:
 *   get:
 *     summary: 获取项目详情
 *     tags: [项目]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 项目ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 项目不存在
 */
mobileRouter.get('/:id', projectController.getMobileProject);

module.exports = {
  admin: adminRouter,
  mobile: mobileRouter
};
