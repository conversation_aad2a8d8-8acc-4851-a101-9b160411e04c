代付请求地址：https://pay.aiffpay.com/pay/transfer
请求方式：POST
国家	测试商户号	测试号限额	代付密钥
菲律宾	999500111	1-100	ABCDEFGHIJKLMNOPQRSTUVWXYZ
注：测试完毕后更换正式商户号和代付秘钥，即可进入正式环境
注：正式商户号的代付秘钥请联系业务经理，绑定IP后获取
Header：
参数名	必选	类型	说明
Content-Type	是	string	application/x-www-form-urlencoded
代付请求参数
参数值	参数名	类型	是否必填	说明
sign_type	签名方式	String	Y	固定值MD5，不参与签名
sign	签名	String	Y	不参与签名
mch_id	商户代码	String	Y	平台分配唯一
mch_transferId	商家转账订单号	String	Y	保证每笔订单唯一
transfer_amount	转账金额	String	Y	测试限额1-100
apply_date	申请时间	String	Y	时间格式：yyyy-MM-dd HH:mm:ss
bank_code	收款银行代码	String	Y	参照银行编码
receive_name	收款银行户名	String	Y	银行户名
receive_account	收款银行账号	String	Y	钱包账号
remark	备注	String	N	备注
back_url	异步通知地址	String	N	若填写则需参与签名
代付请求参数：（以post表单请求，且参数的值不能为空）

apply_date=2021-11-11 11:11:11，
back_url=http://www.baidu.com，
bank_code=IDPT0001，
mch_id=*********，
mch_transferId=WE*********，
receive_account=*********，
receive_name=test，
remark=ABCD0123456，
transfer_amount=100，
sign=01825d3784856463f69e49968b403997
请求参数签名串：(如果非必填参数，提交就需要参与签名)
apply_date=2021-11-11 11:11:11&back_url=http://www.baidu.com&bank_code=IDPT0001&mch_id=*********&mch_transferId=WE*********&receive_account=*********&receive_name=test&remark=ABCD0123456&transfer_amount=100&key=KSIZ9QAGUTP79B1V5BBNNJ9LJP78GLLJ
代付同步响应（返回 json 数据）
参数值	参数名	类型	是否必填	说明
respCode	响应状态	String	Y	SUCCESS：响应成功 FAIL:响应失败
errorMsg	响应失败原因	String	Y	响应成功时为 null
- 以下参数只有响应成功才有值
signType	签名方式 String	Y	MD5 不参与签名
sign	签名	String	Y	不参与签名
mchId	商户代码	String	Y	商户代码
merTransferId	商家转账单号	String	Y	商家转账单号
transferAmount	转账金额	String	Y	转账金额
applyDate	订单时间	String	Y	订单时间
tradeNo	平台转账单号	String	Y	平台转账单号
tradeResult	是否转账成功状态	String	Y	详见附录代付结果
同步响应JSON参数：

“signType”: “MD5”,
“sign”: “cd45a585e2cf3ff687e21dd0e05ced6d”,
“respCode”: “SUCCESS”,
“mchId”: “*********”,
“merTransferId”: “WE*********””,
“transferAmount”: “100.00”,
“applyDate”: “2021-11-11 11:11:11”,
“tradeNo”: “88888888”,
“tradeResult”: “0”,
“errorMsg”: null

代付异步通知消息(以post的form形式返回数据)
1：订单支付成功，发送异步支付成功通知
2：商户未收到通知，系统会连续补发通知8 次
3：商户收到异步通知，需向平台返回“success”终止补发
参数值	参数名	类型	是否必填	说明
tradeResult	订单状态	String	Y	1：代付成功2：代付失败
merTransferId	商家转账单号	String	Y	代付使用的转账单号
merNo	商户代码	String	Y	平台分配唯一
tradeNo	平台订单号	String	Y	平台唯一
transferAmount	代付金额	String	Y	元为单位保留俩位小数
applyDate	订单时间	String	Y	订单时间
version	版本号	String	Y	默认1.0
respCode	回调状态	String	Y	默认SUCCESS
sign	签名	String	N	不参与签名
signType	签名方式	String	N	MD5 不参与签名
异步回调通知内容(以post的form形式返回数据)：

"tradeResult":"1",
"merTransferId":"WE*********",
"merNo":"*********",
"tradeNo":"88888888",
"transferAmount":"100.00",
"sign":"70c7b490bd9cfb291316a45c2d2a4e58",
"signType":"MD5",
"applyDate":"2021-11-11 11:11:11",
"version":"1.0",
"respCode":"SUCCESS"
异步响应签名验证源串形式：
applyDate=2021-11-11 11:11:11&merNo=*********&merTransferId=WE*********&respCode=SUCCESS&tradeNo=88888888&tradeResult=1&transferAmount=100.00&version=1.0&key=KSIZ9QAGUTP79B1V5BBNNJ9LJP78GLLJ

tradeResult 状态码	状态描述
0	申请成功
1	转账成功
2	转账失败
3	转账拒绝
4	转账处理中