const express = require('express');
const customerServiceImageController = require('../controllers/customerServiceImageController');
const { verifyAdminToken, verifyUserToken } = require('../middlewares/authMiddleware');

const router = express.Router();

// 管理端路由
const adminRouter = express.Router();

// 所有管理端路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/customer-service-images:
 *   get:
 *     summary: 获取客服图片列表
 *     tags: [客服图片]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: boolean
 *         description: 状态

 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: 排序字段
 *       - in: query
 *         name: order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: 排序方式
 *     responses:
 *       200:
 *         description: 获取成功
 */
adminRouter.get('/', customerServiceImageController.getCustomerServiceImages);

/**
 * @swagger
 * /api/admin/customer-service-images/{id}:
 *   get:
 *     summary: 获取客服图片详情
 *     tags: [客服图片]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服图片ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 客服图片不存在
 */
adminRouter.get('/:id', customerServiceImageController.getCustomerServiceImage);

/**
 * @swagger
 * /api/admin/customer-service-images:
 *   post:
 *     summary: 创建客服图片
 *     tags: [客服图片]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - attachment_id
 *               - file_path
 *             properties:
 *               attachment_id:
 *                 type: integer
 *                 description: 附件ID
 *               file_name:
 *                 type: string
 *                 description: 文件名
 *               file_path:
 *                 type: string
 *                 description: 文件路径
 *               file_size:
 *                 type: integer
 *                 description: 文件大小

 *               status:
 *                 type: boolean
 *                 description: 状态
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 附件不存在
 */
adminRouter.post('/', customerServiceImageController.createCustomerServiceImage);

/**
 * @swagger
 * /api/admin/customer-service-images/{id}:
 *   put:
 *     summary: 更新客服图片
 *     tags: [客服图片]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服图片ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               attachment_id:
 *                 type: integer
 *                 description: 附件ID
 *               file_name:
 *                 type: string
 *                 description: 文件名
 *               file_path:
 *                 type: string
 *                 description: 文件路径
 *               file_size:
 *                 type: integer
 *                 description: 文件大小

 *               status:
 *                 type: boolean
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 客服图片不存在
 */
adminRouter.put('/:id', customerServiceImageController.updateCustomerServiceImage);

/**
 * @swagger
 * /api/admin/customer-service-images/batch-delete:
 *   post:
 *     summary: 批量删除客服图片
 *     tags: [客服图片]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 客服图片ID数组
 *     responses:
 *       200:
 *         description: 批量删除成功
 *       400:
 *         description: 参数错误
 */
adminRouter.post('/batch-delete', customerServiceImageController.batchDeleteCustomerServiceImages);

/**
 * @swagger
 * /api/admin/customer-service-images/{id}:
 *   delete:
 *     summary: 删除客服图片
 *     tags: [客服图片]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 客服图片ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 客服图片不存在
 */
adminRouter.delete('/:id', customerServiceImageController.deleteCustomerServiceImage);



// 移动端路由
const mobileRouter = express.Router();

// 移除用户认证中间件，使客服图片API公开访问
// mobileRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/customer-service-images:
 *   get:
 *     summary: 获取客服图片列表
 *     tags: [客服图片]
 *     responses:
 *       200:
 *         description: 获取成功
 */
mobileRouter.get('/', customerServiceImageController.getMobileCustomerServiceImages);

// 注册路由
router.use('/admin/customer-service-images', adminRouter);
router.use('/mobile/customer-service-images', mobileRouter);

module.exports = router;
