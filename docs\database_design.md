# FOX 投资平台数据库设计文档

本文档详细描述了 FOX 投资平台的数据库设计，包括所有表结构、字段说明和关系。

## 数据库表结构

### 1. 用户表 (users)

存储系统用户信息，包括普通用户和代理商。

```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    country_code VARCHAR(10) DEFAULT '+86',
    avatar VARCHAR(255) DEFAULT 'https://via.placeholder.com/30',
    invite_code VARCHAR(20) NOT NULL UNIQUE,
    inviter_id INT,
    level INT DEFAULT 1,
    gender ENUM('男', '女') DEFAULT '男',
    points INT DEFAULT 0,
    balance DECIMAL(15,2) DEFAULT 0.00,
    frozen_amount DECIMAL(15,2) DEFAULT 0.00,
    income_account_currency DECIMAL(15,2) DEFAULT 0.00,
    income_account_usdt DECIMAL(15,2) DEFAULT 0.00,
    deposit_account_currency DECIMAL(15,2) DEFAULT 0.00,
    deposit_account_usdt DECIMAL(15,2) DEFAULT 0.00,
    total_deposit DECIMAL(15,2) DEFAULT 0.00,
    withdrawal_amount DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'disabled') DEFAULT 'active',
    allow_purchase BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### 2. 管理员表 (admins)

存储系统管理员信息。

```sql
CREATE TABLE admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    is_super BOOLEAN DEFAULT FALSE,
    status BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 角色表 (roles)

存储系统角色信息。

```sql
CREATE TABLE roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parent_id INT,
    name VARCHAR(50) NOT NULL,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES roles(id) ON DELETE SET NULL
);
```

### 4. 权限表 (permissions)

存储系统权限信息。

```sql
CREATE TABLE permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    type ENUM('menu', 'operation') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 5. 角色权限关联表 (role_permissions)

存储角色和权限的关联关系。

```sql
CREATE TABLE role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY (role_id, permission_id)
);
```

### 6. 管理员角色关联表 (admin_roles)

存储管理员和角色的关联关系。

```sql
CREATE TABLE admin_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    role_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    UNIQUE KEY (admin_id, role_id)
);
```

### 7. 投资项目表 (projects)

存储投资项目信息。

```sql
CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    category VARCHAR(50),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    video_url VARCHAR(255),
    price DECIMAL(15,2) NOT NULL,
    sell_price DECIMAL(15,2),
    min_investment DECIMAL(15,2) NOT NULL,
    max_investment DECIMAL(15,2) NOT NULL,
    price_type VARCHAR(50) DEFAULT '固定价格',
    payment_method VARCHAR(50) DEFAULT '余额支付',
    purchase_time VARCHAR(100) DEFAULT '不限',
    duration INT NOT NULL,
    duration_unit VARCHAR(10) DEFAULT '天',
    custom_return BOOLEAN DEFAULT FALSE,
    expected_return DECIMAL(5,2) NOT NULL,
    profit_time INT NOT NULL,
    quantity INT DEFAULT 0,
    actual_quantity INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    max_purchase_times INT DEFAULT 0,
    simultaneous_purchases INT DEFAULT 0,
    max_profit_times INT DEFAULT 0,
    sold_quantity INT DEFAULT 0,
    vip_level VARCHAR(20),
    vip_type VARCHAR(50),
    return_principal BOOLEAN DEFAULT TRUE,
    is_free BOOLEAN DEFAULT FALSE,
    currency VARCHAR(10) DEFAULT 'CNY',
    status BOOLEAN DEFAULT TRUE,
    sell_status BOOLEAN DEFAULT TRUE,
    commission_enabled BOOLEAN DEFAULT FALSE,
    level1_commission DECIMAL(5,2) DEFAULT 0.00,
    level2_commission DECIMAL(5,2) DEFAULT 0.00,
    level3_commission DECIMAL(5,2) DEFAULT 0.00,
    settlement_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 8. 用户投资表 (investments)

存储用户投资记录。

```sql
CREATE TABLE investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_id INT NOT NULL,
    investment_type ENUM('自动', '手动') DEFAULT '手动',
    quantity INT DEFAULT 1,
    currency VARCHAR(10) DEFAULT 'CNY',
    total_investment DECIMAL(15,2) NOT NULL,
    profit_cycle INT NOT NULL,
    profit_rate DECIMAL(5,2) NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    daily_profit DECIMAL(15,2) DEFAULT 0.00,
    last_profit_time DATETIME,
    profit_count INT DEFAULT 0,
    status ENUM('进行中', '暂停', '完成') DEFAULT '进行中',
    first_investment_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_investment_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);
```

### 9. 投资收益记录表 (investment_profits)

存储投资收益记录。

```sql
CREATE TABLE investment_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    investment_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    profit_time DATETIME NOT NULL,
    status ENUM('已发放', '未发放') DEFAULT '已发放',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 10. 佣金记录表 (commissions)

存储佣金记录。

```sql
CREATE TABLE commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    from_user_id INT NOT NULL,
    investment_id INT NOT NULL,
    level INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    rate DECIMAL(5,2) NOT NULL,
    status ENUM('已发放', '未发放') DEFAULT '已发放',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
);
```

### 11. 用户交易记录表 (transactions)

存储用户交易记录。

```sql
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('充值', '提现', '投资', '收益', '佣金', '赠金', '扣除') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance DECIMAL(15,2) NOT NULL,
    description VARCHAR(255),
    related_id INT,
    status ENUM('成功', '失败', '处理中') DEFAULT '成功',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 12. 用户银行卡表 (user_bank_cards)

存储用户银行卡信息。

```sql
CREATE TABLE user_bank_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    card_number VARCHAR(50) NOT NULL,
    card_holder VARCHAR(100) NOT NULL,
    branch VARCHAR(100),
    is_default BOOLEAN DEFAULT FALSE,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 13. 收款银行卡表 (receiving_bank_cards)

存储系统收款银行卡信息。

```sql
CREATE TABLE receiving_bank_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bank_name VARCHAR(100) NOT NULL,
    card_number VARCHAR(50) NOT NULL,
    card_holder VARCHAR(100) NOT NULL,
    branch VARCHAR(100),
    daily_limit DECIMAL(15,2),
    is_default BOOLEAN DEFAULT FALSE,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 14. 充值订单表 (deposits)

存储充值订单信息。

```sql
CREATE TABLE deposits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    actual_amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_account VARCHAR(100),
    receiving_card_id INT,
    transaction_id VARCHAR(100),
    status ENUM('待支付', '已支付', '已取消', '已完成') DEFAULT '待支付',
    remark VARCHAR(255),
    payment_time DATETIME,
    completion_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiving_card_id) REFERENCES receiving_bank_cards(id) ON DELETE SET NULL
);
```

### 15. 提现记录表 (withdrawals)

存储提现记录信息。

```sql
CREATE TABLE withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    actual_amount DECIMAL(15,2) NOT NULL,
    bank_card_id INT NOT NULL,
    status ENUM('待审核', '审核通过', '已拒绝', '已完成') DEFAULT '待审核',
    remark VARCHAR(255),
    approval_time DATETIME,
    completion_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_card_id) REFERENCES user_bank_cards(id) ON DELETE CASCADE
);
```

### 16. 系统参数表 (system_params)

存储系统参数配置。

```sql
CREATE TABLE system_params (
    id INT AUTO_INCREMENT PRIMARY KEY,
    param_key VARCHAR(100) NOT NULL UNIQUE,
    param_value TEXT,
    param_type VARCHAR(50) NOT NULL DEFAULT 'text',
    description VARCHAR(255),
    group_name VARCHAR(50) NOT NULL DEFAULT 'basic',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 17. 幸运转盘表 (lucky_wheel)

存储幸运转盘奖品配置。

```sql
CREATE TABLE lucky_wheel (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    prize_type ENUM('现金', '积分', '优惠券', '实物', '谢谢参与') NOT NULL,
    prize_value DECIMAL(15,2) DEFAULT 0.00,
    probability DECIMAL(5,2) NOT NULL,
    color VARCHAR(20),
    status BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 18. 用户级别表 (user_levels)

存储用户级别配置。

```sql
CREATE TABLE user_levels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    level INT NOT NULL UNIQUE,
    upgrade_users INT DEFAULT 0,
    upgrade_amount DECIMAL(15,2) DEFAULT 0.00,
    return_rate DECIMAL(5,2) DEFAULT 0.00,
    upgrade_bonus DECIMAL(15,2) DEFAULT 0.00,
    image_url VARCHAR(255),
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 19. 支付通道表 (payment_channels)

存储支付通道配置。

```sql
CREATE TABLE payment_channels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    country_code VARCHAR(10) DEFAULT 'CN',
    deposit_enabled BOOLEAN DEFAULT TRUE,
    withdraw_enabled BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    weight INT DEFAULT 0,
    balance DECIMAL(15,2) DEFAULT 0.00,
    config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 20. 邀请奖励表 (invite_rewards)

存储邀请奖励配置。

```sql
CREATE TABLE invite_rewards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    deposit_users INT DEFAULT 0,
    register_users INT DEFAULT 0,
    bonus DECIMAL(15,2) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 21. 客服管理表 (customer_services)

存储客服信息配置，包括各种客服渠道的信息。

```sql
CREATE TABLE customer_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(100) NOT NULL,
    url VARCHAR(255) NOT NULL,
    status BOOLEAN DEFAULT TRUE,
    icon VARCHAR(100),
    weight INT DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 字段说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键，自增ID |
| type | VARCHAR(50) | 客服类型，如"微信"、"QQ"、"电话"等 |
| title | VARCHAR(100) | 客服标题，如"客服微信"、"客服QQ"等 |
| url | VARCHAR(255) | 客服链接，可以是网址、微信号、QQ号等 |
| status | BOOLEAN | 状态，TRUE表示启用，FALSE表示禁用 |
| icon | VARCHAR(100) | 图标名称或图标URL |
| weight | INT | 权重，用于排序，数值越大越靠前 |
| description | TEXT | 描述信息 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 索引

- 主键索引：`id`
- 普通索引：`type`、`status`、`weight`

#### 使用场景

1. **客服渠道展示**：在前台页面展示各种客服联系方式
2. **客服渠道管理**：在后台管理系统中管理客服渠道
3. **客服渠道排序**：根据权重对客服渠道进行排序展示

#### 关联关系

客服管理表是一个独立的表，不直接与其他表关联。

### 22. 轮播图表 (banners)

存储轮播图配置。

```sql
CREATE TABLE banners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100),
    image_url VARCHAR(255) NOT NULL,
    url VARCHAR(255),
    position VARCHAR(50) DEFAULT 'home',
    sort_order INT DEFAULT 0,
    status BOOLEAN DEFAULT TRUE,
    start_time DATETIME,
    end_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 23. 每日抽奖表 (daily_lottery)

存储每日抽奖配置。

```sql
CREATE TABLE daily_lottery (
    id INT AUTO_INCREMENT PRIMARY KEY,
    day INT NOT NULL,
    category VARCHAR(50) NOT NULL,
    project_name VARCHAR(100),
    prize_name VARCHAR(100) NOT NULL,
    prize_amount DECIMAL(15,2) DEFAULT 0.00,
    prize_type ENUM('现金', '积分', '优惠券', '实物') NOT NULL,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 24. 兑换码表 (redeem_codes)

存储兑换码配置。

```sql
CREATE TABLE redeem_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    quantity INT DEFAULT 1,
    used_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 25. 用户兑换记录表 (redeem_records)

存储用户兑换记录。

```sql
CREATE TABLE redeem_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    code_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    status ENUM('成功', '失败') DEFAULT '成功',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (code_id) REFERENCES redeem_codes(id) ON DELETE CASCADE
);
```

### 26. 幸运转盘记录表 (lucky_wheel_records)

存储用户幸运转盘记录。

```sql
CREATE TABLE lucky_wheel_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    prize_id INT NOT NULL,
    prize_name VARCHAR(100) NOT NULL,
    prize_type ENUM('现金', '积分', '优惠券', '实物', '谢谢参与') NOT NULL,
    prize_value DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (prize_id) REFERENCES lucky_wheel(id) ON DELETE CASCADE
);
```

### 27. 每日抽奖记录表 (daily_lottery_records)

存储用户每日抽奖记录。

```sql
CREATE TABLE daily_lottery_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    lottery_id INT NOT NULL,
    prize_name VARCHAR(100) NOT NULL,
    prize_amount DECIMAL(15,2) DEFAULT 0.00,
    prize_type ENUM('现金', '积分', '优惠券', '实物') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lottery_id) REFERENCES daily_lottery(id) ON DELETE CASCADE
);
```

### 28. 附件表 (attachments)

存储系统附件信息，包括图片、视频、文档等各类文件。

```sql
CREATE TABLE attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(50) DEFAULT '未归类',
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    width INT,
    height INT,
    file_type VARCHAR(20) NOT NULL,
    frame_count INT,
    mime_type VARCHAR(100) NOT NULL,
    metadata TEXT,
    storage_engine VARCHAR(20) DEFAULT 'local',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 字段说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键，自增ID |
| category | VARCHAR(50) | 附件分类，默认为"未归类" |
| filename | VARCHAR(255) | 文件名，用于显示和下载 |
| original_name | VARCHAR(255) | 原始文件名，上传时的文件名 |
| file_path | VARCHAR(255) | 文件存储路径 |
| file_size | INT | 文件大小，单位为字节 |
| width | INT | 图片宽度，仅对图片有效 |
| height | INT | 图片高度，仅对图片有效 |
| file_type | VARCHAR(20) | 文件类型，如jpg、png、mp4等 |
| frame_count | INT | 视频帧数，仅对视频有效 |
| mime_type | VARCHAR(100) | MIME类型，如image/jpeg、video/mp4等 |
| metadata | TEXT | 文件元数据，JSON格式存储 |
| storage_engine | VARCHAR(20) | 存储引擎，默认为"local"，可选值：local、oss、cos等 |
| upload_time | DATETIME | 上传时间 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 索引

- 主键索引：`id`
- 普通索引：`category`、`file_type`、`upload_time`

#### 使用场景

1. **图片管理**：存储系统中使用的各类图片，如产品图片、用户头像等
2. **视频管理**：存储系统中使用的视频文件，如产品介绍视频等
3. **文档管理**：存储系统中使用的文档文件，如用户协议、隐私政策等
4. **其他文件**：存储系统中使用的其他类型文件

#### 关联关系

附件表是一个独立的表，不直接与其他表关联。其他表可以通过存储附件ID或URL来引用附件表中的数据。

## 数据库关系图

数据库关系图将在后续更新中提供，展示各表之间的关联关系。

## 初始数据

### 超级管理员账号
```sql
INSERT INTO admins (username, password, nickname, is_super, status)
VALUES ('admin', '$2a$10$X7SIl.../hashed_password...', '超级管理员', TRUE, TRUE);
```

### 初始角色
```sql
INSERT INTO roles (id, parent_id, name, status) VALUES
(1, NULL, '超级管理员组', TRUE),
(2, NULL, '管理员组', TRUE);
```

### 初始权限
```sql
-- 菜单权限
INSERT INTO permissions (name, code, type) VALUES
('首页', 'dashboard:view', 'menu'),
('会员列表', 'members:view', 'menu'),
('充值订单', 'deposits:view', 'menu'),
('用户投资', 'investments:view', 'menu'),
('取款记录', 'withdrawals:view', 'menu'),
('用户流水', 'transactions:view', 'menu'),
('佣金记录', 'commissions:view', 'menu'),
('用户银行卡', 'user-cards:view', 'menu'),
('收款银行卡', 'receiving-cards:view', 'menu'),
('代理管理', 'agents:view', 'menu'),
('系统设置', 'settings:view', 'menu'),
('通知消息', 'notifications:view', 'menu'),
('管理员设置', 'admins:view', 'menu'),
('角色管理', 'roles:view', 'menu');

-- 操作权限
INSERT INTO permissions (name, code, type) VALUES
('添加会员', 'members:add', 'operation'),
('编辑会员', 'members:edit', 'operation'),
('删除会员', 'members:delete', 'operation'),
('赠金操作', 'members:gift', 'operation'),
('赠投操作', 'members:invest', 'operation'),
('查看下级', 'members:subordinates', 'operation'),

('添加项目', 'projects:add', 'operation'),
('编辑项目', 'projects:edit', 'operation'),
('删除项目', 'projects:delete', 'operation'),

('审核充值', 'deposits:approve', 'operation'),
('拒绝充值', 'deposits:reject', 'operation'),

('审核提现', 'withdrawals:approve', 'operation'),
('拒绝提现', 'withdrawals:reject', 'operation'),

('添加管理员', 'admins:add', 'operation'),
('编辑管理员', 'admins:edit', 'operation'),
('删除管理员', 'admins:delete', 'operation'),

('编辑角色', 'roles:edit', 'operation');
```

### 超级管理员角色关联
```sql
INSERT INTO admin_roles (admin_id, role_id) VALUES (1, 1);
```

### 超级管理员角色权限关联
```sql
-- 为超级管理员角色分配所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;
```

## 开发进度

### 数据库设计与实现

- **完成时间**: 2025-04-23
- **状态**: 已完成
- **详情**:
  - 设计并记录了28个数据库表结构
  - 在MySQL中成功创建了所有表
  - 创建了数据库用户 `user1`（密码: `123456`）并授予了对 `fox_db` 数据库的所有权限
  - 尝试插入初始数据，但发现部分数据已存在

### 已创建的表

1. 用户表 (users)
2. 管理员表 (admins)
3. 角色表 (roles)
4. 权限表 (permissions)
5. 角色权限关联表 (role_permissions)
6. 管理员角色关联表 (admin_roles)
7. 投资项目表 (projects)
8. 用户投资表 (investments)
9. 投资收益记录表 (investment_profits)
10. 佣金记录表 (commissions)
11. 用户交易记录表 (transactions)
12. 用户银行卡表 (user_bank_cards)
13. 收款银行卡表 (receiving_bank_cards)
14. 充值订单表 (deposits)
15. 提现记录表 (withdrawals)
16. 系统参数表 (system_params)
17. 幸运转盘表 (lucky_wheel)
18. 用户级别表 (user_levels)
19. 支付通道表 (payment_channels)
20. 邀请奖励表 (invite_rewards)
21. 客服管理表 (customer_services)
22. 轮播图表 (banners)
23. 每日抽奖表 (daily_lottery)
24. 兑换码表 (redeem_codes)
25. 用户兑换记录表 (redeem_records)
26. 幸运转盘记录表 (lucky_wheel_records)
27. 每日抽奖记录表 (daily_lottery_records)
28. 附件表 (attachments)

### 下一步计划

- 完善后端API接口
- 优化用户认证和权限控制
- 完善前端页面与后端API对接
- 实现数据统计和报表功能

## 版本历史

| 版本 | 日期 | 描述 | 作者 |
|------|------|------|------|
| 1.0 | 2025-04-23 | 初始版本 | FOX开发团队 |
| 1.1 | 2025-04-23 | 添加开发进度 | FOX开发团队 |
| 1.2 | 2025-05-30 | 添加附件表和客服管理表详细说明 | FOX开发团队 |

