const { Bank, BankChannelMapping, BankCard } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 获取所有银行
exports.getAllBanks = async (req, res) => {
  try {
    const banks = await Bank.findAll({
      order: [['name', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: banks
    });
  } catch (error) {
    console.error('获取银行列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取单个银行
exports.getBank = async (req, res) => {
  try {
    const { id } = req.params;

    const bank = await Bank.findByPk(id);

    if (!bank) {
      return res.status(404).json({
        code: 404,
        message: '银行不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: bank
    });
  } catch (error) {
    console.error('获取银行错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 创建银行
exports.createBank = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { name, status } = req.body;

    // 验证请求数据
    if (!name) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '银行名称不能为空',
        data: null
      });
    }

    // 检查银行名称是否已存在
    const existingBank = await Bank.findOne({
      where: {
        name
      }
    });

    if (existingBank) {
      await transaction.rollback();
      return res.status(409).json({
        code: 409,
        message: '银行名称已存在',
        data: null
      });
    }

    // 创建银行
    const bank = await Bank.create({
      name,
      status: status !== undefined ? status : true
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: bank
    });
  } catch (error) {
    await transaction.rollback();
    console.error('创建银行错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新银行
exports.updateBank = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const { name, status } = req.body;

    // 验证请求数据
    if (!name) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '银行名称不能为空',
        data: null
      });
    }

    // 查找银行
    const bank = await Bank.findByPk(id);

    if (!bank) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '银行不存在',
        data: null
      });
    }

    // 检查银行名称是否已存在（排除当前银行）
    if (name !== bank.name) {
      const existingBank = await Bank.findOne({
        where: {
          name,
          id: { [Op.ne]: id }
        }
      });

      if (existingBank) {
        await transaction.rollback();
        return res.status(409).json({
          code: 409,
          message: '银行名称已存在',
          data: null
        });
      }
    }

    // 更新银行
    await bank.update({
      name,
      status: status !== undefined ? status : bank.status
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: bank
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新银行错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新银行状态
exports.updateBankStatus = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 验证请求数据
    if (status === undefined) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '状态不能为空',
        data: null
      });
    }

    // 查找银行
    const bank = await Bank.findByPk(id);

    if (!bank) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '银行不存在',
        data: null
      });
    }

    // 更新银行状态
    await bank.update({
      status
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: bank
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新银行状态错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除银行
exports.deleteBank = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;

    // 查找银行
    const bank = await Bank.findByPk(id);

    if (!bank) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '银行不存在',
        data: null
      });
    }

    // 检查是否有关联的银行编码映射
    const mappings = await BankChannelMapping.findAll({
      where: {
        bank_id: id
      }
    });

    if (mappings.length > 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '该银行已被银行编码映射引用，无法删除',
        data: null
      });
    }

    // 检查是否有关联的银行卡
    const bankCards = await BankCard.findAll({
      where: {
        bank_id: id
      }
    });

    if (bankCards.length > 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '该银行已被银行卡引用，无法删除',
        data: null
      });
    }

    // 删除银行
    await bank.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('删除银行错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
