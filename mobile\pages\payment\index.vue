<template>
  <view class="page-container">
    <!-- 顶部导航栏 (使用自定义样式) -->
    <view class="custom-header">
      <view class="back-button-wrapper">
        <back-button></back-button>
      </view>
      <text class="custom-header-title">支付详情</text>
      <view class="custom-header-placeholder"></view>
    </view>

    <!-- 支付信息 -->
    <view class="payment-info-section">
      <view class="payment-amount">
        <text class="amount-label">支付金额</text>
        <text class="amount-value">MXN$ {{ amount }}</text>
      </view>
      <view class="payment-method">
        <text class="method-label">支付方式</text>
        <text class="method-value">{{ paymentMethodName }}</text>
      </view>
      <view class="order-number">
        <text class="order-label">订单号</text>
        <text class="order-value">{{ orderNumber }}</text>
      </view>
    </view>

    <!-- 支付说明 -->
    <view class="payment-instructions" v-if="paymentInstructions">
      <view class="instructions-title">支付说明</view>
      <rich-text class="instructions-content" :nodes="paymentInstructions"></rich-text>
    </view>

    <!-- 银行卡信息 -->
    <view class="bank-info-section" v-if="bankInfo">
      <view class="bank-info-title">收款银行卡信息</view>
      <view class="bank-info-item">
        <text class="bank-info-label">银行名称</text>
        <text class="bank-info-value">{{ bankInfo.bank_name }}</text>
      </view>
      <view class="bank-info-item">
        <text class="bank-info-label">卡号</text>
        <view class="bank-info-value-copy">
          <text class="bank-info-value">{{ bankInfo.card_number }}</text>
          <view class="copy-button" @click="copyText(bankInfo.card_number)">
            <view class="svg-icon">
              <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="#00e5ff"/>
              </svg>
            </view>
          </view>
        </view>
      </view>
      <view class="bank-info-item">
        <text class="bank-info-label">持卡人</text>
        <view class="bank-info-value-copy">
          <text class="bank-info-value">{{ bankInfo.card_holder }}</text>
          <view class="copy-button" @click="copyText(bankInfo.card_holder)">
            <view class="svg-icon">
              <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="#00e5ff"/>
              </svg>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="cancel-button" @click="cancelOrder">取消订单</button>
      <button class="confirm-button" @click="confirmPayment">我已完成支付</button>
    </view>

    <!-- 支付提示 -->
    <view class="payment-tips">
      <text class="tips-title">温馨提示</text>
      <view class="tips-content">
        <text class="tip-item">1. 请在30分钟内完成支付，超时订单将自动取消</text>
        <text class="tip-item">2. 支付完成后，请点击"我已完成支付"按钮</text>
        <text class="tip-item">3. 如遇到支付问题，请联系客服</text>
      </view>
    </view>
  </view>
</template>

<script>
import BackButton from '@/components/back-button.vue';
import { getRechargeOrderDetail, cancelRechargeOrder } from '../../services/api/recharge.js';

export default {
  components: {
    BackButton
  },
  data() {
    return {
      orderNumber: '',
      amount: '0.00',
      paymentMethod: '',
      paymentMethodName: '',
      paymentInstructions: '',
      bankInfo: null,
      loading: false
    }
  },
  onLoad(options) {
    // 获取URL参数
    this.orderNumber = options.order_number || '';
    this.amount = options.amount || '0.00';
    this.paymentMethod = options.payment_method || '';

    // 根据支付方式设置支付方式名称
    this.setPaymentMethodName();

    // 获取订单详情
    this.fetchOrderDetail();
  },
  methods: {
    // 设置支付方式名称
    setPaymentMethodName() {
      switch(this.paymentMethod) {
        case 'bank':
          this.paymentMethodName = '银行转账';
          break;
        case 'alipay':
          this.paymentMethodName = '支付宝';
          break;
        case 'wechat':
          this.paymentMethodName = '微信支付';
          break;
        default:
          this.paymentMethodName = this.paymentMethod;
      }
    },

    // 获取订单详情
    async fetchOrderDetail() {
      if (!this.orderNumber) return;

      this.loading = true;
      try {
        const response = await getRechargeOrderDetail(this.orderNumber);

        if (response && response.code === 200 && response.data) {
          const orderData = response.data;

          // 更新订单信息
          this.amount = orderData.amount;
          this.paymentMethod = orderData.payment_method;
          this.setPaymentMethodName();

          // 设置支付说明
          if (orderData.payment_instructions) {
            this.paymentInstructions = orderData.payment_instructions;
          }

          // 设置银行卡信息
          if (orderData.bank_card) {
            this.bankInfo = orderData.bank_card;
          }
        } else {
          uni.showToast({
            title: '获取订单详情失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 复制文本
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: 'Copied successfully',
            icon: 'success'
          });
        }
      });
    },

    // 取消订单
    cancelOrder() {
      uni.showModal({
        title: 'Cancel Order',
        content: 'Are you sure you want to cancel this top-up order?',
        confirmText: 'Cancel Order',
        cancelText: 'Continue Payment',
        success: async (res) => {
          if (res.confirm) {
            try {
              const response = await cancelRechargeOrder(this.orderNumber);

              if (response && response.code === 200) {
                uni.showToast({
                  title: 'Order cancelled',
                  icon: 'success'
                });

                // 返回上一页
                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              } else {
                uni.showToast({
                  title: response?.message || 'Failed to cancel order',
                  icon: 'none'
                });
              }
            } catch (error) {
              uni.showToast({
                title: 'Network error, please try again later',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 确认支付
    confirmPayment() {
      uni.showLoading({
        title: 'Processing...'
      });

      // 模拟支付确认
      setTimeout(() => {
        uni.hideLoading();

        uni.showModal({
          title: 'Payment Confirmation',
          content: 'We have received your payment confirmation and are verifying your payment information. Please wait patiently.',
          showCancel: false,
          confirmText: 'OK',
          success: () => {
            // 返回上一页
            uni.navigateBack();
          }
        });
      }, 1500);
    }
  }
}
</script>

<style lang="scss">
/* 页面容器样式 */
.page-container {
  padding-bottom: 40rpx;
}

/* 自定义顶部导航栏样式 */
.custom-header {
  background-color: $fox-bg-color-dark;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  min-height: 120rpx;
}

.custom-header-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  color: $fox-primary-color;
  font-size: 38rpx;
  font-weight: 500;
  pointer-events: none;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-header-placeholder {
  width: 60rpx;
  height: 60rpx;
  visibility: hidden;
}

.back-button-wrapper {
  position: relative;
  z-index: 10;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* SVG图标样式 */
.svg-icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 支付信息区域 */
.payment-info-section {
  padding: 40rpx 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.payment-amount {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.amount-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.amount-value {
  color: $fox-primary-color;
  font-size: 60rpx;
  font-weight: bold;
}

.payment-method, .order-number {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.method-label, .order-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
}

.method-value, .order-value {
  color: $fox-text-color;
  font-size: 28rpx;
}

/* 支付说明 */
.payment-instructions {
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.instructions-title {
  color: $fox-primary-color;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.instructions-content {
  color: $fox-text-color;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 银行卡信息 */
.bank-info-section {
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.bank-info-title {
  color: $fox-primary-color;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.bank-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.bank-info-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
}

.bank-info-value-copy {
  display: flex;
  align-items: center;
}

.bank-info-value {
  color: $fox-text-color;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.copy-button {
  padding: 4rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  gap: 20rpx;
}

.cancel-button, .confirm-button {
  flex: 1;
  padding: 24rpx 0;
  border-radius: 12rpx;
  font-size: 30rpx;
  text-align: center;
}

.cancel-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.confirm-button {
  background-color: #FF8C00;
  color: #0a0e1a;
  font-weight: bold;
}

/* 支付提示 */
.payment-tips {
  padding: 30rpx;
}

.tips-title {
  color: #FF8C00;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.tip-item {
  color: rgba(255, 255, 255, 0.7);
  font-size: 26rpx;
  line-height: 1.5;
}

/* PC端响应式设计 */
@media screen and (min-width: 768px) {
  .payment-info-section, .payment-instructions, .bank-info-section, .action-buttons, .payment-tips {
    max-width: 700px;
    margin: 0 auto;
  }
}
</style>
