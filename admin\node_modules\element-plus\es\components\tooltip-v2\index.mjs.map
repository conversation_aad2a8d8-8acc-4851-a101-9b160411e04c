{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/tooltip-v2/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TooltipV2 from './src/tooltip.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTooltipV2: SFCWithInstall<typeof TooltipV2> =\n  withInstall(TooltipV2)\nexport * from './src/arrow'\nexport * from './src/content'\nexport * from './src/root'\nexport * from './src/tooltip'\nexport * from './src/trigger'\nexport * from './src/constants'\n\nexport default ElTooltipV2\n"], "names": [], "mappings": ";;;;;;;;;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC,SAAS;;;;"}