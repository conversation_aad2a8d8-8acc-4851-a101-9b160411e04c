const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const PaymentChannel = sequelize.define('PaymentChannel', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '支付通道名称',
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '支付通道代码',
  },
  country_code: {
    type: DataTypes.STRING(10),
    allowNull: true,
    defaultValue: 'CN',
    comment: '国家代码',
  },
  icon: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '图标URL',
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: true,
    comment: '是否启用',
  },
  deposit_enabled: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: true,
    comment: '是否启用充值',
  },
  withdraw_enabled: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: true,
    comment: '是否启用提现',
  },
  min_deposit_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '最低充值金额',
  },
  max_deposit_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '最高充值金额',
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    defaultValue: false,
    comment: '是否默认通道',
  },
  weight: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '权重',
  },
  // 余额字段已移除
  config: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '配置信息',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: true,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'payment_channels',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

module.exports = PaymentChannel;
