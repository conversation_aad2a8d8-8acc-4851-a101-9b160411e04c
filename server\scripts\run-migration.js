/**
 * 执行数据库迁移脚本
 */
const path = require('path');
const fs = require('fs');

// 获取迁移文件名
const migrationName = process.argv[2];
if (!migrationName) {
  console.error('请提供迁移文件名');
  console.error('用法: node run-migration.js <迁移文件名>');
  process.exit(1);
}

// 构建迁移文件路径
const migrationPath = path.join(__dirname, '../migrations', `${migrationName}.js`);

// 检查文件是否存在
if (!fs.existsSync(migrationPath)) {
  console.error(`迁移文件不存在: ${migrationPath}`);
  process.exit(1);
}

// 导入迁移模块
const migration = require(migrationPath);

// 执行迁移
async function runMigration() {
  try {
    console.log(`开始执行迁移: ${migrationName}`);
    await migration.up();
    console.log(`迁移成功: ${migrationName}`);
    process.exit(0);
  } catch (error) {
    console.error(`迁移失败: ${migrationName}`, error);
    process.exit(1);
  }
}

runMigration();
