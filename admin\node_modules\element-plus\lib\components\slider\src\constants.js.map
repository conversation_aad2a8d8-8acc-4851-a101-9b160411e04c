{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/slider/src/constants.ts"], "sourcesContent": ["import type { ComputedR<PERSON>, Injection<PERSON><PERSON>, Ref, ToRefs } from 'vue'\nimport type { SliderProps } from './slider'\n\nexport interface SliderContext extends ToRefs<SliderProps> {\n  precision: ComputedRef<number>\n  sliderSize: Ref<number>\n  emitChange: () => void\n  resetSize: () => void\n  updateDragging: (val: boolean) => void\n}\n\nexport const sliderContextKey: InjectionKey<SliderContext> =\n  Symbol('sliderContextKey')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,gBAAgB,GAAG,MAAM,CAAC,kBAAkB;;;;"}