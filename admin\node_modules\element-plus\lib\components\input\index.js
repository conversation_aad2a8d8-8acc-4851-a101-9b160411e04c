'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var input$1 = require('./src/input2.js');
var input = require('./src/input.js');
var install = require('../../utils/vue/install.js');

const ElInput = install.withInstall(input$1["default"]);

exports.inputEmits = input.inputEmits;
exports.inputProps = input.inputProps;
exports.ElInput = ElInput;
exports["default"] = ElInput;
//# sourceMappingURL=index.js.map
