/**
 * 监控日志
 * 用于监控收益系统的日志
 */
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 日志文件路径
const logFilePath = path.join(__dirname, 'logs', 'combined.log');

// 创建可读流
const fileStream = fs.createReadStream(logFilePath);

// 创建接口
const rl = readline.createInterface({
  input: fileStream,
  crlfDelay: Infinity
});

// 监控日志
console.log(`开始监控日志文件: ${logFilePath}`);
console.log('按 Ctrl+C 停止监控');

// 读取现有日志
rl.on('line', (line) => {
  // 只输出包含"收益"或"error"的行
  if (line.includes('收益') || line.includes('error')) {
    console.log(line);
  }
});

// 监控新日志
let lastSize = fs.statSync(logFilePath).size;

// 每秒检查一次日志文件是否有更新
const interval = setInterval(() => {
  const stats = fs.statSync(logFilePath);
  const size = stats.size;

  if (size > lastSize) {
    // 文件有更新，读取新内容
    const stream = fs.createReadStream(logFilePath, {
      start: lastSize,
      end: size
    });

    stream.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');

      lines.forEach(line => {
        // 只输出包含"收益"或"error"的行
        if (line.includes('收益') || line.includes('error')) {
          console.log(line);
        }
      });
    });

    lastSize = size;
  }
}, 1000);

// 处理退出
process.on('SIGINT', () => {
  clearInterval(interval);
  rl.close();
  console.log('停止监控');
  process.exit(0);
});
