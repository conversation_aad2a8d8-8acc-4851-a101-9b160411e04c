# Team页面标题间距优化

## 修改概述

为Team页面的邀请标题和二维码之间增加间距，让布局看起来更加美观和舒适。

## 修改内容

### 📐 **间距调整**

#### **邀请标题底部间距**
```scss
// 修改前
.invite-title {
  font-size: 36rpx;
  color: $fox-text-color;
  margin-bottom: 30rpx;  // 原来的间距
}

// 修改后
.invite-title {
  font-size: 36rpx;
  color: $fox-text-color;
  margin-bottom: 50rpx;  // 增加20rpx间距
}
```

### 🎨 **视觉效果对比**

#### **修改前（紧凑布局）**
```
┌─────────────────────────────────┐
│    Invite Friends to Earn       │
│         Together                │
│    [QR Code Area]               │  ← 间距较小
│   Invite Code: ABC123           │
└─────────────────────────────────┘
```

#### **修改后（舒适布局）**
```
┌─────────────────────────────────┐
│    Invite Friends to Earn       │
│         Together                │
│                                 │  ← 增加的间距
│    [QR Code Area]               │
│   Invite Code: ABC123           │
└─────────────────────────────────┘
```

## 设计原理

### ✨ **视觉改进**

1. **呼吸感** - 标题和二维码之间有更多空间
2. **层次感** - 清晰的内容区域划分
3. **平衡感** - 更好的视觉比例
4. **专业感** - 符合现代UI设计标准

### 📱 **用户体验**

1. **可读性提升** - 标题更加突出
2. **视觉舒适** - 减少视觉压迫感
3. **内容聚焦** - 用户注意力更好地分配
4. **操作友好** - 更清晰的功能区域划分

## 技术实现

### 🔧 **CSS修改**
- **文件**: `mobile/pages/invite/index.vue`
- **样式类**: `.invite-title`
- **属性**: `margin-bottom`
- **数值**: `30rpx` → `50rpx`（增加20rpx）

### 📊 **间距标准**
```scss
// 页面间距层级
$spacing-xs: 10rpx;   // 最小间距
$spacing-sm: 20rpx;   // 小间距
$spacing-md: 30rpx;   // 中等间距（原来的标准）
$spacing-lg: 50rpx;   // 大间距（新的标准）
$spacing-xl: 80rpx;   // 超大间距
```

## 响应式考虑

### 📱 **移动端适配**
- **小屏幕**: 50rpx间距在小屏幕上仍然合适
- **大屏幕**: 间距会按比例缩放，保持视觉平衡
- **横屏模式**: 间距比例保持一致

### 🖥️ **PC端兼容**
由于使用rpx单位，在PC端浏览器中会自动适配：
- **1920px屏幕**: 约25px间距
- **1366px屏幕**: 约18px间距
- **保持比例**: 与其他元素的间距比例一致

## 设计一致性

### 🎯 **与其他元素的协调**

#### **邀请卡片内部间距**
```scss
.invite-container {
  padding: 30rpx 20rpx;        // 容器内边距
}

.invite-title {
  margin-bottom: 50rpx;        // 标题底部间距（新）
}

.qr-code {
  margin: 0 auto 30rpx;        // 二维码底部间距
}

.invite-link {
  margin-bottom: 20rpx;        // 链接底部间距
}
```

#### **间距层次**
1. **标题到二维码**: 50rpx（最大间距）
2. **二维码到链接**: 30rpx（中等间距）
3. **链接到按钮**: 20rpx（小间距）

## 总结

通过将邀请标题的底部间距从30rpx增加到50rpx，实现了：

1. **视觉优化** - 更舒适的布局间距
2. **层次清晰** - 更好的内容区域划分
3. **用户体验提升** - 减少视觉压迫感
4. **设计专业** - 符合现代UI设计标准

这个小的调整让Team页面的邀请卡片看起来更加美观和专业，提升了整体的用户体验。
