import request from '@/utils/request'

// 将响应数据转换为标准格式
function formatResponse(response) {
  // 如果原始响应中已经有code和message，则保留这些值
  const code = response.data && response.data.code ? response.data.code : 200;
  const message = response.data && response.data.message ? response.data.message : 'success';

  return {
    code: code,
    message: message,
    data: response.data && response.data.data ? response.data.data : response
  }
}

// 获取管理员列表
export function getAdmins(params) {
  return request({
    url: '/api/admin/admins',
    method: 'get',
    params
  }).then(response => formatResponse(response))
}

// 获取管理员详情
export function getAdmin(id) {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'get'
  }).then(response => formatResponse(response))
}

// 创建管理员
export function createAdmin(data) {
  return request({
    url: '/api/admin/admins',
    method: 'post',
    data
  }).then(response => {
    // 如果响应中包含账号已存在的错误信息，则返回错误
    if (response && response.data && response.data.message &&
        (response.data.message.includes('账号已存在') ||
         response.data.message.includes('用户名已存在') ||
         response.data.message.includes('already exists'))) {
      return {
        code: 400,
        message: '账号已存在，请使用其他账号',
        data: null
      };
    }
    return formatResponse(response);
  }).then(response => {
    // 检查是否是账号已存在的特殊响应
    if (response && response.__isAccountExistsError) {
      // 返回一个特殊的响应对象，而不是抛出错误
      return {
        code: 409,
        message: '账号已存在',
        data: null,
        __isAccountExistsError: true
      };
    }
    return response;
  })
}

// 更新管理员
export function updateAdmin(id, data) {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'put',
    data
  }).then(response => formatResponse(response))
}

// 删除管理员
export function deleteAdmin(id) {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'delete'
  }).then(response => {
    // 处理可能的空响应
    if (!response || !response.data) {
      return {
        code: 200,
        message: '删除成功',
        data: null
      };
    }
    return formatResponse(response);
  })
}

// 重置管理员密码
export function resetPassword(id, data) {
  return request({
    url: `/api/admin/admins/${id}/password`,
    method: 'put',
    data
  }).then(response => formatResponse(response))
}
