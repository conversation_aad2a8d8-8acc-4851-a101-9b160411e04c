/**
 * 方案5.1：当前时区状态测试
 * 检查当前数据库时区设置和数据状态
 */

const moment = require('moment');

/**
 * 简单的数据库连接测试
 */
async function testCurrentTimezoneStatus() {
  console.log('🔍 检查当前时区状态...\n');

  try {
    // 使用简单的MySQL连接
    const mysql = require('mysql2/promise');
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'MySQL3352~!',
      database: 'fox_db'
    });

    console.log('✅ 数据库连接成功\n');

    // 1. 检查数据库时区设置
    console.log('1. 检查数据库时区设置：');
    const [timezoneResult] = await connection.execute(
      'SELECT @@session.time_zone as session_timezone, @@system_time_zone as system_timezone, @@global.time_zone as global_timezone, NOW() as db_current_time, UTC_TIMESTAMP() as utc_time'
    );

    const dbInfo = timezoneResult[0];
    console.log(`  会话时区: ${dbInfo.session_timezone}`);
    console.log(`  系统时区: ${dbInfo.system_timezone}`);
    console.log(`  全局时区: ${dbInfo.global_timezone}`);
    console.log(`  数据库当前时间: ${dbInfo.db_current_time}`);
    console.log(`  数据库UTC时间: ${dbInfo.utc_time}\n`);

    // 2. 检查系统参数中的时区设置
    console.log('2. 检查系统参数中的时区设置：');
    const [paramResult] = await connection.execute(`
      SELECT param_key, param_value, created_at, updated_at
      FROM system_params 
      WHERE param_key = '[site.timezone]'
      LIMIT 1
    `);

    if (paramResult.length > 0) {
      const param = paramResult[0];
      console.log(`  参数键: ${param.param_key}`);
      console.log(`  参数值: ${param.param_value}`);
      console.log(`  创建时间: ${param.created_at}`);
      console.log(`  更新时间: ${param.updated_at}\n`);
    } else {
      console.log('  ⚠️  未找到时区配置参数\n');
    }

    // 3. 检查最新的投资记录时间格式
    console.log('3. 检查最新投资记录时间格式：');
    const [investmentResult] = await connection.execute(`
      SELECT id, start_time, last_profit_time, created_at
      FROM investments 
      ORDER BY created_at DESC 
      LIMIT 3
    `);

    investmentResult.forEach((inv, index) => {
      console.log(`  投资${index + 1} (ID: ${inv.id}):`);
      console.log(`    开始时间: ${inv.start_time}`);
      console.log(`    上次收益: ${inv.last_profit_time || '无'}`);
      console.log(`    创建时间: ${inv.created_at}`);
      console.log('');
    });

    // 4. 检查最新的收益记录时间格式
    console.log('4. 检查最新收益记录时间格式：');
    const [profitResult] = await connection.execute(`
      SELECT id, investment_id, profit_time, created_at
      FROM investment_profits 
      ORDER BY created_at DESC 
      LIMIT 3
    `);

    profitResult.forEach((profit, index) => {
      console.log(`  收益${index + 1} (ID: ${profit.id}):`);
      console.log(`    投资ID: ${profit.investment_id}`);
      console.log(`    收益时间: ${profit.profit_time}`);
      console.log(`    创建时间: ${profit.created_at}`);
      console.log('');
    });

    // 5. 时间一致性检查
    console.log('5. 时间一致性检查：');
    const now = new Date();
    const dbCurrentTime = new Date(dbInfo.db_current_time);
    const dbUtcTime = new Date(dbInfo.utc_time);
    
    console.log(`  应用程序时间: ${now.toISOString()}`);
    console.log(`  数据库当前时间: ${dbCurrentTime.toISOString()}`);
    console.log(`  数据库UTC时间: ${dbUtcTime.toISOString()}`);
    
    const appDbDiff = Math.abs(now.getTime() - dbUtcTime.getTime()) / 1000;
    const dbTimeDiff = Math.abs(dbCurrentTime.getTime() - dbUtcTime.getTime()) / 1000 / 3600;
    
    console.log(`  应用程序与数据库UTC差异: ${appDbDiff.toFixed(1)}秒`);
    console.log(`  数据库当前时间与UTC差异: ${dbTimeDiff.toFixed(1)}小时\n`);

    // 6. 分析当前状态
    console.log('6. 当前状态分析：');
    
    let currentStatus = '未知';
    let needsMigration = false;
    
    if (dbInfo.session_timezone === '+00:00' || dbInfo.session_timezone === 'UTC') {
      currentStatus = 'UTC时区';
      needsMigration = false;
    } else if (dbInfo.session_timezone === '+08:00') {
      currentStatus = '中国时区(+08:00)';
      needsMigration = true;
    } else {
      currentStatus = `其他时区(${dbInfo.session_timezone})`;
      needsMigration = true;
    }
    
    console.log(`  数据库时区状态: ${currentStatus}`);
    console.log(`  是否需要迁移: ${needsMigration ? '是' : '否'}`);
    
    if (paramResult.length > 0) {
      const configuredTimezone = paramResult[0].param_value;
      console.log(`  配置的时区: ${configuredTimezone}`);
      
      if (configuredTimezone === '+00:00') {
        console.log(`  配置状态: ✅ 已配置为UTC`);
      } else {
        console.log(`  配置状态: ⚠️  配置为非UTC时区`);
      }
    }
    
    console.log('');

    // 7. 建议的下一步行动
    console.log('7. 建议的下一步行动：');
    
    if (!needsMigration && paramResult.length > 0 && paramResult[0].param_value === '+00:00') {
      console.log('  ✅ 系统已经配置为UTC，无需迁移');
      console.log('  ✅ 可以直接测试收益发放功能');
      console.log('  ✅ 建议验证前端时间显示是否正确');
    } else if (!needsMigration) {
      console.log('  ⚠️  数据库时区是UTC，但系统参数可能需要更新');
      console.log('  📝 建议更新system_params中的时区配置');
    } else {
      console.log('  🔄 需要执行UTC迁移');
      console.log('  📊 建议先在测试环境执行迁移脚本');
      console.log('  🛡️ 确保完整备份数据库');
    }

    await connection.end();
    
    return {
      success: true,
      currentTimezone: dbInfo.session_timezone,
      needsMigration: needsMigration,
      configuredTimezone: paramResult.length > 0 ? paramResult[0].param_value : null,
      timeDifference: appDbDiff
    };

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🧪 方案5.1 当前时区状态检查');
  console.log('=====================================\n');

  const result = await testCurrentTimezoneStatus();
  
  if (result.success) {
    console.log('🎉 检查完成！');
    
    if (!result.needsMigration) {
      console.log('\n🚀 系统已准备就绪，可以进行下一步测试！');
    } else {
      console.log('\n⚠️  需要执行迁移，请谨慎操作！');
    }
  } else {
    console.log('❌ 检查失败，请检查数据库连接和配置');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  testCurrentTimezoneStatus,
  main
};
