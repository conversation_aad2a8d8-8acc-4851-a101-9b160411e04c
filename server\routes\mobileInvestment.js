/**
 * 移动端投资路由
 */
const express = require('express');
const router = express.Router();
const mobileInvestmentController = require('../controllers/mobileInvestmentController');
const { verifyUserToken } = require('../middlewares/authMiddleware');

// 所有路由都需要用户认证
router.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/investments:
 *   post:
 *     summary: 创建投资记录
 *     tags: [移动端投资]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - project_id
 *               - amount
 *             properties:
 *               project_id:
 *                 type: integer
 *                 description: 项目ID
 *               amount:
 *                 type: number
 *                 description: 投资金额
 *               quantity:
 *                 type: integer
 *                 description: 购买数量
 *                 default: 1
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 项目不存在
 *       500:
 *         description: 服务器错误
 */
router.post('/', mobileInvestmentController.createInvestment);

/**
 * @swagger
 * /api/mobile/investments:
 *   get:
 *     summary: 获取用户投资记录
 *     tags: [移动端投资]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 投资状态
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       500:
 *         description: 服务器错误
 */
router.get('/', mobileInvestmentController.getUserInvestments);

/**
 * @swagger
 * /api/mobile/investments/{id}:
 *   get:
 *     summary: 获取投资详情
 *     tags: [移动端投资]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: 投资ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 投资记录不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', mobileInvestmentController.getInvestmentById);

module.exports = router;
