/**
 * 移动端充值路由
 */
const express = require('express');
const router = express.Router();
const mobileDepositController = require('../controllers/mobileDepositController');
const { verifyUserToken } = require('../middlewares/authMiddleware');

// 使用用户认证中间件
router.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/deposits:
 *   post:
 *     summary: 创建充值订单
 *     tags: [移动端充值]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - payment_method
 *             properties:
 *               amount:
 *                 type: number
 *                 description: 充值金额
 *               payment_method:
 *                 type: string
 *                 description: 支付方式
 *               bank_card_id:
 *                 type: integer
 *                 description: 银行卡ID（当支付方式为bank时必填）
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       500:
 *         description: 服务器错误
 */
router.post('/', mobileDepositController.createDeposit);

/**
 * @swagger
 * /api/mobile/deposits:
 *   get:
 *     summary: 获取用户充值记录
 *     tags: [移动端充值]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 订单状态
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       500:
 *         description: 服务器错误
 */
router.get('/', mobileDepositController.getUserDeposits);

/**
 * @swagger
 * /api/mobile/deposits/{orderNumber}:
 *   get:
 *     summary: 获取充值订单详情
 *     tags: [移动端充值]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderNumber
 *         schema:
 *           type: string
 *         required: true
 *         description: 订单号
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:orderNumber', mobileDepositController.getDepositDetail);

/**
 * @swagger
 * /api/mobile/deposits/{orderNumber}/cancel:
 *   post:
 *     summary: 取消充值订单
 *     tags: [移动端充值]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderNumber
 *         schema:
 *           type: string
 *         required: true
 *         description: 订单号
 *     responses:
 *       200:
 *         description: 取消成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器错误
 */
router.post('/:orderNumber/cancel', mobileDepositController.cancelDeposit);

module.exports = router;
