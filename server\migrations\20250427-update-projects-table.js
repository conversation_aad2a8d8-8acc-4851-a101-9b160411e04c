'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加新列
    try {
      await queryInterface.addColumn('projects', 'sell_price', {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00,
        comment: '卖出价格'
      });
    } catch (error) {
      console.log('Column sell_price already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'price_type', {
        type: Sequelize.STRING(50),
        allowNull: true,
        defaultValue: '固定价格',
        comment: '价格类型'
      });
    } catch (error) {
      console.log('Column price_type already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'payment_method', {
        type: Sequelize.STRING(50),
        allowNull: true,
        defaultValue: '余额支付',
        comment: '支付方式'
      });
    } catch (error) {
      console.log('Column payment_method already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'purchase_time', {
        type: Sequelize.STRING(100),
        allowNull: true,
        defaultValue: '00:00:00-23:59:59',
        comment: '购买时间范围'
      });
    } catch (error) {
      console.log('Column purchase_time already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'actual_quantity', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '实际数量'
      });
    } catch (error) {
      console.log('Column actual_quantity already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'sort_order', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 100,
        comment: '排序顺序'
      });
    } catch (error) {
      console.log('Column sort_order already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'max_purchase_times', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '最多购买次数'
      });
    } catch (error) {
      console.log('Column max_purchase_times already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'simultaneous_purchases', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '同时购买数量'
      });
    } catch (error) {
      console.log('Column simultaneous_purchases already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'max_profit_times', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '最多收益次数'
      });
    } catch (error) {
      console.log('Column max_profit_times already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'vip_level_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'VIP级别ID'
      });
    } catch (error) {
      console.log('Column vip_level_id already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'vip_type_operator', {
        type: Sequelize.STRING(10),
        allowNull: true,
        defaultValue: '=',
        comment: 'VIP类型操作符'
      });
    } catch (error) {
      console.log('Column vip_type_operator already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'currency', {
        type: Sequelize.STRING(10),
        allowNull: false,
        defaultValue: 'CNY',
        comment: '货币类型'
      });
    } catch (error) {
      console.log('Column currency already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'sell_status', {
        type: Sequelize.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '出售状态：0=待售, 1=在售, 2=售完'
      });
    } catch (error) {
      console.log('Column sell_status already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'settlement_type', {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: 'natural',
        comment: '结算类型：natural=自然时间, purchase=购买时间'
      });
    } catch (error) {
      console.log('Column settlement_type already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'return_principal', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否到期退本'
      });
    } catch (error) {
      console.log('Column return_principal already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'is_free', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否免费项目'
      });
    } catch (error) {
      console.log('Column is_free already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'commission_enabled', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否启用佣金'
      });
    } catch (error) {
      console.log('Column commission_enabled already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'level1_commission', {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0.00,
        comment: '一级佣金比例(%)'
      });
    } catch (error) {
      console.log('Column level1_commission already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'level2_commission', {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0.00,
        comment: '二级佣金比例(%)'
      });
    } catch (error) {
      console.log('Column level2_commission already exists, skipping...');
    }

    try {
      await queryInterface.addColumn('projects', 'level3_commission', {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0.00,
        comment: '三级佣金比例(%)'
      });
    } catch (error) {
      console.log('Column level3_commission already exists, skipping...');
    }

    // 修改现有列
    try {
      await queryInterface.changeColumn('projects', 'status', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: '状态：true=启用, false=禁用'
      });
    } catch (error) {
      console.log('Column status already changed, skipping...');
    }

    // 重命名列
    try {
      await queryInterface.renameColumn('projects', 'title', 'name');
    } catch (error) {
      console.log('Column title already renamed to name, skipping...');
    }

    try {
      await queryInterface.renameColumn('projects', 'cover_image', 'image_id');
    } catch (error) {
      console.log('Column cover_image already renamed to image_id, skipping...');
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 删除新增的列
    await queryInterface.removeColumn('projects', 'sell_price');
    await queryInterface.removeColumn('projects', 'price_type');
    await queryInterface.removeColumn('projects', 'payment_method');
    await queryInterface.removeColumn('projects', 'purchase_time');
    await queryInterface.removeColumn('projects', 'actual_quantity');
    await queryInterface.removeColumn('projects', 'sort_order');
    await queryInterface.removeColumn('projects', 'max_purchase_times');
    await queryInterface.removeColumn('projects', 'simultaneous_purchases');
    await queryInterface.removeColumn('projects', 'max_profit_times');
    await queryInterface.removeColumn('projects', 'vip_level_id');
    await queryInterface.removeColumn('projects', 'vip_type_operator');
    await queryInterface.removeColumn('projects', 'currency');
    await queryInterface.removeColumn('projects', 'sell_status');
    await queryInterface.removeColumn('projects', 'settlement_type');
    await queryInterface.removeColumn('projects', 'return_principal');
    await queryInterface.removeColumn('projects', 'is_free');
    await queryInterface.removeColumn('projects', 'commission_enabled');
    await queryInterface.removeColumn('projects', 'level1_commission');
    await queryInterface.removeColumn('projects', 'level2_commission');
    await queryInterface.removeColumn('projects', 'level3_commission');

    // 恢复原有列
    await queryInterface.changeColumn('projects', 'status', {
      type: Sequelize.STRING(20),
      allowNull: false,
      defaultValue: 'draft',
      comment: '状态：draft=草稿, pending=待审核, active=活跃, completed=已完成, cancelled=已取消'
    });

    // 恢复列名
    await queryInterface.renameColumn('projects', 'name', 'title');
    await queryInterface.renameColumn('projects', 'image_id', 'cover_image');
  }
};
