const bcrypt = require('bcryptjs');

async function testPassword() {
  // 数据库中存储的密码哈希
  const storedHash = '$2b$10$XdQzVVa7RYqUUJ6MrKKzneYZ9qPO8KyGVOOSJZIJ8aHnfL1zhwM6y';
  
  // 用户输入的密码
  const inputPassword = '123456';
  
  // 验证密码
  const isMatch = await bcrypt.compare(inputPassword, storedHash);
  
  console.log(`密码 "${inputPassword}" 与存储的哈希值匹配: ${isMatch}`);
  
  // 如果不匹配，尝试生成新的哈希值
  if (!isMatch) {
    const newHash = await bcrypt.hash(inputPassword, 10);
    console.log(`为密码 "${inputPassword}" 生成的新哈希值: ${newHash}`);
  }
}

testPassword().catch(console.error);
