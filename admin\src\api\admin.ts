import request from '@/utils/request'

/**
 * 获取管理员列表
 * @param params 查询参数
 */
export function getAdmins(params?: any) {
  return request({
    url: '/api/admin/admins',
    method: 'get',
    params
  })
}

/**
 * 获取管理员详情
 * @param id 管理员ID
 */
export function getAdmin(id: number) {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'get'
  })
}

/**
 * 创建管理员
 * @param data 管理员数据
 */
export function createAdmin(data: any) {
  return request({
    url: '/api/admin/admins',
    method: 'post',
    data
  })
}

/**
 * 更新管理员
 * @param id 管理员ID
 * @param data 管理员数据
 */
export function updateAdmin(id: number, data: any) {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除管理员
 * @param id 管理员ID
 */
export function deleteAdmin(id: number) {
  return request({
    url: `/api/admin/admins/${id}`,
    method: 'delete'
  })
}

/**
 * 重置管理员密码
 * @param id 管理员ID
 * @param data 密码数据
 */
export function resetAdminPassword(id: number, data: { password: string }) {
  return request({
    url: `/api/admin/admins/${id}/password`,
    method: 'put',
    data
  })
}
