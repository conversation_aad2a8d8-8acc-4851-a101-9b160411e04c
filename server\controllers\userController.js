const { User, Investment, UserRelation, AccountBalance, BankCard, Transaction, UserLevel } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const transactionController = require('./transactionController');
const balanceService = require('../services/balanceService');

// 管理员端 - 获取用户列表
exports.getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, status, parent_id, count_only } = req.query;

    // 如果只需要获取总数，则执行简单的计数查询
    if (count_only === 'true') {
      console.log('Getting user count only');
      const where = {};

      if (keyword) {
        where[Op.or] = [
          { username: { [Op.like]: `%${keyword}%` } },
          { name: { [Op.like]: `%${keyword}%` } },
          { email: { [Op.like]: `%${keyword}%` } },
          { phone: { [Op.like]: `%${keyword}%` } },
          { invite_code: { [Op.like]: `%${keyword}%` } },
          { user_id: { [Op.like]: `%${keyword}%` } }
        ];
      }

      if (status) {
        where.status = status;
      }

      if (parent_id) {
        where.inviter_id = parent_id;
      }

      // 使用简单的计数查询，确保只计算实际存在的用户
      const count = await User.count({
        where,
        distinct: true, // 使用distinct确保不重复计数
        include: [] // 不包含任何关联，避免关联查询导致的重复计数
      });
      console.log(`Count query result: ${count} records`);

      return res.status(200).json({
        code: 200,
        message: 'Success',
        data: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          items: []
        }
      });
    }

    // 构建查询条件
    const where = {};

    if (keyword) {
      where[Op.or] = [
        { username: { [Op.like]: `%${keyword}%` } },
        { name: { [Op.like]: `%${keyword}%` } },
        { email: { [Op.like]: `%${keyword}%` } },
        { phone: { [Op.like]: `%${keyword}%` } },
        { invite_code: { [Op.like]: `%${keyword}%` } },
        { user_id: { [Op.like]: `%${keyword}%` } }
      ];
    }

    if (status) {
      where.status = status;
    }

    if (parent_id) {
      where.inviter_id = parent_id;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    // 先使用count方法获取准确的总数
    const realCount = await User.count({
      where,
      distinct: true, // 使用distinct确保不重复计数
      include: [] // 不包含任何关联，避免关联查询导致的重复计数
    });
    console.log(`主查询计数结果: ${realCount} 条记录`);

    // 使用findAll方法获取分页数据
    const rows = await User.findAll({
      where,
      attributes: { exclude: ['password'] },
      include: [
        {
          model: User,
          as: 'inviter',
          attributes: ['id', 'username', 'name', 'inviter_id'],
          required: false,
          include: [
            {
              model: User,
              as: 'inviter',
              attributes: ['id', 'username', 'inviter_id'],
              required: false,
              include: [
                {
                  model: User,
                  as: 'inviter',
                  attributes: ['id', 'username'],
                  required: false
                }
              ]
            }
          ]
        },
        {
          model: AccountBalance,
          as: 'account_balances',
          required: false
        },
        {
          model: UserLevel,
          as: 'level',
          attributes: ['id', 'name', 'level'],
          required: false
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 处理用户数据，添加前端需要的字段
    const processedUsers = await Promise.all(rows.map(async (user) => {
      // 获取用户的下级用户数量
      const firstLevelCount = await User.count({ where: { inviter_id: user.id } });

      // 获取二级下级用户
      const firstLevelUsers = await User.findAll({
        where: { inviter_id: user.id },
        attributes: ['id']
      });
      const firstLevelIds = firstLevelUsers.map(u => u.id);

      let secondLevelCount = 0;
      let thirdLevelCount = 0;

      if (firstLevelIds.length > 0) {
        // 获取二级下级用户数量
        secondLevelCount = await User.count({
          where: { inviter_id: { [Op.in]: firstLevelIds } }
        });

        // 获取二级下级用户ID
        const secondLevelUsers = await User.findAll({
          where: { inviter_id: { [Op.in]: firstLevelIds } },
          attributes: ['id']
        });
        const secondLevelIds = secondLevelUsers.map(u => u.id);

        if (secondLevelIds.length > 0) {
          // 获取三级下级用户数量
          thirdLevelCount = await User.count({
            where: { inviter_id: { [Op.in]: secondLevelIds } }
          });
        }
      }

      // 获取直属上级信息
      let parentUsername = null;
      let parentId = null;

      // 获取最远级别的代理商信息
      let topAgentUsername = null;
      let topAgentId = null;

      // 如果用户有直属上级
      if (user.inviter) {
        parentUsername = user.inviter.username;
        parentId = user.inviter.id;

        // 先假设直属上级就是最远级别的代理商
        topAgentUsername = user.inviter.username;
        topAgentId = user.inviter.id;

        // 检查是否有更远的上级
        if (user.inviter.inviter) {
          // 有二级上级
          if (user.inviter.inviter.inviter) {
            // 有三级上级，这是最远的
            topAgentUsername = user.inviter.inviter.inviter.username;
            topAgentId = user.inviter.inviter.inviter.id;
          } else {
            // 二级上级是最远的
            topAgentUsername = user.inviter.inviter.username;
            topAgentId = user.inviter.inviter.id;
          }
        }
      }

      // 获取用户等级信息
      let userLevel = 0;
      let userLevelName = '';

      if (user.level) {
        userLevel = user.level.level || 0;
        userLevelName = user.level.name || '';
      }

      // 使用余额管理服务获取用户余额信息
      let incomeBalance = 0;
      let depositBalance = 0;
      let totalBalance = 0;

      try {
        // 调用余额管理服务获取用户余额信息
        const balanceInfo = await balanceService.getUserBalances(user.id);
        incomeBalance = balanceInfo.incomeBalance;
        depositBalance = balanceInfo.depositBalance;
        totalBalance = balanceInfo.totalBalance;
      } catch (error) {
        console.error(`获取用户 ${user.id} 余额信息错误:`, error);

        // 如果余额服务调用失败，回退到原来的方式
        if (user.account_balances && user.account_balances.length > 0) {
          // 查找收入账户余额
          const incomeAccount = user.account_balances.find(
            account => account.account_type === 'income' && account.currency === 'CNY'
          );
          if (incomeAccount) {
            incomeBalance = parseFloat(incomeAccount.balance);
          }

          // 查找充值账户余额
          const depositAccount = user.account_balances.find(
            account => account.account_type === 'deposit' && account.currency === 'CNY'
          );
          if (depositAccount) {
            depositBalance = parseFloat(depositAccount.balance);
          }

          totalBalance = incomeBalance + depositBalance;
        }
      }

      // 计算用户的总充值金额（从Deposit表中统计已完成的充值）
      let totalDepositAmount = 0;
      try {
        const { Deposit } = require('../models');
        const depositSum = await Deposit.sum('actual_amount', {
          where: {
            user_id: user.id,
            status: 'completed'
          }
        });
        totalDepositAmount = parseFloat(depositSum) || 0;
      } catch (error) {
        console.error(`计算用户 ${user.id} 总充值金额错误:`, error);
      }

      // 计算用户的总取款金额（从Withdrawal表中统计已完成的取款）
      let totalWithdrawalAmount = 0;
      try {
        const { Withdrawal } = require('../models');
        const withdrawalSum = await Withdrawal.sum('actual_amount', {
          where: {
            user_id: user.id,
            status: 'completed'
          }
        });
        totalWithdrawalAmount = parseFloat(withdrawalSum) || 0;
      } catch (error) {
        console.error(`计算用户 ${user.id} 总取款金额错误:`, error);
      }

      // 返回处理后的用户数据
      return {
        id: user.id,
        user_id: user.user_id,
        username: user.username,
        name: user.name,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        balance: user.balance,
        status: user.status,
        created_at: user.created_at,
        updated_at: user.updated_at,
        invite_code: user.invite_code,
        parent_username: parentUsername,
        parent_id: parentId,
        agent_username: topAgentUsername,
        agent_id: topAgentId,
        first_level_subordinates: firstLevelCount,
        second_level_subordinates: secondLevelCount,
        third_level_subordinates: thirdLevelCount,
        total_subordinates: firstLevelCount + secondLevelCount + thirdLevelCount,
        level: userLevel,
        level_name: userLevelName,
        income_balance: incomeBalance,
        deposit_balance: depositBalance,
        total_balance: totalBalance,
        total_deposit: totalDepositAmount,      // 总充值金额
        withdrawal_amount: totalWithdrawalAmount // 总取款金额
      };
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: realCount, // 使用准确的总数
        page: parseInt(page),
        limit: parseInt(limit),
        items: processedUsers
      }
    });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取用户详情
exports.getUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: 'User not found',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: 'Success',
      data: user
    });
  } catch (error) {
    console.error('Get user details error:', error);
    return res.status(500).json({
      code: 500,
      message: 'Internal server error',
      data: null
    });
  }
};

// 管理员端 - 创建用户
exports.createUser = async (req, res) => {
  try {
    const { username, password, name, email, phone, id_card } = req.body;

    // 验证请求数据
    if (!username || !password || !name || !phone) {
      return res.status(400).json({
        code: 400,
        message: 'Username, password, name and phone number cannot be empty',
        data: null
      });
    }

    // 检查用户名是否已存在
    const existingUsername = await User.findOne({ where: { username } });
    if (existingUsername) {
      return res.status(409).json({
        code: 409,
        message: 'Username already exists',
        data: null
      });
    }

    // 检查手机号是否已存在
    const existingPhone = await User.findOne({ where: { phone } });
    if (existingPhone) {
      return res.status(409).json({
        code: 409,
        message: 'Phone number already exists',
        data: null
      });
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(409).json({
          code: 409,
          message: 'Email already exists',
          data: null
        });
      }
    }

    // 创建用户
    const user = await User.create({
      username,
      password,
      name,
      email,
      phone,
      id_card,
      status: 'active'
    });

    // 生成用户ID (格式为U+6位数字)
    const userId = `U${String(user.id).padStart(6, '0')}`;
    user.user_id = userId;
    await user.save();

    return res.status(201).json({
      code: 201,
      message: 'Created successfully',
      data: {
        id: user.id,
        user_id: user.user_id,
        username: user.username,
        name: user.name,
        email: user.email,
        phone: user.phone,
        id_card: user.id_card,
        balance: user.balance,
        status: user.status,
        created_at: user.createdAt
      }
    });
  } catch (error) {
    console.error('创建用户错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 更新用户
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, phone, status } = req.body;

    console.log('更新用户请求数据:', req.body);

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 检查手机号是否已存在
    if (phone && phone !== user.phone) {
      const existingPhone = await User.findOne({ where: { phone } });
      if (existingPhone) {
        return res.status(409).json({
          code: 409,
          message: '手机号已存在',
          data: null
        });
      }
    }

    // 检查邮箱是否已存在
    if (email && email !== user.email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(409).json({
          code: 409,
          message: '邮箱已存在',
          data: null
        });
      }
    }

    // 更新用户信息
    if (name) user.name = name;
    if (email) user.email = email;
    if (phone) user.phone = phone;
    if (status) user.status = status;

    await user.save();

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: {
        id: user.id,
        user_id: user.user_id,
        username: user.username,
        name: user.name,
        email: user.email,
        phone: user.phone,
        balance: user.balance,
        status: user.status,
        updated_at: user.updated_at
      }
    });
  } catch (error) {
    console.error('更新用户错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 重置用户密码
exports.resetPassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { password } = req.body;

    // 验证请求数据
    if (!password) {
      return res.status(400).json({
        code: 400,
        message: '密码不能为空',
        data: null
      });
    }

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 更新密码
    user.password = password;
    await user.save();

    return res.status(200).json({
      code: 200,
      message: '密码重置成功',
      data: null
    });
  } catch (error) {
    console.error('重置用户密码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 调整用户余额
exports.adjustBalance = async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, type, remark, account_type = 'deposit' } = req.body;

    // 验证请求数据
    if (!amount || !type) {
      return res.status(400).json({
        code: 400,
        message: '金额和类型不能为空',
        data: null
      });
    }

    if (!['add', 'subtract'].includes(type)) {
      return res.status(400).json({
        code: 400,
        message: '类型只能是add或subtract',
        data: null
      });
    }

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    try {
      // 使用余额管理服务调整余额
      const description = remark || `管理员${type === 'add' ? '增加' : '减少'}余额 ${amount}`;

      // 确定交易类型：bonus(赠金) 或 deduction(扣除)
      const transactionType = type === 'add' ? 'bonus' : 'deduction';

      // 调用余额管理服务
      const result = await balanceService.adjustBalance(
        id,
        account_type,
        amount,
        type,
        transactionType,
        description
      );

      // 获取最新的账户余额信息
      const accountBalance = await AccountBalance.findOne({
        where: {
          user_id: id,
          account_type
        }
      });

      return res.status(200).json({
        code: 200,
        message: '余额调整成功',
        data: {
          id: user.id,
          username: user.username,
          name: user.name,
          account_type,
          balance: result.newBalance,
          transaction_id: result.transactionId,
          updated_at: accountBalance ? accountBalance.updated_at : new Date()
        }
      });
    } catch (error) {
      if (error.message === '用户余额不足') {
        return res.status(400).json({
          code: 400,
          message: '用户余额不足',
          data: null
        });
      }
      throw error;
    }
  } catch (error) {
    console.error('调整用户余额错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取用户投资列表
exports.getUserInvestments = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 构建查询条件
    const where = { user_id: id };

    if (status) {
      where.status = status;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Investment.findAndCountAll({
      where,
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'type', 'expected_return', 'duration', 'duration_unit']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取用户投资列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取用户交易列表
exports.getUserTransactions = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, type, start_date, end_date } = req.query;

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 构建查询条件
    const where = { user_id: id };

    if (type) {
      where.type = type;
    }

    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Transaction.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取用户交易列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 更新用户个人信息
exports.updateProfile = async (req, res) => {
  try {
    const user = req.user;
    const { name, email, avatar } = req.body;

    // 检查邮箱是否已存在
    if (email && email !== user.email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(409).json({
          code: 409,
          message: '邮箱已存在',
          data: null
        });
      }
    }

    // 更新用户信息
    if (name) user.name = name;
    if (email) user.email = email;
    if (avatar) user.avatar = avatar;

    await user.save();

    // 获取用户账户余额
    const accountBalances = await AccountBalance.findAll({
      where: { user_id: user.id }
    });

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        balance: user.balance,
        status: user.status,
        account_balances: accountBalances,
        updated_at: user.updated_at
      }
    });
  } catch (error) {
    console.error('更新用户个人信息错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取用户下级会员
exports.getUserSubordinates = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, level, keyword } = req.query;

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 构建查询条件
    const where = { parent_id: id };

    // 根据级别筛选
    if (level) {
      where.level = parseInt(level);
    }

    // 分页查询
    const offset = (page - 1) * limit;

    // 查询用户关系
    const { count, rows } = await UserRelation.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'name', 'email', 'phone', 'avatar', 'balance', 'status', 'invite_code', 'inviter_id', 'created_at'],
          where: keyword ? {
            [Op.or]: [
              { username: { [Op.like]: `%${keyword}%` } },
              { name: { [Op.like]: `%${keyword}%` } },
              { email: { [Op.like]: `%${keyword}%` } },
              { phone: { [Op.like]: `%${keyword}%` } },
              { invite_code: { [Op.like]: `%${keyword}%` } }
            ]
          } : undefined,
          include: [
            {
              model: User,
              as: 'inviter',
              attributes: ['id', 'username', 'inviter_id'],
              required: false,
              include: [
                {
                  model: User,
                  as: 'inviter',
                  attributes: ['id', 'username', 'inviter_id'],
                  required: false,
                  include: [
                    {
                      model: User,
                      as: 'inviter',
                      attributes: ['id', 'username'],
                      required: false
                    }
                  ]
                }
              ]
            },
            {
              model: AccountBalance,
              as: 'account_balances',
              required: false
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 处理用户数据，添加前端需要的字段
    const processedUsers = await Promise.all(rows.map(async (relation) => {
      const user = relation.user;
      if (!user) return null; // 跳过没有关联用户的记录

      // 获取用户的下级用户数量
      const firstLevelCount = await User.count({ where: { inviter_id: user.id } });

      // 获取二级下级用户
      const firstLevelUsers = await User.findAll({
        where: { inviter_id: user.id },
        attributes: ['id']
      });
      const firstLevelIds = firstLevelUsers.map(u => u.id);

      let secondLevelCount = 0;
      let thirdLevelCount = 0;

      if (firstLevelIds.length > 0) {
        // 获取二级下级用户数量
        secondLevelCount = await User.count({
          where: { inviter_id: { [Op.in]: firstLevelIds } }
        });

        // 获取二级下级用户ID
        const secondLevelUsers = await User.findAll({
          where: { inviter_id: { [Op.in]: firstLevelIds } },
          attributes: ['id']
        });
        const secondLevelIds = secondLevelUsers.map(u => u.id);

        if (secondLevelIds.length > 0) {
          // 获取三级下级用户数量
          thirdLevelCount = await User.count({
            where: { inviter_id: { [Op.in]: secondLevelIds } }
          });
        }
      }

      // 获取直属上级信息
      let parentUsername = null;
      let parentId = null;

      // 获取最远级别的代理商信息
      let topAgentUsername = null;
      let topAgentId = null;

      // 如果用户有直属上级
      if (user.inviter) {
        parentUsername = user.inviter.username;
        parentId = user.inviter_id;

        // 先假设直属上级就是最远级别的代理商
        topAgentUsername = user.inviter.username;
        topAgentId = user.inviter_id;

        // 检查是否有更远的上级
        if (user.inviter.inviter) {
          // 有二级上级
          if (user.inviter.inviter.inviter) {
            // 有三级上级，这是最远的
            topAgentUsername = user.inviter.inviter.inviter.username;
            topAgentId = user.inviter.inviter.inviter.id;
          } else {
            // 二级上级是最远的
            topAgentUsername = user.inviter.inviter.username;
            topAgentId = user.inviter.inviter.id;
          }
        }
      }

      // 返回处理后的用户数据
      return {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        balance: user.balance,
        status: user.status,
        created_at: user.created_at,
        invite_code: user.invite_code,
        parent_username: parentUsername,
        parent_id: parentId,
        agent_username: topAgentUsername,
        agent_id: topAgentId,
        level: relation.level,
        first_level_subordinates: firstLevelCount,
        second_level_subordinates: secondLevelCount,
        third_level_subordinates: thirdLevelCount,
        total_subordinates: firstLevelCount + secondLevelCount + thirdLevelCount,
        account_balances: user.account_balances || []
      };
    }));

    // 过滤掉null值
    const filteredUsers = processedUsers.filter(user => user !== null);

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: filteredUsers
      }
    });
  } catch (error) {
    console.error('获取用户下级会员错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
