import request from '@/utils/request'
import { get, post, put, del } from '@/utils/request'

/**
 * 获取轮播图列表
 * @param params 查询参数
 */
export function getBanners(params?: any): Promise<any> {
  return get('/api/admin/banners', params)
}

/**
 * 获取轮播图详情
 * @param id 轮播图ID
 */
export function getBanner(id: number): Promise<any> {
  return get(`/api/admin/banners/${id}`)
}

/**
 * 创建轮播图
 * @param data 轮播图数据
 */
export function createBanner(data: any): Promise<any> {
  return post('/api/admin/banners', data)
}

/**
 * 更新轮播图
 * @param id 轮播图ID
 * @param data 轮播图数据
 */
export function updateBanner(id: number, data: any): Promise<any> {
  return put(`/api/admin/banners/${id}`, data)
}

/**
 * 删除轮播图
 * @param ids 轮播图ID数组
 */
export function deleteBanners(ids: number[]): Promise<any> {
  return del('/api/admin/banners', { ids })
}

/**
 * 更新轮播图排序
 * @param id 轮播图ID
 * @param sort_order 排序值
 */
export function updateBannerSortOrder(id: number, sort_order: number): Promise<any> {
  return put(`/api/admin/banners/${id}/sort`, { sort_order })
}
