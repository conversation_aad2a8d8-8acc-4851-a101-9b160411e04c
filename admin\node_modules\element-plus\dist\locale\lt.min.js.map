{"version": 3, "file": "lt.min.js", "sources": ["../../../../packages/locale/lang/lt.ts"], "sourcesContent": ["export default {\n  name: 'lt',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: 'Šiandien',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Pasirink datą',\n      selectTime: 'Pasirink laiką',\n      startDate: 'Data nuo',\n      startTime: 'Laikas nuo',\n      endDate: 'Data iki',\n      endTime: 'Laikas iki',\n      prevYear: 'Metai atgal',\n      nextYear: 'Metai į priekį',\n      prevMonth: 'Mėn. atgal',\n      nextMonth: 'Mėn. į priekį',\n      year: '',\n      month1: 'Sausis',\n      month2: 'Vasaris',\n      month3: 'Kovas',\n      month4: 'Balandis',\n      month5: 'Gegu<PERSON><PERSON>',\n      month6: 'Bir<PERSON><PERSON><PERSON>',\n      month7: 'Liepa',\n      month8: 'Rugp<PERSON><PERSON><PERSON>',\n      month9: 'Rugsėjis',\n      month10: '<PERSON><PERSON>',\n      month11: 'Lapkrit<PERSON>',\n      month12: '<PERSON><PERSON>od<PERSON>',\n      // week: 'savait<PERSON>',\n      weeks: {\n        sun: 'S.',\n        mon: 'Pr.',\n        tue: 'A.',\n        wed: 'T.',\n        thu: 'K.',\n        fri: 'Pn.',\n        sat: 'Š.',\n      },\n      months: {\n        jan: 'Sau',\n        feb: 'Vas',\n        mar: 'Kov',\n        apr: 'Bal',\n        may: 'Geg',\n        jun: 'Bir',\n        jul: 'Lie',\n        aug: 'Rugp',\n        sep: 'Rugs',\n        oct: 'Spa',\n        nov: 'Lap',\n        dec: 'Gruo',\n      },\n    },\n    select: {\n      loading: 'Kraunasi',\n      noMatch: 'Duomenų nerasta',\n      noData: 'Nėra duomenų',\n      placeholder: 'Pasirink',\n    },\n    mention: {\n      loading: 'Kraunasi',\n    },\n    cascader: {\n      noMatch: 'Duomenų nerasta',\n      loading: 'Kraunasi',\n      placeholder: 'Pasirink',\n      noData: 'Nėra duomenų',\n    },\n    pagination: {\n      goto: 'Eiti į',\n      pagesize: '/p',\n      total: 'Viso {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Žinutė',\n      confirm: 'OK',\n      cancel: 'Atšaukti',\n      error: 'Klaida įvestuose duomenyse',\n    },\n    upload: {\n      deleteTip: 'spauskite \"Trinti\" norėdami pašalinti',\n      delete: 'Trinti',\n      preview: 'Peržiūrėti',\n      continue: 'Toliau',\n    },\n    table: {\n      emptyText: 'Duomenų nerasta',\n      confirmFilter: 'Patvirtinti',\n      resetFilter: 'Atstatyti',\n      clearFilter: 'Išvalyti',\n      sumText: 'Suma',\n    },\n    tour: {\n      next: 'Kitas',\n      previous: 'Ankstesnis',\n      finish: 'Baigti',\n    },\n    tree: {\n      emptyText: 'Nėra duomenų',\n    },\n    transfer: {\n      noMatch: 'Duomenų nerasta',\n      noData: 'Nėra duomenų',\n      titles: ['Sąrašas 1', 'Sąrašas 2'],\n      filterPlaceholder: 'Įvesk raktažodį',\n      noCheckedFormat: 'Viso: {total}',\n      hasCheckedFormat: 'Pažymėta {checked} iš {total}',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,0BAA0B,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,wBAAwB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iDAAiD,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,2BAA2B,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,8CAA8C,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}