/**
 * 统一的收益处理服务
 * 作为所有收益处理的统一入口
 */
const { sequelize, Investment, Project, InvestmentProfit } = require('../models');
const { Op } = require('sequelize');
const redisClient = require('../utils/redisClient');
const balanceService = require('./balanceService');
const commissionService = require('./commissionService');
const logger = require('../utils/logger');
const dateUtils = require('../utils/dateUtils');

/**
 * 处理投资收益
 * @param {Object} investment - 投资记录
 * @param {Date} [specificProfitTime] - 指定的收益时间（用于补偿）
 * @param {boolean} [isCompensation=false] - 是否是补偿调用
 * @returns {Promise<Object>} - 处理结果
 */
exports.processInvestmentProfit = async (investment, specificProfitTime = null, isCompensation = false) => {
  try {
    // 检查投资记录是否有效
    if (!investment || !investment.id) {
      logger.error('无效的投资记录');
      return {
        success: false,
        message: '无效的投资记录'
      };
    }

    // 检查是否可以使用队列系统
    const queueService = require('./queueService');
    if (queueService.profitQueue) {


      // 创建任务数据
      const jobData = {
        investment_id: investment.id,
        specific_profit_time: specificProfitTime ? specificProfitTime.toISOString() : null,
        is_compensation: isCompensation
      };

      try {
        // 添加到队列
        const job = await queueService.profitQueue.add(jobData, {
          jobId: `profit-direct:${investment.id}:${Date.now()}`,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000
          }
        });



        return {
          success: true,
          message: '收益任务已添加到队列',
          investment_id: investment.id,
          job_id: job.id,
          queued: true
        };
      } catch (queueError) {
        logger.error(`将投资ID ${investment.id} 的收益任务添加到队列失败:`, queueError);

        // 如果添加到队列失败，回退到直接处理
      }
    } else {

    }

    // 如果队列系统不可用或添加到队列失败，直接处理收益
    let result = null;

    try {
      // 检查是否可以创建事务
      if (sequelize && typeof sequelize.transaction === 'function') {
        // 添加事务创建重试逻辑
        const maxRetries = 3;
        let retryCount = 0;
        let txError = null;

        while (retryCount < maxRetries) {
          try {
            // 创建事务
            transaction = await sequelize.transaction();

            // 如果成功创建事务，跳出重试循环
            if (transaction) break;
          } catch (error) {
            txError = error;
            retryCount++;
            logger.warn(`为投资ID ${investment.id} 创建事务失败 (尝试 ${retryCount}/${maxRetries}): ${error.message}`);

            // 如果还有重试机会，等待一段时间后重试
            if (retryCount < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 递增等待时间
            }
          }
        }

        // 如果成功创建了事务
        if (transaction) {
          try {
            // 处理收益的核心逻辑
            result = await processInvestmentProfitCore(investment, specificProfitTime, transaction, isCompensation);

            // 根据结果提交或回滚事务
            if (result && result.success) {
              await transaction.commit();
            } else {
              if (transaction) await transaction.rollback();
            }
          } catch (error) {
            // 如果处理过程中出错
            logger.error(`处理投资ID ${investment.id} 的收益过程中出错:`, error);
            if (transaction) await transaction.rollback();
            result = {
              success: false,
              message: `处理收益失败: ${error.message}`,
              investment_id: investment.id
            };
          }
        } else {
          // 如果多次重试后仍无法创建事务
          logger.error(`为投资ID ${investment.id} 创建事务失败，已重试 ${maxRetries} 次:`, txError);
          result = {
            success: false,
            message: `无法创建事务: ${txError ? txError.message : '未知错误'}`,
            investment_id: investment.id
          };
        }
      } else {
        // 无法创建事务，返回错误
        logger.error(`无法创建事务，无法处理投资ID ${investment.id} 的收益`);
        result = {
          success: false,
          message: '无法创建事务',
          investment_id: investment.id
        };
      }

      return result;
    } catch (error) {
      // 回滚事务
      if (transaction) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          logger.error(`回滚投资ID ${investment.id} 的事务失败:`, rollbackError);
        }
      }

      logger.error(`处理投资ID ${investment.id} 的收益失败:`, error);
      return {
        success: false,
        message: `收益发放失败: ${error.message}`,
        investment_id: investment.id
      };
    }
  } catch (outerError) {
    logger.error(`处理投资ID ${investment ? investment.id : 'unknown'} 的收益过程中发生未处理的错误:`, outerError);
    return {
      success: false,
      message: `收益发放失败: ${outerError.message}`,
      investment_id: investment ? investment.id : 'unknown'
    };
  }
};



/**
 * 收益处理的核心逻辑
 * @param {Object} investment - 投资记录
 * @param {Date} [specificProfitTime] - 指定的收益时间（用于补偿）
 * @param {Object} [existingTransaction] - 现有事务（可选）
 * @param {boolean} [isCompensation=false] - 是否是补偿调用
 * @returns {Promise<Object>} - 处理结果
 */
async function processInvestmentProfitCore(investment, specificProfitTime = null, existingTransaction = null, isCompensation = false) {
  // 处理事务逻辑
  let transaction = existingTransaction;
  let needToCommit = false;

  if (!transaction && sequelize && typeof sequelize.transaction === 'function') {
    // 添加事务创建重试逻辑
    const maxRetries = 3;
    let retryCount = 0;
    let lastError = null;

    while (retryCount < maxRetries) {
      try {
        // 无论是否为补偿调用，如果没有提供事务，都创建一个新事务
        transaction = await sequelize.transaction();
        needToCommit = true;
        logger.info(`为投资ID ${investment.id} 创建新事务${isCompensation ? '(补偿调用)' : ''}`);
        break; // 成功创建事务，跳出循环
      } catch (error) {
        lastError = error;
        retryCount++;
        logger.warn(`为投资ID ${investment.id} 创建事务失败 (尝试 ${retryCount}/${maxRetries}): ${error.message}`);

        // 如果还有重试机会，等待一段时间后重试
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 递增等待时间
        }
      }
    }

    // 如果多次重试后仍无法创建事务
    if (!transaction) {
      logger.error(`为投资ID ${investment.id} 创建事务失败，已重试 ${maxRetries} 次:`, lastError);
      return {
        success: false,
        message: `无法创建事务: ${lastError ? lastError.message : '未知错误'}`,
        investment_id: investment.id
      };
    }
  } else if (transaction) {
    logger.info(`为投资ID ${investment.id} 使用现有事务${isCompensation ? '(补偿调用)' : ''}`);
  } else {
    logger.error(`为投资ID ${investment.id} 处理收益时无法使用事务`);
    // 如果没有事务且无法创建事务，立即返回错误
    return {
      success: false,
      message: '无法使用事务',
      investment_id: investment.id
    };
  }

  try {
    // 确保投资记录包含项目信息
    let project = investment.project;
    if (!project) {
      if (transaction) {
        project = await Project.findByPk(investment.project_id, { transaction });
      } else {
        project = await Project.findByPk(investment.project_id);
      }
      if (!project) {
        if (!existingTransaction && transaction) await transaction.rollback();
        return {
          success: false,
          message: '项目不存在',
          investment_id: investment.id
        };
      }
    }

    // 使用指定的收益时间或当前时间
    const profitTime = specificProfitTime || new Date();

    // 检查是否已存在相同日期的收益记录
    let existingProfit;
    const ProfitModel = exports.InvestmentProfit || InvestmentProfit;
    const profitDateStr = profitTime.toISOString().split('T')[0];

    if (transaction) {
      // 使用日期部分进行比较
      existingProfit = await ProfitModel.findOne({
        where: {
          investment_id: investment.id,
          [Op.and]: [
            sequelize.where(
              sequelize.fn('DATE', sequelize.col('profit_time')),
              profitDateStr
            )
          ]
        },
        transaction
      });
    } else {
      existingProfit = await ProfitModel.findOne({
        where: {
          investment_id: investment.id,
          [Op.and]: [
            sequelize.where(
              sequelize.fn('DATE', sequelize.col('profit_time')),
              profitDateStr
            )
          ]
        }
      });
    }

    if (existingProfit) {
      if (!existingTransaction && transaction) await transaction.rollback();
      logger.warn(`投资ID ${investment.id} 在日期 ${profitDateStr} 已有收益记录，跳过发放`);
      return {
        success: false,
        message: '该投资在此日期已有收益记录',
        investment_id: investment.id,
        existing_profit_id: existingProfit.id
      };
    }

    // 检查投资状态
    if (investment.status !== 'active' && investment.status !== 1) {
      if (!existingTransaction && transaction) await transaction.rollback();
      return {
        success: false,
        message: '投资不是活跃状态',
        investment_id: investment.id
      };
    }

    // 检查是否达到最大收益次数
    if (project.max_profit_times > 0 && investment.profit_count >= project.max_profit_times) {
      try {
        // 如果达到最大收益次数，将投资状态更新为已完成
        investment.status = 'completed';
        if (transaction) {
          await investment.save({ transaction });
        } else {
          await investment.save();
        }

        if (!existingTransaction && transaction) await transaction.commit();

        logger.info(`投资ID ${investment.id} 已达到最大收益次数，状态更新为已完成`);
        return {
          success: false,
          message: '已达到最大收益次数',
          investment_id: investment.id
        };
      } catch (error) {
        logger.error(`更新投资ID ${investment.id} 状态失败:`, error);
        if (!existingTransaction && transaction) await transaction.rollback();
        return {
          success: false,
          message: `更新投资状态失败: ${error.message}`,
          investment_id: investment.id
        };
      }
    }

    // 计算收益金额
    const profitAmount = (investment.amount * investment.profit_rate / 100).toFixed(2);

    // 创建收益记录
    let profitRecord;
    try {
      // 使用注入的模型或默认模型
      const ProfitModel = exports.InvestmentProfit || InvestmentProfit;

      // 使用上面计算的理论收益时间
      if (transaction) {
        profitRecord = await ProfitModel.create({
          investment_id: investment.id,
          user_id: investment.user_id,
          amount: profitAmount,
          profit_time: profitTime, // 使用指定的收益时间或当前时间
          status: 'pending'
        }, { transaction });
      } else {
        profitRecord = await ProfitModel.create({
          investment_id: investment.id,
          user_id: investment.user_id,
          amount: profitAmount,
          profit_time: profitTime, // 使用指定的收益时间或当前时间
          status: 'pending'
        });
      }
    } catch (error) {
      logger.error(`创建投资ID ${investment.id} 的收益记录失败:`, error);
      if (!existingTransaction && transaction) await transaction.rollback();
      return {
        success: false,
        message: `创建收益记录失败: ${error.message}`,
        investment_id: investment.id
      };
    }

    // 将收益添加到用户的收入账户
    let result;
    try {
      result = await balanceService.adjustBalance(
        investment.user_id,
        'income', // 收益添加到收入账户
        profitAmount,
        'add',
        'profit', // 交易类型为收益
        `投资收益 ${project.name}`, // 统一描述格式
        investment.id,
        'investment',
        transaction
      );
    } catch (error) {
      logger.error(`调整投资ID ${investment.id} 用户余额失败:`, error);
      if (!existingTransaction && transaction) await transaction.rollback();
      return {
        success: false,
        message: `调整用户余额失败: ${error.message}`,
        investment_id: investment.id
      };
    }

    // 更新收益记录状态和关联的交易ID
    try {
      profitRecord.status = 'paid';
      profitRecord.transaction_id = result.transactionId;
      if (transaction) {
        await profitRecord.save({ transaction });
      } else {
        await profitRecord.save();
      }
    } catch (error) {
      logger.error(`更新投资ID ${investment.id} 的收益记录状态失败:`, error);
      if (!existingTransaction && transaction) await transaction.rollback();
      return {
        success: false,
        message: `更新收益记录状态失败: ${error.message}`,
        investment_id: investment.id
      };
    }

    // 更新投资记录
    try {
      // 计算理论收益时间
      let theoreticalProfitTime;
      const profitTimeHours = project.profit_time * 60 * 60 * 1000; // 转换为毫秒

      // 如果有上次收益时间，基于上次收益时间计算
      if (investment.last_profit_time) {
        const lastProfitTime = new Date(investment.last_profit_time);
        theoreticalProfitTime = new Date(lastProfitTime.getTime() + profitTimeHours);
        logger.info(`投资ID ${investment.id} 基于上次收益时间 ${dateUtils.formatDateTime(lastProfitTime)} 计算理论收益时间: ${dateUtils.formatDateTime(theoreticalProfitTime)}`);
      } else {
        // 如果没有上次收益时间，基于购买时间计算
        const startTime = new Date(investment.start_time || investment.created_at);
        theoreticalProfitTime = new Date(startTime.getTime() + profitTimeHours);
        logger.info(`投资ID ${investment.id} 基于购买时间 ${dateUtils.formatDateTime(startTime)} 计算理论收益时间: ${dateUtils.formatDateTime(theoreticalProfitTime)}`);
      }

      // 使用理论收益时间作为last_profit_time，而不是实际发放时间
      // 如果是补偿调用且指定了收益时间，使用指定的收益时间
      investment.last_profit_time = isCompensation && specificProfitTime ? specificProfitTime : theoreticalProfitTime;
      investment.profit_count += 1;
      investment.total_profit = (parseFloat(investment.total_profit || 0) + parseFloat(profitAmount)).toFixed(2);
      if (transaction) {
        await investment.save({ transaction });
      } else {
        await investment.save();
      }
    } catch (error) {
      logger.error(`更新投资ID ${investment.id} 记录失败:`, error);
      if (!existingTransaction && transaction) await transaction.rollback();
      return {
        success: false,
        message: `更新投资记录失败: ${error.message}`,
        investment_id: investment.id
      };
    }

    // 处理收益返佣
    try {
      await commissionService.processIncomeCommission(
        investment.user_id,
        profitAmount,
        result.transactionId,
        investment.id,
        transaction
      );
    } catch (error) {
      logger.error(`处理投资ID ${investment.id} 的收益返佣失败:`, error);
      // 返佣失败不影响收益发放，继续执行
    }

    // 如果需要提交事务，则提交事务
    if (needToCommit && transaction) {
      try {
        await transaction.commit();
      } catch (error) {
        logger.error(`提交投资ID ${investment.id} 的事务失败:`, error);
        // 事务提交失败，但收益已经处理完成，继续执行
      }
    }

    // 安排下一次收益任务
    try {
      if (await isRedisAvailable()) {
        await scheduleNextProfitTask(investment);
      } else {
        logger.warn(`Redis不可用，无法为投资ID ${investment.id} 安排下一次收益任务`);
      }
    } catch (error) {
      logger.error(`安排投资ID ${investment.id} 的下一次收益任务失败:`, error);
    }

    return {
      success: true,
      message: '收益发放成功',
      investment_id: investment.id,
      profit_id: profitRecord.id,
      amount: profitAmount
    };
  } catch (error) {
    // 如果需要回滚事务，则回滚事务
    if (needToCommit && transaction) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        logger.error(`回滚投资ID ${investment.id} 的事务失败:`, rollbackError);
      }
    }

    throw error;
  }
}

/**
 * 计算理论收益时间
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @param {Date} [specificProfitTime] - 指定的收益时间（用于补偿）
 * @returns {Date} - 理论收益时间
 */
function calculateTheoreticalProfitTime(investment, project, specificProfitTime = null) {
  try {
    // 如果指定了收益时间（用于补偿），直接使用
    if (specificProfitTime) {
      const date = new Date(specificProfitTime);
      logger.info(`投资ID ${investment.id} 使用指定的收益时间: ${dateUtils.formatDateTime(date)}`);
      return date;
    }

    // 如果是首次收益
    if (!investment.last_profit_time) {
      const baseTime = new Date(investment.start_time || investment.created_at);
      const firstProfitTime = new Date(baseTime.getTime() + project.profit_time * 60 * 60 * 1000);
      logger.info(`投资ID ${investment.id} 首次收益，基于开始时间 ${dateUtils.formatDateTime(baseTime)} 计算理论收益时间: ${dateUtils.formatDateTime(firstProfitTime)}`);
      return firstProfitTime;
    }

    // 如果不是首次收益，基于上次收益时间计算
    const lastProfitTime = new Date(investment.last_profit_time);
    const nextProfitTime = new Date(lastProfitTime.getTime() + project.profit_time * 60 * 60 * 1000);
    logger.info(`投资ID ${investment.id} 非首次收益，基于上次收益时间 ${dateUtils.formatDateTime(lastProfitTime)} 计算理论收益时间: ${dateUtils.formatDateTime(nextProfitTime)}`);
    return nextProfitTime;
  } catch (error) {
    // 如果计算过程中出现错误，记录错误并返回当前时间作为理论收益时间
    logger.error(`计算投资ID ${investment.id} 的理论收益时间失败: ${error.message}`, error);
    const now = new Date();
    logger.warn(`使用当前时间 ${dateUtils.formatDateTime(now)} 作为理论收益时间`);
    return now;
  }
}

/**
 * 安排下一次收益任务
 * @param {Object} investment - 投资记录
 * @returns {Promise<Object>} - 结果
 */
async function scheduleNextProfitTask(investment) {
  try {
    // 确保投资记录包含项目信息
    let project = investment.project;
    if (!project) {
      project = await Project.findByPk(investment.project_id);
      if (!project) {
        throw new Error('项目不存在');
      }
    }

    // 计算下一次收益时间
    let nextProfitTime;
    const profitTimeHours = project.profit_time * 60 * 60 * 1000; // 转换为毫秒

    // 如果有上次收益时间，基于上次收益时间计算
    if (investment.last_profit_time) {
      const lastProfitTime = new Date(investment.last_profit_time);
      nextProfitTime = new Date(lastProfitTime.getTime() + profitTimeHours);
      logger.info(`投资ID ${investment.id} 基于上次收益时间 ${dateUtils.formatDateTime(lastProfitTime)} 计算下一次收益时间`);
    } else {
      // 如果没有上次收益时间，基于购买时间计算
      const startTime = new Date(investment.start_time || investment.created_at);
      nextProfitTime = new Date(startTime.getTime() + profitTimeHours);
      logger.info(`投资ID ${investment.id} 基于购买时间 ${dateUtils.formatDateTime(startTime)} 计算下一次收益时间`);
    }

    // 如果计算出的下一次收益时间已经过去，则递增直到未来时间
    const now = new Date();
    while (nextProfitTime <= now) {
      nextProfitTime = new Date(nextProfitTime.getTime() + profitTimeHours);
      logger.info(`投资ID ${investment.id} 计算的收益时间已过，递增至 ${dateUtils.formatDateTime(nextProfitTime)}`);
    }

    // 添加到Redis队列
    await redisClient.addProfitTask(investment.id, nextProfitTime);

    logger.info(`投资ID ${investment.id} 的下一次收益任务已安排，时间: ${dateUtils.formatDateTime(nextProfitTime)}`);

    return {
      success: true,
      investment_id: investment.id,
      next_profit_time: nextProfitTime
    };
  } catch (error) {
    logger.error(`安排投资ID ${investment.id} 的下一次收益任务失败:`, error);
    throw error;
  }
}

/**
 * 检查Redis是否可用
 * @returns {Promise<boolean>} - Redis是否可用
 */
async function isRedisAvailable() {
  try {
    await redisClient.client.ping();
    return true;
  } catch (error) {
    logger.warn('Redis不可用:', error.message);
    return false;
  }
}

// 导出其他方法，便于测试和其他模块使用
exports.calculateTheoreticalProfitTime = calculateTheoreticalProfitTime;
exports.scheduleNextProfitTask = scheduleNextProfitTask;
exports.isRedisAvailable = isRedisAvailable;