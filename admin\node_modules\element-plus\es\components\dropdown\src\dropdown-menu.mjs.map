{"version": 3, "file": "dropdown-menu.mjs", "sources": ["../../../../../../packages/components/dropdown/src/dropdown-menu.vue"], "sourcesContent": ["<template>\n  <ul\n    :ref=\"dropdownListWrapperRef\"\n    :class=\"dropdownKls\"\n    :style=\"rovingFocusGroupRootStyle\"\n    :tabindex=\"-1\"\n    :role=\"role\"\n    :aria-labelledby=\"triggerId\"\n    @blur=\"onBlur\"\n    @focus=\"onFocus\"\n    @keydown.self=\"handleKeydown\"\n    @mousedown.self=\"onMousedown\"\n  >\n    <slot />\n  </ul>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, inject, unref } from 'vue'\nimport { composeEventHandlers, composeRefs } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { FOCUS_TRAP_INJECTION_KEY } from '@element-plus/components/focus-trap'\nimport {\n  ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n  ROVING_FOCUS_GROUP_INJECTION_KEY,\n  focusFirst,\n} from '@element-plus/components/roving-focus-group'\nimport { useNamespace } from '@element-plus/hooks'\nimport { DROPDOWN_INJECTION_KEY } from './tokens'\nimport {\n  DROPDOWN_COLLECTION_INJECTION_KEY,\n  FIRST_LAST_KEYS,\n  LAST_KEYS,\n  dropdownMenuProps,\n} from './dropdown'\nimport { useDropdown } from './useDropdown'\n\nexport default defineComponent({\n  name: 'ElDropdownMenu',\n  props: dropdownMenuProps,\n  setup(props) {\n    const ns = useNamespace('dropdown')\n    const { _elDropdownSize } = useDropdown()\n    const size = _elDropdownSize.value\n\n    const { focusTrapRef, onKeydown } = inject(\n      FOCUS_TRAP_INJECTION_KEY,\n      undefined\n    )!\n\n    const { contentRef, role, triggerId } = inject(\n      DROPDOWN_INJECTION_KEY,\n      undefined\n    )!\n\n    const { collectionRef: dropdownCollectionRef, getItems } = inject(\n      DROPDOWN_COLLECTION_INJECTION_KEY,\n      undefined\n    )!\n\n    const {\n      rovingFocusGroupRef,\n      rovingFocusGroupRootStyle,\n      tabIndex,\n      onBlur,\n      onFocus,\n      onMousedown,\n    } = inject(ROVING_FOCUS_GROUP_INJECTION_KEY, undefined)!\n\n    const { collectionRef: rovingFocusGroupCollectionRef } = inject(\n      ROVING_FOCUS_COLLECTION_INJECTION_KEY,\n      undefined\n    )!\n\n    const dropdownKls = computed(() => {\n      return [ns.b('menu'), ns.bm('menu', size?.value)]\n    })\n\n    const dropdownListWrapperRef = composeRefs(\n      contentRef,\n      dropdownCollectionRef,\n      focusTrapRef,\n      rovingFocusGroupRef,\n      rovingFocusGroupCollectionRef\n    )\n\n    const composedKeydown = composeEventHandlers(\n      (e: KeyboardEvent) => {\n        props.onKeydown?.(e)\n      },\n      (e) => {\n        const { currentTarget, code, target } = e\n        const isKeydownContained = (currentTarget as Node).contains(\n          target as Node\n        )\n\n        if (isKeydownContained) {\n          // TODO: implement typeahead search\n        }\n\n        if (EVENT_CODE.tab === code) {\n          e.stopImmediatePropagation()\n        }\n\n        e.preventDefault()\n\n        if (target !== unref(contentRef) || !FIRST_LAST_KEYS.includes(code))\n          return\n        const items = getItems<{ disabled: boolean }>().filter(\n          (item) => !item.disabled\n        )\n        const targets = items.map((item) => item.ref!)\n        if (LAST_KEYS.includes(code)) {\n          targets.reverse()\n        }\n        focusFirst(targets)\n      }\n    )\n\n    const handleKeydown = (e: KeyboardEvent) => {\n      composedKeydown(e)\n      onKeydown(e)\n    }\n\n    return {\n      size,\n      rovingFocusGroupRootStyle,\n      tabIndex,\n      dropdownKls,\n      role,\n      triggerId,\n      dropdownListWrapperRef,\n      handleKeydown,\n      onBlur,\n      onFocus,\n      onMousedown,\n    }\n  },\n})\n</script>\n"], "names": ["DROPDOWN_COLLECTION_INJECTION_KEY", "ROVING_FOCUS_COLLECTION_INJECTION_KEY", "_openBlock", "_createElementBlock", "_normalizeClass", "_normalizeStyle", "_withModifiers", "_renderSlot"], "mappings": ";;;;;;;;;;;;;;AAqCA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,gBAAA;AAAA,EACN,KAAO,EAAA,iBAAA;AAAA,EACP,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAClC,IAAM,MAAA,EAAE,eAAgB,EAAA,GAAI,WAAY,EAAA,CAAA;AACxC,IAAA,MAAM,OAAO,eAAgB,CAAA,KAAA,CAAA;AAE7B,IAAM,MAAA,EAAE,YAAc,EAAA,SAAA,EAAc,GAAA,MAAA,CAAA,wBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAClC,MAAA,EAAA,UAAA,EAAA,IAAA,EAAA,SAAA,EAAA,GAAA,MAAA,CAAA,sBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,GAAA,MAAA,CAAAA,wBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACF,MAAA;AAEA,MAAA,mBAA0B;AAAc,MACtC,yBAAA;AAAA,MACA,QAAA;AAAA,MACF,MAAA;AAEA,MAAA,OAAQ;AAAmD,MACzD,WAAA;AAAA,KACA,GAAA,MAAA,CAAA,gCAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACF,MAAA,EAAA,aAAA,EAAA,6BAAA,EAAA,GAAA,MAAA,CAAAC,0BAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAEA,IAAM,MAAA,WAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACJ,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,sBAAA,GAAA,WAAA,CAAA,UAAA,EAAA,qBAAA,EAAA,YAAA,EAAA,mBAAA,EAAA,6BAAA,CAAA,CAAA;AAAA,IACA,MAAA,eAAA,GAAA,oBAAA,CAAA,CAAA,CAAA,KAAA;AAAA,MACA,IAAA,EAAA,CAAA;AAAA,MACA,CAAA,EAAA,GAAA,KAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KACF,EAAA,CAAI,CAAO,KAAA;AAEX,MAAM,MAAE,EAAe,aAAA,EAAA,IAAA,EAAA,MAAA,EAAA,GAAA,CAAA,CAAA;AAAkC,MACvD,aAAA,CAAA,QAAA,CAAA,MAAA,EAAA;AAIF,MAAM,IAAA,UAAA,CAAA,GAAc,SAAS,EAAM;AACjC,QAAO,CAAA,CAAA,wBAAqB,EAAA,CAAA;AAAoB,OACjD;AAED,MAAA,CAAA,CAAA,cAA+B,EAAA,CAAA;AAAA,MAC7B,IAAA,MAAA,KAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA,eAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AAAA,QACA,OAAA;AAAA,MACA,MAAA,KAAA,GAAA,QAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AAAA,MACA,MAAA,OAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAAA,MACA,IAAA,SAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AAAA,QACF,OAAA,CAAA,OAAA,EAAA,CAAA;AAEA,OAAA;AAAwB,MACtB,UAAsB,CAAA,OAAA,CAAA,CAAA;AACpB,KAAA,CAAA,CAAA;AAAmB,IACrB,MAAA,aAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACA,eAAO,CAAA,CAAA,CAAA,CAAA;AACL,MAAA,SAAQ,CAAA,CAAA,CAAA,CAAA;AACR,KAAA,CAAA;AAAmD,IACjD,OAAA;AAAA,MACF,IAAA;AAEA,MAAA,yBAAwB;AAAA,MAExB,QAAA;AAEA,MAAI,WAAA;AACF,MAAA,IAAA;AAA2B,MAC7B,SAAA;AAEA,MAAA,sBAAiB;AAEjB,MAAA;AACE,MAAA,MAAA;AACF,MAAM,OAAA;AAA0C,MAC9C,WAAU;AAAM,KAClB,CAAA;AACA,GAAA;AACA,CAAI,CAAA,CAAA;AACc,SAClB,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,EAAA,OAAAC,SAAW,EAAO,EAAAC,kBAAA,CAAA,IAAA,EAAA;AAAA,IACpB,GAAA,EAAA,IAAA,CAAA,sBAAA;AAAA,IACF,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AAEA,IAAM,KAAA,EAAAC,cAAgB,CAAC,IAAqB,CAAA,yBAAA,CAAA;AAC1C,IAAA,QAAA,EAAA,CAAA,CAAA;AACA,IAAA,IAAA,EAAA,IAAA,CAAA,IAAW;AAAA,IACb,iBAAA,EAAA,IAAA,CAAA,SAAA;AAEA,IAAO,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,IACL,OAAA,EAAA,IAAA,CAAA,OAAA;AAAA,IACA,SAAA,EAAAC,aAAA,CAAA,IAAA,CAAA,aAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IACA,WAAA,EAAAA,aAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,GACA,EAAA;AAAA,IACAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,GACA,EAAA,EAAA,EAAA,CAAA,MAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,aAAA,CAAA,CAAA,CAAA;AAAA,CACA;AACA,mBACA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,mBAAA,CAAA,CAAA,CAAA;;;;"}