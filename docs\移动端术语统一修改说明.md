# 移动端术语统一修改说明

## 📋 **修改概述**

将移动端首页中的"Today's Invitations"修改为"Today's Referrals"，以保持系统术语的一致性。

## 🔧 **修改详情**

### **修改文件**
- **文件路径**: `mobile\pages\home\index.vue`
- **修改位置**: 第73行

### **修改内容**
```html
<!-- 修改前 -->
<text class="stats-label">Today's Invitations</text>

<!-- 修改后 -->
<text class="stats-label">Today's Referrals</text>
```

## 🎯 **修改原因**

### **术语一致性分析**
通过对系统的全面分析，发现系统中广泛使用以下术语：

1. **"Referrals"** - 在邀请页面、API接口、管理端中广泛使用
2. **"Team Members"** - 在团队管理功能中使用
3. **"Investment Plans"** - 产品被称为投资计划
4. **"Buy"** - 购买按钮统一使用Buy

### **系统中"Referrals"的使用场景**
- 邀请页面标题和功能
- API接口命名（如 `/api/invite/referrals`）
- 管理端相关功能
- 文档和配置中的术语

### **选择"Referrals"的优势**
1. **术语统一** - 与系统其他部分保持一致
2. **专业性** - "Referrals"比"Invitations"更专业，更符合金融/投资平台的语境
3. **国际化** - "Referrals"是国际通用的推荐术语
4. **用户理解** - 在投资平台中，用户更容易理解"Referrals"的含义

## 📊 **术语对比**

| 术语 | 专业性 | 一致性 | 国际化 | 推荐度 |
|------|--------|--------|--------|--------|
| Invitations | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ | ❌ |
| Referrals | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ |

## 🔍 **影响范围**

### **修改范围**
- **仅前端显示**: 只修改用户界面显示的文本
- **数据变量**: 保持 `todayInvites` 变量名不变（内部变量无需修改）
- **功能逻辑**: 不影响任何业务逻辑和数据处理

### **兼容性**
- **向后兼容**: 完全兼容现有功能
- **API接口**: 不影响任何API接口
- **数据库**: 不涉及数据库修改

## ✅ **验证方法**

### **测试步骤**
1. 启动移动端应用
2. 登录用户账户
3. 查看首页统计卡片
4. 确认显示为"Today's Referrals"

### **预期结果**
- 首页统计卡片正确显示"Today's Referrals"
- 功能正常，数据显示正确
- 与系统其他部分术语保持一致

## 📝 **相关文件**

### **已确认使用"Referrals"的文件**
- `mobile\pages\invite\index.vue` - 邀请页面
- `mobile\services\api\invite.js` - 邀请相关API
- `server\controllers\inviteController.js` - 邀请控制器
- `admin\src\views\systemParams\panels\UserPanel.vue` - 用户配置面板

### **术语统一状态**
- ✅ 移动端首页 - 已修改为"Today's Referrals"
- ✅ 邀请页面 - 已使用"Referrals"术语
- ✅ API接口 - 已使用"referrals"命名
- ✅ 管理端 - 已使用"邀请"相关术语

## 🎯 **总结**

通过将"Today's Invitations"修改为"Today's Referrals"，实现了：

1. **术语统一** - 与系统整体术语保持一致
2. **专业提升** - 使用更专业的金融投资平台术语
3. **用户体验** - 提供更一致的用户界面体验
4. **维护性** - 降低术语混乱带来的维护成本

这个小修改虽然简单，但对提升系统的专业性和一致性具有重要意义。
