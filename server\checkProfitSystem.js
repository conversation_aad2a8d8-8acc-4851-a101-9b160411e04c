const profitSystem = require('./services/profitSystem');
const profitPoller = require('./services/profitPoller');
const profitCompensator = require('./services/profitCompensator');
const profitScheduler = require('./services/profitScheduler');
const { Investment, Project } = require('./models');

async function checkProfitSystem() {
  try {
    console.log('检查收益系统状态...');

    // 检查投资记录
    const investment = await Investment.findByPk(116, {
      include: [{ model: Project, as: 'project' }]
    });

    console.log('投资记录:', JSON.stringify(investment, null, 2));

    // 计算下一次收益时间
    if (investment) {
      const nextProfitTime = profitScheduler.calculateNextProfitTime(investment, investment.project);
      console.log('计算的下一次收益时间:', nextProfitTime);
      console.log('当前时间:', new Date());
      console.log('是否应该发放收益:', nextProfitTime <= new Date());
    }

    // 手动执行一次轮询
    console.log('手动执行一次轮询...');
    await profitPoller.pollRedisTasks();

    // 手动执行一次补偿检查
    console.log('手动执行一次补偿检查...');
    const compensationResult = await profitCompensator.runCompensationCheck();
    console.log('补偿检查结果:', JSON.stringify(compensationResult, null, 2));

    process.exit(0);
  } catch (error) {
    console.error('检查收益系统失败:', error);
    process.exit(1);
  }
}

checkProfitSystem();
