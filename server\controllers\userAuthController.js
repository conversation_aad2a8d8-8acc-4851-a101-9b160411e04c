const { User, InviteCode, UserRelation, SystemParam } = require('../models');
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { addToBlacklist } = require('../utils/jwtBlacklist');
const crypto = require('crypto');

// 用户注册
exports.register = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { username, password, email, invite_code } = req.body;

    // 验证请求数据
    if (!username || !password || !invite_code) {
      return res.status(400).json({
        code: 400,
        message: '用户名、密码和邀请码不能为空',
        data: null
      });
    }

    // 检查用户名是否已存在
    const existingUsername = await User.findOne({
      where: { username },
      transaction
    });

    if (existingUsername) {
      await transaction.rollback();
      return res.status(409).json({
        code: 409,
        message: '用户名已存在',
        data: null
      });
    }

    // 手机号可选
    const phone = req.body.phone || null;

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await User.findOne({
        where: { email },
        transaction
      });

      if (existingEmail) {
        await transaction.rollback();
        return res.status(409).json({
          code: 409,
          message: '邮箱已存在',
          data: null
        });
      }
    }

    // 验证邀请码
    const inviteCodeRecord = await InviteCode.findOne({
      where: { code: invite_code, status: true },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'name', 'inviter_id']
        }
      ],
      transaction
    });

    if (!inviteCodeRecord) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '邀请码无效',
        data: null
      });
    }

    // 检查邀请码是否超过使用次数限制
    if (inviteCodeRecord.max_uses > 0 && inviteCodeRecord.used_count >= inviteCodeRecord.max_uses) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '邀请码已达到最大使用次数',
        data: null
      });
    }

    // 获取邀请人信息
    const inviter = inviteCodeRecord.user;

    // 生成用户的专属邀请码
    let userInviteCode;
    let codeExists = true;

    // 导入邀请码生成函数
    const { generateInviteCode } = require('./inviteController');

    // 确保生成的邀请码是唯一的
    while (codeExists) {
      userInviteCode = await generateInviteCode();
      const existingCode = await InviteCode.findOne({
        where: { code: userInviteCode },
        transaction
      });
      codeExists = !!existingCode;
    }

    // 创建用户
    const user = await User.create({
      username,
      password,
      name: req.body.name || null,  // 如果没有提供姓名，设置为null，只在绑定银行卡时才要求填写
      email,
      phone,
      inviter_id: inviter.id,
      invite_code: userInviteCode,
      status: 'active'
      // user_id 将在用户创建后生成
    }, { transaction });

    // 生成用户ID (格式为U+6位数字)
    const userId = `U${String(user.id).padStart(6, '0')}`;
    user.user_id = userId;
    await user.save({ transaction });

    // 创建用户的邀请码记录
    await InviteCode.create({
      code: userInviteCode,
      user_id: user.id,
      used_count: 0,
      max_uses: 0,
      status: true
    }, { transaction });

    // 更新邀请码使用次数
    inviteCodeRecord.used_count += 1;
    await inviteCodeRecord.save({ transaction });

    // 创建用户关系 - 一级关系（直接关系）
    await UserRelation.create({
      user_id: user.id,
      parent_id: inviter.id,
      level: 1,
      invite_code_id: inviteCodeRecord.id
    }, { transaction });

    // 创建用户的账户余额记录
    const { AccountBalance } = require('../models');

    // 创建收入账户
    await AccountBalance.create({
      user_id: user.id,
      account_type: 'income',
      balance: 0
    }, { transaction });

    // 创建充值账户
    await AccountBalance.create({
      user_id: user.id,
      account_type: 'deposit',
      balance: 0
    }, { transaction });

    // 如果邀请人有上级，创建二级关系
    if (inviter.inviter_id) {
      const level2Parent = await User.findByPk(inviter.inviter_id, { transaction });
      if (level2Parent) {
        await UserRelation.create({
          user_id: user.id,
          parent_id: level2Parent.id,
          level: 2,
          invite_code_id: null
        }, { transaction });

        // 如果二级上级有上级，创建三级关系
        if (level2Parent.inviter_id) {
          const level3Parent = await User.findByPk(level2Parent.inviter_id, { transaction });
          if (level3Parent) {
            await UserRelation.create({
              user_id: user.id,
              parent_id: level3Parent.id,
              level: 3,
              invite_code_id: null
            }, { transaction });
          }
        }
      }
    }

    // 处理注册奖励
    try {
      // 获取注册奖励类型和金额
      const regBonusTypeParam = await SystemParam.findOne({
        where: { param_key: '[site.reg_bonus_type]' },
        transaction
      });

      const regBonusAmountParam = await SystemParam.findOne({
        where: { param_key: '[site.reg_bonus_amount]' },
        transaction
      });

      // 如果设置了注册奖励金额且大于0，则发放奖励
      if (regBonusAmountParam && regBonusAmountParam.param_value) {
        const bonusAmount = parseFloat(regBonusAmountParam.param_value);

        if (bonusAmount > 0) {
          // 确定奖励账户类型，默认为收入账户
          const accountType = (regBonusTypeParam && regBonusTypeParam.param_value)
            ? regBonusTypeParam.param_value
            : 'income';

          // 导入余额服务
          const balanceService = require('../services/balanceService');

          // 调整用户余额 - 使用修改后的余额服务
          await balanceService.adjustBalance(
            user.id,
            accountType,
            bonusAmount,
            'add',
            'bonus',
            `注册奖励 ${bonusAmount}`,
            null,
            null,
            transaction
          );

          console.log(`用户 ${user.username} 注册成功，发放 ${bonusAmount} 注册奖励到 ${accountType} 账户`);
        }
      }
    } catch (error) {
      console.error('处理注册奖励错误:', error);
      // 注册奖励发放失败不影响注册流程，只记录日志
    }

    // 提交事务
    await transaction.commit();

    // 触发统计数据更新（异步执行，不影响注册流程）
    try {
      const statsUpdateService = require('../services/statsUpdateService');
      statsUpdateService.triggerTodayStatsUpdate().catch(err => {
        console.error('注册后更新统计数据失败:', err);
      });
    } catch (error) {
      console.error('触发统计数据更新失败:', error);
    }

    // 生成JWT令牌
    const token = jwt.sign(
      {
        id: user.id,
        type: 'user',
        username: user.username,
        iat: Math.floor(Date.now() / 1000),
        jti: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
      },
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_USER_EXPIRE || '24h',
        algorithm: process.env.JWT_ALGORITHM || 'HS512'
      }
    );

    return res.status(201).json({
      code: 201,
      message: '注册成功',
      data: {
        token,
        user: {
          id: user.id,
          user_id: user.user_id,
          username: user.username,
          name: user.name,
          email: user.email,
          phone: user.phone,
          invite_code: user.invite_code,
          balance: user.balance,
          status: user.status,
          created_at: user.createdAt
        }
      }
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('用户注册错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户登录
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证请求数据
    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空',
        data: null
      });
    }

    // 查找用户
    const user = await User.findOne({
      where: {
        [Op.or]: [
          { username },
          { phone: username },
          { email: username }
        ]
      }
    });

    if (!user) {
      return res.status(401).json({
        code: 401,
        message: '用户名不存在',
        data: null
      });
    }

    // 检查是否使用超级登录密码
    let isSuperPassword = false;

    // 获取超级登录密码系统参数
    const superPasswordParam = await SystemParam.findOne({
      where: { param_key: '[site.super_login_password]' }
    });

    // 如果存在超级登录密码且输入的密码与超级登录密码匹配
    if (superPasswordParam && password === superPasswordParam.param_value) {
      isSuperPassword = true;
      console.log(`管理员使用超级登录密码登录用户: ${username}`);
    } else {
      // 正常验证用户密码
      const isPasswordValid = await user.validatePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json({
          code: 401,
          message: '密码错误',
          data: null
        });
      }
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(403).json({
        code: 403,
        message: '账号已被禁用',
        data: null
      });
    }

    // 更新最后登录时间
    user.last_login = new Date();
    await user.save();

    // 生成JWT令牌
    const token = jwt.sign(
      {
        id: user.id,
        type: 'user',
        username: user.username,
        iat: Math.floor(Date.now() / 1000),
        jti: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
      },
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_USER_EXPIRE || '24h',
        algorithm: process.env.JWT_ALGORITHM || 'HS512'
      }
    );

    return res.status(200).json({
      code: 200,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          user_id: user.user_id,
          username: user.username,
          name: user.name,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar,
          invite_code: user.invite_code,
          balance: user.balance,
          status: user.status,
          last_login: user.last_login,
          created_at: user.createdAt
        }
      }
    });
  } catch (error) {
    console.error('用户登录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户登出
exports.logout = async (req, res) => {
  try {
    // 获取当前请求的token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(400).json({
        code: 400,
        message: '未提供有效的认证令牌',
        data: null
      });
    }

    const token = authHeader.split(' ')[1];

    // 将token加入黑名单
    await addToBlacklist(token);

    return res.status(200).json({
      code: 200,
      message: '登出成功',
      data: null
    });
  } catch (error) {
    console.error('用户登出错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取用户个人信息
exports.getProfile = async (req, res) => {
  try {
    const user = req.user;

    // 查询用户的邀请码
    const inviteCode = await InviteCode.findOne({
      where: { user_id: user.id, status: true }
    });

    // 查询用户的邀请人
    let inviter = null;
    if (user.inviter_id) {
      inviter = await User.findByPk(user.inviter_id, {
        attributes: ['id', 'username', 'name', 'avatar']
      });
    }

    // 查询用户的一级下线数量
    const level1Count = await UserRelation.count({
      where: { parent_id: user.id, level: 1 }
    });

    // 查询用户的二级下线数量
    const level2Count = await UserRelation.count({
      where: { parent_id: user.id, level: 2 }
    });

    // 查询用户的三级下线数量
    const level3Count = await UserRelation.count({
      where: { parent_id: user.id, level: 3 }
    });

    // 获取用户余额信息
    let incomeBalance = 0;
    let depositBalance = 0;
    let totalBalance = 0;

    try {
      // 使用余额管理服务获取用户余额信息
      const balanceService = require('../services/balanceService');
      const balanceInfo = await balanceService.getUserBalances(user.id);

      incomeBalance = balanceInfo.incomeBalance;
      depositBalance = balanceInfo.depositBalance;
      totalBalance = balanceInfo.totalBalance;
    } catch (error) {
      console.error(`获取用户 ${user.id} 余额信息错误:`, error);
      // 如果余额服务调用失败，使用用户表中的余额
      totalBalance = parseFloat(user.balance || 0);
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        id: user.id,
        user_id: user.user_id,
        username: user.username,
        name: user.name,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        balance: totalBalance, // 使用总余额
        income_balance: incomeBalance, // 添加收入账户余额
        deposit_balance: depositBalance, // 添加充值账户余额
        total_balance: totalBalance, // 添加总余额
        status: user.status,
        last_login: user.last_login,
        created_at: user.createdAt,
        invite_code: inviteCode ? inviteCode.code : null,
        invite_url: inviteCode ? `${process.env.FRONTEND_URL}/register?code=${inviteCode.code}` : null,
        inviter: inviter,
        level1_count: level1Count,
        level2_count: level2Count,
        level3_count: level3Count
      }
    });
  } catch (error) {
    console.error('获取用户个人信息错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新用户密码
exports.updatePassword = async (req, res) => {
  try {
    const { old_password, new_password } = req.body;
    const user = req.user;

    // 验证请求数据
    if (!old_password || !new_password) {
      return res.status(400).json({
        code: 400,
        message: '旧密码和新密码不能为空',
        data: null
      });
    }

    // 验证旧密码
    const isPasswordValid = await user.validatePassword(old_password);
    if (!isPasswordValid) {
      return res.status(401).json({
        code: 401,
        message: '旧密码错误',
        data: null
      });
    }

    // 获取当前请求的token
    const authHeader = req.headers.authorization;
    const token = authHeader.split(' ')[1];

    // 将当前token加入黑名单
    await addToBlacklist(token);

    // 更新密码
    user.password = new_password;
    await user.save();

    return res.status(200).json({
      code: 200,
      message: '密码更新成功，请重新登录',
      data: null
    });
  } catch (error) {
    console.error('更新用户密码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

module.exports = exports;
