/**
 * 收益补偿器
 * 负责检查和补偿错过的收益
 */
const { Investment, Project, InvestmentProfit } = require('../models');
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const dateUtils = require('../utils/dateUtils');

/**
 * 执行补偿检查
 * 每小时执行一次
 */
const runCompensationCheck = async () => {
  try {
    logger.info('开始执行补偿检查...');

    // 获取所有活跃的投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      },
      include: [
        {
          model: Project,
          as: 'project'
        }
      ]
    });

    logger.info(`找到 ${activeInvestments.length} 条活跃投资记录`);

    // 处理结果统计
    const results = {
      total: activeInvestments.length,
      processed: 0,
      details: []
    };

    // 当前时间
    const now = new Date();

    // 逐个检查投资
    for (const investment of activeInvestments) {
      try {
        // 使用统一服务计算理论收益时间
        const profitUnifiedService = require('./profitUnifiedService');
        const nextProfitTime = profitUnifiedService.calculateTheoreticalProfitTime(investment, investment.project);

        // 计算当前时间与理论收益时间的差距
        const timeGap = now.getTime() - nextProfitTime.getTime();
        const minDelay = 60 * 1000; // 至少延迟1分钟再发放，防止提前发放

        // 如果下一次收益时间已经过去至少1分钟，处理收益发放
        if (timeGap >= minDelay) {
          try {
            // 使用统一服务处理收益，标记为补偿调用
            const result = await profitUnifiedService.processInvestmentProfit(investment, nextProfitTime, true);

            if (result && result.success) {
              results.processed++;
              results.details.push({
                investment_id: investment.id,
                message: '收益发放成功',
                profit_time: dateUtils.formatDateTime(nextProfitTime),
                delay_minutes: Math.floor(timeGap / 60000)
              });
            } else {
              results.details.push({
                investment_id: investment.id,
                message: result ? result.message : '收益发放失败'
              });
            }
          } catch (error) {
            logger.error(`处理投资ID ${investment.id} 的补偿收益失败:`, error);
            results.details.push({
              investment_id: investment.id,
              message: `处理补偿收益失败: ${error.message}`
            });
          }
        } else if (timeGap > 0) {
          // 理论收益时间已过，但未达到最小延迟
          results.details.push({
            investment_id: investment.id,
            message: `理论收益时间刚过 ${Math.floor(timeGap / 1000)} 秒，等待至少 ${Math.floor(minDelay / 1000)} 秒后发放`,
            next_profit_time: dateUtils.formatDateTime(nextProfitTime)
          });
        } else {
          // 理论收益时间未到
          results.details.push({
            investment_id: investment.id,
            message: '下一次收益时间未到',
            next_profit_time: dateUtils.formatDateTime(nextProfitTime)
          });
        }
      } catch (error) {
        logger.error(`处理投资ID ${investment.id} 的补偿检查失败:`, error);
        results.details.push({
          investment_id: investment.id,
          message: `补偿检查失败: ${error.message}`
        });
      }
    }

    logger.info(`补偿检查完成: 总计 ${results.total}, 处理 ${results.processed}`);
    return results;
  } catch (error) {
    logger.error('执行补偿检查失败:', error);
    return {
      success: false,
      message: '补偿检查失败: ' + error.message
    };
  }
};

// 补偿器实例
let compensatorInterval = null;

/**
 * 启动补偿器
 */
const startCompensator = () => {
  // 如果已经启动，先停止
  if (compensatorInterval) {
    clearInterval(compensatorInterval);
  }

  // 每5分钟执行一次补偿检查
  compensatorInterval = setInterval(runCompensationCheck, 5 * 60 * 1000);
  logger.info('收益补偿器已启动，每5分钟执行一次检查');

  // 立即执行一次补偿检查
  runCompensationCheck().then(result => {
    logger.info('启动时补偿检查完成:', result);
  }).catch(error => {
    logger.error('启动时补偿检查失败:', error);
  });
};

/**
 * 停止补偿器
 */
const stopCompensator = () => {
  if (compensatorInterval) {
    clearInterval(compensatorInterval);
    compensatorInterval = null;
    logger.info('收益补偿器已停止');
  }
};

/**
 * 执行深度补偿检查
 * 检查所有投资的所有理论收益时间点，确保没有遗漏
 */
const runDeepCompensationCheck = async () => {
  try {
    logger.info('开始执行深度补偿检查...');

    // 获取所有活跃的投资记录
    const activeInvestments = await Investment.findAll({
      where: {
        status: 'active'
      },
      include: [
        {
          model: Project,
          as: 'project'
        }
      ]
    });

    logger.info(`找到 ${activeInvestments.length} 条活跃投资记录`);

    // 处理结果统计
    const results = {
      total: activeInvestments.length,
      compensated: 0,
      skipped: 0,
      details: []
    };

    // 当前时间
    const now = new Date();

    // 逐个检查投资
    for (const investment of activeInvestments) {
      try {
        // 获取投资的所有收益记录，按时间排序
        const profits = await InvestmentProfit.findAll({
          where: {
            investment_id: investment.id
          },
          order: [['profit_time', 'ASC']]
        });

        // 确定基准时间点（最后一次收益时间或投资开始时间）
        let baseTime;
        let expectedProfitCount = 0;

        if (profits.length > 0 && profits[profits.length - 1].profit_time) {
          // 使用最后一次收益时间作为基准
          baseTime = new Date(profits[profits.length - 1].profit_time);
          logger.info(`投资ID ${investment.id} 使用最后一次收益时间 ${dateUtils.formatDateTime(baseTime)} 作为基准`);
        } else {
          // 使用投资开始时间作为基准
          baseTime = new Date(investment.start_time || investment.created_at);
          logger.info(`投资ID ${investment.id} 使用投资开始时间 ${dateUtils.formatDateTime(baseTime)} 作为基准`);
        }

        // 计算从基准时间到现在应该发放的收益次数
        const profitCycleMs = investment.project.profit_time * 60 * 60 * 1000;
        const timeSinceBase = now.getTime() - baseTime.getTime();
        expectedProfitCount = Math.floor(timeSinceBase / profitCycleMs);

        logger.info(`投资ID ${investment.id} 从基准时间到现在应该发放 ${expectedProfitCount} 次收益`);

        // 如果没有需要发放的收益，跳过
        if (expectedProfitCount <= 0) {
          results.skipped++;
          results.details.push({
            investment_id: investment.id,
            message: '没有需要发放的收益'
          });
          continue;
        }

        // 计算所有应该发放的收益时间点
        const theoreticalProfitTimes = [];
        let currentTime = new Date(baseTime);

        for (let i = 0; i < expectedProfitCount; i++) {
          currentTime = new Date(currentTime.getTime() + profitCycleMs);
          if (currentTime <= now) {
            theoreticalProfitTimes.push(new Date(currentTime));
          }
        }

        // 检查每个理论时间点是否已经有对应的收益记录
        const existingProfitTimes = profits.map(p => {
          // 确保profit_time是Date对象
          if (p.profit_time) {
            if (typeof p.profit_time === 'string') {
              return new Date(p.profit_time).getTime();
            } else if (p.profit_time instanceof Date) {
              return p.profit_time.getTime();
            }
          }
          return 0;
        });

        const missingProfitTimes = [];

        for (const theoreticalTime of theoreticalProfitTimes) {
          const theoreticalTimeMs = theoreticalTime.getTime();

          // 检查是否存在该时间点的收益记录（允许1分钟的误差）
          const hasProfit = existingProfitTimes.some(existingTimeMs =>
            Math.abs(existingTimeMs - theoreticalTimeMs) < 60000
          );

          if (!hasProfit) {
            missingProfitTimes.push(theoreticalTime);
          }
        }

        // 如果有缺失的收益时间点，记录并处理
        if (missingProfitTimes.length > 0) {
          logger.info(`投资ID ${investment.id} 有 ${missingProfitTimes.length} 个缺失的收益时间点`);

          // 处理每个缺失的收益时间点
          const processedTimes = [];
          const failedTimes = [];

          // 按时间顺序排序缺失的收益时间点
          missingProfitTimes.sort((a, b) => a.getTime() - b.getTime());

          // 创建一个临时的投资对象副本，用于顺序处理收益
          const tempInvestment = JSON.parse(JSON.stringify(investment));
          tempInvestment.project = JSON.parse(JSON.stringify(investment.project));

          // 如果有最后一次收益时间，使用它作为基准
          if (profits.length > 0 && profits[profits.length - 1].profit_time) {
            tempInvestment.last_profit_time = profits[profits.length - 1].profit_time;
          }

          // 顺序处理每个缺失的收益时间点
          for (const missingTime of missingProfitTimes) {
            try {
              // 计算当前时间与理论收益时间的差距
              const timeGap = now.getTime() - missingTime.getTime();
              const minDelay = 60 * 1000; // 至少延迟1分钟再发放，防止提前发放

              // 只处理已经过去至少1分钟的收益时间点
              if (timeGap >= minDelay) {
                // 使用统一服务处理收益，标记为补偿调用
                const profitUnifiedService = require('./profitUnifiedService');

                // 使用理论收益时间作为收益时间
                const result = await profitUnifiedService.processInvestmentProfit(tempInvestment, missingTime, true);

                if (result && result.success) {
                  // 如果成功，更新临时投资对象的last_profit_time
                  if (result.last_profit_time) {
                    tempInvestment.last_profit_time = result.last_profit_time;
                  } else {
                    // 如果没有返回last_profit_time，使用理论收益时间
                    tempInvestment.last_profit_time = missingTime;
                  }

                  // 更新临时投资对象的profit_count
                  tempInvestment.profit_count = (tempInvestment.profit_count || 0) + 1;

                  processedTimes.push({
                    time: dateUtils.formatDateTime(missingTime),
                    result: result,
                    delay_minutes: Math.floor(timeGap / 60000)
                  });
                } else {
                  failedTimes.push({
                    time: dateUtils.formatDateTime(missingTime),
                    message: result ? result.message : '收益发放失败'
                  });
                }
              } else {
                // 理论收益时间刚过或未到
                logger.info(`投资ID ${investment.id} 在时间点 ${dateUtils.formatDateTime(missingTime)} 的收益时间刚过或未到，跳过处理`);
                failedTimes.push({
                  time: dateUtils.formatDateTime(missingTime),
                  message: `理论收益时间刚过 ${Math.floor(timeGap / 1000)} 秒，等待至少 ${Math.floor(minDelay / 1000)} 秒后发放`
                });
              }
            } catch (error) {
              logger.error(`处理投资ID ${investment.id} 在时间点 ${dateUtils.formatDateTime(missingTime)} 的补偿收益失败:`, error);
              failedTimes.push({
                time: dateUtils.formatDateTime(missingTime),
                message: error.message
              });
            }
          }

          results.compensated++;
          results.details.push({
            investment_id: investment.id,
            missing_count: missingProfitTimes.length,
            processed_count: processedTimes.length,
            failed_count: failedTimes.length,
            message: `处理了 ${processedTimes.length} 个缺失的收益时间点，失败 ${failedTimes.length} 个`,
            processed_times: processedTimes,
            failed_times: failedTimes
          });
        } else {
          results.skipped++;
          results.details.push({
            investment_id: investment.id,
            message: '没有缺失的收益时间点'
          });
        }
      } catch (error) {
        logger.error(`处理投资ID ${investment.id} 的深度补偿检查失败:`, error);
        results.details.push({
          investment_id: investment.id,
          message: `深度补偿检查失败: ${error.message}`
        });
      }
    }

    logger.info(`深度补偿检查完成: 总计 ${results.total}, 补偿 ${results.compensated}, 跳过 ${results.skipped}`);
    return results;
  } catch (error) {
    logger.error('执行深度补偿检查失败:', error);
    return {
      success: false,
      message: '深度补偿检查失败: ' + error.message
    };
  }
};

module.exports = {
  runCompensationCheck,
  runDeepCompensationCheck,
  startCompensator,
  stopCompensator
};
