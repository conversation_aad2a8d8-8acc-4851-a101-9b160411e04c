// 尝试多种可能的路径导入数据库连接
let db;
try {
  db = require('../config/database');
} catch (error) {
  try {
    db = require('../models/db');
  } catch (innerError) {
    console.error('无法加载数据库模块:', error);
    console.error('尝试备用路径也失败:', innerError);
    // 创建一个模拟的数据库对象，防止代码崩溃
    db = {
      query: async () => [[], []],
      escape: (val) => `'${val}'`
    };
  }
}

// 尝试导入Attachment模型
let Attachment;
try {
  ({ Attachment } = require('../models'));
} catch (error) {
  console.error('无法加载Attachment模型:', error);
  // 创建一个模拟的Attachment对象
  Attachment = {};
}
const iconv = require('iconv-lite');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const sharp = require('sharp');
const ffmpeg = require('fluent-ffmpeg');
const { promisify } = require('util');
const statAsync = promisify(fs.stat);
const readdirAsync = promisify(fs.readdir);

// 上传目录
const UPLOAD_DIR = path.join(__dirname, '../uploads');
// 确保上传目录存在
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 按日期创建目录
    const today = new Date();
    const dateDir = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`;
    const uploadPath = path.join(UPLOAD_DIR, dateDir);

    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // 确保文件名正确编码
    const originalFilename = iconv.decode(Buffer.from(file.originalname, 'binary'), 'utf8');
    console.log('原始文件名(filename):', file.originalname);
    console.log('转换后文件名(filename):', originalFilename);

    // 生成唯一文件名，但保留原始文件名
    const fileExt = path.extname(originalFilename);
    const fileNameWithoutExt = path.basename(originalFilename, fileExt);
    // 使用原始文件名和UUID的组合，确保唯一性
    const uniqueId = uuidv4().substring(0, 8);
    const fileName = `${fileNameWithoutExt}_${uniqueId}${fileExt}`;
    cb(null, fileName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    // 图片
    'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    // 视频
    'video/mp4', 'video/webm', 'video/ogg', 'video/quicktime',
    // 文档
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    // 压缩包
    'application/zip', 'application/x-rar-compressed'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 创建上传中间件
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  }
});

// 获取文件类型
const getFileType = (mimetype) => {
  const type = mimetype.split('/')[0];
  const extension = mimetype.split('/')[1];

  if (type === 'image') {
    return extension;
  } else if (type === 'video') {
    return extension;
  } else if (mimetype === 'application/pdf') {
    return 'pdf';
  } else if (mimetype === 'application/msword' || mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return 'doc';
  } else if (mimetype === 'application/zip') {
    return 'zip';
  } else if (mimetype === 'application/x-rar-compressed') {
    return 'rar';
  } else {
    return 'other';
  }
};

// 处理图片信息
const processImageInfo = async (filePath) => {
  try {
    const metadata = await sharp(filePath).metadata();
    return {
      width: metadata.width,
      height: metadata.height,
      frameCount: metadata.pages || 1
    };
  } catch (error) {
    console.error('处理图片信息失败:', error);
    return {
      width: null,
      height: null,
      frameCount: null
    };
  }
};

// 处理视频信息
const processVideoInfo = (filePath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        console.error('处理视频信息失败:', err);
        resolve({
          width: null,
          height: null,
          frameCount: null
        });
      } else {
        const videoStream = metadata.streams.find(s => s.codec_type === 'video');
        resolve({
          width: videoStream ? videoStream.width : null,
          height: videoStream ? videoStream.height : null,
          frameCount: videoStream ? videoStream.nb_frames || null : null
        });
      }
    });
  });
};

// 获取附件列表
exports.getAttachments = async (req, res) => {
  try {
    console.log('获取附件列表 - 请求参数:', req.query);
    const { page = 1, pageSize = 10, category, filename, fileType, storageEngine, startDate, endDate } = req.query;

    // 记录详细的请求参数
    console.log('处理后的参数:', {
      page, pageSize, category, filename, fileType, storageEngine, startDate, endDate
    });

    // 构建查询条件
    let conditions = [];
    let params = [];

    if (category !== undefined) {
      conditions.push('category = ?');
      params.push(category);
    }

    if (filename) {
      conditions.push('(filename LIKE ? OR original_name LIKE ?)');
      params.push(`%${filename}%`, `%${filename}%`);
    }

    if (fileType) {
      // 处理文件类型筛选
      // 如果是通用类型（image, video, document），使用简单的LIKE条件
      if (fileType === 'image') {
        // 使用简单条件，避免复杂的OR语句
        conditions.push('file_type IN ("jpg", "jpeg", "png", "gif", "webp", "bmp", "svg")');
        // 不需要添加参数，因为值已经直接写在SQL中
      } else if (fileType === 'video') {
        conditions.push('file_type IN ("mp4", "webm", "avi", "mov", "flv")');
      } else if (fileType === 'document') {
        conditions.push('file_type IN ("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt")');
      } else {
        // 如果是具体的文件类型，使用精确匹配
        conditions.push('file_type = ?');
        params.push(fileType);
      }
    }

    if (storageEngine) {
      conditions.push('storage_engine = ?');
      params.push(storageEngine);
    }

    if (startDate) {
      conditions.push('created_at >= ?');
      params.push(startDate);
    }

    if (endDate) {
      conditions.push('created_at <= ?');
      params.push(endDate);
    }

    // 构建WHERE子句
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // 记录SQL查询
    const countSql = `SELECT COUNT(*) as total FROM attachments ${whereClause}`;
    console.log('计算总数SQL:', countSql);
    console.log('SQL参数:', params);

    // 声明变量在外部作用域
    let total = 0;
    let attachments = [];

    try {
      // 计算总数
      let countResult;
      try {
        [countResult] = await db.query(countSql, params);
        console.log('计算总数结果:', countResult);
      } catch (countError) {
        console.error('计算总数错误:', countError);
        // 如果计算总数失败，尝试不使用条件的简单查询
        console.log('尝试简单查询计算总数');
        [countResult] = await db.query('SELECT COUNT(*) as total FROM attachments');
        console.log('简单查询计算总数结果:', countResult);
      }

      total = countResult && countResult[0] ? countResult[0].total : 0;
      console.log('总数:', total);

      // 分页查询
      const offset = (page - 1) * pageSize;
      let querySql, queryParams;

      // 如果有文件类型筛选，使用特殊处理
      if (fileType) {
        if (fileType === 'image') {
          // 直接在SQL中使用IN，避免参数问题
          querySql = `SELECT * FROM attachments WHERE file_type IN ("jpg", "jpeg", "png", "gif", "webp", "bmp", "svg") ORDER BY id DESC LIMIT ${parseInt(pageSize)} OFFSET ${offset}`;
          queryParams = [];
        } else if (fileType === 'video') {
          querySql = `SELECT * FROM attachments WHERE file_type IN ("mp4", "webm", "avi", "mov", "flv") ORDER BY id DESC LIMIT ${parseInt(pageSize)} OFFSET ${offset}`;
          queryParams = [];
        } else if (fileType === 'document') {
          querySql = `SELECT * FROM attachments WHERE file_type IN ("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt") ORDER BY id DESC LIMIT ${parseInt(pageSize)} OFFSET ${offset}`;
          queryParams = [];
        } else {
          // 单一文件类型
          querySql = `SELECT * FROM attachments WHERE file_type = ? ORDER BY id DESC LIMIT ${parseInt(pageSize)} OFFSET ${offset}`;
          queryParams = [fileType];
        }
      } else {
        // 没有文件类型筛选，使用原始条件
        querySql = `SELECT * FROM attachments ${whereClause} ORDER BY id DESC LIMIT ${parseInt(pageSize)} OFFSET ${offset}`;
        queryParams = [...params];
      }

      console.log('查询SQL:', querySql);
      console.log('查询参数:', queryParams);

      try {
        const [queryResult] = await db.query(querySql, queryParams);
        attachments = queryResult || [];
        console.log('查询结果数量:', attachments.length);
      } catch (queryError) {
        console.error('查询错误:', queryError);
        // 如果查询失败，尝试不使用条件的简单查询
        console.log('尝试简单查询获取附件');
        const [fallbackResult] = await db.query(`SELECT * FROM attachments ORDER BY id DESC LIMIT ${parseInt(pageSize)} OFFSET ${offset}`);
        attachments = fallbackResult || [];
        console.log('简单查询结果数量:', attachments.length);
      }
    } catch (sqlError) {
      console.error('SQL执行错误:', sqlError);
      // 返回友好的错误信息，避免500错误
      return res.status(400).json({
        code: 400,
        message: '查询参数错误，请检查参数格式',
        error: sqlError.message
      });
    }

    // 如果没有结果，返回空数组
    if (!attachments || !Array.isArray(attachments)) {
      console.log('没有查询结果或结果不是数组，返回空列表');
      return res.json({
        code: 200,
        data: {
          total: 0,
          list: []
        }
      });
    }

    // 格式化数据
    let formattedAttachments = [];
    try {
      formattedAttachments = attachments.map(attachment => {
        try {
          // 格式化日期为 YYYY-MM-DD HH:MM:SS
          let formattedDate = '';
          try {
            const uploadDate = new Date(attachment.created_at || new Date());
            // 转换为本地时间
            const year = uploadDate.getFullYear();
            const month = String(uploadDate.getMonth() + 1).padStart(2, '0');
            const day = String(uploadDate.getDate()).padStart(2, '0');
            const hours = String(uploadDate.getHours()).padStart(2, '0');
            const minutes = String(uploadDate.getMinutes()).padStart(2, '0');
            const seconds = String(uploadDate.getSeconds()).padStart(2, '0');
            formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          } catch (dateError) {
            console.error('日期格式化错误:', dateError);
            formattedDate = new Date().toISOString();
          }

          // 安全地解析元数据
          let metadata = null;
          try {
            if (attachment.metadata) {
              metadata = typeof attachment.metadata === 'string'
                ? JSON.parse(attachment.metadata)
                : attachment.metadata;
            }
          } catch (metadataError) {
            console.error('元数据解析错误:', metadataError);
          }

          // 构建URL
          let url = attachment.url;
          if (!url && attachment.file_path) {
            try {
              url = `${req.protocol}://${req.get('host')}${attachment.file_path}`;
            } catch (urlError) {
              console.error('URL构建错误:', urlError);
              url = attachment.file_path || '';
            }
          }

          return {
            id: attachment.id || 0,
            category: attachment.category || '未归类',
            filename: attachment.filename || '',
            originalName: attachment.original_name || attachment.filename || '',
            filePath: attachment.file_path || '',
            fileSize: attachment.file_size || 0,
            width: attachment.width || null,
            height: attachment.height || null,
            fileType: attachment.file_type || '',
            frameCount: attachment.frame_count || null,
            mimeType: attachment.mime_type || '',
            metadata: metadata,
            storageEngine: attachment.storage_engine || 'local',
            uploadTime: formattedDate,
            url: url
          };
        } catch (itemError) {
          console.error('处理单个附件时出错:', itemError, attachment);
          // 返回一个基本的附件对象，避免整个映射失败
          return {
            id: attachment.id || 0,
            filename: attachment.filename || '未知文件',
            fileType: attachment.file_type || '',
            url: attachment.url || attachment.file_path || ''
          };
        }
      });
    } catch (mapError) {
      console.error('格式化附件列表时出错:', mapError);
      // 如果映射完全失败，返回原始数据
      formattedAttachments = attachments.map(a => ({ ...a }));
    }

    res.json({
      code: 200,
      data: {
        total,
        list: formattedAttachments
      }
    });
  } catch (error) {
    console.error('获取附件列表失败:', error);
    res.status(500).json({ code: 500, message: '获取附件列表失败' });
  }
};

// 获取附件详情
exports.getAttachmentById = async (req, res) => {
  try {
    const { id } = req.params;

    const [attachments] = await db.query(`SELECT * FROM attachments WHERE id = ${id}`);

    if (attachments.length === 0) {
      return res.status(404).json({ message: '附件不存在' });
    }

    const attachment = attachments[0];

    // 格式化数据
    // 格式化日期为 YYYY-MM-DD HH:MM:SS
    const uploadDate = new Date(attachment.created_at);
    // 转换为本地时间
    const year = uploadDate.getFullYear();
    const month = String(uploadDate.getMonth() + 1).padStart(2, '0');
    const day = String(uploadDate.getDate()).padStart(2, '0');
    const hours = String(uploadDate.getHours()).padStart(2, '0');
    const minutes = String(uploadDate.getMinutes()).padStart(2, '0');
    const seconds = String(uploadDate.getSeconds()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    const formattedAttachment = {
      id: attachment.id,
      category: attachment.category,
      filename: attachment.filename,
      originalName: attachment.original_name,
      filePath: attachment.file_path,
      fileSize: attachment.file_size,
      width: attachment.width,
      height: attachment.height,
      fileType: attachment.file_type,
      frameCount: attachment.frame_count,
      mimeType: attachment.mime_type,
      metadata: attachment.metadata ? JSON.parse(attachment.metadata) : null,
      storageEngine: attachment.storage_engine,
      uploadTime: formattedDate,
      url: attachment.url || `${req.protocol}://${req.get('host')}${attachment.file_path}`
    };

    res.json({
      code: 200,
      data: formattedAttachment
    });
  } catch (error) {
    console.error('获取附件详情失败:', error);
    res.status(500).json({ message: '获取附件详情失败' });
  }
};

// 上传附件
exports.uploadAttachment = async (req, res) => {
  try {
    // 使用multer中间件处理上传
    upload.single('file')(req, res, async (err) => {
      if (err) {
        console.error('文件上传失败:', err);
        return res.status(400).json({ message: err.message || '文件上传失败' });
      }

      if (!req.file) {
        return res.status(400).json({ message: '没有上传文件' });
      }

      const file = req.file;
      const { category = '未归类', metadata } = req.body;

      // 获取文件类型
      const fileType = getFileType(file.mimetype);

      // 处理文件信息
      let width = null;
      let height = null;
      let frameCount = null;

      if (file.mimetype.startsWith('image/')) {
        const imageInfo = await processImageInfo(file.path);
        width = imageInfo.width;
        height = imageInfo.height;
        frameCount = imageInfo.frameCount;
      } else if (file.mimetype.startsWith('video/')) {
        const videoInfo = await processVideoInfo(file.path);
        width = videoInfo.width;
        height = videoInfo.height;
        frameCount = videoInfo.frameCount;
      }

      // 相对路径
      const relativePath = file.path.replace(path.join(__dirname, '..'), '').replace(/\\/g, '/');

      // 构建URL
      const url = `${req.protocol}://${req.get('host')}${relativePath}`;

      // 确保文件名正确编码
      const originalFilename = iconv.decode(Buffer.from(file.originalname, 'binary'), 'utf8');
      console.log('原始文件名:', file.originalname);
      console.log('转换后文件名:', originalFilename);

      // 保存到数据库，使用字符串拼接但注意转义
      const escapedCategory = db.escape(category);
      const escapedFilename = db.escape(originalFilename);
      const escapedOriginalName = db.escape(originalFilename);
      const escapedRelativePath = db.escape(relativePath);
      const escapedFileSize = file.size;
      const escapedWidth = width !== null ? width : 'NULL';
      const escapedHeight = height !== null ? height : 'NULL';
      const escapedFrameCount = frameCount !== null ? frameCount : 'NULL';
      const escapedFileType = db.escape(fileType);
      const escapedMimeType = db.escape(file.mimetype);
      const escapedMetadata = metadata ? db.escape(JSON.stringify(metadata)) : 'NULL';
      const escapedStorageEngine = db.escape('local');
      const escapedUrl = db.escape(url);

      await db.query(
        `INSERT INTO attachments
        (category, filename, original_name, file_path, file_size, width, height, frame_count, file_type, mime_type, metadata, storage_engine, url)
        VALUES (${escapedCategory}, ${escapedFilename}, ${escapedOriginalName}, ${escapedRelativePath}, ${escapedFileSize}, ${escapedWidth}, ${escapedHeight}, ${escapedFrameCount}, ${escapedFileType}, ${escapedMimeType}, ${escapedMetadata}, ${escapedStorageEngine}, ${escapedUrl})`
      );

      // 获取新插入的附件
      const [attachments] = await db.query(`SELECT * FROM attachments WHERE id = LAST_INSERT_ID()`);

      if (attachments.length === 0) {
        return res.status(500).json({ message: '附件保存失败' });
      }

      const attachment = attachments[0];

      // 格式化数据
      // 格式化日期为 YYYY-MM-DD HH:MM:SS
      const uploadDate = new Date(attachment.created_at);
      // 转换为本地时间
      const year = uploadDate.getFullYear();
      const month = String(uploadDate.getMonth() + 1).padStart(2, '0');
      const day = String(uploadDate.getDate()).padStart(2, '0');
      const hours = String(uploadDate.getHours()).padStart(2, '0');
      const minutes = String(uploadDate.getMinutes()).padStart(2, '0');
      const seconds = String(uploadDate.getSeconds()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

      const formattedAttachment = {
        id: attachment.id,
        category: attachment.category,
        filename: attachment.filename,
        originalName: attachment.original_name,
        filePath: attachment.file_path,
        fileSize: attachment.file_size,
        width: attachment.width,
        height: attachment.height,
        fileType: attachment.file_type,
        frameCount: attachment.frame_count,
        mimeType: attachment.mime_type,
        metadata: attachment.metadata ? JSON.parse(attachment.metadata) : null,
        storageEngine: attachment.storage_engine,
        uploadTime: formattedDate,
        url: attachment.url || url
      };

      res.status(201).json({
        code: 201,
        data: formattedAttachment
      });
    });
  } catch (error) {
    console.error('上传附件失败:', error);
    res.status(500).json({ message: '上传附件失败' });
  }
};

// 更新附件信息
exports.updateAttachment = async (req, res) => {
  try {
    const { id } = req.params;
    const { category, filename, metadata } = req.body;

    // 检查附件是否存在
    const [attachments] = await db.query(`SELECT * FROM attachments WHERE id = ${id}`);

    if (attachments.length === 0) {
      return res.status(404).json({ message: '附件不存在' });
    }

    // 构建更新字段
    const updateFields = [];
    const updateParams = [];

    if (category !== undefined) {
      updateFields.push('category = ?');
      updateParams.push(category);
    }

    if (filename) {
      updateFields.push('filename = ?');
      updateParams.push(filename);
    }

    if (metadata !== undefined) {
      updateFields.push('metadata = ?');
      updateParams.push(metadata ? JSON.stringify(metadata) : null);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ message: '没有提供要更新的字段' });
    }

    // 执行更新
    await db.query(
      `UPDATE attachments SET ${updateFields.join(', ')} WHERE id = ${id}`,
      [...updateParams]
    );

    // 获取更新后的附件
    const [updatedAttachments] = await db.query(`SELECT * FROM attachments WHERE id = ${id}`);

    if (updatedAttachments.length === 0) {
      return res.status(500).json({ message: '附件更新失败' });
    }

    const attachment = updatedAttachments[0];

    // 格式化数据
    // 格式化日期为 YYYY-MM-DD HH:MM:SS
    const uploadDate = new Date(attachment.created_at);
    // 转换为本地时间
    const year = uploadDate.getFullYear();
    const month = String(uploadDate.getMonth() + 1).padStart(2, '0');
    const day = String(uploadDate.getDate()).padStart(2, '0');
    const hours = String(uploadDate.getHours()).padStart(2, '0');
    const minutes = String(uploadDate.getMinutes()).padStart(2, '0');
    const seconds = String(uploadDate.getSeconds()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    const formattedAttachment = {
      id: attachment.id,
      category: attachment.category,
      filename: attachment.filename,
      originalName: attachment.original_name,
      filePath: attachment.file_path,
      fileSize: attachment.file_size,
      width: attachment.width,
      height: attachment.height,
      fileType: attachment.file_type,
      frameCount: attachment.frame_count,
      mimeType: attachment.mime_type,
      metadata: attachment.metadata ? JSON.parse(attachment.metadata) : null,
      storageEngine: attachment.storage_engine,
      uploadTime: formattedDate,
      url: attachment.url || `${req.protocol}://${req.get('host')}${attachment.file_path}`
    };

    res.json({
      code: 200,
      data: formattedAttachment
    });
  } catch (error) {
    console.error('更新附件失败:', error);
    res.status(500).json({ message: '更新附件失败' });
  }
};

// 删除附件
exports.deleteAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取附件信息
    console.log('删除附件, ID:', id);
    const attachment = await Attachment.findByPk(id);

    if (!attachment) {
      console.log('附件不存在, ID:', id);
      return res.status(404).json({ code: 404, message: '附件不存在' });
    }

    console.log('找到附件:', attachment.toJSON());

    // 删除物理文件
    const filePath = path.join(__dirname, '..', attachment.file_path);
    console.log('物理文件路径:', filePath);

    if (fs.existsSync(filePath)) {
      console.log('删除物理文件');
      fs.unlinkSync(filePath);
    } else {
      console.log('物理文件不存在');
    }

    // 从数据库中删除
    console.log('从数据库中删除附件');
    await attachment.destroy();

    res.json({
      code: 200,
      message: '附件删除成功'
    });
  } catch (error) {
    console.error('删除附件失败:', error);
    res.status(500).json({ message: '删除附件失败' });
  }
};

// 批量删除附件
exports.batchDeleteAttachments = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: '无效的附件ID列表' });
    }

    // 获取附件信息
    console.log('批量删除附件, IDs:', ids);
    const attachments = await Attachment.findAll({
      where: {
        id: ids
      }
    });

    console.log('找到附件数量:', attachments.length);

    // 删除物理文件
    for (const attachment of attachments) {
      const filePath = path.join(__dirname, '..', attachment.file_path);
      console.log('删除物理文件:', filePath);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      } else {
        console.log('物理文件不存在:', filePath);
      }
    }

    // 从数据库中删除
    console.log('从数据库中删除附件');
    await Attachment.destroy({
      where: {
        id: ids
      }
    });

    res.json({
      code: 200,
      data: {
        message: '附件批量删除成功',
        count: attachments.length
      }
    });
  } catch (error) {
    console.error('批量删除附件失败:', error);
    res.status(500).json({ message: '批量删除附件失败' });
  }
};

// 扫描服务器附件
exports.scanServerAttachments = async (req, res) => {
  try {
    // 扫描上传目录
    const scanDirectory = async (dir) => {
      const files = await readdirAsync(dir);
      let newFiles = 0;

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await statAsync(filePath);

        if (stat.isDirectory()) {
          // 递归扫描子目录
          newFiles += await scanDirectory(filePath);
        } else {
          // 检查文件是否已在数据库中
          const relativePath = filePath.replace(path.join(__dirname, '..'), '').replace(/\\/g, '/');

          const [existingFiles] = await db.query(`SELECT id FROM attachments WHERE file_path = '${relativePath}'`);

          if (existingFiles.length === 0) {
            // 文件不在数据库中，添加它
            const mimetype = getMimeType(filePath);
            const fileType = getFileType(mimetype);

            // 处理文件信息
            let width = null;
            let height = null;
            let frameCount = null;

            if (mimetype.startsWith('image/')) {
              const imageInfo = await processImageInfo(filePath);
              width = imageInfo.width;
              height = imageInfo.height;
              frameCount = imageInfo.frameCount;
            } else if (mimetype.startsWith('video/')) {
              const videoInfo = await processVideoInfo(filePath);
              width = videoInfo.width;
              height = videoInfo.height;
              frameCount = videoInfo.frameCount;
            }

            // 构建URL
            const url = `${req.protocol}://${req.get('host')}${relativePath}`;

            // 保存到数据库
            await db.query(
              `INSERT INTO attachments
              (category, filename, original_name, file_path, file_size, width, height, frame_count, file_type, mime_type, metadata, storage_engine, url)
              VALUES ('系统扫描', '${path.basename(filePath)}', '${path.basename(filePath)}', '${relativePath}', ${stat.size}, ${width || 'NULL'}, ${height || 'NULL'}, ${frameCount || 'NULL'}, '${fileType}', '${mimetype}', NULL, 'local', '${url}')`
            );

            newFiles++;
          }
        }
      }

      return newFiles;
    };

    // 根据文件扩展名获取MIME类型
    const getMimeType = (filePath) => {
      const ext = path.extname(filePath).toLowerCase();

      const mimeTypes = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml',
        '.mp4': 'video/mp4',
        '.webm': 'video/webm',
        '.ogg': 'video/ogg',
        '.mov': 'video/quicktime',
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.zip': 'application/zip',
        '.rar': 'application/x-rar-compressed'
      };

      return mimeTypes[ext] || 'application/octet-stream';
    };

    // 开始扫描
    const newFilesCount = await scanDirectory(UPLOAD_DIR);

    res.json({
      code: 200,
      data: {
        message: '扫描完成',
        newFiles: newFilesCount
      }
    });
  } catch (error) {
    console.error('扫描服务器附件失败:', error);
    res.status(500).json({ message: '扫描服务器附件失败' });
  }
};
