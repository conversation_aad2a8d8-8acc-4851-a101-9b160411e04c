'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 修改transactions表的type列，添加新的交易类型
    await queryInterface.sequelize.query(`
      ALTER TABLE transactions 
      MODIFY COLUMN type ENUM('deposit', 'withdrawal', 'investment', 'investment_gift', 'investment_purchase', 'profit', 'commission', 'bonus', 'deduction') 
      NOT NULL 
      COMMENT '交易类型：deposit=充值, withdrawal=提现, investment=投资, investment_gift=赠送投资, investment_purchase=购买投资, profit=收益, commission=佣金, bonus=赠金, deduction=扣除';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // 回滚修改，恢复原来的type列定义
    await queryInterface.sequelize.query(`
      ALTER TABLE transactions 
      MODIFY COLUMN type ENUM('deposit', 'withdrawal', 'investment', 'profit', 'commission', 'bonus', 'deduction') 
      NOT NULL 
      COMMENT '交易类型：deposit=充值, withdrawal=提现, investment=投资, profit=收益, commission=佣金, bonus=赠金, deduction=扣除';
    `);
  }
};
