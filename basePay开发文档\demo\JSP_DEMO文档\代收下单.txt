  {
    <%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="cn.test.utils.*"%>
<%@ page import="java.io.*"%>
<%@ page import="java.util.*"%>
<%

    String reqUrl = "https://payment.weglobalpayment.com/pay/web";

    request.setCharacterEncoding("UTF-8");
   String version = (String) request.getParameter("version");
    String mch_id = (String) request.getParameter("mch_id");
    String merchant_key = "";//商户秘钥必填
    String notify_url = (String) request.getParameter("notify_url");
    String mch_order_no = (String) request.getParameter("mch_order_no");
    String pay_type = (String) request.getParameter("pay_type");
    String trade_amount = (String) request.getParameter("trade_amount");
    String order_date = (String) request.getParameter("order_date");
    String goods_name = (String) request.getParameter("goods_name");
    String sign_type = (String) request.getParameter("sign_type");

    //可选的
    String bank_code = (String) request.getParameter("bank_code");//网银必填，其他一定不填 【商户后台下载银行编码】
    String mch_return_msg = (String) request.getParameter("mch_return_msg");
    String page_url = (String) request.getParameter("page_url");//同步返回地址

    Map<String, String> params = new TreeMap<String, String>();
    params.put("version", version);
    params.put("goods_name", goods_name);
    params.put("mch_id", mch_id);
    params.put("mch_order_no", mch_order_no);
    params.put("notify_url", notify_url);
    params.put("order_date", order_date);
    params.put("pay_type", pay_type);
    params.put("trade_amount", trade_amount);

    //可选的
    /* params.put("page_url", page_url); */
    /* params.put("mch_return_msg", mch_return_msg); */
    /* params.put("bank_code", bank_code); */


    String signInfo = SignUtil.sortData(params);
    String sign = SignAPI.sign(signInfo, merchant_key); // 签名   signInfo签名参数排序，  merchant_key商户私钥
    params.put("sign", sign);
    params.put("sign_type", sign_type);//不参与签名

    System.out.println("请求参数：" + params.toString());
    String result = HttpClientUtil.doPost(reqUrl, params, "utf-8");                         

    System.out.println("签名参数排序：" + signInfo.length() + " --> " + signInfo);
    System.out.println("sign值：" + sign.length() + " --> " + sign);

    System.out.println("result值：" + result);
    System.out.println(
            "---------------------------------------------------------------------------------------------------------------------------------------------");

    PrintWriter pw = response.getWriter();
    pw.write(result); // 返回result数据给请求页面
    pw.flush();
    pw.close();
%>

    }
  }