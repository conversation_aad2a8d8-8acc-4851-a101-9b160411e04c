/**
 * 统计数据更新服务
 * 用于在关键操作完成后自动触发统计数据更新
 */
const statisticsService = require('./statisticsService');
const logger = require('../utils/logger');

/**
 * 触发今日统计数据更新
 * @returns {Promise<Object>} 更新结果
 */
async function triggerTodayStatsUpdate() {
  try {
    logger.info('触发今日统计数据更新');
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    
    const results = await statisticsService.updateDailyStatistics(formattedDate);
    logger.info(`今日(${formattedDate})统计数据更新完成`);

    return {
      success: true,
      message: `今日(${formattedDate})统计数据更新成功`
    };
  } catch (error) {
    logger.error('触发今日统计数据更新失败:', error);
    return {
      success: false,
      message: '触发今日统计数据更新失败: ' + error.message
    };
  }
}

/**
 * 触发总统计数据更新
 * @returns {Promise<Object>} 更新结果
 */
async function triggerTotalStatsUpdate() {
  try {
    logger.info('触发总统计数据更新');
    
    // 创建一个新的事务
    const sequelize = require('../config/database');
    const transaction = await sequelize.transaction();
    
    try {
      const results = await statisticsService.updateTotalStatistics(transaction);
      await transaction.commit();

      logger.info('总统计数据更新完成');

      return {
        success: true,
        message: '总统计数据更新成功'
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    logger.error('触发总统计数据更新失败:', error);
    return {
      success: false,
      message: '触发总统计数据更新失败: ' + error.message
    };
  }
}

/**
 * 触发完整统计数据更新（今日和总计）
 * @returns {Promise<Object>} 更新结果
 */
async function triggerFullStatsUpdate() {
  try {
    logger.info('触发完整统计数据更新');
    
    // 先更新今日统计
    const todayResult = await triggerTodayStatsUpdate();
    
    // 再更新总统计
    const totalResult = await triggerTotalStatsUpdate();
    
    return {
      success: todayResult.success && totalResult.success,
      message: '完整统计数据更新完成',
      today: todayResult,
      total: totalResult
    };
  } catch (error) {
    logger.error('触发完整统计数据更新失败:', error);
    return {
      success: false,
      message: '触发完整统计数据更新失败: ' + error.message
    };
  }
}

module.exports = {
  triggerTodayStatsUpdate,
  triggerTotalStatsUpdate,
  triggerFullStatsUpdate
};
