import request from '@/utils/request'

/**
 * 获取项目列表
 * @param params 查询参数
 */
export function getProjects(params?: any) {
  return request({
    url: '/api/admin/projects',
    method: 'get',
    params
  })
}

/**
 * 获取项目详情
 * @param id 项目ID
 */
export function getProject(id: number) {
  return request({
    url: `/api/admin/projects/${id}`,
    method: 'get'
  })
}

/**
 * 创建项目
 * @param data 项目数据
 */
export function createProject(data: any) {
  return request({
    url: '/api/admin/projects',
    method: 'post',
    data
  })
}

/**
 * 更新项目
 * @param id 项目ID
 * @param data 项目数据
 */
export function updateProject(id: number, data: any) {
  return request({
    url: `/api/admin/projects/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除项目
 * @param id 项目ID
 */
export function deleteProject(id: number) {
  return request({
    url: `/api/admin/projects/${id}`,
    method: 'delete'
  })
}

/**
 * 更新项目排序
 * @param id 项目ID
 * @param sort_order 排序值
 */
export function updateProjectSortOrder(id: number, sort_order: number) {
  return request({
    url: `/api/admin/projects/${id}/sort`,
    method: 'put',
    data: { sort_order }
  })
}

/**
 * 批量更新项目排序
 * @param updates 排序更新数据数组
 */
export function updateProjectSortOrders(updates: Array<{id: number, sort_order: number}>) {
  return request({
    url: '/api/admin/projects/sort/batch',
    method: 'put',
    data: updates
  })
}

/**
 * 获取项目订单列表
 * @param id 项目ID
 * @param params 查询参数
 */
export function getProjectOrders(id: number, params?: any) {
  return request({
    url: `/api/admin/projects/${id}/orders`,
    method: 'get',
    params
  })
}
