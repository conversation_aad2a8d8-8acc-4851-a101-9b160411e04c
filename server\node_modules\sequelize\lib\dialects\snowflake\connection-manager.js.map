{"version": 3, "sources": ["../../../src/dialects/snowflake/connection-manager.js"], "sourcesContent": ["'use strict';\n\nconst AbstractConnectionManager = require('../abstract/connection-manager');\nconst SequelizeErrors = require('../../errors');\nconst { logger } = require('../../utils/logger');\nconst DataTypes = require('../../data-types').snowflake;\nconst debug = logger.debugContext('connection:snowflake');\nconst parserStore = require('../parserStore')('snowflake');\n\n/**\n * Snowflake Connection Manager\n *\n * Get connections, validate and disconnect them.\n *\n * @private\n */\nclass ConnectionManager extends AbstractConnectionManager {\n  constructor(dialect, sequelize) {\n    sequelize.config.port = sequelize.config.port || 3306;\n    super(dialect, sequelize);\n    this.lib = this._loadDialectModule('snowflake-sdk');\n    this.refreshTypeParser(DataTypes);\n  }\n\n  _refreshTypeParser(dataType) {\n    parserStore.refresh(dataType);\n  }\n\n  _clearTypeParser() {\n    parserStore.clear();\n  }\n\n  static _typecast(field, next) {\n    if (parserStore.get(field.type)) {\n      return parserStore.get(field.type)(field, this.sequelize.options, next);\n    }\n    return next();\n  }\n\n  /**\n   * Connect with a snowflake database based on config, Handle any errors in connection\n   * Set the pool handlers on connection.error\n   * Also set proper timezone once connection is connected.\n   *\n   * @param {object} config\n   * @returns {Promise<Connection>}\n   * @private\n   */\n  async connect(config) {\n    const connectionConfig = {\n      account: config.host,\n      username: config.username,\n      password: config.password,\n      database: config.database,\n      warehouse: config.warehouse,\n      role: config.role,\n      /*\n      flags: '-FOUND_ROWS',\n      timezone: this.sequelize.options.timezone,\n      typeCast: ConnectionManager._typecast.bind(this),\n      bigNumberStrings: false,\n      supportBigNumbers: true,\n      */\n      ...config.dialectOptions\n    };\n\n    try {\n\n      const connection = await new Promise((resolve, reject) => {\n        this.lib.createConnection(connectionConfig).connect((err, conn) => {\n          if (err) {\n            console.log(err);\n            reject(err);\n          } else {\n            resolve(conn);\n          }\n        });\n      });\n\n      debug('connection acquired');\n\n      if (!this.sequelize.config.keepDefaultTimezone) {\n        // default value is '+00:00', put a quick workaround for it.\n        const tzOffset = this.sequelize.options.timezone === '+00:00' ? 'Etc/UTC' : this.sequelize.options.timezone;\n        const isNamedTzOffset = /\\//.test(tzOffset);\n        if ( isNamedTzOffset ) {\n          await new Promise((resolve, reject) => {\n            connection.execute({\n              sqlText: `ALTER SESSION SET timezone = '${tzOffset}'`,\n              complete(err) {\n                if (err) {\n                  console.log(err);\n                  reject(err);\n                } else {\n                  resolve();\n                }\n              }\n            });\n          });\n        } else {\n          throw Error('only support time zone name for snowflake!');\n        }\n      }\n\n      return connection;\n    } catch (err) {\n      switch (err.code) {\n        case 'ECONNREFUSED':\n          throw new SequelizeErrors.ConnectionRefusedError(err);\n        case 'ER_ACCESS_DENIED_ERROR':\n          throw new SequelizeErrors.AccessDeniedError(err);\n        case 'ENOTFOUND':\n          throw new SequelizeErrors.HostNotFoundError(err);\n        case 'EHOSTUNREACH':\n          throw new SequelizeErrors.HostNotReachableError(err);\n        case 'EINVAL':\n          throw new SequelizeErrors.InvalidConnectionError(err);\n        default:\n          throw new SequelizeErrors.ConnectionError(err);\n      }\n    }\n  }\n\n  async disconnect(connection) {\n    // Don't disconnect connections with CLOSED state\n    if (!connection.isUp()) {\n      debug('connection tried to disconnect but was already at CLOSED state');\n      return;\n    }\n\n    return new Promise((resolve, reject) => {\n      connection.destroy(err => {\n        if (err) {\n          console.error(`Unable to disconnect: ${err.message}`);\n          reject(err);\n        } else {\n          console.log(`Disconnected connection with id: ${connection.getId()}`);\n          resolve(connection.getId());\n        }\n      });\n    });\n  }\n\n  validate(connection) {\n    return connection.isUp();\n  }\n}\n\nmodule.exports = ConnectionManager;\nmodule.exports.ConnectionManager = ConnectionManager;\nmodule.exports.default = ConnectionManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,4BAA4B,QAAQ;AAC1C,MAAM,kBAAkB,QAAQ;AAChC,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,QAAQ,OAAO,aAAa;AAClC,MAAM,cAAc,QAAQ,kBAAkB;AAS9C,gCAAgC,0BAA0B;AAAA,EACxD,YAAY,SAAS,WAAW;AAC9B,cAAU,OAAO,OAAO,UAAU,OAAO,QAAQ;AACjD,UAAM,SAAS;AACf,SAAK,MAAM,KAAK,mBAAmB;AACnC,SAAK,kBAAkB;AAAA;AAAA,EAGzB,mBAAmB,UAAU;AAC3B,gBAAY,QAAQ;AAAA;AAAA,EAGtB,mBAAmB;AACjB,gBAAY;AAAA;AAAA,SAGP,UAAU,OAAO,MAAM;AAC5B,QAAI,YAAY,IAAI,MAAM,OAAO;AAC/B,aAAO,YAAY,IAAI,MAAM,MAAM,OAAO,KAAK,UAAU,SAAS;AAAA;AAEpE,WAAO;AAAA;AAAA,QAYH,QAAQ,QAAQ;AACpB,UAAM,mBAAmB;AAAA,MACvB,SAAS,OAAO;AAAA,MAChB,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,MACjB,WAAW,OAAO;AAAA,MAClB,MAAM,OAAO;AAAA,OAQV,OAAO;AAGZ,QAAI;AAEF,YAAM,aAAa,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,aAAK,IAAI,iBAAiB,kBAAkB,QAAQ,CAAC,KAAK,SAAS;AACjE,cAAI,KAAK;AACP,oBAAQ,IAAI;AACZ,mBAAO;AAAA,iBACF;AACL,oBAAQ;AAAA;AAAA;AAAA;AAKd,YAAM;AAEN,UAAI,CAAC,KAAK,UAAU,OAAO,qBAAqB;AAE9C,cAAM,WAAW,KAAK,UAAU,QAAQ,aAAa,WAAW,YAAY,KAAK,UAAU,QAAQ;AACnG,cAAM,kBAAkB,KAAK,KAAK;AAClC,YAAK,iBAAkB;AACrB,gBAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrC,uBAAW,QAAQ;AAAA,cACjB,SAAS,iCAAiC;AAAA,cAC1C,SAAS,KAAK;AACZ,oBAAI,KAAK;AACP,0BAAQ,IAAI;AACZ,yBAAO;AAAA,uBACF;AACL;AAAA;AAAA;AAAA;AAAA;AAAA,eAKH;AACL,gBAAM,MAAM;AAAA;AAAA;AAIhB,aAAO;AAAA,aACA,KAAP;AACA,cAAQ,IAAI;AAAA,aACL;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA,aAC9C;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AACH,gBAAM,IAAI,gBAAgB,sBAAsB;AAAA,aAC7C;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA;AAEjD,gBAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAAA;AAAA;AAAA,QAK5C,WAAW,YAAY;AAE3B,QAAI,CAAC,WAAW,QAAQ;AACtB,YAAM;AACN;AAAA;AAGF,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,iBAAW,QAAQ,SAAO;AACxB,YAAI,KAAK;AACP,kBAAQ,MAAM,yBAAyB,IAAI;AAC3C,iBAAO;AAAA,eACF;AACL,kBAAQ,IAAI,oCAAoC,WAAW;AAC3D,kBAAQ,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,SAAS,YAAY;AACnB,WAAO,WAAW;AAAA;AAAA;AAItB,OAAO,UAAU;AACjB,OAAO,QAAQ,oBAAoB;AACnC,OAAO,QAAQ,UAAU;", "names": []}