{"version": 3, "file": "no-unnecessary-qualifier.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-qualifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,wEAAsE;AACtE,sDAAwC;AACxC,+CAAiC;AAEjC,kCAAwD;AAExD,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,0BAA0B;IAChC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,oBAAoB,EAClB,0DAA0D;SAC7D;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,iBAAiB,GAAc,EAAE,CAAC;QACxC,IAAI,gCAAgC,GAAyB,IAAI,CAAC;QAClE,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;QAC7D,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C,SAAS,mBAAmB,CAC1B,MAAiB,EACjB,OAAuB;YAEvB,OAAO,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC1D,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAClC,CAAC,CAAC,IAAI,CAAC;QACX,CAAC;QAED,SAAS,wBAAwB,CAAC,MAAiB;YACjD,MAAM,kBAAkB,GAAG,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC;YAE1D,IACE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC7B,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,CAC1C,EACD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEnD,OAAO,KAAK,IAAI,IAAI,IAAI,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAa,EACb,KAAqB,EACrB,IAAY;YAEZ,MAAM,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAErD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,SAAS,eAAe,CAAC,QAAmB,EAAE,OAAkB;YAC9D,OAAO,QAAQ,KAAK,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;QAED,SAAS,sBAAsB,CAC7B,SAA0D,EAC1D,IAAyB;YAEzB,MAAM,eAAe,GAAG,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAEhE,IACE,eAAe,KAAK,SAAS;gBAC7B,CAAC,wBAAwB,CAAC,eAAe,CAAC,EAC1C,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,cAAc,GAAG,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAE1D,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBACjC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,mEAAmE;YACnE,MAAM,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACzD,MAAM,SAAS,GAAG,gBAAgB,CAChC,WAAW,EACX,cAAc,CAAC,KAAK,EACpB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CACzB,CAAC;YACF,OAAO,CACL,SAAS,KAAK,SAAS,IAAI,eAAe,CAAC,cAAc,EAAE,SAAS,CAAC,CACtE,CAAC;QACJ,CAAC;QAED,SAAS,oBAAoB,CAC3B,IAAmB,EACnB,SAA0D,EAC1D,IAAyB;YAEzB,0FAA0F;YAC1F,IACE,CAAC,gCAAgC;gBACjC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,EACvC,CAAC;gBACD,gCAAgC,GAAG,IAAI,CAAC;gBACxC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE;wBACJ,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;qBAC/B;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,gBAAgB,CACvB,IAGgC;YAEhC,iBAAiB,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,eAAe;YACtB,iBAAiB,CAAC,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,SAAS,+BAA+B,CAAC,IAAmB;YAC1D,IAAI,IAAI,KAAK,gCAAgC,EAAE,CAAC;gBAC9C,gCAAgC,GAAG,IAAI,CAAC;YAC1C,CAAC;QACH,CAAC;QAED,SAAS,0BAA0B,CACjC,IAAmB;YAEnB,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACzE,CAAC;QAED,SAAS,sBAAsB,CAC7B,IAAmB;YAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACvC,CAAC,0BAA0B,CAAC,IAAI,CAAC;oBAC/B,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CACvC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,qCAAqC,CACnC,IAA4B;gBAE5B,gBAAgB,CAAC,IAAI,CAAC,MAAsC,CAAC,CAAC;YAChE,CAAC;YACD,iBAAiB,EAAE,gBAAgB;YACnC,gEAAgE,EAC9D,gBAAgB;YAClB,8DAA8D,EAC5D,gBAAgB;YAClB,0BAA0B,EAAE,eAAe;YAC3C,wBAAwB,EAAE,eAAe;YACzC,qEAAqE,EACnE,eAAe;YACjB,mEAAmE,EACjE,eAAe;YACjB,eAAe,CAAC,IAA8B;gBAC5C,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,CAAC;YACD,kCAAkC,EAAE,UAClC,IAA+B;gBAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAA+B,CAAC;gBACtD,IAAI,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACxC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;YACD,sBAAsB,EAAE,+BAA+B;YACvD,uBAAuB,EAAE,+BAA+B;SACzD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}