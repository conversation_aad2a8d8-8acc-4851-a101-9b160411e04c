/**
 * 数据库更新脚本
 * 执行SQL文件，添加理论收益时间字段和唯一索引
 */
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateDatabase() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'MySQL3352~!',
    database: process.env.DB_NAME || 'fox_db'
  });

  try {
    console.log('开始执行数据库更新...');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'add_theoretical_profit_time.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // 分割SQL语句
    const sqlStatements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);

    // 执行每条SQL语句
    for (const stmt of sqlStatements) {
      console.log(`执行SQL: ${stmt}`);
      await connection.query(stmt);
    }

    console.log('数据库更新成功');
    process.exit(0);
  } catch (error) {
    console.error('数据库更新失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

updateDatabase();
