const { Bank, BankCard } = require('../models');
const sequelize = require('../config/database');

const migrateBankCardData = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('开始迁移银行卡数据...');
    
    // 获取所有银行
    const banks = await Bank.findAll();
    
    if (banks.length === 0) {
      console.error('没有找到银行数据，请先运行create_bank_tables.sql脚本');
      await transaction.rollback();
      return;
    }
    
    console.log(`找到${banks.length}家银行`);
    
    // 获取所有银行卡
    const bankCards = await BankCard.findAll({
      attributes: ['id', 'bank_name']
    });
    
    console.log(`找到${bankCards.length}张银行卡需要迁移`);
    
    // 为每个银行卡设置bank_id
    let matchedCount = 0;
    let unmatchedCount = 0;
    
    for (const card of bankCards) {
      // 查找匹配的银行
      const matchedBank = banks.find(bank => 
        bank.name.toLowerCase() === card.bank_name.toLowerCase()
      );
      
      if (matchedBank) {
        await card.update({
          bank_id: matchedBank.id
        }, { transaction });
        matchedCount++;
      } else {
        // 如果找不到匹配的银行，使用"其他银行"
        const otherBank = banks.find(bank => bank.name === '其他银行');
        
        if (otherBank) {
          await card.update({
            bank_id: otherBank.id
          }, { transaction });
          matchedCount++;
        } else {
          console.warn(`无法为银行卡ID ${card.id} (${card.bank_name}) 找到匹配的银行`);
          unmatchedCount++;
        }
      }
    }
    
    await transaction.commit();
    console.log(`银行卡数据迁移完成：${matchedCount}张银行卡已更新，${unmatchedCount}张银行卡未匹配`);
  } catch (error) {
    await transaction.rollback();
    console.error('银行卡数据迁移失败:', error);
  }
};

// 执行迁移
migrateBankCardData();
