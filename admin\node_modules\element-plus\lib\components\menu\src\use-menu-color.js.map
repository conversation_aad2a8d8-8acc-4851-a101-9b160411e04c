{"version": 3, "file": "use-menu-color.js", "sources": ["../../../../../../packages/components/menu/src/use-menu-color.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { TinyColor } from '@ctrl/tinycolor'\n\nimport type { MenuProps } from './menu'\n\nexport default function useMenuColor(props: MenuProps) {\n  const menuBarColor = computed(() => {\n    const color = props.backgroundColor\n    return color ? new TinyColor(color).shade(20).toString() : ''\n  })\n  return menuBarColor\n}\n"], "names": ["computed", "TinyColor"], "mappings": ";;;;;;;AAEe,SAAS,YAAY,CAAC,KAAK,EAAE;AAC5C,EAAE,MAAM,YAAY,GAAGA,YAAQ,CAAC,MAAM;AACtC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;AACxC,IAAI,OAAO,KAAK,GAAG,IAAIC,mBAAS,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;AAClE,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,YAAY,CAAC;AACtB;;;;"}