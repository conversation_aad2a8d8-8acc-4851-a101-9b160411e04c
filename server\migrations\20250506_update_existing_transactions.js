'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 获取所有交易记录
    const transactions = await queryInterface.sequelize.query(
      'SELECT id, amount, balance FROM transactions ORDER BY id ASC',
      { type: Sequelize.QueryTypes.SELECT }
    );

    // 更新每条交易记录的交易前余额
    for (const transaction of transactions) {
      const beforeBalance = parseFloat(transaction.balance) - parseFloat(transaction.amount);
      await queryInterface.sequelize.query(
        'UPDATE transactions SET before_balance = ? WHERE id = ?',
        {
          replacements: [beforeBalance, transaction.id],
          type: Sequelize.QueryTypes.UPDATE
        }
      );
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 不需要回滚操作，因为这只是数据更新
  }
};
