{"version": 3, "file": "cascader2.mjs", "sources": ["../../../../../../packages/components/cascader/src/cascader.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"tooltipRef\"\n    :visible=\"popperVisible\"\n    :teleported=\"teleported\"\n    :popper-class=\"[nsCascader.e('dropdown'), popperClass]\"\n    :popper-options=\"popperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :stop-popper-mouse-event=\"false\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :transition=\"`${nsCascader.namespace.value}-zoom-in-top`\"\n    effect=\"light\"\n    pure\n    :persistent=\"persistent\"\n    @hide=\"hideSuggestionPanel\"\n  >\n    <template #default>\n      <div\n        v-clickoutside:[contentRef]=\"() => togglePopperVisible(false)\"\n        :class=\"cascaderKls\"\n        :style=\"cascaderStyle\"\n        @click=\"() => togglePopperVisible(readonly ? undefined : true)\"\n        @keydown=\"handleKeyDown\"\n        @mouseenter=\"inputHover = true\"\n        @mouseleave=\"inputHover = false\"\n      >\n        <el-input\n          ref=\"input\"\n          v-model=\"inputValue\"\n          :placeholder=\"currentPlaceholder\"\n          :readonly=\"readonly\"\n          :disabled=\"isDisabled\"\n          :validate-event=\"false\"\n          :size=\"realSize\"\n          :class=\"inputClass\"\n          :tabindex=\"multiple && filterable && !isDisabled ? -1 : undefined\"\n          @compositionstart=\"handleComposition\"\n          @compositionupdate=\"handleComposition\"\n          @compositionend=\"handleComposition\"\n          @focus=\"handleFocus\"\n          @blur=\"handleBlur\"\n          @input=\"handleInput\"\n        >\n          <template v-if=\"$slots.prefix\" #prefix>\n            <slot name=\"prefix\" />\n          </template>\n          <template #suffix>\n            <el-icon\n              v-if=\"clearBtnVisible\"\n              key=\"clear\"\n              :class=\"[nsInput.e('icon'), 'icon-circle-close']\"\n              @click.stop=\"handleClear\"\n            >\n              <circle-close />\n            </el-icon>\n            <el-icon\n              v-else\n              key=\"arrow-down\"\n              :class=\"cascaderIconKls\"\n              @click.stop=\"togglePopperVisible()\"\n            >\n              <arrow-down />\n            </el-icon>\n          </template>\n        </el-input>\n\n        <div\n          v-if=\"multiple\"\n          ref=\"tagWrapper\"\n          :class=\"[\n            nsCascader.e('tags'),\n            nsCascader.is('validate', Boolean(validateState)),\n          ]\"\n        >\n          <el-tag\n            v-for=\"tag in presentTags\"\n            :key=\"tag.key\"\n            :type=\"tagType\"\n            :size=\"tagSize\"\n            :effect=\"tagEffect\"\n            :hit=\"tag.hitState\"\n            :closable=\"tag.closable\"\n            disable-transitions\n            @close=\"deleteTag(tag)\"\n          >\n            <template v-if=\"tag.isCollapseTag === false\">\n              <span>{{ tag.text }}</span>\n            </template>\n            <template v-else>\n              <el-tooltip\n                :disabled=\"popperVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                placement=\"bottom\"\n                effect=\"light\"\n              >\n                <template #default>\n                  <span>{{ tag.text }}</span>\n                </template>\n                <template #content>\n                  <div :class=\"nsCascader.e('collapse-tags')\">\n                    <div\n                      v-for=\"(tag2, idx) in allPresentTags.slice(\n                        maxCollapseTags\n                      )\"\n                      :key=\"idx\"\n                      :class=\"nsCascader.e('collapse-tag')\"\n                    >\n                      <el-tag\n                        :key=\"tag2.key\"\n                        class=\"in-tooltip\"\n                        :type=\"tagType\"\n                        :size=\"tagSize\"\n                        :effect=\"tagEffect\"\n                        :hit=\"tag2.hitState\"\n                        :closable=\"tag2.closable\"\n                        disable-transitions\n                        @close=\"deleteTag(tag2)\"\n                      >\n                        <span>{{ tag2.text }}</span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </template>\n          </el-tag>\n          <input\n            v-if=\"filterable && !isDisabled\"\n            v-model=\"searchInputValue\"\n            type=\"text\"\n            :class=\"nsCascader.e('search-input')\"\n            :placeholder=\"presentText ? '' : inputPlaceholder\"\n            @input=\"(e) => handleInput(searchInputValue, e as KeyboardEvent)\"\n            @click.stop=\"togglePopperVisible(true)\"\n            @keydown.delete=\"handleDelete\"\n            @compositionstart=\"handleComposition\"\n            @compositionupdate=\"handleComposition\"\n            @compositionend=\"handleComposition\"\n            @focus=\"handleFocus\"\n            @blur=\"handleBlur\"\n          />\n        </div>\n      </div>\n    </template>\n\n    <template #content>\n      <el-cascader-panel\n        v-show=\"!filtering\"\n        ref=\"cascaderPanelRef\"\n        v-model=\"checkedValue\"\n        :options=\"options\"\n        :props=\"props.props\"\n        :border=\"false\"\n        :render-label=\"$slots.default\"\n        @expand-change=\"handleExpandChange\"\n        @close=\"$nextTick(() => togglePopperVisible(false))\"\n      >\n        <template #empty>\n          <slot name=\"empty\" />\n        </template>\n      </el-cascader-panel>\n      <el-scrollbar\n        v-if=\"filterable\"\n        v-show=\"filtering\"\n        ref=\"suggestionPanel\"\n        tag=\"ul\"\n        :class=\"nsCascader.e('suggestion-panel')\"\n        :view-class=\"nsCascader.e('suggestion-list')\"\n        @keydown=\"handleSuggestionKeyDown\"\n      >\n        <template v-if=\"suggestions.length\">\n          <li\n            v-for=\"item in suggestions\"\n            :key=\"item.uid\"\n            :class=\"[\n              nsCascader.e('suggestion-item'),\n              nsCascader.is('checked', item.checked),\n            ]\"\n            :tabindex=\"-1\"\n            @click=\"handleSuggestionClick(item)\"\n          >\n            <slot name=\"suggestion-item\" :item=\"item\">\n              <span>{{ item.text }}</span>\n              <el-icon v-if=\"item.checked\">\n                <check />\n              </el-icon>\n            </slot>\n          </li>\n        </template>\n        <slot v-else name=\"empty\">\n          <li :class=\"nsCascader.e('empty-text')\">\n            {{ t('el.cascader.noMatch') }}\n          </li>\n        </slot>\n      </el-scrollbar>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, useAttrs, watch } from 'vue'\nimport { cloneDeep, debounce } from 'lodash-unified'\nimport { useCssVar, useResizeObserver } from '@vueuse/core'\nimport {\n  debugWarn,\n  focusNode,\n  getSibling,\n  isClient,\n  isPromise,\n} from '@element-plus/utils'\nimport ElCascaderPanel from '@element-plus/components/cascader-panel'\nimport ElInput from '@element-plus/components/input'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport { ClickOutside as vClickoutside } from '@element-plus/directives'\nimport {\n  useComposition,\n  useEmptyValues,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { ArrowDown, Check, CircleClose } from '@element-plus/icons-vue'\nimport { cascaderEmits, cascaderProps } from './cascader'\n\nimport type { Options } from '@element-plus/components/popper'\nimport type { ComputedRef, Ref, StyleValue } from 'vue'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type {\n  CascaderNode,\n  CascaderPanelInstance,\n  CascaderValue,\n  Tag,\n} from '@element-plus/components/cascader-panel'\n\nconst popperOptions: Partial<Options> = {\n  modifiers: [\n    {\n      name: 'arrowPosition',\n      enabled: true,\n      phase: 'main',\n      fn: ({ state }) => {\n        const { modifiersData, placement } = state as any\n        if (['right', 'left', 'bottom', 'top'].includes(placement)) return\n        modifiersData.arrow.x = 35\n      },\n      requires: ['arrow'],\n    },\n  ],\n}\nconst COMPONENT_NAME = 'ElCascader'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(cascaderProps)\nconst emit = defineEmits(cascaderEmits)\nconst attrs = useAttrs()\n\nlet inputInitialHeight = 0\nlet pressDeleteCount = 0\n\nconst nsCascader = useNamespace('cascader')\nconst nsInput = useNamespace('input')\n\nconst { t } = useLocale()\nconst { form, formItem } = useFormItem()\nconst { valueOnClear } = useEmptyValues(props)\nconst { isComposing, handleComposition } = useComposition({\n  afterComposition(event) {\n    const text = (event.target as HTMLInputElement)?.value\n    handleInput(text)\n  },\n})\n\nconst tooltipRef: Ref<TooltipInstance | null> = ref(null)\nconst input: Ref<InputInstance | null> = ref(null)\nconst tagWrapper = ref(null)\nconst cascaderPanelRef: Ref<CascaderPanelInstance | null> = ref(null)\nconst suggestionPanel: Ref<ScrollbarInstance | null> = ref(null)\nconst popperVisible = ref(false)\nconst inputHover = ref(false)\nconst filtering = ref(false)\nconst filterFocus = ref(false)\nconst inputValue = ref('')\nconst searchInputValue = ref('')\nconst presentTags: Ref<Tag[]> = ref([])\nconst allPresentTags: Ref<Tag[]> = ref([])\nconst suggestions: Ref<CascaderNode[]> = ref([])\n\nconst cascaderStyle = computed<StyleValue>(() => {\n  return attrs.style as StyleValue\n})\n\nconst isDisabled = computed(() => props.disabled || form?.disabled)\nconst inputPlaceholder = computed(\n  () => props.placeholder || t('el.cascader.placeholder')\n)\nconst currentPlaceholder = computed(() =>\n  searchInputValue.value || presentTags.value.length > 0 || isComposing.value\n    ? ''\n    : inputPlaceholder.value\n)\nconst realSize = useFormSize()\nconst tagSize = computed(() =>\n  realSize.value === 'small' ? 'small' : 'default'\n)\nconst multiple = computed(() => !!props.props.multiple)\nconst readonly = computed(() => !props.filterable || multiple.value)\nconst searchKeyword = computed(() =>\n  multiple.value ? searchInputValue.value : inputValue.value\n)\nconst checkedNodes: ComputedRef<CascaderNode[]> = computed(\n  () => cascaderPanelRef.value?.checkedNodes || []\n)\nconst clearBtnVisible = computed(() => {\n  if (\n    !props.clearable ||\n    isDisabled.value ||\n    filtering.value ||\n    !inputHover.value\n  )\n    return false\n\n  return !!checkedNodes.value.length\n})\nconst presentText = computed(() => {\n  const { showAllLevels, separator } = props\n  const nodes = checkedNodes.value\n  return nodes.length\n    ? multiple.value\n      ? ''\n      : nodes[0].calcText(showAllLevels, separator)\n    : ''\n})\n\nconst validateState = computed(() => formItem?.validateState || '')\n\nconst checkedValue = computed<CascaderValue>({\n  get() {\n    return cloneDeep(props.modelValue) as CascaderValue\n  },\n  set(val) {\n    // https://github.com/element-plus/element-plus/issues/17647\n    const value = val ?? valueOnClear.value\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n    if (props.validateEvent) {\n      formItem?.validate('change').catch((err) => debugWarn(err))\n    }\n  },\n})\n\nconst cascaderKls = computed(() => {\n  return [\n    nsCascader.b(),\n    nsCascader.m(realSize.value),\n    nsCascader.is('disabled', isDisabled.value),\n    attrs.class,\n  ]\n})\n\nconst cascaderIconKls = computed(() => {\n  return [\n    nsInput.e('icon'),\n    'icon-arrow-down',\n    nsCascader.is('reverse', popperVisible.value),\n  ]\n})\n\nconst inputClass = computed(() => {\n  return nsCascader.is('focus', popperVisible.value || filterFocus.value)\n})\n\nconst contentRef = computed(() => {\n  return tooltipRef.value?.popperRef?.contentRef\n})\n\nconst togglePopperVisible = (visible?: boolean) => {\n  if (isDisabled.value) return\n\n  visible = visible ?? !popperVisible.value\n\n  if (visible !== popperVisible.value) {\n    popperVisible.value = visible\n    input.value?.input?.setAttribute('aria-expanded', `${visible}`)\n\n    if (visible) {\n      updatePopperPosition()\n      nextTick(cascaderPanelRef.value?.scrollToExpandingNode)\n    } else if (props.filterable) {\n      syncPresentTextValue()\n    }\n\n    emit('visibleChange', visible)\n  }\n}\n\nconst updatePopperPosition = () => {\n  nextTick(() => {\n    tooltipRef.value?.updatePopper()\n  })\n}\nconst hideSuggestionPanel = () => {\n  filtering.value = false\n}\n\nconst genTag = (node: CascaderNode): Tag => {\n  const { showAllLevels, separator } = props\n  return {\n    node,\n    key: node.uid,\n    text: node.calcText(showAllLevels, separator),\n    hitState: false,\n    closable: !isDisabled.value && !node.isDisabled,\n    isCollapseTag: false,\n  }\n}\n\nconst deleteTag = (tag: Tag) => {\n  const node = tag.node as CascaderNode\n  node.doCheck(false)\n  cascaderPanelRef.value?.calculateCheckedValue()\n  emit('removeTag', node.valueByOption)\n}\n\nconst calculatePresentTags = () => {\n  if (!multiple.value) return\n\n  const nodes = checkedNodes.value\n  const tags: Tag[] = []\n\n  const allTags: Tag[] = []\n  nodes.forEach((node) => allTags.push(genTag(node)))\n  allPresentTags.value = allTags\n\n  if (nodes.length) {\n    nodes\n      .slice(0, props.maxCollapseTags)\n      .forEach((node) => tags.push(genTag(node)))\n    const rest = nodes.slice(props.maxCollapseTags)\n    const restCount = rest.length\n\n    if (restCount) {\n      if (props.collapseTags) {\n        tags.push({\n          key: -1,\n          text: `+ ${restCount}`,\n          closable: false,\n          isCollapseTag: true,\n        })\n      } else {\n        rest.forEach((node) => tags.push(genTag(node)))\n      }\n    }\n  }\n\n  presentTags.value = tags\n}\n\nconst calculateSuggestions = () => {\n  const { filterMethod, showAllLevels, separator } = props\n  const res = cascaderPanelRef.value\n    ?.getFlattedNodes(!props.props.checkStrictly)\n    ?.filter((node) => {\n      if (node.isDisabled) return false\n      node.calcText(showAllLevels, separator)\n      return filterMethod(node, searchKeyword.value)\n    })\n\n  if (multiple.value) {\n    presentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n    allPresentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n  }\n\n  filtering.value = true\n  suggestions.value = res!\n  updatePopperPosition()\n}\n\nconst focusFirstNode = () => {\n  let firstNode!: HTMLElement\n\n  if (filtering.value && suggestionPanel.value) {\n    firstNode = suggestionPanel.value.$el.querySelector(\n      `.${nsCascader.e('suggestion-item')}`\n    )\n  } else {\n    firstNode = cascaderPanelRef.value?.$el.querySelector(\n      `.${nsCascader.b('node')}[tabindex=\"-1\"]`\n    )\n  }\n\n  if (firstNode) {\n    firstNode.focus()\n    !filtering.value && firstNode.click()\n  }\n}\n\nconst updateStyle = () => {\n  const inputInner = input.value?.input\n  const tagWrapperEl = tagWrapper.value\n  const suggestionPanelEl = suggestionPanel.value?.$el\n\n  if (!isClient || !inputInner) return\n\n  if (suggestionPanelEl) {\n    const suggestionList = suggestionPanelEl.querySelector(\n      `.${nsCascader.e('suggestion-list')}`\n    )\n    suggestionList.style.minWidth = `${inputInner.offsetWidth}px`\n  }\n\n  if (tagWrapperEl) {\n    const { offsetHeight } = tagWrapperEl\n    // 2 is el-input__wrapper padding\n    const height =\n      presentTags.value.length > 0\n        ? `${Math.max(offsetHeight, inputInitialHeight) - 2}px`\n        : `${inputInitialHeight}px`\n    inputInner.style.height = height\n    updatePopperPosition()\n  }\n}\n\nconst getCheckedNodes = (leafOnly: boolean) => {\n  return cascaderPanelRef.value?.getCheckedNodes(leafOnly)\n}\n\nconst handleExpandChange = (value: CascaderValue) => {\n  updatePopperPosition()\n  emit('expandChange', value)\n}\n\nconst handleKeyDown = (e: KeyboardEvent) => {\n  if (isComposing.value) return\n\n  switch (e.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      togglePopperVisible()\n      break\n    case EVENT_CODE.down:\n      togglePopperVisible(true)\n      nextTick(focusFirstNode)\n      e.preventDefault()\n      break\n    case EVENT_CODE.esc:\n      if (popperVisible.value === true) {\n        e.preventDefault()\n        e.stopPropagation()\n        togglePopperVisible(false)\n      }\n      break\n    case EVENT_CODE.tab:\n      togglePopperVisible(false)\n      break\n  }\n}\n\nconst handleClear = () => {\n  cascaderPanelRef.value?.clearCheckedNodes()\n  if (!popperVisible.value && props.filterable) {\n    syncPresentTextValue()\n  }\n  togglePopperVisible(false)\n  emit('clear')\n}\n\nconst syncPresentTextValue = () => {\n  const { value } = presentText\n  inputValue.value = value\n  searchInputValue.value = value\n}\n\nconst handleSuggestionClick = (node: CascaderNode) => {\n  const { checked } = node\n\n  if (multiple.value) {\n    cascaderPanelRef.value?.handleCheckChange(node, !checked, false)\n  } else {\n    !checked && cascaderPanelRef.value?.handleCheckChange(node, true, false)\n    togglePopperVisible(false)\n  }\n}\n\nconst handleSuggestionKeyDown = (e: KeyboardEvent) => {\n  const target = e.target as HTMLElement\n  const { code } = e\n\n  switch (code) {\n    case EVENT_CODE.up:\n    case EVENT_CODE.down: {\n      e.preventDefault()\n      const distance = code === EVENT_CODE.up ? -1 : 1\n      focusNode(\n        getSibling(\n          target,\n          distance,\n          `.${nsCascader.e('suggestion-item')}[tabindex=\"-1\"]`\n        ) as HTMLElement\n      )\n      break\n    }\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      target.click()\n      break\n  }\n}\n\nconst handleDelete = () => {\n  const tags = presentTags.value\n  const lastTag = tags[tags.length - 1]\n  pressDeleteCount = searchInputValue.value ? 0 : pressDeleteCount + 1\n\n  if (!lastTag || !pressDeleteCount || (props.collapseTags && tags.length > 1))\n    return\n\n  if (lastTag.hitState) {\n    deleteTag(lastTag)\n  } else {\n    lastTag.hitState = true\n  }\n}\n\nconst handleFocus = (e: FocusEvent) => {\n  const el = e.target as HTMLInputElement\n  const name = nsCascader.e('search-input')\n  if (el.className === name) {\n    filterFocus.value = true\n  }\n  emit('focus', e)\n}\n\nconst handleBlur = (e: FocusEvent) => {\n  filterFocus.value = false\n  emit('blur', e)\n}\n\nconst handleFilter = debounce(() => {\n  const { value } = searchKeyword\n\n  if (!value) return\n\n  const passed = props.beforeFilter(value)\n\n  if (isPromise(passed)) {\n    passed.then(calculateSuggestions).catch(() => {\n      /* prevent log error */\n    })\n  } else if (passed !== false) {\n    calculateSuggestions()\n  } else {\n    hideSuggestionPanel()\n  }\n}, props.debounce)\n\nconst handleInput = (val: string, e?: KeyboardEvent) => {\n  !popperVisible.value && togglePopperVisible(true)\n\n  if (e?.isComposing) return\n\n  val ? handleFilter() : hideSuggestionPanel()\n}\n\nconst getInputInnerHeight = (inputInner: HTMLElement): number =>\n  Number.parseFloat(\n    useCssVar(nsInput.cssVarName('input-height'), inputInner).value\n  ) - 2\n\nwatch(filtering, updatePopperPosition)\n\nwatch(\n  [checkedNodes, isDisabled, () => props.collapseTags],\n  calculatePresentTags\n)\n\nwatch(presentTags, () => {\n  nextTick(() => updateStyle())\n})\n\nwatch(realSize, async () => {\n  await nextTick()\n  const inputInner = input.value!.input!\n  inputInitialHeight = getInputInnerHeight(inputInner) || inputInitialHeight\n  updateStyle()\n})\n\nwatch(presentText, syncPresentTextValue, { immediate: true })\n\nonMounted(() => {\n  const inputInner = input.value!.input!\n\n  const inputInnerHeight = getInputInnerHeight(inputInner)\n\n  inputInitialHeight = inputInner.offsetHeight || inputInnerHeight\n  useResizeObserver(inputInner, updateStyle)\n})\n\ndefineExpose({\n  /**\n   * @description get an array of currently selected node,(leafOnly) whether only return the leaf checked nodes, default is `false`\n   */\n  getCheckedNodes,\n  /**\n   * @description cascader panel ref\n   */\n  cascaderPanelRef,\n  /**\n   * @description toggle the visible of popper\n   */\n  togglePopperVisible,\n  /**\n   * @description cascader content ref\n   */\n  contentRef,\n  /**\n   * @description selected content text\n   */\n  presentText,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref", "_withCtx", "_withDirectives", "_createElementBlock"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;mCAsQc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAnBA,IAAA,MAAM,aAAkC,GAAA;AAAA,MACtC,SAAW,EAAA;AAAA,QACT;AAAA,UACE,IAAM,EAAA,eAAA;AAAA,UACN,OAAS,EAAA,IAAA;AAAA,UACT,KAAO,EAAA,MAAA;AAAA,UACP,EAAI,EAAA,CAAC,EAAE,KAAA,EAAY,KAAA;AACjB,YAAM,MAAA,EAAE,aAAe,EAAA,SAAA,EAAc,GAAA,KAAA,CAAA;AACrC,YAAI,IAAA,CAAC,SAAS,MAAQ,EAAA,QAAA,EAAU,KAAK,CAAE,CAAA,QAAA,CAAS,SAAS,CAAG;AAC5D,cAAA,OAAA;AAAwB,YAC1B,aAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AAAA,WACA;AAAkB,UACpB,QAAA,EAAA,CAAA,OAAA,CAAA;AAAA,SACF;AAAA,OACF;AASA,KAAA,CAAA;AAEA,IAAA,MAAyB,KAAA,GAAA,QAAA,EAAA,CAAA;AACzB,IAAA,IAAI,kBAAmB,GAAA,CAAA,CAAA;AAEvB,IAAM,IAAA;AACN,IAAM,MAAA,UAAU,eAAoB,CAAA,UAAA,CAAA,CAAA;AAEpC,IAAM,MAAA,OAAI,GAAc,YAAA,CAAA,OAAA,CAAA,CAAA;AACxB,IAAA,MAAM,EAAE,CAAA,EAAA,GAAM,SAAS,EAAA,CAAA;AACvB,IAAA,MAAM,EAAE,IAAA,EAAA,QAAiB,EAAA,GAAA,WAAA,EAAe,CAAK;AAC7C,IAAA,MAAM,EAAE,YAAA,EAA+B,GAAA,cAAA,CAAA,KAAmB,CAAA,CAAA;AAAA,IAAA,mBACvC,EAAO,iBAAA,EAAA,GAAA,cAAA,CAAA;AACtB,MAAM,sBAA2C,EAAA;AACjD,QAAA,IAAA,EAAA,CAAA;AAAgB,QAClB,MAAA,IAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,QACD,WAAA,CAAA,IAAA,CAAA,CAAA;AAED,OAAM;AACN,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAa,IAAI,IAAI,CAAA,CAAA;AAC3B,IAAM,MAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAA,GAAA,CAAA,KAAqD,CAAI;AAC/D,IAAM,MAAA,gBAAgB,MAAS,CAAA,IAAA,CAAA,CAAA;AAC/B,IAAM,MAAA,kBAAsB,GAAA,CAAA,IAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,gBAAgB,GAAK,CAAA,KAAA,CAAA,CAAA;AAC3B,IAAM,MAAA,UAAA,GAAA,SAAuB,CAAA,CAAA;AAC7B,IAAM,MAAA,SAAA,GAAA,SAAmB,CAAA,CAAA;AACzB,IAAM,MAAA,WAAA,GAAA,GAAA,CAAA,KAAuB,CAAE,CAAA;AAC/B,IAAM,MAAA,UAAA,GAAA,GAA8B,CAAA,EAAC,CAAC,CAAA;AACtC,IAAM,MAAA,gBAAA,GAAiC,GAAC,CAAC,EAAA,CAAA,CAAA;AACzC,IAAM,MAAA,WAAA,GAAmC,GAAI,CAAA,EAAE,CAAA,CAAA;AAE/C,IAAM,MAAA,cAAA,WAAqC;AACzC,IAAA,MAAA,WAAa,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IACf,MAAC,aAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAA,kBAA4B,CAAA;AAC5B,KAAA,CAAA,CAAA;AAAyB,IAAA,MACjB,UAAqB,GAAA,QAAA,CAAA,MAA2B,KAAA,CAAA,QAAA,KAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IACxD,MAAA,gBAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,WAAA,IAAA,CAAA,CAAA,yBAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAM,kBAAqB,GAAA,QAAA,CAAA,MAAA,gBAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,IAAA,WAAA,CAAA,KAAA,GAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAAS,MAAA,QACjB,GAAA,WAAA,EAAA,CAAA;AAEI,IACvB,MAAA,OAAA,GAAA,QAAA,CAAA,MAAA,QAAA,CAAA,KAAA,KAAA,OAAA,GAAA,OAAA,GAAA,SAAA,CAAA,CAAA;AACA,IAAA,MAAM,WAAW,QAAY,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AAC7B,IAAA,MAAM,QAAU,GAAA,QAAA,CAAA,MAAA,CAAA,KAAA,CAAA,UAAA,IAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MACd,aAAmB,GAAA,QAAA,CAAA,MAAU,QAAU,CAAA,KAAA,GAAA,gBAAA,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IACzC,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAA,IAAM;AACN,MAAA,8BAAgC,CAAC,KAAM,KAAA,IAAA,GAAA,YAAuB,YAAK,KAAA,EAAA,CAAA;AACnE,KAAA,CAAA,CAAA;AAAsB,IAAA,MACpB,eAAiB,GAAA,QAAA,CAAA,MAAA;AAAoC,MACvD,IAAA,CAAA,KAAA,CAAA,SAAA,IAAA,UAAA,CAAA,KAAA,IAAA,SAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA;AACA,QAAA,OAAkD,KAAA,CAAA;AAAA,MAChD,OAAM,CAAA,CAAA,YAAA,CAAA,KAAwB,CAAA,MAAA,CAAA;AAAiB,KACjD,CAAA,CAAA;AACA,IAAM,MAAA,WAAA,GAAA,eAAiC;AACrC,MACE,qBACA,EAAA,SAAA,EAAA;AAIA,MAAO,MAAA,KAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAET,MAAO,OAAA,KAAE,CAAA,MAAA,GAAa,QAAM,CAAA,KAAA,GAAA,EAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,aAAA,EAAA,SAAA,CAAA,GAAA,EAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AACD,IAAM,MAAA,aAAA,WAA6B,CAAA,MAAA,CAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,aAAA,KAAA,EAAA,CAAA,CAAA;AACjC,IAAM,MAAA,YAAiB,GAAA,QAAA,CAAA;AACvB,MAAA,GAAA,GAAM;AACN,QAAO,OAAA,SACH,CAAA,KAAA,CAAA,UACE,CAAA,CAAA;AAEF,OACL;AAED,MAAA,GAAA,CAAM,GAAgB,EAAA;AAEtB,QAAA,iBAAqB,IAAwB,IAAA,GAAA,GAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAAA,QACrC,IAAA,CAAA,kBAAA,EAAA,KAAA,CAAA,CAAA;AACJ,QAAO,IAAA,CAAA,YAAU,OAAgB,CAAA,CAAA;AAAA,QACnC,IAAA,KAAA,CAAA,aAAA,EAAA;AAAA,UACI,QAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAEP,SAAM;AACN,OAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,WAAyB,GAAA,QAAA,CAAA,MAAA;AACvB,MAAU,OAAA;AAAgD,QAC5D,UAAA,CAAA,CAAA,EAAA;AAAA,QACF,UAAA,CAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA;AAAA,QACD,UAAA,CAAA,EAAA,CAAA,UAAA,EAAA,UAAA,CAAA,KAAA,CAAA;AAED,QAAM,KAAA,CAAA,KAAA;AACJ,OAAO,CAAA;AAAA,KAAA,CAAA,CAAA;AACQ,IACb,MAAA,eAAa,GAAA,QAAc,CAAA,MAAA;AAAA,MAAA,OAChB;AAA+B,QAC1C,OAAM,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QACR,iBAAA;AAAA,QACD,UAAA,CAAA,EAAA,CAAA,SAAA,EAAA,aAAA,CAAA,KAAA,CAAA;AAED,OAAM,CAAA;AACJ,KAAO,CAAA,CAAA;AAAA,IACL,MAAA,UAAgB,GAAA,QAAA,CAAA,MAAA;AAAA,MAChB,OAAA,UAAA,CAAA,EAAA,CAAA,OAAA,EAAA,aAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAC4C,IAC9C,MAAA,UAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,IAAA,EAAA,EAAA,EAAA,CAAA;AAED,MAAM,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,aAA4B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAChC,KAAA,CAAA,CAAA;AAAsE,IACxE,MAAC,mBAAA,GAAA,CAAA,OAAA,KAAA;AAED,MAAM,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAO,IAAA,UAAA,CAAA;AAA6B,QACrC,OAAA;AAED,MAAM,OAAA,GAAA,OAAA,IAAA,IAAA,GAA6C,OAAA,GAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACjD,MAAA,IAAI,YAAkB,aAAA,CAAA,KAAA,EAAA;AAEtB,QAAU,aAAA,CAAA,KAAW,GAAe,OAAA,CAAA;AAEpC,QAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,gBAA0B,IAAO,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,eAAA,EAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACnC,QAAA,IAAA,OAAA,EAAA;AACA,UAAA,oBAAoB,EAAA,CAAA;AAEpB,UAAA,QAAa,CAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,CAAA;AACX,SAAqB,MAAA,IAAA,KAAA,CAAA,UAAA,EAAA;AACrB,UAAS,oBAAA,EAAA,CAAA;AAA6C,SACxD;AACE,QAAqB,IAAA,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OACvB;AAEA,KAAA,CAAA;AAA6B,IAC/B,MAAA,oBAAA,GAAA,MAAA;AAAA,MACF,QAAA,CAAA,MAAA;AAEA,QAAA;AACE,QAAA,CAAA,EAAA,GAAA,UAAe,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AACb,OAAA,CAAA,CAAA;AAA+B,KAAA,CACjC;AAAC,IACH,MAAA,mBAAA,GAAA,MAAA;AACA,MAAA;AACE,KAAA,CAAA;AAAkB,IACpB,MAAA,MAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAM,MAAA,EAAA,aAAsC,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AAC1C,MAAM,OAAA;AACN,QAAO,IAAA;AAAA,QACL,GAAA,EAAA,IAAA,CAAA,GAAA;AAAA,QACA,MAAU,IAAA,CAAA,QAAA,CAAA,aAAA,EAAA,SAAA,CAAA;AAAA,QACV,QAAM,EAAA,KAAc;AAAwB,QAC5C,QAAU,EAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAAA,QACV,aAAsB,EAAA,KAAA;AAAe,OAAA,CACrC;AAAe,KACjB,CAAA;AAAA,IACF,MAAA,SAAA,GAAA,CAAA,GAAA,KAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAA,MAAM,OAAO,GAAI,CAAA,IAAA,CAAA;AACjB,MAAA,IAAA,CAAK,QAAQ,KAAK,CAAA,CAAA;AAClB,MAAA,CAAA,EAAA,GAAA,sBAA8C,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,EAAA,CAAA;AAC9C,MAAK,IAAA,CAAA,WAAA,EAAa,KAAK,aAAa,CAAA,CAAA;AAAA,KACtC,CAAA;AAEA,IAAA,MAAM,uBAAuB,MAAM;AACjC,MAAI,IAAA,CAAC,SAAS,KAAO;AAErB,QAAA;AACA,MAAA,MAAM,QAAe,YAAA,CAAA,KAAA,CAAA;AAErB,MAAA,MAAM,UAAiB;AACvB,MAAM,MAAA,OAAA,GAAkB,EAAA,CAAA;AACxB,MAAA,KAAA,CAAA,OAAA,CAAA,CAAe,IAAQ,KAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAEvB,MAAA,cAAkB,CAAA,KAAA,GAAA,OAAA,CAAA;AAChB,MAAA,IAAA,KACS,CAAA,MAAA,EAAS;AAElB,QAAA,KAAA,CAAM,KAAO,CAAA,CAAA,EAAA,KAAY,CAAA,eAAqB,CAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAC9C,QAAA,MAAM,YAAY,CAAK,KAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AAEvB,QAAA,MAAe,SAAA,GAAA,IAAA,CAAA,MAAA,CAAA;AACb,QAAA,IAAA,SAAwB,EAAA;AACtB,UAAA,IAAA,KAAU,CAAA,YAAA,EAAA;AAAA,YAAA,IACH,CAAA,IAAA,CAAA;AAAA,cACL,GAAA,EAAA,CAAM;AAAc,cACpB,IAAU,EAAA,CAAA,EAAA,EAAA,SAAA,CAAA,CAAA;AAAA,cACV,QAAe,EAAA,KAAA;AAAA,cAChB,aAAA,EAAA,IAAA;AAAA,aACI,CAAA,CAAA;AACL,WAAK,MAAA;AAAyC,YAChD,IAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAAA,WACF;AAAA,SACF;AAEA,OAAA;AAAoB,MACtB,WAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,oBAAqC,GAAA,MAAA;AACrC,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AAGF,MAAI,MAAA,EAAA,cAAiB,aAAO,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AAC5B,MAAK,MAAA,GAAA,GAAA,CAAA,EAAA,yBAAiC,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA;AACtC,QAAO,IAAA,IAAA,CAAA,UAAA;AAAsC,UAC9C,OAAA,KAAA,CAAA;AAEH,QAAA,aAAoB,CAAA,aAAA,EAAA,SAAA,CAAA,CAAA;AAClB,QAAY,OAAA,YAAc,CAAA,IAAA,EAAA,aAAS,CAAA,KAAA,CAAA,CAAA;AACjC,OAAA,CAAA,CAAA;AAAe,MAAA,IAChB,QAAA,CAAA,KAAA,EAAA;AACD,QAAe,WAAA,CAAA,KAAA,CAAA,OAAc,CAAA,CAAA,GAAC,KAAQ;AACpC,UAAA,GAAA,CAAI,QAAW,GAAA,KAAA,CAAA;AAAA,SAChB,CAAA,CAAA;AAAA,QACH,cAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA;AAEA,UAAA,GAAA,CAAA,QAAkB,GAAA,KAAA,CAAA;AAClB,SAAA,CAAA,CAAA;AACA,OAAqB;AAAA,MACvB,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,MAAA,uBAA6B,CAAA;AAC3B,MAAI,oBAAA,EAAA,CAAA;AAEJ,KAAI,CAAA;AACF,IAAY,MAAA,cAAA,GAAA,MAAA;AAA0B,MAAA,IACpC,EAAI,CAAA;AAA+B,MACrC,IAAA,SAAA,CAAA;AAAA,MACF,IAAO,SAAA,CAAA,KAAA,IAAA,eAAA,CAAA,KAAA,EAAA;AACL,QAAY,SAAA,GAAA,eAAA,CAAA,SAA4B,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OAAA,MAClC;AAAoB,QAC1B,SAAA,GAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAAA,OACF;AAEA,MAAA,IAAI,SAAW,EAAA;AACb,QAAA,SAAA,CAAU,KAAM,EAAA,CAAA;AAChB,QAAC,CAAA,SAAA,CAAU,KAAS,IAAA,SAAA,CAAU,KAAM,EAAA,CAAA;AAAA,OACtC;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,cAAc,MAAM;AACxB,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AACN,MAAA,MAAM,gBAA0B,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAChC,MAAM,MAAA,YAAA,GAAA;AAEN,MAAI,MAAa,iBAAa,GAAA,CAAA,EAAA,GAAA,eAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA;AAE9B,MAAA,IAAI,CAAmB,QAAA,IAAA,CAAA,UAAA;AACrB,QAAA;AAAyC,MAAA,IACvC,iBAAiB,EAAA;AAAkB,QACrC,MAAA,cAAA,GAAA,iBAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,QAAA,cAAA,CAAe,KAAM,CAAA,QAAA,GAAW,CAAG,EAAA,UAAA,CAAW,WAAW,CAAA,EAAA,CAAA,CAAA;AAAA,OAC3D;AAEA,MAAA,IAAI,YAAc,EAAA;AAChB,QAAM,MAAA,EAAE,cAAiB,GAAA,YAAA,CAAA;AAEzB,QAAA,MAAM,MACJ,GAAA,WAAA,CAAY,KAAM,CAAA,MAAA,GAAS,IACvB,CAAG,EAAA,IAAA,CAAK,GAAI,CAAA,YAAA,EAAc,kBAAkB,CAAA,GAAI,CAAC,CAAA,EAAA,CAAA,GACjD,GAAG,kBAAkB,CAAA,EAAA,CAAA,CAAA;AAC3B,QAAA,UAAA,CAAW,MAAM,MAAS,GAAA,MAAA,CAAA;AAC1B,QAAqB,oBAAA,EAAA,CAAA;AAAA,OACvB;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,QAAsB,KAAA;AAC7C,MAAO,IAAA,EAAA,CAAA;AAAgD,MACzD,OAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,QAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAqB,MAAA,kBAAA,GAAA,CAAA,KAAA,KAAA;AACrB,MAAA,sBAA0B,CAAA;AAAA,MAC5B,IAAA,CAAA,cAAA,EAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,aAAmB,GAAA,CAAA,CAAA,KAAA;AAEvB,MAAA,IAAA,WAAgB,CAAA,KAAA;AAAA,QACd,OAAgB;AAAA,MAAA,QACA,CAAA,CAAA,IAAA;AACd,QAAoB,KAAA,UAAA,CAAA,KAAA,CAAA;AACpB,QAAA,KAAA,UAAA,CAAA,WAAA;AAAA,6BACc,EAAA,CAAA;AACd,UAAA,MAAA;AACA,QAAA,KAAA,UAAuB,CAAA,IAAA;AACvB,UAAA,mBAAiB,CAAA,IAAA,CAAA,CAAA;AACjB,UAAA,QAAA,CAAA,cAAA,CAAA,CAAA;AAAA,0BACc,EAAA,CAAA;AACd,UAAI,MAAA;AACF,QAAA,KAAA,UAAiB,CAAA,GAAA;AACjB,UAAA,IAAE,aAAgB,CAAA,KAAA,KAAA,IAAA,EAAA;AAClB,YAAA,CAAA,CAAA,cAAA,EAAA,CAAA;AAAyB,YAC3B,CAAA,CAAA,eAAA,EAAA,CAAA;AACA,YAAA,mBAAA,CAAA,KAAA,CAAA,CAAA;AAAA;AAEA,UAAA,MAAA;AACA,QAAA,KAAA,UAAA,CAAA,GAAA;AAAA,UACJ,mBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,UACF,MAAA;AAEA,OAAA;AACE,KAAA,CAAA;AACA,IAAA,MAAI,WAAC,GAAA,MAAuB;AAC1B,MAAqB,IAAA,EAAA,CAAA;AAAA,MACvB,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,EAAA,CAAA;AACA,MAAA,IAAA,CAAA,aAAA,CAAA,KAAyB,IAAA,KAAA,CAAA,UAAA,EAAA;AACzB,QAAA,oBAAY,EAAA,CAAA;AAAA,OACd;AAEA,MAAA,2BAA6B;AAC3B,MAAM,IAAA,CAAA,SAAQ;AACd,KAAA,CAAA;AACA,IAAA,MAAA,oBAAyB,GAAA,MAAA;AAAA,MAC3B,MAAA,EAAA,KAAA,EAAA,GAAA,WAAA,CAAA;AAEA,MAAM,UAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACJ,MAAM,gBAAU,CAAI,KAAA,GAAA,KAAA,CAAA;AAEpB,KAAA,CAAA;AACE,IAAA,MAAA,qBAAwB,GAAA,CAAA,IAAA,KAAA;AAAuC,MACjE,IAAO,EAAA,EAAA,EAAA,CAAA;AACL,MAAA,eAA6B,EAAA,GAAA,IAAA,CAAA;AAC7B,MAAA,IAAA,QAAA,CAAA,KAAA,EAAA;AAAyB,QAC3B,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AAAA,OACF,MAAA;AAEA,QAAM,CAAA,OAAA,KAAA,CAAA,EAAA,GAAA,gBAAgD,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACpD,QAAA,mBAAiB,CAAA,KAAA,CAAA,CAAA;AACjB,OAAM;AAEN,KAAA,CAAA;AAAc,IAAA,6BACI,GAAA,CAAA,CAAA,KAAA;AAAA,MAChB,MAAA,iBAAsB,CAAA;AACpB,MAAA,MAAE,EAAe,IAAA,EAAA,GAAA,CAAA,CAAA;AACjB,MAAA,QAAA,IAAiB;AACjB,QAAA,KAAA,UAAA,CAAA,EAAA,CAAA;AAAA,QACE,KAAA,UAAA,CAAA,IAAA,EAAA;AAAA,UACE,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,UACA,MAAA,QAAA,GAAA,IAAA,KAAA,UAAA,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAAA,UAAA,SACI,CAAA,UAAa,CAAA,MAAA,EAAA,QAAA,EAAkB,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AAAA,UACrC,MAAA;AAAA,SACF;AACA,QAAA,KAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACF,KAAA,UAAA,CAAA,WAAA;AAAA,gBACgB,CAAA,KAAA,EAAA,CAAA;AAAA,gBACA;AACd,OAAA;AACA,KAAA,CAAA;AAAA,IACJ,MAAA,YAAA,GAAA,MAAA;AAAA,MACF,MAAA,IAAA,GAAA,WAAA,CAAA,KAAA,CAAA;AAEA,MAAA,oBAA2B,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACzB,MAAA,gBAAyB,GAAA,gBAAA,CAAA,KAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,CAAA;AACzB,MAAA,IAAA,CAAA,OAAgB,IAAA,CAAA,gBAAU,IAAU,KAAA,CAAA,YAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACpC,QAAmB,OAAA;AAEnB,MAAA,IAAI,OAAY,CAAA;AACd,QAAA,SAAA,CAAA,OAAA,CAAA,CAAA;AAEF,OAAA;AACE,QAAA,OAAA,CAAA,QAAiB,GAAA,IAAA,CAAA;AAAA,OACZ;AACL,KAAA,CAAA;AAAmB,IACrB,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACF,MAAA,EAAA,GAAA,CAAA,CAAA,MAAA,CAAA;AAEA,MAAM,MAAA,IAAA,GAAA,UAAiC,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AACrC,MAAA,IAAA,EAAM,UAAO,KAAA,IAAA,EAAA;AACb,QAAM,WAAO,CAAW,KAAA,GAAA,IAAE,CAAc;AACxC,OAAI;AACF,MAAA,IAAA,CAAA,OAAA,EAAY,CAAQ,CAAA,CAAA;AAAA,KACtB,CAAA;AACA,IAAA,MAAA,UAAc,GAAC,CAAA,CAAA,KAAA;AAAA,MACjB,WAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAM,IAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA;AACJ,KAAA,CAAA;AACA,IAAA,MAAA,YAAc,GAAA,QAAA,CAAA,MAAA;AAAA,MAChB,MAAA,EAAA,KAAA,EAAA,GAAA,aAAA,CAAA;AAEA,MAAM,IAAA,CAAA,KAAA;AACJ,QAAM;AAEN,MAAA,MAAY,MAAA,GAAA,KAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAEZ,MAAM,IAAA,SAAA,CAAA,MAAe,CAAA,EAAA;AAErB,QAAI,MAAA,CAAA,IAAA,CAAU,oBAAS,CAAA,CAAA,KAAA,CAAA,MAAA;AACrB,SAAA,CAAA,CAAA;AAA8C,OAAA,MAE7C,IAAA,MAAA,KAAA,KAAA,EAAA;AAAA,QACH,oBAAsB,EAAO,CAAA;AAC3B,OAAqB,MAAA;AAAA,QAChB,mBAAA,EAAA,CAAA;AACL,OAAoB;AAAA,KACtB,EAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AAAA,IACF,iBAAiB,GAAA,CAAA,GAAA,EAAA,CAAA,KAAA;AAEjB,MAAM,CAAA,aAAA,CAAA,KAAe,IAAa,mBAAsB,CAAA,IAAA,CAAA,CAAA;AACtD,MAAC,IAAA,CAAA,IAAA,IAAA,GAAuB,KAAA,CAAA,GAAA,CAAA,CAAA,WAAA;AAExB,QAAA,OAAoB;AAEpB,MAAM,GAAA,GAAA,YAAA,KAAiB,mBAAoB,EAAA,CAAA;AAAA,KAC7C,CAAA;AAEA,IAAM,MAAA,mBAAA,GAAsB,CAAC,UAAA,KAC3B,MAAO,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,CAAA,cAAA,CAAA,EAAA,UAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAAA,eACa,EAAA,oBAAyB,CAAA,CAAA;AAAe,IAC5D,KAAI,CAAA,CAAA,YAAA,EAAA,UAAA,EAAA,MAAA,KAAA,CAAA,YAAA,CAAA,EAAA,oBAAA,CAAA,CAAA;AAEN,IAAA,KAAA,CAAM,WAAW,EAAoB,MAAA;AAErC,MAAA,QAAA,CAAA,MAAA,WAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CACE,CAAC;AAAkD,IACnD,KAAA,CAAA,QAAA,EAAA,YAAA;AAAA,MACF,MAAA,QAAA,EAAA,CAAA;AAEA,MAAA,mBAAyB,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AACvB,MAAS,wCAAmB,CAAA,UAAA,CAAA,IAAA,kBAAA,CAAA;AAAA,MAC7B,WAAA,EAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,KAAA,CAAA,WAAe,EAAA,oBAAA,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACf,IAAM,SAAA,CAAA,MAAA;AACN,MAAqB,MAAA,UAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AACrB,MAAY,MAAA,gBAAA,GAAA,mBAAA,CAAA,UAAA,CAAA,CAAA;AAAA,MACb,kBAAA,GAAA,UAAA,CAAA,YAAA,IAAA,gBAAA,CAAA;AAED,MAAA,iBAAmB,CAAA,UAAA,EAAA,WAAwB,CAAA,CAAA;AAE3C,KAAA,CAAA,CAAA;AACE,IAAM,MAAA,CAAA;AAEN,MAAM,eAAA;AAEN,MAAA,gBAAA;AACA,MAAA;AAAyC,MAC1C,UAAA;AAED,MAAa,WAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,SAAA,CAAA,EAAA;AAAA,QAIX,OAAA,EAAA,YAAA;AAAA,QAAA,GAAA,EAAA,UAAA;AAAA,QAAA,OAAA,EAAA,aAAA,CAAA,KAAA;AAAA,QAAA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAIA,cAAA,EAAA,CAAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,QAAA,gBAAA,EAAA,aAAA;AAAA,QAAA,qBAAA,EAAA,IAAA,CAAA,kBAAA;AAAA,QAAA,yBAAA,EAAA,KAAA;AAAA,QAIA,kBAAA,EAAA,KAAA;AAAA,QAAA,SAAA,EAAA,IAAA,CAAA,SAAA;AAAA,QAAA,UAAA,EAAA,CAAA,EAAAA,KAAA,CAAA,UAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAIA,IAAA,EAAA,EAAA;AAAA,QAAA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAAA,MAAA,EAAA,mBAAA;AAAA,OAAA,EAAA;AAAA,QAIA,OAAA,EAAAC,OAAA,CAAA,MAAA;AAAA,UACDC,cAAA,EAAAJ,SAAA,EAAA,EAAAK,kBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}