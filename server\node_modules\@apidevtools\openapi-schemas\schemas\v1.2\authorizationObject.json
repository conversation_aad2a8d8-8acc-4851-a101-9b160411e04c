{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/authorizationObject.json#", "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "additionalProperties": {"oneOf": [{"$ref": "#/definitions/basicAuth"}, {"$ref": "#/definitions/apiKey"}, {"$ref": "#/definitions/oauth2"}]}, "definitions": {"basicAuth": {"required": ["type"], "properties": {"type": {"enum": ["basicAuth"]}}, "additionalProperties": false}, "apiKey": {"required": ["type", "passAs", "keyname"], "properties": {"type": {"enum": ["<PERSON><PERSON><PERSON><PERSON>"]}, "passAs": {"enum": ["header", "query"]}, "keyname": {"type": "string"}}, "additionalProperties": false}, "oauth2": {"type": "object", "required": ["type", "grantTypes"], "properties": {"type": {"enum": ["oauth2"]}, "scopes": {"type": "array", "items": {"$ref": "#/definitions/oauth2Scope"}}, "grantTypes": {"$ref": "oauth2GrantType.json#"}}, "additionalProperties": false}, "oauth2Scope": {"type": "object", "required": ["scope"], "properties": {"scope": {"type": "string"}, "description": {"type": "string"}}, "additionalProperties": false}}}