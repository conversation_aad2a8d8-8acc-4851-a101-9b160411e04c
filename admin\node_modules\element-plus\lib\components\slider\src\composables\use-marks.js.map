{"version": 3, "file": "use-marks.js", "sources": ["../../../../../../../packages/components/slider/src/composables/use-marks.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport type { SliderProps } from '../slider'\nimport type { SliderMarkerProps } from '../marker'\n\nexport interface Mark extends SliderMarkerProps {\n  point: number\n  position: number\n}\n\nexport const useMarks = (props: SliderProps) => {\n  return computed(() => {\n    if (!props.marks) {\n      return []\n    }\n\n    const marksKeys = Object.keys(props.marks)\n    return marksKeys\n      .map(Number.parseFloat)\n      .sort((a, b) => a - b)\n      .filter((point) => point <= props.max && point >= props.min)\n      .map(\n        (point): Mark => ({\n          point,\n          position: ((point - props.min) * 100) / (props.max - props.min),\n          mark: props.marks![point],\n        })\n      )\n  })\n}\n"], "names": ["computed"], "mappings": ";;;;;;AACY,MAAC,QAAQ,GAAG,CAAC,KAAK,KAAK;AACnC,EAAE,OAAOA,YAAQ,CAAC,MAAM;AACxB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AACtB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC/C,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AAC9I,MAAM,KAAK;AACX,MAAM,QAAQ,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACnE,MAAM,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAC9B,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,CAAC,CAAC;AACL;;;;"}