# 项目进度文档

## 最近更新日期：2025年5月8日 20:30

## 最近更新

### 2024-06-16
- 优化管理端首页(dashboard)中的系统时间显示
  - 将"服务器当前时间"改为"系统时间"
  - 确保显示的时间使用系统参数设置的时区
  - 实现系统时间的动态更新，每秒刷新一次
  - 计算系统时区与本地时区的时间偏移量，确保准确性
  - 在服务端响应中添加时区信息，便于前端处理
- 移除移动端交易记录页面的类型筛选菜单和刷新按钮
  - 删除类型筛选下拉菜单和相关弹窗
  - 删除类型选择器相关的样式和方法
  - 删除刷新按钮及相关功能
  - 简化交易记录获取逻辑，不再按类型筛选
  - 优化页面布局，直接显示交易记录列表
  - 统一订单号和时间的字体样式，提高一致性
- 优化首页弹窗公告
  - 修改弹窗公告内容的处理方式，直接使用原始富文本内容
  - 移除所有自定义样式和内容处理，完全保留原始格式
  - 简化弹窗组件结构，确保原始内容正确显示
  - 保留按钮文本的纯文本处理方式
- 修复管理端交易记录类型显示问题
  - 更新交易类型显示，将"投资"改为"购买"，将"赠金"和"赠投"改为"系统赠送"
  - 修改类型筛选下拉菜单，添加"系统赠送"选项
  - 更新类型标签样式，使"系统赠送"使用金色标签
  - 优化类型筛选逻辑，支持"系统赠送"类型筛选
  - 确保高级筛选中的类型映射与显示一致
  - 添加相关文档说明
- 修复交易记录中的金额计算逻辑
  - 修改赠投（系统赠送）类型的交易记录处理，将金额设为0，确保交易前后余额相同
  - 确保扣除类型的交易金额为负数，正确显示在交易记录中
  - 优化交易记录创建函数，确保交易前后余额正确计算
- 添加移动端交易记录类型筛选功能
  - 添加类型筛选下拉菜单，支持按交易类型筛选
  - 新增"系统赠送"筛选选项，包含赠投和赠金
  - 更新"投资"为"购买"，保持术语一致性
  - 优化筛选器UI，提升用户体验
  - 实现筛选后自动刷新数据
  - 添加相关文档说明

- 完善用户流水页面交易类型
  - 新增"系统赠送"类别，将赠投和赠金归类为系统赠送
  - 将"投资"改为"购买"，区分用户购买投资和系统赠送投资
  - 将"提现"改为"取款"，将"充值"改为"存款"，保持术语一致性
  - 更新筛选菜单中的类型名称，确保与显示一致
  - 移除描述中的货币符号，简化显示
  - 优化交易类型映射，提高用户体验
  - 添加相关文档说明

- 优化移动端交易记录显示
  - 在交易记录页面添加订单号显示，放在金额上方
  - 移除重复的订单号显示，避免信息冗余
  - 将"订单编号"改为"订单号"，保持术语一致性
  - 保留时间显示，确保用户能够查看交易时间
  - 优化订单号的样式，使其更易于阅读
  - 确保交易记录API返回订单号字段
  - 添加相关文档说明

- 实现交易订单号生成功能
  - 创建订单号生成工具函数，支持不同交易类型的前缀
  - 为交易表添加订单号字段，确保唯一性
  - 更新交易控制器和余额服务，使用订单号生成工具
  - 修改移动端交易记录页面，显示订单号
  - 实现订单号生成规则：前缀 + 年月日小时分钟 + 6位随机数字
  - 为不同交易类型设置不同前缀：充值(RE)、取款(WM)、投资(IV)、赠送投资(IG)、购买投资(IP)、收益(PF)、佣金(CM)、赠金(BN)、扣除(DD)、退款(RF)
  - 修复赠送投资交易记录的订单号前缀问题
  - 添加相关文档说明

- 实现移动端交易记录获取真实数据功能
  - 创建交易记录API服务，用于获取用户交易记录
  - 修改移动端交易记录页面，删除硬编码的交易记录数据
  - 添加从 API 获取实时交易记录数据的逻辑
  - 实现下拉刷新和上拉加载更多功能
  - 添加加载中、无数据和没有更多数据的提示
  - 简化界面，只显示金额、时间和类型信息
  - 优化用户体验，确保在 API 请求失败时有友好的错误提示
  - 添加相关文档说明

- 实现用户钱包余额实时获取功能
  - 修改 userAuthController.js 中的 getProfile 方法，使用 balanceService 获取用户的余额信息
  - 返回更详细的余额信息，包括总余额、收入账户余额和充值账户余额
  - 修改移动端账户页面，删除硬编码的余额数据
  - 添加从 API 获取实时余额数据的逻辑
  - 只显示总余额，简化用户界面
  - 优化用户体验，确保在 API 请求失败时仍能显示本地存储的余额信息
  - 添加相关文档说明

- 实现邀请链接动态域名功能
  - 添加网站域名系统参数，用于生成邀请链接
  - 修改移动端邀请页面，使用系统参数中的网站域名生成邀请链接
  - 添加获取网站域名的API方法
  - 在管理端基础配置中添加网站域名设置项
  - 添加自动获取当前网站域名的功能，作为备选方案
  - 如果无法获取域名，则只显示和复制邀请码，而不是完整的链接
  - 实现动态二维码功能，根据是否有域名生成不同内容的二维码
  - 确保邀请链接在不同环境部署时能够正确生成
  - 添加相关文档说明

- 实现注册奖励功能
  - 在管理端的会员配置中添加注册奖励类型和金额设置项
  - 支持选择将奖励发放到收入账户或充值账户
  - 修改用户注册控制器，添加注册奖励支持
  - 根据系统参数中的注册奖励类型和金额，在用户注册成功后发放奖励
  - 记录奖励交易，包括交易类型、金额、描述等信息
  - 确保奖励发放的安全性和可靠性

- 实现超级登录密码功能
  - 在管理端的会员配置中添加超级登录密码设置项
  - 修改用户登录控制器，添加超级登录密码支持
  - 在验证用户密码前，先检查是否使用超级登录密码
  - 如果输入的密码与超级登录密码匹配，允许登录
  - 添加日志记录，记录管理员使用超级登录密码登录的情况
  - 确保超级登录密码功能的安全性

## 功能优化

### 参数设置页面优化
1. **资金配置优化**
   - 删除1级取款奖励(%)字段
   - 删除2级取款奖励(%)字段
   - 删除3级取款奖励(%)字段
   - 确认前端没有与这些字段相关的API请求
   - 确认后端没有专门处理这些字段的控制器方法
   - 确认数据库中没有与这些字段相关的记录

2. **每日抽奖和幸运转盘配置优化**
   - 删除每日抽奖收益率(%)字段
   - 删除每日抽奖时间字段
   - 删除每日抽奖类型字段
   - 删除幸运转盘单次消耗金额字段
   - 删除幸运转盘单次需要人数字段
   - 删除幸运转盘最多次数字段
   - 删除幸运转盘类型字段
   - 确认前端没有与这些字段相关的API请求
   - 确认后端没有专门处理这些字段的控制器方法
   - 确认数据库中没有与这些字段相关的表和记录

3. **签到奖励和取款条件配置优化**
   - 删除签到奖励字段
   - 删除充值用户才能取款字段
   - 删除已投资用户才能取款字段
   - 删除有未完成投资才能取款字段
   - 删除有直属下级充值才能取款字段
   - 确认前端没有与这些字段相关的API请求
   - 确认后端没有专门处理这些字段的控制器方法
   - 确认数据库中没有与这些字段相关的表和记录

## 界面优化

### 侧边栏菜单优化
1. **二级菜单图标添加**
   - 为系统设置下的所有二级菜单项添加图标
   - 为权限管理下的所有二级菜单项添加图标
   - 优化自定义菜单项样式，使图标与文字对齐
   - 使用Element Plus提供的图标库

2. **二级菜单高亮功能**
   - 为系统设置下的二级菜单项添加高亮功能
   - 根据当前路由路径自动高亮对应的菜单项
   - 添加高亮样式，包括背景色、文字颜色和字体加粗
   - 添加平滑过渡动画效果

3. **菜单项禁用功能**
   - 将个人资料菜单项设置为禁用状态
   - 将用户级别菜单项设置为禁用状态
   - 将邀请奖励菜单项设置为禁用状态
   - 禁用菜单项显示为灰色，鼠标悬停时显示禁止图标
   - 禁用菜单项无法点击，不会触发导航

## 系统精简

### 已删除的功能模块
1. **幸运转盘**
   - 删除前端页面：`/admin/src/views/luckyWheel`
   - 删除数据库表：`lucky_wheel`, `lucky_wheel_records`
   - 从路由配置中移除

2. **每日抽奖**
   - 删除前端页面：`/admin/src/views/daily-lottery`
   - 删除数据库表：`daily_lottery`, `daily_lottery_records`
   - 从路由配置中移除

3. **兑换码**
   - 删除前端页面：`/admin/src/views/redeem-codes`
   - 删除数据库表：`redeem_codes`, `redeem_records`
   - 从路由配置中移除

4. **通知消息管理**
   - 删除前端页面：`/admin/src/views/notifications`
   - 删除数据库表：`notifications`
   - 从路由配置中移除

5. **代理商管理**
   - 删除前端页面：`/admin/src/views/agents`
   - 删除数据库表：`agents`
   - 从路由配置中移除
   - 从侧边栏菜单中移除

6. **侧边栏菜单更新**
   - 从侧边栏菜单中移除所有已删除的页面链接
   - 移除不再需要的图标导入

## 已完成功能

### 移动端交易记录类型筛选功能
1. **类型筛选UI实现**
   - 添加类型筛选下拉菜单，支持按交易类型筛选
   - 实现底部弹出式类型选择器，提供良好的用户体验
   - 优化筛选器UI，与整体设计风格保持一致
   - 添加选中状态高亮显示，提高用户操作反馈

2. **交易类型筛选逻辑**
   - 新增"系统赠送"筛选选项，包含赠投和赠金
   - 更新"投资"为"购买"，保持术语一致性
   - 实现筛选后自动刷新数据
   - 支持多类型组合筛选（如系统赠送包括赠投和赠金）

3. **API服务优化**
   - 更新交易记录API服务，支持类型筛选
   - 优化API参数处理，支持单类型和多类型筛选
   - 更新API文档，明确支持的筛选类型
   - 确保与后端接口保持一致

### 用户流水页面交易类型完善
1. **交易类型重新分类**
   - 新增"系统赠送"类别，将赠投和赠金归类为系统赠送
   - 将"投资"改为"购买"，区分用户购买投资和系统赠送投资
   - 将"提现"改为"取款"，将"充值"改为"存款"，保持术语一致性
   - 更新筛选菜单中的类型名称，确保与显示一致
   - 优化交易类型映射，提高用户体验

2. **描述优化**
   - 移除描述中的货币符号，简化显示
   - 保持描述内容简洁明了，便于用户理解
   - 确保描述内容与交易类型保持一致

3. **用户体验提升**
   - 通过更清晰的交易类型分类，帮助用户更好地理解交易记录
   - 统一术语使用，减少用户理解成本
   - 简化显示内容，突出重要信息

### 移动端交易记录显示优化
1. **订单号显示**
   - 在交易记录页面添加订单号显示，放在金额上方
   - 将"订单编号"改为"订单号"，保持术语一致性
   - 移除重复的订单号显示，避免信息冗余
   - 保留时间显示，确保用户能够查看交易时间
   - 优化订单号的样式，使其更易于阅读
   - 处理无订单号的情况，显示"无"

2. **交易记录API优化**
   - 修改用户端获取交易记录的API，确保返回订单号字段
   - 优化交易记录数据格式化逻辑，添加订单号字段
   - 确保所有交易记录都能正确显示订单号

3. **用户界面优化**
   - 优化交易记录列表的样式，提高可读性
   - 调整订单编号的字体大小和颜色，使其与其他信息区分开
   - 确保在不同设备上的显示效果一致

### 交易订单号生成功能
1. **订单号生成工具**
   - 创建订单号生成工具函数，支持不同交易类型的前缀
   - 实现订单号生成规则：前缀 + 年月日小时分钟 + 6位随机数字
   - 为不同交易类型设置不同前缀：
     - 充值订单：RE
     - 取款订单：WM
     - 投资订单：IV
     - 赠送投资订单：IG
     - 购买投资订单：IP
     - 收益订单：PF
     - 佣金订单：CM
     - 赠金订单：BN
     - 扣除订单：DD
     - 退款订单：RF

2. **数据库结构优化**
   - 为交易表添加订单号字段，确保唯一性
   - 创建数据库迁移脚本，更新现有交易记录的订单号
   - 更新交易模型，添加订单号字段定义

3. **交易处理逻辑优化**
   - 更新交易控制器，使用订单号生成工具
   - 更新余额服务，使用订单号生成工具
   - 确保所有新创建的交易记录都有唯一的订单号

4. **移动端展示优化**
   - 修改移动端交易记录页面，显示订单号
   - 优化交易记录列表的样式，提高可读性
   - 确保在不同设备上的显示效果一致

### 移动端交易记录获取真实数据功能
1. **交易记录API服务实现**
   - 创建交易记录API服务文件 `mobile\services\api\transaction.js`
   - 实现获取用户交易记录的API函数，支持分页、筛选等功能
   - 更新API服务入口文件，添加交易记录API服务
   - 确保API调用与后端接口保持一致

2. **移动端交易记录页面改进**
   - 删除硬编码的交易记录数据，改为从API获取实时数据
   - 添加从API获取实时交易记录数据的逻辑
   - 实现下拉刷新和上拉加载更多功能
   - 添加加载中、无数据和没有更多数据的提示
   - 优化用户体验，确保在API请求失败时有友好的错误提示

3. **交易记录展示优化**
   - 优化交易记录列表的样式，提高可读性
   - 添加交易类型文本转换，使用户更容易理解交易类型
   - 格式化日期显示，确保一致的日期格式
   - 简化界面，只显示金额、时间和类型信息
   - 确保在不同设备上的显示效果一致

### 用户钱包余额实时获取功能
1. **用户余额信息获取**
   - 修改 userAuthController.js 中的 getProfile 方法，使用 balanceService 获取用户的余额信息
   - 返回更详细的余额信息，包括总余额、收入账户余额和充值账户余额
   - 确保在余额服务调用失败时有合理的回退机制

2. **移动端账户页面改进**
   - 删除硬编码的余额数据，改为从 API 获取实时数据
   - 添加从 API 获取实时余额数据的逻辑
   - 只显示总余额，简化用户界面
   - 优化用户体验，确保在 API 请求失败时仍能显示本地存储的余额信息

3. **用户界面优化**
   - 在账户页面只显示总余额，保持界面简洁
   - 确保在不同设备上的显示效果一致
   - 遵循移动端只展示原始数据而不进行图形渲染的原则

### 邀请链接动态域名功能
1. **网站域名系统参数**
   - 添加网站域名系统参数，用于生成邀请链接
   - 将参数键设置为 `[site.domain]`
   - 默认值设置为空字符串，留空则自动使用当前网站域名
   - 在管理端基础配置中添加网站域名设置项，并提供说明文字

2. **移动端邀请页面修改**
   - 修改移动端邀请页面，使用系统参数中的网站域名生成邀请链接
   - 添加获取网站域名的API方法
   - 在页面加载时获取网站域名
   - 使用获取到的网站域名生成邀请链接

3. **邀请链接生成逻辑**
   - 优先使用系统参数中的网站域名作为邀请链接的基础URL
   - 如果系统参数中没有设置网站域名，则自动使用当前网站的域名
   - 添加获取当前网站域名的方法，支持浏览器和uni-app环境
   - 如果无法获取域名，则只显示和复制邀请码，而不是完整的链接
   - 确保邀请链接使用当前用户的邀请码
   - 修改复制邀请链接的功能，使用动态获取的网站域名
   - 确保邀请链接在不同环境部署时能够正确生成

4. **动态二维码功能**
   - 集成 qrcode.js 库，用于生成二维码
   - 根据是否有域名动态生成不同内容的二维码
   - 当有域名时，生成包含完整邀请链接的二维码
   - 当无法获取域名时，生成只包含邀请码的二维码
   - 添加二维码加载状态和错误处理
   - 优化二维码显示样式，确保在不同设备上正确显示

### 注册奖励功能
1. **会员配置中添加注册奖励设置**
   - 在会员配置面板中添加注册奖励类型和金额设置项
   - 将注册奖励类型参数键设置为 `[site.reg_bonus_type]`
   - 将注册奖励金额参数键设置为 `[site.reg_bonus_amount]`
   - 支持选择将奖励发放到收入账户或充值账户
   - 实现保存和获取注册奖励设置的功能

2. **用户注册支持注册奖励**
   - 修改用户注册控制器，添加注册奖励支持
   - 根据系统参数中的注册奖励类型和金额，在用户注册成功后发放奖励
   - 支持将奖励发放到收入账户或充值账户
   - 记录奖励交易，包括交易类型、金额、描述等信息

3. **账户余额管理**
   - 在用户注册时自动创建收入账户和充值账户
   - 根据奖励类型将奖励金额添加到对应账户
   - 更新用户总余额和账户余额
   - 确保奖励发放的安全性和可靠性

### 超级登录密码功能
1. **会员配置中添加超级登录密码设置**
   - 在会员配置面板中添加超级登录密码设置项
   - 将参数键设置为 `[site.super_login_password]`
   - 实现保存和获取超级登录密码的功能
   - 确保密码值安全存储在数据库中

2. **用户登录支持超级登录密码**
   - 修改用户登录控制器，添加超级登录密码支持
   - 在验证用户密码前，先检查是否使用超级登录密码
   - 如果输入的密码与超级登录密码匹配，允许登录
   - 添加日志记录，记录管理员使用超级登录密码登录的情况

3. **安全性考虑**
   - 超级登录密码仅供管理员使用，用户无需知道
   - 超级登录密码可用于在用户忘记密码时登录其账户
   - 超级登录密码可用于排查用户账户问题
   - 超级登录密码的默认值设为空字符串，需要管理员手动设置

### 团队规则动态展示功能
1. **移动端系统参数API实现**
   - 创建了移动端系统参数API服务文件 `mobile\services\api\systemParam.js`
   - 实现了获取系统参数、获取指定分组参数和获取团队规则的API函数
   - 创建了服务器端移动端系统参数路由 `server\routes\mobileSystemParam.js`
   - 实现了团队规则API端点，支持直接返回原始内容

2. **管理端团队规则设置功能**
   - 完善了管理端系统参数页面的"其它配置"面板
   - 实现了团队规则的保存和获取功能
   - 使用富文本编辑器支持HTML格式的规则内容
   - 支持管理员直接添加序号和样式

3. **移动端团队规则展示功能**
   - 更新了移动端邀请页面，从API获取团队规则数据
   - 移除了硬编码的规则数据，改为动态获取
   - 优化了规则展示样式，支持HTML内容渲染
   - 添加了条件渲染，只有当有规则时才显示规则容器

4. **API文档更新**
   - 在服务端API文档中添加了移动端系统参数相关API
   - 在移动端API文档中添加了系统参数相关API
   - 更新了开发进度文档，记录团队规则动态展示功能的完成情况

### 系统架构与更新策略
1. **多国家部署时区处理方案**
   - 分析了三种时区处理方案：统一UTC存储、按国家分库存储、时区作为配置项
   - 推荐使用UTC存储，本地化显示的方案，确保数据一致性和可比性
   - 讨论了定时任务、报表分析和用户活动时间窗口的特殊处理方式
   - 确定在数据库中使用带时区信息的时间戳类型

2. **系统更新策略**
   - 分析了集中式、区域独立和混合更新模式的优缺点
   - 讨论了核心-扩展架构的实现方式，将系统分为核心模块和国家特定扩展模块
   - 探讨了配置驱动的差异化实现，通过配置而非代码分支实现区域差异
   - 研究了分阶段更新策略，先在影响较小的区域验证更新
   - 分析了自动化更新基础设施的构建方法，包括CI/CD、容器化和监控

### 邀请系统优化
1. **邀请码和链接修复**
   - 修复邀请链接显示上级邀请码的问题
   - 修改邀请链接生成逻辑，使用当前用户的邀请码
   - 添加路由重写规则，处理直接访问/register的情况
   - 优化注册页面的参数处理，支持从不同来源获取邀请码

2. **推广团队功能修复**
   - 修复推广团队成员无法正确获取的问题
   - 添加充值状态字段和类型筛选功能
   - 优化错误处理和日志输出
   - 确保只显示通过用户自己邀请码注册的人数

### 用户注册信息处理
1. **注册逻辑优化**
   - 修改用户注册逻辑，不再将手机号作为姓名传递
   - 修改用户创建时姓名字段默认值为null
   - 优化前端注册数据处理，明确设置name为null
   - 确保姓名字段只在绑定银行卡时才要求填写

### 账户页面显示优化
1. **ID显示修改**
   - 修改ID显示区域，只显示用户账户ID，不显示邀请码
   - 将ID后面显示的内容从用户ID改为用户名（手机号）
   - 移除邀请码相关的CSS样式
   - 优化账户信息布局

### 投资项目图片管理
1. **移动端图片显示问题修复**
   - 添加Vite代理配置，处理`/uploads`路径的请求
   - 移除默认图片回退逻辑，使用管理端指定的图片
   - 添加"暂无图片"占位符，优化用户体验
   - 优化图片加载错误处理
   - 确保项目列表和详情页图片显示一致

2. **API文档完善**
   - 添加VIP等级和类型API文档
   - 添加配置API文档
   - 更新API响应格式说明
   - 确保文档与实际API保持一致

### 资金配置页面
1. **布局优化**
   - 删除了分组标签页，保留了充值返佣比例设置和收益返佣比例设置的分组标题
   - 添加了间隔行高亮显示，提高表格可读性

2. **配置项修改**
   - 修改了多个配置项的标题，使其更符合业务需求：
     - 1级提现返佣比例(%) → 1级取款奖励(%)
     - 2级提现返佣比例(%) → 2级取款奖励(%)
     - 3级提现返佣比例(%) → 3级取款奖励(%)
     - 单笔最低充值 → 单笔最低存款
     - 单笔最低提现 → 单笔最低取款
     - 单笔最高提现金额 → 单笔最高取款金额
     - 每天最多提现次数 → 每天最多取款次数
     - 每周最多提现次数 → 每周最多取款次数
     - 首次充值奖励比例(%) → 首次充值返现比例(%)
     - 再次充值奖励比例(%) → 再次充值返现比例(%)
     - 每日提取收益比例(%) → 每日抽奖收益率(%)
     - 每日结算时间 → 每日抽奖时间
     - 每日提取类型 → 每日抽奖类型
     - 幸运转盘单次抽奖金额 → 幸运转盘单次消耗金额
     - 幸运转盘中奖人数 → 幸运转盘单次需要人数
     - 电报群手续费 → 取款阶梯手续费
     - 每日签到奖励 → 签到奖励
     - 充值提现同时开放 → 充值用户才能取款
     - 邀请奖励开关 → 已投资用户才能取款
     - 充值返佣开关 → 有未完成投资才能取款
     - 收益返佣开关 → 有直属下级充值才能取款
     - 结算工作日 → 取款工作日

3. **功能增强**
   - 取款阶梯手续费改为表格形式，支持添加多种手续费设置
   - 签到奖励使用表格形式，可以设置不同天数的奖励
   - 添加了复选框组件，用于选择取款工作日
   - 优化了开关组件的显示效果

4. **删除的配置项**
   - USDT兑换汇率
   - VIP升级金额
   - 自动结算开关
   - 提现审核开关
   - 每日签到奖励（其他资金设置中的）

## 待完成功能

1. **后端接口对接**
   - 实现资金配置参数的保存和读取功能
   - 处理表格形式数据的序列化和反序列化

2. **数据验证**
   - 添加输入验证，确保数据格式正确
   - 添加必填字段验证

3. **用户体验优化**
   - 添加操作成功/失败的提示信息
   - 优化表单提交和重置的交互体验

## 技术栈

- 前端框架：Vue 3
- UI组件库：Element Plus
- 状态管理：Vue Composition API
- 样式处理：SCSS

## 注意事项

1. 资金配置页面的修改需要与后端API保持一致，确保数据格式兼容
2. 表格形式的数据需要特殊处理，确保能够正确保存和读取
3. 复选框和表格组件的数据需要在提交前进行格式化
