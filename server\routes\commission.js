/**
 * 佣金路由
 */
const express = require('express');
const router = express.Router();
const commissionController = require('../controllers/commissionController');
const { verifyAdminToken, verifyUserToken } = require('../middlewares/authMiddleware');

// 管理员端路由
const adminRouter = express.Router();

// 所有管理员端路由都需要验证token
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/commissions:
 *   get:
 *     summary: 获取佣金记录列表
 *     tags: [佣金管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         description: 接收佣金的用户ID
 *       - in: query
 *         name: from_user_id
 *         schema:
 *           type: integer
 *         description: 产生佣金的用户ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: 佣金类型
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', commissionController.getCommissions);

/**
 * @swagger
 * /api/admin/commissions/{id}:
 *   get:
 *     summary: 获取佣金记录详情
 *     tags: [佣金管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 佣金记录ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 佣金记录不存在
 */
adminRouter.get('/:id', commissionController.getCommission);

/**
 * @swagger
 * /api/admin/commissions/stats:
 *   get:
 *     summary: 获取佣金统计
 *     tags: [佣金管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         description: 接收佣金的用户ID
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/stats', commissionController.getCommissionStats);

// 用户端路由
const userRouter = express.Router();

// 所有用户端路由都需要验证token
userRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/user/commissions:
 *   get:
 *     summary: 获取我的佣金记录
 *     tags: [用户佣金]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: 佣金类型
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
userRouter.get('/', commissionController.getUserCommissions);

/**
 * @swagger
 * /api/user/commissions/stats:
 *   get:
 *     summary: 获取我的佣金统计
 *     tags: [用户佣金]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
userRouter.get('/stats', commissionController.getUserCommissionStats);

module.exports = {
  admin: adminRouter,
  user: userRouter
};
