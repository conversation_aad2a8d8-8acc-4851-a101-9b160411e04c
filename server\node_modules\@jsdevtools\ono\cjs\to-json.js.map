{"version": 3, "file": "to-json.js", "sourceRoot": "", "sources": ["../src/to-json.ts"], "names": [], "mappings": ";;;AAEA,MAAM,YAAY,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AACzD,MAAM,cAAc,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AACjE,MAAM,eAAe,GAAG,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;AAElD;;;GAGG;AACH,SAAgB,MAAM;IACpB,4EAA4E;IAC5E,0DAA0D;IAC1D,IAAI,IAAI,GAAQ,EAAE,CAAC;IACnB,IAAI,KAAK,GAAG,IAAW,CAAC;IAExB,KAAK,IAAI,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;QAClC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,IAAI,GAAG,OAAO,KAAK,CAAC;YAExB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aACnB;SACF;KACF;IAED,OAAO,IAAqB,CAAC;AAC/B,CAAC;AAlBD,wBAkBC;AAGD;;;GAGG;AACH,SAAgB,WAAW,CAAC,GAAW,EAAE,OAA+B,EAAE;IACxE,IAAI,IAAI,GAA2B,EAAE,CAAC;IAEtC,oEAAoE;IACpE,OAAO,GAAG,IAAI,GAAG,KAAK,eAAe,EAAE;QACrC,IAAI,GAAG,IAAI,CAAC,MAAM,CAChB,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAC/B,MAAM,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAClC,CAAC;QACF,GAAG,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAW,CAAC;KAC5C;IAED,gCAAgC;IAChC,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAE/B,0BAA0B;IAC1B,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;QAC3C,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACxB;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AArBD,kCAqBC"}