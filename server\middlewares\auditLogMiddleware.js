const fs = require('fs');
const path = require('path');
const { createLogger, format, transports } = require('winston');
const { combine, timestamp, printf } = format;

// 确保日志目录存在
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 创建日志格式
const logFormat = printf(({ level, message, timestamp }) => {
  return `${timestamp} ${level}: ${message}`;
});

// 创建Winston日志记录器
const logger = createLogger({
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    logFormat
  ),
  transports: [
    new transports.Console(),
    new transports.File({ filename: path.join(logDir, 'audit.log') }),
    new transports.File({ filename: path.join(logDir, 'error.log'), level: 'error' })
  ]
});

/**
 * 审计日志中间件
 * 记录所有请求和响应信息
 */
const auditLogMiddleware = (req, res, next) => {
  // 获取请求开始时间
  const startTime = Date.now();
  
  // 获取用户信息
  const user = req.admin ? `${req.admin.username}(ID:${req.admin.id})` : 'anonymous';
  
  // 记录请求信息
  const requestInfo = {
    timestamp: new Date().toISOString(),
    user,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    referer: req.headers.referer || '',
    requestBody: req.method !== 'GET' ? JSON.stringify(req.body) : ''
  };
  
  // 记录请求日志
  logger.info(`REQUEST: ${JSON.stringify(requestInfo)}`);
  
  // 捕获响应
  const originalSend = res.send;
  res.send = function(body) {
    // 计算请求处理时间
    const duration = Date.now() - startTime;
    
    // 记录响应信息
    const responseInfo = {
      timestamp: new Date().toISOString(),
      user,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: Buffer.isBuffer(body) ? body.length : (typeof body === 'string' ? body.length : JSON.stringify(body).length)
    };
    
    // 记录响应日志
    logger.info(`RESPONSE: ${JSON.stringify(responseInfo)}`);
    
    // 如果是错误响应，记录错误日志
    if (res.statusCode >= 400) {
      logger.error(`ERROR: ${JSON.stringify({
        ...responseInfo,
        response: typeof body === 'string' ? body : JSON.stringify(body)
      })}`);
    }
    
    // 调用原始的send方法
    originalSend.call(this, body);
  };
  
  next();
};

// 记录未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error(`UNCAUGHT EXCEPTION: ${error.message}`, { stack: error.stack });
});

// 记录未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  logger.error(`UNHANDLED REJECTION: ${reason}`);
});

module.exports = auditLogMiddleware;
