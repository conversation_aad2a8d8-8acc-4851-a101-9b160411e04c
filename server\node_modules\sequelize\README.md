<p align="center">
  <img src="logo.svg" width="100" alt="Sequelize logo" />
  <h1 align="center"><a href="https://sequelize.org">Sequelize</a></h1>
</p>

[![npm version](https://badgen.net/npm/v/sequelize)](https://www.npmjs.com/package/sequelize)
[![Build Status](https://github.com/sequelize/sequelize/workflows/CI/badge.svg)](https://github.com/sequelize/sequelize/actions?query=workflow%3ACI)
[![npm downloads](https://badgen.net/npm/dm/sequelize)](https://www.npmjs.com/package/sequelize)
[![contributors](https://img.shields.io/github/contributors/sequelize/sequelize)](https://github.com/sequelize/sequelize/graphs/contributors)
[![Open Collective](https://img.shields.io/opencollective/backers/sequelize)](https://opencollective.com/sequelize#section-contributors)
[![sponsor](https://img.shields.io/opencollective/all/sequelize?label=sponsors)](https://opencollective.com/sequelize)
[![Merged PRs](https://badgen.net/github/merged-prs/sequelize/sequelize)](https://github.com/sequelize/sequelize)
[![semantic-release](https://img.shields.io/badge/%20%20%F0%9F%93%A6%F0%9F%9A%80-semantic--release-e10079.svg)](https://github.com/semantic-release/semantic-release)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

Sequelize is an easy-to-use and promise-based [Node.js](https://nodejs.org/en/about/) [ORM tool](https://en.wikipedia.org/wiki/Object-relational_mapping) for [Postgres](https://en.wikipedia.org/wiki/PostgreSQL), [MySQL](https://en.wikipedia.org/wiki/MySQL), [MariaDB](https://en.wikipedia.org/wiki/MariaDB), [SQLite](https://en.wikipedia.org/wiki/SQLite), [DB2](https://en.wikipedia.org/wiki/IBM_Db2_Family), [Microsoft SQL Server](https://en.wikipedia.org/wiki/Microsoft_SQL_Server), and [Snowflake](https://www.snowflake.com/). It features solid transaction support, relations, eager and lazy loading, read replication and more.

Would you like to contribute? Read [our contribution guidelines](https://github.com/sequelize/sequelize/blob/main/CONTRIBUTING.md) to know more. There are many ways to help! 😃

## 🚀 Seeking New Maintainers for Sequelize! 🚀  

We're looking for new maintainers to help finalize and release the next major version of Sequelize! If you're passionate about open-source and database ORMs, we'd love to have you onboard.  

### 💰 Funding Available  
We distribute **$2,500 per quarter** among maintainers and have additional funds for full-time contributions.  

### 🛠️ What You’ll Work On  
- Finalizing and releasing Sequelize’s next major version  
- Improving TypeScript support and database integrations  
- Fixing critical issues and shaping the ORM’s future  

### 🤝 How to Get Involved  
Interested? Join our Slack and reach out to **@WikiRik** or **@sdepold**:  
➡️ **[sequelize.org/slack](https://sequelize.org/slack)**  

We’d love to have you on board! 🚀  

## :computer: Getting Started

Ready to start using Sequelize? Head to [sequelize.org](https://sequelize.org) to begin!

- [Our Getting Started guide for Sequelize 6 (stable)](https://sequelize.org/docs/v6/getting-started)

## :money_with_wings: Supporting the project

Do you like Sequelize and would like to give back to the engineering team behind it?

We have recently created an [OpenCollective based money pool](https://opencollective.com/sequelize) which is shared amongst all core maintainers based on their contributions. Every support is wholeheartedly welcome. ❤️

## :pencil: Major version changelog

Please find upgrade information to major versions here:

- [Upgrade from v5 to v6](https://sequelize.org/docs/v6/other-topics/upgrade-to-v6)

## :book: Resources

- [Documentation](https://sequelize.org)
- [Databases Compatibility Table](https://sequelize.org/releases/)
- [Changelog](https://github.com/sequelize/sequelize/releases)
- [Discussions](https://github.com/sequelize/sequelize/discussions)
- [Slack](https://sequelize.org/slack)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/sequelize.js)

### :wrench: Tools

- [CLI](https://github.com/sequelize/cli)
- [With TypeScript](https://sequelize.org/docs/v6/other-topics/typescript)
- [Enhanced TypeScript with decorators](https://github.com/RobinBuschmann/sequelize-typescript)
- [For GraphQL](https://github.com/mickhansen/graphql-sequelize)
- [For CockroachDB](https://github.com/cockroachdb/sequelize-cockroachdb)
- [Awesome Sequelize](https://sequelize.org/docs/v6/other-topics/resources/)
- [For YugabyteDB](https://github.com/yugabyte/sequelize-yugabytedb)

### :speech_balloon: Translations

- [English](https://sequelize.org) (Official)
- [中文文档](https://github.com/demopark/sequelize-docs-Zh-CN) (Unofficial)

## :warning: Responsible disclosure

If you have security issues to report, please refer to our
[Responsible Disclosure Policy](https://github.com/sequelize/sequelize/blob/main/SECURITY.md) for more details.
