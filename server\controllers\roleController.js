const { Role, Permission, RolePermission } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 获取角色列表
exports.getRoles = async (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, status } = req.query;

    // 构建查询条件
    const where = {};

    if (keyword) {
      where.name = { [Op.like]: `%${keyword}%` };
    }

    if (status !== undefined) {
      where.status = status === 'true';
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Role.findAndCountAll({
      where,
      order: [['id', 'ASC']],
      offset,
      limit: parseInt(limit)
    });

    // 获取每个角色的权限
    const roles = await Promise.all(rows.map(async (role) => {
      const rolePermissions = await RolePermission.findAll({
        where: { role_id: role.id },
        attributes: ['permission_id']
      });

      const permissions = rolePermissions.map(rp => rp.permission_id);

      return {
        ...role.toJSON(),
        permissions
      };
    }));

    console.log('角色列表数据:', JSON.stringify({
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      items: roles
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: roles
      }
    });
  } catch (error) {
    console.error('获取角色列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取角色详情
exports.getRole = async (req, res) => {
  try {
    const { id } = req.params;

    const role = await Role.findByPk(id);

    if (!role) {
      return res.status(404).json({
        code: 404,
        message: '角色不存在',
        data: null
      });
    }

    // 获取角色的权限
    const rolePermissions = await RolePermission.findAll({
      where: { role_id: role.id },
      attributes: ['permission_id']
    });

    const permissions = rolePermissions.map(rp => rp.permission_id);

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        ...role.toJSON(),
        permissions
      }
    });
  } catch (error) {
    console.error('获取角色详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 创建角色
exports.createRole = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { name, description, permissions, status } = req.body;

    // 验证请求数据
    if (!name) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '角色名称不能为空',
        data: null
      });
    }

    // 检查角色名称是否已存在
    const existingRole = await Role.findOne({ where: { name } });
    if (existingRole) {
      await transaction.rollback();
      return res.status(409).json({
        code: 409,
        message: '角色名称已存在',
        data: null
      });
    }

    // 创建角色
    const role = await Role.create({
      name,
      description,
      status: status !== undefined ? status : true
    }, { transaction });

    // 分配权限
    if (permissions && permissions.length > 0) {
      const rolePermissions = permissions.map(permission_id => ({
        role_id: role.id,
        permission_id
      }));

      await RolePermission.bulkCreate(rolePermissions, { transaction });
    }

    await transaction.commit();

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: {
        id: role.id,
        name: role.name,
        description: role.description,
        status: role.status,
        permissions: permissions || [],
        created_at: role.createdAt
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('创建角色错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新角色
exports.updateRole = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { name, description, permissions, status } = req.body;

    // 查找角色
    const role = await Role.findByPk(id);
    if (!role) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '角色不存在',
        data: null
      });
    }

    // 超级管理员角色不能修改
    if (role.id === 1) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '超级管理员角色不能修改',
        data: null
      });
    }

    // 检查角色名称是否已存在
    if (name && name !== role.name) {
      const existingRole = await Role.findOne({ where: { name } });
      if (existingRole) {
        await transaction.rollback();
        return res.status(409).json({
          code: 409,
          message: '角色名称已存在',
          data: null
        });
      }
    }

    // 更新角色信息
    if (name) role.name = name;
    if (description !== undefined) role.description = description;
    if (status !== undefined) role.status = status;

    await role.save({ transaction });

    // 更新权限
    if (permissions) {
      // 删除旧的权限
      await RolePermission.destroy({
        where: { role_id: role.id },
        transaction
      });

      // 添加新的权限
      if (permissions.length > 0) {
        const rolePermissions = permissions.map(permission_id => ({
          role_id: role.id,
          permission_id
        }));

        await RolePermission.bulkCreate(rolePermissions, { transaction });
      }
    }

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: {
        id: role.id,
        name: role.name,
        description: role.description,
        status: role.status,
        permissions: permissions || [],
        updated_at: role.updatedAt
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新角色错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除角色
exports.deleteRole = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // 查找角色
    const role = await Role.findByPk(id);
    if (!role) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '角色不存在',
        data: null
      });
    }

    // 超级管理员角色不能删除
    if (role.id === 1) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '超级管理员角色不能删除',
        data: null
      });
    }



    // 删除角色的权限关联
    await RolePermission.destroy({
      where: { role_id: role.id },
      transaction
    });

    // 删除角色
    await role.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('删除角色错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取所有权限
exports.getAllPermissions = async (req, res) => {
  try {
    const permissions = await Permission.findAll({
      order: [['type', 'ASC'], ['name', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: permissions
    });
  } catch (error) {
    console.error('获取权限列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
