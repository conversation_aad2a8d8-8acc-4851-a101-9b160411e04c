const { Bank, BankChannelMapping, PaymentChannel } = require('../models');
const sequelize = require('../config/database');

const initBankMappingData = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('开始初始化银行编码映射数据...');
    
    // 获取所有银行
    const banks = await Bank.findAll();
    
    if (banks.length === 0) {
      console.error('没有找到银行数据，请先运行create_bank_tables.sql脚本');
      await transaction.rollback();
      return;
    }
    
    console.log(`找到${banks.length}家银行`);
    
    // 获取所有支付通道
    const paymentChannels = await PaymentChannel.findAll();
    
    if (paymentChannels.length === 0) {
      console.error('没有找到支付通道数据');
      await transaction.rollback();
      return;
    }
    
    console.log(`找到${paymentChannels.length}个支付通道`);
    
    // 检查是否已有映射数据
    const mappingCount = await BankChannelMapping.count();
    
    if (mappingCount > 0) {
      console.log(`已存在${mappingCount}条银行编码映射数据，跳过初始化`);
      await transaction.rollback();
      return;
    }
    
    // 为每个银行和支付通道创建默认映射
    const mappings = [];
    
    for (const bank of banks) {
      for (const channel of paymentChannels) {
        // 根据银行和通道生成默认编码
        let defaultCode;
        
        if (channel.code === 'pay1') {
          // pay1通道的编码格式
          defaultCode = bank.name.substring(0, 5).replace(/\s+/g, '').toUpperCase() + '01';
        } else {
          // 其他通道的编码格式
          defaultCode = bank.name.replace(/\s+/g, '_').toUpperCase();
        }
        
        mappings.push({
          bank_id: bank.id,
          payment_channel_id: channel.id,
          bank_code: defaultCode,
          status: true
        });
      }
    }
    
    await BankChannelMapping.bulkCreate(mappings, { transaction });
    
    await transaction.commit();
    console.log(`银行编码映射数据初始化完成：创建了${mappings.length}条映射记录`);
  } catch (error) {
    await transaction.rollback();
    console.error('银行编码映射数据初始化失败:', error);
  }
};

// 执行初始化
initBankMappingData();
