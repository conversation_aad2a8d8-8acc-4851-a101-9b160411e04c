{"version": 3, "file": "form2.mjs", "sources": ["../../../../../../packages/components/form/src/form.vue"], "sourcesContent": ["<template>\n  <form :class=\"formClasses\">\n    <slot />\n  </form>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, reactive, toRefs, watch } from 'vue'\nimport { debugWarn, isFunction } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from './hooks'\nimport { formContextKey } from './constants'\nimport { formEmits, formProps } from './form'\nimport { filterFields, useFormLabelWidth } from './utils'\n\nimport type { ValidateFieldsError } from 'async-validator'\nimport type { Arrayable } from '@element-plus/utils'\nimport type {\n  FormContext,\n  FormItemContext,\n  FormValidateCallback,\n  FormValidationResult,\n} from './types'\nimport type { FormItemProp } from './form-item'\n\nconst COMPONENT_NAME = 'ElForm'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(formProps)\nconst emit = defineEmits(formEmits)\n\nconst fields: FormItemContext[] = []\n\nconst formSize = useFormSize()\nconst ns = useNamespace('form')\nconst formClasses = computed(() => {\n  const { labelPosition, inline } = props\n  return [\n    ns.b(),\n    // todo: in v2.2.0, we can remove default\n    // in fact, remove it doesn't affect the final style\n    ns.m(formSize.value || 'default'),\n    {\n      [ns.m(`label-${labelPosition}`)]: labelPosition,\n      [ns.m('inline')]: inline,\n    },\n  ]\n})\n\nconst getField: FormContext['getField'] = (prop) => {\n  return fields.find((field) => field.prop === prop)\n}\n\nconst addField: FormContext['addField'] = (field) => {\n  fields.push(field)\n}\n\nconst removeField: FormContext['removeField'] = (field) => {\n  if (field.prop) {\n    fields.splice(fields.indexOf(field), 1)\n  }\n}\n\nconst resetFields: FormContext['resetFields'] = (properties = []) => {\n  if (!props.model) {\n    debugWarn(COMPONENT_NAME, 'model is required for resetFields to work.')\n    return\n  }\n  filterFields(fields, properties).forEach((field) => field.resetField())\n}\n\nconst clearValidate: FormContext['clearValidate'] = (props = []) => {\n  filterFields(fields, props).forEach((field) => field.clearValidate())\n}\n\nconst isValidatable = computed(() => {\n  const hasModel = !!props.model\n  if (!hasModel) {\n    debugWarn(COMPONENT_NAME, 'model is required for validate to work.')\n  }\n  return hasModel\n})\n\nconst obtainValidateFields = (props: Arrayable<FormItemProp>) => {\n  if (fields.length === 0) return []\n\n  const filteredFields = filterFields(fields, props)\n  if (!filteredFields.length) {\n    debugWarn(COMPONENT_NAME, 'please pass correct props!')\n    return []\n  }\n  return filteredFields\n}\n\nconst validate = async (\n  callback?: FormValidateCallback\n): FormValidationResult => validateField(undefined, callback)\n\nconst doValidateField = async (\n  props: Arrayable<FormItemProp> = []\n): Promise<boolean> => {\n  if (!isValidatable.value) return false\n\n  const fields = obtainValidateFields(props)\n  if (fields.length === 0) return true\n\n  let validationErrors: ValidateFieldsError = {}\n  for (const field of fields) {\n    try {\n      await field.validate('')\n      if (field.validateState === 'error') field.resetField()\n    } catch (fields) {\n      validationErrors = {\n        ...validationErrors,\n        ...(fields as ValidateFieldsError),\n      }\n    }\n  }\n\n  if (Object.keys(validationErrors).length === 0) return true\n  return Promise.reject(validationErrors)\n}\n\nconst validateField: FormContext['validateField'] = async (\n  modelProps = [],\n  callback\n) => {\n  const shouldThrow = !isFunction(callback)\n  try {\n    const result = await doValidateField(modelProps)\n    // When result is false meaning that the fields are not validatable\n    if (result === true) {\n      await callback?.(result)\n    }\n    return result\n  } catch (e) {\n    if (e instanceof Error) throw e\n\n    const invalidFields = e as ValidateFieldsError\n\n    if (props.scrollToError) {\n      scrollToField(Object.keys(invalidFields)[0])\n    }\n    await callback?.(false, invalidFields)\n    return shouldThrow && Promise.reject(invalidFields)\n  }\n}\n\nconst scrollToField = (prop: FormItemProp) => {\n  const field = filterFields(fields, prop)[0]\n  if (field) {\n    field.$el?.scrollIntoView(props.scrollIntoViewOptions)\n  }\n}\n\nwatch(\n  () => props.rules,\n  () => {\n    if (props.validateOnRuleChange) {\n      validate().catch((err) => debugWarn(err))\n    }\n  },\n  { deep: true, flush: 'post' }\n)\n\nprovide(\n  formContextKey,\n  reactive({\n    ...toRefs(props),\n    emit,\n\n    resetFields,\n    clearValidate,\n    validateField,\n    getField,\n    addField,\n    removeField,\n\n    ...useFormLabelWidth(),\n  })\n)\n\ndefineExpose({\n  /**\n   * @description Validate the whole form. Receives a callback or returns `Promise`.\n   */\n  validate,\n  /**\n   * @description Validate specified fields.\n   */\n  validateField,\n  /**\n   * @description Reset specified fields and remove validation result.\n   */\n  resetFields,\n  /**\n   * @description Clear validation message for specified fields.\n   */\n  clearValidate,\n  /**\n   * @description Scroll to the specified fields.\n   */\n  scrollToField,\n  /**\n   * @description All fields context.\n   */\n  fields,\n})\n</script>\n"], "names": ["fields", "props", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_renderSlot"], "mappings": ";;;;;;;;;;;mCA0Bc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAIA,IAAA,MAAM,SAA4B,EAAC,CAAA;AAEnC,IAAA,MAAM,WAAW,WAAY,EAAA,CAAA;AAC7B,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAM,MAAA,EAAE,aAAe,EAAA,MAAA,EAAW,GAAA,KAAA,CAAA;AAClC,MAAO,OAAA;AAAA,QACL,GAAG,CAAE,EAAA;AAAA,QAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,KAAA,IAAA,SAAA,CAAA;AAAA,QAAA;AAAA,UAGF,CAAA,EAAE,CAAS,CAAA,CAAA,CAAA,MAAA,EAAA,aAAkB,CAAA,CAAA,CAAA,GAAA,aAAA;AAAA,UAChC,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,GAAA,MAAA;AAAA,SAAA;AACoC,OAAA,CAAA;AAChB,KACpB,CAAA,CAAA;AAAA,IACF,MAAA,QAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACD,OAAA,MAAA,CAAA,IAAA,CAAA,CAAA,KAAA,KAAA,KAAA,CAAA,IAAA,KAAA,IAAA,CAAA,CAAA;AAED,KAAM,CAAA;AACJ,IAAA,MAAA,WAAmB,CAAA,KAAW,KAAA;AAAmB,MACnD,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,WAAiB,GAAA,CAAA,KAAA,KAAA;AAAA,MACnB,IAAA,KAAA,CAAA,IAAA,EAAA;AAEA,QAAM,MAAA,CAAA,MAAA,CAAA,MAAqD,CAAA,OAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACzD,OAAA;AACE,KAAA,CAAA;AAAsC,IACxC,MAAA,WAAA,GAAA,CAAA,UAAA,GAAA,EAAA,KAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,KAAA,EAAA;AAEA,QAAA,SAAgD,CAAA,cAAc,EAAA,4CAAO,CAAA,CAAA;AACnE,QAAI;AACF,OAAA;AACA,MAAA,YAAA,CAAA,MAAA,EAAA,UAAA,CAAA,CAAA,OAAA,CAAA,CAAA,KAAA,KAAA,KAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAA,KACF,CAAA;AACA,IAAa,MAAA,aAAA,GAAA,CAAQ,WAAY,KAAA;AAAqC,MACxE,YAAA,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA,OAAA,CAAA,CAAA,KAAA,KAAA,KAAA,CAAA,aAAA,EAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAa,MAAA,aAAA,GAAA,QAAe,CAAA,MAAA;AAAwC,MACtE,MAAA,QAAA,GAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAEA,MAAM,IAAA,CAAA,QAAA,EAAA;AACJ,QAAM,SAAA,CAAA,cAAmB,EAAA,yCAAA,CAAA,CAAA;AACzB,OAAA;AACE,MAAA,OAAA;AAAmE,KACrE,CAAA,CAAA;AACA,IAAO,MAAA,oBAAA,GAAA,CAAA,MAAA,KAAA;AAAA,MACR,IAAA,MAAA,CAAA,MAAA,KAAA,CAAA;AAED,QAAM,OAAA,EAAA,CAAA;AACJ,MAAA,MAAW,cAAA,GAAc,YAAQ,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA;AAEjC,MAAM,IAAA,CAAA,cAAA,CAAA,MAA8B,EAAA;AACpC,QAAI,wBAAwB,EAAA,4BAAA,CAAA,CAAA;AAC1B,QAAA,OAAA,EAAA,CAAU;AACV,OAAA;AAAQ,MACV,OAAA,cAAA,CAAA;AACA,KAAO,CAAA;AAAA,IACT,MAAA,QAAA,GAAA,OAAA,QAAA,KAAA,aAAA,CAAA,KAAA,CAAA,EAAA,QAAA,CAAA,CAAA;AAEA,IAAA,MAAM,eAAW,GACf,OACyB,MAAA,GAAA,EAAA,KAAA;AAE3B,MAAA,IAAM,CAAkB,aAAA,CAAA,KAAA;AAGtB,QAAI,OAAe,KAAA,CAAA;AAEnB,MAAMA,MAAAA,OAAAA,GAAS,qBAAqBC,MAAK,CAAA,CAAA;AACzC,MAAID,IAAAA,OAAAA,CAAO,MAAW,KAAA,CAAA;AAEtB,QAAA;AACA,MAAA,IAAA,gBAAoBA,GAAQ,EAAA,CAAA;AAC1B,MAAI,KAAA,MAAA,KAAA,IAAA,OAAA,EAAA;AACF,QAAM,IAAA;AACN,UAAA,MAAU,KAAA,CAAA,QAAA,CAAA,EAAA,CAAA,CAAA;AAA4C,mBACvC,CAAA,aAAA,KAAA,OAAA;AACf,YAAmB,KAAA,CAAA,UAAA,EAAA,CAAA;AAAA,SAAA,CAAA,OACd,OAAA,EAAA;AAAA,UAAA,gBACCA,GAAAA;AAAA,YACN,GAAA,gBAAA;AAAA,YACF,GAAA,OAAA;AAAA,WACF,CAAA;AAEA,SAAA;AACA,OAAO;AAA+B,MACxC,IAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,MAAA,KAAA,CAAA;AAEA,QAAA,OAAoD,IAAA,CAAA;AAIlD,MAAM,OAAA,OAAA,CAAA,MAAe,CAAA,gBAAmB,CAAA,CAAA;AACxC,KAAI,CAAA;AACF,IAAM,MAAA,aAAS,GAAM,OAAA,UAAA,GAA0B,EAAA,EAAA,QAAA,KAAA;AAE/C,MAAA,MAAI,WAAW,GAAM,CAAA,UAAA,CAAA,QAAA,CAAA,CAAA;AACnB,MAAA,IAAA;AAAuB,QACzB,MAAA,MAAA,GAAA,MAAA,eAAA,CAAA,UAAA,CAAA,CAAA;AACA,QAAO,IAAA,MAAA,KAAA,IAAA,EAAA;AAAA,iBACG,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACV,SAAI;AAEJ,QAAA,OAAsB,MAAA,CAAA;AAEtB,OAAA,CAAA,UAAU;AACR,QAAA,IAAA,CAAA,YAAqB,KAAA;AAAsB,UAC7C,MAAA,CAAA,CAAA;AACA,QAAM,MAAA,kBAAkB;AACxB,QAAO,IAAA,KAAA,CAAA,aAAuB,EAAA;AAAoB,UACpD,aAAA,CAAA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,SACF;AAEA,QAAM,OAAA,QAAA,IAAiB,IAAuB,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,EAAA,aAAA,CAAA,CAAA,CAAA;AAC5C,QAAA,OAAc,WAAA,IAAA,OAAqB,CAAA,MAAA,CAAI,aAAG,CAAA,CAAA;AAC1C,OAAA;AACE,KAAM,CAAA;AAA+C,IACvD,MAAA,aAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACF,IAAA,EAAA,CAAA;AAEA,MAAA,MAAA,KAAA,GAAA,YAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACE,SAAY,EAAA;AAAA,QACN,CAAA,EAAA,GAAA,KAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,cAAA,CAAA,KAAA,CAAA,qBAAA,CAAA,CAAA;AACJ,OAAA;AACE,KAAA,CAAA;AAAwC,IAC1C,KAAA,CAAA,MAAA,KAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACF,IAAA,KAAA,CAAA,oBAAA,EAAA;AAAA,QACE,QAAY,EAAA,CAAA,KAAA,CAAA,CAAA,GAAc,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,OAC9B;AAEA,KAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KAAA,EAAA,MAAA,EAAA,CAAA,CAAA;AAAA,IACE,OAAA,CAAA,cAAA,EAAA,QAAA,CAAA;AAAA,MACA,GAAS,MAAA,CAAA,KAAA,CAAA;AAAA,MACP,IAAA;AAAe,MACf,WAAA;AAAA,MAEA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,GAAA,iBAAA,EAAA;AAAA,KAAA,CAAA,CAAA,CAEA;AAAqB,IAAA,MACtB,CAAA;AAAA,MACH,QAAA;AAEA,MAAa,aAAA;AAAA,MAAA,WAAA;AAAA,MAAA,aAAA;AAAA,MAAA,aAAA;AAAA,MAIX,MAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAE,SAAA,EAAA,EAAAC,kBAAA,CAAA,MAAA,EAAA;AAAA,QAIA,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,WAAA,CAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,OAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KAIA,CAAA;AAAA,GAAA;AAAA,CAAA,CAAA,CAAA;AAAA,WAIA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;;;;"}