'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查callback_status字段是否存在
    const tableInfo = await queryInterface.describeTable('deposits');

    if (!tableInfo.callback_status) {
      console.log('添加callback_status字段...');
      // 添加callback_status字段
      await queryInterface.addColumn('deposits', 'callback_status', {
        type: Sequelize.ENUM('no_callback', 'mock_callback', 'channel_callback'),
        allowNull: true,
        defaultValue: 'no_callback',
        after: 'completion_time'
      });
    } else {
      console.log('callback_status字段已存在，跳过');
    }

    // 检查payment_platform_order_no字段是否存在
    if (!tableInfo.payment_platform_order_no) {
      console.log('添加payment_platform_order_no字段...');
      // 添加payment_platform_order_no字段
      await queryInterface.addColumn('deposits', 'payment_platform_order_no', {
        type: Sequelize.STRING(100),
        allowNull: true,
        after: 'callback_status'
      });
    } else {
      console.log('payment_platform_order_no字段已存在，跳过');
    }

    // 更新现有记录，设置默认回调状态
    await queryInterface.sequelize.query(`
      UPDATE deposits SET callback_status = 'no_callback' WHERE callback_status IS NULL
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // 删除callback_status字段
    await queryInterface.removeColumn('deposits', 'callback_status');

    // 删除payment_platform_order_no字段
    await queryInterface.removeColumn('deposits', 'payment_platform_order_no');
  }
};
