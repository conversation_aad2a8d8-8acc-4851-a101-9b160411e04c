{"version": 3, "file": "link2.js", "sources": ["../../../../../../packages/components/link/src/link.vue"], "sourcesContent": ["<template>\n  <a\n    :class=\"linkKls\"\n    :href=\"disabled || !href ? undefined : href\"\n    :target=\"disabled || !href ? undefined : target\"\n    @click=\"handleClick\"\n  >\n    <el-icon v-if=\"icon\"><component :is=\"icon\" /></el-icon>\n    <span v-if=\"$slots.default\" :class=\"ns.e('inner')\">\n      <slot />\n    </span>\n\n    <slot v-if=\"$slots.icon\" name=\"icon\" />\n  </a>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { linkEmits, linkProps } from './link'\n\ndefineOptions({\n  name: 'ElLink',\n})\nconst props = defineProps(linkProps)\nconst emit = defineEmits(linkEmits)\n\nconst ns = useNamespace('link')\n\nconst linkKls = computed(() => [\n  ns.b(),\n  ns.m(props.type),\n  ns.is('disabled', props.disabled),\n  ns.is('underline', props.underline && !props.disabled),\n])\n\nfunction handleClick(event: MouseEvent) {\n  if (!props.disabled) emit('click', event)\n}\n</script>\n"], "names": ["useNamespace", "computed"], "mappings": ";;;;;;;;;;uCAsBc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,MAAM,CAAA,CAAA;AAE9B,IAAM,MAAA,OAAA,GAAUC,aAAS,MAAM;AAAA,MAC7B,GAAG,CAAE,EAAA;AAAA,MACL,EAAA,CAAG,CAAE,CAAA,KAAA,CAAM,IAAI,CAAA;AAAA,MACf,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,MAChC,GAAG,EAAG,CAAA,WAAA,EAAa,MAAM,SAAa,IAAA,CAAC,MAAM,QAAQ,CAAA;AAAA,KACtD,CAAA,CAAA;AAED,IAAA,SAAS,YAAY,KAAmB,EAAA;AACtC,MAAA,IAAI,CAAC,KAAA,CAAM,QAAU;AAAmB,QAC1C,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}