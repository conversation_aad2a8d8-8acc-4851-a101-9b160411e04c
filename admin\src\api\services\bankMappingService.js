import request from '@/utils/request';

const bankMappingService = {
  // 获取银行编码映射
  getBankMappings(params) {
    return request({
      url: '/api/admin/bank-mappings',
      method: 'get',
      params
    });
  },

  // 添加/更新银行编码映射
  upsertBankMapping(data) {
    return request({
      url: '/api/admin/bank-mappings',
      method: 'post',
      data
    });
  },

  // 删除银行编码映射
  deleteBankMapping(id) {
    return request({
      url: `/api/admin/bank-mappings/${id}`,
      method: 'delete'
    });
  },

  // 获取所有银行
  getAllBanks() {
    return request({
      url: '/api/admin/banks',
      method: 'get'
    });
  },

  // 获取单个银行
  getBank(id) {
    return request({
      url: `/api/admin/banks/${id}`,
      method: 'get'
    });
  },

  // 创建银行
  createBank(data) {
    return request({
      url: '/api/admin/banks',
      method: 'post',
      data
    });
  },

  // 更新银行
  updateBank(id, data) {
    return request({
      url: `/api/admin/banks/${id}`,
      method: 'put',
      data
    });
  },

  // 更新银行状态
  updateBankStatus(id, status) {
    return request({
      url: `/api/admin/banks/${id}/status`,
      method: 'put',
      data: { status }
    });
  },

  // 删除银行
  deleteBank(id) {
    return request({
      url: `/api/admin/banks/${id}`,
      method: 'delete'
    });
  }
};

export default bankMappingService;
