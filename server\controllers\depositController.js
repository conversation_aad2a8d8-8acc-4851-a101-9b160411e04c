/**
 * 充值订单控制器
 * 处理充值订单的创建、审核、完成等操作
 */
const { Deposit, User, Transaction, BankCard, PaymentChannel } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const balanceService = require('../services/balanceService');
const commissionService = require('../services/commissionService');
const { generateOrderNumber } = require('../utils/orderUtils');

// 管理员端 - 获取充值订单列表
exports.getDeposits = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, user_id, user_id_str, start_date, end_date, keyword } = req.query;

    console.log('接收到的查询参数:', req.query);
    console.log('关键词:', keyword);
    if (keyword) {
      console.log('关键词长度:', keyword.length);
      console.log('关键词类型:', typeof keyword);
    }

    // 构建查询条件
    const where = {};

    // 构建用户关联查询条件
    const userWhere = {};
    let userRequired = false;

    // 根据状态筛选
    if (status) {
      where.status = status;
    }

    // 根据用户ID筛选
    if (user_id) {
      where.user_id = user_id;
    }

    // 根据用户ID字符串（user_id字段）筛选
    if (user_id_str) {
      userWhere.user_id = user_id_str;
      userRequired = true;
    }

    // 根据日期范围筛选
    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // 关键词搜索（订单号或用户名）
    if (keyword) {
      console.log('构建关键词搜索条件');
      console.log('关键词:', keyword);

      // 不在主查询条件中设置，而是在后面的查询中处理
      // 这样可以避免复杂的条件导致的问题
    }

    // 分页查询
    const offset = (page - 1) * limit;
    console.log('分页参数:', { page, limit, offset });

    console.log('执行数据库查询...');

    // 构建查询选项
    const options = {
      where: { ...where }, // 复制where条件
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email'],
          where: Object.keys(userWhere).length > 0 ? userWhere : undefined,
          required: userRequired
        },
        {
          model: BankCard,
          as: 'receiving_card',
          attributes: ['id', 'bank_name', 'card_number', 'card_holder']
        },
        {
          model: BankCard,
          as: 'bank_card',
          attributes: ['id', 'bank_name', 'card_number', 'card_holder']
        },
        {
          model: Transaction,
          as: 'transaction',
          attributes: ['id', 'type', 'amount', 'status', 'created_at']
        },
        {
          model: PaymentChannel,
          as: 'payment_channel',
          attributes: ['id', 'name', 'code', 'status']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit),
      subQuery: false, // 尝试禁用子查询，可能会提高性能
      distinct: true // 确保计数正确
    };

    // 如果有关键词，构建复杂的查询条件
    if (keyword) {
      console.log('添加复杂搜索条件');

      // 使用sequelize.literal构建复杂的OR条件
      // 对关键词进行转义，防止SQL注入
      const escapedKeyword = keyword.replace(/'/g, "''");

      options.where = {
        ...options.where,
        [Op.or]: [
          { order_number: { [Op.like]: `%${escapedKeyword}%` } },
          sequelize.literal(`User.username LIKE '%${escapedKeyword}%'`)
        ]
      };

      // 设置关联表为必须
      options.include[0].required = true;
    }

    console.log('执行查询，选项:', JSON.stringify(options, null, 2));
    const { count, rows } = await Deposit.findAndCountAll(options);

    console.log(`查询结果: 找到 ${count} 条记录`);
    if (rows.length > 0) {
      console.log('第一条记录:', {
        id: rows[0].id,
        order_number: rows[0].order_number,
        user_id: rows[0].user_id,
        username: rows[0].user ? rows[0].user.username : 'N/A'
      });
    } else {
      console.log('没有找到匹配的记录');
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取充值订单列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取充值订单详情
exports.getDeposit = async (req, res) => {
  try {
    const { id } = req.params;

    const deposit = await Deposit.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email']
        },
        {
          model: BankCard,
          as: 'receiving_card',
          attributes: ['id', 'bank_name', 'card_number', 'card_holder']
        },
        {
          model: Transaction,
          as: 'transaction',
          attributes: ['id', 'type', 'amount', 'status', 'created_at']
        }
      ]
    });

    if (!deposit) {
      return res.status(404).json({
        code: 404,
        message: '充值订单不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: deposit
    });
  } catch (error) {
    console.error('获取充值订单详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 创建充值订单
exports.createDeposit = async (req, res) => {
  try {
    const { user_id, amount, payment_method, remark } = req.body;

    // 验证请求数据
    if (!user_id || !amount || !payment_method) {
      return res.status(400).json({
        code: 400,
        message: '用户ID、金额和支付方式不能为空',
        data: null
      });
    }

    // 查找用户
    const user = await User.findByPk(user_id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 生成订单号
    const orderNumber = generateOrderNumber('D');

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 创建充值订单
      const deposit = await Deposit.create({
        user_id,
        order_number: orderNumber,
        amount,
        actual_amount: amount, // 实际到账金额与充值金额相同
        payment_method,
        status: 'pending', // 初始状态为待支付
        remark
      }, { transaction });

      // 提交事务
      await transaction.commit();

      return res.status(201).json({
        code: 201,
        message: '创建成功',
        data: deposit
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('创建充值订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 审核充值订单
exports.approveDeposit = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, remark } = req.body;

    // 验证请求数据
    if (!status) {
      return res.status(400).json({
        code: 400,
        message: '状态不能为空',
        data: null
      });
    }

    if (!['completed', 'cancelled'].includes(status)) {
      return res.status(400).json({
        code: 400,
        message: '状态只能是completed或cancelled',
        data: null
      });
    }

    // 查找充值订单
    const deposit = await Deposit.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user'
        }
      ]
    });

    if (!deposit) {
      return res.status(404).json({
        code: 404,
        message: '充值订单不存在',
        data: null
      });
    }

    // 检查订单状态
    if (deposit.status !== 'pending' && deposit.status !== 'paid') {
      return res.status(400).json({
        code: 400,
        message: '订单状态不正确',
        data: null
      });
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      deposit.status = status;
      deposit.remark = remark || deposit.remark;
      deposit.completion_time = new Date();

      // 如果状态为已完成，则增加用户余额
      if (status === 'completed') {
        // 使用余额服务增加用户充值账户余额
        const result = await balanceService.adjustBalance(
          deposit.user_id,
          'deposit', // 充值账户
          deposit.amount,
          'add',
          'deposit', // 交易类型为充值
          `充值 ${deposit.amount}`,
          deposit.id,
          'deposit',
          transaction
        );

        // 更新充值订单关联的交易ID
        deposit.transaction_id = result.transactionId;

        // 处理充值返佣
        await commissionService.processDepositCommission(
          deposit.user_id,
          parseFloat(deposit.amount),
          result.transactionId,
          transaction
        );
      }

      await deposit.save({ transaction });

      // 提交事务
      await transaction.commit();

      // 如果状态为已完成，触发统计数据更新（异步执行，不影响主流程）
      if (status === 'completed') {
        try {
          const statsUpdateService = require('../services/statsUpdateService');
          statsUpdateService.triggerFullStatsUpdate().catch(err => {
            console.error('充值完成后更新统计数据失败:', err);
          });
        } catch (error) {
          console.error('触发统计数据更新失败:', error);
        }
      }

      return res.status(200).json({
        code: 200,
        message: status === 'completed' ? '充值已完成' : '充值已取消',
        data: deposit
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('审核充值订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 模拟支付回调
exports.mockCallback = async (req, res) => {
  try {
    const { id } = req.params;

    // 查找充值订单
    const deposit = await Deposit.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user'
        },
        {
          model: PaymentChannel,
          as: 'payment_channel'
        }
      ]
    });

    if (!deposit) {
      return res.status(404).json({
        code: 404,
        message: '充值订单不存在',
        data: null
      });
    }

    // 检查订单状态
    if (deposit.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: `订单状态不正确，当前状态: ${deposit.status}`,
        data: null
      });
    }

    // 生成模拟的支付平台订单号
    const paymentPlatformOrderNo = `MOCK${Date.now()}${Math.floor(Math.random() * 1000)}`;

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      deposit.status = 'completed';
      deposit.payment_time = new Date();
      deposit.completion_time = new Date();
      deposit.payment_platform_order_no = paymentPlatformOrderNo;
      deposit.callback_status = 'mock_callback'; // 设置回调状态为模拟回调

      // 使用余额服务增加用户充值账户余额
      const result = await balanceService.adjustBalance(
        deposit.user_id,
        'deposit', // 充值账户
        deposit.amount,
        'add',
        'deposit', // 交易类型为充值
        `充值 ${deposit.amount}`,
        deposit.id,
        'deposit',
        transaction
      );

      // 更新充值订单关联的交易ID
      deposit.transaction_id = result.transactionId;

      // 处理充值返佣
      await commissionService.processDepositCommission(
        deposit.user_id,
        parseFloat(deposit.amount),
        result.transactionId,
        transaction
      );

      await deposit.save({ transaction });

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          console.error('模拟回调后更新统计数据失败:', err);
        });
      } catch (error) {
        console.error('触发统计数据更新失败:', error);
      }

      return res.status(200).json({
        code: 200,
        message: '模拟回调成功',
        data: deposit
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('模拟回调错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 更新支付平台订单号
exports.updatePaymentPlatformOrderNo = async (req, res) => {
  try {
    const { id } = req.params;
    const { payment_platform_order_no } = req.body;

    // 验证请求数据
    if (!payment_platform_order_no) {
      return res.status(400).json({
        code: 400,
        message: '支付平台订单号不能为空',
        data: null
      });
    }

    // 查找充值订单
    const deposit = await Deposit.findByPk(id);

    if (!deposit) {
      return res.status(404).json({
        code: 404,
        message: '充值订单不存在',
        data: null
      });
    }

    // 更新支付平台订单号
    deposit.payment_platform_order_no = payment_platform_order_no;
    await deposit.save();

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: deposit
    });
  } catch (error) {
    console.error('更新支付平台订单号错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

module.exports = exports;
