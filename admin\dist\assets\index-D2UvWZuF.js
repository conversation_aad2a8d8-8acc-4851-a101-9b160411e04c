/* empty css             *//* empty css                *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                 */import{d as v,r as c,a as h,o as E,c as V,b,e as s,w as t,f as k,E as C,u as F,g as B,h as R,i as q,j as f,k as I,l as N,m as S,n as U,p as i,_ as A}from"./index-LncY9lAB.js";import{u as K}from"./user-DIT1kost.js";import"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";const L={class:"login-container"},M={class:"login-form"},j=v({__name:"index",setup(T){const g=F(),_=K(),a=c(),n=c(!1),m=()=>{const e=navigator.userAgent.toLowerCase();return e.includes("chrome")&&!e.includes("edg")&&!e.includes("opr")},r=h({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}]},d=()=>{if(!m()){i.error("禁止访问");return}a.value&&a.value.validate(async e=>{if(e){n.value=!0;try{await _.login({username:r.username,password:r.password})&&(i.success("登录成功"),g.push({path:"/"}))}catch(o){i.error(o.message||"登录失败")}finally{n.value=!1}}})};return E(()=>{if(!m()){const e=document.querySelector(".login-form");e&&(e.style.pointerEvents="none"(e).style.opacity="0.5")}}),(e,o)=>{const p=q,l=R,x=S,y=C;return B(),V("div",L,[b("div",M,[s(y,{ref_key:"loginFormRef",ref:a,model:r,rules:w,"status-icon":"",onKeyup:k(d,["enter"])},{default:t(()=>[s(l,{prop:"username"},{default:t(()=>[s(p,{modelValue:r.username,"onUpdate:modelValue":o[0]||(o[0]=u=>r.username=u),placeholder:"用户名","prefix-icon":f(I),clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),s(l,{prop:"password"},{default:t(()=>[s(p,{modelValue:r.password,"onUpdate:modelValue":o[1]||(o[1]=u=>r.password=u),type:"password",placeholder:"密码","prefix-icon":f(N),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),s(l,null,{default:t(()=>[s(x,{type:"primary",loading:n.value,class:"login-button",onClick:d},{default:t(()=>o[2]||(o[2]=[U("登录")])),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])])])}}}),X=A(j,[["__scopeId","data-v-4c57c083"]]);export{X as default};
