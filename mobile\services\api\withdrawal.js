/**
 * 提现相关 API 服务
 */
import request from '../request';

/**
 * 创建提现订单
 * @param {Object} data - 提现数据
 * @param {number} data.amount - 提现金额
 * @param {number} data.bank_card_id - 银行卡ID
 * @returns {Promise<Object>} 创建结果
 */
export function createWithdrawal(data) {
  return request({
    url: '/api/mobile/withdrawals',
    method: 'POST',
    data,
    headers: {
      'X-Request-Source': 'mobile-app'
    }
  });
}

/**
 * 获取提现记录
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=10] - 每页数量
 * @param {string} [params.status] - 订单状态
 * @returns {Promise<Object>} 提现记录数据
 */
export function getWithdrawals(params) {
  return request({
    url: '/api/mobile/withdrawals',
    method: 'GET',
    params
  });
}

/**
 * 获取提现详情
 * @param {number} id - 提现订单ID
 * @returns {Promise<Object>} 提现详情数据
 */
export function getWithdrawalDetail(id) {
  return request({
    url: `/api/mobile/withdrawals/${id}`,
    method: 'GET'
  });
}
