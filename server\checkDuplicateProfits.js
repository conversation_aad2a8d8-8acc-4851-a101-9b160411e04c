const mysql = require('mysql2/promise');

async function checkDuplicateProfits() {
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'MySQL3352~!',
      database: 'fox_db'
    });

    console.log('数据库连接成功');

    // 查找用户
    const [users] = await connection.execute(
      'SELECT id, username, user_id FROM users WHERE username = ?',
      ['13100000029']
    );

    if (users.length === 0) {
      console.log('未找到用户 13100000029');
      await connection.end();
      return;
    }

    const user = users[0];
    console.log('找到用户:', user.id, user.username, user.user_id);

    // 查找用户的投资
    const [investments] = await connection.execute(
      'SELECT i.id, i.project_id, i.profit_count, i.last_profit_time, p.name, p.profit_time ' +
      'FROM investments i ' +
      'JOIN projects p ON i.project_id = p.id ' +
      'WHERE i.user_id = ?',
      [user.id]
    );

    console.log(`用户有 ${investments.length} 条投资记录`);

    // 查找2025-05-18的收益记录
    const [profits] = await connection.execute(
      'SELECT ip.id, ip.investment_id, ip.amount, ip.profit_time, ip.status, ip.created_at ' +
      'FROM investment_profits ip ' +
      'WHERE ip.user_id = ? AND ip.profit_time >= ? AND ip.profit_time < ? ' +
      'ORDER BY ip.profit_time ASC, ip.created_at ASC',
      [user.id, '2025-05-18 00:00:00', '2025-05-19 00:00:00']
    );

    console.log(`2025-05-18 有 ${profits.length} 条收益记录`);

    // 分析收益记录
    const profitsByTime = {};

    profits.forEach(profit => {
      // 将日期转换为字符串格式，便于比较
      const timeKey = profit.profit_time.toISOString ?
                      profit.profit_time.toISOString() :
                      new Date(profit.profit_time).toISOString();

      if (!profitsByTime[timeKey]) {
        profitsByTime[timeKey] = [];
      }
      profitsByTime[timeKey].push(profit);
    });

    // 检查是否有重复的收益时间
    for (const [timeKey, profitList] of Object.entries(profitsByTime)) {
      if (profitList.length > 1) {
        console.log(`发现重复收益时间: ${timeKey}, 共 ${profitList.length} 条记录`);

        for (let i = 0; i < profitList.length; i++) {
          const profit = profitList[i];
          console.log(`记录 ${i + 1}:`);
          console.log(`  ID: ${profit.id}`);
          console.log(`  投资ID: ${profit.investment_id}`);
          console.log(`  金额: ${profit.amount}`);
          console.log(`  状态: ${profit.status}`);
          console.log(`  创建时间: ${profit.created_at}`);

          // 查询投资和项目信息
          const [investmentInfo] = await connection.execute(
            'SELECT i.id, i.last_profit_time, p.name, p.profit_time ' +
            'FROM investments i ' +
            'JOIN projects p ON i.project_id = p.id ' +
            'WHERE i.id = ?',
            [profit.investment_id]
          );

          if (investmentInfo.length > 0) {
            const info = investmentInfo[0];
            console.log(`  项目名称: ${info.name}`);
            console.log(`  收益周期: ${info.profit_time} 小时`);
            console.log(`  上次收益时间: ${info.last_profit_time}`);
          }
        }
      }
    }

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkDuplicateProfits();
