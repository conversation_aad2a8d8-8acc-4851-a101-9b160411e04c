'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查表是否存在
      const [tables] = await queryInterface.sequelize.query(
        "SHOW TABLES LIKE 'bank_cards'"
      );

      if (tables.length === 0) {
        console.log('bank_cards表不存在，跳过修改');
        return;
      }

      // 获取表结构
      const [columns] = await queryInterface.sequelize.query(
        "SHOW COLUMNS FROM bank_cards"
      );

      // 获取所有列名
      const columnNames = columns.map(column => column.Field);

      // 1. 检查branch列是否存在，如果存在则重命名为bank_code
      if (columnNames.includes('branch')) {
        console.log('将branch列重命名为bank_code...');
        await queryInterface.renameColumn('bank_cards', 'branch', 'bank_code');
      } else if (!columnNames.includes('bank_code')) {
        // 如果branch不存在且bank_code也不存在，则添加bank_code列
        console.log('添加bank_code列...');
        await queryInterface.addColumn('bank_cards', 'bank_code', {
          type: Sequelize.STRING(100),
          allowNull: true,
          comment: '银行代码'
        });
      } else {
        console.log('bank_code列已存在，跳过重命名');
      }

      // 2. 检查is_default列是否存在，如果存在则删除
      if (columnNames.includes('is_default')) {
        console.log('删除is_default列...');
        await queryInterface.removeColumn('bank_cards', 'is_default');
      } else {
        console.log('is_default列不存在，跳过删除');
      }

      console.log('bank_cards表修改完成');
    } catch (error) {
      console.error('迁移错误:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 1. 将 bank_code 列重命名回 branch
      await queryInterface.renameColumn('bank_cards', 'bank_code', 'branch');

      // 2. 重新添加 is_default 列
      await queryInterface.addColumn('bank_cards', 'is_default', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否默认卡'
      });

      // 3. 重新添加 is_default 索引
      await queryInterface.addIndex('bank_cards', ['is_default']);
    } catch (error) {
      console.error('回滚错误:', error);
      throw error;
    }
  }
};
