/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";import{d as he,r as _,a as A,q as W,o as Pe,p as c,c as F,b as o,e as l,w as s,m as De,f as Ee,i as Ie,aa as xe,ab as Se,ac as Le,n as v,x as Ue,j as y,ad as G,aw as ze,aB as J,af as Q,ag as Re,ai as Ae,aj as Fe,ak as $e,V as P,al as Te,y as $,an as Me,ap as Ye,aq as Be,a8 as q,a9 as N,at as je,E as qe,a7 as X,h as Ne,ao as He,a<PERSON> as Oe,az as Ke,g as w,_ as We}from"./index-LncY9lAB.js";import{s as T}from"./request-Cd-6Wde0.js";import{g as Ge}from"./role-B80O1_oO.js";import"./index-t--hEgTQ.js";function M(f){const d=f.data&&f.data.code?f.data.code:200,k=f.data&&f.data.message?f.data.message:"success";return{code:d,message:k,data:f.data&&f.data.data?f.data.data:f}}function Je(f){return T({url:"/api/admin/admins",method:"get",params:f}).then(d=>M(d))}function Qe(f){return T({url:"/api/admin/admins",method:"post",data:f}).then(d=>d&&d.data&&d.data.message&&(d.data.message.includes("账号已存在")||d.data.message.includes("用户名已存在")||d.data.message.includes("already exists"))?{code:400,message:"账号已存在，请使用其他账号",data:null}:M(d)).then(d=>d&&d.__isAccountExistsError?{code:409,message:"账号已存在",data:null,__isAccountExistsError:!0}:d)}function Xe(f,d){return T({url:`/api/admin/admins/${f}`,method:"put",data:d}).then(k=>M(k))}function Z(f){return T({url:`/api/admin/admins/${f}`,method:"delete"}).then(d=>!d||!d.data?{code:200,message:"删除成功",data:null}:M(d))}const Ze={class:"admins-container"},el={class:"toolbar"},ll={class:"toolbar-left"},tl={class:"toolbar-right"},al={class:"table-wrapper"},sl={class:"operation-buttons-container"},ol={class:"pagination-container"},nl={class:"dialog-footer"},rl={class:"filter-container"},il={class:"filter-section"},dl={class:"section-content"},ul={class:"filter-grid"},ml={class:"filter-item"},cl={class:"filter-item"},fl={class:"filter-item"},pl={class:"filter-item"},gl={class:"filter-section"},vl={class:"section-content"},_l={class:"filter-grid"},wl={class:"filter-item"},bl={class:"filter-item"},yl={class:"filter-section"},Vl={class:"section-content"},kl={class:"filter-grid"},Cl={class:"filter-item"},hl={class:"filter-footer"},Pl={class:"delete-confirm-text"},Dl={class:"dialog-footer"},El=he({__name:"index",setup(f){const d=_(!1),k=_([]),C=_([]),g=A({currentPage:1,pageSize:10,total:0}),R=_(""),D=_(!1),i=A({id:"",username:"",nickname:"",roleId:null,is_super:null,status:null,lastLoginRange:null});_();const E=_([]),ee=async()=>{try{E.value=[{id:1,name:"超级管理员组"},{id:2,name:"管理员组"}];const a=await Ge({limit:100});console.log("角色数据响应:",a),a&&a.data&&a.data.items&&a.data.items.length>0?(E.value=a.data.items.map(e=>({id:e.id,name:e.name})),console.log("角色选项已更新:",E.value)):console.warn("使用默认角色选项，因为响应数据不完整")}catch(a){console.error("加载角色数据失败:",a)}},I=_(!1),U=_(!0),h=_(),m=A({id:null,username:"",nickname:"",password:"",confirmPassword:"",roleId:null,status:!0,is_super:!1}),x=_(!1),Y=_(""),B=_(()=>{}),le=A({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],nickname:[{required:!0,message:"请输入昵称",trigger:"blur"}],password:[{validator:(a,e,r)=>{e===""?r(new Error("请输入密码")):(m.confirmPassword!==""&&h.value&&h.value.validateField("confirmPassword",()=>null),r())},trigger:"blur"}],confirmPassword:[{validator:(a,e,r)=>{e===""?r(new Error("请再次输入密码")):e!==m.password?r(new Error("两次输入密码不一致")):r()},trigger:"blur"}],roleId:[{required:!0,message:"请选择所属角色",trigger:"change"}]}),H=W(()=>{let a=[...k.value];if(R.value){const e=R.value.toLowerCase();a=a.filter(r=>r.username&&r.username.toLowerCase().includes(e)||r.nickname&&r.nickname.toLowerCase().includes(e))}if(i.id&&(a=a.filter(e=>String(e.id).includes(i.id))),i.username&&(a=a.filter(e=>e.username&&e.username.toLowerCase().includes(i.username.toLowerCase()))),i.nickname&&(a=a.filter(e=>e.nickname&&e.nickname.toLowerCase().includes(i.nickname.toLowerCase()))),i.roleId!==null&&(a=a.filter(e=>e.roleId===i.roleId)),i.is_super!==null&&(a=a.filter(e=>e.is_super===i.is_super)),i.status!==null&&(a=a.filter(e=>e.status===i.status)),i.lastLoginRange&&i.lastLoginRange.length===2){const[e,r]=i.lastLoginRange;a=a.filter(n=>{const p=new Date(n.lastLogin).getTime();return p>=new Date(e).getTime()&&p<=new Date(r).getTime()})}return a}),te=W(()=>k.value);Pe(()=>{ee(),V()});const V=async()=>{d.value=!0;try{const{currentPage:a,pageSize:e}=g,n=await Je({page:a,limit:e});if(console.log("管理员列表响应:",n),n&&n.data&&n.data.items){const p=n.data.items.map(u=>{let S=null;return u.roles&&u.roles.length>0&&(S=u.roles[0].id),{id:u.id,username:u.username,nickname:u.nickname,roleId:S,is_super:u.is_super,status:u.status,lastLogin:u.last_login}});console.log("处理后的管理员数据:",p),k.value=p,g.total=n.data.total}else console.error("响应数据格式不正确:",n),c.error("加载数据失败")}catch(a){c.error("加载数据失败"),console.error(a)}finally{d.value=!1}},ae=()=>{g.currentPage=1,V()},se=a=>{C.value=a},oe=()=>{B.value(),x.value=!1},ne=()=>{if(C.value.length===0){c.warning("请选择要删除的管理员");return}if(C.value.some(e=>e.is_super)){c.warning("超级管理员账号不能被删除");return}Y.value=`确认删除选中的 ${C.value.length} 个管理员吗？此操作不可恢复。`,B.value=async()=>{try{const r=C.value.map(u=>u.id).map(u=>Z(u));(await Promise.all(r)).every(u=>u.code===200)?(c.success("批量删除成功"),C.value=[],V()):(c.warning("部分管理员删除失败"),V())}catch(e){console.error("批量删除错误:",e),c.error("批量删除失败")}},x.value=!0},re=a=>{if(a.is_super){c.warning("超级管理员账号不能被删除");return}if(a.roleId===1){c.warning("超级管理员组的管理员不能被删除");return}Y.value=`确认删除管理员 "${a.username}" 吗？此操作不可恢复。`,B.value=async()=>{try{const e=await Z(a.id);e&&e.code===200?(c.success("删除成功"),V()):c.error(e&&e.message||"删除失败")}catch(e){console.error("删除管理员错误:",e),c.success("删除成功"),V()}},x.value=!0},O=()=>{g.currentPage=1},ie=()=>{D.value=!0},de=()=>{D.value=!1,g.currentPage=1;const a=H.value.length;c.success(`筛选完成，共找到 ${a} 条符合条件的记录`)},ue=()=>{Object.keys(i).forEach(a=>{a==="id"||a==="username"||a==="nickname"?i[a]="":i[a]=null}),g.currentPage=1,c.success("筛选条件已重置")},me=()=>{U.value=!0,fe(),I.value=!0},ce=a=>{if(!a)return"-";const e=E.value.find(r=>r.id===a);return e?e.name:"-"},fe=()=>{h.value&&h.value.resetFields(),Object.assign(m,{id:null,username:"",nickname:"",password:"",confirmPassword:"",roleId:null,status:!0,is_super:!1})},pe=async()=>{if(h.value)try{if(await h.value.validate()){const e=m.roleId===1,r={username:m.username,nickname:m.nickname,password:m.password,role_id:m.roleId,is_super:e,status:m.status};if(console.log("提交的数据:",r),U.value)try{const n=await Qe(r);console.log("添加管理员响应:",n),c.success("创建成功"),V(),I.value=!1}catch(n){console.error("添加管理员错误:",n),n.response&&n.response.status===409||c.error("创建失败，请重试")}else{if(!m.id)return;const n={...r};n.password&&delete n.password;try{const p=await Xe(m.id,n);console.log("更新管理员响应:",p),c.success("更新成功"),V(),I.value=!1}catch(p){console.error("更新管理员错误:",p),c.error("更新失败，请重试")}}}}catch(a){console.error("提交表单错误:",a),c.error("提交失败")}},ge=()=>{c.info("功能尚未实现")},K=a=>{g.pageSize=a,g.currentPage>Math.ceil(H.value.length/g.pageSize)&&(g.currentPage=1)},ve=a=>{g.currentPage=a};return(a,e)=>{const r=Ue,n=De,p=Ie,u=Te,S=Me,_e=Ye,we=xe,b=je,z=Be,be=Se,L=Ne,ye=He,Ve=qe,j=Le,ke=Oe,Ce=$e;return w(),F("div",Ze,[e[47]||(e[47]=o("div",{class:"page-header"},[o("h2",{class:"page-title"},"管理员管理")],-1)),o("div",el,[o("div",ll,[l(n,{class:"toolbar-button",type:"default",onClick:ae},{default:s(()=>[l(r,null,{default:s(()=>[l(y(G))]),_:1}),e[23]||(e[23]=v("刷新 "))]),_:1}),l(n,{class:"toolbar-button",type:"primary",onClick:me},{default:s(()=>[l(r,null,{default:s(()=>[l(y(ze))]),_:1}),e[24]||(e[24]=v("添加 "))]),_:1}),l(n,{class:"toolbar-button",type:"danger",disabled:C.value.length===0,onClick:ne},{default:s(()=>[l(r,null,{default:s(()=>[l(y(J))]),_:1}),e[25]||(e[25]=v("删除 "))]),_:1},8,["disabled"])]),o("div",tl,[l(p,{modelValue:R.value,"onUpdate:modelValue":e[0]||(e[0]=t=>R.value=t),placeholder:"搜索用户名/昵称",class:"search-input",onKeyup:Ee(O,["enter"])},null,8,["modelValue"]),l(n,{class:"search-button",type:"primary",onClick:O},{default:s(()=>[l(r,null,{default:s(()=>[l(y(Q))]),_:1})]),_:1}),l(n,{class:"toolbar-button filter-button",type:"default",onClick:ie},{default:s(()=>[l(r,null,{default:s(()=>[l(y(Re))]),_:1}),e[26]||(e[26]=v("筛选 "))]),_:1}),l(n,{class:"toolbar-button export-button",type:"default",onClick:ge},{default:s(()=>[l(r,null,{default:s(()=>[l(y(Ae))]),_:1}),e[27]||(e[27]=v("导出 "))]),_:1})])]),l(we,{class:"table-card"},{default:s(()=>[o("div",al,[Fe((w(),P(_e,{ref:"adminsTable",data:te.value,border:"",stripe:"","highlight-current-row":"","row-key":"id",style:{width:"100%"},onSelectionChange:se,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:s(()=>[l(u,{type:"selection",width:"40",align:"center"}),l(u,{prop:"id",label:"ID","min-width":"70",align:"center"}),l(u,{prop:"username",label:"用户名","min-width":"120",align:"center"}),l(u,{prop:"nickname",label:"昵称","min-width":"120",align:"center"},{default:s(t=>[v($(t.row.nickname||"-"),1)]),_:1}),l(u,{label:"所属角色","min-width":"120",align:"center"},{default:s(t=>[v($(ce(t.row.roleId)),1)]),_:1}),l(u,{label:"类型",width:"100",align:"center"},{default:s(t=>[t.row.is_super?(w(),P(S,{key:0,type:"danger"},{default:s(()=>e[28]||(e[28]=[v("超级管理员")])),_:1})):(w(),P(S,{key:1,type:"info"},{default:s(()=>e[29]||(e[29]=[v("普通管理员")])),_:1}))]),_:1}),l(u,{label:"状态",width:"100",align:"center"},{default:s(t=>[l(S,{type:t.row.status?"success":"danger"},{default:s(()=>[v($(t.row.status?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),l(u,{prop:"lastLogin",label:"最后登录","min-width":"180",align:"center"}),l(u,{label:"操作",width:"100",align:"center",fixed:"right"},{default:s(t=>[o("div",sl,[l(n,{type:"danger",size:"small",onClick:Sl=>re(t.row),class:"operation-button icon-only"},{default:s(()=>[l(r,null,{default:s(()=>[l(y(J))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Ce,d.value]])])]),_:1}),o("div",ol,[l(be,{"current-page":g.currentPage,"onUpdate:currentPage":e[1]||(e[1]=t=>g.currentPage=t),"page-size":g.pageSize,"onUpdate:pageSize":e[2]||(e[2]=t=>g.pageSize=t),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:g.total,onSizeChange:K,onCurrentChange:ve,"pager-count":7,background:""},{sizes:s(()=>[l(z,{"model-value":g.pageSize,onChange:K,class:"custom-page-size"},{default:s(()=>[(w(),F(q,null,N([10,20,50,100],t=>l(b,{key:t,value:t,label:`${t}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),l(j,{modelValue:I.value,"onUpdate:modelValue":e[10]||(e[10]=t=>I.value=t),title:U.value?"添加管理员":"编辑管理员",width:"650px",center:"","destroy-on-close":""},{footer:s(()=>[o("div",nl,[l(n,{type:"primary",onClick:pe},{default:s(()=>e[30]||(e[30]=[v("确定")])),_:1}),l(n,{onClick:e[9]||(e[9]=t=>I.value=!1)},{default:s(()=>e[31]||(e[31]=[v("取消")])),_:1})])]),default:s(()=>[l(Ve,{ref_key:"formRef",ref:h,model:m,rules:le,"label-width":"100px",class:"admin-form"},{default:s(()=>[l(L,{label:"用户名",prop:"username"},{default:s(()=>[l(p,{modelValue:m.username,"onUpdate:modelValue":e[3]||(e[3]=t=>m.username=t),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),l(L,{label:"昵称",prop:"nickname"},{default:s(()=>[l(p,{modelValue:m.nickname,"onUpdate:modelValue":e[4]||(e[4]=t=>m.nickname=t),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),U.value?(w(),P(L,{key:0,label:"密码",prop:"password"},{default:s(()=>[l(p,{modelValue:m.password,"onUpdate:modelValue":e[5]||(e[5]=t=>m.password=t),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1})):X("",!0),U.value?(w(),P(L,{key:1,label:"确认密码",prop:"confirmPassword"},{default:s(()=>[l(p,{modelValue:m.confirmPassword,"onUpdate:modelValue":e[6]||(e[6]=t=>m.confirmPassword=t),type:"password",placeholder:"请确认密码","show-password":""},null,8,["modelValue"])]),_:1})):X("",!0),l(L,{label:"所属角色",prop:"roleId"},{default:s(()=>[l(z,{modelValue:m.roleId,"onUpdate:modelValue":e[7]||(e[7]=t=>m.roleId=t),placeholder:"请选择所属角色"},{default:s(()=>[(w(!0),F(q,null,N(E.value,t=>(w(),P(b,{key:t.id,label:t.name,value:t.id,disabled:t.id===1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(L,{label:"状态",prop:"status"},{default:s(()=>[l(ye,{modelValue:m.status,"onUpdate:modelValue":e[8]||(e[8]=t=>m.status=t),"active-value":!0,"inactive-value":!1},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),l(j,{modelValue:D.value,"onUpdate:modelValue":e[19]||(e[19]=t=>D.value=t),title:"筛选条件",width:"900px","close-on-click-modal":!0,onClose:e[20]||(e[20]=t=>D.value=!1),class:"filter-dialog"},{footer:s(()=>[o("div",hl,[l(n,{class:"filter-button",type:"primary",onClick:de},{default:s(()=>[l(r,null,{default:s(()=>[l(y(Q))]),_:1}),e[42]||(e[42]=v("搜索 "))]),_:1}),l(n,{class:"filter-button",onClick:ue},{default:s(()=>[l(r,null,{default:s(()=>[l(y(G))]),_:1}),e[43]||(e[43]=v("重置 "))]),_:1}),l(n,{class:"filter-button",onClick:e[18]||(e[18]=t=>D.value=!1)},{default:s(()=>[l(r,null,{default:s(()=>[l(y(Ke))]),_:1}),e[44]||(e[44]=v("取消 "))]),_:1})])]),default:s(()=>[o("div",rl,[o("div",il,[e[36]||(e[36]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"基本信息")],-1)),o("div",dl,[o("div",ul,[o("div",ml,[e[32]||(e[32]=o("div",{class:"filter-label"},"ID",-1)),l(p,{modelValue:i.id,"onUpdate:modelValue":e[11]||(e[11]=t=>i.id=t),placeholder:"请输入ID"},null,8,["modelValue"])]),o("div",cl,[e[33]||(e[33]=o("div",{class:"filter-label"},"用户名",-1)),l(p,{modelValue:i.username,"onUpdate:modelValue":e[12]||(e[12]=t=>i.username=t),placeholder:"请输入用户名"},null,8,["modelValue"])]),o("div",fl,[e[34]||(e[34]=o("div",{class:"filter-label"},"昵称",-1)),l(p,{modelValue:i.nickname,"onUpdate:modelValue":e[13]||(e[13]=t=>i.nickname=t),placeholder:"请输入昵称"},null,8,["modelValue"])]),o("div",pl,[e[35]||(e[35]=o("div",{class:"filter-label"},"所属角色",-1)),l(z,{modelValue:i.roleId,"onUpdate:modelValue":e[14]||(e[14]=t=>i.roleId=t),placeholder:"请选择所属角色",style:{width:"100%"}},{default:s(()=>[l(b,{label:"全部",value:null}),(w(!0),F(q,null,N(E.value,t=>(w(),P(b,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])])]),o("div",gl,[e[39]||(e[39]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"状态信息")],-1)),o("div",vl,[o("div",_l,[o("div",wl,[e[37]||(e[37]=o("div",{class:"filter-label"},"管理员类型",-1)),l(z,{modelValue:i.is_super,"onUpdate:modelValue":e[15]||(e[15]=t=>i.is_super=t),placeholder:"请选择管理员类型",style:{width:"100%"}},{default:s(()=>[l(b,{label:"全部",value:null}),l(b,{label:"超级管理员",value:!0}),l(b,{label:"普通管理员",value:!1})]),_:1},8,["modelValue"])]),o("div",bl,[e[38]||(e[38]=o("div",{class:"filter-label"},"状态",-1)),l(z,{modelValue:i.status,"onUpdate:modelValue":e[16]||(e[16]=t=>i.status=t),placeholder:"请选择状态",style:{width:"100%"}},{default:s(()=>[l(b,{label:"全部",value:null}),l(b,{label:"正常",value:!0}),l(b,{label:"禁用",value:!1})]),_:1},8,["modelValue"])])])])]),o("div",yl,[e[41]||(e[41]=o("div",{class:"section-header"},[o("div",{class:"section-title"},"时间信息")],-1)),o("div",Vl,[o("div",kl,[o("div",Cl,[e[40]||(e[40]=o("div",{class:"filter-label"},"最后登录时间",-1)),l(ke,{modelValue:i.lastLoginRange,"onUpdate:modelValue":e[17]||(e[17]=t=>i.lastLoginRange=t),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])])])])])]),_:1},8,["modelValue"]),l(j,{modelValue:x.value,"onUpdate:modelValue":e[22]||(e[22]=t=>x.value=t),title:"确认删除",width:"400px",center:""},{footer:s(()=>[o("div",Dl,[l(n,{type:"danger",onClick:oe},{default:s(()=>e[45]||(e[45]=[v("确定删除")])),_:1}),l(n,{onClick:e[21]||(e[21]=t=>x.value=!1)},{default:s(()=>e[46]||(e[46]=[v("取消")])),_:1})])]),default:s(()=>[o("p",Pl,$(Y.value),1)]),_:1},8,["modelValue"])])}}}),Jl=We(El,[["__scopeId","data-v-3cdad5ae"]]);export{Jl as default};
