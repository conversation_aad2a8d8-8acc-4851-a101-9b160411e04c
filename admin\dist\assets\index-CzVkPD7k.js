/* empty css             *//* empty css                   *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import{aE as Re,aF as ke,a5 as le,d as We,r as w,q as Ve,a as Je,o as Qe,c as ee,b as l,a7 as we,e as a,a8 as de,a9 as fe,w as d,m as Xe,f as et,i as tt,aa as lt,ab as at,ac as nt,p as C,v as it,y as x,n as m,x as ot,j as te,ad as st,aB as rt,af as ut,V as pe,ag as dt,ah as ft,ai as pt,an as mt,aj as ct,ak as vt,al as gt,ap as _t,aq as bt,at as yt,E as ht,aA as Mt,g as A,_ as xt}from"./index-LncY9lAB.js";import{s as ae}from"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";function De(T){return T.user_id?ae({url:`/api/admin/users/${T.user_id}/investments`,method:"get",params:{page:T.page,limit:T.limit,status:T.status}}):ae({url:"/api/admin/investments",method:"get",params:T})}function $t(T){return ae({url:`/api/admin/investments/${T}`,method:"delete"})}function Ct(T){return ae({url:"/api/admin/investments/batch",method:"delete",data:{ids:T}})}function Tt(T,Z){return ae({url:`/api/admin/investments/${T}/profits`,method:"get",params:Z})}var Pe={exports:{}};(function(T,Z){(function(P,Y){T.exports=Y()})(ke,function(){var P="minute",Y=/[+-]\d\d(?::?\d\d)?/g,K=/([+-]|\d\d)/g;return function(I,$,z){var _=$.prototype;z.utc=function(o){var c={date:o,utc:!0,args:arguments};return new $(c)},_.utc=function(o){var c=z(this.toDate(),{locale:this.$L,utc:!0});return o?c.add(this.utcOffset(),P):c},_.local=function(){return z(this.toDate(),{locale:this.$L,utc:!1})};var D=_.parse;_.parse=function(o){o.utc&&(this.$u=!0),this.$utils().u(o.$offset)||(this.$offset=o.$offset),D.call(this,o)};var q=_.init;_.init=function(){if(this.$u){var o=this.$d;this.$y=o.getUTCFullYear(),this.$M=o.getUTCMonth(),this.$D=o.getUTCDate(),this.$W=o.getUTCDay(),this.$H=o.getUTCHours(),this.$m=o.getUTCMinutes(),this.$s=o.getUTCSeconds(),this.$ms=o.getUTCMilliseconds()}else q.call(this)};var k=_.utcOffset;_.utcOffset=function(o,c){var V=this.$utils().u;if(V(o))return this.$u?0:V(this.$offset)?k.call(this):this.$offset;if(typeof o=="string"&&(o=function(M){M===void 0&&(M="");var H=M.match(Y);if(!H)return null;var S=(""+H[0]).match(K)||["-",0,0],F=S[0],E=60*+S[1]+ +S[2];return E===0?0:F==="+"?E:-E}(o),o===null))return this;var h=Math.abs(o)<=16?60*o:o,y=this;if(c)return y.$offset=h,y.$u=o===0,y;if(o!==0){var v=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(y=this.local().add(h+v,P)).$offset=h,y.$x.$localOffset=v}else y=this.utc();return y};var f=_.format;_.format=function(o){var c=o||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return f.call(this,c)},_.valueOf=function(){var o=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*o},_.isUTC=function(){return!!this.$u},_.toISOString=function(){return this.toDate().toISOString()},_.toString=function(){return this.toDate().toUTCString()};var b=_.toDate;_.toDate=function(o){return o==="s"&&this.$offset?z(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():b.call(this)};var g=_.diff;_.diff=function(o,c,V){if(o&&this.$u===o.$u)return g.call(this,o,c,V);var h=this.local(),y=z(o).local();return g.call(h,y,c,V)}}})})(Pe);var Vt=Pe.exports;const Ie=Re(Vt);var ze={exports:{}};(function(T,Z){(function(P,Y){T.exports=Y()})(ke,function(){var P={year:0,month:1,day:2,hour:3,minute:4,second:5},Y={};return function(K,I,$){var z,_=function(f,b,g){g===void 0&&(g={});var o=new Date(f),c=function(V,h){h===void 0&&(h={});var y=h.timeZoneName||"short",v=V+"|"+y,M=Y[v];return M||(M=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:V,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:y}),Y[v]=M),M}(b,g);return c.formatToParts(o)},D=function(f,b){for(var g=_(f,b),o=[],c=0;c<g.length;c+=1){var V=g[c],h=V.type,y=V.value,v=P[h];v>=0&&(o[v]=parseInt(y,10))}var M=o[3],H=M===24?0:M,S=o[0]+"-"+o[1]+"-"+o[2]+" "+H+":"+o[4]+":"+o[5]+":000",F=+f;return($.utc(S).valueOf()-(F-=F%1e3))/6e4},q=I.prototype;q.tz=function(f,b){f===void 0&&(f=z);var g,o=this.utcOffset(),c=this.toDate(),V=c.toLocaleString("en-US",{timeZone:f}),h=Math.round((c-new Date(V))/1e3/60),y=15*-Math.round(c.getTimezoneOffset()/15)-h;if(!Number(y))g=this.utcOffset(0,b);else if(g=$(V,{locale:this.$L}).$set("millisecond",this.$ms).utcOffset(y,!0),b){var v=g.utcOffset();g=g.add(o-v,"minute")}return g.$x.$timezone=f,g},q.offsetName=function(f){var b=this.$x.$timezone||$.tz.guess(),g=_(this.valueOf(),b,{timeZoneName:f}).find(function(o){return o.type.toLowerCase()==="timezonename"});return g&&g.value};var k=q.startOf;q.startOf=function(f,b){if(!this.$x||!this.$x.$timezone)return k.call(this,f,b);var g=$(this.format("YYYY-MM-DD HH:mm:ss:SSS"),{locale:this.$L});return k.call(g,f,b).tz(this.$x.$timezone,!0)},$.tz=function(f,b,g){var o=g&&b,c=g||b||z,V=D(+$(),c);if(typeof f!="string")return $(f).tz(c);var h=function(H,S,F){var E=H-60*S*1e3,L=D(E,F);if(S===L)return[E,S];var J=D(E-=60*(L-S)*1e3,F);return L===J?[E,L]:[H-60*Math.min(L,J)*1e3,Math.max(L,J)]}($.utc(f,o).valueOf(),V,c),y=h[0],v=h[1],M=$(y).utcOffset(v);return M.$x.$timezone=c,M},$.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},$.tz.setDefault=function(f){z=f}}})})(ze);var wt=ze.exports;const Ue=Re(wt);le.extend(Ie);le.extend(Ue);function j(T,Z="YYYY-MM-DD HH:mm:ss"){if(!T)return"-";try{return le(T).format(Z)}catch(P){return console.error("日期格式化错误:",P,T),"-"}}const Dt={class:"investments-container"},Rt={class:"tabs"},kt=["onClick"],Pt={class:"toolbar"},It={class:"toolbar-left"},zt={class:"toolbar-right"},Ut={key:0,class:"filter-tags-container"},Yt={class:"filter-tags-header"},Nt={class:"filter-tags-content"},St={class:"table-wrapper"},jt={class:"operation-buttons-container"},qt={class:"pagination-container"},Ot={class:"detail-info"},Ft={class:"detail-row"},Et={class:"detail-item"},Ht={class:"detail-item"},Bt={class:"detail-row"},Lt={class:"detail-item"},At={class:"detail-item"},Zt={class:"detail-row"},Gt={class:"detail-item"},Kt={class:"detail-item"},Wt={class:"detail-row"},Jt={class:"detail-item"},Qt={class:"detail-item"},Xt={class:"detail-row"},el={class:"detail-item"},tl={class:"detail-item"},ll={class:"repayment-list"},al={class:"profit-pagination"},nl={class:"dialog-footer"},il={class:"dialog-footer"},ol={class:"filter-container"},sl={class:"filter-section"},rl={class:"section-content"},ul={class:"filter-grid"},dl={class:"filter-item"},fl={class:"filter-item"},pl={class:"filter-item"},ml={class:"filter-item"},cl={class:"filter-item"},vl={class:"filter-item"},gl={class:"range-inputs"},_l={class:"filter-item"},bl={class:"filter-item"},yl={class:"filter-section"},hl={class:"section-content"},Ml={class:"filter-grid"},xl={class:"filter-item"},$l={class:"range-inputs"},Cl={class:"filter-item"},Tl={class:"range-inputs"},Vl={class:"filter-item"},wl={class:"range-inputs"},Dl={class:"filter-item"},Rl={class:"range-inputs"},kl={class:"filter-item"},Pl={class:"range-inputs"},Il={class:"filter-section"},zl={class:"section-content"},Ul={class:"filter-grid"},Yl={class:"filter-item"},Nl={class:"filter-item"},Sl={class:"filter-item"},jl={class:"filter-footer"},ql=We({__name:"index",setup(T){le.extend(Ie),le.extend(Ue);const Z=[{label:"全部",value:"all"},{label:"进行中",value:"active"},{label:"暂停",value:"paused"},{label:"完成",value:"completed"}],P=w("all"),Y=w(!1),K=w(!1),I=w(""),$=w([]),z=w(""),_=w(null),D=w(1),q=w(10),k=w(30),f=w([]),b=w([]),g=w(1),o=w(10),c=w(0),V=w(!1),h=w(!1),y=w(!1),v=w(null),M=w([]),H=Ve(()=>(console.log("计算filteredData，当前tableData长度:",f.value.length),f.value)),S=()=>{if(console.log("执行performSearch函数，当前搜索内容:",I.value,"上次搜索内容:",z.value),I.value===z.value){console.log("搜索内容没有变化，不重复搜索");return}z.value=I.value,console.log("开始执行搜索:",I.value),D.value=1,console.log("重置到第一页，当前页码:",D.value),console.log("调用fetchData函数获取数据"),B()},F=()=>{_.value&&(clearTimeout(_.value),_.value=null),S()},E=()=>{_.value&&(clearTimeout(_.value),_.value=null),console.log("搜索框失去焦点，执行搜索，搜索内容:",I.value),S()},L=()=>{!I.value.trim()&&z.value!==""&&(z.value="",D.value=1,B())},J=()=>{C.info("功能尚未实现")},Ye=i=>{M.value=i},me=i=>{q.value=i,D.value=1,B()},Ne=i=>{D.value=i,B()},ne=(i=1)=>{if(!v.value){C.warning("当前没有选中的投资记录");return}K.value=!0,g.value=i;const e={page:g.value,limit:o.value};Tt(v.value.id,e).then(r=>{try{let p=null,s=0;r&&r.items?(p=r.items,s=r.total||p.length):r&&r.data&&r.data.items?(p=r.data.items,s=r.data.total||p.length):Array.isArray(r)&&(p=r,s=p.length),p&&p.length>0?(c.value=s,b.value=p.map(U=>{const N=typeof U.amount=="string"?parseFloat(U.amount):Number(U.amount);return{id:U.id,investment_id:U.investment_id,amount:N,status:U.status==="pending"?"待发放":"已发放",profit_time:j(U.profit_time),created_at:j(U.created_at),updated_at:j(U.updated_at)}}),b.value.length>0?C.success(`成功获取到 ${b.value.length} 条收益记录，共 ${c.value} 条`):C.info("没有找到收益记录")):(b.value=[],c.value=0,C.warning("没有找到收益记录数据"))}catch{b.value=[],c.value=0,C.error("处理收益记录数据时出错")}}).catch(r=>{C.error("获取投资收益记录失败，请稍后再试"),b.value=[],c.value=0}).finally(()=>{K.value=!1})},Se=i=>{o.value=i,ne(1)},je=i=>{ne(i)},qe=i=>{v.value=i,V.value=!0,b.value=[],g.value=1,ne(1)},Oe=i=>{if(i.status==="完成"){C.warning("该笔投资已完成，不能执行删除操作");return}v.value=i,h.value=!0},Fe=()=>{if(M.value.length===0){C.warning("请选择要删除的记录");return}if(M.value.filter(e=>e.status==="完成").length>0){C.warning("选中的记录中包含已完成的投资，不能执行删除操作");return}M.value.length===1?v.value=M.value[0]:v.value=null,h.value=!0},Ee=()=>{if(v.value)$t(v.value.id).then(()=>{var i;C.success(`投资记录 ID:${(i=v.value)==null?void 0:i.id} 删除成功`),f.value=f.value.filter(e=>{var r;return e.id!==((r=v.value)==null?void 0:r.id)}),k.value=f.value.length,h.value=!1,B()}).catch(i=>{C.error("删除投资记录失败，请稍后再试")});else if(M.value.length>0){const i=M.value.map(e=>e.id);Ct(i).then(()=>{C.success(`成功删除 ${i.length} 条投资记录`),f.value=f.value.filter(e=>!i.includes(e.id)),M.value=[],k.value=f.value.length,h.value=!1,B()}).catch(e=>{C.error("批量删除投资记录失败，请稍后再试")})}},B=()=>{Y.value=!0;const i={page:D.value,limit:q.value};I.value&&(i.keyword=I.value),P.value!=="all"&&(i.status=P.value),De(i).then(e=>{try{if(!e){f.value=[],$.value=[],k.value=0;return}const p=(e.items||[]).map(s=>{const U=parseFloat((s.amount*s.profit_rate/100).toFixed(2)),N=s.is_gift===!0,u=s.user&&s.user.user_id?s.user.user_id:"";return{id:s.id||0,investment_type:N?"赠送":"购买",user_id:s.user_id||0,user_id_ref:u||"",username:s.user&&s.user.username?s.user.username:`用户ID:${s.user_id||"未知"}`,project_id:s.project_id||0,project_name:s.project&&s.project.name?s.project.name:`项目ID:${s.project_id||"未知"}`,quantity:s.quantity||1,amount:Number(s.amount)||0,profit_cycle:Number(s.profit_cycle)||0,profit_rate:Number(s.profit_rate)||0,total_profit:Number(s.total_profit)||0,single_profit:Number(U)||0,last_profit_time:j(s.last_profit_time),profit_count:Number(s.profit_count)||0,status:s.status==="active"?"进行中":s.status==="paused"?"暂停":"完成",start_time:j(s.start_time),end_time:j(s.end_time),created_at:j(s.created_at),updated_at:j(s.updated_at)}});f.value=p,$.value=[...p],k.value=e.total||p.length}catch{C.error("处理数据时出错，请稍后再试"),f.value=[],$.value=[],k.value=0}}).catch(e=>{C.error("获取投资列表失败，请稍后再试"),f.value=[],$.value=[],k.value=0}).finally(()=>{Y.value=!1})},Q=i=>{if(i==null)return"0.00";let e;if(typeof i=="string"){const r=i.replace(/[^\d.-]/g,"");e=parseFloat(r)}else e=Number(i);return isNaN(e)?"0.00":e.toFixed(2)},He=i=>{switch(i){case"已购买":return"success";case"进行中":return"success";case"暂停":return"warning";case"完成":return"info";default:return""}},W=w([]),ce=Ve(()=>W.value.length),t=Je({id:"",investmentType:"",userId:"",username:"",projectName:"",quantityMin:null,quantityMax:null,amountMin:null,amountMax:null,profitCycleMin:null,profitCycleMax:null,profitRateMin:null,profitRateMax:null,totalProfitMin:null,totalProfitMax:null,lastProfitTimeRange:null,profitCountMin:null,profitCountMax:null,status:"",startTimeRange:null,endTimeRange:null}),Be=()=>{y.value=!0},ve=()=>{const i=[];if(t.id&&i.push({key:"id",label:`ID: ${t.id}`}),t.investmentType&&i.push({key:"investmentType",label:`投资方式: ${t.investmentType}`}),t.userId&&i.push({key:"userId",label:`用户ID: ${t.userId}`}),t.username&&i.push({key:"username",label:`用户名: ${t.username}`}),t.projectName&&i.push({key:"projectName",label:`项目名称: ${t.projectName}`}),t.status&&i.push({key:"status",label:`状态: ${t.status}`}),t.quantityMin!==null||t.quantityMax!==null){const e=t.quantityMin||"0",r=t.quantityMax||"∞";i.push({key:"quantity",label:`数量: ${e} - ${r}`})}if(t.amountMin!==null||t.amountMax!==null){const e=t.amountMin||"0",r=t.amountMax||"∞";i.push({key:"amount",label:`投资金额: ${e} - ${r}`})}if(t.totalProfitMin!==null||t.totalProfitMax!==null){const e=t.totalProfitMin||"0",r=t.totalProfitMax||"∞";i.push({key:"totalProfit",label:`总收益: ${e} - ${r}`})}if(t.profitCycleMin!==null||t.profitCycleMax!==null){const e=t.profitCycleMin||"0",r=t.profitCycleMax||"∞";i.push({key:"profitCycle",label:`收益周期: ${e} - ${r}小时`})}if(t.profitRateMin!==null||t.profitRateMax!==null){const e=t.profitRateMin||"0",r=t.profitRateMax||"∞";i.push({key:"profitRate",label:`收益率: ${e} - ${r}%`})}if(t.profitCountMin!==null||t.profitCountMax!==null){const e=t.profitCountMin||"0",r=t.profitCountMax||"∞";i.push({key:"profitCount",label:`收益次数: ${e} - ${r}`})}t.lastProfitTimeRange&&t.lastProfitTimeRange.length===2&&i.push({key:"lastProfitTimeRange",label:`最后收益时间: ${t.lastProfitTimeRange[0]} 至 ${t.lastProfitTimeRange[1]}`}),t.startTimeRange&&t.startTimeRange.length===2&&i.push({key:"startTimeRange",label:`开始时间: ${t.startTimeRange[0]} 至 ${t.startTimeRange[1]}`}),t.endTimeRange&&t.endTimeRange.length===2&&i.push({key:"endTimeRange",label:`结束时间: ${t.endTimeRange[0]} 至 ${t.endTimeRange[1]}`}),W.value=i},Le=i=>{switch(i){case"id":t.id="";break;case"investmentType":t.investmentType="";break;case"userId":t.userId="";break;case"username":t.username="";break;case"projectName":t.projectName="";break;case"status":t.status="";break;case"quantity":t.quantityMin=null,t.quantityMax=null;break;case"amount":t.amountMin=null,t.amountMax=null;break;case"totalProfit":t.totalProfitMin=null,t.totalProfitMax=null;break;case"profitCycle":t.profitCycleMin=null,t.profitCycleMax=null;break;case"profitRate":t.profitRateMin=null,t.profitRateMax=null;break;case"profitCount":t.profitCountMin=null,t.profitCountMax=null;break;case"lastProfitTimeRange":t.lastProfitTimeRange=null;break;case"startTimeRange":t.startTimeRange=null;break;case"endTimeRange":t.endTimeRange=null;break}ve(),D.value=1,_e(),C.success("已移除筛选条件")},Ae=()=>{ge(),W.value=[],C.success("已清除所有筛选条件")},ge=()=>{t.id="",t.investmentType="",t.userId="",t.username="",t.projectName="",t.quantityMin=null,t.quantityMax=null,t.amountMin=null,t.amountMax=null,t.profitCycleMin=null,t.profitCycleMax=null,t.profitRateMin=null,t.profitRateMax=null,t.totalProfitMin=null,t.totalProfitMax=null,t.lastProfitTimeRange=null,t.profitCountMin=null,t.profitCountMax=null,t.status="",t.startTimeRange=null,t.endTimeRange=null,W.value=[],D.value=1,B(),C.success("筛选条件已重置")},_e=()=>{ve(),y.value=!1;const i={page:1,limit:1e3},e=[];if(t.id&&e.push(t.id),t.username&&e.push(t.username),t.projectName&&e.push(t.projectName),e.length>0&&(i.keyword=e[0]),I.value&&(i.keyword=I.value),t.userId&&(i.user_id=t.userId),t.investmentType){const r={购买:"purchase",赠送:"gift"};i.investment_type=r[t.investmentType]||t.investmentType}if(t.status){const r={进行中:"active",暂停:"paused",完成:"completed"};i.status=r[t.status]||t.status}P.value!=="all"&&(i.status=P.value),D.value=1,Y.value=!0,De(i).then(r=>{try{if(!r){f.value=[],k.value=0;return}const p=r.items||[],s=r.total||0,U=p.map(u=>{const ie=parseFloat((u.amount*u.profit_rate/100).toFixed(2)),oe=u.is_gift===!0,R=u.user&&u.user.user_id?u.user.user_id:"";return{id:u.id||0,investment_type:oe?"赠送":"购买",user_id:u.user_id||0,user_id_ref:R||"",username:u.user&&u.user.username?u.user.username:`用户ID:${u.user_id||"未知"}`,project_id:u.project_id||0,project_name:u.project&&u.project.name?u.project.name:`项目ID:${u.project_id||"未知"}`,quantity:u.quantity||1,amount:Number(u.amount)||0,profit_cycle:Number(u.profit_cycle)||0,profit_rate:Number(u.profit_rate)||0,total_profit:Number(u.total_profit)||0,single_profit:Number(ie)||0,last_profit_time:j(u.last_profit_time),profit_count:Number(u.profit_count)||0,status:u.status==="active"?"进行中":u.status==="paused"?"暂停":"完成",start_time:j(u.start_time),end_time:j(u.end_time),created_at:j(u.created_at),updated_at:j(u.updated_at)}}),N=Ze(U);f.value=N,$.value=[...N],k.value=N.length,N.length===0?C.warning("没有找到符合条件的数据"):C.success(`筛选成功，找到 ${N.length} 条符合条件的数据`)}catch{C.error("处理筛选数据时出错，请稍后再试"),f.value=[],$.value=[],k.value=0}}).catch(r=>{C.error("获取筛选数据失败，请稍后再试"),f.value=[],$.value=[],k.value=0}).finally(()=>{Y.value=!1})},Ze=i=>i.filter(e=>{if(t.id&&!e.id.toString().includes(t.id)||t.quantityMin!==null&&e.quantity<t.quantityMin||t.quantityMax!==null&&e.quantity>t.quantityMax||t.amountMin!==null&&e.amount<t.amountMin||t.amountMax!==null&&e.amount>t.amountMax||t.totalProfitMin!==null&&e.total_profit<t.totalProfitMin||t.totalProfitMax!==null&&e.total_profit>t.totalProfitMax||t.profitRateMin!==null&&e.profit_rate<t.profitRateMin||t.profitRateMax!==null&&e.profit_rate>t.profitRateMax||t.profitCycleMin!==null&&e.profit_cycle<t.profitCycleMin||t.profitCycleMax!==null&&e.profit_cycle>t.profitCycleMax||t.profitCountMin!==null&&e.profit_count<t.profitCountMin||t.profitCountMax!==null&&e.profit_count>t.profitCountMax)return!1;if(t.lastProfitTimeRange&&t.lastProfitTimeRange.length===2){const r=new Date(e.last_profit_time),p=new Date(t.lastProfitTimeRange[0]),s=new Date(t.lastProfitTimeRange[1]);if(r<p||r>s)return!1}if(t.startTimeRange&&t.startTimeRange.length===2){const r=new Date(e.start_time),p=new Date(t.startTimeRange[0]),s=new Date(t.startTimeRange[1]);if(r<p||r>s)return!1}if(t.endTimeRange&&t.endTimeRange.length===2){const r=new Date(e.end_time),p=new Date(t.endTimeRange[0]),s=new Date(t.endTimeRange[1]);if(r<p||r>s)return!1}return!0});return Qe(()=>{B(),$.value=[...f.value]}),(i,e)=>{const r=ot,p=Xe,s=tt,U=ft,N=mt,u=gt,ie=_t,oe=lt,R=yt,X=bt,be=at,se=nt,re=Mt,Ge=ht,Ke=vt;return A(),ee("div",Dt,[l("div",Rt,[(A(),ee(de,null,fe(Z,(n,G)=>l("div",{key:G,class:it(["tab",{active:P.value===n.value}]),onClick:ue=>P.value=n.value},x(n.label),11,kt)),64))]),l("div",Pt,[l("div",It,[a(p,{class:"toolbar-button",type:"default",onClick:B},{default:d(()=>[a(r,null,{default:d(()=>[a(te(st))]),_:1}),e[34]||(e[34]=m("刷新 "))]),_:1}),a(p,{class:"toolbar-button",type:"danger",disabled:M.value.length===0,onClick:Fe},{default:d(()=>[a(r,null,{default:d(()=>[a(te(rt))]),_:1}),e[35]||(e[35]=m("删除 "))]),_:1},8,["disabled"])]),l("div",zt,[a(s,{modelValue:I.value,"onUpdate:modelValue":e[0]||(e[0]=n=>I.value=n),placeholder:"搜索用户名",class:"search-input",onKeyup:et(F,["enter"]),onBlur:E,onInput:L},null,8,["modelValue"]),a(p,{class:"search-button",type:"primary",onClick:F},{default:d(()=>[a(r,null,{default:d(()=>[a(te(ut))]),_:1})]),_:1}),a(p,{class:"toolbar-button filter-button",type:"default",onClick:Be},{default:d(()=>[a(r,null,{default:d(()=>[a(te(dt))]),_:1}),e[36]||(e[36]=m("筛选 ")),ce.value>0?(A(),pe(U,{key:0,value:ce.value,class:"filter-badge"},null,8,["value"])):we("",!0)]),_:1}),a(p,{class:"toolbar-button export-button",type:"default",onClick:J},{default:d(()=>[a(r,null,{default:d(()=>[a(te(pt))]),_:1}),e[37]||(e[37]=m("导出 "))]),_:1})])]),W.value.length>0?(A(),ee("div",Ut,[l("div",Yt,[e[39]||(e[39]=l("span",{class:"filter-tags-title"},"当前筛选条件：",-1)),a(p,{type:"text",size:"small",onClick:Ae,class:"clear-all-btn"},{default:d(()=>e[38]||(e[38]=[m(" 清除所有筛选 ")])),_:1})]),l("div",Nt,[(A(!0),ee(de,null,fe(W.value,n=>(A(),pe(N,{key:n.key,closable:"",onClose:G=>Le(n.key),class:"filter-tag",type:"info"},{default:d(()=>[m(x(n.label),1)]),_:2},1032,["onClose"]))),128))])])):we("",!0),a(oe,{class:"table-card"},{default:d(()=>[l("div",St,[ct((A(),pe(ie,{data:H.value,style:{width:"100%"},border:"",stripe:"","highlight-current-row":"",onSelectionChange:Ye},{default:d(()=>[a(u,{type:"selection",width:"40",align:"center",fixed:"left"}),a(u,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),a(u,{prop:"investment_type",label:"投资方式","min-width":"90",align:"center"},{default:d(n=>[a(N,{type:n.row.investment_type==="赠送"?"success":"primary"},{default:d(()=>[m(x(n.row.investment_type),1)]),_:2},1032,["type"])]),_:1}),a(u,{prop:"user_id_ref",label:"用户ID","min-width":"90",align:"center"}),a(u,{prop:"username",label:"用户名","min-width":"110",align:"center"}),a(u,{prop:"project_name",label:"项目名称","min-width":"100",align:"center"}),a(u,{prop:"quantity",label:"数量","min-width":"60",align:"center"}),a(u,{prop:"amount",label:"投资金额","min-width":"90",align:"center"},{default:d(n=>[m(x(Q(n.row.amount)),1)]),_:1}),a(u,{prop:"profit_cycle",label:"收益周期(小时)","min-width":"120",align:"center"}),a(u,{prop:"profit_rate",label:"收益率(%)","min-width":"100",align:"center"},{default:d(n=>[m(x(Number(n.row.profit_rate).toFixed(2))+"% ",1)]),_:1}),a(u,{prop:"total_profit",label:"总收益","min-width":"90",align:"center"},{default:d(n=>[m(x(Q(n.row.total_profit)),1)]),_:1}),a(u,{prop:"last_profit_time",label:"最后收益时间","min-width":"160",align:"center",sortable:""},{default:d(n=>[l("span",null,x(n.row.last_profit_time||"暂无收益"),1)]),_:1}),a(u,{prop:"profit_count",label:"收益次数","min-width":"90",align:"center"}),a(u,{prop:"status",label:"状态","min-width":"90",align:"center"},{default:d(n=>[a(N,{type:He(n.row.status),size:"small"},{default:d(()=>[m(x(n.row.status),1)]),_:2},1032,["type"])]),_:1}),a(u,{prop:"start_time",label:"开始时间",width:"180",align:"center",sortable:""},{default:d(n=>[m(x(n.row.start_time),1)]),_:1}),a(u,{prop:"end_time",label:"结束时间",width:"180",align:"center",sortable:""},{default:d(n=>[m(x(n.row.end_time),1)]),_:1}),a(u,{label:"操作",width:"150",fixed:"right",align:"center"},{default:d(n=>[l("div",jt,[a(p,{type:"primary",size:"small",onClick:G=>qe(n.row),class:"operation-button"},{default:d(()=>e[40]||(e[40]=[m(" 详情 ")])),_:2},1032,["onClick"]),a(p,{type:"danger",size:"small",onClick:G=>Oe(n.row),class:"operation-button"},{default:d(()=>e[41]||(e[41]=[m(" 删除 ")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Ke,Y.value]])])]),_:1}),l("div",qt,[a(be,{"current-page":D.value,"onUpdate:currentPage":e[1]||(e[1]=n=>D.value=n),"page-size":q.value,"onUpdate:pageSize":e[2]||(e[2]=n=>q.value=n),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:k.value,onSizeChange:me,onCurrentChange:Ne,"pager-count":7,background:""},{sizes:d(()=>[a(X,{"model-value":q.value,onChange:me,class:"custom-page-size"},{default:d(()=>[(A(),ee(de,null,fe([10,20,50,100],n=>a(R,{key:n,value:n,label:`${n}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),a(se,{modelValue:V.value,"onUpdate:modelValue":e[7]||(e[7]=n=>V.value=n),title:"收益详情",width:"700px",center:""},{footer:d(()=>[l("span",nl,[a(p,{type:"primary",onClick:e[5]||(e[5]=n=>ne(g.value.value)),loading:K.value},{default:d(()=>e[52]||(e[52]=[m("刷新收益记录")])),_:1},8,["loading"]),a(p,{onClick:e[6]||(e[6]=n=>V.value=!1)},{default:d(()=>e[53]||(e[53]=[m("关闭")])),_:1})])]),default:d(()=>{var n,G,ue,ye,he,Me,xe,$e,Ce,Te;return[l("div",Ot,[l("div",Ft,[l("div",Et,[e[42]||(e[42]=l("strong",null,"用户名：",-1)),m(x((n=v.value)==null?void 0:n.username),1)]),l("div",Ht,[e[43]||(e[43]=l("strong",null,"用户ID：",-1)),m(x((G=v.value)==null?void 0:G.user_id_ref),1)])]),l("div",Bt,[l("div",Lt,[e[44]||(e[44]=l("strong",null,"项目名称：",-1)),m(x((ue=v.value)==null?void 0:ue.project_name),1)]),l("div",At,[e[45]||(e[45]=l("strong",null,"项目ID：",-1)),m(x((ye=v.value)==null?void 0:ye.project_id),1)])]),l("div",Zt,[l("div",Gt,[e[46]||(e[46]=l("strong",null,"投资方式：",-1)),m(x((he=v.value)==null?void 0:he.investment_type),1)]),l("div",Kt,[e[47]||(e[47]=l("strong",null,"投资金额：",-1)),m(x(Q(((Me=v.value)==null?void 0:Me.amount)||0)),1)])]),l("div",Wt,[l("div",Jt,[e[48]||(e[48]=l("strong",null,"总收益：",-1)),m(x(Q(((xe=v.value)==null?void 0:xe.total_profit)||0)),1)]),l("div",Qt,[e[49]||(e[49]=l("strong",null,"已收益次数：",-1)),m(x(($e=v.value)==null?void 0:$e.profit_count),1)])]),l("div",Xt,[l("div",el,[e[50]||(e[50]=l("strong",null,"收益周期：",-1)),m(x((Ce=v.value)==null?void 0:Ce.profit_cycle)+"小时",1)]),l("div",tl,[e[51]||(e[51]=l("strong",null,"最后收益时间：",-1)),l("span",null,x(((Te=v.value)==null?void 0:Te.last_profit_time)||"暂无收益"),1)])])]),l("div",ll,[a(ie,{data:b.value,style:{width:"100%"},border:""},{default:d(()=>[a(u,{label:"收益时间",align:"center"},{default:d(O=>[m(x(O.row.profit_time),1)]),_:1}),a(u,{label:"收益金额",align:"center"},{default:d(O=>[m(x(Q(O.row.amount)),1)]),_:1}),a(u,{label:"状态",align:"center"},{default:d(O=>[a(N,{type:O.row.status==="已发放"?"success":"warning",size:"small"},{default:d(()=>[m(x(O.row.status),1)]),_:2},1032,["type"])]),_:1}),a(u,{label:"发放时间",align:"center"},{default:d(O=>[m(x(O.row.updated_at),1)]),_:1})]),_:1},8,["data"]),l("div",al,[a(be,{"current-page":g.value,"onUpdate:currentPage":e[3]||(e[3]=O=>g.value=O),"page-size":o.value,"onUpdate:pageSize":e[4]||(e[4]=O=>o.value=O),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:c.value,onSizeChange:Se,onCurrentChange:je,"pager-count":5,background:"",small:""},null,8,["current-page","page-size","total"])])])]}),_:1},8,["modelValue"]),a(se,{modelValue:h.value,"onUpdate:modelValue":e[9]||(e[9]=n=>h.value=n),title:"删除确认",width:"400px",center:"","close-on-click-modal":!0},{footer:d(()=>[l("span",il,[a(p,{type:"danger",onClick:Ee},{default:d(()=>e[54]||(e[54]=[m("删除")])),_:1}),a(p,{onClick:e[8]||(e[8]=n=>h.value=!1)},{default:d(()=>e[55]||(e[55]=[m("取消")])),_:1})])]),default:d(()=>[e[56]||(e[56]=l("span",null,"确认要删除该投资记录吗？此操作无法撤销。",-1))]),_:1},8,["modelValue"]),a(se,{modelValue:y.value,"onUpdate:modelValue":e[33]||(e[33]=n=>y.value=n),title:"筛选条件",width:"900px","close-on-click-modal":!0,class:"filter-dialog"},{footer:d(()=>[l("div",jl,[a(p,{class:"filter-button",type:"primary",onClick:_e},{default:d(()=>e[82]||(e[82]=[m(" 搜索 ")])),_:1}),a(p,{class:"filter-button",onClick:ge},{default:d(()=>e[83]||(e[83]=[m(" 重置 ")])),_:1}),a(p,{class:"filter-button",onClick:e[32]||(e[32]=n=>y.value=!1)},{default:d(()=>e[84]||(e[84]=[m(" 取消 ")])),_:1})])]),default:d(()=>[l("div",ol,[a(Ge,{"label-position":"top",model:t,"label-width":"100px"},{default:d(()=>[l("div",sl,[e[66]||(e[66]=l("div",{class:"section-header"},[l("div",{class:"section-title"},"基本信息")],-1)),l("div",rl,[l("div",ul,[l("div",dl,[e[57]||(e[57]=l("div",{class:"filter-label"},"ID",-1)),a(s,{modelValue:t.id,"onUpdate:modelValue":e[10]||(e[10]=n=>t.id=n),placeholder:"请输入ID",clearable:""},null,8,["modelValue"])]),l("div",fl,[e[58]||(e[58]=l("div",{class:"filter-label"},"投资方式",-1)),a(X,{modelValue:t.investmentType,"onUpdate:modelValue":e[11]||(e[11]=n=>t.investmentType=n),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:d(()=>[a(R,{label:"全部",value:""}),a(R,{label:"赠送",value:"赠送"}),a(R,{label:"购买",value:"购买"})]),_:1},8,["modelValue"])]),l("div",pl,[e[59]||(e[59]=l("div",{class:"filter-label"},"用户ID",-1)),a(s,{modelValue:t.userId,"onUpdate:modelValue":e[12]||(e[12]=n=>t.userId=n),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])]),l("div",ml,[e[60]||(e[60]=l("div",{class:"filter-label"},"用户名",-1)),a(s,{modelValue:t.username,"onUpdate:modelValue":e[13]||(e[13]=n=>t.username=n),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),l("div",cl,[e[61]||(e[61]=l("div",{class:"filter-label"},"项目名称",-1)),a(X,{modelValue:t.projectName,"onUpdate:modelValue":e[14]||(e[14]=n=>t.projectName=n),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:d(()=>[a(R,{label:"全部",value:""}),a(R,{label:"VIP1",value:"VIP1"}),a(R,{label:"VIP2",value:"VIP2"}),a(R,{label:"VIP3",value:"VIP3"})]),_:1},8,["modelValue"])]),l("div",vl,[e[63]||(e[63]=l("div",{class:"filter-label"},"数量",-1)),l("div",gl,[a(s,{modelValue:t.quantityMin,"onUpdate:modelValue":e[15]||(e[15]=n=>t.quantityMin=n),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[62]||(e[62]=l("span",null,"-",-1)),a(s,{modelValue:t.quantityMax,"onUpdate:modelValue":e[16]||(e[16]=n=>t.quantityMax=n),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),l("div",_l,[e[64]||(e[64]=l("div",{class:"filter-label"},"投资方式",-1)),a(X,{modelValue:t.investmentType,"onUpdate:modelValue":e[17]||(e[17]=n=>t.investmentType=n),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:d(()=>[a(R,{label:"全部",value:""}),a(R,{label:"赠送",value:"赠送"}),a(R,{label:"购买",value:"购买"})]),_:1},8,["modelValue"])]),l("div",bl,[e[65]||(e[65]=l("div",{class:"filter-label"},"状态",-1)),a(X,{modelValue:t.status,"onUpdate:modelValue":e[18]||(e[18]=n=>t.status=n),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:d(()=>[a(R,{label:"全部",value:""}),a(R,{label:"进行中",value:"进行中"}),a(R,{label:"暂停",value:"暂停"}),a(R,{label:"完成",value:"完成"})]),_:1},8,["modelValue"])])])])]),l("div",yl,[e[77]||(e[77]=l("div",{class:"section-header"},[l("div",{class:"section-title"},"金额和收益")],-1)),l("div",hl,[l("div",Ml,[l("div",xl,[e[68]||(e[68]=l("div",{class:"filter-label"},"投资金额",-1)),l("div",$l,[a(s,{modelValue:t.amountMin,"onUpdate:modelValue":e[19]||(e[19]=n=>t.amountMin=n),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[67]||(e[67]=l("span",null,"-",-1)),a(s,{modelValue:t.amountMax,"onUpdate:modelValue":e[20]||(e[20]=n=>t.amountMax=n),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),l("div",Cl,[e[70]||(e[70]=l("div",{class:"filter-label"},"总收益",-1)),l("div",Tl,[a(s,{modelValue:t.totalProfitMin,"onUpdate:modelValue":e[21]||(e[21]=n=>t.totalProfitMin=n),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[69]||(e[69]=l("span",null,"-",-1)),a(s,{modelValue:t.totalProfitMax,"onUpdate:modelValue":e[22]||(e[22]=n=>t.totalProfitMax=n),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),l("div",Vl,[e[72]||(e[72]=l("div",{class:"filter-label"},"收益周期(小时)",-1)),l("div",wl,[a(s,{modelValue:t.profitCycleMin,"onUpdate:modelValue":e[23]||(e[23]=n=>t.profitCycleMin=n),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[71]||(e[71]=l("span",null,"-",-1)),a(s,{modelValue:t.profitCycleMax,"onUpdate:modelValue":e[24]||(e[24]=n=>t.profitCycleMax=n),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),l("div",Dl,[e[74]||(e[74]=l("div",{class:"filter-label"},"收益率(%)",-1)),l("div",Rl,[a(s,{modelValue:t.profitRateMin,"onUpdate:modelValue":e[25]||(e[25]=n=>t.profitRateMin=n),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[73]||(e[73]=l("span",null,"-",-1)),a(s,{modelValue:t.profitRateMax,"onUpdate:modelValue":e[26]||(e[26]=n=>t.profitRateMax=n),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),l("div",kl,[e[76]||(e[76]=l("div",{class:"filter-label"},"收益次数",-1)),l("div",Pl,[a(s,{modelValue:t.profitCountMin,"onUpdate:modelValue":e[27]||(e[27]=n=>t.profitCountMin=n),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[75]||(e[75]=l("span",null,"-",-1)),a(s,{modelValue:t.profitCountMax,"onUpdate:modelValue":e[28]||(e[28]=n=>t.profitCountMax=n),placeholder:"最大值",clearable:""},null,8,["modelValue"])])])])])]),l("div",Il,[e[81]||(e[81]=l("div",{class:"section-header"},[l("div",{class:"section-title"},"时间信息")],-1)),l("div",zl,[l("div",Ul,[l("div",Yl,[e[78]||(e[78]=l("div",{class:"filter-label"},"最后收益时间",-1)),a(re,{modelValue:t.lastProfitTimeRange,"onUpdate:modelValue":e[29]||(e[29]=n=>t.lastProfitTimeRange=n),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),l("div",Nl,[e[79]||(e[79]=l("div",{class:"filter-label"},"开始时间",-1)),a(re,{modelValue:t.startTimeRange,"onUpdate:modelValue":e[30]||(e[30]=n=>t.startTimeRange=n),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),l("div",Sl,[e[80]||(e[80]=l("div",{class:"filter-label"},"结束时间",-1)),a(re,{modelValue:t.endTimeRange,"onUpdate:modelValue":e[31]||(e[31]=n=>t.endTimeRange=n),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])])])])]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}}}),aa=xt(ql,[["__scopeId","data-v-c0e96b7b"]]);export{aa as default};
