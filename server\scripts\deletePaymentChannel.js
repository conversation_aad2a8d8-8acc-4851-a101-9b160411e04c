/**
 * 删除指定ID的支付通道
 */
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

const { PaymentChannel } = require('../models');
const sequelize = require('../config/database');

/**
 * 删除支付通道
 * @param {number} channelId - 支付通道ID
 */
async function deletePaymentChannel(channelId) {
  const transaction = await sequelize.transaction();
  
  try {
    console.log(`🔍 查找ID为${channelId}的支付通道...`);
    
    // 查找支付通道
    const channel = await PaymentChannel.findByPk(channelId, { transaction });
    
    if (!channel) {
      console.log(`❌ 未找到ID为${channelId}的支付通道`);
      await transaction.rollback();
      return false;
    }
    
    console.log(`✅ 找到支付通道: ${channel.name} (${channel.code})`);
    console.log(`📋 通道信息:`);
    console.log(`  ID: ${channel.id}`);
    console.log(`  名称: ${channel.name}`);
    console.log(`  代码: ${channel.code}`);
    console.log(`  国家代码: ${channel.country_code || '未设置'}`);
    console.log(`  存款开关: ${channel.deposit_enabled ? '开启' : '关闭'}`);
    console.log(`  取款开关: ${channel.withdraw_enabled ? '开启' : '关闭'}`);
    console.log(`  默认通道: ${channel.is_default ? '是' : '否'}`);
    
    // 删除支付通道
    await channel.destroy({ transaction });
    
    // 提交事务
    await transaction.commit();
    
    console.log(`✅ 支付通道删除成功`);
    return true;
    
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error(`❌ 删除失败:`, error.message);
    throw error;
  }
}

/**
 * 列出所有支付通道
 */
async function listPaymentChannels() {
  try {
    console.log('\n📋 当前所有支付通道:');
    
    const channels = await PaymentChannel.findAll({
      order: [['id', 'ASC']]
    });
    
    if (channels.length === 0) {
      console.log('  无支付通道');
      return;
    }
    
    channels.forEach(channel => {
      console.log(`  ID: ${channel.id} | 名称: ${channel.name} | 代码: ${channel.code} | 状态: ${channel.deposit_enabled ? '启用' : '禁用'}`);
    });
    
  } catch (error) {
    console.error('❌ 获取支付通道列表失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🚀 开始删除支付通道...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功\n');

    // 列出删除前的支付通道
    await listPaymentChannels();

    // 删除ID为2的支付通道
    const success = await deletePaymentChannel(2);
    
    if (success) {
      // 列出删除后的支付通道
      await listPaymentChannels();
      console.log('\n🎉 支付通道删除完成！');
    }

  } catch (error) {
    console.error('\n❌ 操作失败:', error.message);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  deletePaymentChannel,
  listPaymentChannels
};
