import request from '@/utils/request'

/**
 * 获取交易记录列表
 * @param params 查询参数
 * @returns 交易记录列表
 */
export function getTransactions(params: any) {
  return request({
    url: '/api/admin/transactions',
    method: 'get',
    params
  })
}

/**
 * 获取交易记录详情
 * @param id 交易记录ID
 * @returns 交易记录详情
 */
export function getTransaction(id: number) {
  return request({
    url: `/api/admin/transactions/${id}`,
    method: 'get'
  })
}

/**
 * 获取用户交易记录
 * @param userId 用户ID
 * @param params 查询参数
 * @returns 用户交易记录
 */
export function getUserTransactions(userId: number, params: any) {
  return request({
    url: `/api/admin/users/${userId}/transactions`,
    method: 'get',
    params
  })
}
