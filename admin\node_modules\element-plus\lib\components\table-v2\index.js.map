{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/table-v2/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TableV2 from './src/table-v2'\nimport AutoResizer from './src/components/auto-resizer'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport {\n  Alignment as TableV2Alignment,\n  FixedDir as TableV2FixedDir,\n  SortOrder as TableV2SortOrder,\n} from './src/constants'\nexport { default as TableV2 } from './src/table-v2'\nexport * from './src/auto-resizer'\nexport { placeholderSign as TableV2Placeholder } from './src/private'\n\nexport const ElTableV2: SFCWithInstall<typeof TableV2> = withInstall(TableV2)\nexport const ElAutoResizer: SFCWithInstall<typeof AutoResizer> =\n  withInstall(AutoResizer)\n\nexport type {\n  Column,\n  Columns,\n  SortBy,\n  SortState,\n  TableV2CustomizedHeaderSlotParam,\n} from './src/types'\nexport type { TableV2Instance } from './src/table-v2'\nexport * from './src/table'\nexport * from './src/row'\n\nexport type { HeaderCellSlotProps } from './src/renderers/header-cell'\n"], "names": ["withInstall", "TableV2", "AutoResizer"], "mappings": ";;;;;;;;;;;;;AAWY,MAAC,SAAS,GAAGA,mBAAW,CAACC,kBAAO,EAAE;AAClC,MAAC,aAAa,GAAGD,mBAAW,CAACE,wBAAW;;;;;;;;;;;;;"}