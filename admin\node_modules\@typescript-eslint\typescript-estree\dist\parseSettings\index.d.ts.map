{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/parseSettings/index.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,EAAE,MAAM,YAAY,CAAC;AAEtC,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,wCAAwC,CAAC;AACrF,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAEjD,KAAK,WAAW,GAAG,QAAQ,GAAG,mBAAmB,GAAG,YAAY,CAAC;AAGjE,OAAO,QAAQ,YAAY,CAAC;IAE1B,KAAK,gBAAgB;KAAG;CACzB;AAED,OAAO,QAAQ,gCAAgC,CAAC;IAE9C,KAAK,gBAAgB;KAAG;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IACH,eAAe,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACH,IAAI,EAAE,EAAE,CAAC,UAAU,GAAG,MAAM,CAAC;IAE7B;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,QAAQ,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;IAE7B;;;;OAIG;IACH,gCAAgC,EAAE,OAAO,CAAC;IAE1C;;OAEG;IACH,UAAU,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC;IAE7B;;OAEG;IACH,2CAA2C,EAAE,OAAO,CAAC;IAErD;;OAEG;IACH,qBAAqB,EAAE,OAAO,CAAC;IAE/B;;OAEG;IACH,2BAA2B,EAAE,sBAAsB,GAAG,SAAS,CAAC;IAEhE;;;;;;OAMG;IACH,gDAAgD,EAAE,OAAO,CAAC;IAE1D;;OAEG;IACH,mBAAmB,EAAE,MAAM,EAAE,CAAC;IAE9B;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,gBAAgB,EAAE,EAAE,CAAC,gBAAgB,CAAC;IAEtC;;;;OAIG;IACH,GAAG,EAAE,OAAO,CAAC;IAEb;;OAEG;IACH,GAAG,EAAE,OAAO,CAAC;IAEb;;OAEG;IACH,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IAE/B;;OAEG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B;;OAEG;IACH,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IAEtC;;OAEG;IACH,QAAQ,EAAE,SAAS,aAAa,EAAE,CAAC;IAEnC;;OAEG;IACH,KAAK,EAAE,OAAO,CAAC;IAEf;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,kCAAkC,EAAE,OAAO,CAAC;IAE5C;;OAEG;IACH,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC;IAEhC;;OAEG;IACH,kBAAkB,EAAE,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAE9C;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,MAAM,aAAa,GAAG,QAAQ,CAAC,oBAAoB,CAAC,CAAC"}