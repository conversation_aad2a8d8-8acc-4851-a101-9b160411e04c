/**
 * 收益调度器
 * 负责计算下一次收益时间并安排收益任务
 */
const redisClient = require('../utils/redisClient');
const { Investment, Project } = require('../models');
const logger = require('../utils/logger');
const profitLogger = require('../utils/profitLogger');

/**
 * 计算下一次收益时间
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @returns {Date} - 下一次收益时间
 */
const calculateNextProfitTime = (investment, project) => {
  // 如果有上次收益时间，直接在其基础上加一个周期
  if (investment.last_profit_time) {
    const lastProfitTime = new Date(investment.last_profit_time); // UTC时间
    const nextProfitTime = new Date(lastProfitTime.getTime() + project.profit_time * 60 * 60 * 1000);

    // 方案5.1：记录详细日志（标注UTC时间）
    profitLogger.profitCalc(investment.id, `基于上次收益时间(UTC): ${lastProfitTime.toISOString()}`);
    profitLogger.profitCalc(investment.id, `下一次收益时间(UTC): ${nextProfitTime.toISOString()}`);

    return nextProfitTime;
  }

  // 如果没有上次收益时间，使用投资开始时间（UTC时间）
  const baseTime = new Date(investment.start_time || investment.created_at);
  const nextProfitTime = new Date(baseTime.getTime() + project.profit_time * 60 * 60 * 1000);

  // 方案5.1：记录详细日志（标注UTC时间）
  profitLogger.firstProfit(investment.id, `基于开始时间(UTC): ${baseTime.toISOString()}`);
  profitLogger.firstProfit(investment.id, `首次收益时间(UTC): ${nextProfitTime.toISOString()}`);

  return nextProfitTime;
};

/**
 * 安排下一次收益任务
 * @param {Object} investment - 投资记录
 * @param {Object} [project] - 项目信息（可选）
 * @returns {Promise<Object>} - 结果
 */
const scheduleNextProfitTask = async (investment, project = null) => {
  try {
    // 如果没有传入项目信息，则查询
    if (!project) {
      project = await Project.findByPk(investment.project_id);
    }

    // 计算下一次收益时间
    const nextProfitTime = calculateNextProfitTime(investment, project);

    // 添加到Redis队列
    await redisClient.addProfitTask(investment.id, nextProfitTime);

    profitLogger.profitTask(investment.id, `下一次收益任务已安排，时间(UTC): ${nextProfitTime.toISOString()}`);

    return {
      success: true,
      investment_id: investment.id,
      next_profit_time: nextProfitTime
    };
  } catch (error) {
    logger.error(`安排投资ID ${investment.id} 的下一次收益任务失败:`, error);
    return {
      success: false,
      message: `安排下一次收益任务失败: ${error.message}`,
      investment_id: investment.id
    };
  }
};

module.exports = {
  calculateNextProfitTime,
  scheduleNextProfitTask
};
