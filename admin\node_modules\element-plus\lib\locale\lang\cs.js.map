{"version": 3, "file": "cs.js", "sources": ["../../../../../packages/locale/lang/cs.ts"], "sourcesContent": ["export default {\n  name: 'cs',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Vymazat',\n    },\n    datepicker: {\n      now: 'Te<PERSON>',\n      today: 'D<PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Vymazat',\n      confirm: 'OK',\n      selectDate: 'Vybrat datum',\n      selectTime: 'Vybrat čas',\n      startDate: 'Datum začátku',\n      startTime: 'Čas začátku',\n      endDate: 'Datum konce',\n      endTime: 'Čas konce',\n      prevYear: 'Předchozí rok',\n      nextYear: 'P<PERSON><PERSON><PERSON>t<PERSON> rok',\n      prevMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mě<PERSON>',\n      nextMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> měsíc',\n      day: 'Den',\n      week: 'Týden',\n      month: 'M<PERSON>s<PERSON>c',\n      year: 'Rok',\n      month1: 'Leden',\n      month2: 'Únor',\n      month3: 'Březen',\n      month4: '<PERSON>en',\n      month5: '<PERSON><PERSON><PERSON><PERSON>',\n      month6: '<PERSON>erve<PERSON>',\n      month7: 'Červene<PERSON>',\n      month8: 'Sr<PERSON>',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: 'Říjen',\n      month11: 'Listopad',\n      month12: 'Prosinec',\n      weeks: {\n        sun: 'Ne',\n        mon: 'Po',\n        tue: 'Út',\n        wed: 'St',\n        thu: 'Čt',\n        fri: 'Pá',\n        sat: 'So',\n      },\n      months: {\n        jan: 'Led',\n        feb: 'Úno',\n        mar: 'Bře',\n        apr: 'Dub',\n        may: 'Kvě',\n        jun: 'Čer',\n        jul: 'Čvc',\n        aug: 'Srp',\n        sep: 'Zář',\n        oct: 'Říj',\n        nov: 'Lis',\n        dec: 'Pro',\n      },\n    },\n    select: {\n      loading: 'Načítání',\n      noMatch: 'Žádná shoda',\n      noData: 'Žádná data',\n      placeholder: 'Vybrat',\n    },\n    mention: {\n      loading: 'Načítání',\n    },\n    cascader: {\n      noMatch: 'Žádná shoda',\n      loading: 'Načítání',\n      placeholder: 'Vybrat',\n      noData: 'Žádná data',\n    },\n    pagination: {\n      goto: 'Jít na',\n      pagesize: 'na stranu',\n      total: 'Celkem {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Zpráva',\n      confirm: 'OK',\n      cancel: 'Zrušit',\n      error: 'Neplatný vstup',\n    },\n    upload: {\n      deleteTip: 'Stisknout pro smazání',\n      delete: 'Vymazat',\n      preview: 'Náhled',\n      continue: 'Pokračovat',\n    },\n    table: {\n      emptyText: 'Žádná data',\n      confirmFilter: 'Potvrdit',\n      resetFilter: 'Resetovat',\n      clearFilter: 'Vše',\n      sumText: 'Celkem',\n    },\n    tree: {\n      emptyText: 'Žádná data',\n    },\n    transfer: {\n      noMatch: 'Žádná shoda',\n      noData: 'Žádná data',\n      titles: ['Seznam 1', 'Seznam 2'],\n      filterPlaceholder: 'Klíčové slovo',\n      noCheckedFormat: '{total} položek',\n      hasCheckedFormat: '{checked}/{total} vybráno',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,SAAS,EAAE,uBAAuB;AACxC,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,QAAQ,EAAE,uBAAuB;AACvC,MAAM,QAAQ,EAAE,4BAA4B;AAC5C,MAAM,SAAS,EAAE,iCAAiC;AAClD,MAAM,SAAS,EAAE,sCAAsC;AACvD,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,aAAa;AAC1B,QAAQ,GAAG,EAAE,aAAa;AAC1B,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,MAAM,EAAE,uBAAuB;AACrC,MAAM,WAAW,EAAE,QAAQ;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,wBAAwB;AACvC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,MAAM,EAAE,uBAAuB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6BAA6B;AAC9C,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,iBAAiB;AACjC,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,uBAAuB;AACxC,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,uBAAuB;AACxC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,MAAM,EAAE,uBAAuB;AACrC,MAAM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AACtC,MAAM,iBAAiB,EAAE,0BAA0B;AACnD,MAAM,eAAe,EAAE,sBAAsB;AAC7C,MAAM,gBAAgB,EAAE,8BAA8B;AACtD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}