/**
 * 系统参数 API 服务
 */
import { get } from '../../utils/request';

/**
 * 获取系统参数
 * @param {string} key - 参数键
 * @returns {Promise<Object>} 系统参数数据
 */
export const getSystemParam = (key) => {
  return get(`/mobile/system-params/key/${key}`);
};

/**
 * 获取指定分组的系统参数
 * @param {string} group - 参数分组
 * @returns {Promise<Object>} 系统参数列表
 */
export const getSystemParamsByGroup = (group) => {
  return get(`/mobile/system-params/group/${group}`);
};

/**
 * 获取团队规则
 * @returns {Promise<Object>} 团队规则数据
 */
export const getTeamRules = () => {
  return get('/mobile/system-params/team-rules');
};

/**
 * 获取充值规则
 * @returns {Promise<Object>} 充值规则数据
 */
export const getRechargeRules = () => {
  return get('/mobile/system-params/key/[site.recharge_rules]');
};

/**
 * 获取取款规则
 * @returns {Promise<Object>} 取款规则数据
 */
export const getWithdrawRules = () => {
  return get('/mobile/system-params/key/[site.withdraw_rules]');
};

/**
 * 获取弹窗公告
 * @returns {Promise<Object>} 弹窗公告数据
 */
export const getPopupNotice = () => {
  return get('/mobile/system-params/key/[site.popup_notice]');
};

/**
 * 获取弹窗按钮文本
 * @returns {Promise<Object>} 弹窗按钮文本数据
 */
export const getPopupButtonText = () => {
  return get('/mobile/system-params/key/[site.popup_button_text]');
};

/**
 * 获取弹窗公告按钮链接
 * @returns {Promise<Object>} 弹窗公告按钮链接数据
 */
export const getPopupButtonLink = () => {
  return get('/mobile/system-params/key/[site.popup_button_link]');
};

/**
 * 获取客服说明文本
 * @returns {Promise<Object>} 客服说明文本数据
 */
export const getCustomerServiceInfo = () => {
  return get('/mobile/system-params/key/[site.customer_service_notice]');
};

/**
 * 获取网站域名
 * @returns {Promise<Object>} 网站域名数据
 */
export const getSiteDomain = () => {
  return get('/mobile/system-params/key/[site.domain]');
};

/**
 * 获取APP下载二维码
 * @returns {Promise<Object>} 二维码数据
 */
export const getAppQRCode = () => {
  return get('/app/qrcode');
};

/**
 * 获取APP下载信息
 * @returns {Promise<Object>} APP下载信息
 */
export const getAppDownloadInfo = () => {
  return get('/app/download-info');
};
