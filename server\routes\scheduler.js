/**
 * 定时任务路由
 */
const express = require('express');
const router = express.Router();
const schedulers = require('../schedulers');
const { verifyAdminToken } = require('../middlewares/authMiddleware');

// 手动触发收益发放的API已被移除，因为它们可能导致重复收益问题

/**
 * @swagger
 * /api/admin/schedulers/compensation-check:
 *   post:
 *     summary: 手动触发收益发放补偿检查
 *     tags: [Schedulers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功触发收益发放补偿检查
 *       500:
 *         description: 服务器内部错误
 */
router.post('/compensation-check', verifyAdminToken, async (req, res) => {
  try {
    const result = await schedulers.triggerCompensationCheck();
    return res.status(200).json({
      code: 200,
      message: '收益发放补偿检查触发成功',
      data: result
    });
  } catch (error) {
    console.error('触发收益发放补偿检查失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

/**
 * @swagger
 * /api/admin/schedulers/deep-compensation-check:
 *   post:
 *     summary: 手动触发深度补偿检查（检查所有投资的所有理论收益时间点）
 *     tags: [Schedulers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功触发深度补偿检查
 *       500:
 *         description: 服务器内部错误
 */
router.post('/deep-compensation-check', verifyAdminToken, async (req, res) => {
  try {
    const result = await schedulers.triggerDeepCompensationCheck();
    return res.status(200).json({
      code: 200,
      message: '深度补偿检查触发成功',
      data: result
    });
  } catch (error) {
    console.error('触发深度补偿检查失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

/**
 * @swagger
 * /api/admin/schedulers/long-cycle-check:
 *   post:
 *     summary: 手动触发长周期产品收益检查
 *     tags: [Schedulers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功触发长周期产品收益检查
 *       500:
 *         description: 服务器内部错误
 */
router.post('/long-cycle-check', verifyAdminToken, async (req, res) => {
  try {
    const result = await schedulers.triggerLongCycleCheck();
    return res.status(200).json({
      code: 200,
      message: '长周期产品收益检查触发成功',
      data: result
    });
  } catch (error) {
    console.error('触发长周期产品收益检查失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

/**
 * @swagger
 * /api/admin/schedulers/queue-stats:
 *   get:
 *     summary: 获取任务队列统计信息
 *     tags: [Schedulers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取任务队列统计信息
 *       500:
 *         description: 服务器内部错误
 */
router.get('/queue-stats', verifyAdminToken, async (req, res) => {
  try {
    const result = await schedulers.getQueueStats();
    return res.status(200).json({
      code: 200,
      message: '获取任务队列统计信息成功',
      data: result
    });
  } catch (error) {
    console.error('获取任务队列统计信息失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

/**
 * @swagger
 * /api/admin/schedulers/kbpay-order-check:
 *   post:
 *     summary: 手动触发KB支付订单检查
 *     tags: [Schedulers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功触发KB支付订单检查
 *       500:
 *         description: 服务器内部错误
 */
router.post('/kbpay-order-check', verifyAdminToken, async (req, res) => {
  try {
    const result = await schedulers.triggerKBPayOrderCheck();
    return res.status(200).json({
      code: 200,
      message: 'KB支付订单检查触发成功',
      data: result
    });
  } catch (error) {
    console.error('触发KB支付订单检查失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

/**
 * @swagger
 * /api/admin/schedulers/update-today-statistics:
 *   post:
 *     summary: 手动更新今日统计数据
 *     tags: [Schedulers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功更新今日统计数据
 *       500:
 *         description: 服务器内部错误
 */
router.post('/update-today-statistics', verifyAdminToken, async (req, res) => {
  try {
    const result = await schedulers.updateTodayStatistics();
    return res.status(200).json({
      code: 200,
      message: '今日统计数据更新成功',
      data: result
    });
  } catch (error) {
    console.error('更新今日统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
});

module.exports = router;
