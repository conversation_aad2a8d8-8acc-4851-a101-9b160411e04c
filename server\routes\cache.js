/**
 * 缓存路由
 */
const express = require('express');
const router = express.Router();
const cacheController = require('../controllers/cacheController');
const { verifyAdminToken } = require('../middlewares/authMiddleware');

/**
 * @swagger
 * /api/admin/cache/stats:
 *   get:
 *     summary: 获取缓存统计信息
 *     tags: [Cache]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取缓存统计信息成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取缓存统计信息成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     usedMemory:
 *                       type: string
 *                       example: 1.25M
 *                     usedMemoryPeak:
 *                       type: string
 *                       example: 1.30M
 *                     connectedClients:
 *                       type: string
 *                       example: 1
 *                     uptime:
 *                       type: string
 *                       example: 3600
 *                     hitRate:
 *                       type: string
 *                       example: 75.5%
 */
router.get('/stats', verifyAdminToken, cacheController.getStats);

/**
 * @swagger
 * /api/admin/cache/clear:
 *   post:
 *     summary: 清除指定前缀的缓存
 *     tags: [Cache]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - prefix
 *             properties:
 *               prefix:
 *                 type: string
 *                 description: 缓存前缀
 *                 example: "user:"
 *     responses:
 *       200:
 *         description: 清除缓存成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "清除前缀为 user: 的缓存成功"
 *                 data:
 *                   type: null
 *                   example: null
 */
router.post('/clear', verifyAdminToken, cacheController.clearByPrefix);

/**
 * @swagger
 * /api/admin/cache/clear-all:
 *   post:
 *     summary: 清除所有缓存
 *     tags: [Cache]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 清除所有缓存成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 清除所有缓存成功
 *                 data:
 *                   type: null
 *                   example: null
 */
router.post('/clear-all', verifyAdminToken, cacheController.clearAll);

module.exports = router;
