/**
 * FOX投资平台数据库迁移脚本
 * 作者：FOX开发团队
 * 日期：2025-06-04
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const config = require('../config/database');

// 读取SQL文件
const readSqlFile = (filename) => {
  const filePath = path.join(__dirname, filename);
  return fs.readFileSync(filePath, 'utf8');
};

// 执行SQL脚本
const executeSqlScript = async (connection, script) => {
  const statements = script
    .split(';')
    .filter(statement => statement.trim() !== '')
    .map(statement => statement.trim() + ';');

  for (const statement of statements) {
    try {
      await connection.query(statement);
      console.log(`执行SQL语句成功: ${statement.substring(0, 50)}...`);
    } catch (error) {
      console.error(`执行SQL语句失败: ${statement.substring(0, 100)}...`);
      console.error(`错误信息: ${error.message}`);
      throw error;
    }
  }
};

// 主迁移函数
const migrate = async () => {
  let connection;

  try {
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'MySQL3352~!',
      database: 'fox_db',
      multipleStatements: true
    });

    console.log('数据库连接成功');

    // 1. 创建新表结构
    console.log('步骤1: 创建新表结构');
    const createTablesScript = readSqlFile('database_restructure.sql');
    await executeSqlScript(connection, createTablesScript);

    // 2. 迁移数据
    console.log('步骤2: 迁移数据');
    const migrateDataScript = readSqlFile('data_migration.sql');
    await executeSqlScript(connection, migrateDataScript);

    // 3. 添加外键约束
    console.log('步骤3: 添加外键约束');
    const addForeignKeysScript = readSqlFile('add_foreign_keys.sql');
    await executeSqlScript(connection, addForeignKeysScript);

    // 4. 重命名表
    console.log('步骤4: 重命名表');
    const renameTablesScript = readSqlFile('rename_tables.sql');
    await executeSqlScript(connection, renameTablesScript);

    console.log('数据库迁移完成');
  } catch (error) {
    console.error('数据库迁移失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
};

// 执行迁移
migrate();
