/**
 * 充值相关 API 服务
 */
import { get, post } from '../../utils/request';

/**
 * 获取支付通道列表
 * @returns {Promise<Object>} 支付通道列表数据
 */
export const getPaymentChannels = () => {
  return get('/mobile/payment-channels');
};

/**
 * 创建充值订单
 * @param {Object} data - 充值订单数据
 * @param {number} data.amount - 充值金额
 * @param {number} data.payment_channel_id - 支付通道ID
 * @returns {Promise<Object>} 充值订单数据
 */
export const createRechargeOrder = (data) => {
  return post('/mobile/deposits', data);
};

/**
 * 获取充值记录
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=10] - 每页数量
 * @param {string} [params.status] - 订单状态，可选值："all"(全部)、"pending"(待支付)、"success"(成功)、"failed"(失败)、"cancelled"(已取消)
 * @returns {Promise<Object>} 充值记录数据
 */
export const getRechargeRecords = (params = {}) => {
  return get('/mobile/deposits', {
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      ...params
    }
  });
};

/**
 * 获取充值订单详情
 * @param {string} orderNumber - 订单号
 * @returns {Promise<Object>} 充值订单详情数据
 */
export const getRechargeOrderDetail = (orderNumber) => {
  return get(`/mobile/deposits/${orderNumber}`);
};

/**
 * 取消充值订单
 * @param {string} orderNumber - 订单号
 * @returns {Promise<Object>} 取消结果
 */
export const cancelRechargeOrder = (orderNumber) => {
  return post(`/mobile/deposits/${orderNumber}/cancel`);
};
