/**
 * 方案5.1：UTC迁移验证脚本
 * 用于验证时区迁移是否成功
 */

const sequelize = require('../config/database').sequelize;
const { Investment, InvestmentProfit, User } = require('../models');
const moment = require('moment');

/**
 * 验证时间数据是否合理
 */
async function validateTimeData() {
  console.log('🔍 开始验证UTC迁移结果...\n');

  try {
    // 1. 验证投资表时间数据
    console.log('📊 验证投资表时间数据...');
    const investments = await Investment.findAll({
      limit: 10,
      order: [['created_at', 'DESC']]
    });

    console.log('投资表样本数据：');
    investments.forEach((inv, index) => {
      console.log(`  ${index + 1}. ID: ${inv.id}`);
      console.log(`     开始时间: ${inv.start_time} (${moment.utc(inv.start_time).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
      console.log(`     上次收益: ${inv.last_profit_time || '无'} ${inv.last_profit_time ? `(${moment.utc(inv.last_profit_time).format('YYYY-MM-DD HH:mm:ss')} UTC)` : ''}`);
      console.log(`     创建时间: ${inv.created_at} (${moment.utc(inv.created_at).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
      console.log('');
    });

    // 2. 验证收益表时间数据
    console.log('💰 验证收益表时间数据...');
    const profits = await InvestmentProfit.findAll({
      limit: 10,
      order: [['created_at', 'DESC']]
    });

    console.log('收益表样本数据：');
    profits.forEach((profit, index) => {
      console.log(`  ${index + 1}. ID: ${profit.id}`);
      console.log(`     收益时间: ${profit.profit_time} (${moment.utc(profit.profit_time).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
      console.log(`     创建时间: ${profit.created_at} (${moment.utc(profit.created_at).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
      console.log('');
    });

    // 3. 验证用户表时间数据
    console.log('👥 验证用户表时间数据...');
    const users = await User.findAll({
      limit: 5,
      order: [['created_at', 'DESC']]
    });

    console.log('用户表样本数据：');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ID: ${user.id}, 用户名: ${user.username}`);
      console.log(`     注册时间: ${user.created_at} (${moment.utc(user.created_at).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
      console.log(`     最后登录: ${user.last_login_time || '无'} ${user.last_login_time ? `(${moment.utc(user.last_login_time).format('YYYY-MM-DD HH:mm:ss')} UTC)` : ''}`);
      console.log('');
    });

    // 4. 检查时间范围合理性
    console.log('📅 检查时间范围合理性...');

    const timeRanges = await sequelize.query(`
      SELECT 
        'investments' as table_name,
        MIN(start_time) as min_time,
        MAX(start_time) as max_time,
        COUNT(*) as record_count
      FROM investments
      UNION ALL
      SELECT 
        'investment_profits' as table_name,
        MIN(profit_time) as min_time,
        MAX(profit_time) as max_time,
        COUNT(*) as record_count
      FROM investment_profits
      UNION ALL
      SELECT 
        'users' as table_name,
        MIN(created_at) as min_time,
        MAX(created_at) as max_time,
        COUNT(*) as record_count
      FROM users
    `);

    console.log('各表时间范围：');
    timeRanges[0].forEach(range => {
      console.log(`  ${range.table_name}:`);
      console.log(`    最早时间: ${range.min_time} (${moment.utc(range.min_time).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
      console.log(`    最晚时间: ${range.max_time} (${moment.utc(range.max_time).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
      console.log(`    记录数量: ${range.record_count}`);
      console.log('');
    });

    // 5. 验证时区配置
    console.log('⚙️ 验证时区配置...');
    const timezoneConfig = await sequelize.query(`
      SELECT param_value 
      FROM system_params 
      WHERE param_key = '[site.timezone]' 
      LIMIT 1
    `);

    if (timezoneConfig[0].length > 0) {
      console.log(`系统时区配置: ${timezoneConfig[0][0].param_value}`);
    } else {
      console.log('⚠️  警告：未找到系统时区配置');
    }

    // 6. 检查数据库时区设置
    console.log('\n🗄️ 检查数据库时区设置...');
    const dbTimezone = await sequelize.query('SELECT @@session.time_zone as session_timezone, @@system_time_zone as system_timezone');
    console.log(`数据库会话时区: ${dbTimezone[0][0].session_timezone}`);
    console.log(`数据库系统时区: ${dbTimezone[0][0].system_timezone}`);

    // 7. 时间一致性检查
    console.log('\n🔄 时间一致性检查...');
    const now = new Date();
    const dbTime = await sequelize.query('SELECT NOW() as db_now, UTC_TIMESTAMP() as db_utc');
    
    console.log(`应用程序时间: ${now.toISOString()}`);
    console.log(`数据库当前时间: ${dbTime[0][0].db_now}`);
    console.log(`数据库UTC时间: ${dbTime[0][0].db_utc}`);

    const timeDiff = Math.abs(now.getTime() - new Date(dbTime[0][0].db_utc).getTime());
    console.log(`时间差异: ${timeDiff}ms`);

    if (timeDiff > 5000) { // 5秒
      console.log('⚠️  警告：应用程序时间与数据库时间差异较大');
    } else {
      console.log('✅ 时间同步正常');
    }

    console.log('\n✅ UTC迁移验证完成！');
    
    return {
      success: true,
      message: 'UTC迁移验证完成',
      timeDifference: timeDiff
    };

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    return {
      success: false,
      message: '验证失败: ' + error.message
    };
  }
}

/**
 * 检查收益发放逻辑
 */
async function validateProfitLogic() {
  console.log('\n🎯 验证收益发放逻辑...');

  try {
    // 查找一个有收益记录的投资
    const investment = await Investment.findOne({
      where: {
        status: 'active'
      },
      include: [
        {
          model: InvestmentProfit,
          as: 'profits',
          limit: 1,
          order: [['profit_time', 'DESC']]
        }
      ]
    });

    if (!investment) {
      console.log('⚠️  未找到活跃的投资记录');
      return { success: true, message: '无活跃投资记录' };
    }

    console.log(`检查投资ID: ${investment.id}`);
    console.log(`投资开始时间: ${investment.start_time} (${moment.utc(investment.start_time).format('YYYY-MM-DD HH:mm:ss')} UTC)`);
    console.log(`上次收益时间: ${investment.last_profit_time || '无'} ${investment.last_profit_time ? `(${moment.utc(investment.last_profit_time).format('YYYY-MM-DD HH:mm:ss')} UTC)` : ''}`);

    // 模拟收益时间计算
    const now = moment.utc();
    const lastProfitTime = investment.last_profit_time ? 
      moment.utc(investment.last_profit_time) : 
      moment.utc(investment.start_time);

    console.log(`当前UTC时间: ${now.format('YYYY-MM-DD HH:mm:ss')}`);
    console.log(`基准时间: ${lastProfitTime.format('YYYY-MM-DD HH:mm:ss')}`);

    const hoursDiff = now.diff(lastProfitTime, 'hours');
    console.log(`时间差异: ${hoursDiff} 小时`);

    console.log('✅ 收益逻辑验证完成');
    
    return {
      success: true,
      message: '收益逻辑验证完成',
      hoursDifference: hoursDiff
    };

  } catch (error) {
    console.error('❌ 收益逻辑验证失败:', error);
    return {
      success: false,
      message: '收益逻辑验证失败: ' + error.message
    };
  }
}

/**
 * 主验证函数
 */
async function main() {
  console.log('🚀 方案5.1 UTC迁移验证脚本');
  console.log('=====================================\n');

  try {
    // 验证时间数据
    const timeValidation = await validateTimeData();
    
    // 验证收益逻辑
    const profitValidation = await validateProfitLogic();

    console.log('\n📋 验证总结:');
    console.log(`时间数据验证: ${timeValidation.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`收益逻辑验证: ${profitValidation.success ? '✅ 通过' : '❌ 失败'}`);

    if (timeValidation.success && profitValidation.success) {
      console.log('\n🎉 所有验证通过！UTC迁移成功！');
    } else {
      console.log('\n⚠️  发现问题，请检查上述错误信息');
    }

  } catch (error) {
    console.error('❌ 验证脚本执行失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  validateTimeData,
  validateProfitLogic,
  main
};
