/**
 * 查询统计表中的数据
 */
const { DailyStatistic, TotalStatistic } = require('./models');
const sequelize = require('./config/database');

async function checkStatisticsData() {
  try {
    // 检查连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 查询最近5天的统计数据
    const dailyStats = await DailyStatistic.findAll({
      order: [['date', 'DESC']],
      limit: 5
    });
    
    console.log('最近5天的统计数据:');
    dailyStats.forEach(stat => {
      console.log(`日期: ${stat.date}`);
      console.log(`  新增会员数: ${stat.new_user_count}`);
      console.log(`  充值金额: ${stat.deposit_amount}`);
      console.log(`  充值笔数: ${stat.deposit_count}`);
      console.log(`  充值人数: ${stat.deposit_user_count}`);
      console.log(`  取款金额: ${stat.withdrawal_amount}`);
      console.log(`  取款笔数: ${stat.withdrawal_count}`);
      console.log(`  投资金额: ${stat.investment_amount}`);
      console.log(`  平台利润: ${stat.platform_profit}`);
      console.log('---');
    });
    
    // 查询累计统计数据
    const totalStats = await TotalStatistic.findOne();
    
    console.log('累计统计数据:');
    console.log(`  会员总数: ${totalStats.total_user_count}`);
    console.log(`  总充值金额: ${totalStats.total_deposit_amount}`);
    console.log(`  总充值笔数: ${totalStats.total_deposit_count}`);
    console.log(`  总充值人数: ${totalStats.total_deposit_user_count}`);
    console.log(`  总取款金额: ${totalStats.total_withdrawal_amount}`);
    console.log(`  总取款笔数: ${totalStats.total_withdrawal_count}`);
    console.log(`  总投资金额: ${totalStats.total_investment_amount}`);
    console.log(`  总平台利润: ${totalStats.total_platform_profit}`);
    console.log(`  最后更新日期: ${totalStats.last_updated_date}`);
    
    process.exit(0);
  } catch (error) {
    console.error('查询统计数据失败:', error);
    process.exit(1);
  }
}

checkStatisticsData();
