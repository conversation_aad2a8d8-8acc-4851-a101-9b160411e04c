import type { CSSProperties, ExtractPropTypes } from 'vue';
export declare const sliderMarkerProps: {
    readonly mark: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }) | ((new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }))[], unknown, unknown, undefined, boolean>;
};
export type SliderMarkerProps = ExtractPropTypes<typeof sliderMarkerProps>;
declare const _default: import("vue").DefineComponent<{
    readonly mark: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }) | ((new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }))[], unknown, unknown, undefined, boolean>;
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<ExtractPropTypes<{
    readonly mark: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }) | ((new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }))[], unknown, unknown, undefined, boolean>;
}>>, {
    readonly mark: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }) | ((new (...args: any[]) => string | {
        style: CSSProperties;
        label: any;
    }) | (() => string | {
        style: CSSProperties;
        label: any;
    }))[], unknown, unknown>;
}>;
export default _default;
