/* empty css             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                *//* empty css                *//* empty css                  */import"./el-form-item-l0sNRNKZ.js";/* empty css                   *//* empty css                  */import{d as G,a as C,r as p,o as O,c as x,b as s,e,w as o,aa as R,ac as W,am as H,y as V,aV as J,E as K,h as Q,i as X,V as Y,n as g,m as Z,a8 as ee,ap as le,al as ae,x as oe,j as U,aG as te,ab as se,b6 as ne,p as b,g as h,_ as ie}from"./index-LncY9lAB.js";const re={class:"profile-container"},de={class:"page-content"},pe={class:"left-content"},ue={class:"profile-header"},me={class:"profile-info"},ce={class:"profile-name"},ge={class:"profile-id"},fe={class:"profile-content"},_e={class:"form-actions"},we={class:"right-content"},be={class:"log-header"},ve={class:"link-container"},Ve={class:"log-link"},he={class:"pagination-container"},Pe={class:"dialog-footer"},ke="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",ye=G({__name:"index",setup(Ee){const t=C({id:1,username:"AbuAdmin123",email:"<EMAIL>",phone:"173-0831-01",ip:"***********",registerTime:"2025-04-05 05:43:10",lastLoginTime:"2025-04-05 05:43:10",avatar:""}),u=p(!1),P=p({}),T=p([{id:201,title:"首页",link:"/octadmin.php/dashboard/chart",ip:"************",time:"2025-04-06 22:04:46"},{id:200,title:"首页",link:"/octadmin.php/dashboard/chart",ip:"************",time:"2025-04-06 22:04:40"},{id:199,title:"首页",link:"/octadmin.php/dashboard/chart",ip:"************",time:"2025-04-06 22:04:12"},{id:198,title:"登录",link:"/octadmin.php/index/login?url=%2Foctac",ip:"************",time:"2025-04-06 22:04:10"},{id:197,title:"首页",link:"/octadmin.php/dashboard/chart",ip:"*************",time:"2025-04-06 22:03:46"}]),f=p(1),v=p(10),z=p(171),_=p(!1),n=C({currentPassword:"",newPassword:"",confirmPassword:""}),I={currentPassword:[{required:!0,message:"请输入当前密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(d,l,w)=>{l!==n.newPassword?w(new Error("两次输入的密码不一致")):w()},trigger:"blur"}]},D=()=>{P.value={...t},u.value=!0},F=()=>{Object.assign(t,P.value),u.value=!1},B=()=>{setTimeout(()=>{b.success("个人资料保存成功"),u.value=!1},500)},S=()=>{setTimeout(()=>{b.success("密码修改成功"),_.value=!1,n.currentPassword="",n.newPassword="",n.confirmPassword=""},500)},A=d=>{navigator.clipboard.writeText(d).then(()=>{b.success("链接已复制到剪贴板")}).catch(()=>{b.error("复制失败，请手动复制")})},L=d=>{v.value=d,f.value=1},j=d=>{f.value=d};return O(()=>{}),(d,l)=>{const w=H,q=J,i=X,r=Q,m=Z,k=K,y=R,E=oe,c=ae,N=le,M=se,$=W;return h(),x("div",re,[s("div",de,[s("div",pe,[e(y,{class:"profile-card"},{default:o(()=>[s("div",ue,[e(w,{size:90,src:t.avatar||ke,class:"profile-avatar"},null,8,["src"]),s("div",me,[s("h1",ce,V(t.username),1),s("p",ge,"ID："+V(t.id),1)])]),e(q),s("div",fe,[e(k,{ref:"profileForm",model:t,"label-width":"120px","label-position":"right"},{default:o(()=>[e(r,{label:"用户名："},{default:o(()=>[e(i,{modelValue:t.username,"onUpdate:modelValue":l[0]||(l[0]=a=>t.username=a),disabled:""},null,8,["modelValue"])]),_:1}),e(r,{label:"邮箱："},{default:o(()=>[e(i,{modelValue:t.email,"onUpdate:modelValue":l[1]||(l[1]=a=>t.email=a),disabled:!u.value},null,8,["modelValue","disabled"])]),_:1}),e(r,{label:"手机号："},{default:o(()=>[e(i,{modelValue:t.phone,"onUpdate:modelValue":l[2]||(l[2]=a=>t.phone=a),disabled:!u.value},null,8,["modelValue","disabled"])]),_:1}),e(r,{label:"IP："},{default:o(()=>[e(i,{modelValue:t.ip,"onUpdate:modelValue":l[3]||(l[3]=a=>t.ip=a),disabled:""},null,8,["modelValue"])]),_:1}),e(r,{label:"注册时间："},{default:o(()=>[e(i,{modelValue:t.registerTime,"onUpdate:modelValue":l[4]||(l[4]=a=>t.registerTime=a),disabled:""},null,8,["modelValue"])]),_:1}),e(r,{label:"最后登录时间："},{default:o(()=>[e(i,{modelValue:t.lastLoginTime,"onUpdate:modelValue":l[5]||(l[5]=a=>t.lastLoginTime=a),disabled:""},null,8,["modelValue"])]),_:1}),s("div",_e,[u.value?(h(),x(ee,{key:1},[e(m,{type:"primary",onClick:B},{default:o(()=>l[14]||(l[14]=[g("确定")])),_:1}),e(m,{onClick:F},{default:o(()=>l[15]||(l[15]=[g("取消")])),_:1})],64)):(h(),Y(m,{key:0,type:"primary",onClick:D},{default:o(()=>l[13]||(l[13]=[g("编辑资料")])),_:1}))])]),_:1},8,["model"])])]),_:1})]),s("div",we,[e(y,{class:"log-card"},{header:o(()=>[s("div",be,[e(E,{class:"log-icon"},{default:o(()=>[e(U(ne))]),_:1}),l[16]||(l[16]=s("span",{class:"log-title"},"操作日志",-1))])]),default:o(()=>[e(N,{data:T.value,style:{width:"100%"},border:"",stripe:"","highlight-current-row":"","header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"},"cell-style":{padding:"0"}},{default:o(()=>[e(c,{prop:"id",label:"ID",width:"70",align:"center"}),e(c,{prop:"title",label:"标题","min-width":"120",align:"center"}),e(c,{prop:"link",label:"链接","min-width":"200",align:"center"},{default:o(a=>[s("div",ve,[s("span",Ve,V(a.row.link),1),e(m,{type:"primary",size:"small",circle:"",class:"copy-button",onClick:Ce=>A(a.row.link)},{default:o(()=>[e(E,null,{default:o(()=>[e(U(te))]),_:1})]),_:2},1032,["onClick"])])]),_:1}),e(c,{prop:"ip",label:"IP","min-width":"120",align:"center"}),e(c,{prop:"time",label:"操作时间","min-width":"160",align:"center",sortable:""})]),_:1},8,["data"]),s("div",he,[e(M,{"current-page":f.value,"onUpdate:currentPage":l[6]||(l[6]=a=>f.value=a),"page-size":v.value,"onUpdate:pageSize":l[7]||(l[7]=a=>v.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:z.value,onSizeChange:L,onCurrentChange:j,"pager-count":7,background:""},null,8,["current-page","page-size","total"])])]),_:1})])]),e($,{modelValue:_.value,"onUpdate:modelValue":l[12]||(l[12]=a=>_.value=a),title:"修改密码",width:"500px",center:""},{footer:o(()=>[s("span",Pe,[e(m,{type:"primary",onClick:S},{default:o(()=>l[17]||(l[17]=[g("确定")])),_:1}),e(m,{onClick:l[11]||(l[11]=a=>_.value=!1)},{default:o(()=>l[18]||(l[18]=[g("取消")])),_:1})])]),default:o(()=>[e(k,{ref:"passwordForm",model:n,rules:I,"label-width":"120px","label-position":"right"},{default:o(()=>[e(r,{label:"当前密码",prop:"currentPassword"},{default:o(()=>[e(i,{modelValue:n.currentPassword,"onUpdate:modelValue":l[8]||(l[8]=a=>n.currentPassword=a),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),e(r,{label:"新密码",prop:"newPassword"},{default:o(()=>[e(i,{modelValue:n.newPassword,"onUpdate:modelValue":l[9]||(l[9]=a=>n.newPassword=a),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),e(r,{label:"确认新密码",prop:"confirmPassword"},{default:o(()=>[e(i,{modelValue:n.confirmPassword,"onUpdate:modelValue":l[10]||(l[10]=a=>n.confirmPassword=a),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),$e=ie(ye,[["__scopeId","data-v-1264940a"]]);export{$e as default};
