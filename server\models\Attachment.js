const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Attachment = sequelize.define('Attachment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: '未归类',
    comment: '分类',
  },
  filename: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '文件名',
  },
  original_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '原始文件名',
  },
  file_path: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '文件路径',
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '文件大小(字节)',
  },
  file_type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '文件类型',
  },
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'MIME类型',
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '元数据',
  },
  storage_engine: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'local',
    comment: '存储引擎',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'attachments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Attachment.associate = (models) => {
  // 附件与用户级别
  Attachment.hasMany(models.UserLevel, {
    foreignKey: 'image_id',
    as: 'user_levels',
  });
  
  // 附件与项目(图片)
  Attachment.hasMany(models.Project, {
    foreignKey: 'image_id',
    as: 'projects_images',
  });
  
  // 附件与项目(视频)
  Attachment.hasMany(models.Project, {
    foreignKey: 'video_id',
    as: 'projects_videos',
  });
  
  // 附件与轮播图
  Attachment.hasMany(models.Banner, {
    foreignKey: 'attachment_id',
    as: 'banners',
  });
};

module.exports = Attachment;
