const express = require('express');
const router = express.Router();
const bankMappingController = require('../../controllers/bankMappingController');
const authMiddleware = require('../../middlewares/authMiddleware');

// 银行管理路由
router.get('/banks', authMiddleware.verifyAdminToken, bankMappingController.getAllBanks);

// 银行编码映射路由
router.get('/bank-mappings', authMiddleware.verifyAdminToken, bankMappingController.getBankMappings);
router.post('/bank-mappings', authMiddleware.verifyAdminToken, bankMappingController.upsertBankMapping);
router.delete('/bank-mappings/:id', authMiddleware.verifyAdminToken, bankMappingController.deleteBankMapping);

module.exports = router;
