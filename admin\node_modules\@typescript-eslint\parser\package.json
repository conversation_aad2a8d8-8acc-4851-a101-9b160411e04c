{"name": "@typescript-eslint/parser", "version": "6.21.0", "description": "An ESLint custom parser which leverages TypeScript ESTree", "files": ["dist", "_ts4.3", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/parser"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "BSD-2-<PERSON><PERSON>", "keywords": ["ast", "ecmascript", "javascript", "typescript", "parser", "syntax", "eslint"], "scripts": {"build": "tsc -b tsconfig.build.json", "postbuild": "downlevel-dts dist _ts4.3/dist --to=4.3", "clean": "tsc -b tsconfig.build.json --clean", "postclean": "rimraf dist && rimraf _ts4.3 && rimraf coverage", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "lint": "npx nx lint", "test": "jest --coverage", "typecheck": "tsc -p tsconfig.json --noEmit"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}, "dependencies": {"@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/types": "6.21.0", "@typescript-eslint/typescript-estree": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4"}, "devDependencies": {"@types/glob": "*", "downlevel-dts": "*", "glob": "*", "jest": "29.7.0", "prettier": "^3.0.3", "rimraf": "*", "typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "typesVersions": {"<4.7": {"*": ["_ts4.3/*"]}}}