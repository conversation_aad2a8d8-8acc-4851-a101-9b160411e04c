'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn('projects', 'max_profit_times', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '最多收益次数'
      });
      console.log('Column max_profit_times added successfully');
    } catch (error) {
      console.log('Error adding column max_profit_times:', error.message);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('projects', 'max_profit_times');
    } catch (error) {
      console.log('Error removing column max_profit_times:', error.message);
    }
  }
};
