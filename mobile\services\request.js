/**
 * 请求工具类
 */

// 服务器基础URL
const BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://api.example.com' // 生产环境API地址
  : ''; // 开发环境API地址，使用相对路径

/**
 * 请求封装
 * @param {Object} options 请求选项
 * @param {string} options.url 请求地址
 * @param {string} [options.method='GET'] 请求方法
 * @param {Object} [options.data] 请求数据
 * @param {Object} [options.params] 查询参数
 * @param {Object} [options.headers] 请求头
 * @returns {Promise<any>} 响应数据
 */
export default function request(options) {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = uni.getStorageSync('userToken');

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // 如果有token，添加到请求头
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // 构建请求URL
    let url = options.url;
    if (!url.startsWith('http')) {
      url = BASE_URL + url;
    }

    // 添加查询参数
    if (options.params) {
      const queryString = Object.keys(options.params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options.params[key])}`)
        .join('&');

      url += (url.includes('?') ? '&' : '?') + queryString;
    }

    // 发起请求
    if (options.data) {
    }

    uni.request({
      url,
      method: options.method || 'GET',
      data: options.data,
      header: headers,
      timeout: 30000, // 增加超时时间到30秒
      success: (res) => {
        // 检查响应头中是否有新的token
        const newToken = res.header && res.header['X-New-Token'];
        if (newToken) {
          // 更新本地存储的token
          uni.setStorageSync('userToken', newToken);
        }

        // 请求成功
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 未授权，清除token并跳转到登录页
          uni.removeStorageSync('userToken');
          uni.removeStorageSync('userInfo');

          uni.showToast({
            title: 'Login expired, please login again',
            icon: 'none',
            duration: 2000
          });

          setTimeout(() => {
            uni.redirectTo({
              url: '/pages/index/index'
            });
          }, 2000);

          reject(new Error('Login expired'));
        } else {
          // 其他错误

          uni.showToast({
            title: res.data.message || '请求失败',
            icon: 'none',
            duration: 2000
          });

          reject(new Error(res.data.message || '请求失败'));
        }
      },
      fail: (err) => {
        // 请求失败

        // 检查是否是超时错误
        if (err.errMsg && err.errMsg.includes('timeout')) {
          uni.showToast({
            title: '请求超时，请检查网络连接或稍后重试',
            icon: 'none',
            duration: 2000
          });
        } else {
          uni.showToast({
            title: '网络请求失败，请检查网络连接',
            icon: 'none',
            duration: 2000
          });
        }

        reject(err);
      }
    });
  });
}
