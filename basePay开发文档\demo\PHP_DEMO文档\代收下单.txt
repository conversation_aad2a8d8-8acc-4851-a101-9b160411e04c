 {
   <? header("content-Type: text/html; charset=UTF-8");?>
<?php
    include ('phpqrcode.php');
    include ('signapi.php');


    $result = null;

    $merchant_key ="";//支付秘钥

    $version = $_POST["version"];

    $mch_id = $_POST["mch_id"];

    $notify_url = $_POST["notify_url"];

    $page_url = $_POST["page_url"];

    $mch_order_no = $_POST["mch_order_no"];

    $pay_type =$_POST["pay_type"];

    $trade_amount = $_POST["trade_amount"];

    $order_date = $_POST["order_date"];

    $bank_code = $_POST["bank_code"];

    $goods_name = $_POST["goods_name"];

    $sign_type = $_POST["sign_type"];

    $mch_return_msg = $_POST["mch_return_msg"];

    $signStr = "";

    if($bank_code != ""){
        $signStr = $signStr."bank_code=".$bank_code."&";
    }

    $signStr = $signStr."goods_name=".$goods_name."&";
    $signStr = $signStr."mch_id=".$mch_id."&";    
    $signStr = $signStr."mch_order_no=".$mch_order_no."&";
    if($mch_return_msg != ""){
        $signStr = $signStr."mch_return_msg=".$mch_return_msg."&";
    }
    $signStr = $signStr."notify_url=".$notify_url."&";    
    $signStr = $signStr."order_date=".$order_date."&";
    if($page_url != ""){
        $signStr = $signStr."page_url=".$page_url."&";
    }
    $signStr = $signStr."pay_type=".$pay_type."&";
    $signStr = $signStr."trade_amount=".$trade_amount."&";
    $signStr = $signStr."version=".$version;
    $signAPI = new signapi();
    $sign = $signAPI->sign($signStr,$merchant_key);

    $postdata=array(
    'goods_name'=>$goods_name,
    'mch_id'=>$mch_id,
    'mch_order_no'=>$mch_order_no,
    'notify_url'=>$notify_url,
    'order_date'=>$order_date,
    'pay_type'=>$pay_type,
    'trade_amount'=>$trade_amount,
    'version'=>$version,
    /** 下面这些参数有填写才需要提交，不填写的不需要提交也不需要参与签名 */
    /**'bank_code'=>$bank_code,
    'mch_return_msg'=>$mch_return_msg,
    'page_url'=>$page_url,*/
    'sign_type'=>$sign_type,
    'sign'=>$sign);

    $ch = curl_init();    
    curl_setopt($ch,CURLOPT_URL,"https://payment.weglobalpayment.com/pay/web"); //支付请求地址
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postdata));  
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response=curl_exec($ch);

    //$res=simplexml_load_string($response);

    curl_close($ch);

    echo $response;    
?>

    }
  }