/**
 * 日期时间工具
 * 用于处理日期时间格式化和计算
 */

/**
 * 格式化日期时间
 * @param {string|Date} dateTime 日期时间
 * @param {string} format 格式
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(dateTime, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateTime) return '';
  
  try {
    const date = new Date(dateTime);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的日期:', dateTime);
      return '';
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '';
  }
}

/**
 * 格式化日期
 * @param {string|Date} dateTime 日期时间
 * @returns {string} 格式化后的日期
 */
export function formatDate(dateTime) {
  return formatDateTime(dateTime, 'YYYY-MM-DD');
}

/**
 * 格式化时间
 * @param {string|Date} dateTime 日期时间
 * @returns {string} 格式化后的时间
 */
export function formatTime(dateTime) {
  return formatDateTime(dateTime, 'HH:mm:ss');
}

/**
 * 获取当前时间
 * @returns {Date} 当前时间
 */
export function getCurrentTime() {
  return new Date(); // 浏览器本地时间（用于显示）
}

/**
 * 获取当前UTC时间
 * @returns {Date} 当前UTC时间（用于发送到服务器）
 */
export function getCurrentUTCTime() {
  return dayjs.utc().toDate();
}

/**
 * 获取今天的开始时间
 * @returns {Date} 今天的开始时间
 */
export function getTodayStart() {
  return dayjs().startOf('day').toDate(); // 本地时区（用于显示）
}

/**
 * 获取今天开始时间（UTC时区）
 * @returns {Date} 今天开始时间（用于查询）
 */
export function getTodayStartUTC() {
  return dayjs.utc().startOf('day').toDate();
}

/**
 * 获取今天的结束时间
 * @returns {Date} 今天的结束时间
 */
export function getTodayEnd() {
  return dayjs().endOf('day').toDate(); // 本地时区（用于显示）
}

/**
 * 获取今天结束时间（UTC时区）
 * @returns {Date} 今天结束时间（用于查询）
 */
export function getTodayEndUTC() {
  return dayjs.utc().endOf('day').toDate();
}

/**
 * 计算两个日期之间的差值（天数）
 * @param {Date|string} date1 日期1
 * @param {Date|string} date2 日期2
 * @returns {number} 差值（天数）
 */
export function daysBetween(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2 - d1);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * 计算两个日期之间的差值（小时数）
 * @param {Date|string} date1 日期1
 * @param {Date|string} date2 日期2
 * @returns {number} 差值（小时数）
 */
export function hoursBetween(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2 - d1);
  return Math.ceil(diffTime / (1000 * 60 * 60));
}

/**
 * 将日期添加指定天数
 * @param {Date|string} date 日期
 * @param {number} days 天数
 * @returns {Date} 新日期
 */
export function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * 将日期添加指定小时数
 * @param {Date|string} date 日期
 * @param {number} hours 小时数
 * @returns {Date} 新日期
 */
export function addHours(date, hours) {
  const result = new Date(date);
  result.setHours(result.getHours() + hours);
  return result;
}

/**
 * 检查日期是否在指定范围内
 * @param {Date|string} date 日期
 * @param {Date|string} start 开始日期
 * @param {Date|string} end 结束日期
 * @returns {boolean} 是否在范围内
 */
export function isDateInRange(date, start, end) {
  const d = new Date(date);
  const s = new Date(start);
  const e = new Date(end);
  return d >= s && d <= e;
}

/**
 * 格式化为相对时间（例如：3小时前，2天前）
 * @param {Date|string} date 日期
 * @returns {string} 相对时间
 */
export function formatRelativeTime(date) {
  const now = new Date();
  const d = new Date(date);
  const diffMs = now - d;
  
  // 转换为秒
  const diffSec = Math.floor(diffMs / 1000);
  
  if (diffSec < 60) {
    return '刚刚';
  }
  
  // 转换为分钟
  const diffMin = Math.floor(diffSec / 60);
  
  if (diffMin < 60) {
    return `${diffMin}分钟前`;
  }
  
  // 转换为小时
  const diffHour = Math.floor(diffMin / 60);
  
  if (diffHour < 24) {
    return `${diffHour}小时前`;
  }
  
  // 转换为天
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffDay < 30) {
    return `${diffDay}天前`;
  }
  
  // 转换为月
  const diffMonth = Math.floor(diffDay / 30);
  
  if (diffMonth < 12) {
    return `${diffMonth}个月前`;
  }
  
  // 转换为年
  const diffYear = Math.floor(diffMonth / 12);
  return `${diffYear}年前`;
}
