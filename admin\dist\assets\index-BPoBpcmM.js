/* empty css             *//* empty css                   *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                 */import{d as Je,r as c,a as X,q as G,o as de,c as Q,b as s,a7 as R,e as l,w as n,m as Ke,f as Le,i as Ge,a8 as ie,a9 as me,aa as Qe,ab as We,ac as Xe,p,n as m,x as Ze,j as N,ad as el,ae as pe,aB as fe,af as ll,V as U,ag as tl,ah as al,ai as ol,y as E,an as nl,aj as sl,ak as rl,al as ul,ap as dl,aq as il,at as ml,E as pl,h as fl,aC as cl,aA as bl,g as S,a0 as vl,_ as yl}from"./index-LncY9lAB.js";import{s as q}from"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";const Z={getDeposits(M){return q({url:"/api/admin/deposits",method:"get",params:M})},getDeposit(M){return q({url:`/api/admin/deposits/${M}`,method:"get"})},mockCallback(M){return q({url:`/api/admin/deposits/${M.id}/mock-callback`,method:"post"})},updatePaymentPlatformOrderNo(M,_){return q({url:`/api/admin/deposits/${M}/payment-platform-order-no`,method:"put",data:{payment_platform_order_no:_}})},exportDeposits(M){return q({url:"/api/admin/deposits/export",method:"get",params:M,responseType:"blob"})}},gl={class:"deposits-container"},Vl={class:"toolbar"},kl={class:"toolbar-left"},wl={class:"toolbar-right"},Sl={key:0,class:"filter-tags-container"},Ml={class:"filter-tags-header"},_l={class:"filter-tags-content"},Al={class:"table-wrapper"},Tl={class:"operation-buttons-container"},xl={class:"pagination-container"},Nl={class:"dialog-footer"},Cl={class:"dialog-footer"},Ul={class:"dialog-footer"},Dl={class:"dialog-footer"},Il={class:"dialog-footer"},hl={class:"filter-container"},$l={class:"filter-section"},Ol={class:"section-content"},Rl={class:"filter-grid"},El={class:"filter-item"},Pl={class:"filter-item"},Fl={class:"filter-item"},zl={class:"filter-item"},Yl={class:"filter-item"},ql={class:"range-inputs"},Bl={class:"filter-item"},jl={class:"filter-item"},Hl={class:"filter-item"},Jl={class:"filter-item"},Kl={class:"filter-section"},Ll={class:"section-content"},Gl={class:"filter-grid"},Ql={class:"filter-item"},Wl={class:"range-inputs"},Xl={class:"filter-item"},Zl={class:"range-inputs"},et={class:"filter-section"},lt={class:"section-content"},tt={class:"filter-grid"},at={class:"filter-item"},ot={class:"filter-item"},nt={class:"filter-item"},st={class:"filter-item"},rt={class:"filter-item"},ut={class:"filter-section"},dt={class:"section-content"},it={class:"filter-grid"},mt={class:"filter-item"},pt={class:"filter-item"},ft={class:"filter-footer"},ct=Je({__name:"index",setup(M){const _=c(!1),k=c([]),y=c("");c(""),c("");const w=c(1),C=c(10),D=c(0),B=c([]),W=c([]),P=c(!1),j=c(!1),I=c(!1),h=c(!1),H=c(!1),A=X({id:0,orderNumber:"",paymentPlatformOrderNo:""}),T=c(null),i=X({id:0,userId:0,username:"",projectName:"",quantity:1,orderNumber:"",amount:0,actualAmount:0,paymentMethod:"",paymentStatus:"",status:"",orderType:"充值",paymentTime:"",paymentPlatformOrderNo:"",paymentUrl:"",receivingAccount:"",uploadVoucher:"",receiptVoucher:"",commissionStatus:"",callbackStatus:"",createdAt:""}),ce=G(()=>B.value);G(()=>(w.value-1)*C.value+1),G(()=>{const o=w.value*C.value;return o>D.value?D.value:o});const F=c(""),x=c(null),ee=()=>{y.value!==F.value&&(F.value=y.value,console.log("搜索:",y.value),w.value=1,g())},le=()=>{x.value&&(clearTimeout(x.value),x.value=null),ee()},be=()=>{y.value.trim()&&(x.value&&(clearTimeout(x.value),x.value=null),x.value=window.setTimeout(()=>{ee(),x.value=null},200))},ve=()=>{y.value="",F.value="",g()},ye=()=>{y.value.trim()||(F.value="",g())},ge=()=>{p.info("功能尚未实现")},Ve=o=>{k.value=o},te=o=>{C.value=o,w.value=1,g()},ke=o=>{w.value=o,g()},we=()=>{if(k.value.length!==1){p.warning("请选择一条订单进行编辑");return}const o=k.value[0];if(o.status==="已完成"){p.warning("已完成的订单不允许编辑");return}T.value=o,Object.assign(i,T.value),I.value=!0},Se=()=>{if(k.value.length===0){p.warning("请选择要删除的订单");return}if(k.value.some(e=>e.status==="已完成")){p.warning("已完成的订单不允许删除");return}k.value.length>1?vl.confirm(`确定要删除选中的 ${k.value.length} 条订单吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{p.success(`成功删除 ${k.value.length} 条订单`),g()}).catch(()=>{}):(T.value=k.value[0],h.value=!0)},Me=o=>{T.value=o,P.value=!0},_e=async()=>{if(T.value)try{_.value=!0;const o=await Z.mockCallback({id:T.value.id});o.code===200?(p.success("模拟回调成功"),await g()):p.error(o.message||"操作失败")}catch(o){console.error("模拟回调错误:",o),p.error("操作失败，请稍后重试")}finally{_.value=!1,P.value=!1}},Ae=async()=>{if(!A.paymentPlatformOrderNo){p.warning("请输入支付平台订单号");return}try{_.value=!0;const o=await Z.updatePaymentPlatformOrderNo(A.id,A.paymentPlatformOrderNo);o.code===200?(p.success("更新成功"),await g()):p.error(o.message||"操作失败")}catch(o){console.error("更新支付平台订单号错误:",o),p.error("操作失败，请稍后重试")}finally{_.value=!1,H.value=!1}},Te=o=>{p.warning("禁止操作！")},xe=()=>{j.value=!1,p.warning("禁止操作！")},Ne=o=>{T.value=o,Object.assign(i,JSON.parse(JSON.stringify(o))),I.value=!0},Ce=()=>{p.warning("后端暂未实现订单编辑功能，请联系开发人员"),I.value=!1},Ue=o=>{if(o.status==="已完成"){p.warning("已完成的订单不允许删除");return}T.value=o,h.value=!0},De=()=>{p.warning("后端暂未实现订单删除功能，请联系开发人员"),h.value=!1},g=async()=>{_.value=!0;try{const o={page:w.value,limit:C.value};y.value&&(console.log("搜索关键字:",y.value),console.log("搜索关键字长度:",y.value.length),o.keyword=y.value,console.log("作为关键词搜索:",o.keyword));const e=[];if(t.id&&e.push(t.id),t.orderNumber&&e.push(t.orderNumber),t.username&&e.push(t.username),t.userIdStr&&e.push(t.userIdStr),e.length>0&&(o.keyword=e[0]),t.userId&&(o.user_id=t.userId),t.userIdStr&&(o.user_id_str=t.userIdStr),t.status){const u={待支付:"pending",已完成:"completed",已取消:"cancelled"};o.status=u[t.status]||t.status}t.createdAtRange&&t.createdAtRange.length===2&&(o.start_date=t.createdAtRange[0],o.end_date=t.createdAtRange[1]);const f=await Z.getDeposits(o);if(f&&f.code===200){let u=f.data.items.map(r=>{const $=r.user||{username:"",user_id:""};return{id:r.id,userId:r.user_id,userIdStr:$&&$.user_id?$.user_id:"",username:$.username||"",orderNumber:r.order_number,amount:r.amount,actualAmount:r.actual_amount,paymentMethod:r.payment_method,paymentStatus:r.payment_status||(r.status==="completed"?"支付成功":"未支付"),status:Ie(r.status),orderType:"充值",paymentTime:r.payment_time||"",paymentPlatformOrderNo:r.payment_platform_order_no||"",callbackStatus:he(r.callback_status),commissionStatus:r.commission_status||"未返佣",createdAt:r.created_at}});u=Pe(u),B.value=u,D.value=f.data.total||f.data.items.length,W.value=[...u]}else p.error(f.message||"获取数据失败"),B.value=[],W.value=[],D.value=0;F.value=y.value}catch(o){console.error("获取数据失败:",o),p.error("获取数据失败"),B.value=[],W.value=[],D.value=0}finally{_.value=!1}},Ie=o=>{switch(o){case"pending":return"待支付";case"completed":return"已完成";case"cancelled":return"已取消";default:return o}},he=o=>{if(!o)return"未回调";switch(o){case"no_callback":return"未回调";case"mock_callback":return"模拟回调";case"channel_callback":return"通道回调";default:return o}};de(()=>{g()});const ae=o=>typeof o=="string"?parseFloat(o).toFixed(2):o.toFixed(2),$e=o=>o==="通道回调"||o==="channel_callback"?"success":o==="模拟回调"||o==="mock_callback"?"danger":o==="未回调"||o==="no_callback"||!o?"warning":"",Oe=o=>o==="支付成功"||o==="success"?"success":o==="支付失败"||o==="failed"?"danger":o==="未支付"||o==="pending"||!o?"warning":"",Re=o=>o==="已完成"||o==="completed"?"success":o==="已取消"||o==="cancelled"?"danger":o==="待支付"||o==="pending"?"warning":"",z=c(!1),Y=c([]),oe=G(()=>Y.value.length),t=X({id:"",userId:"",userIdStr:"",username:"",projectName:"",quantityMin:"",quantityMax:"",orderNumber:"",platformOrderNumber:"",receivingAccount:"",orderType:"",amountMin:"",amountMax:"",actualAmountMin:"",actualAmountMax:"",paymentMethod:"",paymentStatus:"",status:"",callbackStatus:"",commissionStatus:"",paymentTimeRange:[],createdAtRange:[],orderAmountType:"",orderAmount:null,actualAmountType:"",actualAmount:null,quantity:"",uploadVoucher:"",receiptVoucher:""}),Ee=()=>{z.value=!0},ne=()=>{Object.assign(t,{id:"",userId:"",userIdStr:"",username:"",projectName:"",quantityMin:"",quantityMax:"",orderNumber:"",platformOrderNumber:"",receivingAccount:"",orderType:"",amountMin:"",amountMax:"",actualAmountMin:"",actualAmountMax:"",paymentMethod:"",paymentStatus:"",status:"",callbackStatus:"",commissionStatus:"",paymentTimeRange:[],createdAtRange:[],orderAmountType:"",orderAmount:null,actualAmountType:"",actualAmount:null,quantity:"",uploadVoucher:"",receiptVoucher:""})},Pe=o=>o.filter(e=>{if(t.platformOrderNumber&&!e.paymentPlatformOrderNo.includes(t.platformOrderNumber)||t.orderType&&e.orderType!==t.orderType||t.amountMin&&e.amount<parseFloat(t.amountMin)||t.amountMax&&e.amount>parseFloat(t.amountMax)||t.actualAmountMin&&e.actualAmount<parseFloat(t.actualAmountMin)||t.actualAmountMax&&e.actualAmount>parseFloat(t.actualAmountMax)||t.paymentMethod&&e.paymentMethod!==t.paymentMethod||t.paymentStatus&&e.paymentStatus!==t.paymentStatus||t.callbackStatus&&e.callbackStatus!==t.callbackStatus||t.commissionStatus&&e.commissionStatus!==t.commissionStatus)return!1;if(t.paymentTimeRange&&t.paymentTimeRange.length===2)if(e.paymentTime){const f=new Date(e.paymentTime),u=new Date(t.paymentTimeRange[0]),r=new Date(t.paymentTimeRange[1]);if(f<u||f>r)return!1}else return!1;return!0}),se=()=>{const o=[];if(t.id&&o.push({key:"id",label:`ID: ${t.id}`}),t.userIdStr&&o.push({key:"userIdStr",label:`用户ID: ${t.userIdStr}`}),t.username&&o.push({key:"username",label:`用户名: ${t.username}`}),t.orderNumber&&o.push({key:"orderNumber",label:`订单号: ${t.orderNumber}`}),t.platformOrderNumber&&o.push({key:"platformOrderNumber",label:`支付平台订单号: ${t.platformOrderNumber}`}),t.orderType&&o.push({key:"orderType",label:`订单类型: ${t.orderType}`}),t.amountMin||t.amountMax){const e=t.amountMin||"0",f=t.amountMax||"∞";o.push({key:"amount",label:`订单金额: ${e} - ${f}`})}if(t.actualAmountMin||t.actualAmountMax){const e=t.actualAmountMin||"0",f=t.actualAmountMax||"∞";o.push({key:"actualAmount",label:`实付金额: ${e} - ${f}`})}t.paymentStatus&&o.push({key:"paymentStatus",label:`支付状态: ${t.paymentStatus}`}),t.status&&o.push({key:"status",label:`订单状态: ${t.status}`}),t.paymentMethod&&o.push({key:"paymentMethod",label:`支付通道: ${t.paymentMethod}`}),t.callbackStatus&&o.push({key:"callbackStatus",label:`回调状态: ${t.callbackStatus}`}),t.commissionStatus&&o.push({key:"commissionStatus",label:`返佣状态: ${t.commissionStatus}`}),t.paymentTimeRange&&t.paymentTimeRange.length===2&&o.push({key:"paymentTimeRange",label:`支付时间: ${t.paymentTimeRange[0]} 至 ${t.paymentTimeRange[1]}`}),t.createdAtRange&&t.createdAtRange.length===2&&o.push({key:"createdAtRange",label:`创建时间: ${t.createdAtRange[0]} 至 ${t.createdAtRange[1]}`}),Y.value=o},Fe=o=>{switch(o){case"id":t.id="";break;case"userIdStr":t.userIdStr="";break;case"username":t.username="";break;case"orderNumber":t.orderNumber="";break;case"platformOrderNumber":t.platformOrderNumber="";break;case"orderType":t.orderType="";break;case"amount":t.amountMin="",t.amountMax="";break;case"actualAmount":t.actualAmountMin="",t.actualAmountMax="";break;case"paymentStatus":t.paymentStatus="";break;case"status":t.status="";break;case"paymentMethod":t.paymentMethod="";break;case"callbackStatus":t.callbackStatus="";break;case"commissionStatus":t.commissionStatus="";break;case"paymentTimeRange":t.paymentTimeRange=[];break;case"createdAtRange":t.createdAtRange=[];break}se(),w.value=1,g(),p.success("已移除筛选条件")},ze=()=>{ne(),Y.value=[],w.value=1,g(),p.success("已清除所有筛选条件")},Ye=()=>{se(),z.value=!1,w.value=1,g(),p.success("筛选条件已应用")};return de(()=>{g()}),(o,e)=>{const f=Ze,u=Ke,r=Ge,$=al,J=nl,b=ul,qe=dl,Be=Qe,d=ml,V=il,je=We,O=Xe,v=fl,re=cl,K=bl,ue=pl,He=rl;return S(),Q("div",gl,[s("div",Vl,[s("div",kl,[l(u,{class:"toolbar-button",type:"default",onClick:g},{default:n(()=>[l(f,null,{default:n(()=>[l(N(el))]),_:1}),e[53]||(e[53]=m("刷新 "))]),_:1}),l(u,{class:"toolbar-button",type:"default",disabled:k.value.length!==1,onClick:we},{default:n(()=>[l(f,null,{default:n(()=>[l(N(pe))]),_:1}),e[54]||(e[54]=m("编辑 "))]),_:1},8,["disabled"]),l(u,{class:"toolbar-button",type:"danger",disabled:k.value.length===0,onClick:Se},{default:n(()=>[l(f,null,{default:n(()=>[l(N(fe))]),_:1}),e[55]||(e[55]=m("删除 "))]),_:1},8,["disabled"])]),s("div",wl,[l(r,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>y.value=a),placeholder:"搜索用户名/订单号",class:"search-input",onKeyup:Le(le,["enter"]),onBlur:be,onInput:ye},null,8,["modelValue"]),l(u,{class:"search-button",type:"primary",onClick:le},{default:n(()=>[l(f,null,{default:n(()=>[l(N(ll))]),_:1})]),_:1}),l(u,{class:"reset-button",type:"default",onClick:ve,disabled:!y.value},{default:n(()=>e[56]||(e[56]=[m(" 重置 ")])),_:1},8,["disabled"]),l(u,{class:"toolbar-button filter-button",type:"default",onClick:Ee},{default:n(()=>[l(f,null,{default:n(()=>[l(N(tl))]),_:1}),e[57]||(e[57]=m("筛选 ")),oe.value>0?(S(),U($,{key:0,value:oe.value,class:"filter-badge"},null,8,["value"])):R("",!0)]),_:1}),l(u,{class:"toolbar-button export-button",type:"default",onClick:ge},{default:n(()=>[l(f,null,{default:n(()=>[l(N(ol))]),_:1}),e[58]||(e[58]=m("导出 "))]),_:1})])]),Y.value.length>0?(S(),Q("div",Sl,[s("div",Ml,[e[60]||(e[60]=s("span",{class:"filter-tags-title"},"当前筛选条件：",-1)),l(u,{type:"text",size:"small",onClick:ze,class:"clear-all-btn"},{default:n(()=>e[59]||(e[59]=[m(" 清除所有筛选 ")])),_:1})]),s("div",_l,[(S(!0),Q(ie,null,me(Y.value,a=>(S(),U(J,{key:a.key,closable:"",onClose:L=>Fe(a.key),class:"filter-tag",type:"info"},{default:n(()=>[m(E(a.label),1)]),_:2},1032,["onClose"]))),128))])])):R("",!0),l(Be,{class:"table-card"},{default:n(()=>[s("div",Al,[sl((S(),U(qe,{data:ce.value,style:{width:"100%"},border:"",stripe:"","highlight-current-row":"",onSelectionChange:Ve},{default:n(()=>[l(b,{type:"selection",width:"40",align:"center",fixed:"left"}),l(b,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),l(b,{prop:"userIdStr",label:"用户ID","min-width":"90",align:"center",fixed:"left"}),l(b,{prop:"username",label:"用户名","min-width":"110",align:"center",fixed:"left"}),l(b,{prop:"orderNumber",label:"订单号","min-width":"170",align:"center",fixed:"left"}),l(b,{prop:"amount",label:"订单金额","min-width":"90",align:"center",fixed:"left",sortable:""},{default:n(a=>[m(E(ae(a.row.amount)),1)]),_:1}),l(b,{prop:"actualAmount",label:"实付金额","min-width":"90",align:"center",fixed:"left",sortable:""},{default:n(a=>[m(E(ae(a.row.actualAmount)),1)]),_:1}),l(b,{prop:"paymentMethod",label:"支付通道","min-width":"100",align:"center"}),l(b,{prop:"paymentStatus",label:"支付状态","min-width":"90",align:"center"},{default:n(a=>[l(J,{type:Oe(a.row.paymentStatus),size:"small"},{default:n(()=>[m(E(a.row.paymentStatus),1)]),_:2},1032,["type"])]),_:1}),l(b,{prop:"status",label:"订单状态","min-width":"90",align:"center"},{default:n(a=>[l(J,{type:Re(a.row.status),size:"small"},{default:n(()=>[m(E(a.row.status),1)]),_:2},1032,["type"])]),_:1}),l(b,{prop:"orderType",label:"订单类型","min-width":"90",align:"center"}),l(b,{prop:"paymentTime",label:"支付时间","min-width":"140",align:"center",sortable:""}),l(b,{prop:"paymentPlatformOrderNo",label:"支付平台订单号","min-width":"140",align:"center"}),l(b,{prop:"commissionStatus",label:"返佣状态","min-width":"90",align:"center"}),l(b,{prop:"createdAt",label:"创建时间","min-width":"160",align:"center",sortable:""}),l(b,{prop:"callbackStatus",label:"回调状态","min-width":"90",align:"center",fixed:"right"},{default:n(a=>[l(J,{type:$e(a.row.callbackStatus),size:"small"},{default:n(()=>[m(E(a.row.callbackStatus),1)]),_:2},1032,["type"])]),_:1}),l(b,{label:"操作","min-width":"200",fixed:"right",align:"center"},{default:n(a=>[s("div",Tl,[a.row.status==="待支付"&&a.row.callbackStatus==="未回调"?(S(),U(u,{key:0,type:"warning",size:"small",onClick:L=>Me(a.row),class:"operation-button"},{default:n(()=>e[61]||(e[61]=[m(" 模拟回调 ")])),_:2},1032,["onClick"])):R("",!0),a.row.status==="待支付"&&a.row.callbackStatus==="未回调"?(S(),U(u,{key:1,type:"primary",size:"small",onClick:L=>Te(a.row),class:"operation-button"},{default:n(()=>e[62]||(e[62]=[m(" 手动到账 ")])),_:2},1032,["onClick"])):R("",!0),a.row.status!=="已完成"?(S(),U(u,{key:2,type:"primary",size:"small",onClick:L=>Ne(a.row),class:"operation-button"},{default:n(()=>[l(f,null,{default:n(()=>[l(N(pe))]),_:1})]),_:2},1032,["onClick"])):R("",!0),a.row.status!=="已完成"?(S(),U(u,{key:3,type:"danger",size:"small",onClick:L=>Ue(a.row),class:"operation-button"},{default:n(()=>[l(f,null,{default:n(()=>[l(N(fe))]),_:1})]),_:2},1032,["onClick"])):R("",!0)])]),_:1})]),_:1},8,["data"])),[[He,_.value]])])]),_:1}),s("div",xl,[l(je,{"current-page":w.value,"onUpdate:currentPage":e[1]||(e[1]=a=>w.value=a),"page-size":C.value,"onUpdate:pageSize":e[2]||(e[2]=a=>C.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:D.value,onSizeChange:te,onCurrentChange:ke,"pager-count":7,background:""},{sizes:n(()=>[l(V,{"model-value":C.value,onChange:te,class:"custom-page-size"},{default:n(()=>[(S(),Q(ie,null,me([10,20,50,100],a=>l(d,{key:a,value:a,label:`${a}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),l(O,{modelValue:P.value,"onUpdate:modelValue":e[4]||(e[4]=a=>P.value=a),title:"信息",width:"400px",center:""},{footer:n(()=>[s("span",Nl,[l(u,{type:"primary",onClick:_e},{default:n(()=>e[63]||(e[63]=[m("确定")])),_:1}),l(u,{onClick:e[3]||(e[3]=a=>P.value=!1)},{default:n(()=>e[64]||(e[64]=[m("取消")])),_:1})])]),default:n(()=>[e[65]||(e[65]=s("span",null,"确认模拟回调吗？",-1))]),_:1},8,["modelValue"]),l(O,{modelValue:j.value,"onUpdate:modelValue":e[6]||(e[6]=a=>j.value=a),title:"手动到账确认",width:"400px",center:""},{footer:n(()=>[s("span",Cl,[l(u,{type:"primary",onClick:xe},{default:n(()=>e[66]||(e[66]=[m("确定")])),_:1}),l(u,{onClick:e[5]||(e[5]=a=>j.value=!1)},{default:n(()=>e[67]||(e[67]=[m("取消")])),_:1})])]),default:n(()=>[e[68]||(e[68]=s("span",null,"确认为该订单手动到账吗？",-1))]),_:1},8,["modelValue"]),l(O,{modelValue:I.value,"onUpdate:modelValue":e[23]||(e[23]=a=>I.value=a),title:"编辑订单",width:"800px",center:""},{footer:n(()=>[s("span",Ul,[l(u,{type:"primary",onClick:Ce},{default:n(()=>e[69]||(e[69]=[m("确定")])),_:1}),l(u,{onClick:e[22]||(e[22]=a=>I.value=!1)},{default:n(()=>e[70]||(e[70]=[m("取消")])),_:1})])]),default:n(()=>[l(ue,{model:i,"label-width":"120px"},{default:n(()=>[l(v,{label:"ID"},{default:n(()=>[l(r,{modelValue:i.id,"onUpdate:modelValue":e[7]||(e[7]=a=>i.id=a),disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"用户ID"},{default:n(()=>[l(r,{modelValue:i.userId,"onUpdate:modelValue":e[8]||(e[8]=a=>i.userId=a),disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"用户名"},{default:n(()=>[l(r,{modelValue:i.username,"onUpdate:modelValue":e[9]||(e[9]=a=>i.username=a),disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"订单号"},{default:n(()=>[l(r,{modelValue:i.orderNumber,"onUpdate:modelValue":e[10]||(e[10]=a=>i.orderNumber=a),disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"订单金额"},{default:n(()=>[l(re,{modelValue:i.amount,"onUpdate:modelValue":e[11]||(e[11]=a=>i.amount=a),precision:2,step:.01,min:0,style:{width:"100%"},disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"实付金额"},{default:n(()=>[l(re,{modelValue:i.actualAmount,"onUpdate:modelValue":e[12]||(e[12]=a=>i.actualAmount=a),precision:2,step:.01,min:0,style:{width:"100%"},disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"支付通道"},{default:n(()=>[l(r,{modelValue:i.paymentMethod,"onUpdate:modelValue":e[13]||(e[13]=a=>i.paymentMethod=a),disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"支付状态"},{default:n(()=>[l(V,{modelValue:i.paymentStatus,"onUpdate:modelValue":e[14]||(e[14]=a=>i.paymentStatus=a),style:{width:"100%"}},{default:n(()=>[l(d,{label:"未支付",value:"未支付"}),l(d,{label:"支付成功",value:"支付成功"}),l(d,{label:"支付失败",value:"支付失败"})]),_:1},8,["modelValue"])]),_:1}),l(v,{label:"订单状态"},{default:n(()=>[l(V,{modelValue:i.status,"onUpdate:modelValue":e[15]||(e[15]=a=>i.status=a),style:{width:"100%"}},{default:n(()=>[l(d,{label:"待支付",value:"待支付"}),l(d,{label:"已完成",value:"已完成"}),l(d,{label:"已取消",value:"已取消"})]),_:1},8,["modelValue"])]),_:1}),l(v,{label:"订单类型"},{default:n(()=>[l(V,{modelValue:i.orderType,"onUpdate:modelValue":e[16]||(e[16]=a=>i.orderType=a),style:{width:"100%"},disabled:""},{default:n(()=>[l(d,{label:"充值",value:"充值"})]),_:1},8,["modelValue"])]),_:1}),l(v,{label:"支付时间"},{default:n(()=>[l(K,{modelValue:i.paymentTime,"onUpdate:modelValue":e[17]||(e[17]=a=>i.paymentTime=a),type:"datetime",placeholder:"选择日期时间",style:{width:"100%"},disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"支付平台订单号"},{default:n(()=>[l(r,{modelValue:i.paymentPlatformOrderNo,"onUpdate:modelValue":e[18]||(e[18]=a=>i.paymentPlatformOrderNo=a),disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"返佣状态"},{default:n(()=>[l(V,{modelValue:i.commissionStatus,"onUpdate:modelValue":e[19]||(e[19]=a=>i.commissionStatus=a),style:{width:"100%"}},{default:n(()=>[l(d,{label:"未返佣",value:"未返佣"}),l(d,{label:"已返佣",value:"已返佣"}),l(d,{label:"不返佣",value:"不返佣"})]),_:1},8,["modelValue"])]),_:1}),l(v,{label:"创建时间"},{default:n(()=>[l(K,{modelValue:i.createdAt,"onUpdate:modelValue":e[20]||(e[20]=a=>i.createdAt=a),type:"datetime",placeholder:"选择日期时间",disabled:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(v,{label:"回调状态"},{default:n(()=>[l(V,{modelValue:i.callbackStatus,"onUpdate:modelValue":e[21]||(e[21]=a=>i.callbackStatus=a),style:{width:"100%"},disabled:""},{default:n(()=>[l(d,{label:"未回调",value:"未回调"}),l(d,{label:"模拟回调",value:"模拟回调"}),l(d,{label:"通道回调",value:"通道回调"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(O,{modelValue:h.value,"onUpdate:modelValue":e[25]||(e[25]=a=>h.value=a),title:"删除确认",width:"400px",center:""},{footer:n(()=>[s("span",Dl,[l(u,{type:"danger",onClick:De},{default:n(()=>e[71]||(e[71]=[m("删除")])),_:1}),l(u,{onClick:e[24]||(e[24]=a=>h.value=!1)},{default:n(()=>e[72]||(e[72]=[m("取消")])),_:1})])]),default:n(()=>[e[73]||(e[73]=s("span",null,"确认要删除该订单记录吗？此操作无法撤销。",-1))]),_:1},8,["modelValue"]),l(O,{modelValue:H.value,"onUpdate:modelValue":e[29]||(e[29]=a=>H.value=a),title:"更新支付平台订单号",width:"500px",center:""},{footer:n(()=>[s("span",Il,[l(u,{type:"primary",onClick:Ae},{default:n(()=>e[74]||(e[74]=[m("确定")])),_:1}),l(u,{onClick:e[28]||(e[28]=a=>H.value=!1)},{default:n(()=>e[75]||(e[75]=[m("取消")])),_:1})])]),default:n(()=>[l(ue,{model:A,"label-width":"120px"},{default:n(()=>[l(v,{label:"订单号"},{default:n(()=>[l(r,{modelValue:A.orderNumber,"onUpdate:modelValue":e[26]||(e[26]=a=>A.orderNumber=a),disabled:""},null,8,["modelValue"])]),_:1}),l(v,{label:"支付平台订单号",required:""},{default:n(()=>[l(r,{modelValue:A.paymentPlatformOrderNo,"onUpdate:modelValue":e[27]||(e[27]=a=>A.paymentPlatformOrderNo=a),placeholder:"请输入支付平台订单号"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(O,{modelValue:z.value,"onUpdate:modelValue":e[52]||(e[52]=a=>z.value=a),title:"筛选条件",width:"900px","close-on-click-modal":!0,class:"filter-dialog"},{footer:n(()=>[s("div",ft,[l(u,{class:"filter-button",type:"primary",onClick:Ye},{default:n(()=>e[101]||(e[101]=[m(" 搜索 ")])),_:1}),l(u,{class:"filter-button",onClick:ne},{default:n(()=>e[102]||(e[102]=[m(" 重置 ")])),_:1}),l(u,{class:"filter-button",onClick:e[51]||(e[51]=a=>z.value=!1)},{default:n(()=>e[103]||(e[103]=[m(" 取消 ")])),_:1})])]),default:n(()=>[s("div",hl,[s("div",$l,[e[86]||(e[86]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"基本信息")],-1)),s("div",Ol,[s("div",Rl,[s("div",El,[e[76]||(e[76]=s("div",{class:"filter-label"},"ID",-1)),l(r,{modelValue:t.id,"onUpdate:modelValue":e[30]||(e[30]=a=>t.id=a),placeholder:"请输入ID",clearable:""},null,8,["modelValue"])]),s("div",Pl,[e[77]||(e[77]=s("div",{class:"filter-label"},"用户ID",-1)),l(r,{modelValue:t.userIdStr,"onUpdate:modelValue":e[31]||(e[31]=a=>t.userIdStr=a),placeholder:"请输入用户ID (如U000001)",clearable:""},null,8,["modelValue"])]),s("div",Fl,[e[78]||(e[78]=s("div",{class:"filter-label"},"用户名",-1)),l(r,{modelValue:t.username,"onUpdate:modelValue":e[32]||(e[32]=a=>t.username=a),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),s("div",zl,[e[79]||(e[79]=s("div",{class:"filter-label"},"项目名称",-1)),l(r,{modelValue:t.projectName,"onUpdate:modelValue":e[33]||(e[33]=a=>t.projectName=a),placeholder:"请输入项目名称",clearable:""},null,8,["modelValue"])]),s("div",Yl,[e[81]||(e[81]=s("div",{class:"filter-label"},"数量",-1)),s("div",ql,[l(r,{modelValue:t.quantityMin,"onUpdate:modelValue":e[34]||(e[34]=a=>t.quantityMin=a),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[80]||(e[80]=s("span",null,"-",-1)),l(r,{modelValue:t.quantityMax,"onUpdate:modelValue":e[35]||(e[35]=a=>t.quantityMax=a),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),s("div",Bl,[e[82]||(e[82]=s("div",{class:"filter-label"},"订单号",-1)),l(r,{modelValue:t.orderNumber,"onUpdate:modelValue":e[36]||(e[36]=a=>t.orderNumber=a),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),s("div",jl,[e[83]||(e[83]=s("div",{class:"filter-label"},"支付平台订单号",-1)),l(r,{modelValue:t.platformOrderNumber,"onUpdate:modelValue":e[37]||(e[37]=a=>t.platformOrderNumber=a),placeholder:"请输入支付平台订单号",clearable:""},null,8,["modelValue"])]),s("div",Hl,[e[84]||(e[84]=s("div",{class:"filter-label"},"收款账号",-1)),l(r,{modelValue:t.receivingAccount,"onUpdate:modelValue":e[38]||(e[38]=a=>t.receivingAccount=a),placeholder:"请输入收款账号",clearable:""},null,8,["modelValue"])]),s("div",Jl,[e[85]||(e[85]=s("div",{class:"filter-label"},"订单类型",-1)),l(V,{modelValue:t.orderType,"onUpdate:modelValue":e[39]||(e[39]=a=>t.orderType=a),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:n(()=>[l(d,{label:"全部",value:""}),l(d,{label:"充值",value:"充值"})]),_:1},8,["modelValue"])])])])]),s("div",Kl,[e[91]||(e[91]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"金额信息")],-1)),s("div",Ll,[s("div",Gl,[s("div",Ql,[e[88]||(e[88]=s("div",{class:"filter-label"},"订单金额",-1)),s("div",Wl,[l(r,{modelValue:t.amountMin,"onUpdate:modelValue":e[40]||(e[40]=a=>t.amountMin=a),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[87]||(e[87]=s("span",null,"-",-1)),l(r,{modelValue:t.amountMax,"onUpdate:modelValue":e[41]||(e[41]=a=>t.amountMax=a),placeholder:"最大值",clearable:""},null,8,["modelValue"])])]),s("div",Xl,[e[90]||(e[90]=s("div",{class:"filter-label"},"实付金额",-1)),s("div",Zl,[l(r,{modelValue:t.actualAmountMin,"onUpdate:modelValue":e[42]||(e[42]=a=>t.actualAmountMin=a),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[89]||(e[89]=s("span",null,"-",-1)),l(r,{modelValue:t.actualAmountMax,"onUpdate:modelValue":e[43]||(e[43]=a=>t.actualAmountMax=a),placeholder:"最大值",clearable:""},null,8,["modelValue"])])])])])]),s("div",et,[e[97]||(e[97]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"状态信息")],-1)),s("div",lt,[s("div",tt,[s("div",at,[e[92]||(e[92]=s("div",{class:"filter-label"},"支付状态",-1)),l(V,{modelValue:t.paymentStatus,"onUpdate:modelValue":e[44]||(e[44]=a=>t.paymentStatus=a),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:n(()=>[l(d,{label:"全部",value:""}),l(d,{label:"未支付",value:"未支付"}),l(d,{label:"支付成功",value:"支付成功"}),l(d,{label:"支付失败",value:"支付失败"})]),_:1},8,["modelValue"])]),s("div",ot,[e[93]||(e[93]=s("div",{class:"filter-label"},"订单状态",-1)),l(V,{modelValue:t.status,"onUpdate:modelValue":e[45]||(e[45]=a=>t.status=a),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:n(()=>[l(d,{label:"全部",value:""}),l(d,{label:"待支付",value:"待支付"}),l(d,{label:"已完成",value:"已完成"}),l(d,{label:"已取消",value:"已取消"})]),_:1},8,["modelValue"])]),s("div",nt,[e[94]||(e[94]=s("div",{class:"filter-label"},"支付通道",-1)),l(V,{modelValue:t.paymentMethod,"onUpdate:modelValue":e[46]||(e[46]=a=>t.paymentMethod=a),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:n(()=>[l(d,{label:"全部",value:""}),l(d,{label:"kbpay",value:"kbpay"})]),_:1},8,["modelValue"])]),s("div",st,[e[95]||(e[95]=s("div",{class:"filter-label"},"回调状态",-1)),l(V,{modelValue:t.callbackStatus,"onUpdate:modelValue":e[47]||(e[47]=a=>t.callbackStatus=a),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:n(()=>[l(d,{label:"全部",value:""}),l(d,{label:"未回调",value:"未回调"}),l(d,{label:"模拟回调",value:"模拟回调"}),l(d,{label:"通道回调",value:"通道回调"})]),_:1},8,["modelValue"])]),s("div",rt,[e[96]||(e[96]=s("div",{class:"filter-label"},"返佣状态",-1)),l(V,{modelValue:t.commissionStatus,"onUpdate:modelValue":e[48]||(e[48]=a=>t.commissionStatus=a),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:n(()=>[l(d,{label:"全部",value:""}),l(d,{label:"已返佣",value:"已返佣"}),l(d,{label:"未返佣",value:"未返佣"}),l(d,{label:"不返佣",value:"不返佣"})]),_:1},8,["modelValue"])])])])]),s("div",ut,[e[100]||(e[100]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"时间信息")],-1)),s("div",dt,[s("div",it,[s("div",mt,[e[98]||(e[98]=s("div",{class:"filter-label"},"支付时间",-1)),l(K,{modelValue:t.paymentTimeRange,"onUpdate:modelValue":e[49]||(e[49]=a=>t.paymentTimeRange=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),s("div",pt,[e[99]||(e[99]=s("div",{class:"filter-label"},"创建时间",-1)),l(K,{modelValue:t.createdAtRange,"onUpdate:modelValue":e[50]||(e[50]=a=>t.createdAtRange=a),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])])])])])]),_:1},8,["modelValue"])])}}}),$t=yl(ct,[["__scopeId","data-v-67023c88"]]);export{$t as default};
