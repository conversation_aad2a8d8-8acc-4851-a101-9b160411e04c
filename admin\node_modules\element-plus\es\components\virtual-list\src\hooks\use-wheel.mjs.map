{"version": 3, "file": "use-wheel.mjs", "sources": ["../../../../../../../packages/components/virtual-list/src/hooks/use-wheel.ts"], "sourcesContent": ["import { cAF, isFirefox, rAF } from '@element-plus/utils'\nimport { HORIZONTAL, VERTICAL } from '../defaults'\n\nimport type { ComputedRef } from 'vue'\nimport type { LayoutDirection } from '../types'\n\nconst LayoutKeys = {\n  [HORIZONTAL]: 'deltaX',\n  [VERTICAL]: 'deltaY',\n} as const\n\ninterface ListWheelState {\n  atStartEdge: ComputedRef<boolean> // exclusive to reachEnd\n  atEndEdge: ComputedRef<boolean>\n  layout: ComputedRef<LayoutDirection>\n}\n\ntype ListWheelHandler = (offset: number) => void\n\nconst useWheel = (\n  { atEndEdge, atStartEdge, layout }: ListWheelState,\n  onWheelDelta: ListWheelHandler\n) => {\n  let frameHandle: number\n  let offset = 0\n\n  // let scrollLock = false\n  // let lockHandle = null\n\n  // const lockScroll = () => {\n  //   clearTimeout(lockHandle)\n  //   scrollLock = true\n  //   lockHandle = setTimeout(() => scrollLock = false, 50)\n  // }\n\n  const hasReachedEdge = (offset: number) => {\n    const edgeReached =\n      (offset < 0 && atStartEdge.value) || (offset > 0 && atEndEdge.value)\n\n    return edgeReached\n  }\n\n  const onWheel = (e: WheelEvent) => {\n    cAF(frameHandle)\n\n    const newOffset = e[LayoutKeys[layout.value]]\n\n    if (hasReachedEdge(offset) && hasReachedEdge(offset + newOffset)) return\n\n    offset += newOffset\n\n    if (!isFirefox()) {\n      e.preventDefault()\n    }\n\n    frameHandle = rAF(() => {\n      onWheelDelta(offset)\n      offset = 0\n    })\n  }\n\n  return {\n    hasReachedEdge,\n    onWheel,\n  }\n}\n\nexport default useWheel\n"], "names": [], "mappings": ";;;;AAEA,MAAM,UAAU,GAAG;AACnB,EAAE,CAAC,UAAU,GAAG,QAAQ;AACxB,EAAE,CAAC,QAAQ,GAAG,QAAQ;AACtB,CAAC,CAAC;AACG,MAAC,QAAQ,GAAG,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,YAAY,KAAK;AACvE,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,MAAM,cAAc,GAAG,CAAC,OAAO,KAAK;AACtC,IAAI,MAAM,WAAW,GAAG,OAAO,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC;AAC3F,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK;AACzB,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;AACrB,IAAI,MAAM,SAAS,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAClD,IAAI,IAAI,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,IAAI,MAAM,IAAI,SAAS,CAAC;AACxB,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AACtB,MAAM,CAAC,CAAC,cAAc,EAAE,CAAC;AACzB,KAAK;AACL,IAAI,WAAW,GAAG,GAAG,CAAC,MAAM;AAC5B,MAAM,YAAY,CAAC,MAAM,CAAC,CAAC;AAC3B,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;"}