/**
 * 数据迁移脚本
 * 为已有收益记录填充理论收益时间
 */
const mysql = require('mysql2/promise');
require('dotenv').config();

// 简单的日志函数
const logger = {
  info: console.log,
  error: console.error,
  warn: console.warn
};

/**
 * 计算理论收益时间
 * @param {Object} investment - 投资记录
 * @param {Object} project - 项目信息
 * @param {Date} profitTime - 实际收益时间
 * @returns {Date} - 理论收益时间
 */
function calculateTheoreticalProfitTime(investment, project, profitTime) {
  // 计算投资开始时间
  const startTime = new Date(investment.start_time || investment.created_at);

  // 计算收益周期（毫秒）
  const profitCycleMs = project.profit_time * 60 * 60 * 1000;

  // 计算从开始时间到收益时间经过了多少个完整周期
  const elapsedMs = profitTime.getTime() - startTime.getTime();
  const cycles = Math.floor(elapsedMs / profitCycleMs);

  // 计算理论收益时间
  return new Date(startTime.getTime() + cycles * profitCycleMs);
}

/**
 * 为已有收益记录填充理论收益时间
 */
async function migrateExistingProfits() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'MySQL3352~!',
    database: process.env.DB_NAME || 'fox_db'
  });

  try {
    console.log('开始为已有收益记录填充理论收益时间...');

    // 获取所有投资记录
    console.log('正在查询投资记录...');
    const [investments] = await connection.query(`
      SELECT i.*, p.profit_time as project_profit_time
      FROM investments i
      JOIN projects p ON i.project_id = p.id
    `);

    console.log(`找到 ${investments.length} 条投资记录`);

    // 如果没有投资记录，尝试直接查询收益记录
    if (investments.length === 0) {
      console.log('没有找到投资记录，尝试直接查询收益记录...');
      const [profits] = await connection.query('SELECT * FROM investment_profits');
      console.log(`直接查询到 ${profits.length} 条收益记录`);
    }

    let successCount = 0;
    let failedCount = 0;

    for (const investment of investments) {
      // 获取该投资的所有收益记录，按时间排序
      const [profits] = await connection.query(
        'SELECT * FROM investment_profits WHERE investment_id = ? ORDER BY profit_time ASC',
        [investment.id]
      );

      if (profits.length === 0) continue;

      console.log(`处理投资ID ${investment.id} 的 ${profits.length} 条收益记录`);

      for (const profit of profits) {
        try {
          // 计算理论收益时间
          const theoreticalTime = calculateTheoreticalProfitTime(
            investment,
            { profit_time: investment.project_profit_time },
            new Date(profit.profit_time)
          );

          // 更新收益记录
          await connection.query(
            'UPDATE investment_profits SET theoretical_profit_time = ? WHERE id = ?',
            [theoreticalTime, profit.id]
          );

          console.log(`更新收益ID ${profit.id} 的理论时间为 ${theoreticalTime.toISOString()} 成功`);
          successCount++;
        } catch (error) {
          console.error(`更新收益ID ${profit.id} 的理论时间失败:`, error);
          failedCount++;
        }
      }
    }

    console.log(`迁移完成: 成功 ${successCount}, 失败 ${failedCount}`);
    return { success: successCount, failed: failedCount };
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

// 执行迁移
console.log('开始执行迁移脚本...');
migrateExistingProfits()
  .then(result => {
    console.log('迁移结果:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('迁移过程出错:', error);
    process.exit(1);
  });
