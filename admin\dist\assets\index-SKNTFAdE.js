/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                    */import{p as c,d as Ye,r as v,o as Re,c as F,b as r,e,m as Ae,w as a,i as Le,f as Be,aa as Pe,ab as qe,ac as Fe,a0 as Ve,n as I,x as We,j as h,ad as Qe,aw as He,ae as _e,aB as he,af as Ke,aj as je,ak as Oe,V as Ce,al as Ge,aL as vt,M as ge,y as Z,ao as Je,ap as Xe,aq as Ze,a8 as et,a9 as tt,at as lt,E as at,h as ot,aP as $e,aI as ft,a7 as Se,aU as gt,au as st,az as ke,g as U,_ as nt,ar as _t,as as ht,ag as Ee,ai as yt,an as bt,v as wt,aG as Dt,aQ as It,aC as Vt,aA as Ct}from"./index-LncY9lAB.js";/* empty css                        */import{A as $t}from"./AttachmentSelector-YIf-YS41.js";import{s as W}from"./request-Cd-6Wde0.js";/* empty css                    *//* empty css                 */import"./attachments-CNgOoo0P.js";import"./index-t--hEgTQ.js";const St=async(m,w="复制成功",$="复制失败，请手动复制")=>{await kt(m)?c.success(w):c.error($)},kt=async m=>{try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(m),!0;{const w=document.createElement("textarea");w.value=m,w.style.position="fixed",w.style.left="-999999px",w.style.top="-999999px",document.body.appendChild(w),w.focus(),w.select();const $=document.execCommand("copy");return document.body.removeChild(w),$}}catch(w){return console.error("复制到剪贴板失败:",w),!1}},J=(m,w="YYYY-MM-DD HH:mm:ss")=>{if(!m)return"";try{const $=typeof m=="string"?new Date(m):m;if(isNaN($.getTime()))return"";const H=$.getFullYear(),R=String($.getMonth()+1).padStart(2,"0"),M=String($.getDate()).padStart(2,"0"),z=String($.getHours()).padStart(2,"0"),Q=String($.getMinutes()).padStart(2,"0"),Y=String($.getSeconds()).padStart(2,"0");return w.replace("YYYY",String(H)).replace("MM",R).replace("DD",M).replace("HH",z).replace("mm",Q).replace("ss",Y)}catch($){return console.error("日期格式化错误:",$),""}};function Tt(m){return W({url:"/api/admin/customer-service-images",method:"get",params:m})}function xt(m){return W({url:"/api/admin/customer-service-images",method:"post",data:m})}function Me(m,w){return W({url:`/api/admin/customer-service-images/${m}`,method:"put",data:w})}function Ne(m){return console.log("删除客服图片 API - ID:",m,"类型:",typeof m),typeof m!="number"||isNaN(m)?(console.error("删除客服图片 API - 无效的ID:",m),Promise.reject(new Error("无效的ID"))):W({url:`/api/admin/customer-service-images/${m}`,method:"delete"})}const Et={class:"customer-service-images-container"},Mt={class:"toolbar"},Nt={class:"toolbar-left"},Ut={class:"toolbar-right"},zt={class:"table-wrapper"},Yt={class:"image-preview-cell"},Rt={class:"image-error"},At={key:1,class:"no-image"},Lt={class:"operation-buttons-container"},Bt={class:"pagination-container"},Pt={class:"image-upload-container"},qt={key:0,class:"image-container"},Ft={class:"image-error"},Wt={class:"image-overlay"},Qt={key:1,class:"empty-image-container"},Ht={class:"image-actions"},Kt={key:0,class:"form-tip"},jt={class:"dialog-footer"},Ot=Ye({__name:"CustomerServiceImages",setup(m){const w=v(null),$=v(null),H=v(!1),R=v(!1),M=v(!1),z=v(!1),Q=v(""),Y=v([]),O=v(!1),K=v([]),S=v(1),T=v(10),B=v(0),y=v({file_name:"",file_path:"",status:1}),ie={attachment_id:[{required:!0,message:"请选择图片",trigger:"change"}],file_name:[{required:!0,message:"请输入文件名",trigger:"blur"}]},u=o=>o?o.startsWith("http")?o:`${o}`:"",f=o=>{if(!o)return"0 B";const s=["B","KB","MB","GB","TB"];let g=0;for(;o>=1024&&g<s.length-1;)o/=1024,g++;return`${o.toFixed(2)} ${s[g]}`},ye=o=>{Y.value=o},re=o=>{T.value=o,S.value=1,P()},be=o=>{S.value=o,P()},ue=()=>{S.value=1,P()},P=async()=>{H.value=!0;try{const o=await Tt({page:S.value,limit:T.value,sort:"id",order:"desc"});if(o.code===200&&o.data){console.log("fetchData - 原始响应数据:",o.data);const s=o.data.items.map(g=>(g.id!==void 0&&g.id!==null&&(g.id=Number(g.id)),g));console.log("fetchData - 处理后的数据:",s),K.value=s,B.value=o.data.total}else c.error(o.message||"获取数据失败")}catch(o){console.error("获取客服图片列表失败:",o),c.error("获取数据失败，请重试")}finally{H.value=!1}},de=()=>{M.value=!1,y.value={file_name:"",file_path:"",status:1},R.value=!0},ce=o=>{if(!o){if(Y.value.length!==1){c.warning("请选择一条记录");return}o=Y.value[0]}M.value=!0,y.value={id:o.id,attachment_id:o.attachment_id,file_name:o.file_name,file_path:o.file_path,file_size:o.file_size,status:o.status},R.value=!0},ee=o=>{if(console.log("handleDelete - 传入的row:",o),o&&(console.log("handleDelete - row.id:",o.id,"类型:",typeof o.id),o.id===void 0||o.id===null||isNaN(Number(o.id)))){console.error("handleDelete - 传入的row对象ID无效:",o),c.error("无效的记录ID");return}const s=o?[o]:Y.value;if(console.log("handleDelete - 处理的items:",s),s.length===0){c.warning("请至少选择一条记录");return}if(s.some(p=>p==null||p.id===void 0||p.id===null||isNaN(Number(p.id)))){console.error("handleDelete - items中存在无效的ID:",s),c.error("选中的记录中包含无效的ID");return}Ve.confirm(`确定要删除选中的${s.length}条记录吗？此操作不可撤销。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{if(s.length===1){const p=s[0].id;if(console.log("单条删除 - ID:",p,"类型:",typeof p),p==null||isNaN(Number(p))){console.error("单条删除 - 无效的ID:",p),c.error("无效的ID");return}const A=Number(p);console.log("单条删除 - 转换后的ID:",A,"类型:",typeof A);const D=await Ne(A);console.log("单条删除 - 响应:",D),D.code===200?(c.success("删除成功"),P()):c.error(D.message||"删除失败")}else{console.log("批量删除 - 原始选中的项目:",s),console.log("批量删除 - 原始选中的项目类型:",typeof s,Array.isArray(s)),s.forEach((_,k)=>{console.log(`批量删除 - 项目[${k}]:`,_),console.log(`批量删除 - 项目[${k}].id:`,_.id,typeof _.id)}),console.log("批量删除 - 检查每个项目的ID:");const p=[],A=[];for(const _ of s)_&&_.id!==void 0&&_.id!==null&&!isNaN(Number(_.id))?(p.push(_),console.log(`批量删除 - 有效项目ID: ${_.id}, 类型: ${typeof _.id}`)):(A.push(_),console.log(`批量删除 - 无效项目ID: ${_==null?void 0:_.id}, 类型: ${typeof(_==null?void 0:_.id)}`));console.log(`批量删除 - 有效项目数量: ${p.length}, 无效项目数量: ${A.length}`);const D=p.map(_=>Number(_.id));if(console.log("批量删除 - 过滤后的IDs:",D),console.log("批量删除 - 过滤后的IDs类型:",typeof D,Array.isArray(D)),console.log("批量删除 - 过滤后的IDs JSON:",JSON.stringify({ids:D})),D.length===0){c.error("没有有效的ID可删除");return}try{console.log("=== 批量删除 - 开始 ==="),console.log("批量删除 - 使用单个删除API逐个删除"),console.log("批量删除 - 检查每个ID的类型:"),D.forEach((N,V)=>{console.log(`批量删除 - ID[${V}]:`,N,typeof N,isNaN(N)?"无效":"有效")});let _=0,k=0;for(const N of D)try{console.log(`批量删除 - 正在删除ID: ${N}, 类型: ${typeof N}`);const V=Number(N);if(console.log(`批量删除 - 转换后的ID: ${V}, 类型: ${typeof V}`),isNaN(V)){console.error(`批量删除 - ID ${N} 无效，无法转换为数字`),k++;continue}const j=await Ne(V);console.log(`批量删除 - 删除ID ${V} 响应:`,j),j.code===200?_++:(k++,console.error(`批量删除 - 删除ID ${V} 失败:`,j.message))}catch(V){k++,console.error(`批量删除 - 删除ID ${N} 出错:`,V)}console.log(`批量删除 - 成功: ${_}, 失败: ${k}`),console.log("=== 批量删除 - 结束 ==="),_>0?(c.success(`成功删除${_}条记录${k>0?`，${k}条记录删除失败`:""}`),P()):c.error("批量删除失败")}catch(_){console.error("批量删除请求错误:",_),c.error("批量删除失败，请检查网络连接")}}}catch(p){console.error("删除客服图片失败:",p),c.error("删除失败，请重试")}}).catch(()=>{})},E=async o=>{try{o.statusLoading=!0;const s=await Me(o.id,{status:o.status});s.code===200?c.success(`状态已${o.status===1?"启用":"禁用"}`):(o.status=o.status===1?0:1,c.error(s.message||"更新状态失败"))}catch(s){o.status=o.status===1?0:1,console.error("更新客服图片状态失败:",s),c.error("更新状态失败，请重试")}finally{o.statusLoading=!1}},we=()=>{var o;(o=$.value)==null||o.validate(async s=>{if(s){z.value=!0;try{const g={attachment_id:y.value.attachment_id,file_name:y.value.file_name,file_path:y.value.file_path,file_size:y.value.file_size,status:y.value.status};let p;if(M.value){if(!y.value.id||isNaN(Number(y.value.id))){c.error("无效的ID");return}p=await Me(Number(y.value.id),g)}else p=await xt(g);p.code===200||p.code===201?(c.success(M.value?"编辑成功":"添加成功"),R.value=!1,P()):c.error(p.message||(M.value?"编辑失败":"添加失败"))}catch(g){console.error(M.value?"编辑客服图片失败:":"添加客服图片失败:",g),c.error(M.value?"编辑失败，请重试":"添加失败，请重试")}finally{z.value=!1}}})},te=()=>{O.value=!0},De=o=>{var s;o&&(y.value.attachment_id=o.id,y.value.file_path=o.file_path,y.value.file_name=o.file_name||o.filename||"未命名",y.value.file_size=o.file_size,y.value.file_type=o.file_type||((s=o.mime_type)==null?void 0:s.split("/")[1])||"unknown")},Ie=o=>{o.stopPropagation()};return Re(()=>{P()}),(o,s)=>{const g=We,p=Ae,A=Le,D=Ge,_=vt,k=Je,N=Xe,V=Pe,j=lt,q=Ze,le=qe,ae=ot,pe=at,me=Fe,oe=Oe;return U(),F("div",Et,[r("div",Mt,[r("div",Nt,[e(p,{class:"toolbar-button",type:"default",onClick:P},{default:a(()=>[e(g,null,{default:a(()=>[e(h(Qe))]),_:1}),s[10]||(s[10]=I("刷新 "))]),_:1}),e(p,{class:"toolbar-button",type:"success",onClick:de},{default:a(()=>[e(g,null,{default:a(()=>[e(h(He))]),_:1}),s[11]||(s[11]=I("添加 "))]),_:1}),e(p,{class:"toolbar-button",type:"primary",onClick:s[0]||(s[0]=()=>ce()),disabled:Y.value.length!==1},{default:a(()=>[e(g,null,{default:a(()=>[e(h(_e))]),_:1}),s[12]||(s[12]=I("编辑 "))]),_:1},8,["disabled"]),e(p,{class:"toolbar-button",type:"danger",onClick:s[1]||(s[1]=()=>ee()),disabled:Y.value.length===0},{default:a(()=>[e(g,null,{default:a(()=>[e(h(he))]),_:1}),s[13]||(s[13]=I("删除 "))]),_:1},8,["disabled"])]),r("div",Ut,[e(A,{modelValue:Q.value,"onUpdate:modelValue":s[2]||(s[2]=l=>Q.value=l),placeholder:"搜索图片名称",class:"search-input",clearable:"",onKeyup:Be(ue,["enter"])},null,8,["modelValue"]),e(p,{class:"search-button",type:"primary",onClick:ue},{default:a(()=>[e(g,null,{default:a(()=>[e(h(Ke))]),_:1})]),_:1})])]),e(V,{class:"table-card"},{default:a(()=>[r("div",zt,[je((U(),Ce(N,{ref_key:"imagesTable",ref:w,data:K.value,border:"",stripe:"",style:{width:"100%"},"highlight-current-row":"",onSelectionChange:ye,"cell-style":{whiteSpace:"nowrap",overflow:"visible"},"header-cell-style":{whiteSpace:"nowrap",overflow:"visible"},"show-overflow-tooltip":!1,"table-layout":"auto"},{default:a(()=>[e(D,{type:"selection",width:"55"}),e(D,{prop:"id",label:"ID",width:"60",sortable:""}),e(D,{label:"图片",width:"120",align:"center"},{default:a(l=>[r("div",Yt,[l.row.file_path?(U(),Ce(_,{key:0,src:u(l.row.file_path),"preview-src-list":[u(l.row.file_path)],fit:"cover",class:"image-preview",style:{width:"60px",height:"60px","border-radius":"4px"}},{error:a(()=>[r("div",Rt,[e(g,null,{default:a(()=>[e(h(ge))]),_:1})])]),_:2},1032,["src","preview-src-list"])):(U(),F("div",At,[e(g,null,{default:a(()=>[e(h(ge))]),_:1})]))])]),_:1}),e(D,{prop:"file_name",label:"文件名","min-width":"150","show-overflow-tooltip":""}),e(D,{prop:"file_path",label:"物理路径","min-width":"180","show-overflow-tooltip":""}),e(D,{prop:"file_size",label:"文件大小",width:"120",align:"center"},{default:a(l=>[I(Z(f(l.row.file_size)),1)]),_:1}),e(D,{prop:"status",label:"状态",width:"80"},{default:a(({row:l})=>[e(k,{modelValue:l.status,"onUpdate:modelValue":t=>l.status=t,"active-value":1,"inactive-value":0,onChange:t=>E(l),loading:l.statusLoading},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])]),_:1}),e(D,{prop:"created_at",label:"创建时间",width:"160"},{default:a(({row:l})=>[I(Z(h(J)(l.created_at)),1)]),_:1}),e(D,{fixed:"right",label:"操作",width:"150",align:"center"},{default:a(({row:l,$index:t})=>[r("div",Lt,[e(p,{class:"operation-button icon-only",size:"small",type:"default",onClick:()=>ce(l)},{default:a(()=>[e(g,null,{default:a(()=>[e(h(_e))]),_:1})]),_:2},1032,["onClick"]),e(p,{type:"danger",size:"small",onClick:()=>ee(l),class:"operation-button icon-only"},{default:a(()=>[e(g,null,{default:a(()=>[e(h(he))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[oe,H.value]])])]),_:1}),r("div",Bt,[e(le,{"current-page":S.value,"onUpdate:currentPage":s[3]||(s[3]=l=>S.value=l),"page-size":T.value,"onUpdate:pageSize":s[4]||(s[4]=l=>T.value=l),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:B.value,onSizeChange:re,onCurrentChange:be,"pager-count":7,background:""},{sizes:a(()=>[e(q,{"model-value":T.value,onChange:re,class:"custom-page-size"},{default:a(()=>[(U(),F(et,null,tt([10,20,50,100],l=>e(j,{key:l,value:l,label:`${l}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),e(me,{modelValue:R.value,"onUpdate:modelValue":s[8]||(s[8]=l=>R.value=l),title:M.value?"编辑客服图片":"添加客服图片",width:"550px","close-on-click-modal":!1,center:""},{footer:a(()=>[r("div",jt,[e(p,{type:"primary",loading:z.value,onClick:we},{default:a(()=>[e(g,null,{default:a(()=>[e(h(st))]),_:1}),s[16]||(s[16]=I("确定 "))]),_:1},8,["loading"]),e(p,{onClick:s[7]||(s[7]=l=>R.value=!1)},{default:a(()=>[e(g,null,{default:a(()=>[e(h(ke))]),_:1}),s[17]||(s[17]=I("取消 "))]),_:1})])]),default:a(()=>[e(pe,{ref_key:"formRef",ref:$,model:y.value,"label-width":"100px","label-position":"left",rules:ie},{default:a(()=>[e(ae,{label:"图片",prop:"attachment_id",required:""},{default:a(()=>[r("div",Pt,[r("div",{class:"image-preview-wrapper",onClick:te},[y.value.file_path?(U(),F("div",qt,[e(_,{src:u(y.value.file_path),class:"image-preview","preview-src-list":[u(y.value.file_path)],fit:"cover",style:{width:"100px",height:"100px","border-radius":"4px"}},{error:a(()=>[r("div",Ft,[e(g,null,{default:a(()=>[e(h(ge))]),_:1})])]),_:1},8,["src","preview-src-list"]),r("div",Wt,[e(p,{type:"primary",circle:"",size:"small",class:"preview-button",onClick:$e(Ie,["stop"])},{default:a(()=>[e(g,null,{default:a(()=>[e(h(ft))]),_:1})]),_:1})])])):(U(),F("div",Qt,[e(g,{class:"image-uploader-icon"},{default:a(()=>[e(h(ge))]),_:1}),s[14]||(s[14]=r("span",{class:"upload-text"},"点击选择图片",-1))]))]),r("div",Ht,[e(p,{type:"primary",size:"small",onClick:te},{default:a(()=>[e(g,null,{default:a(()=>[e(h(gt))]),_:1}),s[15]||(s[15]=I("从附件选择图片 "))]),_:1}),y.value.file_path?(U(),F("div",Kt,"鼠标移至图片上可预览")):Se("",!0)])])]),_:1}),e(ae,{label:"文件名",prop:"file_name"},{default:a(()=>[e(A,{modelValue:y.value.file_name,"onUpdate:modelValue":s[5]||(s[5]=l=>y.value.file_name=l),placeholder:"请输入文件名"},null,8,["modelValue"])]),_:1}),e(ae,{label:"状态",prop:"status"},{default:a(()=>[e(k,{modelValue:y.value.status,"onUpdate:modelValue":s[6]||(s[6]=l=>y.value.status=l),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e($t,{visible:O.value,"onUpdate:visible":s[9]||(s[9]=l=>O.value=l),fileType:"image",onSelect:De},null,8,["visible"])])}}}),Gt=nt(Ot,[["__scopeId","data-v-9a5a8ded"]]);function Jt(m){return W({url:"/api/admin/customer-services",method:"get",params:m})}function Xt(m){return W({url:"/api/admin/customer-services",method:"post",data:m})}function Ue(m,w){return W({url:`/api/admin/customer-services/${m}`,method:"put",data:w})}function Zt(m){return W({url:`/api/admin/customer-services/${m}`,method:"delete"})}function el(m){return W({url:"/api/admin/customer-services/batch-delete",method:"post",data:{ids:m}})}function tl(m){return W({url:"/api/admin/customer-services/batch-update-weights",method:"post",data:m})}const ll={class:"customer-service-container"},al={class:"tab-container"},ol={key:0},sl={class:"toolbar"},nl={class:"toolbar-left"},il={class:"toolbar-right"},rl={class:"table-wrapper"},ul={class:"type-tag-content"},dl={class:"url-cell"},cl={class:"url-text"},pl={class:"operation-buttons-container"},ml={class:"pagination-container"},vl={class:"dialog-footer"},fl={class:"filter-panel"},gl={class:"filter-item"},_l={class:"filter-item"},hl={class:"filter-item"},yl={class:"filter-item"},bl={class:"filter-item"},wl={class:"filter-item"},Dl={class:"range-inputs"},Il={class:"filter-item"},Vl={class:"filter-item"},Cl={class:"dialog-footer"},$l={key:1,class:"images-tab-content"},ze=40,Sl=Ye({__name:"index",setup(m){const w=v("service"),$=()=>{w.value==="service"&&(K.value="",o())},H=v(null),R=v(null),M=v(!1),z=v(!1),Q=v(!1),Y=v(!0),O=v(!1),K=v(""),S=v([]),T=v([]),B=v(1),y=v(10),ie=v(0),u=v({id:"",type:"",title:"",url:"",status:"",weightMin:"",weightMax:"",createTimeRange:[],updateTimeRange:[]}),f=v({type:"WhatsApp",title:"",url:"",status:1,icon:"",weight:0,description:""}),ye={type:[{required:!0,message:"请选择平台类型",trigger:"change"}],title:[],url:[{required:!0,message:"请输入URL链接",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入正确的URL格式（以http或https开头）",trigger:"blur"}],description:[{required:!0,message:"请输入描述内容",trigger:"blur"}],weight:[{required:!0,message:"请输入权重值",trigger:"blur"}]},re=l=>{St(l,"客服链接已复制到剪贴板")},be=l=>({微信:"iconfont icon-wechat",企业微信:"iconfont icon-qiyeweixin",QQ:"iconfont icon-QQ",电话:"iconfont icon-phone",邮箱:"iconfont icon-email",在线客服:"iconfont icon-service",其他:"iconfont icon-other"})[l]||"iconfont icon-link",ue=l=>({微信:"success",企业微信:"success",QQ:"info",电话:"warning",邮箱:"info",在线客服:"danger",其他:""})[l]||"",P=l=>{console.log("选中的行:",l),S.value=l},de=l=>{y.value=l,B.value=1,E()},ce=l=>{B.value=l,E()},ee=()=>{B.value=1,E()},E=async()=>{var l,t;M.value=!0;try{const i={page:B.value,limit:y.value,sort:"weight",order:"asc"};K.value&&(i.keyword=K.value),u.value.type&&(i.type=u.value.type),u.value.title&&(i.title=u.value.title),u.value.url&&(i.url=u.value.url),u.value.status!==""&&(i.status=u.value.status),u.value.weightMin&&(i.weightMin=u.value.weightMin),u.value.weightMax&&(i.weightMax=u.value.weightMax),u.value.createTimeRange&&u.value.createTimeRange.length===2&&(i.createTimeStart=J(u.value.createTimeRange[0],"YYYY-MM-DD"),i.createTimeEnd=J(u.value.createTimeRange[1],"YYYY-MM-DD")),u.value.updateTimeRange&&u.value.updateTimeRange.length===2&&(i.updateTimeStart=J(u.value.updateTimeRange[0],"YYYY-MM-DD"),i.updateTimeEnd=J(u.value.updateTimeRange[1],"YYYY-MM-DD"));const b=await Jt(i);(l=b.data)!=null&&l.items?T.value=b.data.items.map(d=>({...d,status:typeof d.status=="boolean"?d.status?1:0:Number(d.status),createTime:d.created_at||d.createTime,updateTime:d.updated_at||d.updateTime,statusLoading:!1})):T.value=[],ie.value=((t=b.data)==null?void 0:t.total)||0}catch(i){console.error("获取客服列表失败:",i),c.error("获取数据失败，请重试")}finally{M.value=!1}},we=()=>{Y.value=!1,f.value={type:"WhatsApp",title:"",url:"",description:"",weight:1,status:1},z.value=!0},te=l=>{if(console.log("编辑按钮传入的行:",l),console.log("当前选中的行:",S.value),l&&(l instanceof Event||"isTrusted"in l)&&(l=void 0,console.log("传入了事件对象，已忽略")),!l){if(S.value.length!==1){c.warning("请选择一条记录");return}l=S.value[0],console.log("使用选中行数据:",l)}Y.value=!0,f.value={...l},z.value=!0},De=()=>{if(console.log("confirmDelete - 选中的项目:",S.value),S.value.length===0){c.warning("请至少选择一条记录");return}const l=[],t=[];for(const i of S.value)i&&i.id!==void 0&&i.id!==null&&!isNaN(Number(i.id))?(l.push(i),console.log(`confirmDelete - 有效项目ID: ${i.id}, 类型: ${typeof i.id}`)):(t.push(i),console.log(`confirmDelete - 无效项目ID: ${i==null?void 0:i.id}, 类型: ${typeof(i==null?void 0:i.id)}`));if(console.log(`confirmDelete - 有效项目数量: ${l.length}, 无效项目数量: ${t.length}`),l.length===0){console.error("confirmDelete - 没有有效的项目"),c.error("选中的记录中没有有效的ID");return}Ve.confirm("确定要删除选中的客服渠道吗？此操作不可撤销","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const i=l.map(b=>{const d=typeof b.id=="string"?parseInt(b.id):b.id;return console.log(`confirmDelete - 转换后的ID: ${d}, 类型: ${typeof d}`),d});console.log("confirmDelete - 批量删除的IDs:",i),await el(i),c.success("批量删除成功"),E(),S.value=[]}catch(i){console.error("批量删除客服渠道失败:",i),c.error("批量删除失败，请重试")}}).catch(()=>{console.log("confirmDelete - 用户取消删除")})},Ie=(l,t)=>{if(console.log("handleDeleteItem - 传入的row:",l),!l||l.id===void 0||l.id===null){console.error("handleDeleteItem - 无效的row对象或ID:",l),c.error("无效的客服渠道ID");return}console.log("handleDeleteItem - row.id:",l.id,"类型:",typeof l.id),Ve.confirm("确定要删除该客服渠道吗？此操作不可撤销","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const i=typeof l.id=="string"?parseInt(l.id):l.id;if(console.log("handleDeleteItem - 转换后的ID:",i,"类型:",typeof i),isNaN(i)){console.error("handleDeleteItem - ID无效，无法转换为数字:",l.id),c.error("无效的客服渠道ID");return}console.log("handleDeleteItem - 调用删除API, ID:",i),await Zt(i),c.success("删除成功"),E()}catch(i){console.error("删除客服渠道失败:",i),c.error("删除失败，请重试")}}).catch(()=>{console.log("handleDeleteItem - 用户取消删除")})},o=()=>{u.value={id:"",type:"",title:"",url:"",status:"",weightMin:"",weightMax:"",createTimeRange:[],updateTimeRange:[]}},s=()=>{Q.value=!1,B.value=1,E(),c.success("筛选条件已应用")},g=()=>{c.info("导出功能尚未实现")},p=l=>{l&&l.validate(async t=>{if(t)try{O.value=!0;const i=!!f.value.id;if(i&&f.value.id!==void 0){const b=typeof f.value.id=="string"?parseInt(f.value.id):f.value.id,d={type:f.value.type,title:f.value.title,url:f.value.url,status:f.value.status,description:f.value.description,weight:f.value.weight},C=T.value.find(x=>x.id===b),L=C&&C.weight!==d.weight;await Ue(b,d),L&&(await E(),T.value.sort((x,ve)=>{const se=d.weight||1;return x.id===b?se-1:ve.id===b?-(se-1):x.weight-ve.weight}),await oe(),c({message:"客服权重已更新，所有权重值已重新计算",type:"success",duration:2e3}))}else{const b={type:f.value.type,title:f.value.title,url:f.value.url,status:f.value.status,description:f.value.description,weight:f.value.weight||1};await Xt(b),await E(),b.weight&&b.weight>0&&await oe()}c.success(`${i?"编辑":"新增"}客服渠道成功`),z.value=!1,await E()}catch(i){console.error("提交表单出错",i),c.error("操作失败，请重试")}finally{O.value=!1}})},A=()=>{p(R.value)},D=async l=>{const t=l.status;try{l.statusLoading=!0;const i={type:l.type,title:l.title,url:l.url,status:l.status,description:l.description,weight:l.weight};await Ue(l.id,i),c.success(`客服渠道"${l.title}"${l.status===1?"已启用":"已禁用"}`),setTimeout(()=>{E()},300)}catch(i){console.error("更新状态失败:",i),c.error("更新状态失败，请重试"),l.status=t}finally{l.statusLoading=!1}},_=l=>{l?Ie(l):De()},k=v(!1),N=v(null),V=v(-1),j=v(-1),q=v(null),le=v(0),ae=(l,t)=>{l.preventDefault(),l.stopPropagation(),k.value=!0,V.value=t,N.value=T.value[t],le.value=l.clientY,document.addEventListener("mousemove",pe),document.addEventListener("mouseup",me),document.body.style.userSelect="none";const i=document.querySelectorAll(".el-table__row");i[t]&&(q.value=i[t],q.value.classList.add("row-dragging"),i.forEach((b,d)=>{d!==t&&(b.style.opacity="0.6")}))},pe=l=>{if(!k.value||!q.value)return;const t=l.clientY-le.value;q.value.style.transform=`translateY(${t}px)`;const i=Math.round(t/ze)+V.value;document.querySelectorAll(".el-table__row").forEach((d,C)=>{C===i&&C!==V.value?d.classList.add("row-drop-target"):d.classList.remove("row-drop-target")}),j.value=i},me=async l=>{if(!k.value||V.value===-1)return;document.removeEventListener("mousemove",pe),document.removeEventListener("mouseup",me),document.body.style.userSelect="",q.value&&(q.value.style.transform="",q.value.classList.remove("row-dragging"));const t=document.querySelectorAll(".el-table__row");t.forEach(d=>{d.style.opacity="1"});const i=l.clientY-le.value,b=Math.round(i/ze)+V.value;if(b>=0&&b<T.value.length&&b!==V.value)try{c({message:"正在更新排序...",type:"info",duration:1e3});const d=T.value.splice(V.value,1)[0];T.value.splice(b,0,d),await oe(),c({message:"更新排序成功，已重新计算所有权重",type:"success",duration:2e3}),await E()}catch(d){console.error("更新排序失败:",d),c.error("更新排序失败，请检查网络连接")}t.forEach(d=>d.classList.remove("row-drop-target")),k.value=!1,N.value=null,V.value=-1,j.value=-1,q.value=null},oe=async()=>{const l=T.value.map((i,b)=>({id:i.id,weight:b+1})),t=await tl(l);if(t.code!==200)throw new Error(t.message||"更新权重失败");return t};return Re(()=>{E()}),(l,t)=>{const i=ht,b=_t,d=We,C=Ae,L=Le,x=Ge,ve=bt,se=Je,it=Xe,rt=Pe,G=lt,fe=Ze,ut=qe,X=ot,dt=Vt,ct=at,Te=Fe,xe=Ct,pt=Oe;return U(),F("div",ll,[r("div",al,[e(b,{modelValue:w.value,"onUpdate:modelValue":t[0]||(t[0]=n=>w.value=n),onTabClick:$},{default:a(()=>[e(i,{label:"客服管理",name:"service"}),e(i,{label:"客服图片",name:"images"})]),_:1},8,["modelValue"])]),w.value==="service"?(U(),F("div",ol,[r("div",sl,[r("div",nl,[e(C,{class:"toolbar-button",type:"default",onClick:E},{default:a(()=>[e(d,null,{default:a(()=>[e(h(Qe))]),_:1}),t[25]||(t[25]=I("刷新 "))]),_:1}),e(C,{class:"toolbar-button",type:"success",onClick:we},{default:a(()=>[e(d,null,{default:a(()=>[e(h(He))]),_:1}),t[26]||(t[26]=I("添加 "))]),_:1}),e(C,{class:"toolbar-button",type:"primary",onClick:t[1]||(t[1]=()=>te()),disabled:S.value.length!==1},{default:a(()=>[e(d,null,{default:a(()=>[e(h(_e))]),_:1}),t[27]||(t[27]=I("编辑 "))]),_:1},8,["disabled"]),e(C,{class:"toolbar-button",type:"danger",onClick:t[2]||(t[2]=()=>_()),disabled:S.value.length===0},{default:a(()=>[e(d,null,{default:a(()=>[e(h(he))]),_:1}),t[28]||(t[28]=I("删除 "))]),_:1},8,["disabled"])]),r("div",il,[e(L,{modelValue:K.value,"onUpdate:modelValue":t[3]||(t[3]=n=>K.value=n),placeholder:"搜索客服名称",class:"search-input",clearable:"",onKeyup:Be(ee,["enter"])},null,8,["modelValue"]),e(C,{class:"search-button",type:"primary",onClick:ee},{default:a(()=>[e(d,null,{default:a(()=>[e(h(Ke))]),_:1})]),_:1}),e(C,{class:"toolbar-button filter-button",type:"default",onClick:t[4]||(t[4]=n=>Q.value=!0)},{default:a(()=>[e(d,null,{default:a(()=>[e(h(Ee))]),_:1}),t[29]||(t[29]=I("筛选 "))]),_:1}),e(C,{class:"toolbar-button export-button",type:"default",onClick:g},{default:a(()=>[e(d,null,{default:a(()=>[e(h(yt))]),_:1}),t[30]||(t[30]=I("导出 "))]),_:1})])]),e(rt,{class:"table-card"},{default:a(()=>[r("div",rl,[je((U(),Ce(it,{ref_key:"serviceTable",ref:H,data:T.value,border:"",stripe:"",style:{width:"100%"},"highlight-current-row":"",onSelectionChange:P,"cell-style":{whiteSpace:"nowrap",overflow:"visible"},"header-cell-style":{whiteSpace:"nowrap",overflow:"visible"},"show-overflow-tooltip":!1,"table-layout":"auto"},{default:a(()=>[e(x,{type:"selection",width:"55"}),e(x,{prop:"id",label:"ID",width:"60",sortable:""}),e(x,{prop:"type",label:"平台类型",width:"120"},{default:a(({row:n})=>[e(ve,{type:ue(n.type),effect:"light"},{default:a(()=>[r("div",ul,[r("i",{class:wt([be(n.type),"service-icon"])},null,2),I(" "+Z(n.type),1)])]),_:2},1032,["type"])]),_:1}),e(x,{prop:"title",label:"客服标题",width:"150","show-overflow-tooltip":""}),e(x,{prop:"url",label:"客服链接","min-width":"180"},{default:a(({row:n})=>[r("div",dl,[r("span",cl,Z(n.url),1),e(C,{type:"primary",link:"",icon:h(Dt),onClick:$e(ne=>re(n.url),["stop"])},null,8,["icon","onClick"])])]),_:1}),e(x,{prop:"description",label:"描述","min-width":"180","show-overflow-tooltip":""}),e(x,{prop:"status",label:"状态",width:"80"},{default:a(({row:n})=>[e(se,{modelValue:n.status,"onUpdate:modelValue":ne=>n.status=ne,"active-value":1,"inactive-value":0,onChange:ne=>D(n),loading:n.statusLoading},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])]),_:1}),e(x,{prop:"createTime",label:"创建时间",width:"160"},{default:a(({row:n})=>[I(Z(h(J)(n.createTime)),1)]),_:1}),e(x,{prop:"updateTime",label:"更新时间",width:"160"},{default:a(({row:n})=>[I(Z(h(J)(n.updateTime)),1)]),_:1}),e(x,{prop:"weight",label:"权重",width:"80",sortable:""}),e(x,{fixed:"right",label:"操作",width:"220",align:"center"},{default:a(({row:n,$index:ne})=>[r("div",pl,[e(C,{class:"operation-button icon-only drag-handle",size:"small",type:"primary",onMousedown:$e(mt=>ae(mt,ne),["prevent"])},{default:a(()=>[e(d,null,{default:a(()=>[e(h(It))]),_:1})]),_:2},1032,["onMousedown"]),e(C,{class:"operation-button icon-only",size:"small",type:"default",onClick:()=>te(n)},{default:a(()=>[e(d,null,{default:a(()=>[e(h(_e))]),_:1})]),_:2},1032,["onClick"]),e(C,{type:"danger",size:"small",onClick:()=>_(n),class:"operation-button icon-only"},{default:a(()=>[e(d,null,{default:a(()=>[e(h(he))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[pt,M.value]])])]),_:1}),r("div",ml,[e(ut,{"current-page":B.value,"onUpdate:currentPage":t[5]||(t[5]=n=>B.value=n),"page-size":y.value,"onUpdate:pageSize":t[6]||(t[6]=n=>y.value=n),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:ie.value,onSizeChange:de,onCurrentChange:ce,"pager-count":7,background:""},{sizes:a(()=>[e(fe,{"model-value":y.value,onChange:de,class:"custom-page-size"},{default:a(()=>[(U(),F(et,null,tt([10,20,50,100],n=>e(G,{key:n,value:n,label:`${n}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),e(Te,{modelValue:z.value,"onUpdate:modelValue":t[14]||(t[14]=n=>z.value=n),title:Y.value?"编辑客服渠道":"添加客服渠道",width:"550px","close-on-click-modal":!1,center:""},{footer:a(()=>[r("div",vl,[e(C,{type:"primary",loading:O.value,onClick:A},{default:a(()=>[e(d,null,{default:a(()=>[e(h(st))]),_:1}),t[31]||(t[31]=I("确定 "))]),_:1},8,["loading"]),e(C,{onClick:t[13]||(t[13]=n=>z.value=!1)},{default:a(()=>[e(d,null,{default:a(()=>[e(h(ke))]),_:1}),t[32]||(t[32]=I("取消 "))]),_:1})])]),default:a(()=>[e(ct,{ref_key:"formRef",ref:R,model:f.value,"label-width":"100px","label-position":"left",rules:ye},{default:a(()=>[e(X,{label:"平台类型",prop:"type"},{default:a(()=>[e(fe,{modelValue:f.value.type,"onUpdate:modelValue":t[7]||(t[7]=n=>f.value.type=n),style:{width:"100%"}},{default:a(()=>[e(G,{label:"WhatsApp",value:"WhatsApp"}),e(G,{label:"Telegram",value:"Telegram"})]),_:1},8,["modelValue"])]),_:1}),e(X,{label:"标题名称",prop:"title"},{default:a(()=>[e(L,{modelValue:f.value.title,"onUpdate:modelValue":t[8]||(t[8]=n=>f.value.title=n),placeholder:"请输入标题名称"},null,8,["modelValue"])]),_:1}),e(X,{label:"URL链接",prop:"url"},{default:a(()=>[e(L,{modelValue:f.value.url,"onUpdate:modelValue":t[9]||(t[9]=n=>f.value.url=n),placeholder:"请输入URL链接"},null,8,["modelValue"])]),_:1}),e(X,{label:"描述",prop:"description"},{default:a(()=>[e(L,{modelValue:f.value.description,"onUpdate:modelValue":t[10]||(t[10]=n=>f.value.description=n),type:"textarea",placeholder:"请输入描述内容",rows:"3"},null,8,["modelValue"])]),_:1}),e(X,{label:"权重",prop:"weight"},{default:a(()=>[e(dt,{modelValue:f.value.weight,"onUpdate:modelValue":t[11]||(t[11]=n=>f.value.weight=n),min:0,max:999,placeholder:"权重"},null,8,["modelValue"])]),_:1}),e(X,{label:"状态",prop:"status"},{default:a(()=>[e(se,{modelValue:f.value.status,"onUpdate:modelValue":t[12]||(t[12]=n=>f.value.status=n),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),e(Te,{modelValue:Q.value,"onUpdate:modelValue":t[24]||(t[24]=n=>Q.value=n),title:"筛选条件",width:"800px","close-on-click-modal":!1,center:""},{footer:a(()=>[r("div",Cl,[e(C,{type:"primary",onClick:s},{default:a(()=>[e(d,null,{default:a(()=>[e(h(Ee))]),_:1}),t[42]||(t[42]=I("应用筛选 "))]),_:1}),e(C,{onClick:o},{default:a(()=>[e(d,null,{default:a(()=>[e(h(ke))]),_:1}),t[43]||(t[43]=I("重置 "))]),_:1})])]),default:a(()=>[r("div",fl,[r("div",gl,[t[33]||(t[33]=r("div",{class:"filter-label"},"ID",-1)),e(L,{modelValue:u.value.id,"onUpdate:modelValue":t[15]||(t[15]=n=>u.value.id=n),placeholder:"请输入ID",clearable:""},null,8,["modelValue"])]),r("div",_l,[t[34]||(t[34]=r("div",{class:"filter-label"},"渠道名称",-1)),e(L,{modelValue:u.value.title,"onUpdate:modelValue":t[16]||(t[16]=n=>u.value.title=n),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),r("div",hl,[t[35]||(t[35]=r("div",{class:"filter-label"},"类型",-1)),e(fe,{modelValue:u.value.type,"onUpdate:modelValue":t[17]||(t[17]=n=>u.value.type=n),placeholder:"请选择类型",clearable:"",style:{width:"100%"}},{default:a(()=>[e(G,{label:"WhatsApp",value:"WhatsApp"}),e(G,{label:"Telegram",value:"Telegram"})]),_:1},8,["modelValue"])]),r("div",yl,[t[36]||(t[36]=r("div",{class:"filter-label"},"状态",-1)),e(fe,{modelValue:u.value.status,"onUpdate:modelValue":t[18]||(t[18]=n=>u.value.status=n),placeholder:"请选择状态",clearable:"",style:{width:"100%"}},{default:a(()=>[e(G,{label:"启用",value:1}),e(G,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),r("div",bl,[t[37]||(t[37]=r("div",{class:"filter-label"},"链接",-1)),e(L,{modelValue:u.value.url,"onUpdate:modelValue":t[19]||(t[19]=n=>u.value.url=n),placeholder:"请输入链接",clearable:""},null,8,["modelValue"])]),r("div",wl,[t[39]||(t[39]=r("div",{class:"filter-label"},"权重范围",-1)),r("div",Dl,[e(L,{modelValue:u.value.weightMin,"onUpdate:modelValue":t[20]||(t[20]=n=>u.value.weightMin=n),placeholder:"最小值",style:{width:"45%"}},null,8,["modelValue"]),t[38]||(t[38]=r("span",{style:{margin:"0 10px"}},"至",-1)),e(L,{modelValue:u.value.weightMax,"onUpdate:modelValue":t[21]||(t[21]=n=>u.value.weightMax=n),placeholder:"最大值",style:{width:"45%"}},null,8,["modelValue"])])]),r("div",Il,[t[40]||(t[40]=r("div",{class:"filter-label"},"创建时间",-1)),e(xe,{modelValue:u.value.createTimeRange,"onUpdate:modelValue":t[22]||(t[22]=n=>u.value.createTimeRange=n),type:"daterange","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),r("div",Vl,[t[41]||(t[41]=r("div",{class:"filter-label"},"更新时间",-1)),e(xe,{modelValue:u.value.updateTimeRange,"onUpdate:modelValue":t[23]||(t[23]=n=>u.value.updateTimeRange=n),type:"daterange","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])])])]),_:1},8,["modelValue"])])):Se("",!0),w.value==="images"?(U(),F("div",$l,[e(Gt)])):Se("",!0)])}}}),Zl=nt(Sl,[["__scopeId","data-v-db4a3fd7"]]);export{Zl as default};
