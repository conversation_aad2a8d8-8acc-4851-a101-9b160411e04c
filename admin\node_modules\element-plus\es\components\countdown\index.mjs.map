{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/countdown/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Countdown from './src/countdown.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCountdown: SFCWithInstall<typeof Countdown> =\n  withInstall(Countdown)\nexport default ElCountdown\n\nexport * from './src/countdown'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC,SAAS;;;;"}