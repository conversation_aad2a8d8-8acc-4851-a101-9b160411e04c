require('dotenv').config();
const bcrypt = require('bcryptjs');
const { Sequelize, DataTypes } = require('sequelize');
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 从环境变量获取数据库配置
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || 3306;
const DB_DATABASE = process.env.DB_DATABASE || 'fox_db';
const DB_USERNAME = process.env.DB_USERNAME || 'root';
const DB_PASSWORD = 'MySQL3352~!';

async function initDatabase() {
  try {
    // 创建数据库连接（不指定数据库名）
    console.log('正在连接到MySQL服务器...');
    const connection = await mysql.createConnection({
      host: DB_HOST,
      port: DB_PORT,
      user: DB_USERNAME,
      password: DB_PASSWORD
    });

    // 创建数据库
    console.log(`正在创建数据库 ${DB_DATABASE}...`);
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${DB_DATABASE}`);
    console.log(`数据库 ${DB_DATABASE} 创建成功`);

    // 关闭连接
    await connection.end();

    // 创建 Sequelize 实例
    const sequelize = new Sequelize(DB_DATABASE, DB_USERNAME, DB_PASSWORD, {
      host: DB_HOST,
      port: DB_PORT,
      dialect: 'mysql',
      define: {
        timestamps: true,
        underscored: true,
      },
      logging: console.log
    });

    // 手动定义模型
    // Admin 模型
    const Admin = sequelize.define('Admin', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
      },
      password: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true,
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      avatar: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      role: {
        type: DataTypes.ENUM('super', 'admin', 'operator'),
        defaultValue: 'operator',
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive'),
        defaultValue: 'active',
        allowNull: false,
      },
      last_login: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    }, {
      tableName: 'admins',
      timestamps: true,
    });

    // User 模型
    const User = sequelize.define('User', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
      },
      password: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true,
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: false,
        unique: true,
      },
      avatar: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      id_card: {
        type: DataTypes.STRING(18),
        allowNull: true,
      },
      balance: {
        type: DataTypes.DECIMAL(12, 2),
        defaultValue: 0,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'blocked'),
        defaultValue: 'active',
        allowNull: false,
      },
      last_login: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    }, {
      tableName: 'users',
      timestamps: true,
    });

    // Project 模型
    const Project = sequelize.define('Project', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      title: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      cover_image: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      type: {
        type: DataTypes.ENUM('fixed', 'flexible'),
        defaultValue: 'fixed',
        allowNull: false,
      },
      expected_return: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: false,
      },
      min_investment: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
        defaultValue: 1000,
      },
      max_investment: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: true,
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      total_amount: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
      },
      current_amount: {
        type: DataTypes.DECIMAL(12, 2),
        defaultValue: 0,
        allowNull: false,
      },
      start_time: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      end_time: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('draft', 'pending', 'active', 'completed', 'cancelled'),
        defaultValue: 'draft',
        allowNull: false,
      },
      risk_level: {
        type: DataTypes.ENUM('low', 'medium', 'high'),
        defaultValue: 'medium',
        allowNull: false,
      },
      admin_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    }, {
      tableName: 'projects',
      timestamps: true,
    });

    // Order 模型
    const Order = sequelize.define('Order', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      order_no: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      project_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      amount: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
      },
      expected_return: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
      },
      actual_return: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM('pending', 'paid', 'completed', 'cancelled', 'refunded'),
        defaultValue: 'pending',
        allowNull: false,
      },
      payment_method: {
        type: DataTypes.ENUM('balance', 'alipay', 'wechat', 'bank'),
        allowNull: true,
      },
      payment_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      start_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      end_time: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    }, {
      tableName: 'orders',
      timestamps: true,
    });

    // 定义模型之间的关系
    Admin.hasMany(Project, { foreignKey: 'admin_id', as: 'projects' });
    Project.belongsTo(Admin, { foreignKey: 'admin_id', as: 'admin' });

    User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' });
    Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

    Project.hasMany(Order, { foreignKey: 'project_id', as: 'orders' });
    Order.belongsTo(Project, { foreignKey: 'project_id', as: 'project' });

    // 同步数据库模型
    console.log('正在同步数据库模型...');
    await sequelize.sync({ force: true });
    console.log('数据库模型同步完成');

    // 创建超级管理员
    console.log('正在创建超级管理员...');
    const hashedPassword = await bcrypt.hash('admin123', 10);

    const admin = await Admin.create({
      username: 'admin',
      password: hashedPassword,
      name: '超级管理员',
      email: '<EMAIL>',
      role: 'super',
      status: 'active'
    });

    console.log('超级管理员创建成功');

    // 创建测试管理员
    console.log('正在创建测试管理员...');
    const operator = await Admin.create({
      username: 'operator',
      password: hashedPassword,
      name: '运营人员',
      email: '<EMAIL>',
      role: 'operator',
      status: 'active'
    });

    console.log('测试管理员创建成功');

    // 创建测试用户
    console.log('正在创建测试用户...');
    const user1 = await User.create({
      username: 'user1',
      password: hashedPassword,
      name: '测试用户1',
      email: '<EMAIL>',
      phone: '13800138001',
      balance: 10000,
      status: 'active'
    });

    const user2 = await User.create({
      username: 'user2',
      password: hashedPassword,
      name: '测试用户2',
      email: '<EMAIL>',
      phone: '13800138002',
      balance: 20000,
      status: 'active'
    });

    console.log('测试用户创建成功');

    // 创建测试项目
    console.log('正在创建测试项目...');
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30);

    const project1 = await Project.create({
      title: '稳健理财产品A',
      description: '这是一个低风险的理财产品，适合保守型投资者。',
      type: 'fixed',
      expected_return: 5.5,
      min_investment: 1000,
      max_investment: 50000,
      duration: 90,
      total_amount: 1000000,
      current_amount: 0,
      start_time: startDate,
      end_time: endDate,
      status: 'active',
      risk_level: 'low',
      admin_id: admin.id
    });

    const project2 = await Project.create({
      title: '高收益理财产品B',
      description: '这是一个高风险高收益的理财产品，适合激进型投资者。',
      type: 'flexible',
      expected_return: 12.0,
      min_investment: 5000,
      max_investment: 100000,
      duration: 180,
      total_amount: 2000000,
      current_amount: 0,
      start_time: startDate,
      end_time: endDate,
      status: 'active',
      risk_level: 'high',
      admin_id: admin.id
    });

    console.log('测试项目创建成功');

    // 创建测试订单
    console.log('正在创建测试订单...');
    const order = await Order.create({
      order_no: 'FOX202504200001',
      user_id: user1.id,
      project_id: project1.id,
      amount: 10000,
      expected_return: 137.5, // 10000 * 5.5% * 90 / 365
      status: 'paid',
      payment_method: 'balance',
      payment_time: new Date(),
      start_time: new Date(),
      end_time: new Date(new Date().setDate(new Date().getDate() + 90))
    });

    // 更新项目已募集金额
    project1.current_amount = 10000;
    await project1.save();

    console.log('测试订单创建成功');

    console.log('数据库初始化完成');
    process.exit(0);
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

initDatabase();
