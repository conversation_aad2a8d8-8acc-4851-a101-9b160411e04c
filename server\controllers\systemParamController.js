const { SystemParam } = require('../models');
const { Op } = require('sequelize');
const timezoneUtils = require('../utils/timezoneUtils');
const timezoneConfig = require('../utils/timezoneConfig');

// 获取系统参数列表
exports.getSystemParams = async (req, res) => {
  try {
    const { group_name } = req.query;

    // 构建查询条件
    const where = {};

    if (group_name) {
      where.group_name = group_name;
    }

    // 查询系统参数
    const params = await SystemParam.findAll({
      where,
      order: [
        ['group_name', 'ASC'],
        ['sort_order', 'ASC']
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: params
    });
  } catch (error) {
    console.error('获取系统参数失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};

// 获取指定分组的系统参数
exports.getSystemParamsByGroup = async (req, res) => {
  try {
    const { group } = req.params;

    // 查询系统参数
    const params = await SystemParam.findAll({
      where: {
        group_name: group
      },
      order: [
        ['sort_order', 'ASC']
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        items: params
      }
    });
  } catch (error) {
    console.error('获取系统参数失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};

// 获取系统参数详情
exports.getSystemParamByKey = async (req, res) => {
  try {
    const { key } = req.params;

    // 查询系统参数
    const param = await SystemParam.findOne({
      where: {
        param_key: key
      }
    });

    if (!param) {
      return res.status(404).json({
        code: 404,
        message: '参数不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: param
    });
  } catch (error) {
    console.error('获取系统参数失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};

// 更新系统参数
exports.updateSystemParams = async (req, res) => {
  try {
    const { params } = req.body;

    if (!params || !Array.isArray(params) || params.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '参数错误',
        data: null
      });
    }

    // 检查是否有时区参数
    const timezoneParam = params.find(param => param.param_key === '[site.timezone]');
    let oldTimezone = null;
    let timezoneChanged = false;

    if (timezoneParam) {
      // 验证时区格式
      const isValidOffset = /^[+-]([01]\d|2[0-3]):([0-5]\d)$/.test(timezoneParam.param_value);
      if (!isValidOffset) {
        return res.status(400).json({
          code: 400,
          message: '时区格式无效，请使用 +/-HH:MM 格式',
          data: null
        });
      }

      // 获取旧的时区设置
      const oldTimezoneParam = await SystemParam.findOne({
        where: { param_key: '[site.timezone]' }
      });

      if (oldTimezoneParam) {
        oldTimezone = oldTimezoneParam.param_value;
        // 检查时区是否变更
        if (oldTimezone !== timezoneParam.param_value) {
          timezoneChanged = true;
        }
      } else {
        // 如果之前没有时区设置，现在添加了，也视为变更
        timezoneChanged = true;
      }
    }

    // 批量更新系统参数
    for (const param of params) {
      if (!param.param_key) continue;

      const [systemParam, created] = await SystemParam.findOrCreate({
        where: { param_key: param.param_key },
        defaults: {
          param_value: param.param_value,
          param_type: param.param_type || 'text',
          description: param.description || '',
          group_name: param.group_name || 'basic',
          sort_order: param.sort_order || 0
        }
      });

      if (!created) {
        await systemParam.update({
          param_value: param.param_value,
          param_type: param.param_type || systemParam.param_type,
          description: param.description || systemParam.description,
          group_name: param.group_name || systemParam.group_name,
          sort_order: param.sort_order || systemParam.sort_order
        });
      }
    }

    // 如果时区设置变更，刷新全局时区配置
    if (timezoneChanged) {
      try {
        console.log('时区设置已变更，刷新全局时区配置...');
        const newTimezone = await timezoneConfig.refreshTimezone();
        console.log(`全局时区配置已刷新为: ${newTimezone}`);
      } catch (error) {
        console.error('刷新全局时区配置失败:', error);
        // 时区刷新失败不影响参数更新结果
      }
    }

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: {
        oldTimezone,
        timezoneChanged
      }
    });
  } catch (error) {
    console.error('更新系统参数失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};

// 创建系统参数
exports.createSystemParam = async (req, res) => {
  try {
    const { param_key, param_value, param_type, description, group_name, sort_order } = req.body;

    if (!param_key || param_value === undefined) {
      return res.status(400).json({
        code: 400,
        message: '参数键和参数值不能为空',
        data: null
      });
    }

    // 检查参数键是否已存在
    const existingParam = await SystemParam.findOne({
      where: { param_key }
    });

    if (existingParam) {
      return res.status(400).json({
        code: 400,
        message: '参数键已存在',
        data: null
      });
    }

    // 创建系统参数
    const systemParam = await SystemParam.create({
      param_key,
      param_value,
      param_type: param_type || 'text',
      description: description || '',
      group_name: group_name || 'basic',
      sort_order: sort_order || 0
    });

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: systemParam
    });
  } catch (error) {
    console.error('创建系统参数失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};

// 删除系统参数
exports.deleteSystemParam = async (req, res) => {
  try {
    const { key } = req.params;

    // 查询系统参数
    const param = await SystemParam.findOne({
      where: { param_key: key }
    });

    if (!param) {
      return res.status(404).json({
        code: 404,
        message: '参数不存在',
        data: null
      });
    }

    // 删除系统参数
    await param.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除系统参数失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
};
