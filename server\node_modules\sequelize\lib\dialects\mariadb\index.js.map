{"version": 3, "sources": ["../../../src/dialects/mariadb/index.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst AbstractDialect = require('../abstract');\nconst ConnectionManager = require('./connection-manager');\nconst Query = require('./query');\nconst QueryGenerator = require('./query-generator');\nconst { MySQLQueryInterface } = require('../mysql/query-interface');\nconst DataTypes = require('../../data-types').mariadb;\n\nclass MariadbDialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new ConnectionManager(this, sequelize);\n    this.queryGenerator = new QueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n    this.queryInterface = new MySQLQueryInterface(\n      sequelize,\n      this.queryGenerator\n    );\n  }\n\n  canBackslashEscape() {\n    return true;\n  }\n}\n\nMariadbDialect.prototype.supports = _.merge(\n  _.cloneDeep(AbstractDialect.prototype.supports),\n  {\n    'VALUES ()': true,\n    'LIMIT ON UPDATE': true,\n    lock: true,\n    forShare: 'LOCK IN SHARE MODE',\n    settingIsolationLevelDuringTransaction: false,\n    schemas: true,\n    inserts: {\n      ignoreDuplicates: ' IGNORE',\n      updateOnDuplicate: ' ON DUPLICATE KEY UPDATE'\n    },\n    index: {\n      collate: false,\n      length: true,\n      parser: true,\n      type: true,\n      using: 1\n    },\n    constraints: {\n      dropConstraint: false,\n      check: false\n    },\n    indexViaAlter: true,\n    indexHints: true,\n    NUMERIC: true,\n    GEOMETRY: true,\n    JSON: true,\n    REGEXP: true\n  }\n);\n\nMariadbDialect.prototype.defaultVersion = '10.1.44'; // minimum supported version\nMariadbDialect.prototype.Query = Query;\nMariadbDialect.prototype.QueryGenerator = QueryGenerator;\nMariadbDialect.prototype.DataTypes = DataTypes;\nMariadbDialect.prototype.name = 'mariadb';\nMariadbDialect.prototype.TICK_CHAR = '`';\nMariadbDialect.prototype.TICK_CHAR_LEFT = MariadbDialect.prototype.TICK_CHAR;\nMariadbDialect.prototype.TICK_CHAR_RIGHT = MariadbDialect.prototype.TICK_CHAR;\n\nmodule.exports = MariadbDialect;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,oBAAoB,QAAQ;AAClC,MAAM,QAAQ,QAAQ;AACtB,MAAM,iBAAiB,QAAQ;AAC/B,MAAM,EAAE,wBAAwB,QAAQ;AACxC,MAAM,YAAY,QAAQ,oBAAoB;AAE9C,6BAA6B,gBAAgB;AAAA,EAC3C,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACrD,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,UAAU;AAAA,MACV;AAAA;AAEF,SAAK,iBAAiB,IAAI,oBACxB,WACA,KAAK;AAAA;AAAA,EAIT,qBAAqB;AACnB,WAAO;AAAA;AAAA;AAIX,eAAe,UAAU,WAAW,EAAE,MACpC,EAAE,UAAU,gBAAgB,UAAU,WACtC;AAAA,EACE,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,wCAAwC;AAAA,EACxC,SAAS;AAAA,EACT,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,mBAAmB;AAAA;AAAA,EAErB,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA;AAAA,EAET,aAAa;AAAA,IACX,gBAAgB;AAAA,IAChB,OAAO;AAAA;AAAA,EAET,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA;AAIZ,eAAe,UAAU,iBAAiB;AAC1C,eAAe,UAAU,QAAQ;AACjC,eAAe,UAAU,iBAAiB;AAC1C,eAAe,UAAU,YAAY;AACrC,eAAe,UAAU,OAAO;AAChC,eAAe,UAAU,YAAY;AACrC,eAAe,UAAU,iBAAiB,eAAe,UAAU;AACnE,eAAe,UAAU,kBAAkB,eAAe,UAAU;AAEpE,OAAO,UAAU;", "names": []}