{"version": 3, "file": "prev.mjs", "sources": ["../../../../../../../packages/components/pagination/src/components/prev.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Prev from './prev.vue'\n\nexport const paginationPrevProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1,\n  },\n  prevText: {\n    type: String,\n  },\n  prevIcon: {\n    type: iconPropType,\n  },\n} as const)\n\nexport const paginationPrevEmits = {\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\n\nexport type PaginationPrevProps = ExtractPropTypes<typeof paginationPrevProps>\n\nexport type PrevInstance = InstanceType<typeof Prev> & unknown\n"], "names": [], "mappings": ";;;AACY,MAAC,mBAAmB,GAAG,UAAU,CAAC;AAC9C,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,mBAAmB,GAAG;AACnC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C;;;;"}