/**
 * 投资项目相关 API 服务
 */
import { get, post } from '../../utils/request';

/**
 * 获取投资项目列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.type - 项目类型
 * @param {string} params.category - 项目分类
 * @returns {Promise} - 返回项目列表
 */
export function getProjects(params) {
  return get('/mobile/projects', { params });
}

/**
 * 获取投资项目详情
 * @param {number} id - 项目ID
 * @param {Object} params - 查询参数
 * @param {string} params.fields - 需要返回的字段，逗号分隔
 * @returns {Promise} - 返回项目详情
 */
export function getProject(id, params = {}) {
  return get(`/mobile/projects/${id}`, { params });
}

/**
 * 投资项目
 * @param {Object} data - 投资数据
 * @param {number} data.project_id - 项目ID
 * @param {number} data.amount - 投资金额
 * @returns {Promise} - 返回投资结果
 */
export function investProject(data) {
  return post('/mobile/investments', data);
}

/**
 * 获取用户的投资记录
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 投资状态
 * @returns {Promise} - 返回投资记录
 */
export function getUserInvestments(params) {
  return get('/mobile/investments', { params });
}
