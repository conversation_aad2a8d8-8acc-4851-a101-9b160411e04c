const { Project, UserLevel, Attachment, Investment } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 管理员端 - 获取项目列表
exports.getProjects = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      keyword,
      status,
      type,
      category,
      vipLevel,
      sellStatus,
      createTimeStart,
      createTimeEnd,
      updateTimeStart,
      updateTimeEnd,
      customReturnEnabled
    } = req.query;

    // 构建查询条件
    const where = {};

    if (keyword) {
      where[Op.or] = [
        { name: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ];
    }

    if (status !== undefined) {
      where.status = status === '1' || status === 'true';
    }

    if (type) {
      where.type = type;
    }

    if (category) {
      where.category = category;
    }

    if (vipLevel) {
      // 查询对应的VIP级别ID
      const userLevel = await sequelize.models.UserLevel.findOne({
        where: { level: vipLevel }
      });
      if (userLevel) {
        where.vip_level_id = userLevel.id;
      }
    }

    if (sellStatus !== undefined) {
      where.sell_status = parseInt(sellStatus);
    }

    if (customReturnEnabled !== undefined) {
      where.custom_return_enabled = customReturnEnabled === '1' || customReturnEnabled === 'true';
    }

    // 创建时间范围查询
    if (createTimeStart || createTimeEnd) {
      where.created_at = {};
      if (createTimeStart) {
        where.created_at[Op.gte] = new Date(createTimeStart);
      }
      if (createTimeEnd) {
        where.created_at[Op.lte] = new Date(createTimeEnd);
      }
    }

    // 更新时间范围查询
    if (updateTimeStart || updateTimeEnd) {
      where.updated_at = {};
      if (updateTimeStart) {
        where.updated_at[Op.gte] = new Date(updateTimeStart);
      }
      if (updateTimeEnd) {
        where.updated_at[Op.lte] = new Date(updateTimeEnd);
      }
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Project.findAndCountAll({
      where,
      include: [
        {
          model: sequelize.models.Attachment,
          as: 'image',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.Attachment,
          as: 'video',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.UserLevel,
          as: 'vip_level',
          attributes: ['id', 'name', 'level'],
          required: false
        }
      ],
      order: [['sort_order', 'DESC'], ['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 处理返回数据
    const items = rows.map(project => {
      const item = project.toJSON();

      // 处理图片和视频URL
      item.imageUrl = item.image ? item.image.file_path : null;
      item.videoUrl = item.video ? item.video.file_path : null;

      // 处理VIP级别
      item.vipLevel = item.vip_level ? item.vip_level.name : '';
      item.vipLevelNumber = item.vip_level ? item.vip_level.level : 0;

      // 格式化时间
      item.createTime = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
      item.updateTime = new Date(item.updated_at).toLocaleString('zh-CN', { hour12: false });

      // 处理布尔值转换为数字
      item.status = item.status ? 1 : 0;

      // 计算实际可用状态（用于状态开关显示）
      // 如果项目被禁用或库存为0，则实际状态为禁用
      if (!item.status || (item.quantity > 0 && item.actual_quantity <= 0)) {
        item.actualStatus = 0; // 实际不可用
      } else {
        item.actualStatus = 1; // 实际可用
      }

      item.commissionEnabled = item.commission_enabled ? 1 : 0;
      item.returnPrincipal = item.return_principal;
      item.isFree = item.is_free;

      // 添加每周收益日
      item.weeklyProfitDays = item.weekly_profit_days || '1,2,3,4,5,6,7';

      // 添加自定义收益率信息
      item.customReturnEnabled = item.custom_return_enabled ? 1 : 0;
      item.customReturnHours = item.custom_return_hours;
      item.customReturnRate = item.custom_return_rate;

      // 添加缺失的字段转换
      item.minInvestment = item.min_investment;
      item.maxInvestment = item.max_investment;
      item.expectedReturn = item.expected_return;
      item.maxPurchaseTimes = item.max_purchase_times;
      item.simultaneousPurchases = item.simultaneous_purchases;
      item.maxProfitTimes = item.max_profit_times;
      item.priceType = item.price_type;
      item.paymentMethod = item.payment_method;
      item.purchaseTime = item.purchase_time;
      item.sellPrice = item.sell_price;
      // 动态计算出售状态
      if (!item.status) {
        // 如果项目状态为禁用，显示为待售
        item.sellStatus = 0; // 待售
      } else if (item.quantity > 0 && item.actual_quantity <= 0) {
        // 如果设置了数量限制且库存为0，显示为售完
        item.sellStatus = 2; // 售完
      } else if (item.quantity > 0 && item.actual_quantity > 0 && item.sell_status === 2) {
        // 如果有库存但状态还是售完，自动恢复到在售状态
        item.sellStatus = 1; // 在售
      } else {
        // 其他情况使用原始的sell_status
        item.sellStatus = item.sell_status;
      }

      item.vipTypeOperator = item.vip_type_operator;
      item.settlementType = item.settlement_type;
      item.level1Commission = item.level1_commission;
      item.level2Commission = item.level2_commission;
      item.level3Commission = item.level3_commission;
      item.actualQuantity = item.actual_quantity;
      item.sortOrder = item.sort_order;
      item.profitTime = item.profit_time;
      item.soldQuantity = item.sold_quantity;

      return item;
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items
      }
    });
  } catch (error) {
    console.error('获取项目列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取项目详情
exports.getProject = async (req, res) => {
  try {
    const { id } = req.params;

    const project = await Project.findByPk(id, {
      include: [
        {
          model: sequelize.models.Attachment,
          as: 'image',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.Attachment,
          as: 'video',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.UserLevel,
          as: 'vip_level',
          attributes: ['id', 'name', 'level'],
          required: false
        }
      ]
    });

    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 处理返回数据
    const item = project.toJSON();

    // 处理图片和视频URL
    item.imageUrl = item.image ? item.image.file_path : null;
    item.videoUrl = item.video ? item.video.file_path : null;

    // 处理VIP级别
    item.vipLevel = item.vip_level ? item.vip_level.name : '';
    item.vipLevelNumber = item.vip_level ? item.vip_level.level : 0;

    // 格式化时间
    item.createTime = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
    item.updateTime = new Date(item.updated_at).toLocaleString('zh-CN', { hour12: false });

    // 处理布尔值转换为数字
    item.status = item.status ? 1 : 0;
    item.commissionEnabled = item.commission_enabled ? 1 : 0;
    item.returnPrincipal = item.return_principal;
    item.isFree = item.is_free;

    // 添加每周收益日
    item.weeklyProfitDays = item.weekly_profit_days || '1,2,3,4,5,6,7';

    // 处理购买时间范围
    item.purchaseTimeRange = item.purchase_time ? item.purchase_time.split('-') : ['00:00:00', '23:59:59'];

    // 添加自定义收益率信息
    item.customReturnEnabled = item.custom_return_enabled ? 1 : 0;
    item.customReturnHours = item.custom_return_hours;
    item.customReturnRate = item.custom_return_rate;

    // 添加缺失的字段转换
    item.maxPurchaseTimes = item.max_purchase_times;
    item.simultaneousPurchases = item.simultaneous_purchases;
    item.maxProfitTimes = item.max_profit_times;
    item.priceType = item.price_type;
    item.paymentMethod = item.payment_method;
    item.sellPrice = item.sell_price;
    // 动态计算出售状态
    if (!item.status) {
      // 如果项目状态为禁用，显示为待售
      item.sellStatus = 0; // 待售
    } else if (item.quantity > 0 && item.actual_quantity <= 0) {
      // 如果设置了数量限制且库存为0，显示为售完
      item.sellStatus = 2; // 售完
    } else if (item.quantity > 0 && item.actual_quantity > 0 && item.sell_status === 2) {
      // 如果有库存但状态还是售完，自动恢复到在售状态
      item.sellStatus = 1; // 在售
    } else {
      // 其他情况使用原始的sell_status
      item.sellStatus = item.sell_status;
    }

    item.vipTypeOperator = item.vip_type_operator;
    item.settlementType = item.settlement_type;
    item.level1Commission = item.level1_commission;
    item.level2Commission = item.level2_commission;
    item.level3Commission = item.level3_commission;
    item.actualQuantity = item.actual_quantity;
    item.sortOrder = item.sort_order;
    item.soldQuantity = item.sold_quantity;

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: item
    });
  } catch (error) {
    console.error('获取项目详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 创建项目
exports.createProject = async (req, res) => {
  try {
    const {
      name,
      description,
      type,
      category,
      price,
      sell_price,
      price_type,
      payment_method,
      purchase_time,
      min_investment,
      max_investment,
      duration,
      expected_return,
      profit_time,
      quantity,
      actual_quantity,
      sort_order,
      max_purchase_times,
      simultaneous_purchases,
      max_profit_times,
      vip_level_number,
      vip_type_operator,
      currency,
      status,
      sell_status,
      weekly_profit_days,
      return_principal,
      is_free,
      commission_enabled,
      level1_commission,
      level2_commission,
      level3_commission,
      image_id,
      video_id,
      custom_return_enabled,
      custom_return_hours,
      custom_return_rate
    } = req.body;

    // 验证请求数据
    if (!name || !type || !price || !expected_return || !duration || !profit_time) {
      return res.status(400).json({
        code: 400,
        message: '项目名称、类型、价格、收益率、投资期限和收益时间不能为空',
        data: null
      });
    }

    // 查找VIP级别ID
    let vip_level_id = null;
    if (vip_level_number !== undefined && vip_level_number !== null && vip_level_number >= 0) {
      const userLevel = await sequelize.models.UserLevel.findOne({
        where: { level: vip_level_number }
      });
      if (userLevel) {
        vip_level_id = userLevel.id;
      }
    }

    // 创建项目
    const project = await Project.create({
      name,
      description,
      type,
      category: category || '基础',
      image_id,
      video_id,
      price,
      sell_price: sell_price || 0,
      price_type: price_type || '固定价格',
      payment_method: payment_method || '余额支付',
      purchase_time: purchase_time || '00:00:00-23:59:59',
      min_investment: min_investment || price,
      max_investment: max_investment || price,
      duration,
      expected_return,
      profit_time,
      quantity: quantity || 0,
      actual_quantity: actual_quantity !== undefined ? actual_quantity : (quantity || 0),
      sold_quantity: 0,
      sort_order: sort_order || 100,
      max_purchase_times: max_purchase_times || 0,
      simultaneous_purchases: simultaneous_purchases || 0,
      max_profit_times: max_profit_times || 0,
      vip_level_id,
      vip_type_operator: vip_type_operator || '=',
      currency: currency || 'CNY',
      status: status === undefined ? true : (status === 1 || status === '1' || status === true),
      sell_status: sell_status === undefined ? 1 : parseInt(sell_status),
      weekly_profit_days: weekly_profit_days || '1,2,3,4,5,6,7',
      return_principal: return_principal === true || return_principal === 1 || return_principal === '1',
      is_free: is_free === true || is_free === 1 || is_free === '1',
      commission_enabled: commission_enabled === true || commission_enabled === 1 || commission_enabled === '1',
      level1_commission: level1_commission || 0,
      level2_commission: level2_commission || 0,
      level3_commission: level3_commission || 0,
      custom_return_enabled: custom_return_enabled === true || custom_return_enabled === 1 || custom_return_enabled === '1',
      custom_return_hours: custom_return_hours || null,
      custom_return_rate: custom_return_rate || null
    });

    // 处理返回数据
    const item = project.toJSON();

    // 处理VIP级别
    const vipLevel = await sequelize.models.UserLevel.findByPk(project.vip_level_id);
    item.vipLevel = vipLevel ? vipLevel.name : '';
    item.vipLevelNumber = vipLevel ? vipLevel.level : 0;

    // 格式化时间
    item.createTime = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
    item.updateTime = new Date(item.updated_at).toLocaleString('zh-CN', { hour12: false });

    // 处理布尔值转换为数字
    item.status = item.status ? 1 : 0;
    item.commissionEnabled = item.commission_enabled ? 1 : 0;
    item.returnPrincipal = item.return_principal;
    item.isFree = item.is_free;

    // 添加每周收益日
    item.weeklyProfitDays = item.weekly_profit_days || '1,2,3,4,5,6,7';

    // 处理购买时间范围
    item.purchaseTimeRange = item.purchase_time ? item.purchase_time.split('-') : ['00:00:00', '23:59:59'];

    // 添加自定义收益信息
    item.customReturnEnabled = item.custom_return_enabled ? 1 : 0;
    item.customReturnHours = item.custom_return_hours;
    item.customReturnRate = item.custom_return_rate;

    // 添加缺失的字段转换
    item.maxPurchaseTimes = item.max_purchase_times;
    item.simultaneousPurchases = item.simultaneous_purchases;
    item.maxProfitTimes = item.max_profit_times;
    item.priceType = item.price_type;
    item.paymentMethod = item.payment_method;
    item.sellPrice = item.sell_price;
    // 动态计算出售状态
    if (!item.status) {
      // 如果项目状态为禁用，显示为待售
      item.sellStatus = 0; // 待售
    } else if (item.quantity > 0 && item.actual_quantity <= 0) {
      // 如果设置了数量限制且库存为0，显示为售完
      item.sellStatus = 2; // 售完
    } else if (item.quantity > 0 && item.actual_quantity > 0 && item.sell_status === 2) {
      // 如果有库存但状态还是售完，自动恢复到在售状态
      item.sellStatus = 1; // 在售
    } else {
      // 其他情况使用原始的sell_status
      item.sellStatus = item.sell_status;
    }

    item.vipTypeOperator = item.vip_type_operator;
    item.settlementType = item.settlement_type;
    item.level1Commission = item.level1_commission;
    item.level2Commission = item.level2_commission;
    item.level3Commission = item.level3_commission;
    item.actualQuantity = item.actual_quantity;
    item.sortOrder = item.sort_order;

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: item
    });
  } catch (error) {
    console.error('创建项目错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 更新项目
exports.updateProject = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      type,
      category,
      price,
      sell_price,
      price_type,
      payment_method,
      purchase_time,
      min_investment,
      max_investment,
      duration,
      expected_return,
      profit_time,
      quantity,
      actual_quantity,
      sort_order,
      max_purchase_times,
      simultaneous_purchases,
      max_profit_times,
      vip_level_number,
      vip_type_operator,
      currency,
      status,
      sell_status,
      weekly_profit_days,
      return_principal,
      is_free,
      commission_enabled,
      level1_commission,
      level2_commission,
      level3_commission,
      image_id,
      video_id,
      custom_return_enabled,
      custom_return_hours,
      custom_return_rate
    } = req.body;

    // 查找项目
    const project = await Project.findByPk(id);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 查找VIP级别ID
    let vip_level_id = null;
    if (vip_level_number !== undefined && vip_level_number !== null && vip_level_number >= 0) {
      const userLevel = await sequelize.models.UserLevel.findOne({
        where: { level: vip_level_number }
      });
      if (userLevel) {
        vip_level_id = userLevel.id;
      }
    }

    // 更新项目信息
    if (name !== undefined) project.name = name;
    if (description !== undefined) project.description = description;
    if (type !== undefined) project.type = type;
    if (category !== undefined) project.category = category;
    if (image_id !== undefined) project.image_id = image_id;
    if (video_id !== undefined) project.video_id = video_id;
    if (price !== undefined) project.price = price;
    if (sell_price !== undefined) project.sell_price = sell_price;
    if (price_type !== undefined) project.price_type = price_type;
    if (payment_method !== undefined) project.payment_method = payment_method;
    if (purchase_time !== undefined) project.purchase_time = purchase_time;

    // 更新投资相关字段
    console.log('收到的数据:', {
      min_investment,
      max_investment,
      duration,
      expected_return,
      profit_time
    });

    if (min_investment !== undefined) project.min_investment = min_investment;
    if (max_investment !== undefined) project.max_investment = max_investment;
    if (duration !== undefined) project.duration = duration;
    if (expected_return !== undefined) project.expected_return = expected_return;
    if (profit_time !== undefined) project.profit_time = profit_time;

    // 添加调试日志
    console.log('更新前的项目数据:', {
      min_investment: project.min_investment,
      max_investment: project.max_investment,
      duration: project.duration,
      expected_return: project.expected_return,
      profit_time: project.profit_time
    });

    // 更新项目数量相关字段
    if (quantity !== undefined) {
      // 如果修改了总数量，需要相应调整实际数量
      const oldQuantity = project.quantity;
      project.quantity = quantity;

      // 如果没有明确指定实际数量，则根据总数量变化调整实际数量
      if (actual_quantity === undefined) {
        // 如果原来的总数量为0（不限制），则设置实际数量为新的总数量
        if (oldQuantity === 0) {
          project.actual_quantity = quantity;
        }
        // 如果新的总数量为0（不限制），则设置实际数量为0
        else if (quantity === 0) {
          project.actual_quantity = 0;
        }
        // 否则，根据总数量的变化调整实际数量
        else {
          const diff = quantity - oldQuantity;
          project.actual_quantity = Math.max(0, project.actual_quantity + diff);
        }
      }
    }

    // 如果明确指定了实际数量，直接使用指定的值
    if (actual_quantity !== undefined) {
      project.actual_quantity = actual_quantity;
    }

    // 检查库存恢复逻辑：如果产品之前售完，现在有库存了，自动恢复到在售状态
    if (project.quantity > 0 && project.actual_quantity > 0 && project.sell_status === 2) {
      // 如果设置了数量限制，且现在有库存，且当前是售完状态，则恢复到在售状态
      project.sell_status = 1;
      console.log(`产品ID ${project.id} 库存已恢复，自动从售完状态恢复到在售状态`);
    }
    if (sort_order !== undefined) project.sort_order = sort_order;
    if (max_purchase_times !== undefined) project.max_purchase_times = max_purchase_times;
    if (simultaneous_purchases !== undefined) project.simultaneous_purchases = simultaneous_purchases;
    if (max_profit_times !== undefined) project.max_profit_times = max_profit_times;
    if (vip_level_id !== null) project.vip_level_id = vip_level_id;
    if (vip_type_operator !== undefined) project.vip_type_operator = vip_type_operator;
    if (currency !== undefined) project.currency = currency;
    if (status !== undefined) project.status = status === 1 || status === '1' || status === true;
    if (sell_status !== undefined) project.sell_status = parseInt(sell_status);
    if (weekly_profit_days !== undefined) project.weekly_profit_days = weekly_profit_days;
    if (return_principal !== undefined) project.return_principal = return_principal === true || return_principal === 1 || return_principal === '1';
    if (is_free !== undefined) project.is_free = is_free === true || is_free === 1 || is_free === '1';
    if (commission_enabled !== undefined) project.commission_enabled = commission_enabled === true || commission_enabled === 1 || commission_enabled === '1';
    if (level1_commission !== undefined) project.level1_commission = level1_commission;
    if (level2_commission !== undefined) project.level2_commission = level2_commission;
    if (level3_commission !== undefined) project.level3_commission = level3_commission;
    if (custom_return_enabled !== undefined) project.custom_return_enabled = custom_return_enabled === true || custom_return_enabled === 1 || custom_return_enabled === '1';
    if (custom_return_hours !== undefined) project.custom_return_hours = custom_return_hours;
    if (custom_return_rate !== undefined) project.custom_return_rate = custom_return_rate;

    // 打印 SQL 查询
    console.log('保存前的项目数据:', {
      min_investment: project.min_investment,
      max_investment: project.max_investment,
      duration: project.duration,
      expected_return: project.expected_return,
      profit_time: project.profit_time
    });

    // 强制设置这些字段为脏数据，确保它们被包含在 SQL 更新语句中
    project.changed('duration', true);
    project.changed('profit_time', true);

    await project.save();

    // 打印保存后的项目数据
    console.log('保存后的项目数据:', {
      min_investment: project.min_investment,
      max_investment: project.max_investment,
      duration: project.duration,
      expected_return: project.expected_return,
      profit_time: project.profit_time
    });

    // 获取更新后的项目（包含关联数据）
    const updatedProject = await Project.findByPk(id, {
      include: [
        {
          model: sequelize.models.Attachment,
          as: 'image',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.Attachment,
          as: 'video',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.UserLevel,
          as: 'vip_level',
          attributes: ['id', 'name', 'level'],
          required: false
        }
      ]
    });

    // 处理返回数据
    const item = updatedProject.toJSON();

    // 处理图片和视频URL
    item.imageUrl = item.image ? item.image.file_path : null;
    item.videoUrl = item.video ? item.video.file_path : null;

    // 处理VIP级别
    item.vipLevel = item.vip_level ? item.vip_level.name : '';
    item.vipLevelNumber = item.vip_level ? item.vip_level.level : 0;

    // 格式化时间
    item.createTime = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
    item.updateTime = new Date(item.updated_at).toLocaleString('zh-CN', { hour12: false });

    // 处理布尔值转换为数字
    item.status = item.status ? 1 : 0;
    item.commissionEnabled = item.commission_enabled ? 1 : 0;
    item.returnPrincipal = item.return_principal;
    item.isFree = item.is_free;

    // 添加每周收益日
    item.weeklyProfitDays = item.weekly_profit_days || '1,2,3,4,5,6,7';

    // 处理购买时间范围
    item.purchaseTimeRange = item.purchase_time ? item.purchase_time.split('-') : ['00:00:00', '23:59:59'];

    // 添加自定义收益信息
    item.customReturnEnabled = item.custom_return_enabled ? 1 : 0;
    item.customReturnHours = item.custom_return_hours;
    item.customReturnRate = item.custom_return_rate;

    // 添加缺失的字段转换
    item.maxPurchaseTimes = item.max_purchase_times;
    item.simultaneousPurchases = item.simultaneous_purchases;
    item.maxProfitTimes = item.max_profit_times;
    item.priceType = item.price_type;
    item.paymentMethod = item.payment_method;
    item.sellPrice = item.sell_price;
    // 动态计算出售状态
    if (!item.status) {
      // 如果项目状态为禁用，显示为待售
      item.sellStatus = 0; // 待售
    } else if (item.quantity > 0 && item.actual_quantity <= 0) {
      // 如果设置了数量限制且库存为0，显示为售完
      item.sellStatus = 2; // 售完
    } else if (item.quantity > 0 && item.actual_quantity > 0 && item.sell_status === 2) {
      // 如果有库存但状态还是售完，自动恢复到在售状态
      item.sellStatus = 1; // 在售
    } else {
      // 其他情况使用原始的sell_status
      item.sellStatus = item.sell_status;
    }

    item.vipTypeOperator = item.vip_type_operator;
    item.settlementType = item.settlement_type;
    item.level1Commission = item.level1_commission;
    item.level2Commission = item.level2_commission;
    item.level3Commission = item.level3_commission;
    item.actualQuantity = item.actual_quantity;
    item.sortOrder = item.sort_order;
    item.soldQuantity = item.sold_quantity;

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: item
    });
  } catch (error) {
    console.error('更新项目错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 删除项目
exports.deleteProject = async (req, res) => {
  try {
    const { id } = req.params;

    // 查找项目
    const project = await Project.findByPk(id);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 检查项目是否有关联的投资记录
    const investmentCount = await sequelize.models.Investment.count({
      where: { project_id: id }
    });

    if (investmentCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '该项目已有投资记录，无法删除',
        data: null
      });
    }

    // 删除项目
    await project.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除项目错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取项目订单列表
exports.getProjectOrders = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    // 查找项目
    const project = await Project.findByPk(id);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 构建查询条件
    const where = { project_id: id };

    if (status) {
      where.status = status;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Investment.findAndCountAll({
      where,
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'phone']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 处理返回数据
    const items = rows.map(investment => {
      const item = investment.toJSON();

      // 格式化时间
      item.createTime = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
      item.updateTime = new Date(item.updated_at).toLocaleString('zh-CN', { hour12: false });

      return item;
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items
      }
    });
  } catch (error) {
    console.error('获取项目订单列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 获取项目列表
exports.getMobileProjects = async (req, res) => {
  try {
    // 设置缓存控制头，确保获取最新数据
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    const { page = 1, limit = 10, type, category } = req.query;
    const { Op } = require('sequelize');

    // 构建查询条件 - 动态处理状态恢复
    const where = {
      status: true,
      [Op.and]: [
        {
          [Op.or]: [
            { sell_status: 1 },  // 明确在售的产品
            {
              [Op.and]: [
                { sell_status: 2 },  // 售完状态
                { quantity: { [Op.gt]: 0 } },  // 设置了数量限制
                { actual_quantity: { [Op.gt]: 0 } }  // 但现在有库存
              ]
            }
          ]
        },
        {
          [Op.or]: [
            { quantity: 0 },  // 不限制数量的产品
            { actual_quantity: { [Op.gt]: 0 } }  // 或者有库存的产品
          ]
        }
      ]
    };

    if (type) {
      where.type = type;
    }

    if (category) {
      where.category = category;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Project.findAndCountAll({
      where,
      include: [
        {
          model: sequelize.models.Attachment,
          as: 'image',
          attributes: ['id', 'file_path'],
          required: false
        }
      ],
      order: [['sort_order', 'DESC'], ['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    // 处理返回数据
    const items = rows.map(project => {
      const item = project.toJSON();

      // 处理图片URL
      item.imageUrl = item.image ? item.image.file_path : null;

      // 动态处理状态恢复 - 如果产品有库存但状态是售完，在返回数据中显示为在售
      if (item.quantity > 0 && item.actual_quantity > 0 && item.sell_status === 2) {
        item.sell_status = 1; // 在返回数据中显示为在售

        // 异步更新数据库状态（不阻塞响应）
        setImmediate(async () => {
          try {
            await Project.update(
              { sell_status: 1 },
              { where: { id: item.id } }
            );
            console.log(`移动端查询时自动恢复产品 ${item.id} 的销售状态`);
          } catch (error) {
            console.error(`更新产品 ${item.id} 销售状态失败:`, error);
          }
        });
      }

      // 格式化时间
      item.createTime = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
      item.updateTime = new Date(item.updated_at).toLocaleString('zh-CN', { hour12: false });

      return item;
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items
      }
    });
  } catch (error) {
    console.error('获取移动端项目列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 获取项目详情
exports.getMobileProject = async (req, res) => {
  try {
    const { id } = req.params;

    const project = await Project.findByPk(id, {
      include: [
        {
          model: sequelize.models.Attachment,
          as: 'image',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.Attachment,
          as: 'video',
          attributes: ['id', 'file_path'],
          required: false
        },
        {
          model: sequelize.models.UserLevel,
          as: 'vip_level',
          attributes: ['id', 'name', 'level'],
          required: false
        }
      ]
    });

    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 检查项目状态
    if (!project.status) {
      return res.status(400).json({
        code: 400,
        message: '项目不可用',
        data: null
      });
    }

    // 检查项目销售状态
    if (project.sell_status !== 1) {
      return res.status(400).json({
        code: 400,
        message: '项目暂停销售',
        data: null
      });
    }

    // 检查项目库存（如果设置了数量限制）
    if (project.quantity > 0 && project.actual_quantity <= 0) {
      return res.status(400).json({
        code: 400,
        message: '项目已售罄',
        data: null
      });
    }

    // 处理返回数据
    const item = project.toJSON();

    // 处理图片和视频URL
    item.imageUrl = item.image ? item.image.file_path : null;
    item.videoUrl = item.video ? item.video.file_path : null;

    // 处理VIP级别
    item.vipLevel = item.vip_level ? item.vip_level.name : '';
    item.vipLevelNumber = item.vip_level ? item.vip_level.level : 0;

    // 格式化时间
    item.createTime = new Date(item.created_at).toLocaleString('zh-CN', { hour12: false });
    item.updateTime = new Date(item.updated_at).toLocaleString('zh-CN', { hour12: false });

    // 处理布尔值转换为数字
    item.status = item.status ? 1 : 0;
    item.commissionEnabled = item.commission_enabled ? 1 : 0;
    item.returnPrincipal = item.return_principal;
    item.isFree = item.is_free;

    // 添加每周收益日
    item.weeklyProfitDays = item.weekly_profit_days || '1,2,3,4,5,6,7';

    // 处理购买时间范围
    item.purchaseTimeRange = item.purchase_time ? item.purchase_time.split('-') : ['00:00:00', '23:59:59'];

    // 添加自定义收益率信息
    item.customReturnEnabled = item.custom_return_enabled ? 1 : 0;
    item.customReturnHours = item.custom_return_hours;
    item.customReturnRate = item.custom_return_rate;

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: item
    });
  } catch (error) {
    console.error('获取移动端项目详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新项目排序
exports.updateProjectSortOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const { sort_order } = req.body;

    if (sort_order === undefined || sort_order === null) {
      return res.status(400).json({
        code: 400,
        message: '排序值不能为空',
        data: null
      });
    }

    // 查找项目
    const project = await Project.findByPk(id);

    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 更新排序
    await project.update({
      sort_order: parseInt(sort_order)
    });

    res.json({
      code: 200,
      message: '更新项目排序成功',
      data: project
    });
  } catch (error) {
    console.error('更新项目排序失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新项目排序失败',
      data: null
    });
  }
};

// 批量更新项目排序
exports.updateProjectSortOrders = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const updates = req.body;

    if (!Array.isArray(updates) || updates.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '请提供有效的排序更新数据',
        data: null
      });
    }

    // 批量更新排序
    const updatePromises = updates.map(async (item) => {
      const { id, sort_order } = item;

      if (!id || sort_order === undefined || isNaN(parseInt(sort_order))) {
        return null;
      }

      const project = await Project.findByPk(id);

      if (!project) {
        return null;
      }

      return project.update({
        sort_order: parseInt(sort_order)
      }, { transaction });
    });

    await Promise.all(updatePromises.filter(Boolean));
    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '批量更新项目排序成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('批量更新项目排序失败:', error);
    return res.status(500).json({
      code: 500,
      message: '批量更新项目排序失败',
      data: null
    });
  }
};
