-- 创建数据库
CREATE DATABASE IF NOT EXISTS fox_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE fox_db;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VA<PERSON>HA<PERSON>(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    country_code VARCHAR(10) DEFAULT '+86',
    avatar VARCHAR(255) DEFAULT 'https://via.placeholder.com/30',
    invite_code VARCHAR(20) NOT NULL UNIQUE,
    inviter_id INT,
    level INT DEFAULT 1,
    gender ENUM('男', '女') DEFAULT '男',
    points INT DEFAULT 0,
    balance DECIMAL(15,2) DEFAULT 0.00,
    frozen_amount DECIMAL(15,2) DEFAULT 0.00,
    income_account_currency DECIMAL(15,2) DEFAULT 0.00,
    income_account_usdt DECIMAL(15,2) DEFAULT 0.00,
    deposit_account_currency DECIMAL(15,2) DEFAULT 0.00,
    deposit_account_usdt DECIMAL(15,2) DEFAULT 0.00,
    total_deposit DECIMAL(15,2) DEFAULT 0.00,
    withdrawal_amount DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'disabled') DEFAULT 'active',
    allow_purchase BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建管理员表
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nickname VARCHAR(50),
    is_super BOOLEAN DEFAULT FALSE,
    status BOOLEAN DEFAULT TRUE,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建角色表
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parent_id INT,
    name VARCHAR(50) NOT NULL,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES roles(id) ON DELETE SET NULL
);

-- 创建权限表
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    type ENUM('menu', 'operation') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY (role_id, permission_id)
);

-- 创建管理员角色关联表
CREATE TABLE IF NOT EXISTS admin_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    role_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    UNIQUE KEY (admin_id, role_id)
);

-- 创建投资项目表
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    category VARCHAR(50),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    video_url VARCHAR(255),
    price DECIMAL(15,2) NOT NULL,
    sell_price DECIMAL(15,2),
    min_investment DECIMAL(15,2) NOT NULL,
    max_investment DECIMAL(15,2) NOT NULL,
    price_type VARCHAR(50) DEFAULT '固定价格',
    payment_method VARCHAR(50) DEFAULT '余额支付',
    purchase_time VARCHAR(100) DEFAULT '不限',
    duration INT NOT NULL,
    duration_unit VARCHAR(10) DEFAULT '天',
    custom_return BOOLEAN DEFAULT FALSE,
    expected_return DECIMAL(5,2) NOT NULL,
    profit_time INT NOT NULL,
    quantity INT DEFAULT 0,
    actual_quantity INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    max_purchase_times INT DEFAULT 0,
    simultaneous_purchases INT DEFAULT 0,
    max_profit_times INT DEFAULT 0,
    sold_quantity INT DEFAULT 0,
    vip_level VARCHAR(20),
    vip_type VARCHAR(50),
    return_principal BOOLEAN DEFAULT TRUE,
    is_free BOOLEAN DEFAULT FALSE,
    currency VARCHAR(10) DEFAULT 'CNY',
    status BOOLEAN DEFAULT TRUE,
    sell_status BOOLEAN DEFAULT TRUE,
    commission_enabled BOOLEAN DEFAULT FALSE,
    level1_commission DECIMAL(5,2) DEFAULT 0.00,
    level2_commission DECIMAL(5,2) DEFAULT 0.00,
    level3_commission DECIMAL(5,2) DEFAULT 0.00,
    settlement_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建用户投资表
CREATE TABLE IF NOT EXISTS investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_id INT NOT NULL,
    investment_type ENUM('自动', '手动') DEFAULT '手动',
    quantity INT DEFAULT 1,
    currency VARCHAR(10) DEFAULT 'CNY',
    total_investment DECIMAL(15,2) NOT NULL,
    profit_cycle INT NOT NULL,
    profit_rate DECIMAL(5,2) NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    daily_profit DECIMAL(15,2) DEFAULT 0.00,
    last_profit_time DATETIME,
    profit_count INT DEFAULT 0,
    status ENUM('进行中', '暂停', '完成') DEFAULT '进行中',
    first_investment_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_investment_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 创建投资收益记录表
CREATE TABLE IF NOT EXISTS investment_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    investment_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    profit_time DATETIME NOT NULL,
    status ENUM('已发放', '未发放') DEFAULT '已发放',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建佣金记录表
CREATE TABLE IF NOT EXISTS commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    from_user_id INT NOT NULL,
    investment_id INT NOT NULL,
    level INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    rate DECIMAL(5,2) NOT NULL,
    status ENUM('已发放', '未发放') DEFAULT '已发放',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
);

-- 创建用户交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('充值', '提现', '投资', '收益', '佣金', '赠金', '扣除') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance DECIMAL(15,2) NOT NULL,
    description VARCHAR(255),
    related_id INT,
    status ENUM('成功', '失败', '处理中') DEFAULT '成功',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建用户银行卡表
CREATE TABLE IF NOT EXISTS user_bank_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    card_number VARCHAR(50) NOT NULL,
    card_holder VARCHAR(100) NOT NULL,
    branch VARCHAR(100),
    is_default BOOLEAN DEFAULT FALSE,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建收款银行卡表
CREATE TABLE IF NOT EXISTS receiving_bank_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bank_name VARCHAR(100) NOT NULL,
    card_number VARCHAR(50) NOT NULL,
    card_holder VARCHAR(100) NOT NULL,
    branch VARCHAR(100),
    daily_limit DECIMAL(15,2),
    is_default BOOLEAN DEFAULT FALSE,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建充值订单表
CREATE TABLE IF NOT EXISTS deposits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    actual_amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_account VARCHAR(100),
    receiving_card_id INT,
    transaction_id VARCHAR(100),
    status ENUM('待支付', '已支付', '已取消', '已完成') DEFAULT '待支付',
    remark VARCHAR(255),
    payment_time DATETIME,
    completion_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiving_card_id) REFERENCES receiving_bank_cards(id) ON DELETE SET NULL
);

-- 创建提现记录表
CREATE TABLE IF NOT EXISTS withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    actual_amount DECIMAL(15,2) NOT NULL,
    bank_card_id INT NOT NULL,
    status ENUM('待审核', '审核通过', '已拒绝', '已完成') DEFAULT '待审核',
    remark VARCHAR(255),
    approval_time DATETIME,
    completion_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_card_id) REFERENCES user_bank_cards(id) ON DELETE CASCADE
);

-- 创建系统参数表
CREATE TABLE IF NOT EXISTS system_params (
    id INT AUTO_INCREMENT PRIMARY KEY,
    param_key VARCHAR(100) NOT NULL UNIQUE,
    param_value TEXT,
    param_type VARCHAR(50) NOT NULL DEFAULT 'text',
    description VARCHAR(255),
    group_name VARCHAR(50) NOT NULL DEFAULT 'basic',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建幸运转盘表
CREATE TABLE IF NOT EXISTS lucky_wheel (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    prize_type ENUM('现金', '积分', '优惠券', '实物', '谢谢参与') NOT NULL,
    prize_value DECIMAL(15,2) DEFAULT 0.00,
    probability DECIMAL(5,2) NOT NULL,
    color VARCHAR(20),
    status BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建用户级别表
CREATE TABLE IF NOT EXISTS user_levels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    level INT NOT NULL UNIQUE,
    upgrade_users INT DEFAULT 0,
    upgrade_amount DECIMAL(15,2) DEFAULT 0.00,
    return_rate DECIMAL(5,2) DEFAULT 0.00,
    upgrade_bonus DECIMAL(15,2) DEFAULT 0.00,
    image_url VARCHAR(255),
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建支付通道表
CREATE TABLE IF NOT EXISTS payment_channels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    country_code VARCHAR(10) DEFAULT 'CN',
    deposit_enabled BOOLEAN DEFAULT TRUE,
    withdraw_enabled BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    weight INT DEFAULT 0,
    balance DECIMAL(15,2) DEFAULT 0.00,
    config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建邀请奖励表
CREATE TABLE IF NOT EXISTS invite_rewards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    deposit_users INT DEFAULT 0,
    register_users INT DEFAULT 0,
    bonus DECIMAL(15,2) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建客服管理表
CREATE TABLE IF NOT EXISTS customer_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(100) NOT NULL,
    url VARCHAR(255) NOT NULL,
    status BOOLEAN DEFAULT TRUE,
    icon VARCHAR(100),
    weight INT DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建轮播图表
CREATE TABLE IF NOT EXISTS banners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100),
    image_url VARCHAR(255) NOT NULL,
    url VARCHAR(255),
    position VARCHAR(50) DEFAULT 'home',
    sort_order INT DEFAULT 0,
    status BOOLEAN DEFAULT TRUE,
    start_time DATETIME,
    end_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建每日抽奖表
CREATE TABLE IF NOT EXISTS daily_lottery (
    id INT AUTO_INCREMENT PRIMARY KEY,
    day INT NOT NULL,
    category VARCHAR(50) NOT NULL,
    project_name VARCHAR(100),
    prize_name VARCHAR(100) NOT NULL,
    prize_amount DECIMAL(15,2) DEFAULT 0.00,
    prize_type ENUM('现金', '积分', '优惠券', '实物') NOT NULL,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建兑换码表
CREATE TABLE IF NOT EXISTS redeem_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    quantity INT DEFAULT 1,
    used_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建用户兑换记录表
CREATE TABLE IF NOT EXISTS redeem_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    code_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    status ENUM('成功', '失败') DEFAULT '成功',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (code_id) REFERENCES redeem_codes(id) ON DELETE CASCADE
);

-- 创建幸运转盘记录表
CREATE TABLE IF NOT EXISTS lucky_wheel_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    prize_id INT NOT NULL,
    prize_name VARCHAR(100) NOT NULL,
    prize_type ENUM('现金', '积分', '优惠券', '实物', '谢谢参与') NOT NULL,
    prize_value DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (prize_id) REFERENCES lucky_wheel(id) ON DELETE CASCADE
);

-- 创建每日抽奖记录表
CREATE TABLE IF NOT EXISTS daily_lottery_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    lottery_id INT NOT NULL,
    prize_name VARCHAR(100) NOT NULL,
    prize_amount DECIMAL(15,2) DEFAULT 0.00,
    prize_type ENUM('现金', '积分', '优惠券', '实物') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lottery_id) REFERENCES daily_lottery(id) ON DELETE CASCADE
);

-- 创建附件表
CREATE TABLE IF NOT EXISTS attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(50) DEFAULT '未归类',
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    width INT,
    height INT,
    file_type VARCHAR(20) NOT NULL,
    frame_count INT,
    mime_type VARCHAR(100) NOT NULL,
    metadata TEXT,
    storage_engine VARCHAR(20) DEFAULT 'local',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建初始数据

-- 创建超级管理员账号
INSERT INTO admins (username, password, nickname, is_super, status) 
VALUES ('admin', '$2a$10$X7SIl.../hashed_password...', '超级管理员', TRUE, TRUE);

-- 创建初始角色
INSERT INTO roles (id, parent_id, name, status) VALUES 
(1, NULL, '超级管理员组', TRUE),
(2, NULL, '管理员组', TRUE);

-- 创建初始权限
-- 菜单权限
INSERT INTO permissions (name, code, type) VALUES
('首页', 'dashboard:view', 'menu'),
('会员列表', 'members:view', 'menu'),
('充值订单', 'deposits:view', 'menu'),
('用户投资', 'investments:view', 'menu'),
('取款记录', 'withdrawals:view', 'menu'),
('用户流水', 'transactions:view', 'menu'),
('佣金记录', 'commissions:view', 'menu'),
('用户银行卡', 'user-cards:view', 'menu'),
('收款银行卡', 'receiving-cards:view', 'menu'),
('代理管理', 'agents:view', 'menu'),
('系统设置', 'settings:view', 'menu'),
('通知消息', 'notifications:view', 'menu'),
('管理员设置', 'admins:view', 'menu'),
('角色管理', 'roles:view', 'menu');

-- 操作权限
INSERT INTO permissions (name, code, type) VALUES
('添加会员', 'members:add', 'operation'),
('编辑会员', 'members:edit', 'operation'),
('删除会员', 'members:delete', 'operation'),
('赠金操作', 'members:gift', 'operation'),
('赠投操作', 'members:invest', 'operation'),
('查看下级', 'members:subordinates', 'operation'),

('添加项目', 'projects:add', 'operation'),
('编辑项目', 'projects:edit', 'operation'),
('删除项目', 'projects:delete', 'operation'),

('审核充值', 'deposits:approve', 'operation'),
('拒绝充值', 'deposits:reject', 'operation'),

('审核提现', 'withdrawals:approve', 'operation'),
('拒绝提现', 'withdrawals:reject', 'operation'),

('添加管理员', 'admins:add', 'operation'),
('编辑管理员', 'admins:edit', 'operation'),
('删除管理员', 'admins:delete', 'operation'),

('编辑角色', 'roles:edit', 'operation');

-- 创建超级管理员角色关联
INSERT INTO admin_roles (admin_id, role_id) VALUES (1, 1);

-- 为超级管理员角色分配所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;
