import request from '@/utils/request'

// 获取所有VIP等级
export function getUserLevels() {
  return request({
    url: '/user-levels',
    method: 'get'
  })
}

// 获取VIP等级详情
export function getUserLevel(id) {
  return request({
    url: `/user-levels/${id}`,
    method: 'get'
  })
}

// 创建VIP等级
export function createUserLevel(data) {
  return request({
    url: '/user-levels',
    method: 'post',
    data
  })
}

// 更新VIP等级
export function updateUserLevel(id, data) {
  return request({
    url: `/user-levels/${id}`,
    method: 'put',
    data
  })
}

// 删除VIP等级
export function deleteUserLevel(id) {
  return request({
    url: `/user-levels/${id}`,
    method: 'delete'
  })
}
