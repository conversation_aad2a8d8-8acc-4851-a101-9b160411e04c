const sequelize = require('../config/database');

// 直接执行迁移
async function runMigration() {
  try {
    console.log('开始添加 payout_method 字段到 bank_channel_mappings 表...');
    
    // 检查表是否存在
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'bank_channel_mappings'");
    if (tables.length === 0) {
      console.log('bank_channel_mappings 表不存在，跳过迁移');
      return;
    }

    // 检查字段是否已存在
    const [columns] = await sequelize.query("SHOW COLUMNS FROM bank_channel_mappings");
    const columnNames = columns.map(column => column.Field);

    if (!columnNames.includes('payout_method')) {
      // 添加 payout_method 字段
      await sequelize.query(`
        ALTER TABLE bank_channel_mappings 
        ADD COLUMN payout_method INT NULL COMMENT '代付方式编号' AFTER payin_method
      `);
      console.log('成功添加 payout_method 字段');
    } else {
      console.log('payout_method 字段已存在，跳过添加');
    }

    await sequelize.close();
    console.log('迁移完成');
  } catch (error) {
    console.error('迁移失败:', error);
    await sequelize.close();
    process.exit(1);
  }
}

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  runMigration();
}

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const [columns] = await queryInterface.sequelize.query("SHOW COLUMNS FROM bank_channel_mappings");
      const columnNames = columns.map(column => column.Field);

      if (!columnNames.includes('payout_method')) {
        await queryInterface.addColumn('bank_channel_mappings', 'payout_method', {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: '代付方式编号'
        });
      }
    } catch (error) {
      console.error('添加字段失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      await queryInterface.removeColumn('bank_channel_mappings', 'payout_method');
    } catch (error) {
      console.error('删除字段失败:', error);
      throw error;
    }
  }
};
