{"version": 3, "file": "use-global-config.mjs", "sources": ["../../../../../../../packages/components/config-provider/src/hooks/use-global-config.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, provide, ref, unref } from 'vue'\nimport { debugWarn, keysOf } from '@element-plus/utils'\nimport {\n  SIZE_INJECTION_KEY,\n  defaultInitialZIndex,\n  defaultNamespace,\n  emptyValuesContextKey,\n  localeContextKey,\n  namespaceContextKey,\n  useLocale,\n  useNamespace,\n  useZIndex,\n  zIndexContextKey,\n} from '@element-plus/hooks'\nimport { configProviderContextKey } from '../constants'\n\nimport type { MaybeRef } from '@vueuse/core'\nimport type { App, Ref } from 'vue'\nimport type { ConfigProviderContext } from '../constants'\n\n// this is meant to fix global methods like `ElMessage(opts)`, this way we can inject current locale\n// into the component as default injection value.\n// refer to: https://github.com/element-plus/element-plus/issues/2610#issuecomment-887965266\nconst globalConfig = ref<ConfigProviderContext>()\n\nexport function useGlobalConfig<\n  K extends keyof ConfigProviderContext,\n  D extends ConfigProviderContext[K]\n>(\n  key: K,\n  defaultValue?: D\n): Ref<Exclude<ConfigProviderContext[K], undefined> | D>\nexport function useGlobalConfig(): Ref<ConfigProviderContext>\nexport function useGlobalConfig(\n  key?: keyof ConfigProviderContext,\n  defaultValue = undefined\n) {\n  const config = getCurrentInstance()\n    ? inject(configProviderContextKey, globalConfig)\n    : globalConfig\n  if (key) {\n    return computed(() => config.value?.[key] ?? defaultValue)\n  } else {\n    return config\n  }\n}\n\n// for components like `ElMessage` `ElNotification` `ElMessageBox`.\nexport function useGlobalComponentSettings(\n  block: string,\n  sizeFallback?: MaybeRef<ConfigProviderContext['size']>\n) {\n  const config = useGlobalConfig()\n\n  const ns = useNamespace(\n    block,\n    computed(() => config.value?.namespace || defaultNamespace)\n  )\n\n  const locale = useLocale(computed(() => config.value?.locale))\n  const zIndex = useZIndex(\n    computed(() => config.value?.zIndex || defaultInitialZIndex)\n  )\n  const size = computed(() => unref(sizeFallback) || config.value?.size || '')\n  provideGlobalConfig(computed(() => unref(config) || {}))\n\n  return {\n    ns,\n    locale,\n    zIndex,\n    size,\n  }\n}\n\nexport const provideGlobalConfig = (\n  config: MaybeRef<ConfigProviderContext>,\n  app?: App,\n  global = false\n) => {\n  const inSetup = !!getCurrentInstance()\n  const oldConfig = inSetup ? useGlobalConfig() : undefined\n\n  const provideFn = app?.provide ?? (inSetup ? provide : undefined)\n  if (!provideFn) {\n    debugWarn(\n      'provideGlobalConfig',\n      'provideGlobalConfig() can only be used inside setup().'\n    )\n    return\n  }\n\n  const context = computed(() => {\n    const cfg = unref(config)\n    if (!oldConfig?.value) return cfg\n    return mergeConfig(oldConfig.value, cfg)\n  })\n  provideFn(configProviderContextKey, context)\n  provideFn(\n    localeContextKey,\n    computed(() => context.value.locale)\n  )\n  provideFn(\n    namespaceContextKey,\n    computed(() => context.value.namespace)\n  )\n  provideFn(\n    zIndexContextKey,\n    computed(() => context.value.zIndex)\n  )\n\n  provideFn(SIZE_INJECTION_KEY, {\n    size: computed(() => context.value.size || ''),\n  })\n\n  provideFn(\n    emptyValuesContextKey,\n    computed(() => ({\n      emptyValues: context.value.emptyValues,\n      valueOnClear: context.value.valueOnClear,\n    }))\n  )\n\n  if (global || !globalConfig.value) {\n    globalConfig.value = context.value\n  }\n  return context\n}\n\nconst mergeConfig = (\n  a: ConfigProviderContext,\n  b: ConfigProviderContext\n): ConfigProviderContext => {\n  const keys = [...new Set([...keysOf(a), ...keysOf(b)])]\n  const obj: Record<string, any> = {}\n  for (const key of keys) {\n    obj[key] = b[key] !== undefined ? b[key] : a[key]\n  }\n  return obj\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAeA,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC;AACpB,SAAS,eAAe,CAAC,GAAG,EAAE,YAAY,GAAG,KAAK,CAAC,EAAE;AAC5D,EAAE,MAAM,MAAM,GAAG,kBAAkB,EAAE,GAAG,MAAM,CAAC,wBAAwB,EAAE,YAAY,CAAC,GAAG,YAAY,CAAC;AACtG,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,OAAO,QAAQ,CAAC,MAAM;AAC1B,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,YAAY,CAAC;AAC/F,KAAK,CAAC,CAAC;AACP,GAAG,MAAM;AACT,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,CAAC;AACM,SAAS,0BAA0B,CAAC,KAAK,EAAE,YAAY,EAAE;AAChE,EAAE,MAAM,MAAM,GAAG,eAAe,EAAE,CAAC;AACnC,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM;AAChD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,gBAAgB,CAAC;AACrF,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM;AAC1C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;AAC5D,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM;AAC1C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,oBAAoB,CAAC;AACtF,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM;AAC9B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACzF,GAAG,CAAC,CAAC;AACL,EAAE,mBAAmB,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC3D,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,IAAI;AACR,GAAG,CAAC;AACJ,CAAC;AACW,MAAC,mBAAmB,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK;AACpE,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,OAAO,GAAG,CAAC,CAAC,kBAAkB,EAAE,CAAC;AACzC,EAAE,MAAM,SAAS,GAAG,OAAO,GAAG,eAAe,EAAE,GAAG,KAAK,CAAC,CAAC;AACzD,EAAE,MAAM,SAAS,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,OAAO,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AACxG,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,SAAS,CAAC,qBAAqB,EAAE,wDAAwD,CAAC,CAAC;AAC/F,IAAI,OAAO;AACX,GAAG;AACH,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM;AACjC,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,IAAI,EAAE,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;AACvD,MAAM,OAAO,GAAG,CAAC;AACjB,IAAI,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;AAC/C,EAAE,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,EAAE,SAAS,CAAC,mBAAmB,EAAE,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1E,EAAE,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,EAAE,SAAS,CAAC,kBAAkB,EAAE;AAChC,IAAI,IAAI,EAAE,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,qBAAqB,EAAE,QAAQ,CAAC,OAAO;AACnD,IAAI,WAAW,EAAE,OAAO,CAAC,KAAK,CAAC,WAAW;AAC1C,IAAI,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY;AAC5C,GAAG,CAAC,CAAC,CAAC,CAAC;AACP,EAAE,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACrC,IAAI,YAAY,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AACvC,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF,MAAM,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;AAC9B,EAAE,MAAM,IAAI,GAAG,CAAC,mBAAmB,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;AACjB,EAAE,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;AAC1B,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACnD,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;;;;"}