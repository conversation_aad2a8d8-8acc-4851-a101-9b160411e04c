# lodash.isarguments v3.1.0

The [lodash](https://lodash.com/) method `_.isArguments` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.isarguments
```

In Node.js:
```js
var isArguments = require('lodash.isarguments');
```

See the [documentation](https://lodash.com/docs#isArguments) or [package source](https://github.com/lodash/lodash/blob/3.1.0-npm-packages/lodash.isarguments) for more details.
