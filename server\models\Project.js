const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Project = sequelize.define('Project', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '项目类型',
  },
  category: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '项目分类',
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '项目名称',
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '项目描述',
  },
  image_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '图片ID',
  },
  video_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '视频ID',
  },
  price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '项目价格',
  },
  sell_price: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '卖出价格',
  },
  price_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    defaultValue: '固定价格',
    comment: '价格类型',
  },
  payment_method: {
    type: DataTypes.STRING(50),
    allowNull: true,
    defaultValue: '余额支付',
    comment: '支付方式',
  },
  purchase_time: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: '00:00:00-23:59:59',
    comment: '购买时间范围',
  },
  min_investment: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '最小投资金额',
  },
  max_investment: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '最大投资金额',
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '投资期限',
  },
  duration_unit: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: '天',
    comment: '期限单位',
  },
  expected_return: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    comment: '预期收益率(%)',
  },
  profit_time: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '收益发放时间(小时)',
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总数量',
  },
  actual_quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '实际数量',
  },
  sold_quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '已售数量',
  },
  sort_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 100,
    comment: '排序顺序',
  },
  max_purchase_times: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '最多购买次数',
  },
  simultaneous_purchases: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '同时购买数量',
  },
  max_profit_times: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '最多收益次数',
  },
  vip_level_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'VIP级别ID',
  },
  vip_type_operator: {
    type: DataTypes.STRING(10),
    allowNull: true,
    defaultValue: '=',
    comment: 'VIP类型操作符',
  },
  currency: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: 'CNY',
    comment: '货币类型',
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '状态：true=启用, false=禁用',
  },
  sell_status: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '出售状态：0=待售, 1=在售, 2=售完',
  },
  weekly_profit_days: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: '1,2,3,4,5,6,7',
    comment: '每周收益天数，例如：1,2,3,4,5,6,7 表示周一至周日',
  },
  return_principal: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否到期退本',
  },
  is_free: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否免费项目',
  },
  custom_return_enabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否启用自定义收益率',
  },
  custom_return_hours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '自定义收益结算时间(小时)',
  },
  custom_return_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: '自定义收益率(%)',
  },
  commission_enabled: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否启用佣金',
  },
  level1_commission: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '一级佣金比例(%)',
  },
  level2_commission: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '二级佣金比例(%)',
  },
  level3_commission: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '三级佣金比例(%)',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'projects',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Project.associate = (models) => {
  // 项目与图片
  Project.belongsTo(models.Attachment, {
    foreignKey: 'image_id',
    as: 'image',
  });

  // 项目与视频
  Project.belongsTo(models.Attachment, {
    foreignKey: 'video_id',
    as: 'video',
  });

  // 项目与VIP级别
  Project.belongsTo(models.UserLevel, {
    foreignKey: 'vip_level_id',
    as: 'vip_level',
  });

  // 项目与投资记录
  Project.hasMany(models.Investment, {
    foreignKey: 'project_id',
    as: 'investments',
  });
};

module.exports = Project;
