const sequelize = require('../config/database');

async function checkTable() {
  try {
    // 检查表结构
    const [columns] = await sequelize.query('SHOW COLUMNS FROM account_balances');
    console.log('account_balances 表的列:');
    console.log(columns);

    // 检查索引
    const [indexes] = await sequelize.query('SHOW INDEX FROM account_balances');
    console.log('\naccount_balances 表的索引:');
    console.log(indexes);

    // 关闭连接
    await sequelize.close();
  } catch (error) {
    console.error('检查表结构时出错:', error);
    process.exit(1);
  }
}

checkTable();
