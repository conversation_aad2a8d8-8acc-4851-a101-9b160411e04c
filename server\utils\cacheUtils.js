/**
 * 缓存工具类
 * 用于缓存频繁访问但很少变化的数据
 */

// 缓存存储
const cacheStore = new Map();

// 缓存过期时间（毫秒）
const DEFAULT_TTL = 5 * 60 * 1000; // 5分钟

/**
 * 获取缓存数据
 * @param {string} key - 缓存键
 * @returns {any} - 缓存数据，如果不存在或已过期则返回null
 */
function get(key) {
  if (!cacheStore.has(key)) {
    return null;
  }

  const cacheItem = cacheStore.get(key);
  
  // 检查是否过期
  if (cacheItem.expireAt && cacheItem.expireAt < Date.now()) {
    cacheStore.delete(key);
    return null;
  }
  
  return cacheItem.value;
}

/**
 * 设置缓存数据
 * @param {string} key - 缓存键
 * @param {any} value - 缓存数据
 * @param {number} ttl - 过期时间（毫秒），默认为5分钟
 */
function set(key, value, ttl = DEFAULT_TTL) {
  const expireAt = ttl > 0 ? Date.now() + ttl : null;
  
  cacheStore.set(key, {
    value,
    expireAt
  });
}

/**
 * 删除缓存数据
 * @param {string} key - 缓存键
 */
function del(key) {
  cacheStore.delete(key);
}

/**
 * 清空所有缓存
 */
function clear() {
  cacheStore.clear();
}

/**
 * 获取或设置缓存数据
 * 如果缓存不存在或已过期，则调用回调函数获取数据并缓存
 * @param {string} key - 缓存键
 * @param {Function} callback - 回调函数，用于获取数据
 * @param {number} ttl - 过期时间（毫秒），默认为5分钟
 * @returns {Promise<any>} - 缓存数据
 */
async function getOrSet(key, callback, ttl = DEFAULT_TTL) {
  // 尝试从缓存获取
  const cachedValue = get(key);
  if (cachedValue !== null) {
    return cachedValue;
  }
  
  // 缓存不存在或已过期，调用回调函数获取数据
  const value = await callback();
  
  // 缓存数据
  set(key, value, ttl);
  
  return value;
}

/**
 * 获取缓存统计信息
 * @returns {Object} - 缓存统计信息
 */
function getStats() {
  return {
    size: cacheStore.size,
    keys: Array.from(cacheStore.keys())
  };
}

module.exports = {
  get,
  set,
  del,
  clear,
  getOrSet,
  getStats
};
