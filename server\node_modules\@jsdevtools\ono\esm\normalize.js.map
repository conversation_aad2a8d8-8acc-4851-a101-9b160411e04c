{"version": 3, "file": "normalize.js", "sourceRoot": "", "sources": ["../src/normalize.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAAoB;IACnD,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;IACxB,OAAO;QACL,cAAc,EAAE,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;QAC7F,MAAM,EAAE,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM;YAC3C,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;KACpE,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAwC,IAAe,EAAE,OAAmB;IACvG,IAAI,aAA4B,CAAC;IACjC,IAAI,KAAoB,CAAC;IACzB,IAAI,UAAqB,CAAC;IAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,oDAAoD;IACpD,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAC/B,UAAU,GAAG,IAAI,CAAC;KACnB;SACI,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,KAAK,EAAE;YAC5B,aAAa,GAAG,IAAI,CAAC,CAAC,CAAM,CAAC;SAC9B;aACI;YACH,KAAK,GAAG,IAAI,CAAC,CAAC,CAAM,CAAC;SACtB;QACD,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC5B;SACI;QACH,aAAa,GAAG,IAAI,CAAC,CAAC,CAAM,CAAC;QAC7B,KAAK,GAAG,IAAI,CAAC,CAAC,CAAM,CAAC;QACrB,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC5B;IAED,mEAAmE;IACnE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;SACvD;aACI;YACH,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAChC;KACF;IAED,IAAI,OAAO,CAAC,cAAc,IAAI,aAAa,IAAI,aAAa,CAAC,OAAO,EAAE;QACpE,6DAA6D;QAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC;KAC3D;IAED,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;AAC3C,CAAC"}