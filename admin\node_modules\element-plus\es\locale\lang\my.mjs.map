{"version": 3, "file": "my.mjs", "sources": ["../../../../../packages/locale/lang/my.ts"], "sourcesContent": ["export default {\n  name: 'my',\n  el: {\n    breadcrumb: {\n      label: 'ဘရတ်ဒ်ခရမ်',\n    },\n    colorpicker: {\n      confirm: 'အိုကေ',\n      clear: 'ရှင်းမယ်',\n      defaultLabel: 'အရောင်ရွေးချယ်ပါ',\n      description:\n        'လက်ရှိအရောင်မှာ {color} ဖြစ်ပါသည်။ တခြားအရောင်ကိုရွေးချယ်လိုပါက enter ကိုနှိပ်ပါ။',\n      alphaLabel: 'alpha တန်ဖိုးကို ရွေးချယ်ပါ',\n    },\n    datepicker: {\n      now: 'ယခု',\n      today: 'ယနေ့',\n      cancel: 'ပယ်ဖျက်',\n      clear: 'ရှင်းမယ်',\n      confirm: 'အိုကေ',\n      dateTablePrompt:\n        'arrow keys နှင့် enter ကိုအသုံးပြုပြီး နေ့ရက် ကိုရွေးချယ်ပါ',\n      monthTablePrompt:\n        'arrow keys နှင့် enter ကိုအသုံးပြုပြီး လ ကိုရွေးချယ်ပါ',\n      yearTablePrompt:\n        'arrow keys နှင့် enter ကိုအသုံးပြုပြီး နှစ် ကိုရွေးချယ်ပါ',\n      selectedDate: 'ရွေးချယ်ထားသော ရက်စွဲ',\n      selectDate: 'ရက်စွဲကို ရွေးချယ်ပါ',\n      selectTime: 'အချိန်ကို ရွေးချယ်ပါ',\n      startDate: 'စတင်မည့်ရက်စွဲ',\n      startTime: 'စတင်မည့်အချိန်',\n      endDate: 'ကုန်ဆုံးမည့်ရက်စွဲ',\n      endTime: 'ကုန်ဆုံးမည့်အချိန်',\n      prevYear: 'ယခင်နှစ်',\n      nextYear: 'နောက်နှစ်',\n      prevMonth: 'ယခင်လ',\n      nextMonth: 'နောက်လ',\n      year: '',\n      month1: 'ဇန်နဝါရီ',\n      month2: 'ဖေဖော်ဝါရီ',\n      month3: 'မတ်',\n      month4: 'ဧပြီ',\n      month5: 'မေ',\n      month6: 'ဇွန်',\n      month7: 'ဇူလိုင်',\n      month8: 'သြဂုတ်',\n      month9: 'စက်တင်ဘာ',\n      month10: 'အောက်တိုဘာ',\n      month11: 'နိုဝင်ဘာ',\n      month12: 'ဒီဇင်ဘာ',\n      week: 'ရက်သတ္တပတ်',\n      weeks: {\n        sun: 'နွေ',\n        mon: 'လာ',\n        tue: 'ဂါ',\n        wed: 'ဟူး',\n        thu: 'ကြာ',\n        fri: 'သော',\n        sat: 'နေ',\n      },\n      weeksFull: {\n        sun: 'တနင်္ဂနွေ',\n        mon: 'တနင်္လာ',\n        tue: 'အင်္ဂါ',\n        wed: 'ဗုဒ္ဓဟူး',\n        thu: 'ကြာသပတေး',\n        fri: 'သောကြာ',\n        sat: 'စနေ',\n      },\n      months: {\n        jan: 'ဇန်',\n        feb: 'ဖေ',\n        mar: 'မတ်',\n        apr: 'ပြီ',\n        may: 'မေ',\n        jun: 'ဇွန်',\n        jul: 'လိုင်',\n        aug: 'ဩ',\n        sep: 'စက်',\n        oct: 'အောက်',\n        nov: 'နို',\n        dec: 'ဒီ',\n      },\n    },\n    inputNumber: {\n      decrease: 'အရေအတွက်လျှော့ချ',\n      increase: 'အရေအတွက်တိုး',\n    },\n    select: {\n      loading: 'ဝန်တင်နေသည်',\n      noMatch: 'ကိုက်ညီသောဒေတာမရှိပါ',\n      noData: 'ဒေတာမရှိပါ',\n      placeholder: 'ရွေးပါ',\n    },\n    mention: {\n      loading: 'ဝန်တင်နေသည်',\n    },\n    dropdown: {\n      toggleDropdown: 'Dropdown စာရင်း ဖွင့်/ပိတ်',\n    },\n    cascader: {\n      noMatch: 'ကိုက်ညီသောဒေတာမရှိပါ',\n      loading: 'ဝန်တင်နေသည်',\n      placeholder: 'ရွေးပါ',\n      noData: 'ဒေတာမရှိပါ',\n    },\n    pagination: {\n      goto: 'သွားမယ်',\n      pagesize: '/စာမျက်နှာ',\n      total: 'စုစုပေါင်း {total}',\n      pageClassifier: '',\n      page: 'စာမျက်နှာ',\n      prev: 'ရှေ့စာမျက်နှာသို့',\n      next: 'နောက်စာမျက်နှာသို့',\n      currentPage: 'စာမျက်နှာ {pager}',\n      prevPages: 'ရှေ့စာမျက်နှာ {pager} သို့',\n      nextPages: 'နောက်စာမျက်နှာ {pager} သို့',\n      deprecationWarning:\n        'ကန့်ကွက်ထားသော အသုံးပြုမှုများကို တွေ့ရှိပါသည်။ အသေးစိတ်အချက်အလက်များကို el-pagination ရဲ့စာရွက်စာတမ်းတွင် ဖတ်ရှုပါ။',\n    },\n    dialog: {\n      close: 'ဤဒိုင်ယာလော့ဂ်ကို ပိတ်ပါ',\n    },\n    drawer: {\n      close: 'ဤဒိုင်ယာလော့ဂ်ကို ပိတ်ပါ',\n    },\n    messagebox: {\n      title: 'မက်ဆေ့ချ်',\n      confirm: 'အိုကေ',\n      cancel: 'ပယ်ဖျက်',\n      error: 'တရားမဝင်ထည့်သွင်းမှု',\n      close: 'ဤဒိုင်ယာလော့ဂ်ကို ပိတ်ပါ',\n    },\n    upload: {\n      deleteTip: 'ဖယ်ရှားရန် ဖျက်မည် ကိုနှိပ်ပါ',\n      delete: 'ဖျက်မည်',\n      preview: 'အစမ်းကြည့်မည်',\n      continue: 'ရှေ့ဆက်မည်',\n    },\n    slider: {\n      defaultLabel: '{min} နှင့် {max} ကြားရှိ ဆလိုက်ဒါ',\n      defaultRangeStartLabel: 'စတင်တန်ဖိုးကို ရွေးပါ',\n      defaultRangeEndLabel: 'အဆုံးသတ်တန်ဖိုးကို ရွေးပါ',\n    },\n    table: {\n      emptyText: 'ဒေတာမရှိပါ',\n      confirmFilter: 'အတည်ပြုမည်',\n      resetFilter: 'ပြန်လည်သတ်မှတ်မည်',\n      clearFilter: 'အားလုံး',\n      sumText: 'ပေါင်းလဒ်',\n    },\n    tour: {\n      next: 'နောက်သို့',\n      previous: 'ရှေ့သို့',\n      finish: 'ပြီးပြီ',\n    },\n    tree: {\n      emptyText: 'ဒေတာမရှိပါ',\n    },\n    transfer: {\n      noMatch: 'ကိုက်ညီသောဒေတာမရှိပါ',\n      noData: 'ဒေတာမရှိပါ',\n      titles: ['စာရင်း ၁', 'စာရင်း ၂'],\n      filterPlaceholder: 'သော့ချက်စကားလုံးကို ရိုက်ထည့်ပါ',\n      noCheckedFormat: '{total} ခု',\n      hasCheckedFormat: '{checked}/{total} ရွေးပြီး',\n    },\n    image: {\n      error: 'မအောင်မြင်ပါ',\n    },\n    pageHeader: {\n      title: 'ပြန်မည်',\n    },\n    popconfirm: {\n      confirmButtonText: 'ဟုတ်ကဲ့',\n      cancelButtonText: 'မလုပ်တော့ဘူး',\n    },\n    carousel: {\n      leftArrow: 'ကာရူဆယ် မြား ဘယ်ဘက်',\n      rightArrow: 'ကာရူဆယ် မြား ညာဘက်',\n      indicator: 'ကာရူဆယ် အညွှန်း {index} သို့ ပြောင်းရန်',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,8DAA8D;AAC3E,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,YAAY,EAAE,kGAAkG;AACtH,MAAM,WAAW,EAAE,mZAAmZ;AACta,MAAM,UAAU,EAAE,iIAAiI;AACnJ,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,oBAAoB;AAC/B,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,eAAe,EAAE,gQAAgQ;AACvR,MAAM,gBAAgB,EAAE,kOAAkO;AAC1P,MAAM,eAAe,EAAE,oPAAoP;AAC3Q,MAAM,YAAY,EAAE,2HAA2H;AAC/I,MAAM,UAAU,EAAE,qHAAqH;AACvI,MAAM,UAAU,EAAE,qHAAqH;AACvI,MAAM,SAAS,EAAE,sFAAsF;AACvG,MAAM,SAAS,EAAE,sFAAsF;AACvG,MAAM,OAAO,EAAE,8GAA8G;AAC7H,MAAM,OAAO,EAAE,8GAA8G;AAC7H,MAAM,QAAQ,EAAE,kDAAkD;AAClE,MAAM,QAAQ,EAAE,wDAAwD;AACxE,MAAM,SAAS,EAAE,gCAAgC;AACjD,MAAM,SAAS,EAAE,sCAAsC;AACvD,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,IAAI,EAAE,8DAA8D;AAC1E,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,cAAc;AAC3B,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,wDAAwD;AACrE,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,cAAc;AAC3B,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,kGAAkG;AAClH,MAAM,QAAQ,EAAE,0EAA0E;AAC1F,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,oEAAoE;AACnF,MAAM,OAAO,EAAE,0HAA0H;AACzI,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,WAAW,EAAE,sCAAsC;AACzD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,oEAAoE;AACnF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,uGAAuG;AAC7H,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0HAA0H;AACzI,MAAM,OAAO,EAAE,oEAAoE;AACnF,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,MAAM,EAAE,8DAA8D;AAC5E,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,QAAQ,EAAE,yDAAyD;AACzE,MAAM,KAAK,EAAE,sEAAsE;AACnF,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,wDAAwD;AACpE,MAAM,IAAI,EAAE,wGAAwG;AACpH,MAAM,IAAI,EAAE,8GAA8G;AAC1H,MAAM,WAAW,EAAE,gEAAgE;AACnF,MAAM,SAAS,EAAE,iHAAiH;AAClI,MAAM,SAAS,EAAE,uHAAuH;AACxI,MAAM,kBAAkB,EAAE,2lBAA2lB;AACrnB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,6IAA6I;AAC1J,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,6IAA6I;AAC1J,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,wDAAwD;AACrE,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,0HAA0H;AACvI,MAAM,KAAK,EAAE,6IAA6I;AAC1J,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,sKAAsK;AACvL,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,wIAAwI;AAC5J,MAAM,sBAAsB,EAAE,2HAA2H;AACzJ,MAAM,oBAAoB,EAAE,mJAAmJ;AAC/K,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,8DAA8D;AAC/E,MAAM,aAAa,EAAE,8DAA8D;AACnF,MAAM,WAAW,EAAE,wGAAwG;AAC3H,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,OAAO,EAAE,wDAAwD;AACvE,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,wDAAwD;AACpE,MAAM,QAAQ,EAAE,kDAAkD;AAClE,MAAM,MAAM,EAAE,4CAA4C;AAC1D,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,8DAA8D;AAC/E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0HAA0H;AACzI,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,MAAM,EAAE,CAAC,6CAA6C,EAAE,6CAA6C,CAAC;AAC5G,MAAM,iBAAiB,EAAE,uLAAuL;AAChN,MAAM,eAAe,EAAE,sBAAsB;AAC7C,MAAM,gBAAgB,EAAE,oEAAoE;AAC5F,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,0EAA0E;AACvF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,4CAA4C;AACrE,MAAM,gBAAgB,EAAE,0EAA0E;AAClG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,0GAA0G;AAC3H,MAAM,UAAU,EAAE,oGAAoG;AACtH,MAAM,SAAS,EAAE,qLAAqL;AACtM,KAAK;AACL,GAAG;AACH,CAAC;;;;"}