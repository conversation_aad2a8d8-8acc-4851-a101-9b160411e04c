const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  order_no: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  project_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'projects',
      key: 'id',
    },
  },
  amount: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
  },
  expected_return: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    comment: '预期收益',
  },
  actual_return: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: true,
    comment: '实际收益',
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid', 'completed', 'cancelled', 'refunded'),
    defaultValue: 'pending',
    allowNull: false,
  },
  payment_method: {
    type: DataTypes.ENUM('balance', 'alipay', 'wechat', 'bank'),
    allowNull: true,
  },
  payment_time: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  start_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '投资开始时间',
  },
  end_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '投资结束时间',
  },
}, {
  tableName: 'orders',
  timestamps: true,
});

module.exports = Order;
