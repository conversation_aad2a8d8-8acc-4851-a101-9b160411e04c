const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'MySQL3352~!',
  database: 'fox_db',
  multipleStatements: true // 允许执行多条SQL语句
};

// 读取SQL文件
const readSqlFile = (filePath) => {
  return fs.readFileSync(filePath, 'utf8');
};

// 执行SQL脚本
const runMigration = async () => {
  let connection;
  try {
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'migrations', 'add_user_id_field.sql');
    const sqlScript = readSqlFile(sqlFilePath);

    // 执行SQL脚本
    console.log('开始执行SQL脚本...');
    await connection.query(sqlScript);
    console.log('SQL脚本执行成功');

  } catch (error) {
    console.error('执行SQL脚本时出错:', error);
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
};

// 执行迁移
runMigration();
