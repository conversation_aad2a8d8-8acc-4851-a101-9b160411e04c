/**
 * 检查佣金计算逻辑
 */
const { User, Transaction, Investment, SystemParam } = require('./models');
const sequelize = require('./config/database');

async function checkCommissionLogic() {
  try {
    // 检查连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 查询用户信息
    const user = await User.findOne({
      where: { username: '80590948500' }
    });
    
    if (!user) {
      console.log('未找到用户: 80590948500');
      process.exit(1);
    }
    
    console.log('用户信息:');
    console.log(`ID: ${user.id}`);
    console.log(`用户名: ${user.username}`);
    console.log(`用户ID: ${user.user_id}`);
    console.log(`上级ID: ${user.inviter_id}`);
    console.log(`等级: ${user.level_id}`);
    console.log(`余额: ${user.balance}`);
    console.log('---');
    
    // 查询上级用户信息
    const inviter = await User.findByPk(user.inviter_id);
    
    if (inviter) {
      console.log('上级用户信息:');
      console.log(`ID: ${inviter.id}`);
      console.log(`用户名: ${inviter.username}`);
      console.log(`用户ID: ${inviter.user_id}`);
      console.log(`上级ID: ${inviter.inviter_id}`);
      console.log(`等级: ${inviter.level_id}`);
      console.log(`余额: ${inviter.balance}`);
      console.log('---');
      
      // 查询上上级用户信息
      if (inviter.inviter_id) {
        const grandInviter = await User.findByPk(inviter.inviter_id);
        
        if (grandInviter) {
          console.log('上上级用户信息:');
          console.log(`ID: ${grandInviter.id}`);
          console.log(`用户名: ${grandInviter.username}`);
          console.log(`用户ID: ${grandInviter.user_id}`);
          console.log(`上级ID: ${grandInviter.inviter_id}`);
          console.log(`等级: ${grandInviter.level_id}`);
          console.log(`余额: ${grandInviter.balance}`);
          console.log('---');
        }
      }
    }
    
    // 查询用户的投资记录
    const investments = await Investment.findAll({
      where: { user_id: user.id },
      order: [['created_at', 'DESC']],
      limit: 5
    });
    
    console.log('用户投资记录:');
    investments.forEach(investment => {
      console.log(`ID: ${investment.id}`);
      console.log(`项目ID: ${investment.project_id}`);
      console.log(`金额: ${investment.amount}`);
      console.log(`状态: ${investment.status}`);
      console.log(`创建时间: ${investment.created_at}`);
      console.log('---');
    });
    
    // 查询用户的收益记录
    const profits = await Transaction.findAll({
      where: { user_id: user.id, type: 'profit' },
      order: [['created_at', 'DESC']],
      limit: 5
    });
    
    console.log('用户收益记录:');
    profits.forEach(profit => {
      console.log(`ID: ${profit.id}`);
      console.log(`订单号: ${profit.order_number}`);
      console.log(`金额: ${profit.amount}`);
      console.log(`状态: ${profit.status}`);
      console.log(`参考ID: ${profit.reference_id}`);
      console.log(`参考类型: ${profit.reference_type}`);
      console.log(`描述: ${profit.description}`);
      console.log(`创建时间: ${profit.created_at}`);
      console.log('---');
    });
    
    // 查询上级用户的佣金记录
    if (inviter) {
      const commissions = await Transaction.findAll({
        where: { user_id: inviter.id, type: 'commission' },
        order: [['created_at', 'DESC']],
        limit: 5
      });
      
      console.log('上级用户佣金记录:');
      commissions.forEach(commission => {
        console.log(`ID: ${commission.id}`);
        console.log(`订单号: ${commission.order_number}`);
        console.log(`金额: ${commission.amount}`);
        console.log(`状态: ${commission.status}`);
        console.log(`参考ID: ${commission.reference_id}`);
        console.log(`参考类型: ${commission.reference_type}`);
        console.log(`描述: ${commission.description}`);
        console.log(`创建时间: ${commission.created_at}`);
        console.log('---');
      });
    }
    
    // 查询佣金配置参数
    const commissionParams = await SystemParam.findAll({
      where: {
        param_key: {
          [sequelize.Op.or]: [
            { [sequelize.Op.like]: '%commission%' },
            { [sequelize.Op.like]: '%profit%' }
          ]
        }
      }
    });
    
    console.log('佣金配置参数:');
    commissionParams.forEach(param => {
      console.log(`键: ${param.param_key}`);
      console.log(`值: ${param.param_value}`);
      console.log(`描述: ${param.description}`);
      console.log('---');
    });
    
    process.exit(0);
  } catch (error) {
    console.error('检查佣金逻辑失败:', error);
    process.exit(1);
  }
}

checkCommissionLogic();
