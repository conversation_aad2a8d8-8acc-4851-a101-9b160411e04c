require('dotenv').config();
const sequelize = require('../config/database');
const { User, InviteCode, UserRelation } = require('../models');

async function createInviteTables() {
  try {
    console.log('开始创建邀请码和用户关系表...');

    // 检查users表是否存在
    const [usersTableExists] = await sequelize.query(`
      SELECT COUNT(*) as count FROM information_schema.TABLES
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'users';
    `);

    // 如果users表不存在，创建它
    if (usersTableExists[0].count === 0) {
      await sequelize.query(`
        CREATE TABLE users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          name VARCHAR(50) NOT NULL,
          email VARCHAR(100) NULL UNIQUE,
          phone VARCHAR(20) NOT NULL UNIQUE,
          avatar VARCHAR(255) NULL,
          balance DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
          status ENUM('active', 'inactive', 'banned') NOT NULL DEFAULT 'active',
          inviter_id INT NULL,
          invite_code VARCHAR(20) NULL,
          last_login DATETIME NULL,
          created_at DATETIME NOT NULL,
          updated_at DATETIME NOT NULL,
          INDEX idx_username (username),
          INDEX idx_email (email),
          INDEX idx_phone (phone),
          INDEX idx_status (status)
        ) ENGINE=InnoDB;
      `);
      console.log('users表创建成功');
    } else {
      // 检查列是否存在
      const [inviterIdExists] = await sequelize.query(`
        SELECT COUNT(*) as count FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'inviter_id';
      `);

      const [inviteCodeExists] = await sequelize.query(`
        SELECT COUNT(*) as count FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'invite_code';
      `);

      // 添加缺失的列
      if (inviterIdExists[0].count === 0) {
        await sequelize.query(`
          ALTER TABLE users
          ADD COLUMN inviter_id INT NULL;
        `);
        console.log('添加 inviter_id 列成功');
      }

      if (inviteCodeExists[0].count === 0) {
        await sequelize.query(`
          ALTER TABLE users
          ADD COLUMN invite_code VARCHAR(20) NULL;
        `);
        console.log('添加 invite_code 列成功');
      }

      // 检查外键是否存在
      const [fkExists] = await sequelize.query(`
        SELECT COUNT(*) as count FROM information_schema.TABLE_CONSTRAINTS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND CONSTRAINT_NAME = 'fk_user_inviter';
      `);

      if (fkExists[0].count === 0) {
        await sequelize.query(`
          ALTER TABLE users
          ADD CONSTRAINT fk_user_inviter FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE SET NULL;
        `);
        console.log('添加外键约束成功');
      }
    }

    console.log('用户表配置成功');

    // 创建邀请码表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS invite_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(20) NOT NULL UNIQUE,
        user_id INT NOT NULL,
        used_count INT NOT NULL DEFAULT 0,
        max_uses INT NOT NULL DEFAULT 0,
        status BOOLEAN NOT NULL DEFAULT TRUE,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        CONSTRAINT fk_invite_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB;
    `);
    console.log('邀请码表创建成功');

    // 创建用户关系表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS user_relations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        parent_id INT NOT NULL,
        level INT NOT NULL,
        invite_code_id INT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        UNIQUE KEY uk_user_parent_level (user_id, parent_id, level),
        INDEX idx_user_id (user_id),
        INDEX idx_parent_id (parent_id),
        INDEX idx_level (level),
        CONSTRAINT fk_relation_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT fk_relation_parent FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT fk_relation_invite_code FOREIGN KEY (invite_code_id) REFERENCES invite_codes(id) ON DELETE SET NULL
      ) ENGINE=InnoDB;
    `);
    console.log('用户关系表创建成功');

    console.log('邀请码和用户关系表创建完成');
    process.exit(0);
  } catch (error) {
    console.error('创建邀请码和用户关系表失败:', error);
    process.exit(1);
  }
}

createInviteTables();
