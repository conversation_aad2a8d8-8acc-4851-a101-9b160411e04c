/**
 * A specific location within a source file.
 *
 * This is always associated with a {@link SourceSpan} which indicates *which*
 * file it refers to.
 *
 * @category Logger
 */
export interface SourceLocation {
  /**
   * The 0-based index of this location within its source file, in terms of
   * UTF-16 code units.
   */
  offset: number;

  /** The 0-based line number of this location. */
  line: number;

  /** The 0-based column number of this location. */
  column: number;
}
