/**
 * 测试统计规则修改
 * 验证取款和充值统计是否按照新规则工作
 */
const { Deposit, Withdrawal, Transaction } = require('./models');
const statisticsService = require('./services/statisticsService');
const moment = require('moment');

async function testStatistics() {
  try {
    console.log('=== 测试统计规则修改 ===\n');

    // 获取今天的日期范围
    const today = moment().format('YYYY-MM-DD');
    const startDate = moment(today).startOf('day').toDate();
    const endDate = moment(today).endOf('day').toDate();

    console.log(`测试日期: ${today}`);
    console.log(`时间范围: ${startDate} 到 ${endDate}\n`);

    // 1. 检查充值订单状态
    console.log('=== 充值订单状态检查 ===');
    const allDeposits = await Deposit.findAll({
      attributes: ['id', 'user_id', 'amount', 'status', 'created_at', 'completion_time'],
      order: [['created_at', 'DESC']],
      limit: 5
    });

    console.log('最近5个充值订单:');
    allDeposits.forEach(deposit => {
      console.log(`ID: ${deposit.id}, 用户: ${deposit.user_id}, 金额: ${deposit.amount}, 状态: ${deposit.status}, 创建时间: ${deposit.created_at}, 完成时间: ${deposit.completion_time}`);
    });

    const completedDeposits = await Deposit.findAll({
      where: { status: 'completed' },
      attributes: ['id', 'user_id', 'amount', 'completion_time'],
      order: [['completion_time', 'DESC']],
      limit: 3
    });

    console.log('\n已完成的充值订单:');
    completedDeposits.forEach(deposit => {
      console.log(`ID: ${deposit.id}, 用户: ${deposit.user_id}, 金额: ${deposit.amount}, 完成时间: ${deposit.completion_time}`);
    });

    // 2. 检查取款订单状态
    console.log('\n=== 取款订单状态检查 ===');
    const allWithdrawals = await Withdrawal.findAll({
      attributes: ['id', 'user_id', 'amount', 'status', 'created_at', 'completion_time'],
      order: [['created_at', 'DESC']],
      limit: 5
    });

    console.log('最近5个取款订单:');
    allWithdrawals.forEach(withdrawal => {
      console.log(`ID: ${withdrawal.id}, 用户: ${withdrawal.user_id}, 金额: ${withdrawal.amount}, 状态: ${withdrawal.status}, 创建时间: ${withdrawal.created_at}, 完成时间: ${withdrawal.completion_time}`);
    });

    const completedWithdrawals = await Withdrawal.findAll({
      where: { status: 'completed' },
      attributes: ['id', 'user_id', 'amount', 'completion_time'],
      order: [['completion_time', 'DESC']],
      limit: 3
    });

    console.log('\n已完成的取款订单:');
    completedWithdrawals.forEach(withdrawal => {
      console.log(`ID: ${withdrawal.id}, 用户: ${withdrawal.user_id}, 金额: ${withdrawal.amount}, 完成时间: ${withdrawal.completion_time}`);
    });

    // 3. 测试新的统计逻辑
    console.log('\n=== 测试新统计逻辑 ===');
    
    // 今日统计
    const todayStats = await statisticsService.getTodayStatistics();
    console.log('今日统计数据:');
    console.log(`- 充值金额: ${todayStats.deposit_amount}`);
    console.log(`- 充值笔数: ${todayStats.deposit_count}`);
    console.log(`- 充值人数: ${todayStats.deposit_user_count}`);
    console.log(`- 取款金额: ${todayStats.withdrawal_amount}`);
    console.log(`- 取款笔数: ${todayStats.withdrawal_count}`);
    console.log(`- 取款人数: ${todayStats.withdrawal_user_count}`);
    console.log(`- 平台利润: ${todayStats.platform_profit}`);

    // 总统计
    const totalStats = await statisticsService.getTotalStatistics();
    console.log('\n总统计数据:');
    console.log(`- 总充值金额: ${totalStats.total_deposit_amount}`);
    console.log(`- 总充值笔数: ${totalStats.total_deposit_count}`);
    console.log(`- 总充值人数: ${totalStats.total_deposit_user_count}`);
    console.log(`- 总取款金额: ${totalStats.total_withdrawal_amount}`);
    console.log(`- 总取款笔数: ${totalStats.total_withdrawal_count}`);
    console.log(`- 总取款人数: ${totalStats.total_withdrawal_user_count}`);
    console.log(`- 总平台利润: ${totalStats.total_platform_profit}`);

    console.log('\n=== 测试完成 ===');

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testStatistics().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('测试异常:', error);
    process.exit(1);
  });
}

module.exports = { testStatistics };
