{"version": 3, "sources": ["../../../src/dialects/sqlite/query-generator.js"], "sourcesContent": ["'use strict';\n\nconst Utils = require('../../utils');\nconst Transaction = require('../../transaction');\nconst _ = require('lodash');\nconst MySqlQueryGenerator = require('../mysql/query-generator');\nconst AbstractQueryGenerator = require('../abstract/query-generator');\n\nclass SQLiteQueryGenerator extends MySqlQueryGenerator {\n  createSchema() {\n    return \"SELECT name FROM `sqlite_master` WHERE type='table' and name!='sqlite_sequence';\";\n  }\n\n  showSchemasQuery() {\n    return \"SELECT name FROM `sqlite_master` WHERE type='table' and name!='sqlite_sequence';\";\n  }\n\n  versionQuery() {\n    return 'SELECT sqlite_version() as `version`';\n  }\n\n  createTableQuery(tableName, attributes, options) {\n    options = options || {};\n\n    const primaryKeys = [];\n    const needsMultiplePrimaryKeys = Object.values(attributes).filter(definition => definition.includes('PRIMARY KEY')).length > 1;\n    const attrArray = [];\n\n    for (const attr in attributes) {\n      if (Object.prototype.hasOwnProperty.call(attributes, attr)) {\n        const dataType = attributes[attr];\n        const containsAutoIncrement = dataType.includes('AUTOINCREMENT');\n\n        let dataTypeString = dataType;\n        if (dataType.includes('PRIMARY KEY')) {\n          if (dataType.includes('INT')) {\n            // Only INTEGER is allowed for primary key, see https://github.com/sequelize/sequelize/issues/969 (no lenght, unsigned etc)\n            dataTypeString = containsAutoIncrement ? 'INTEGER PRIMARY KEY AUTOINCREMENT' : 'INTEGER PRIMARY KEY';\n\n            if (dataType.includes(' REFERENCES')) {\n              dataTypeString += dataType.substr(dataType.indexOf(' REFERENCES'));\n            }\n          }\n\n          if (needsMultiplePrimaryKeys) {\n            primaryKeys.push(attr);\n            if (dataType.includes('NOT NULL')) {\n              dataTypeString = dataType.replace(' PRIMARY KEY', '');\n            } else {\n              dataTypeString = dataType.replace('PRIMARY KEY', 'NOT NULL');\n            }\n          }\n        }\n        attrArray.push(`${this.quoteIdentifier(attr)} ${dataTypeString}`);\n      }\n    }\n\n    const table = this.quoteTable(tableName);\n    let attrStr = attrArray.join(', ');\n    const pkString = primaryKeys.map(pk => this.quoteIdentifier(pk)).join(', ');\n\n    if (options.uniqueKeys) {\n      _.each(options.uniqueKeys, columns => {\n        if (columns.customIndex) {\n          attrStr += `, UNIQUE (${columns.fields.map(field => this.quoteIdentifier(field)).join(', ')})`;\n        }\n      });\n    }\n\n    if (pkString.length > 0) {\n      attrStr += `, PRIMARY KEY (${pkString})`;\n    }\n\n    const sql = `CREATE TABLE IF NOT EXISTS ${table} (${attrStr});`;\n    return this.replaceBooleanDefaults(sql);\n  }\n\n  booleanValue(value) {\n    return value ? 1 : 0;\n  }\n\n  /**\n   * Check whether the statmement is json function or simple path\n   *\n   * @param   {string}  stmt  The statement to validate\n   * @returns {boolean}       true if the given statement is json function\n   * @throws  {Error}         throw if the statement looks like json function but has invalid token\n   */\n  _checkValidJsonStatement(stmt) {\n    if (typeof stmt !== 'string') {\n      return false;\n    }\n\n    // https://sqlite.org/json1.html\n    const jsonFunctionRegex = /^\\s*(json(?:_[a-z]+){0,2})\\([^)]*\\)/i;\n    const tokenCaptureRegex = /^\\s*((?:([`\"'])(?:(?!\\2).|\\2{2})*\\2)|[\\w\\d\\s]+|[().,;+-])/i;\n\n    let currentIndex = 0;\n    let openingBrackets = 0;\n    let closingBrackets = 0;\n    let hasJsonFunction = false;\n    let hasInvalidToken = false;\n\n    while (currentIndex < stmt.length) {\n      const string = stmt.substr(currentIndex);\n      const functionMatches = jsonFunctionRegex.exec(string);\n      if (functionMatches) {\n        currentIndex += functionMatches[0].indexOf('(');\n        hasJsonFunction = true;\n        continue;\n      }\n\n      const tokenMatches = tokenCaptureRegex.exec(string);\n      if (tokenMatches) {\n        const capturedToken = tokenMatches[1];\n        if (capturedToken === '(') {\n          openingBrackets++;\n        } else if (capturedToken === ')') {\n          closingBrackets++;\n        } else if (capturedToken === ';') {\n          hasInvalidToken = true;\n          break;\n        }\n        currentIndex += tokenMatches[0].length;\n        continue;\n      }\n\n      break;\n    }\n\n    // Check invalid json statement\n    hasInvalidToken |= openingBrackets !== closingBrackets;\n    if (hasJsonFunction && hasInvalidToken) {\n      throw new Error(`Invalid json statement: ${stmt}`);\n    }\n\n    // return true if the statement has valid json function\n    return hasJsonFunction;\n  }\n\n  //sqlite can't cast to datetime so we need to convert date values to their ISO strings\n  _toJSONValue(value) {\n    if (value instanceof Date) {\n      return value.toISOString();\n    }\n    if (Array.isArray(value) && value[0] instanceof Date) {\n      return value.map(val => val.toISOString());\n    }\n    return value;\n  }\n\n\n  handleSequelizeMethod(smth, tableName, factory, options, prepend) {\n    if (smth instanceof Utils.Json) {\n      return super.handleSequelizeMethod(smth, tableName, factory, options, prepend);\n    }\n\n    if (smth instanceof Utils.Cast) {\n      if (/timestamp/i.test(smth.type)) {\n        smth.type = 'datetime';\n      }\n    }\n\n    return AbstractQueryGenerator.prototype.handleSequelizeMethod.call(this, smth, tableName, factory, options, prepend);\n  }\n\n  addColumnQuery(table, key, dataType) {\n    const attributes = {};\n    attributes[key] = dataType;\n    const fields = this.attributesToSQL(attributes, { context: 'addColumn' });\n    const attribute = `${this.quoteIdentifier(key)} ${fields[key]}`;\n\n    const sql = `ALTER TABLE ${this.quoteTable(table)} ADD ${attribute};`;\n\n    return this.replaceBooleanDefaults(sql);\n  }\n\n  showTablesQuery() {\n    return 'SELECT name FROM `sqlite_master` WHERE type=\\'table\\' and name!=\\'sqlite_sequence\\';';\n  }\n\n  updateQuery(tableName, attrValueHash, where, options, attributes) {\n    options = options || {};\n    _.defaults(options, this.options);\n\n    attrValueHash = Utils.removeNullValuesFromHash(attrValueHash, options.omitNull, options);\n\n    const modelAttributeMap = {};\n    const values = [];\n    const bind = [];\n    const bindParam = options.bindParam || this.bindParam(bind);\n\n    if (attributes) {\n      _.each(attributes, (attribute, key) => {\n        modelAttributeMap[key] = attribute;\n        if (attribute.field) {\n          modelAttributeMap[attribute.field] = attribute;\n        }\n      });\n    }\n\n    for (const key in attrValueHash) {\n      const value = attrValueHash[key];\n\n      if (value instanceof Utils.SequelizeMethod || options.bindParam === false) {\n        values.push(`${this.quoteIdentifier(key)}=${this.escape(value, modelAttributeMap && modelAttributeMap[key] || undefined, { context: 'UPDATE' })}`);\n      } else {\n        values.push(`${this.quoteIdentifier(key)}=${this.format(value, modelAttributeMap && modelAttributeMap[key] || undefined, { context: 'UPDATE' }, bindParam)}`);\n      }\n    }\n\n    let query;\n    const whereOptions = { ...options, bindParam };\n\n    if (options.limit) {\n      query = `UPDATE ${this.quoteTable(tableName)} SET ${values.join(',')} WHERE rowid IN (SELECT rowid FROM ${this.quoteTable(tableName)} ${this.whereQuery(where, whereOptions)} LIMIT ${this.escape(options.limit)})`;\n    } else {\n      query = `UPDATE ${this.quoteTable(tableName)} SET ${values.join(',')} ${this.whereQuery(where, whereOptions)}`;\n    }\n\n    return { query, bind };\n  }\n\n  truncateTableQuery(tableName, options = {}) {\n    return [\n      `DELETE FROM ${this.quoteTable(tableName)}`,\n      options.restartIdentity ? `; DELETE FROM ${this.quoteTable('sqlite_sequence')} WHERE ${this.quoteIdentifier('name')} = ${Utils.addTicks(Utils.removeTicks(this.quoteTable(tableName), '`'), \"'\")};` : ''\n    ].join('');\n  }\n\n  deleteQuery(tableName, where, options = {}, model) {\n    _.defaults(options, this.options);\n\n    let whereClause = this.getWhereConditions(where, null, model, options);\n\n    if (whereClause) {\n      whereClause = `WHERE ${whereClause}`;\n    }\n\n    if (options.limit) {\n      whereClause = `WHERE rowid IN (SELECT rowid FROM ${this.quoteTable(tableName)} ${whereClause} LIMIT ${this.escape(options.limit)})`;\n    }\n\n    return `DELETE FROM ${this.quoteTable(tableName)} ${whereClause}`;\n  }\n\n  attributesToSQL(attributes) {\n    const result = {};\n    for (const name in attributes) {\n      const dataType = attributes[name];\n      const fieldName = dataType.field || name;\n\n      if (_.isObject(dataType)) {\n        let sql = dataType.type.toString();\n\n        if (Object.prototype.hasOwnProperty.call(dataType, 'allowNull') && !dataType.allowNull) {\n          sql += ' NOT NULL';\n        }\n\n        if (Utils.defaultValueSchemable(dataType.defaultValue)) {\n          // TODO thoroughly check that DataTypes.NOW will properly\n          // get populated on all databases as DEFAULT value\n          // i.e. mysql requires: DEFAULT CURRENT_TIMESTAMP\n          sql += ` DEFAULT ${this.escape(dataType.defaultValue, dataType)}`;\n        }\n\n        if (dataType.unique === true) {\n          sql += ' UNIQUE';\n        }\n\n        if (dataType.primaryKey) {\n          sql += ' PRIMARY KEY';\n\n          if (dataType.autoIncrement) {\n            sql += ' AUTOINCREMENT';\n          }\n        }\n\n        if (dataType.references) {\n          const referencesTable = this.quoteTable(dataType.references.model);\n\n          let referencesKey;\n          if (dataType.references.key) {\n            referencesKey = this.quoteIdentifier(dataType.references.key);\n          } else {\n            referencesKey = this.quoteIdentifier('id');\n          }\n\n          sql += ` REFERENCES ${referencesTable} (${referencesKey})`;\n\n          if (dataType.onDelete) {\n            sql += ` ON DELETE ${dataType.onDelete.toUpperCase()}`;\n          }\n\n          if (dataType.onUpdate) {\n            sql += ` ON UPDATE ${dataType.onUpdate.toUpperCase()}`;\n          }\n\n        }\n\n        result[fieldName] = sql;\n      } else {\n        result[fieldName] = dataType;\n      }\n    }\n\n    return result;\n  }\n\n  showIndexesQuery(tableName) {\n    return `PRAGMA INDEX_LIST(${this.quoteTable(tableName)})`;\n  }\n\n  showConstraintsQuery(tableName, constraintName) {\n    let sql = `SELECT sql FROM sqlite_master WHERE tbl_name='${tableName}'`;\n\n    if (constraintName) {\n      sql += ` AND sql LIKE '%${constraintName}%'`;\n    }\n\n    return `${sql};`;\n  }\n\n  removeIndexQuery(tableName, indexNameOrAttributes) {\n    let indexName = indexNameOrAttributes;\n\n    if (typeof indexName !== 'string') {\n      indexName = Utils.underscore(`${tableName}_${indexNameOrAttributes.join('_')}`);\n    }\n\n    return `DROP INDEX IF EXISTS ${this.quoteIdentifier(indexName)}`;\n  }\n\n  describeTableQuery(tableName, schema, schemaDelimiter) {\n    const table = {\n      _schema: schema,\n      _schemaDelimiter: schemaDelimiter,\n      tableName\n    };\n    return `PRAGMA TABLE_INFO(${this.quoteTable(this.addSchema(table))});`;\n  }\n\n  describeCreateTableQuery(tableName) {\n    return `SELECT sql FROM sqlite_master WHERE tbl_name='${tableName}';`;\n  }\n\n  removeColumnQuery(tableName, attributes) {\n\n    attributes = this.attributesToSQL(attributes);\n\n    let backupTableName;\n    if (typeof tableName === 'object') {\n      backupTableName = {\n        tableName: `${tableName.tableName}_backup`,\n        schema: tableName.schema\n      };\n    } else {\n      backupTableName = `${tableName}_backup`;\n    }\n\n    const quotedTableName = this.quoteTable(tableName);\n    const quotedBackupTableName = this.quoteTable(backupTableName);\n    const attributeNames = Object.keys(attributes).map(attr => this.quoteIdentifier(attr)).join(', ');\n\n    // Temporary table cannot work for foreign keys.\n    return `${this.createTableQuery(backupTableName, attributes)\n    }INSERT INTO ${quotedBackupTableName} SELECT ${attributeNames} FROM ${quotedTableName};`\n      + `DROP TABLE ${quotedTableName};${\n        this.createTableQuery(tableName, attributes)\n      }INSERT INTO ${quotedTableName} SELECT ${attributeNames} FROM ${quotedBackupTableName};`\n      + `DROP TABLE ${quotedBackupTableName};`;\n  }\n\n  _alterConstraintQuery(tableName, attributes, createTableSql) {\n    let backupTableName;\n\n    attributes = this.attributesToSQL(attributes);\n\n    if (typeof tableName === 'object') {\n      backupTableName = {\n        tableName: `${tableName.tableName}_backup`,\n        schema: tableName.schema\n      };\n    } else {\n      backupTableName = `${tableName}_backup`;\n    }\n    const quotedTableName = this.quoteTable(tableName);\n    const quotedBackupTableName = this.quoteTable(backupTableName);\n    const attributeNames = Object.keys(attributes).map(attr => this.quoteIdentifier(attr)).join(', ');\n\n    return `${createTableSql\n      .replace(`CREATE TABLE ${quotedTableName}`, `CREATE TABLE ${quotedBackupTableName}`)\n      .replace(`CREATE TABLE ${quotedTableName.replace(/`/g, '\"')}`, `CREATE TABLE ${quotedBackupTableName}`)\n    }INSERT INTO ${quotedBackupTableName} SELECT ${attributeNames} FROM ${quotedTableName};`\n      + `DROP TABLE ${quotedTableName};`\n      + `ALTER TABLE ${quotedBackupTableName} RENAME TO ${quotedTableName};`;\n  }\n\n  renameColumnQuery(tableName, attrNameBefore, attrNameAfter, attributes) {\n\n    let backupTableName;\n\n    attributes = this.attributesToSQL(attributes);\n\n    if (typeof tableName === 'object') {\n      backupTableName = {\n        tableName: `${tableName.tableName}_backup`,\n        schema: tableName.schema\n      };\n    } else {\n      backupTableName = `${tableName}_backup`;\n    }\n\n    const quotedTableName = this.quoteTable(tableName);\n    const quotedBackupTableName = this.quoteTable(backupTableName);\n    const attributeNamesImport = Object.keys(attributes).map(attr =>\n      attrNameAfter === attr ? `${this.quoteIdentifier(attrNameBefore)} AS ${this.quoteIdentifier(attr)}` : this.quoteIdentifier(attr)\n    ).join(', ');\n    const attributeNamesExport = Object.keys(attributes).map(attr => this.quoteIdentifier(attr)).join(', ');\n\n    // Temporary tables don't support foreign keys, so creating a temporary table will not allow foreign keys to be preserved\n    return `${this.createTableQuery(backupTableName, attributes)\n    }INSERT INTO ${quotedBackupTableName} SELECT ${attributeNamesImport} FROM ${quotedTableName};`\n      + `DROP TABLE ${quotedTableName};${\n        this.createTableQuery(tableName, attributes)\n      }INSERT INTO ${quotedTableName} SELECT ${attributeNamesExport} FROM ${quotedBackupTableName};`\n      + `DROP TABLE ${quotedBackupTableName};`;\n  }\n\n  startTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return `SAVEPOINT ${this.quoteIdentifier(transaction.name)};`;\n    }\n\n    return `BEGIN ${transaction.options.type} TRANSACTION;`;\n  }\n\n  setIsolationLevelQuery(value) {\n    switch (value) {\n      case Transaction.ISOLATION_LEVELS.REPEATABLE_READ:\n        return '-- SQLite is not able to choose the isolation level REPEATABLE READ.';\n      case Transaction.ISOLATION_LEVELS.READ_UNCOMMITTED:\n        return 'PRAGMA read_uncommitted = ON;';\n      case Transaction.ISOLATION_LEVELS.READ_COMMITTED:\n        return 'PRAGMA read_uncommitted = OFF;';\n      case Transaction.ISOLATION_LEVELS.SERIALIZABLE:\n        return '-- SQLite\\'s default isolation level is SERIALIZABLE. Nothing to do.';\n      default:\n        throw new Error(`Unknown isolation level: ${value}`);\n    }\n  }\n\n  replaceBooleanDefaults(sql) {\n    return sql.replace(/DEFAULT '?false'?/g, 'DEFAULT 0').replace(/DEFAULT '?true'?/g, 'DEFAULT 1');\n  }\n\n  /**\n   * Generates an SQL query that returns all foreign keys of a table.\n   *\n   * @param  {TableName} tableName  The name of the table.\n   * @returns {string}            The generated sql query.\n   * @private\n   */\n  getForeignKeysQuery(tableName) {\n    return `PRAGMA foreign_key_list(${this.quoteTable(this.addSchema(tableName))})`;\n  }\n\n  tableExistsQuery(tableName) {\n    return `SELECT name FROM sqlite_master WHERE type='table' AND name=${this.escape(this.addSchema(tableName))};`;\n  }\n\n  /**\n   * Quote identifier in sql clause\n   *\n   * @param {string} identifier\n   * @param {boolean} force\n   *\n   * @returns {string}\n   */\n  quoteIdentifier(identifier, force) {\n    return Utils.addTicks(Utils.removeTicks(identifier, '`'), '`');\n  }\n\n}\n\nmodule.exports = SQLiteQueryGenerator;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,QAAQ,QAAQ;AACtB,MAAM,cAAc,QAAQ;AAC5B,MAAM,IAAI,QAAQ;AAClB,MAAM,sBAAsB,QAAQ;AACpC,MAAM,yBAAyB,QAAQ;AAEvC,mCAAmC,oBAAoB;AAAA,EACrD,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,iBAAiB,WAAW,YAAY,SAAS;AAC/C,cAAU,WAAW;AAErB,UAAM,cAAc;AACpB,UAAM,2BAA2B,OAAO,OAAO,YAAY,OAAO,gBAAc,WAAW,SAAS,gBAAgB,SAAS;AAC7H,UAAM,YAAY;AAElB,eAAW,QAAQ,YAAY;AAC7B,UAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO;AAC1D,cAAM,WAAW,WAAW;AAC5B,cAAM,wBAAwB,SAAS,SAAS;AAEhD,YAAI,iBAAiB;AACrB,YAAI,SAAS,SAAS,gBAAgB;AACpC,cAAI,SAAS,SAAS,QAAQ;AAE5B,6BAAiB,wBAAwB,sCAAsC;AAE/E,gBAAI,SAAS,SAAS,gBAAgB;AACpC,gCAAkB,SAAS,OAAO,SAAS,QAAQ;AAAA;AAAA;AAIvD,cAAI,0BAA0B;AAC5B,wBAAY,KAAK;AACjB,gBAAI,SAAS,SAAS,aAAa;AACjC,+BAAiB,SAAS,QAAQ,gBAAgB;AAAA,mBAC7C;AACL,+BAAiB,SAAS,QAAQ,eAAe;AAAA;AAAA;AAAA;AAIvD,kBAAU,KAAK,GAAG,KAAK,gBAAgB,SAAS;AAAA;AAAA;AAIpD,UAAM,QAAQ,KAAK,WAAW;AAC9B,QAAI,UAAU,UAAU,KAAK;AAC7B,UAAM,WAAW,YAAY,IAAI,QAAM,KAAK,gBAAgB,KAAK,KAAK;AAEtE,QAAI,QAAQ,YAAY;AACtB,QAAE,KAAK,QAAQ,YAAY,aAAW;AACpC,YAAI,QAAQ,aAAa;AACvB,qBAAW,aAAa,QAAQ,OAAO,IAAI,WAAS,KAAK,gBAAgB,QAAQ,KAAK;AAAA;AAAA;AAAA;AAK5F,QAAI,SAAS,SAAS,GAAG;AACvB,iBAAW,kBAAkB;AAAA;AAG/B,UAAM,MAAM,8BAA8B,UAAU;AACpD,WAAO,KAAK,uBAAuB;AAAA;AAAA,EAGrC,aAAa,OAAO;AAClB,WAAO,QAAQ,IAAI;AAAA;AAAA,EAUrB,yBAAyB,MAAM;AAC7B,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO;AAAA;AAIT,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB;AAE1B,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AAEtB,WAAO,eAAe,KAAK,QAAQ;AACjC,YAAM,SAAS,KAAK,OAAO;AAC3B,YAAM,kBAAkB,kBAAkB,KAAK;AAC/C,UAAI,iBAAiB;AACnB,wBAAgB,gBAAgB,GAAG,QAAQ;AAC3C,0BAAkB;AAClB;AAAA;AAGF,YAAM,eAAe,kBAAkB,KAAK;AAC5C,UAAI,cAAc;AAChB,cAAM,gBAAgB,aAAa;AACnC,YAAI,kBAAkB,KAAK;AACzB;AAAA,mBACS,kBAAkB,KAAK;AAChC;AAAA,mBACS,kBAAkB,KAAK;AAChC,4BAAkB;AAClB;AAAA;AAEF,wBAAgB,aAAa,GAAG;AAChC;AAAA;AAGF;AAAA;AAIF,uBAAmB,oBAAoB;AACvC,QAAI,mBAAmB,iBAAiB;AACtC,YAAM,IAAI,MAAM,2BAA2B;AAAA;AAI7C,WAAO;AAAA;AAAA,EAIT,aAAa,OAAO;AAClB,QAAI,iBAAiB,MAAM;AACzB,aAAO,MAAM;AAAA;AAEf,QAAI,MAAM,QAAQ,UAAU,MAAM,cAAc,MAAM;AACpD,aAAO,MAAM,IAAI,SAAO,IAAI;AAAA;AAE9B,WAAO;AAAA;AAAA,EAIT,sBAAsB,MAAM,WAAW,SAAS,SAAS,SAAS;AAChE,QAAI,gBAAgB,MAAM,MAAM;AAC9B,aAAO,MAAM,sBAAsB,MAAM,WAAW,SAAS,SAAS;AAAA;AAGxE,QAAI,gBAAgB,MAAM,MAAM;AAC9B,UAAI,aAAa,KAAK,KAAK,OAAO;AAChC,aAAK,OAAO;AAAA;AAAA;AAIhB,WAAO,uBAAuB,UAAU,sBAAsB,KAAK,MAAM,MAAM,WAAW,SAAS,SAAS;AAAA;AAAA,EAG9G,eAAe,OAAO,KAAK,UAAU;AACnC,UAAM,aAAa;AACnB,eAAW,OAAO;AAClB,UAAM,SAAS,KAAK,gBAAgB,YAAY,EAAE,SAAS;AAC3D,UAAM,YAAY,GAAG,KAAK,gBAAgB,QAAQ,OAAO;AAEzD,UAAM,MAAM,eAAe,KAAK,WAAW,cAAc;AAEzD,WAAO,KAAK,uBAAuB;AAAA;AAAA,EAGrC,kBAAkB;AAChB,WAAO;AAAA;AAAA,EAGT,YAAY,WAAW,eAAe,OAAO,SAAS,YAAY;AAChE,cAAU,WAAW;AACrB,MAAE,SAAS,SAAS,KAAK;AAEzB,oBAAgB,MAAM,yBAAyB,eAAe,QAAQ,UAAU;AAEhF,UAAM,oBAAoB;AAC1B,UAAM,SAAS;AACf,UAAM,OAAO;AACb,UAAM,YAAY,QAAQ,aAAa,KAAK,UAAU;AAEtD,QAAI,YAAY;AACd,QAAE,KAAK,YAAY,CAAC,WAAW,QAAQ;AACrC,0BAAkB,OAAO;AACzB,YAAI,UAAU,OAAO;AACnB,4BAAkB,UAAU,SAAS;AAAA;AAAA;AAAA;AAK3C,eAAW,OAAO,eAAe;AAC/B,YAAM,QAAQ,cAAc;AAE5B,UAAI,iBAAiB,MAAM,mBAAmB,QAAQ,cAAc,OAAO;AACzE,eAAO,KAAK,GAAG,KAAK,gBAAgB,QAAQ,KAAK,OAAO,OAAO,qBAAqB,kBAAkB,QAAQ,QAAW,EAAE,SAAS;AAAA,aAC/H;AACL,eAAO,KAAK,GAAG,KAAK,gBAAgB,QAAQ,KAAK,OAAO,OAAO,qBAAqB,kBAAkB,QAAQ,QAAW,EAAE,SAAS,YAAY;AAAA;AAAA;AAIpJ,QAAI;AACJ,UAAM,eAAe,iCAAK,UAAL,EAAc;AAEnC,QAAI,QAAQ,OAAO;AACjB,cAAQ,UAAU,KAAK,WAAW,kBAAkB,OAAO,KAAK,0CAA0C,KAAK,WAAW,cAAc,KAAK,WAAW,OAAO,uBAAuB,KAAK,OAAO,QAAQ;AAAA,WACrM;AACL,cAAQ,UAAU,KAAK,WAAW,kBAAkB,OAAO,KAAK,QAAQ,KAAK,WAAW,OAAO;AAAA;AAGjG,WAAO,EAAE,OAAO;AAAA;AAAA,EAGlB,mBAAmB,WAAW,UAAU,IAAI;AAC1C,WAAO;AAAA,MACL,eAAe,KAAK,WAAW;AAAA,MAC/B,QAAQ,kBAAkB,iBAAiB,KAAK,WAAW,4BAA4B,KAAK,gBAAgB,aAAa,MAAM,SAAS,MAAM,YAAY,KAAK,WAAW,YAAY,MAAM,UAAU;AAAA,MACtM,KAAK;AAAA;AAAA,EAGT,YAAY,WAAW,OAAO,UAAU,IAAI,OAAO;AACjD,MAAE,SAAS,SAAS,KAAK;AAEzB,QAAI,cAAc,KAAK,mBAAmB,OAAO,MAAM,OAAO;AAE9D,QAAI,aAAa;AACf,oBAAc,SAAS;AAAA;AAGzB,QAAI,QAAQ,OAAO;AACjB,oBAAc,qCAAqC,KAAK,WAAW,cAAc,qBAAqB,KAAK,OAAO,QAAQ;AAAA;AAG5H,WAAO,eAAe,KAAK,WAAW,cAAc;AAAA;AAAA,EAGtD,gBAAgB,YAAY;AAC1B,UAAM,SAAS;AACf,eAAW,QAAQ,YAAY;AAC7B,YAAM,WAAW,WAAW;AAC5B,YAAM,YAAY,SAAS,SAAS;AAEpC,UAAI,EAAE,SAAS,WAAW;AACxB,YAAI,MAAM,SAAS,KAAK;AAExB,YAAI,OAAO,UAAU,eAAe,KAAK,UAAU,gBAAgB,CAAC,SAAS,WAAW;AACtF,iBAAO;AAAA;AAGT,YAAI,MAAM,sBAAsB,SAAS,eAAe;AAItD,iBAAO,YAAY,KAAK,OAAO,SAAS,cAAc;AAAA;AAGxD,YAAI,SAAS,WAAW,MAAM;AAC5B,iBAAO;AAAA;AAGT,YAAI,SAAS,YAAY;AACvB,iBAAO;AAEP,cAAI,SAAS,eAAe;AAC1B,mBAAO;AAAA;AAAA;AAIX,YAAI,SAAS,YAAY;AACvB,gBAAM,kBAAkB,KAAK,WAAW,SAAS,WAAW;AAE5D,cAAI;AACJ,cAAI,SAAS,WAAW,KAAK;AAC3B,4BAAgB,KAAK,gBAAgB,SAAS,WAAW;AAAA,iBACpD;AACL,4BAAgB,KAAK,gBAAgB;AAAA;AAGvC,iBAAO,eAAe,oBAAoB;AAE1C,cAAI,SAAS,UAAU;AACrB,mBAAO,cAAc,SAAS,SAAS;AAAA;AAGzC,cAAI,SAAS,UAAU;AACrB,mBAAO,cAAc,SAAS,SAAS;AAAA;AAAA;AAK3C,eAAO,aAAa;AAAA,aACf;AACL,eAAO,aAAa;AAAA;AAAA;AAIxB,WAAO;AAAA;AAAA,EAGT,iBAAiB,WAAW;AAC1B,WAAO,qBAAqB,KAAK,WAAW;AAAA;AAAA,EAG9C,qBAAqB,WAAW,gBAAgB;AAC9C,QAAI,MAAM,iDAAiD;AAE3D,QAAI,gBAAgB;AAClB,aAAO,mBAAmB;AAAA;AAG5B,WAAO,GAAG;AAAA;AAAA,EAGZ,iBAAiB,WAAW,uBAAuB;AACjD,QAAI,YAAY;AAEhB,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,MAAM,WAAW,GAAG,aAAa,sBAAsB,KAAK;AAAA;AAG1E,WAAO,wBAAwB,KAAK,gBAAgB;AAAA;AAAA,EAGtD,mBAAmB,WAAW,QAAQ,iBAAiB;AACrD,UAAM,QAAQ;AAAA,MACZ,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB;AAAA;AAEF,WAAO,qBAAqB,KAAK,WAAW,KAAK,UAAU;AAAA;AAAA,EAG7D,yBAAyB,WAAW;AAClC,WAAO,iDAAiD;AAAA;AAAA,EAG1D,kBAAkB,WAAW,YAAY;AAEvC,iBAAa,KAAK,gBAAgB;AAElC,QAAI;AACJ,QAAI,OAAO,cAAc,UAAU;AACjC,wBAAkB;AAAA,QAChB,WAAW,GAAG,UAAU;AAAA,QACxB,QAAQ,UAAU;AAAA;AAAA,WAEf;AACL,wBAAkB,GAAG;AAAA;AAGvB,UAAM,kBAAkB,KAAK,WAAW;AACxC,UAAM,wBAAwB,KAAK,WAAW;AAC9C,UAAM,iBAAiB,OAAO,KAAK,YAAY,IAAI,UAAQ,KAAK,gBAAgB,OAAO,KAAK;AAG5F,WAAO,GAAG,KAAK,iBAAiB,iBAAiB,0BAClC,gCAAgC,uBAAuB,8BACpD,mBACd,KAAK,iBAAiB,WAAW,0BACpB,0BAA0B,uBAAuB,oCAChD;AAAA;AAAA,EAGpB,sBAAsB,WAAW,YAAY,gBAAgB;AAC3D,QAAI;AAEJ,iBAAa,KAAK,gBAAgB;AAElC,QAAI,OAAO,cAAc,UAAU;AACjC,wBAAkB;AAAA,QAChB,WAAW,GAAG,UAAU;AAAA,QACxB,QAAQ,UAAU;AAAA;AAAA,WAEf;AACL,wBAAkB,GAAG;AAAA;AAEvB,UAAM,kBAAkB,KAAK,WAAW;AACxC,UAAM,wBAAwB,KAAK,WAAW;AAC9C,UAAM,iBAAiB,OAAO,KAAK,YAAY,IAAI,UAAQ,KAAK,gBAAgB,OAAO,KAAK;AAE5F,WAAO,GAAG,eACP,QAAQ,gBAAgB,mBAAmB,gBAAgB,yBAC3D,QAAQ,gBAAgB,gBAAgB,QAAQ,MAAM,QAAQ,gBAAgB,uCAClE,gCAAgC,uBAAuB,8BACpD,+BACC,mCAAmC;AAAA;AAAA,EAGxD,kBAAkB,WAAW,gBAAgB,eAAe,YAAY;AAEtE,QAAI;AAEJ,iBAAa,KAAK,gBAAgB;AAElC,QAAI,OAAO,cAAc,UAAU;AACjC,wBAAkB;AAAA,QAChB,WAAW,GAAG,UAAU;AAAA,QACxB,QAAQ,UAAU;AAAA;AAAA,WAEf;AACL,wBAAkB,GAAG;AAAA;AAGvB,UAAM,kBAAkB,KAAK,WAAW;AACxC,UAAM,wBAAwB,KAAK,WAAW;AAC9C,UAAM,uBAAuB,OAAO,KAAK,YAAY,IAAI,UACvD,kBAAkB,OAAO,GAAG,KAAK,gBAAgB,sBAAsB,KAAK,gBAAgB,UAAU,KAAK,gBAAgB,OAC3H,KAAK;AACP,UAAM,uBAAuB,OAAO,KAAK,YAAY,IAAI,UAAQ,KAAK,gBAAgB,OAAO,KAAK;AAGlG,WAAO,GAAG,KAAK,iBAAiB,iBAAiB,0BAClC,gCAAgC,6BAA6B,8BAC1D,mBACd,KAAK,iBAAiB,WAAW,0BACpB,0BAA0B,6BAA6B,oCACtD;AAAA;AAAA,EAGpB,sBAAsB,aAAa;AACjC,QAAI,YAAY,QAAQ;AACtB,aAAO,aAAa,KAAK,gBAAgB,YAAY;AAAA;AAGvD,WAAO,SAAS,YAAY,QAAQ;AAAA;AAAA,EAGtC,uBAAuB,OAAO;AAC5B,YAAQ;AAAA,WACD,YAAY,iBAAiB;AAChC,eAAO;AAAA,WACJ,YAAY,iBAAiB;AAChC,eAAO;AAAA,WACJ,YAAY,iBAAiB;AAChC,eAAO;AAAA,WACJ,YAAY,iBAAiB;AAChC,eAAO;AAAA;AAEP,cAAM,IAAI,MAAM,4BAA4B;AAAA;AAAA;AAAA,EAIlD,uBAAuB,KAAK;AAC1B,WAAO,IAAI,QAAQ,sBAAsB,aAAa,QAAQ,qBAAqB;AAAA;AAAA,EAUrF,oBAAoB,WAAW;AAC7B,WAAO,2BAA2B,KAAK,WAAW,KAAK,UAAU;AAAA;AAAA,EAGnE,iBAAiB,WAAW;AAC1B,WAAO,8DAA8D,KAAK,OAAO,KAAK,UAAU;AAAA;AAAA,EAWlG,gBAAgB,YAAY,OAAO;AACjC,WAAO,MAAM,SAAS,MAAM,YAAY,YAAY,MAAM;AAAA;AAAA;AAK9D,OAAO,UAAU;", "names": []}