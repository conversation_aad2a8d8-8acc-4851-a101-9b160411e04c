const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CustomerServiceImage = sequelize.define('CustomerServiceImage', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  },
  attachment_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '附件ID，关联到attachments表',
  },
  file_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '文件名',
  },
  file_path: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '文件路径',
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '文件大小(字节)',
  },

  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '状态：true=启用，false=禁用',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '创建时间',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '更新时间',
  },
}, {
  tableName: 'customer_service_images',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 不需要使用associate方法，关联关系已经在models/index.js中定义

module.exports = CustomerServiceImage;
