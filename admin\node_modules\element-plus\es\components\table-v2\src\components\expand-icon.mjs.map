{"version": 3, "file": "expand-icon.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/components/expand-icon.tsx"], "sourcesContent": ["import ElIcon from '@element-plus/components/icon'\nimport { ArrowRight } from '@element-plus/icons-vue'\n\nimport type { StyleValue } from 'vue'\nimport type { TableV2RowCellRenderParam } from './row'\n\nconst ExpandIcon = (\n  props: TableV2RowCellRenderParam['expandIconProps'] & {\n    class?: string | string[]\n    style: StyleValue\n    size: number\n    expanded: boolean\n    expandable: boolean\n  }\n) => {\n  const { expanded, expandable, onExpand, style, size } = props\n\n  const expandIconProps = {\n    onClick: expandable ? () => onExpand(!expanded) : undefined,\n    class: props.class,\n  } as any\n\n  return (\n    <ElIcon {...expandIconProps} size={size} style={style}>\n      <ArrowRight />\n    </ElIcon>\n  )\n}\n\nexport default ExpandIcon\n\nexport type ExpandIconInstance = ReturnType<typeof ExpandIcon>\n"], "names": ["ExpandIcon", "expanded", "expandable", "onExpand", "style", "size", "onClick", "class", "_createVNode", "_mergeProps"], "mappings": ";;;;;AAMA,EAAMA,MAAAA;IASE,QAAA;IAAEC,UAAF;IAAYC,QAAZ;IAAwBC,KAAxB;IAAkCC,IAAlC;AAAyCC,GAAAA,GAAAA,KAAAA,CAAAA;AAAzC,EAAA,MAAN,eAAA,GAAA;AAEA,IAAA,mBAAqB,GAAG,MAAA,QAAA,CAAA,CAAA,QAAA,CAAA,GAAA,KAAA,CAAA;IACtBC,KAAO,EAAA,KAAY,CAAA,KAAA;IACnBC;EAFsB,OAAxBC,WAAA,CAAA,MAAA,EAAAC,UAAA,CAAA,eAAA,EAAA;AAKA,IAAA,MAAA,EAAA,IAAA;AAAA,IAAA,OAAA,EAAA,KAAA;IAAA,EACkDL;AADlD,IAAA,OAAA,EAAA,MAAA,CAAAI,WAAA,CAAA,UAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,GAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAKD,mBArBD,UAAA;;;;"}