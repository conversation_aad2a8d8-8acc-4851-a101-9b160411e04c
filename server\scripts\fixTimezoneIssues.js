/**
 * 时区问题修复脚本
 * 用于修正由于时区问题导致的数据不一致
 */
const { Investment, InvestmentProfit, Transaction } = require('../models');
const sequelize = require('../config/database');
const timezoneUtils = require('../utils/timezoneUtils');
const moment = require('moment-timezone');

/**
 * 修复投资开始时间
 * 将未来的投资开始时间修正为当前时间
 */
async function fixInvestmentStartTimes() {
  try {
    console.log('开始修复投资开始时间...');
    
    // 获取当前时间
    const now = await timezoneUtils.getCurrentTime();
    const currentTime = now.format('YYYY-MM-DD HH:mm:ss');
    
    // 查找开始时间在未来的投资记录
    const investments = await Investment.findAll({
      where: {
        status: 'active'
      }
    });
    
    let fixedCount = 0;
    const results = [];
    
    // 逐个检查和修复
    for (const investment of investments) {
      // 转换开始时间到系统时区
      const startTime = await timezoneUtils.convertToSystemTimezone(investment.start_time);
      
      // 如果开始时间无效或在未来
      if (!startTime || startTime.isAfter(now)) {
        console.log(`投资ID ${investment.id} 的开始时间在未来或无效: ${investment.start_time}`);
        
        // 修正开始时间为当前时间
        const oldStartTime = investment.start_time;
        investment.start_time = currentTime;
        await investment.save();
        
        fixedCount++;
        results.push({
          investment_id: investment.id,
          old_start_time: oldStartTime,
          new_start_time: currentTime
        });
        
        console.log(`已修复投资ID ${investment.id} 的开始时间: ${oldStartTime} -> ${currentTime}`);
      }
    }
    
    console.log(`投资开始时间修复完成，共修复 ${fixedCount} 条记录`);
    return {
      fixed_count: fixedCount,
      results
    };
  } catch (error) {
    console.error('修复投资开始时间失败:', error);
    throw error;
  }
}

/**
 * 修复收益记录时间
 * 确保收益记录的时间与系统时区一致
 */
async function fixProfitTimes() {
  try {
    console.log('开始修复收益记录时间...');
    
    // 获取系统时区
    const systemTimezone = await timezoneUtils.getSystemTimezone();
    
    // 查找所有收益记录
    const profits = await InvestmentProfit.findAll();
    
    let fixedCount = 0;
    const results = [];
    
    // 逐个检查和修复
    for (const profit of profits) {
      // 转换收益时间到UTC
      const profitTime = moment(profit.profit_time).utc();
      
      // 转换到系统时区
      const localProfitTime = profitTime.clone().utcOffset(systemTimezone);
      
      // 检查是否需要修复
      if (profitTime.format('YYYY-MM-DD HH:mm:ss') !== localProfitTime.format('YYYY-MM-DD HH:mm:ss')) {
        console.log(`收益记录ID ${profit.id} 的时间需要修复: ${profit.profit_time}`);
        
        // 修正收益时间
        const oldProfitTime = profit.profit_time;
        profit.profit_time = localProfitTime.format('YYYY-MM-DD HH:mm:ss');
        await profit.save();
        
        fixedCount++;
        results.push({
          profit_id: profit.id,
          old_profit_time: oldProfitTime,
          new_profit_time: profit.profit_time
        });
        
        console.log(`已修复收益记录ID ${profit.id} 的时间: ${oldProfitTime} -> ${profit.profit_time}`);
      }
    }
    
    console.log(`收益记录时间修复完成，共修复 ${fixedCount} 条记录`);
    return {
      fixed_count: fixedCount,
      results
    };
  } catch (error) {
    console.error('修复收益记录时间失败:', error);
    throw error;
  }
}

/**
 * 运行所有修复
 */
async function runAllFixes() {
  try {
    console.log('开始运行所有修复...');
    
    // 创建事务
    const transaction = await sequelize.transaction();
    
    try {
      // 修复投资开始时间
      const investmentResults = await fixInvestmentStartTimes();
      
      // 修复收益记录时间
      const profitResults = await fixProfitTimes();
      
      // 提交事务
      await transaction.commit();
      
      console.log('所有修复完成');
      return {
        investment_fixes: investmentResults,
        profit_fixes: profitResults
      };
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('运行所有修复失败:', error);
    throw error;
  }
}

// 如果直接运行脚本，则执行所有修复
if (require.main === module) {
  runAllFixes()
    .then(results => {
      console.log('修复结果:', JSON.stringify(results, null, 2));
      process.exit(0);
    })
    .catch(error => {
      console.error('修复失败:', error);
      process.exit(1);
    });
} else {
  // 导出函数供其他模块使用
  module.exports = {
    fixInvestmentStartTimes,
    fixProfitTimes,
    runAllFixes
  };
}
