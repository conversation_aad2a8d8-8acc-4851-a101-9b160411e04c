/**
 * 佣金控制器
 * 处理佣金记录的查询、统计等操作
 */
const { Commission, User, Transaction } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 管理员端 - 获取佣金记录列表
exports.getCommissions = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      user_id,
      user_id_str,
      from_user_id,
      from_user_id_str,
      type,
      start_date,
      end_date,
      username,
      from_username
    } = req.query;

    // 构建查询条件
    const where = {};
    const userWhere = {};
    const fromUserWhere = {};
    let includeUser = false;
    let includeFromUser = false;

    // 根据接收佣金的用户ID筛选
    if (user_id) {
      where.user_id = user_id;
    }

    // 根据接收佣金的用户user_id字段筛选
    if (user_id_str) {
      userWhere.user_id = user_id_str;
      includeUser = true;
    }

    // 根据接收佣金的用户名筛选
    if (username) {
      userWhere.username = { [Op.like]: `%${username}%` };
      includeUser = true;
    }

    // 根据产生佣金的用户ID筛选
    if (from_user_id) {
      where.from_user_id = from_user_id;
    }

    // 根据产生佣金的用户user_id字段筛选
    if (from_user_id_str) {
      fromUserWhere.user_id = from_user_id_str;
      includeFromUser = true;
    }

    // 根据产生佣金的用户名筛选
    if (from_username) {
      fromUserWhere.username = { [Op.like]: `%${from_username}%` };
      includeFromUser = true;
    }

    // 根据佣金类型筛选
    if (type) {
      where.type = type;
    }

    // 根据日期范围筛选
    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // 分页查询
    const offset = (page - 1) * limit;

    // 准备include数组
    const includes = [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email'],
        ...(includeUser ? { where: userWhere } : {})
      },
      {
        model: User,
        as: 'from_user',
        attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email'],
        ...(includeFromUser ? { where: fromUserWhere } : {})
      },
      {
        model: Transaction,
        as: 'transaction',
        attributes: ['id', 'type', 'amount', 'status', 'created_at']
      }
    ];

    const { count, rows } = await Commission.findAndCountAll({
      where,
      include: includes,
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit),
      distinct: true // 确保count计算正确
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取佣金记录列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取佣金记录详情
exports.getCommission = async (req, res) => {
  try {
    const { id } = req.params;

    const commission = await Commission.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email']
        },
        {
          model: User,
          as: 'from_user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email']
        },
        {
          model: Transaction,
          as: 'transaction',
          attributes: ['id', 'type', 'amount', 'status', 'created_at']
        }
      ]
    });

    if (!commission) {
      return res.status(404).json({
        code: 404,
        message: '佣金记录不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: commission
    });
  } catch (error) {
    console.error('获取佣金记录详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取佣金统计
exports.getCommissionStats = async (req, res) => {
  try {
    const { user_id, start_date, end_date } = req.query;

    // 构建查询条件
    const where = {
      status: 'paid' // 只统计已发放的佣金
    };

    // 根据接收佣金的用户ID筛选
    if (user_id) {
      where.user_id = user_id;
    }

    // 根据日期范围筛选
    if (start_date && end_date) {
      where.created_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.created_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.created_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // 统计总佣金金额
    const totalAmount = await Commission.sum('amount', { where });

    // 统计各类型佣金金额
    const typeStats = await Commission.findAll({
      attributes: [
        'type',
        [sequelize.fn('SUM', sequelize.col('amount')), 'total_amount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where,
      group: ['type']
    });

    // 统计各级别佣金金额
    const levelStats = await Commission.findAll({
      attributes: [
        'level',
        [sequelize.fn('SUM', sequelize.col('amount')), 'total_amount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where,
      group: ['level']
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total_amount: totalAmount || 0,
        type_stats: typeStats,
        level_stats: levelStats
      }
    });
  } catch (error) {
    console.error('获取佣金统计错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户端 - 获取我的佣金记录
exports.getUserCommissions = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, type } = req.query;

    // 构建查询条件
    const where = {
      user_id: userId
    };

    // 根据佣金类型筛选
    if (type) {
      where.type = type;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Commission.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'from_user',
          attributes: ['id', 'user_id', 'username']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取我的佣金记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户端 - 获取我的佣金统计
exports.getUserCommissionStats = async (req, res) => {
  try {
    const userId = req.user.id;

    // 构建查询条件
    const where = {
      user_id: userId,
      status: 'paid' // 只统计已发放的佣金
    };

    // 统计总佣金金额
    const totalAmount = await Commission.sum('amount', { where });

    // 统计各类型佣金金额
    const typeStats = await Commission.findAll({
      attributes: [
        'type',
        [sequelize.fn('SUM', sequelize.col('amount')), 'total_amount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where,
      group: ['type']
    });

    // 统计各级别佣金金额
    const levelStats = await Commission.findAll({
      attributes: [
        'level',
        [sequelize.fn('SUM', sequelize.col('amount')), 'total_amount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where,
      group: ['level']
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total_amount: totalAmount || 0,
        type_stats: typeStats,
        level_stats: levelStats
      }
    });
  } catch (error) {
    console.error('获取我的佣金统计错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

module.exports = exports;
