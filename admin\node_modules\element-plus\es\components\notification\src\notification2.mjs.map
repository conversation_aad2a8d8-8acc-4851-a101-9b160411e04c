{"version": 3, "file": "notification2.mjs", "sources": ["../../../../../../packages/components/notification/src/notification.vue"], "sourcesContent": ["<template>\n  <transition\n    :name=\"ns.b('fade')\"\n    @before-leave=\"onClose\"\n    @after-leave=\"$emit('destroy')\"\n  >\n    <div\n      v-show=\"visible\"\n      :id=\"id\"\n      :class=\"[ns.b(), customClass, horizontalClass]\"\n      :style=\"positionStyle\"\n      role=\"alert\"\n      @mouseenter=\"clearTimer\"\n      @mouseleave=\"startTimer\"\n      @click=\"onClick\"\n    >\n      <el-icon v-if=\"iconComponent\" :class=\"[ns.e('icon'), typeClass]\">\n        <component :is=\"iconComponent\" />\n      </el-icon>\n      <div :class=\"ns.e('group')\">\n        <h2 :class=\"ns.e('title')\" v-text=\"title\" />\n        <div\n          v-show=\"message\"\n          :class=\"ns.e('content')\"\n          :style=\"!!title ? undefined : { margin: 0 }\"\n        >\n          <slot>\n            <p v-if=\"!dangerouslyUseHTMLString\">{{ message }}</p>\n            <!-- Caution here, message could've been compromised, never use user's input as message -->\n            <p v-else v-html=\"message\" />\n          </slot>\n        </div>\n        <el-icon v-if=\"showClose\" :class=\"ns.e('closeBtn')\" @click.stop=\"close\">\n          <Close />\n        </el-icon>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, ref } from 'vue'\nimport { useEventListener, useTimeoutFn } from '@vueuse/core'\nimport { CloseComponents, TypeComponentsMap } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useGlobalComponentSettings } from '@element-plus/components/config-provider'\nimport { notificationEmits, notificationProps } from './notification'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElNotification',\n})\n\nconst props = defineProps(notificationProps)\ndefineEmits(notificationEmits)\n\nconst { ns, zIndex } = useGlobalComponentSettings('notification')\nconst { nextZIndex, currentZIndex } = zIndex\n\nconst { Close } = CloseComponents\n\nconst visible = ref(false)\nlet timer: (() => void) | undefined = undefined\n\nconst typeClass = computed(() => {\n  const type = props.type\n  return type && TypeComponentsMap[props.type] ? ns.m(type) : ''\n})\n\nconst iconComponent = computed(() => {\n  if (!props.type) return props.icon\n  return TypeComponentsMap[props.type] || props.icon\n})\n\nconst horizontalClass = computed(() =>\n  props.position.endsWith('right') ? 'right' : 'left'\n)\n\nconst verticalProperty = computed(() =>\n  props.position.startsWith('top') ? 'top' : 'bottom'\n)\n\nconst positionStyle = computed<CSSProperties>(() => {\n  return {\n    [verticalProperty.value]: `${props.offset}px`,\n    zIndex: props.zIndex ?? currentZIndex.value,\n  }\n})\n\nfunction startTimer() {\n  if (props.duration > 0) {\n    ;({ stop: timer } = useTimeoutFn(() => {\n      if (visible.value) close()\n    }, props.duration))\n  }\n}\n\nfunction clearTimer() {\n  timer?.()\n}\n\nfunction close() {\n  visible.value = false\n}\n\nfunction onKeydown({ code }: KeyboardEvent) {\n  if (code === EVENT_CODE.delete || code === EVENT_CODE.backspace) {\n    clearTimer() // press delete/backspace clear timer\n  } else if (code === EVENT_CODE.esc) {\n    // press esc to close the notification\n    if (visible.value) {\n      close()\n    }\n  } else {\n    startTimer() // resume timer\n  }\n}\n\n// lifecycle\nonMounted(() => {\n  startTimer()\n  nextZIndex()\n  visible.value = true\n})\n\nuseEventListener(document, 'keydown', onKeydown)\n\ndefineExpose({\n  visible,\n  /** @description close notification */\n  close,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_Transition"], "mappings": ";;;;;;;;;mCAmDc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAA,MAAM,EAAE,EAAA,EAAI,MAAO,EAAA,GAAI,2BAA2B,cAAc,CAAA,CAAA;AAChE,IAAM,MAAA,EAAE,UAAY,EAAA,aAAA,EAAkB,GAAA,MAAA,CAAA;AAEtC,IAAM,MAAA,EAAE,OAAU,GAAA,eAAA,CAAA;AAElB,IAAM,MAAA,OAAA,GAAU,IAAI,KAAK,CAAA,CAAA;AACzB,IAAA,IAAI,KAAkC,GAAA,KAAA,CAAA,CAAA;AAEtC,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAC/B,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AACnB,MAAO,OAAA,IAAA,IAAQ,kBAAkB,KAAM,CAAA,IAAI,IAAI,EAAG,CAAA,CAAA,CAAE,IAAI,CAAI,GAAA,EAAA,CAAA;AAAA,KAC7D,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgB,SAAS,MAAM;AACnC,MAAA,IAAI,CAAC,KAAA,CAAM,IAAM;AACjB,QAAA,OAAyB,KAAA,CAAA,IAAA,CAAA;AAAqB,MAC/C,OAAA,iBAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA,IAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAAwB,IAAA,qBAChB,GAAkB,QAAA,CAAA,MAAO,KAAc,CAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,OAAA,GAAA,MAAA,CAAA,CAAA;AAAA,IAC/C,MAAA,gBAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,QAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAA,GAAA,QAAA,CAAA,CAAA;AAEA,IAAA,MAAM,aAAmB,GAAA,QAAA,CAAA,MAAA;AAAA,MAAS,MAChC,CAAM;AAAqC,MAC7C,OAAA;AAEA,QAAM,CAAA,gBAAA,CAAgB,SAAwB,EAAM,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA;AAClD,QAAO,MAAA,EAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,aAAA,CAAA,KAAA;AAAA,OAAA,CACL;AAAyC,KACzC,CAAA,CAAA;AAAsC,IACxC,SAAA,UAAA,GAAA;AAAA,MACD,IAAA,KAAA,CAAA,QAAA,GAAA,CAAA,EAAA;AAGC,QAAI,CAAA,EAAA,IAAA,YAAiB,YAAG,CAAA,MAAA;AACtB,UAAA,IAAA,OAAA,CAAA,KAAA;AAAC,YAAG,KAAM,EAAM,CAAA;AACd,SAAI,EAAA,KAAA,CAAA,WAAe;AAAM,OAC3B;AAAiB,KACnB;AAAA,IACF,SAAA,UAAA,GAAA;AAEA,MAAA,KAAA,IAAsB,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,EAAA,CAAA;AACpB,KAAQ;AAAA,IACV,SAAA,KAAA,GAAA;AAEA,MAAA,OAAS,CAAQ,KAAA,GAAA,KAAA,CAAA;AACf,KAAA;AAAgB,IAClB,SAAA,SAAA,CAAA,EAAA,IAAA,EAAA,EAAA;AAEA,MAAS,IAAA,IAAA,KAAA,UAAY,CAAK,MAAkB,IAAA,IAAA,KAAA,UAAA,CAAA,SAAA,EAAA;AAC1C,QAAA,UAAa,EAAA,CAAA;AACX,OAAW,MAAA,IAAA,IAAA,KAAA,UAAA,CAAA,GAAA,EAAA;AAAA,QACb,IAAA,OAAoB,CAAA,KAAA,EAAA;AAElB,UAAA;AACE,SAAM;AAAA,OACR,MAAA;AAAA,QACK,UAAA,EAAA,CAAA;AACL,OAAW;AAAA,KACb;AAAA,IACF,SAAA,CAAA,MAAA;AAGA,MAAA,UAAgB,EAAA,CAAA;AACd,MAAW,UAAA,EAAA,CAAA;AACX,MAAW,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACX,KAAA,CAAA,CAAA;AAAgB,IAClB,gBAAC,CAAA,QAAA,EAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AAED,IAAiB,MAAA,CAAA;AAEjB,MAAa,OAAA;AAAA,MACX,KAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,UAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}