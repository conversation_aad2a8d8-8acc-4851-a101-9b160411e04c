/**
 * 日期时间工具
 * 用于处理日期时间格式化和计算
 */

/**
 * 格式化日期时间
 * @param {string|Date} dateTime 日期时间
 * @param {string} format 格式
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(dateTime, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!dateTime) return '';

  try {
    // 方案5.1：处理UTC时间转换为本地时间显示
    let date;

    // 如果输入是字符串且不包含时区信息，假设为UTC时间
    if (typeof dateTime === 'string' && !dateTime.includes('Z') && !dateTime.includes('+')) {
      // 添加Z标识符表示UTC时间
      date = new Date(dateTime + 'Z');
    } else {
      date = new Date(dateTime);
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '';
    }

    // 使用本地时间进行格式化（自动转换为用户本地时区）
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    let result = format;
    result = result.replace('YYYY', String(year));
    result = result.replace('MM', month);
    result = result.replace('DD', day);
    result = result.replace('HH', hours);
    result = result.replace('mm', minutes);
    result = result.replace('ss', seconds);

    return result;
  } catch (error) {
    return '';
  }
}

/**
 * 格式化日期
 * @param {string|Date} dateTime 日期时间
 * @returns {string} 格式化后的日期
 */
export function formatDate(dateTime) {
  return formatDateTime(dateTime, 'YYYY-MM-DD');
}

/**
 * 格式化时间
 * @param {string|Date} dateTime 日期时间
 * @returns {string} 格式化后的时间
 */
export function formatTime(dateTime) {
  return formatDateTime(dateTime, 'HH:mm:ss');
}

/**
 * 获取当前时间
 * @returns {Date} 当前时间
 */
export function getCurrentTime() {
  return new Date(); // 浏览器本地时间（用于显示）
}

/**
 * 获取当前UTC时间
 * @returns {Date} 当前UTC时间（用于发送到服务器）
 */
export function getCurrentUTCTime() {
  return new Date(new Date().toISOString());
}

/**
 * 获取今天的开始时间
 * @returns {Date} 今天的开始时间
 */
export function getTodayStart() {
  const date = new Date();
  date.setHours(0, 0, 0, 0);
  return date;
}

/**
 * 获取今天的结束时间
 * @returns {Date} 今天的结束时间
 */
export function getTodayEnd() {
  const date = new Date();
  date.setHours(23, 59, 59, 999);
  return date;
}

/**
 * 计算两个日期之间的差值（天数）
 * @param {Date|string} date1 日期1
 * @param {Date|string} date2 日期2
 * @returns {number} 差值（天数）
 */
export function daysBetween(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2 - d1);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * 计算两个日期之间的差值（小时数）
 * @param {Date|string} date1 日期1
 * @param {Date|string} date2 日期2
 * @returns {number} 差值（小时数）
 */
export function hoursBetween(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2 - d1);
  return Math.ceil(diffTime / (1000 * 60 * 60));
}

/**
 * 将日期添加指定天数
 * @param {Date|string} date 日期
 * @param {number} days 天数
 * @returns {Date} 新日期
 */
export function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * 将日期添加指定小时数
 * @param {Date|string} date 日期
 * @param {number} hours 小时数
 * @returns {Date} 新日期
 */
export function addHours(date, hours) {
  const result = new Date(date);
  result.setHours(result.getHours() + hours);
  return result;
}

/**
 * 检查日期是否在指定范围内
 * @param {Date|string} date 日期
 * @param {Date|string} start 开始日期
 * @param {Date|string} end 结束日期
 * @returns {boolean} 是否在范围内
 */
export function isDateInRange(date, start, end) {
  const d = new Date(date);
  const s = new Date(start);
  const e = new Date(end);
  return d >= s && d <= e;
}

/**
 * 格式化为相对时间（例如：3小时前，2天前）
 * @param {Date|string} date 日期
 * @returns {string} 相对时间
 */
export function formatRelativeTime(date) {
  const now = new Date();
  const d = new Date(date);
  const diffMs = now - d;

  // 转换为秒
  const diffSec = Math.floor(diffMs / 1000);

  if (diffSec < 60) {
    return '刚刚';
  }

  // 转换为分钟
  const diffMin = Math.floor(diffSec / 60);

  if (diffMin < 60) {
    return `${diffMin}分钟前`;
  }

  // 转换为小时
  const diffHour = Math.floor(diffMin / 60);

  if (diffHour < 24) {
    return `${diffHour}小时前`;
  }

  // 转换为天
  const diffDay = Math.floor(diffHour / 24);

  if (diffDay < 30) {
    return `${diffDay}天前`;
  }

  // 转换为月
  const diffMonth = Math.floor(diffDay / 30);

  if (diffMonth < 12) {
    return `${diffMonth}个月前`;
  }

  // 转换为年
  const diffYear = Math.floor(diffMonth / 12);
  return `${diffYear}年前`;
}
