/**
 * 添加字段到支付通道表
 */
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

async function up() {
  try {
    // 检查表是否存在
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'payment_channels'");
    if (tables.length === 0) {
      console.log('payment_channels 表不存在，跳过迁移');
      return;
    }

    // 检查字段是否存在
    const [columns] = await sequelize.query("SHOW COLUMNS FROM payment_channels");
    const columnNames = columns.map(column => column.Field);

    // 添加 icon 字段
    if (!columnNames.includes('icon')) {
      await sequelize.query(`
        ALTER TABLE payment_channels
        ADD COLUMN icon VARCHAR(255) NULL COMMENT '图标URL'
      `);
      console.log('添加 icon 字段成功');
    }

    // 添加 status 字段
    if (!columnNames.includes('status')) {
      await sequelize.query(`
        ALTER TABLE payment_channels
        ADD COLUMN status TINYINT(1) DEFAULT 1 NULL COMMENT '是否启用'
      `);
      console.log('添加 status 字段成功');
    }

    // 添加 min_deposit_amount 字段
    if (!columnNames.includes('min_deposit_amount')) {
      await sequelize.query(`
        ALTER TABLE payment_channels
        ADD COLUMN min_deposit_amount DECIMAL(10,2) NULL COMMENT '最低充值金额'
      `);
      console.log('添加 min_deposit_amount 字段成功');
    }

    // 添加 max_deposit_amount 字段
    if (!columnNames.includes('max_deposit_amount')) {
      await sequelize.query(`
        ALTER TABLE payment_channels
        ADD COLUMN max_deposit_amount DECIMAL(10,2) NULL COMMENT '最高充值金额'
      `);
      console.log('添加 max_deposit_amount 字段成功');
    }

    console.log('迁移完成');
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  }
}

async function down() {
  try {
    // 检查表是否存在
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'payment_channels'");
    if (tables.length === 0) {
      console.log('payment_channels 表不存在，跳过回滚');
      return;
    }

    // 删除字段
    await sequelize.query(`
      ALTER TABLE payment_channels
      DROP COLUMN IF EXISTS icon,
      DROP COLUMN IF EXISTS status,
      DROP COLUMN IF EXISTS min_deposit_amount,
      DROP COLUMN IF EXISTS max_deposit_amount
    `);

    console.log('回滚完成');
  } catch (error) {
    console.error('回滚失败:', error);
    throw error;
  }
}

module.exports = { up, down };
