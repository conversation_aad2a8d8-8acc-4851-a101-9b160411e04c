{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/card/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Card from './src/card.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCard: SFCWithInstall<typeof Card> = withInstall(Card)\nexport default ElCard\n\nexport * from './src/card'\nexport type { CardInstance } from './src/instance'\n"], "names": ["withInstall", "Card"], "mappings": ";;;;;;;;AAEY,MAAC,MAAM,GAAGA,mBAAW,CAACC,iBAAI;;;;;;"}