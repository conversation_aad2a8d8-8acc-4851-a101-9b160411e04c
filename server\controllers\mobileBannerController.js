/**
 * 用户端轮播图控制器
 */
const { Banner, Attachment } = require('../models');
const { Op } = require('sequelize');

/**
 * 获取轮播图列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getBanners = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const where = {};
    
    // 状态筛选
    if (status !== undefined) {
      where.status = status === 'true' || status === true || status === '1' || status === 1;
    }
    
    // 当前时间
    const now = new Date();
    
    // 添加有效期筛选
    where[Op.and] = [
      {
        [Op.or]: [
          { start_time: null },
          { start_time: { [Op.lte]: now } }
        ]
      },
      {
        [Op.or]: [
          { end_time: null },
          { end_time: { [Op.gte]: now } }
        ]
      }
    ];
    
    // 查询轮播图
    const { count, rows } = await Banner.findAndCountAll({
      where,
      order: [
        ['sort_order', 'ASC'],
        ['id', 'DESC']
      ],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    // 返回结果
    res.json({
      code: 200,
      message: '获取轮播图列表成功',
      data: {
        total: count,
        items: rows,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取轮播图列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取轮播图列表失败',
      data: null
    });
  }
};

/**
 * 获取轮播图详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getBannerById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询轮播图
    const banner = await Banner.findByPk(id);
    
    // 检查轮播图是否存在
    if (!banner) {
      return res.status(404).json({
        code: 404,
        message: '轮播图不存在',
        data: null
      });
    }
    
    // 检查轮播图是否启用
    if (!banner.status) {
      return res.status(403).json({
        code: 403,
        message: '轮播图已禁用',
        data: null
      });
    }
    
    // 检查轮播图是否在有效期内
    const now = new Date();
    if (banner.start_time && banner.start_time > now) {
      return res.status(403).json({
        code: 403,
        message: '轮播图未到开始时间',
        data: null
      });
    }
    
    if (banner.end_time && banner.end_time < now) {
      return res.status(403).json({
        code: 403,
        message: '轮播图已过期',
        data: null
      });
    }
    
    // 返回结果
    res.json({
      code: 200,
      message: '获取轮播图详情成功',
      data: banner
    });
  } catch (error) {
    console.error('获取轮播图详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取轮播图详情失败',
      data: null
    });
  }
};
