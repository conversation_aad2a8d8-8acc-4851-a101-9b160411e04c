/* empty css             *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                 */import{d as b,r as x,o as h,c as n,e as l,b as E,w as o,al as T,V as i,a7 as P,i as g,a8 as B,a9 as M,at as S,aq as C,m as U,n as p,ap as N,p as u,g as s,a0 as F,_ as $}from"./index-LncY9lAB.js";const q={class:"params-form"},G={key:2,style:{display:"flex","align-items":"center",gap:"10px"}},I={class:"form-actions"},L=b({__name:"EmailPanel",setup(z){const _=x([{title:"邮件发送方式",value:"smtp",key:"[site.mail_type]",type:"select",options:[{label:"邮件传输协议",value:"smtp"}]},{title:"SMTP服务器",value:"mail.privateemail.com",key:"[site.mail_smtp_host]",type:"text"},{title:"SMTP端口",value:"465",key:"[site.mail_smtp_port]",type:"text"},{title:"SMTP用户名",value:"<EMAIL>",key:"[site.mail_smtp_user]",type:"text"},{title:"SMTP密码",value:"DK68qaQGGh557rW",key:"[site.mail_smtp_pass]",type:"text"},{title:"SMTP验证方式",value:"ssl",key:"[site.mail_verify_type]",type:"select",options:[{label:"SSL",value:"ssl"}]},{title:"发件人邮箱",value:"<EMAIL>",key:"[site.mail_from]",type:"email-test"}]),y=()=>{u.success("测试邮件已发送，请检查邮箱")},v=()=>{F.confirm("确定要重置所有设置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{c(),u.success("表单已重置")}).catch(()=>{})},f=()=>{u.success("设置已保存")},c=()=>{console.log("获取邮件配置参数")};return h(()=>{c()}),(D,a)=>{const r=T,d=g,w=S,k=C,m=U,V=N;return s(),n("div",q,[l(V,{data:_.value,class:"param-table"},{default:o(()=>[l(r,{prop:"title",label:"变量标题",width:"150"}),l(r,{prop:"value",label:"变量值"},{default:o(e=>[e.row.type==="text"?(s(),i(d,{key:0,modelValue:e.row.value,"onUpdate:modelValue":t=>e.row.value=t,placeholder:`请输入${e.row.title}`},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.row.type==="select"?(s(),i(k,{key:1,modelValue:e.row.value,"onUpdate:modelValue":t=>e.row.value=t,placeholder:`请选择${e.row.title}`,style:{width:"100%"}},{default:o(()=>[(s(!0),n(B,null,M(e.row.options,t=>(s(),i(w,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.row.type==="email-test"?(s(),n("div",G,[l(d,{modelValue:e.row.value,"onUpdate:modelValue":t=>e.row.value=t,placeholder:`请输入${e.row.title}`},null,8,["modelValue","onUpdate:modelValue","placeholder"]),l(m,{type:"primary",size:"small",onClick:y},{default:o(()=>a[0]||(a[0]=[p("发送测试邮件")])),_:1})])):P("",!0)]),_:1}),l(r,{prop:"key",label:"变量名",width:"200"})]),_:1},8,["data"]),E("div",I,[l(m,{onClick:v},{default:o(()=>a[1]||(a[1]=[p("重置")])),_:1}),l(m,{type:"primary",onClick:f},{default:o(()=>a[2]||(a[2]=[p("保存")])),_:1})])])}}}),X=$(L,[["__scopeId","data-v-b071ca5e"]]);export{X as default};
