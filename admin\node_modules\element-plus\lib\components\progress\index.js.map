{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/progress/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Progress from './src/progress.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElProgress: SFCWithInstall<typeof Progress> = withInstall(Progress)\nexport default ElProgress\n\nexport * from './src/progress'\n"], "names": ["withInstall", "Progress"], "mappings": ";;;;;;;;AAEY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ;;;;;;"}