{"version": 3, "sources": ["../../../src/dialects/mssql/async-queue.ts"], "sourcesContent": ["import BaseError from '../../errors/base-error';\nimport ConnectionError from '../../errors/connection-error';\n\n/**\n * Thrown when a connection to a database is closed while an operation is in progress\n */\nexport class AsyncQueueError extends BaseError {\n  constructor(message: string) {\n    super(message);\n    this.name = 'SequelizeAsyncQueueError';\n  }\n}\n\nclass AsyncQueue {\n  previous: Promise<unknown>;\n  closed: boolean;\n  rejectCurrent: (reason?: any) => void;\n\n  constructor() {\n    this.previous = Promise.resolve();\n    this.closed = false;\n    this.rejectCurrent = () => {\n      /** do nothing */\n    };\n  }\n\n  close() {\n    this.closed = true;\n    this.rejectCurrent(\n      new ConnectionError(\n        new AsyncQueueError(\n          'the connection was closed before this query could finish executing'\n        )\n      )\n    );\n  }\n\n  enqueue(asyncFunction: (...args: any[]) => Promise<unknown>) {\n    // This outer promise might seems superflous since down below we return asyncFunction().then(resolve, reject).\n    // However, this ensures that this.previous will never be a rejected promise so the queue will\n    // always keep going, while still communicating rejection from asyncFunction to the user.\n    return new Promise((resolve, reject) => {\n      this.previous = this.previous.then(() => {\n        this.rejectCurrent = reject;\n        if (this.closed) {\n          return reject(\n            new ConnectionError(\n              new AsyncQueueError(\n                'the connection was closed before this query could be executed'\n              )\n            )\n          );\n        }\n        return asyncFunction().then(resolve, reject);\n      });\n    });\n  }\n}\n\nexport default AsyncQueue;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA,wBAAsB;AACtB,8BAA4B;AAKrB,8BAA8B,0BAAU;AAAA,EAC7C,YAAY,SAAiB;AAC3B,UAAM;AACN,SAAK,OAAO;AAAA;AAAA;AAIhB,iBAAiB;AAAA,EAKf,cAAc;AAJd;AACA;AACA;AAGE,SAAK,WAAW,QAAQ;AACxB,SAAK,SAAS;AACd,SAAK,gBAAgB,MAAM;AAAA;AAAA;AAAA,EAK7B,QAAQ;AACN,SAAK,SAAS;AACd,SAAK,cACH,IAAI,gCACF,IAAI,gBACF;AAAA;AAAA,EAMR,QAAQ,eAAqD;AAI3D,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,WAAW,KAAK,SAAS,KAAK,MAAM;AACvC,aAAK,gBAAgB;AACrB,YAAI,KAAK,QAAQ;AACf,iBAAO,OACL,IAAI,gCACF,IAAI,gBACF;AAAA;AAKR,eAAO,gBAAgB,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA;AAM7C,IAAO,sBAAQ;", "names": []}