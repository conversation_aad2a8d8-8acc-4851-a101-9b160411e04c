const $="***********",a=()=>window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"?"http://localhost:3000":window.location.origin,h=()=>$,u=(t,e="")=>{if(!t)return e;if(t.startsWith("http://")||t.startsWith("https://"))return t;const s=a();return t.startsWith("/")?`${s}${t}`:`${s}/${t}`},p=t=>new Promise(e=>{const s=new Image;s.onload=()=>e(!0),s.onerror=()=>e(!1),s.src=t}),i=async(t,e="")=>{if(!t)return"";const s=a(),c=h(),n=[t,`${s}${t.startsWith("/")?"":"/"}${t}`,`${s}/${t.replace(/^\//,"")}`,`http://${c}${t.startsWith("/")?"":"/"}${t}`,`http://${c}/${t.replace(/^\//,"")}`];e&&n.push(`${s}${e.startsWith("/")?"":"/"}${e}`,`${s}/${e.replace(/^\//,"")}`);const r=t.split("/").pop();r&&(n.push(`${s}/uploads/${r}`),n.push(`http://${c}/uploads/${r}`));for(const o of n)if(await p(o))return o;return t};export{i as f,u as g};
