/**
 * 检查最新的错误日志
 * 用于检查收益系统的最新错误日志
 */
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 日志文件路径
const logFilePath = path.join(__dirname, 'logs', 'error.log');

// 获取文件大小
const stats = fs.statSync(logFilePath);
const fileSize = stats.size;

// 只读取最后10KB的日志
const startPosition = Math.max(0, fileSize - 10 * 1024);

// 创建可读流
const fileStream = fs.createReadStream(logFilePath, {
  start: startPosition,
  end: fileSize
});

// 创建接口
const rl = readline.createInterface({
  input: fileStream,
  crlfDelay: Infinity
});

// 检查日志
console.log(`开始检查最新的错误日志文件: ${logFilePath}`);

// 读取现有日志
let errorCount = 0;
let transactionErrors = 0;
let latestErrorTime = null;

rl.on('line', (line) => {
  // 解析日志时间
  const timeMatch = line.match(/"timestamp":"([^"]+)"/);
  if (timeMatch) {
    const timestamp = timeMatch[1];
    
    // 只检查最近5分钟的日志
    const logTime = new Date(timestamp);
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    if (logTime >= fiveMinutesAgo) {
      // 检查事务错误
      if (line.includes('Cannot read properties of undefined (reading \'transaction\')')) {
        transactionErrors++;
        latestErrorTime = timestamp;
        console.log(`发现事务错误: ${line}`);
      }
      
      // 计算总错误数
      if (line.includes('error')) {
        errorCount++;
      }
    }
  }
});

rl.on('close', () => {
  console.log(`最近5分钟内的总错误数: ${errorCount}`);
  console.log(`最近5分钟内的事务错误数: ${transactionErrors}`);
  
  if (transactionErrors === 0) {
    console.log('最近5分钟内没有发现事务错误，修复成功！');
  } else {
    console.log(`最近5分钟内仍然存在事务错误，最新错误时间: ${latestErrorTime}，修复失败！`);
  }
});
