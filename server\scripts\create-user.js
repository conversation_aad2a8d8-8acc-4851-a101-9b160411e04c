require('dotenv').config();
const bcrypt = require('bcryptjs');
const sequelize = require('../config/database');
const crypto = require('crypto');

async function createUser() {
  const transaction = await sequelize.transaction();

  try {
    console.log('开始创建原始用户...');

    // 检查用户是否已存在
    const [existingUsers] = await sequelize.query(
      "SELECT * FROM users WHERE username = 'user'",
      { transaction }
    );

    if (existingUsers.length > 0) {
      console.log('用户 "user" 已存在，跳过创建');
      await transaction.rollback();
      return;
    }

    // 生成用户的专属邀请码
    const userInviteCode = crypto.randomBytes(3).toString('hex').toUpperCase();

    // 检查邀请码是否已存在
    const [existingCodes] = await sequelize.query(
      "SELECT * FROM invite_codes WHERE code = ?",
      {
        replacements: [userInviteCode],
        transaction
      }
    );

    if (existingCodes.length > 0) {
      console.log('邀请码已存在，请重新运行脚本');
      await transaction.rollback();
      return;
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash('user', 10);

    // 创建用户
    const [result] = await sequelize.query(
      `INSERT INTO users (username, password, name, phone, invite_code, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      {
        replacements: ['user', hashedPassword, 'User', '1234567890', userInviteCode],
        transaction
      }
    );

    const userId = result;
    console.log(`用户创建成功，ID: ${userId}, 邀请码: ${userInviteCode}`);

    // 为用户创建邀请码记录
    await sequelize.query(
      `INSERT INTO invite_codes (code, user_id, status, created_at, updated_at)
       VALUES (?, ?, 1, NOW(), NOW())`,
      {
        replacements: [userInviteCode, userId],
        transaction
      }
    );

    console.log('邀请码记录创建成功');

    // 提交事务
    await transaction.commit();
    console.log('用户创建完成');

  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('创建用户失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行创建用户的函数
createUser();
