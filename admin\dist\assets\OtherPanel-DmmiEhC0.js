/* empty css             *//* empty css                  *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               */import{a as y}from"./index-t--hEgTQ.js";import{d as v,r as k,o as x,c as f,e as o,b as T,w as c,al as b,A as g,ap as w,n as _,m as B,p as s,g as P,a0 as C,_ as E}from"./index-LncY9lAB.js";const I={class:"params-form"},O={class:"form-actions"},z=v({__name:"OtherPanel",setup(F){const i=k([{title:"关于我们",value:"¡Bienvenido a FOX Inversión! FOX le abrirá la puerta a la riqueza y la plataforma se lanzará oficialmente el 5 de abril de 2025.",key:"[site.aboutus]",type:"richText"},{title:"收益返佣规则",value:"",key:"[site.income_notice]",type:"richText"},{title:"团队规则",value:"",key:"[site.team_rules]",type:"richText"},{title:"帮助教程",value:"",key:"[site.help_tutorial]",type:"richText"},{title:"每日抽奖规则",value:"",key:"[site.daily_lottery_rules]",type:"richText"},{title:"幸运转盘规则",value:"",key:"[site.lucky_wheel_rules]",type:"richText"},{title:"充值规则",value:"",key:"[site.recharge_rules]",type:"richText"},{title:"取款规则",value:"",key:"[site.withdraw_rules]",type:"richText"},{title:"弹窗公告",value:"",key:"[site.popup_notice]",type:"richText"},{title:"弹窗按钮文本",value:"",key:"[site.popup_button_text]",type:"richText"},{title:"弹窗公告按钮链接",value:"",key:"[site.popup_button_link]",type:"richText"},{title:"客服说明",value:"",key:"[site.customer_service_notice]",type:"richText"},{title:"投资规则",value:"",key:"[site.investment_rules]",type:"richText"},{title:"VIP规则",value:"",key:"[site.vip_rules]",type:"richText"},{title:"兑奖码规则",value:"",key:"[site.redeem_code_rules]",type:"richText"},{title:"货币兑换规则",value:"",key:"[site.currency_exchange_rules]",type:"richText"},{title:"付款说明",value:"",key:"[site.payment_notice]",type:"richText"},{title:"邀请奖励规则",value:"",key:"[site.invite_reward_rules]",type:"richText"},{title:"滚动公告",value:"",key:"[site.scroll_notice]",type:"richText"}]),m=()=>{C.confirm("确定要重置所有设置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{u(),s.success("表单已重置")}).catch(()=>{})},d=async()=>{try{const e=i.value.map(a=>({param_key:a.key,param_value:a.value,param_type:a.type,group_name:"other"})),t=await y.post("/api/admin/system-params",{params:e},{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});t.data.code===200?s.success("设置已保存"):s.error(t.data.message||"保存失败")}catch(e){console.error("保存其它配置参数失败:",e),s.error("保存失败，请稍后重试")}},u=async()=>{try{console.log("获取其它配置参数");const e=await y.get("/api/admin/system-params",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(e.data.code===200){const t=e.data.data,a=i.value.map(l=>{const n=t.find(r=>r.param_key===l.key);if(n){const r={...l};return r.value=n.param_value||"",r}return{...l}});i.value=a}}catch(e){console.error("获取其它配置参数失败:",e),s.error("获取数据失败，请稍后重试")}};return x(()=>{u()}),(e,t)=>{const a=b,l=g("quill-editor"),n=w,r=B;return P(),f("div",I,[o(n,{data:i.value,class:"param-table"},{default:c(()=>[o(a,{prop:"title",label:"变量标题",width:"150"}),o(a,{prop:"value",label:"变量值"},{default:c(p=>[o(l,{content:p.row.value,"onUpdate:content":h=>p.row.value=h,contentType:"html",theme:"snow",toolbar:"full",placeholder:`请输入${p.row.title}`,style:{height:"200px"}},null,8,["content","onUpdate:content","placeholder"])]),_:1}),o(a,{prop:"key",label:"变量名",width:"200"})]),_:1},8,["data"]),T("div",O,[o(r,{onClick:m},{default:c(()=>t[0]||(t[0]=[_("重置")])),_:1}),o(r,{type:"primary",onClick:d},{default:c(()=>t[1]||(t[1]=[_("保存")])),_:1})])])}}}),X=E(z,[["__scopeId","data-v-5640bf38"]]);export{X as default};
