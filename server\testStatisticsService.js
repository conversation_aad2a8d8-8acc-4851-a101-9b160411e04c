/**
 * 测试统计服务
 */
const statisticsService = require('./services/statisticsService');
const moment = require('moment');

async function testStatisticsService() {
  try {
    console.log('开始测试统计服务...');
    
    // 测试获取今日统计数据
    console.log('测试获取今日统计数据...');
    const todayStats = await statisticsService.getTodayStatistics();
    console.log('今日统计数据:', JSON.stringify(todayStats, null, 2));
    
    // 测试获取昨日统计数据
    console.log('测试获取昨日统计数据...');
    const yesterdayStats = await statisticsService.getYesterdayStatistics();
    console.log('昨日统计数据:', JSON.stringify(yesterdayStats, null, 2));
    
    // 测试获取累计统计数据
    console.log('测试获取累计统计数据...');
    const totalStats = await statisticsService.getTotalStatistics();
    console.log('累计统计数据:', JSON.stringify(totalStats, null, 2));
    
    // 测试获取最近7天的统计数据
    console.log('测试获取最近7天的统计数据...');
    const recentStats = await statisticsService.getRecentDaysStatistics(7);
    console.log(`最近7天的统计数据: ${recentStats.length} 条记录`);
    recentStats.forEach(stat => {
      console.log(`- ${stat.date}: 充值金额 ${stat.deposit_amount}, 取款金额 ${stat.withdrawal_amount}, 平台利润 ${stat.platform_profit}`);
    });
    
    console.log('测试完成！');
    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testStatisticsService();
