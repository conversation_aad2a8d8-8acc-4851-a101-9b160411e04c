/**
 * KB支付服务
 */
const axios = require('axios');
const crypto = require('crypto');
const { PaymentChannel, Deposit, Withdrawal, BankChannelMapping } = require('../models');

/**
 * KB支付服务
 */
class KBPayService {
  /**
   * 创建KB支付服务实例
   * @param {Object} config - 支付通道配置
   */
  constructor(config) {
    this.config = config;
  }

  /**
   * 生成MD5签名
   * @param {Object} params - 请求参数
   * @param {string} key - 签名密钥
   * @returns {string} MD5签名
   */
  generateSignature(params, key) {
    // 按照参数名ASCII码从小到大排序
    const sortedParams = {};
    Object.keys(params).sort().forEach(key => {
      sortedParams[key] = params[key];
    });

    // 构建签名字符串
    let signStr = '';
    for (const key in sortedParams) {
      signStr += `${key}=${sortedParams[key]}&`;
    }
    signStr = signStr.slice(0, -1); // 移除最后的&
    signStr += `&key=${key}`;

    // 生成MD5签名
    return crypto.createHash('md5').update(signStr).digest('hex');
  }

  /**
   * 创建充值订单
   * @param {Object} depositOrder - 充值订单信息
   * @param {number} bankId - 银行ID（可选）
   * @returns {Promise<Object>} 支付结果
   */
  async createPayment(depositOrder, bankId) {
    try {
      // 构建请求参数
      const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳
      const params = {
        merchant_no: this.config.merchant_no,
        order_no: depositOrder.order_number,
        order_amount: depositOrder.amount.toFixed(2),
        notify_url: `https://m.ohyeah012.xyz/api/payment-callbacks/kbpay`,
        timestamp: timestamp
      };

      // 如果提供了银行ID，获取并添加代收方式
      if (bankId) {
        try {
          console.log('=== 查询银行映射信息 ===');
          console.log('银行ID:', bankId);
          console.log('支付通道ID:', depositOrder.payment_channel_id);

          const bankMapping = await BankChannelMapping.findOne({
            where: {
              bank_id: bankId,
              payment_channel_id: depositOrder.payment_channel_id
            },
            include: [
              {
                model: require('../models').Bank,
                as: 'bank',
                attributes: ['id', 'name']
              }
            ]
          });

          console.log('银行映射查询结果:', bankMapping ? {
            id: bankMapping.id,
            bank_id: bankMapping.bank_id,
            bank_name: bankMapping.bank?.name,
            payin_method: bankMapping.payin_method,
            payment_channel_id: bankMapping.payment_channel_id
          } : '未找到映射');

          if (bankMapping && bankMapping.payin_method) {
            // 使用银行映射中的payin_method作为代收方式
            params.payin_method = bankMapping.payin_method;
            console.log('设置payin_method为:', bankMapping.payin_method);
          } else {
            console.log('未找到有效的payin_method，将使用默认配置');
          }
        } catch (error) {
          console.error('获取银行代收方式失败:', error);
        }
      } else {
        console.log('未提供银行ID，跳过银行映射查询');
      }

      // 如果没有从银行映射中获取到代收方式，使用配置中的默认值
      if (!params.payin_method && this.config.payin_method) {
        params.payin_method = this.config.payin_method;
      }

      // 添加支付成功跳转地址（可选）
      if (process.env.PAYMENT_RETURN_URL) {
        params.return_url = process.env.PAYMENT_RETURN_URL;
      }

      // 生成签名
      const signature = this.generateSignature(params, this.config.payin_key);
      params.sign = signature;

      console.log('=== KB支付代收请求 ===');
      console.log('请求URL:', this.config.payin_url);
      console.log('请求参数:', params);
      console.log('发送给kbpay的payin_method:', params.payin_method || '未设置');

      // 发送请求
      const response = await axios.post(this.config.payin_url, new URLSearchParams(params), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      console.log('=== KB支付代收响应 ===');
      console.log('响应状态码:', response.status);
      console.log('响应头:', JSON.stringify(response.headers, null, 2));
      console.log('响应数据:', JSON.stringify(response.data, null, 2));

      // 处理响应
      if (response.data && response.data.code === 0) {
        return {
          success: true,
          data: response.data.data,
          message: '支付创建成功'
        };
      } else {
        return {
          success: false,
          message: response.data?.message || '支付创建失败'
        };
      }
    } catch (error) {
      console.error('=== KB支付代收创建订单错误 ===');
      console.error('错误类型:', error.constructor.name);
      console.error('错误消息:', error.message);
      console.error('错误堆栈:', error.stack);
      if (error.response) {
        console.error('HTTP响应状态:', error.response.status);
        console.error('HTTP响应头:', JSON.stringify(error.response.headers, null, 2));
        console.error('HTTP响应数据:', JSON.stringify(error.response.data, null, 2));
      }
      return {
        success: false,
        message: error.message || '支付服务异常'
      };
    }
  }

  /**
   * 查询订单状态
   * @param {string} orderNumber - 订单号
   * @returns {Promise<Object>} 查询结果
   */
  async queryOrder(orderNumber) {
    try {
      // 构建请求参数
      const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳
      const params = {
        merchant_no: this.config.merchant_no,
        order_no: orderNumber,
        timestamp: timestamp
      };

      // 生成签名
      const signature = this.generateSignature(params, this.config.payin_key);
      params.sign = signature;

      console.log('=== KB支付代收查询请求 ===');
      console.log('请求URL:', this.config.payin_query_url);
      console.log('请求参数:', params);

      // 发送请求
      const response = await axios.post(this.config.payin_query_url, new URLSearchParams(params), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      console.log('=== KB支付代收查询响应 ===');
      console.log('响应状态码:', response.status);
      console.log('响应头:', JSON.stringify(response.headers, null, 2));
      console.log('响应数据:', JSON.stringify(response.data, null, 2));

      // 处理响应
      if (response.data && response.data.code === 0) {
        return {
          success: true,
          data: response.data.data,
          message: '查询成功'
        };
      } else {
        return {
          success: false,
          message: response.data?.message || '查询失败'
        };
      }
    } catch (error) {
      console.error('KB支付查询订单错误:', error);
      return {
        success: false,
        message: error.message || '查询服务异常'
      };
    }
  }

  /**
   * 验证回调签名
   * @param {Object} params - 回调参数
   * @param {string} keyType - 密钥类型，'payin_key'或'payout_key'
   * @returns {boolean} 签名是否有效
   */
  verifyCallbackSignature(params, keyType = 'payin_key') {
    // 提取签名
    const receivedSign = params.sign;

    // 创建用于验证的参数对象（不包含sign字段）
    const verifyParams = { ...params };
    delete verifyParams.sign;

    // 生成签名
    const calculatedSign = this.generateSignature(verifyParams, this.config[keyType]);

    // 比较签名
    return receivedSign === calculatedSign;
  }

  /**
   * 创建提现订单（代付）
   * @param {Object} withdrawalOrder - 提现订单信息
   * @param {Object} bankCard - 银行卡信息
   * @returns {Promise<Object>} 代付结果
   */
  async createPayout(withdrawalOrder, bankCard) {
    try {
      // 检查代付密钥是否存在
      if (!this.config.payout_key) {
        throw new Error('代付密钥未配置');
      }

      // 构建请求参数
      const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳

      // 确保金额是数字类型，并向下取整
      const actualAmount = parseFloat(withdrawalOrder.actual_amount) || parseFloat(withdrawalOrder.amount) || 0;
      const paymentAmount = Math.floor(actualAmount); // 向下取整

      console.log('=== 代付金额处理 ===');
      console.log('原始actual_amount:', withdrawalOrder.actual_amount, typeof withdrawalOrder.actual_amount);
      console.log('原始amount:', withdrawalOrder.amount, typeof withdrawalOrder.amount);
      console.log('处理后的金额:', actualAmount);
      console.log('发送给第三方的金额(向下取整):', paymentAmount);

      const params = {
        merchant_no: this.config.merchant_no,
        order_no: withdrawalOrder.order_number,
        order_amount: paymentAmount.toFixed(2), // 使用向下取整后的金额
        notify_url: `https://m.ohyeah012.xyz/api/payment-callbacks/kbpay-payout`,
        timestamp: timestamp,
        account_name: bankCard.card_holder, // 收款人姓名
        account_no: bankCard.card_number, // 收款账号
      };

      // 根据银行卡的银行信息查询代付方式
      if (bankCard.bank_id) {
        try {
          console.log('=== 查询银行代付方式映射 ===');
          console.log('银行ID:', bankCard.bank_id);
          console.log('支付通道ID:', withdrawalOrder.payment_channel_id);

          const bankMapping = await BankChannelMapping.findOne({
            where: {
              bank_id: bankCard.bank_id,
              payment_channel_id: withdrawalOrder.payment_channel_id
            },
            include: [
              {
                model: require('../models').Bank,
                as: 'bank',
                attributes: ['id', 'name']
              }
            ]
          });

          console.log('银行代付映射查询结果:', bankMapping ? {
            id: bankMapping.id,
            bank_id: bankMapping.bank_id,
            bank_name: bankMapping.bank?.name,
            payin_method: bankMapping.payin_method,
            payment_channel_id: bankMapping.payment_channel_id
          } : '未找到映射');

          if (bankMapping && bankMapping.payout_method) {
            // 使用银行映射中的payout_method作为代付方式
            params.payout_method = bankMapping.payout_method;
            console.log('设置payout_method为:', bankMapping.payout_method);
          } else {
            console.log('未找到有效的payout_method，将使用默认配置');
          }
        } catch (error) {
          console.error('获取银行代付方式失败:', error);
        }
      } else {
        console.log('银行卡未关联银行ID，跳过银行映射查询');
      }

      // 如果没有从银行映射中获取到代付方式，使用配置中的默认值
      if (!params.payout_method && this.config.payout_method) {
        params.payout_method = this.config.payout_method;
        console.log('使用默认payout_method:', this.config.payout_method);
      }

      // 生成签名
      const signature = this.generateSignature(params, this.config.payout_key);
      params.sign = signature;

      console.log('=== KB支付代付请求 ===');
      console.log('请求URL:', this.config.payout_url);
      console.log('请求参数:', params);
      console.log('发送给kbpay的payout_method:', params.payout_method || '未设置');

      // 发送请求
      const response = await axios.post(this.config.payout_url, new URLSearchParams(params), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      console.log('=== KB支付代付响应 ===');
      console.log('响应状态码:', response.status);
      console.log('响应头:', JSON.stringify(response.headers, null, 2));
      console.log('响应数据:', JSON.stringify(response.data, null, 2));

      // 处理响应
      if (response.data && response.data.code === 0) {
        return {
          success: true,
          data: response.data.data,
          message: '代付创建成功'
        };
      } else {
        return {
          success: false,
          message: response.data?.message || '代付创建失败'
        };
      }
    } catch (error) {
      console.error('=== KB支付代付创建订单错误 ===');
      console.error('错误类型:', error.constructor.name);
      console.error('错误消息:', error.message);
      console.error('错误堆栈:', error.stack);
      if (error.response) {
        console.error('HTTP响应状态:', error.response.status);
        console.error('HTTP响应头:', JSON.stringify(error.response.headers, null, 2));
        console.error('HTTP响应数据:', JSON.stringify(error.response.data, null, 2));
      }
      return {
        success: false,
        message: error.message || '代付服务异常'
      };
    }
  }

  /**
   * 查询提现订单状态
   * @param {string} orderNumber - 订单号
   * @returns {Promise<Object>} 查询结果
   */
  async queryPayout(orderNumber) {
    try {
      // 检查代付密钥是否存在
      if (!this.config.payout_key) {
        throw new Error('代付密钥未配置');
      }

      // 构建请求参数
      const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳
      const params = {
        merchant_no: this.config.merchant_no,
        order_no: orderNumber,
        timestamp: timestamp
      };

      // 生成签名
      const signature = this.generateSignature(params, this.config.payout_key);
      params.sign = signature;

      console.log('=== KB支付代付查询请求 ===');
      console.log('请求URL:', this.config.payout_query_url);
      console.log('请求参数:', params);

      // 发送请求
      const response = await axios.post(this.config.payout_query_url, new URLSearchParams(params), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      console.log('=== KB支付代付查询响应 ===');
      console.log('响应状态码:', response.status);
      console.log('响应头:', JSON.stringify(response.headers, null, 2));
      console.log('响应数据:', JSON.stringify(response.data, null, 2));

      // 处理响应
      if (response.data && response.data.code === 0) {
        return {
          success: true,
          data: response.data.data,
          message: '查询成功'
        };
      } else {
        return {
          success: false,
          message: response.data?.message || '查询失败'
        };
      }
    } catch (error) {
      console.error('KB代付查询订单错误:', error);
      return {
        success: false,
        message: error.message || '查询服务异常'
      };
    }
  }

  /**
   * 查询账户余额
   * @returns {Promise<Object>} 查询结果
   */
  async queryBalance() {
    try {
      // 检查代付密钥是否存在
      if (!this.config.payout_key) {
        throw new Error('代付密钥未配置');
      }

      // 构建请求参数
      const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳
      const params = {
        merchant_no: this.config.merchant_no,
        timestamp: timestamp
      };

      // 生成签名
      const signature = this.generateSignature(params, this.config.payout_key);
      params.sign = signature;

      console.log('=== KB支付余额查询请求 ===');
      console.log('请求URL: https://api.kbpay.io/balance');
      console.log('请求参数:', params);

      // 发送请求
      const response = await axios.post('https://api.kbpay.io/balance', new URLSearchParams(params), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      console.log('=== KB支付余额查询响应 ===');
      console.log('响应状态码:', response.status);
      console.log('响应头:', JSON.stringify(response.headers, null, 2));
      console.log('响应数据:', JSON.stringify(response.data, null, 2));

      // 处理响应
      if (response.data && response.data.code === 0) {
        return {
          success: true,
          data: response.data.data,
          message: '查询成功'
        };
      } else {
        return {
          success: false,
          message: response.data?.message || '查询失败'
        };
      }
    } catch (error) {
      console.error('KB查询账户余额错误:', error);
      return {
        success: false,
        message: error.message || '查询服务异常'
      };
    }
  }
}

/**
 * 获取KB支付服务实例
 * @param {number} channelId - 支付通道ID
 * @returns {Promise<KBPayService>} KB支付服务实例
 */
async function getKBPayService(channelId = 1) {
  // 查询支付通道
  const paymentChannel = await PaymentChannel.findByPk(channelId);

  if (!paymentChannel) {
    throw new Error('支付通道不存在');
  }

  // 解析配置
  const config = paymentChannel.config ?
    (typeof paymentChannel.config === 'string' ? JSON.parse(paymentChannel.config) : paymentChannel.config)
    : {};

  // 检查必要配置
  if (!config.merchant_no) {
    throw new Error('支付通道配置不完整：缺少商户号');
  }

  // 设置代收API URL
  if (!config.payin_key) {
    console.warn('支付通道配置不完整：缺少代收密钥，代收功能将不可用');
  } else {
    // 强制使用正式API地址，覆盖数据库中的测试地址
    config.payin_url = 'https://api.kbpay.io/online/payin/submit';
    config.payin_query_url = 'https://api.kbpay.io/online/payin/query';

    console.log('KBPay代收API地址已设置为正式地址:', config.payin_url);
  }

  // 设置代付API URL
  if (!config.payout_key) {
    console.warn('支付通道配置不完整：缺少代付密钥，代付功能将不可用');
  } else {
    // 强制使用正式API地址，覆盖数据库中的测试地址
    config.payout_url = 'https://api.kbpay.io/online/payout/submit';
    config.payout_query_url = 'https://api.kbpay.io/online/payout/query';

    console.log('KBPay代付API地址已设置为正式地址:', config.payout_url);
  }

  // 创建服务实例
  return new KBPayService(config);
}

module.exports = {
  KBPayService,
  getKBPayService
};
