/**
 * 移动端投资控制器
 */
const { Investment, User, Project, Transaction, AccountBalance } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const transactionController = require('./transactionController');
const queueService = require('../services/queueService');
const logger = require('../utils/logger');
const { generateOrderNumberByType } = require('../utils/orderNumberGenerator');
const balanceLockService = require('../services/balanceLockService');

/**
 * 创建投资记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createInvestment = async (req, res) => {
  try {
    const { project_id, amount, quantity = 1 } = req.body;
    const user = req.user;

    // 验证请求数据
    if (!project_id || !amount) {
      return res.status(400).json({
        code: 400,
        message: '项目ID和金额不能为空',
        data: null
      });
    }

    // 查找项目
    const project = await Project.findByPk(project_id);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 检查项目状态
    if (project.status !== true) {
      return res.status(400).json({
        code: 400,
        message: '项目不可投资',
        data: null
      });
    }

    // 检查项目是否在售
    if (project.sell_status !== 1) {
      return res.status(400).json({
        code: 400,
        message: '项目未在售',
        data: null
      });
    }

    // 检查项目数量和库存
    if (project.quantity > 0) {
      // 如果设置了数量限制，检查库存
      if (project.actual_quantity <= 0) {
        return res.status(400).json({
          code: 400,
          message: '项目已售罄',
          data: null
        });
      }

      if (project.actual_quantity < parseInt(quantity)) {
        return res.status(400).json({
          code: 400,
          message: '项目库存不足',
          data: null
        });
      }
    }

    // 检查同时购买数量限制
    if (project.simultaneous_purchases > 0 && parseInt(quantity) > project.simultaneous_purchases) {
      return res.status(400).json({
        code: 400,
        message: `单次最多可购买${project.simultaneous_purchases}份`,
        data: null
      });
    }

    // 检查用户最多购买次数限制
    if (project.max_purchase_times > 0) {
      // 查询用户已购买该项目的次数
      const userPurchaseCount = await Investment.count({
        where: {
          user_id: user.id,
          project_id: project.id
        }
      });

      if (userPurchaseCount >= project.max_purchase_times) {
        return res.status(400).json({
          code: 400,
          message: '您已达到该项目的最大购买次数限制',
          data: null
        });
      }
    }

    // 检查用户VIP等级
    if (project.vip_level_id) {
      try {
        // 获取用户的完整信息，包括VIP等级
        const { UserLevel } = require('../models');
        const userWithLevel = await require('../models').User.findByPk(user.id, {
          include: [
            {
              model: UserLevel,
              as: 'level',
              attributes: ['id', 'level']
            }
          ]
        });

        // 获取用户VIP等级值
        const userLevelValue = userWithLevel.level ? userWithLevel.level.level : 0;

        // 获取项目要求的VIP等级
        const projectLevelRecord = await UserLevel.findByPk(project.vip_level_id);
        const projectLevelValue = projectLevelRecord ? projectLevelRecord.level : 0;

        // 根据运算符比较
        const operator = project.vip_type_operator || '=';
        let levelCheck = false;

        switch (operator) {
          case '=':
            levelCheck = userLevelValue === projectLevelValue;
            break;
          case '>=':
            levelCheck = userLevelValue >= projectLevelValue;
            break;
          case '>':
            levelCheck = userLevelValue > projectLevelValue;
            break;
          case '<=':
            levelCheck = userLevelValue <= projectLevelValue;
            break;
          case '<':
            levelCheck = userLevelValue < projectLevelValue;
            break;
          default:
            levelCheck = userLevelValue === projectLevelValue;
        }

        if (!levelCheck) {
          return res.status(403).json({
            code: 403,
            message: '您的VIP等级不满足购买条件',
            data: null
          });
        }
      } catch (error) {
        logger.error('检查用户VIP等级错误:', error);
        return res.status(500).json({
          code: 500,
          message: '检查用户VIP等级失败',
          data: null
        });
      }
    }

    // 检查用户是否允许购买
    if (user.allow_purchase === false) {
      return res.status(403).json({
        code: 403,
        message: '您当前被禁止购买产品',
        data: null
      });
    }

    // 检查用户余额
    const numAmount = parseFloat(amount);

    // 获取用户的充值账户余额
    const depositAccount = await AccountBalance.findOne({
      where: {
        user_id: user.id,
        account_type: 'deposit'
      }
    });

    // 获取用户的收入账户余额
    const incomeAccount = await AccountBalance.findOne({
      where: {
        user_id: user.id,
        account_type: 'income'
      }
    });

    const depositBalance = depositAccount ? parseFloat(depositAccount.balance) : 0;
    const incomeBalance = incomeAccount ? parseFloat(incomeAccount.balance) : 0;

    // 检查用户总余额是否足够
    const totalBalance = depositBalance + incomeBalance;

    if (totalBalance < numAmount) {
      // 总余额不足
      return res.status(400).json({
        code: 400,
        message: '余额不足',
        data: null
      });
    }

    // 检查用户余额是否已被锁定
    const lockStatus = await balanceLockService.checkLock(user.id);
    if (lockStatus.locked) {
      return res.status(400).json({
        code: 400,
        message: '您的账户余额正在处理中，请稍后再试',
        data: null
      });
    }

    // 锁定用户余额
    const lockResult = await balanceLockService.lockBalance(user.id, numAmount, `购买项目-${project.name}`);
    if (!lockResult.success) {
      return res.status(400).json({
        code: 400,
        message: lockResult.message,
        data: null
      });
    }

    // 确定支付方式
    let useDepositAccount = true;
    let useIncomeAccount = false;
    let depositAmount = 0;
    let incomeAmount = 0;

    if (depositBalance >= numAmount) {
      // 充值账户余额足够，全部从充值账户扣除
      depositAmount = numAmount;
    } else {
      // 充值账户余额不足，需要从两个账户扣除
      depositAmount = depositBalance;
      incomeAmount = numAmount - depositBalance;
      useIncomeAccount = true;
    }

    // 开始事务
    const transaction = await sequelize.transaction();

    try {
      // 创建投资记录
      const investment = await Investment.create({
        user_id: user.id,
        project_id: project.id,
        amount: numAmount,
        quantity: parseInt(quantity),
        profit_rate: project.expected_return,
        profit_cycle: project.profit_time,
        status: 'active',
        start_time: sequelize.literal('CURRENT_TIMESTAMP'), // 使用数据库当前时间，避免时区问题
        is_gift: false, // 用户自己购买的投资
        investment_type: 'user_purchase' // 用户购买
      }, { transaction });

      // 更新项目库存和已售数量
      if (project.quantity > 0) {
        // 减少实际数量（剩余可购买份数），但如果实际数量为0表示不限制，则不减少
        if (project.actual_quantity > 0) {
          project.actual_quantity = Math.max(0, project.actual_quantity - parseInt(quantity));
        }
        // 增加已售数量
        project.sold_quantity = (project.sold_quantity || 0) + parseInt(quantity);
        await project.save({ transaction });
      }

      // 创建交易记录 - 从用户余额中扣除投资金额
      const transactionDescription = `购买项目-${project.name}`;

      // 获取交易前总余额
      const beforeTotalBalance = depositBalance + incomeBalance;

      // 从充值账户扣除金额
      if (useDepositAccount && depositAmount > 0) {
        depositAccount.balance = parseFloat(depositAccount.balance) - depositAmount;
        await depositAccount.save({ transaction });
      }

      // 从收入账户扣除金额
      if (useIncomeAccount && incomeAmount > 0) {
        incomeAccount.balance = parseFloat(incomeAccount.balance) - incomeAmount;
        await incomeAccount.save({ transaction });
      }

      // 计算交易后总余额
      const afterTotalBalance = beforeTotalBalance - numAmount;

      // 生成订单号
      const orderNumber = generateOrderNumberByType('INVESTMENT_PURCHASE');

      // 创建交易记录 - 使用总余额
      await Transaction.create({
        user_id: user.id,
        order_number: orderNumber,
        amount: -numAmount, // 负数表示支出
        type: 'investment',
        description: transactionDescription,
        before_balance: beforeTotalBalance,
        balance: afterTotalBalance, // 交易后总余额
        status: 'success',
        currency: 'CNY',
        reference_id: investment.id,
        reference_type: 'investment'
      }, { transaction });

      // 将投资记录添加到任务队列
      try {
        // 检查是否已导入queueService
        let queueService;
        try {
          queueService = require('../services/queueService');

          // 重新获取投资记录，包含关联数据
          const fullInvestment = {
            ...investment.toJSON(),
            project: project.toJSON()
          };

          // 添加到任务队列
          const queueResult = await queueService.scheduleNextProfitTask(fullInvestment);
          logger.info('投资记录添加到任务队列结果:', queueResult);
        } catch (importError) {
          logger.error('导入任务队列服务失败:', importError);
          // 不影响主流程，继续执行
        }
      } catch (queueError) {
        logger.error('将投资记录添加到任务队列失败:', queueError);
        // 不影响主流程，继续执行
      }

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          logger.error('投资创建后更新统计数据失败:', err);
        });
      } catch (error) {
        logger.error('触发统计数据更新失败:', error);
      }

      // 解锁用户余额
      await balanceLockService.unlockBalance(user.id, lockResult.lockId);

      return res.status(201).json({
        code: 201,
        message: '购买成功',
        data: {
          id: investment.id,
          project_name: project.name,
          amount: numAmount,
          status: investment.status,
          created_at: investment.created_at
        }
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();

      // 解锁用户余额
      await balanceLockService.unlockBalance(user.id, lockResult.lockId);

      throw error;
    }
  } catch (error) {
    logger.error('创建投资记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 获取用户投资记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserInvestments = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const user = req.user;

    // 构建查询条件
    const where = { user_id: user.id };

    if (status && status !== 'undefined') {
      where.status = status;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    // 先检查数据库中是否有该用户的投资记录
    const totalCount = await Investment.count({ where });

    // 如果没有记录，直接返回空结果
    if (totalCount === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          total: 0,
          page: parseInt(page),
          limit: parseInt(limit),
          items: []
        }
      });
    }

    // 有记录，继续查询
    const { count, rows } = await Investment.findAndCountAll({
      where,
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'type', 'expected_return', 'duration', 'duration_unit', 'image_id']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });



    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    logger.error('获取用户投资记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

/**
 * 获取投资详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getInvestmentById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    const investment = await Investment.findOne({
      where: {
        id,
        user_id: user.id
      },
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'type', 'expected_return', 'duration', 'duration_unit', 'image_id', 'description']
        }
      ]
    });

    if (!investment) {
      return res.status(404).json({
        code: 404,
        message: '投资记录不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: investment
    });
  } catch (error) {
    logger.error('获取投资详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
