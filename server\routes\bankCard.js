const express = require('express');
const router = express.Router();
const bankCardController = require('../controllers/bankCardController');
const { verifyAdminToken, verifyUserToken } = require('../middlewares/authMiddleware');

// 管理员端路由
const adminRouter = express.Router();

// 所有管理员路由都需要验证token
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/bank-cards:
 *   get:
 *     summary: 获取银行卡列表
 *     tags: [银行卡管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: card_type
 *         schema:
 *           type: string
 *           enum: [user, system]
 *         description: 卡类型
 *       - in: query
 *         name: status
 *         schema:
 *           type: boolean
 *         description: 状态
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/', bankCardController.getBankCards);

/**
 * @swagger
 * /api/admin/bank-cards/{id}:
 *   get:
 *     summary: 获取银行卡详情
 *     tags: [银行卡管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 银行卡ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 银行卡不存在
 */
adminRouter.get('/:id', bankCardController.getBankCard);

/**
 * @swagger
 * /api/admin/bank-cards:
 *   post:
 *     summary: 创建银行卡
 *     tags: [银行卡管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: integer
 *                 description: 用户ID，NULL表示系统收款卡
 *               bank_name:
 *                 type: string
 *                 description: 银行名称
 *               card_number:
 *                 type: string
 *                 description: 卡号
 *               card_holder:
 *                 type: string
 *                 description: 持卡人姓名
 *               branch:
 *                 type: string
 *                 description: 支行名称
 *               is_default:
 *                 type: boolean
 *                 description: 是否默认卡
 *               card_type:
 *                 type: string
 *                 enum: [user, system]
 *                 description: 卡类型
 *               daily_limit:
 *                 type: number
 *                 description: 日限额，仅对系统卡有效
 *               status:
 *                 type: boolean
 *                 description: 状态
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 */
adminRouter.post('/', bankCardController.createBankCard);

/**
 * @swagger
 * /api/admin/bank-cards/{id}:
 *   put:
 *     summary: 更新银行卡
 *     tags: [银行卡管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 银行卡ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               bank_name:
 *                 type: string
 *                 description: 银行名称
 *               card_number:
 *                 type: string
 *                 description: 卡号
 *               card_holder:
 *                 type: string
 *                 description: 持卡人姓名
 *               branch:
 *                 type: string
 *                 description: 支行名称
 *               is_default:
 *                 type: boolean
 *                 description: 是否默认卡
 *               daily_limit:
 *                 type: number
 *                 description: 日限额，仅对系统卡有效
 *               status:
 *                 type: boolean
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 银行卡不存在
 */
adminRouter.put('/:id', bankCardController.updateBankCard);

/**
 * @swagger
 * /api/admin/bank-cards/batch:
 *   delete:
 *     summary: 批量删除银行卡
 *     tags: [银行卡管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 银行卡ID数组
 *     responses:
 *       200:
 *         description: 批量删除成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 未找到要删除的银行卡
 */
adminRouter.delete('/batch', bankCardController.batchDeleteBankCards);

/**
 * @swagger
 * /api/admin/bank-cards/{id}:
 *   delete:
 *     summary: 删除银行卡
 *     tags: [银行卡管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 银行卡ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 银行卡不存在
 */
adminRouter.delete('/:id', bankCardController.deleteBankCard);

// 用户端路由
const userRouter = express.Router();

// 所有用户路由都需要验证token
userRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/account/bank-cards:
 *   get:
 *     summary: 获取用户银行卡列表
 *     tags: [用户银行卡]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
userRouter.get('/', bankCardController.getUserBankCards);

/**
 * @swagger
 * /api/account/banks:
 *   get:
 *     summary: 获取银行列表
 *     tags: [用户银行卡]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
userRouter.get('/banks', bankCardController.getBanks);

/**
 * @swagger
 * /api/account/bank-cards:
 *   post:
 *     summary: 添加用户银行卡
 *     tags: [用户银行卡]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               bank_name:
 *                 type: string
 *                 description: 银行名称
 *               card_number:
 *                 type: string
 *                 description: 卡号
 *               card_holder:
 *                 type: string
 *                 description: 持卡人姓名
 *               branch:
 *                 type: string
 *                 description: 支行名称
 *               is_default:
 *                 type: boolean
 *                 description: 是否默认卡
 *     responses:
 *       201:
 *         description: 添加成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 */
userRouter.post('/', bankCardController.addUserBankCard);

/**
 * @swagger
 * /api/account/bank-cards/{id}:
 *   put:
 *     summary: 更新用户银行卡
 *     tags: [用户银行卡]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 银行卡ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               bank_name:
 *                 type: string
 *                 description: 银行名称
 *               card_number:
 *                 type: string
 *                 description: 卡号
 *               card_holder:
 *                 type: string
 *                 description: 持卡人姓名
 *               branch:
 *                 type: string
 *                 description: 支行名称
 *               is_default:
 *                 type: boolean
 *                 description: 是否默认卡
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证
 *       404:
 *         description: 银行卡不存在
 */
userRouter.put('/:id', bankCardController.updateUserBankCard);

/**
 * @swagger
 * /api/account/bank-cards/{id}:
 *   delete:
 *     summary: 删除用户银行卡
 *     tags: [用户银行卡]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 银行卡ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 银行卡不存在
 */
userRouter.delete('/:id', bankCardController.deleteUserBankCard);



// 注册路由
router.use('/admin/bank-cards', adminRouter);
router.use('/account/bank-cards', userRouter);

module.exports = router;
