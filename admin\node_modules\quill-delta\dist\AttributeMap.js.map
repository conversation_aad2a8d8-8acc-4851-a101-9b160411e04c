{"version": 3, "file": "AttributeMap.js", "sourceRoot": "", "sources": ["../src/AttributeMap.ts"], "names": [], "mappings": ";;;;;AAAA,sEAAyC;AACzC,kEAAqC;AAMrC,IAAU,YAAY,CA2FrB;AA3FD,WAAU,YAAY;IACpB,SAAgB,OAAO,CACrB,CAAoB,EACpB,CAAoB,EACpB,QAAiB;QAFjB,kBAAA,EAAA,MAAoB;QACpB,kBAAA,EAAA,MAAoB;QAGpB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,CAAC,GAAG,EAAE,CAAC;SACR;QACD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,CAAC,GAAG,EAAE,CAAC;SACR;QACD,IAAI,UAAU,GAAG,0BAAS,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,QAAQ,EAAE;YACb,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAe,UAAC,IAAI,EAAE,GAAG;gBAClE,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;oBAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;iBAC7B;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,EAAE,EAAE,CAAC,CAAC;SACR;QACD,KAAK,IAAM,GAAG,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;gBAChD,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC1B;SACF;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;IACrE,CAAC;IA1Be,oBAAO,UA0BtB,CAAA;IAED,SAAgB,IAAI,CAClB,CAAoB,EACpB,CAAoB;QADpB,kBAAA,EAAA,MAAoB;QACpB,kBAAA,EAAA,MAAoB;QAEpB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,CAAC,GAAG,EAAE,CAAC;SACR;QACD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,CAAC,GAAG,EAAE,CAAC;SACR;QACD,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;aAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACtB,MAAM,CAAe,UAAC,KAAK,EAAE,GAAG;YAC/B,IAAI,CAAC,wBAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;gBAC5B,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACnD;YACD,OAAO,KAAK,CAAC;QACf,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;IACrE,CAAC;IAnBe,iBAAI,OAmBnB,CAAA;IAED,SAAgB,MAAM,CACpB,IAAuB,EACvB,IAAuB;QADvB,qBAAA,EAAA,SAAuB;QACvB,qBAAA,EAAA,SAAuB;QAEvB,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAClB,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAe,UAAC,IAAI,EAAE,GAAG;YACpE,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;gBACtD,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;aACvB;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAe,UAAC,IAAI,EAAE,GAAG;YACtD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;gBACtD,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;aAClB;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EAAE,YAAY,CAAC,CAAC;IACnB,CAAC;IAjBe,mBAAM,SAiBrB,CAAA;IAED,SAAgB,SAAS,CACvB,CAA2B,EAC3B,CAA2B,EAC3B,QAAgB;QAAhB,yBAAA,EAAA,gBAAgB;QAEhB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAO,CAAC,CAAC;SACV;QACD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,CAAC,CAAC,CAAC,0CAA0C;SACrD;QACD,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAe,UAAC,KAAK,EAAE,GAAG;YAChE,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;gBACxB,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,wBAAwB;aAC9C;YACD,OAAO,KAAK,CAAC;QACf,CAAC,EAAE,EAAE,CAAC,CAAC;QACP,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;IACrE,CAAC;IArBe,sBAAS,YAqBxB,CAAA;AACH,CAAC,EA3FS,YAAY,KAAZ,YAAY,QA2FrB;AAED,kBAAe,YAAY,CAAC"}