/* empty css             *//* empty css                   *//* empty css                      *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";import{d as ee,r as m,a as te,q as le,o as se,c as D,b as s,e as o,w as r,m as ae,f as oe,i as ne,aa as re,ab as ie,ac as ue,p as f,n as g,x as de,j as V,ad as me,af as ce,ag as pe,ai as fe,aj as ve,ak as ge,V as z,al as _e,v as E,y as S,ap as be,aq as he,a8 as R,a9 as T,at as ye,aA as Ue,g as w,_ as Ie}from"./index-LncY9lAB.js";const we={class:"commissions-container"},Ve={class:"toolbar"},Se={class:"toolbar-left"},xe={class:"toolbar-right"},Ce={class:"table-wrapper"},De={class:"positive"},ke={class:"pagination-container"},Ae={class:"filter-container"},Me={class:"filter-section"},ze={class:"section-content"},Ee={class:"filter-grid"},Re={class:"filter-item"},Te={class:"filter-item"},$e={class:"filter-item"},Pe={class:"filter-item"},Be={class:"filter-item"},Fe={class:"filter-item"},Ye={class:"filter-section"},je={class:"section-content"},Le={class:"filter-grid"},Ne={class:"filter-item"},He={class:"range-inputs"},We={class:"filter-section"},qe={class:"section-content"},Ge={class:"filter-grid"},Ke={class:"filter-item"},Oe={class:"filter-footer"},Je=ee({__name:"index",setup(Qe){const v=m(""),b=m(!1),t=te({id:"",userId:"",username:"",sourceUserId:"",sourceUsername:"",type:"",commissionAmountMin:null,commissionAmountMax:null,timeRange:null}),c=m(1),p=m(10),h=m(85),_=m(!1),$=m([]),y=m([]),U=m([]),P=m([{label:"充值返佣",value:"充值返佣"},{label:"收益返佣",value:"收益返佣"}]),B=le(()=>{const n=(c.value-1)*p.value,e=n+p.value;return y.value.slice(n,e)}),F=n=>n.toFixed(2),Y=n=>{switch(n){case 1:return"commission-level-1";case 2:return"commission-level-2";case 3:return"commission-level-3";default:return"commission-level-default"}},x=()=>{c.value=1,I()},j=n=>{c.value=n,I()},k=n=>{p.value=n,c.value=1,I(),localStorage.setItem("commissionsPageSize",n.toString())},L=n=>{$.value=n},I=async()=>{_.value=!0;try{const n={page:c.value.toString(),limit:p.value.toString()};v.value&&(v.value.startsWith("U")?n.user_id_str=v.value:n.username=v.value);const e=await fetch(`/api/admin/commissions?${new URLSearchParams(n)}`,{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}});if(!e.ok)throw new Error("获取佣金记录失败");const u=await e.json();if(u.code===200){const d=u.data.items.map(a=>{let i="充值返佣";return a.type&&a.type.includes("收益返佣")&&(i="收益返佣"),{id:a.id,userId:a.user_id,userIdStr:a.user?a.user.user_id||`U${String(a.user_id).padStart(6,"0")}`:`U${String(a.user_id).padStart(6,"0")}`,username:a.user?a.user.username:"",sourceUserId:a.from_user_id,sourceUserIdStr:a.from_user?a.from_user.user_id||`U${String(a.from_user_id).padStart(6,"0")}`:`U${String(a.from_user_id).padStart(6,"0")}`,sourceUsername:a.from_user?a.from_user.username:"",type:i,level:a.level||0,amount:parseFloat(a.amount),createTime:new Date(a.created_at).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")}});y.value=d,U.value=[...d],h.value=u.data.total}else f.error(u.message||"获取佣金记录失败")}catch(n){console.error("获取佣金记录错误:",n),f.error("获取佣金记录失败，请稍后重试")}finally{_.value=!1}},N=()=>{f.info("功能尚未实现")},H=()=>{_.value=!0,A(),v.value="",I(),f.success("数据已刷新")},W=()=>{b.value=!0},A=()=>{t.timeRange=null,t.commissionAmountMin=null,t.commissionAmountMax=null,t.id="",t.userId="",t.username="",t.sourceUserId="",t.sourceUsername="",t.type="",f.success("筛选条件已重置")},q=()=>{A(),y.value=[...U.value],h.value=U.value.length,c.value=1},G=async()=>{_.value=!0;try{const n={page:"1",limit:p.value.toString()};t.id&&(n.id=t.id),t.userId&&(t.userId.startsWith("U")?n.user_id_str=t.userId:n.user_id=t.userId),t.username&&(n.username=t.username),t.sourceUserId&&(t.sourceUserId.startsWith("U")?n.from_user_id_str=t.sourceUserId:n.from_user_id=t.sourceUserId),t.sourceUsername&&(n.from_username=t.sourceUsername),t.type&&(n.type=t.type),t.commissionAmountMin!==null&&t.commissionAmountMin!==""&&(n.min_amount=t.commissionAmountMin),t.commissionAmountMax!==null&&t.commissionAmountMax!==""&&(n.max_amount=t.commissionAmountMax),t.timeRange&&Array.isArray(t.timeRange)&&t.timeRange.length===2&&(n.start_date=t.timeRange[0],n.end_date=t.timeRange[1]);const e=await fetch(`/api/admin/commissions?${new URLSearchParams(n)}`,{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}});if(!e.ok)throw new Error("获取佣金记录失败");const u=await e.json();if(u.code===200){const d=u.data.items.map(a=>{let i="充值返佣";return a.type&&a.type.includes("收益返佣")&&(i="收益返佣"),{id:a.id,userId:a.user_id,username:a.user?a.user.username:"",sourceUserId:a.from_user_id,sourceUsername:a.from_user?a.from_user.username:"",type:i,level:a.level||0,amount:parseFloat(a.amount),createTime:new Date(a.created_at).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")}});y.value=d,h.value=u.data.total,c.value=1,b.value=!1,f.success(`筛选到 ${u.data.total} 条符合条件的记录`)}else f.error(u.message||"获取佣金记录失败")}catch(n){console.error("筛选佣金记录错误:",n),f.error("筛选佣金记录失败，请稍后重试")}finally{_.value=!1}};return se(()=>{const n=localStorage.getItem("commissionsPageSize");n&&(p.value=parseInt(n)),y.value=[...U.value],h.value=U.value.length,I()}),(n,e)=>{const u=de,d=ae,a=ne,i=_e,K=be,O=re,C=ye,M=he,J=ie,Q=Ue,X=ue,Z=ge;return w(),D("div",we,[s("div",Ve,[s("div",Se,[o(d,{class:"toolbar-button",type:"default",onClick:H},{default:r(()=>[o(u,null,{default:r(()=>[o(V(me))]),_:1}),e[14]||(e[14]=g("刷新 "))]),_:1})]),s("div",xe,[o(a,{modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=l=>v.value=l),placeholder:"搜索用户名或用户ID(U000001)",class:"search-input",onKeyup:oe(x,["enter"]),onBlur:x},null,8,["modelValue"]),o(d,{class:"search-button",type:"primary",onClick:x},{default:r(()=>[o(u,null,{default:r(()=>[o(V(ce))]),_:1})]),_:1}),o(d,{class:"toolbar-button filter-button",type:"default",onClick:W},{default:r(()=>[o(u,null,{default:r(()=>[o(V(pe))]),_:1}),e[15]||(e[15]=g("筛选 "))]),_:1}),o(d,{class:"toolbar-button export-button",type:"default",onClick:N},{default:r(()=>[o(u,null,{default:r(()=>[o(V(fe))]),_:1}),e[16]||(e[16]=g("导出 "))]),_:1})])]),o(O,{class:"table-card"},{default:r(()=>[s("div",Ce,[ve((w(),z(K,{ref:"commissionTable",data:B.value,border:"",stripe:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:L},{default:r(()=>[o(i,{type:"selection",width:"40",align:"center",fixed:"left"}),o(i,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),o(i,{prop:"userIdStr",label:"用户ID","min-width":"100",align:"center",fixed:"left"}),o(i,{prop:"username",label:"用户名","min-width":"100",align:"center",fixed:"left"}),o(i,{prop:"sourceUserIdStr",label:"来源用户ID","min-width":"100",align:"center"}),o(i,{prop:"sourceUsername",label:"来源用户名","min-width":"100",align:"center"}),o(i,{label:"佣金类型","min-width":"90",align:"center"},{default:r(l=>[s("span",{class:E(["commission-type",{"commission-recharge":l.row.type==="充值返佣","commission-income":l.row.type==="收益返佣"}])},S(l.row.type),3)]),_:1}),o(i,{label:"级别","min-width":"60",align:"center"},{default:r(l=>[s("span",{class:E(Y(l.row.level))},S(l.row.level)+"级",3)]),_:1}),o(i,{label:"金额","min-width":"90",align:"center"},{default:r(l=>[s("span",De," +"+S(F(l.row.amount)),1)]),_:1}),o(i,{prop:"createTime",label:"返佣时间",width:"150",align:"center",sortable:""},{default:r(l=>[g(S(l.row.createTime),1)]),_:1})]),_:1},8,["data"])),[[Z,_.value]])])]),_:1}),s("div",ke,[o(J,{"current-page":c.value,"onUpdate:currentPage":e[1]||(e[1]=l=>c.value=l),"page-size":p.value,"onUpdate:pageSize":e[2]||(e[2]=l=>p.value=l),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:h.value,onSizeChange:k,onCurrentChange:j,"pager-count":7,background:""},{sizes:r(()=>[o(M,{"model-value":p.value,onChange:k,class:"custom-page-size"},{default:r(()=>[(w(),D(R,null,T([10,20,50,100],l=>o(C,{key:l,value:l,label:`${l}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),o(X,{modelValue:b.value,"onUpdate:modelValue":e[13]||(e[13]=l=>b.value=l),title:"筛选条件",width:"900px","close-on-click-modal":!0,"close-on-press-escape":!0,class:"filter-dialog"},{footer:r(()=>[s("div",Oe,[o(d,{class:"filter-button",type:"primary",onClick:G},{default:r(()=>e[29]||(e[29]=[g(" 搜索 ")])),_:1}),o(d,{class:"filter-button",onClick:q},{default:r(()=>e[30]||(e[30]=[g(" 重置 ")])),_:1}),o(d,{class:"filter-button",onClick:e[12]||(e[12]=l=>b.value=!1)},{default:r(()=>e[31]||(e[31]=[g(" 取消 ")])),_:1})])]),default:r(()=>[s("div",Ae,[s("div",Me,[e[23]||(e[23]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"基本信息")],-1)),s("div",ze,[s("div",Ee,[s("div",Re,[e[17]||(e[17]=s("div",{class:"filter-label"},"ID",-1)),o(a,{modelValue:t.id,"onUpdate:modelValue":e[3]||(e[3]=l=>t.id=l),placeholder:"请输入ID",clearable:""},null,8,["modelValue"])]),s("div",Te,[e[18]||(e[18]=s("div",{class:"filter-label"},"用户ID",-1)),o(a,{modelValue:t.userId,"onUpdate:modelValue":e[4]||(e[4]=l=>t.userId=l),placeholder:"请输入用户ID (如U000001)",clearable:""},null,8,["modelValue"])]),s("div",$e,[e[19]||(e[19]=s("div",{class:"filter-label"},"用户名",-1)),o(a,{modelValue:t.username,"onUpdate:modelValue":e[5]||(e[5]=l=>t.username=l),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),s("div",Pe,[e[20]||(e[20]=s("div",{class:"filter-label"},"来源用户ID",-1)),o(a,{modelValue:t.sourceUserId,"onUpdate:modelValue":e[6]||(e[6]=l=>t.sourceUserId=l),placeholder:"请输入来源用户ID (如U000001)",clearable:""},null,8,["modelValue"])]),s("div",Be,[e[21]||(e[21]=s("div",{class:"filter-label"},"来源用户名",-1)),o(a,{modelValue:t.sourceUsername,"onUpdate:modelValue":e[7]||(e[7]=l=>t.sourceUsername=l),placeholder:"请输入来源用户名",clearable:""},null,8,["modelValue"])]),s("div",Fe,[e[22]||(e[22]=s("div",{class:"filter-label"},"佣金类型",-1)),o(M,{modelValue:t.type,"onUpdate:modelValue":e[8]||(e[8]=l=>t.type=l),placeholder:"请选择",clearable:"",style:{width:"100%"}},{default:r(()=>[o(C,{label:"全部",value:""}),(w(!0),D(R,null,T(P.value,l=>(w(),z(C,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])])]),s("div",Ye,[e[26]||(e[26]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"金额信息")],-1)),s("div",je,[s("div",Le,[s("div",Ne,[e[25]||(e[25]=s("div",{class:"filter-label"},"佣金金额",-1)),s("div",He,[o(a,{modelValue:t.commissionAmountMin,"onUpdate:modelValue":e[9]||(e[9]=l=>t.commissionAmountMin=l),placeholder:"最小值",clearable:""},null,8,["modelValue"]),e[24]||(e[24]=s("span",null,"-",-1)),o(a,{modelValue:t.commissionAmountMax,"onUpdate:modelValue":e[10]||(e[10]=l=>t.commissionAmountMax=l),placeholder:"最大值",clearable:""},null,8,["modelValue"])])])])])]),s("div",We,[e[28]||(e[28]=s("div",{class:"section-header"},[s("div",{class:"section-title"},"时间信息")],-1)),s("div",qe,[s("div",Ge,[s("div",Ke,[e[27]||(e[27]=s("div",{class:"filter-label"},"返佣时间",-1)),o(Q,{modelValue:t.timeRange,"onUpdate:modelValue":e[11]||(e[11]=l=>t.timeRange=l),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])])])])])]),_:1},8,["modelValue"])])}}}),mt=Ie(Je,[["__scopeId","data-v-06b2be02"]]);export{mt as default};
