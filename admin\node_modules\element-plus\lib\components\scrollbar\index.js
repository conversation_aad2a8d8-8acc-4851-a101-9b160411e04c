'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var scrollbar$1 = require('./src/scrollbar2.js');
var util = require('./src/util.js');
var scrollbar = require('./src/scrollbar.js');
var thumb = require('./src/thumb.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElScrollbar = install.withInstall(scrollbar$1["default"]);

exports.BAR_MAP = util.BAR_MAP;
exports.GAP = util.GAP;
exports.renderThumbStyle = util.renderThumbStyle;
exports.scrollbarEmits = scrollbar.scrollbarEmits;
exports.scrollbarProps = scrollbar.scrollbarProps;
exports.thumbProps = thumb.thumbProps;
exports.scrollbarContextKey = constants.scrollbarContextKey;
exports.ElScrollbar = ElScrollbar;
exports["default"] = ElScrollbar;
//# sourceMappingURL=index.js.map
