{"version": 3, "file": "key-spacing.js", "sourceRoot": "", "sources": ["../../src/rules/key-spacing.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAMtE,kCAKiB;AACjB,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,aAAa,CAAC,CAAC;AAKlD,mEAAmE;AACnE,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AAEzB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,aAAa;IACnB,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,2BAA2B,CAAC;QACzC,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EACT,gGAAgG;YAClG,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,YAAY;QACrB,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,CAAC,UAAU,CAAC;QACpB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IAEpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE3C;;WAEG;QACH,SAAS,cAAc,CAAC,QAA2B;YACjD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,6BAA6B;YAC7D,OAAO,IAAA,sBAAe,EACpB,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAE,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CACrD,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,uBAAuB,CAAC,IAAmB;YAClD,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,mBAAY,CAAE,CAAC;YAEjE,OAAO,UAAU,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC;QAChD,CAAC;QAWD,SAAS,aAAa,CACpB,IAAmB;YAEnB,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAC/C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBAClD,CAAC,CAAC,IAAI,CAAC,cAAc,CACtB,CAAC;QACJ,CAAC;QAED,SAAS,YAAY,CACnB,IAAmB;YAEnB,OAAO,CACL,aAAa,CAAC,IAAI,CAAC;gBACnB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CACzD,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,UAAU,CAAC,IAAmC;YACrD,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;gBAClD,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC;YAED,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO,IAAI,CAAC,KAAK,CACf,CAAC,EACD,UAAU,CAAC,aAAa,CACtB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,EACvB,4BAAqB,CACrB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAC5B,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,YAAY,CACnB,IAAmC;YAEnC,OAAO,uBAAuB,CAC5B,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC3C,CAAC,CAAC,IAAI,CAAC,GAAG;gBACV,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAC5B,CAAC,GAAG,CAAC,GAAG,CAAC;QACZ,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAmC,EACnC,6BAAqC,EACrC,IAA0B;YAE1B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;YAChC,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9C,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,6BAA6B,CAAC;YACzE,IAAI,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBACpD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY;oBACrD,GAAG,EAAE,KAAK,CAAC,EAAE;wBACX,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;4BACnB,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;gCACpC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;6BACxB,CAAC,CAAC;wBACL,CAAC;wBACD,OAAO,KAAK,CAAC,gBAAgB,CAC3B,cAAc,EACd,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CACxB,CAAC;oBACJ,CAAC;oBACD,IAAI,EAAE;wBACJ,QAAQ,EAAE,EAAE;wBACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;qBACtB;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,eAAe,CACtB,IAAmC,EACnC,4BAAoC,EACpC,IAA0B;YAE1B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;YAChC,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,cAAc,CAAE,CAAC;YAC7D,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,UAAU,EAAE;gBACrD,eAAe,EAAE,IAAI;aACtB,CAAE,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;YACrB,MAAM,UAAU,GACd,SAAS;gBACT,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM;gBAC3B,CAAC;gBACD,4BAA4B,CAAC;YAC/B,IAAI,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBACpD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc;oBACzD,GAAG,EAAE,KAAK,CAAC,EAAE;wBACX,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;4BACnB,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;gCACnB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;6BACjC,CAAC,CAAC;wBACL,CAAC;wBACD,OAAO,KAAK,CAAC,eAAe,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;oBACpE,CAAC;oBACD,IAAI,EAAE;wBACJ,QAAQ,EAAE,EAAE;wBACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;qBACtB;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,6HAA6H;QAC7H,SAAS,mBAAmB,CAC1B,UAAyB,EACzB,SAAwB;YAExB,MAAM,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;YAC/C,MAAM,uBAAuB,GAAG,CAC9B,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAChE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;YAEjB,IAAI,uBAAuB,KAAK,YAAY,EAAE,CAAC;gBAC7C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,uBAAuB,GAAG,YAAY,KAAK,CAAC,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC;YACd,CAAC;YAED;;;;eAIG;YACH,MAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEhE,IACE,eAAe,CAAC,MAAM;gBACtB,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,YAAY,IAAI,CAAC;gBACrD,uBAAuB,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,EACnE,CAAC;gBACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAChD,IACE,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;wBAC/B,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI;wBACrC,CAAC,EACD,CAAC;wBACD,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,KAAsB;YAC7C,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,MAAM,KAAK,GACT,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAClB,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,EAAE,KAAK,KAAK,QAAQ;oBAC5C,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;oBAC5B,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC;YAC9D,MAAM,WAAW,GACf,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW;gBAC3B,CAAC,CAAC,OAAO,CAAC,SAAS;oBACjB,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK,QAAQ;wBAC3C,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW;wBACrC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW;oBACjC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;YACtC,MAAM,6BAA6B,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,UAAU,GACd,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU;gBAC1B,CAAC,CAAC,OAAO,CAAC,SAAS;oBACjB,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK,QAAQ;wBAC3C,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU;wBACpC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU;oBAChC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;YACpC,MAAM,4BAA4B,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,IAAI,GACR,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI;gBACpB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACjB,CAAC,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK,QAAQ;wBAC3C,CAAC,CAAC,oCAAoC;4BACpC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI;wBACxD,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI;oBAC1B,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC;YAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;oBACxB,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;oBAClD,WAAW,GAAG,IAAI,CAAC,GAAG,CACpB,WAAW,EACX,KAAK,KAAK,OAAO;wBACf,CAAC,CAAC,MAAM,GAAG,6BAA6B;wBACxC,CAAC,CAAC,MAAM;4BACJ,GAAG,CAAC,MAAM;4BACV,4BAA4B;4BAC5B,6BAA6B,CACpC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oBACxB,SAAS;gBACX,CAAC;gBACD,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;gBAChC,MAAM,OAAO,GACX,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC;gBACrE,MAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;gBAEnE,IAAI,UAAU,EAAE,CAAC;oBACf,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EACP,UAAU,GAAG,CAAC;4BACZ,CAAC,CAAC,KAAK,KAAK,OAAO;gCACjB,CAAC,CAAC,UAAU;gCACZ,CAAC,CAAC,YAAY;4BAChB,CAAC,CAAC,KAAK,KAAK,OAAO;gCACjB,CAAC,CAAC,YAAY;gCACd,CAAC,CAAC,cAAc;wBACtB,GAAG,EAAE,KAAK,CAAC,EAAE;4BACX,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gCACnB,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;oCAC7B,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;iCACjB,CAAC,CAAC;4BACL,CAAC;4BACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;wBAClE,CAAC;wBACD,IAAI,EAAE;4BACJ,QAAQ,EAAE,EAAE;4BACZ,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;yBACtB;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;oBACtB,eAAe,CAAC,IAAI,EAAE,4BAA4B,EAAE,IAAI,CAAC,CAAC;gBAC5D,CAAC;qBAAM,CAAC;oBACN,gBAAgB,CAAC,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,mBAAmB,CAC1B,IAAmB,EACnB,EAAE,UAAU,EAA2B;YAEvC,MAAM,WAAW,GACf,CAAC,UAAU;gBACT,CAAC,CAAC,OAAO,CAAC,UAAU;oBAClB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW;oBAChC,CAAC,CAAC,OAAO,CAAC,WAAW;gBACvB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACjB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW;oBAC/B,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;YACtC,MAAM,6BAA6B,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,UAAU,GACd,CAAC,UAAU;gBACT,CAAC,CAAC,OAAO,CAAC,UAAU;oBAClB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU;oBAC/B,CAAC,CAAC,OAAO,CAAC,UAAU;gBACtB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACjB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU;oBAC9B,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;YACpC,MAAM,4BAA4B,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,IAAI,GACR,CAAC,UAAU;gBACT,CAAC,CAAC,OAAO,CAAC,UAAU;oBAClB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI;oBACzB,CAAC,CAAC,OAAO,CAAC,IAAI;gBAChB,CAAC,CAAC,OAAO,CAAC,SAAS;oBACjB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI;oBACxB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC;YAElC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,gBAAgB,CAAC,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC,CAAC;gBAC5D,eAAe,CAAC,IAAI,EAAE,4BAA4B,EAAE,IAAI,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,SAAS,YAAY,CACnB,IAG0B;YAE1B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;YAE/D,MAAM,OAAO,GACX,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;YAExE,IAAI,WAAW,GAAsB,EAAE,CAAC;YACxC,IAAI,iBAAiB,GAAoB,EAAE,CAAC;YAE5C,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;gBAC9C,IAAI,iBAAiB,GAAoB,EAAE,CAAC;gBAC5C,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAEpC,IAAI,QAAQ,GAA8B,SAAS,CAAC;gBAEpD,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;oBAC3B,IAAI,eAAe,GAAG,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/C,IAAI,eAAe,KAAK,QAAQ,EAAE,CAAC;wBACjC,eAAe,GAAG,SAAS,CAAC;oBAC9B,CAAC;oBAED,IAAI,eAAe,IAAI,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC;wBAClE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC/B,CAAC;yBAAM,IAAI,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;wBAC5D,IAAI,eAAe,EAAE,CAAC;4BACpB,kEAAkE;4BAClE,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;4BACxC,iBAAiB,CAAC,GAAG,EAAE,CAAC;wBAC1B,CAAC;wBACD,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC/B,CAAC;yBAAM,CAAC;wBACN,iBAAiB,GAAG,CAAC,IAAI,CAAC,CAAC;wBAC3B,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBACtC,CAAC;oBAED,QAAQ,GAAG,IAAI,CAAC;gBAClB,CAAC;gBAED,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAC1C,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CACnD,CAAC;gBACF,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,iBAAiB,GAAG,OAAO,CAAC;YAC9B,CAAC;YAED,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;gBAChC,eAAe,CAAC,KAAK,CAAC,CAAC;YACzB,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE,CAAC;gBACrC,mBAAmB,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QACD,OAAO;YACL,GAAG,SAAS;YACZ,aAAa,EAAE,YAAY;YAC3B,eAAe,EAAE,YAAY;YAC7B,SAAS,EAAE,YAAY;SACxB,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}