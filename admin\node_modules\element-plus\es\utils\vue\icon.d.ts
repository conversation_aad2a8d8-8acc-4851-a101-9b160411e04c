import type { Component } from 'vue';
export declare const iconPropType: import("vue").PropType<string | Component>;
export declare const CloseComponents: {
    Close: any;
};
export declare const TypeComponents: {
    Close: any;
    SuccessFilled: any;
    InfoFilled: any;
    WarningFilled: any;
    CircleCloseFilled: any;
};
export declare const TypeComponentsMap: {
    success: any;
    warning: any;
    error: any;
    info: any;
};
export declare const ValidateComponentsMap: {
    validating: any;
    success: any;
    error: any;
};
