/**
 * 移动端系统参数控制器
 * 处理系统参数相关操作
 */
const { SystemParam } = require('../models');
const { Op } = require('sequelize');

// 获取指定键的系统参数
exports.getSystemParamByKey = async (req, res) => {
  try {
    const { key } = req.params;

    if (!key) {
      return res.status(400).json({
        code: 400,
        message: '参数键不能为空',
        data: null
      });
    }

    const systemParam = await SystemParam.findOne({
      where: {
        param_key: key
      }
    });

    if (!systemParam) {
      return res.status(404).json({
        code: 404,
        message: '系统参数不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: systemParam
    });
  } catch (error) {
    console.error('获取系统参数错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取指定分组的系统参数
exports.getSystemParamsByGroup = async (req, res) => {
  try {
    const { group } = req.params;

    if (!group) {
      return res.status(400).json({
        code: 400,
        message: '参数分组不能为空',
        data: null
      });
    }

    const systemParams = await SystemParam.findAll({
      where: {
        group_name: group
      },
      order: [['sort_order', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: systemParams
    });
  } catch (error) {
    console.error('获取系统参数错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取团队规则
exports.getTeamRules = async (req, res) => {
  try {
    const teamRules = await SystemParam.findOne({
      where: {
        param_key: '[site.team_rules]'
      }
    });

    if (!teamRules) {
      return res.status(404).json({
        code: 404,
        message: '团队规则不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: teamRules
    });
  } catch (error) {
    console.error('获取团队规则错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

module.exports = exports;
