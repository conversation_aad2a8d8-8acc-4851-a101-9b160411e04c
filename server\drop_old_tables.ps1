# 删除旧表脚本
# 作者：FOX开发团队
# 日期：2025-06-04

# 禁用外键检查
mysql -u root -pMySQL3352~! -e "USE fox_db; SET FOREIGN_KEY_CHECKS = 0;"

# 删除带有_old后缀的表
$oldTables = @(
    "attachments_old",
    "banners_old",
    "commissions_old",
    "deposits_old",
    "investment_profits_old",
    "investments_old",
    "projects_old",
    "system_params_old",
    "transactions_old",
    "user_levels_old",
    "users_old",
    "withdrawals_old"
)

foreach ($table in $oldTables) {
    Write-Host "Dropping table: $table"
    mysql -u root -pMySQL3352~! -e "USE fox_db; DROP TABLE IF EXISTS $table;"
}

# 删除带有_backup后缀的表
$backupTables = @(
    "admin_roles_backup",
    "admins_backup",
    "attachments_backup",
    "commissions_backup",
    "deposits_backup",
    "investment_profits_backup",
    "investments_backup",
    "permissions_backup",
    "projects_backup",
    "receiving_bank_cards_backup",
    "role_permissions_backup",
    "roles_backup",
    "system_params_backup",
    "transactions_backup",
    "user_bank_cards_backup",
    "user_levels_backup",
    "users_backup",
    "withdrawals_backup"
)

foreach ($table in $backupTables) {
    Write-Host "Dropping table: $table"
    mysql -u root -pMySQL3352~! -e "USE fox_db; DROP TABLE IF EXISTS $table;"
}

# 启用外键检查
mysql -u root -pMySQL3352~! -e "USE fox_db; SET FOREIGN_KEY_CHECKS = 1;"

Write-Host "All old tables have been dropped."
