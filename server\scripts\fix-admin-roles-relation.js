require('dotenv').config();
const mysql = require('mysql2/promise');

async function fixAdminRolesRelation() {
  try {
    console.log('开始修复管理员与角色的关联...');

    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USERNAME || 'root',
      password: 'MySQL3352~!',
      database: process.env.DB_DATABASE || 'fox_db'
    });

    // 1. 确保admin_roles表中有正确的关联
    console.log('检查admin_roles表中的关联...');

    // 获取所有管理员
    const [admins] = await connection.execute('SELECT * FROM admins');
    console.log(`找到 ${admins.length} 个管理员`);

    // 获取所有角色
    const [roles] = await connection.execute('SELECT * FROM roles');
    console.log(`找到 ${roles.length} 个角色`);

    // 清空admin_roles表
    await connection.execute('DELETE FROM admin_roles');
    console.log('已清空admin_roles表');

    // 为每个管理员分配角色
    for (const admin of admins) {
      let roleId = 2; // 默认为管理员组

      if (admin.role === 'super') {
        roleId = 1; // 超级管理员组
      }

      await connection.execute(`
        INSERT INTO admin_roles (admin_id, role_id, created_at, updated_at)
        VALUES (${admin.id}, ${roleId}, NOW(), NOW())
      `);
      console.log(`为管理员 ${admin.username} (ID: ${admin.id}) 分配了角色ID: ${roleId}`);
    }

    // 2. 修改Sequelize模型关联查询
    console.log('修改后端API查询，确保返回roles数组...');

    // 创建一个视图，将管理员与角色关联起来
    await connection.execute(`
      CREATE OR REPLACE VIEW admin_with_roles AS
      SELECT
        a.id,
        a.username,
        a.name,
        a.role,
        a.status,
        a.last_login as last_login_at,
        a.created_at,
        a.updated_at,
        JSON_ARRAYAGG(
          JSON_OBJECT(
            'id', r.id,
            'name', r.name
          )
        ) as roles
      FROM
        admins a
      LEFT JOIN
        admin_roles ar ON a.id = ar.admin_id
      LEFT JOIN
        roles r ON ar.role_id = r.id
      GROUP BY
        a.id
    `);
    console.log('已创建admin_with_roles视图');

    console.log('修复完成');
    await connection.end();
    process.exit(0);
  } catch (error) {
    console.error('修复失败:', error);
    process.exit(1);
  }
}

fixAdminRolesRelation();
