const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const InviteCode = sequelize.define('InviteCode', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  code: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
    },
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  used_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false,
    comment: '已使用次数',
  },
  max_uses: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false,
    comment: '最大使用次数，0表示不限制',
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    allowNull: false,
    comment: '状态：true-有效，false-无效',
  },
}, {
  tableName: 'invite_codes',
  timestamps: true,
});

module.exports = InviteCode;
