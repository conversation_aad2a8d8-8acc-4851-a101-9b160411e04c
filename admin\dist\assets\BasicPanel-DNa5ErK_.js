/* empty css             *//* empty css                  *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 */import{d as x,r as w,a6 as U,c as p,g as i,b as r,a8 as S,a9 as T,e as m,i as N,_ as $,o as E,w as b,al as P,V as v,a7 as D,ao as F,aq as L,bg as A,at as M,ap as H,n as J,m as q,a0 as z,p as V}from"./index-LncY9lAB.js";import{a as C}from"./index-t--hEgTQ.js";const G={class:"language-table-wrapper"},X={class:"language-table"},j={class:"language-item"},K={class:"action-buttons"},Q=["onClick"],R=["onClick"],W=x({__name:"LanguageTable",props:{modelValue:{}},emits:["update:modelValue"],setup(g,{emit:_}){const c=g,k=_,n=w([...c.modelValue]);U(()=>c.modelValue,e=>{JSON.stringify(e)!==JSON.stringify(n.value)&&(n.value=JSON.parse(JSON.stringify(e)))},{deep:!0}),U(n,e=>{JSON.stringify(e)!==JSON.stringify(c.modelValue)&&k("update:modelValue",JSON.parse(JSON.stringify(e)))},{deep:!0});const y=e=>{const a={key:"",value:""};e!==void 0?n.value.splice(e+1,0,a):n.value.push(a)},f=e=>{n.value.length>1&&n.value.splice(e,1)};return(e,a)=>{const s=N;return i(),p("div",G,[r("table",X,[(i(!0),p(S,null,T(n.value,(t,d)=>(i(),p("tr",{key:d},[r("td",null,[r("div",j,[m(s,{modelValue:t.key,"onUpdate:modelValue":o=>t.key=o,placeholder:"键",size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue"]),m(s,{modelValue:t.value,"onUpdate:modelValue":o=>t.value=o,placeholder:"值",size:"small",style:{width:"80px",margin:"0 20px"}},null,8,["modelValue","onUpdate:modelValue"]),r("div",K,[r("button",{class:"btn-icon btn-delete",onClick:o=>f(d)},"×",8,Q),r("button",{class:"btn-icon btn-add",onClick:o=>y(d)},"+",8,R)])])])]))),128))]),r("button",{class:"btn-new",onClick:a[0]||(a[0]=t=>y())},"新增")])}}}),Y=$(W,[["__scopeId","data-v-47c46627"]]),Z={class:"data-symbols-table-wrapper"},ee={class:"data-symbols-table"},te={class:"symbol-item"},ae={class:"action-buttons"},le=["onClick"],oe=["onClick"],ne=x({__name:"DataSymbolsTable",props:{modelValue:{}},emits:["update:modelValue"],setup(g,{emit:_}){const c=g,k=_,n=w([...c.modelValue]);U(()=>c.modelValue,e=>{JSON.stringify(e)!==JSON.stringify(n.value)&&(n.value=JSON.parse(JSON.stringify(e)))},{deep:!0}),U(n,e=>{JSON.stringify(e)!==JSON.stringify(c.modelValue)&&k("update:modelValue",JSON.parse(JSON.stringify(e)))},{deep:!0});const y=e=>{const a={key:"",value:""};e!==void 0?n.value.splice(e+1,0,a):n.value.push(a)},f=e=>{n.value.length>1&&n.value.splice(e,1)};return(e,a)=>{const s=N;return i(),p("div",Z,[r("table",ee,[(i(!0),p(S,null,T(n.value,(t,d)=>(i(),p("tr",{key:d},[r("td",null,[r("div",te,[m(s,{modelValue:t.key,"onUpdate:modelValue":o=>t.key=o,placeholder:"代码",size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue"]),m(s,{modelValue:t.value,"onUpdate:modelValue":o=>t.value=o,placeholder:"名称",size:"small",style:{width:"80px",margin:"0 20px"}},null,8,["modelValue","onUpdate:modelValue"]),r("div",ae,[r("button",{class:"btn-icon btn-delete",onClick:o=>f(d)},"×",8,le),r("button",{class:"btn-icon btn-add",onClick:o=>y(d)},"+",8,oe)])])])]))),128))]),r("button",{class:"btn-new",onClick:a[0]||(a[0]=t=>y())},"新增")])}}}),se=$(ne,[["__scopeId","data-v-224b6d51"]]),ue={class:"params-form"},ie={class:"form-actions"},re=x({__name:"BasicPanel",setup(g){const _=w({common:[{id:"UTC+0",name:"(UTC+00:00) 协调世界时",value:"+00:00"},{id:"UTC+8",name:"(UTC+08:00) 中国标准时间",value:"+08:00"},{id:"UTC-5",name:"(UTC-05:00) 东部标准时间",value:"-05:00"},{id:"UTC-6",name:"(UTC-06:00) 中部标准时间",value:"-06:00"},{id:"UTC-7",name:"(UTC-07:00) 山地标准时间",value:"-07:00"},{id:"UTC-8",name:"(UTC-08:00) 太平洋标准时间",value:"-08:00"}],all:[]}),c=w([{title:"站点名称",value:"FOX",key:"[site.name]",type:"text"},{title:"网站域名",value:"",key:"[site.domain]",type:"text",placeholder:"留空则自动使用当前网站域名"},{title:"备案号",value:"",key:"[site.beian]",type:"text"},{title:"CDN地址",value:"",key:"[site.cdnurl]",type:"text"},{title:"版本号",value:"1.0.1",key:"[site.version]",type:"text"},{title:"时区",value:"",key:"[site.timezone]",type:"timezone"},{title:"禁止IP",value:"",key:"[site.forbiddenip]",type:"textarea"},{title:"语言",value:[{key:"backend",value:"zh-cn"},{key:"frontend",value:"es"}],key:"[site.languages]",type:"language"},{title:"后台固定页",value:"dashboard",key:"[site.fixedpage]",type:"text"},{title:"公司地址",value:"",key:"[site.address]",type:"text"},{title:"总资金",value:"10000",key:"[site.金额]",type:"text"},{title:"总资金2",value:"12000",key:"[site.amount2]",type:"text"},{title:"行情数据秘钥",value:"30b263fc999d6b5465fbbe892fc3be8e-c-app",key:"[site.data_api_key]",type:"text"},{title:"行情数据",value:[{key:"BTCUSDT",value:"BTC/USDT"},{key:"ETHUSDT",value:"ETH/USDT"}],key:"[site.data_symbols]",type:"dataSymbols"},{title:"开始时间",value:"2024-06-01 11:37:35",key:"[site.starttime]",type:"text"},{title:"允许购买",value:"1",key:"[site.buy_switch]",type:"switch"},{title:"高级投资",value:"1",key:"[site.advance_buy_switch]",type:"switch"},{title:"结算时间",value:"2025-01-19 17:33:11",key:"[site.settle_time]",type:"text"}]),k=()=>{z.confirm("确定要重置所有设置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{y(),V.success("表单已重置")}).catch(()=>{})},n=async()=>{try{const e=await C.get("/api/admin/config/timezones",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});e.data.code===200&&(_.value=e.data.data)}catch(e){console.error("获取时区列表失败:",e)}},y=async()=>{try{console.log("获取基础配置参数");const e=await C.get("/api/admin/system-params",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(e.data.code===200){const a=e.data.data,s=c.value.map(t=>{const d=a.find(o=>o.param_key===t.key);if(d){const o={...t};if(t.type==="language"||t.type==="dataSymbols")try{o.value=JSON.parse(d.param_value)}catch(h){console.error(`解析JSON失败: ${d.param_key}`,h)}else o.value=d.param_value;return o}return{...t}});c.value=s}}catch(e){console.error("获取基础配置参数失败:",e)}},f=async()=>{try{const e=c.value.map(s=>{let t=s.value;return(s.type==="language"||s.type==="dataSymbols")&&(t=JSON.stringify(t)),{param_key:s.key,param_value:t}}),a=await C.post("/api/admin/system-params",{params:e},{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.data.code===200){V.success("设置已保存");const s=c.value.find(t=>t.key==="[site.timezone]");s&&s.value!==a.data.data.oldTimezone&&z.alert("您已修改时区设置，需要重启服务器才能生效。","提示",{confirmButtonText:"确定",type:"warning"})}else V.error(a.data.message||"保存失败")}catch(e){console.error("保存基础配置参数失败:",e),V.error("保存失败，请稍后重试")}};return E(()=>{n(),y()}),(e,a)=>{const s=P,t=N,d=F,o=M,h=A,B=L,I=H,O=q;return i(),p("div",ue,[m(I,{data:c.value,class:"param-table"},{default:b(()=>[m(s,{prop:"title",label:"变量标题",width:"150"}),m(s,{prop:"value",label:"变量值"},{default:b(l=>[l.row.type==="text"?(i(),v(t,{key:0,modelValue:l.row.value,"onUpdate:modelValue":u=>l.row.value=u,placeholder:`请输入${l.row.title}`},null,8,["modelValue","onUpdate:modelValue","placeholder"])):l.row.type==="textarea"?(i(),v(t,{key:1,modelValue:l.row.value,"onUpdate:modelValue":u=>l.row.value=u,type:"textarea",rows:3,placeholder:`请输入${l.row.title}`},null,8,["modelValue","onUpdate:modelValue","placeholder"])):l.row.type==="switch"?(i(),v(d,{key:2,modelValue:l.row.value,"onUpdate:modelValue":u=>l.row.value=u,"active-value":"1","inactive-value":"0"},null,8,["modelValue","onUpdate:modelValue"])):l.row.type==="timezone"?(i(),v(B,{key:3,modelValue:l.row.value,"onUpdate:modelValue":u=>l.row.value=u,filterable:"",placeholder:`请选择${l.row.title}`,style:{width:"100%"}},{default:b(()=>[m(h,{label:"常用时区"},{default:b(()=>[(i(!0),p(S,null,T(_.value.common,u=>(i(),v(o,{key:u.id,label:u.name,value:u.value},null,8,["label","value"]))),128))]),_:1}),m(h,{label:"所有时区"},{default:b(()=>[(i(!0),p(S,null,T(_.value.all,u=>(i(),v(o,{key:u.id,label:u.name,value:u.value},null,8,["label","value"]))),128))]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):l.row.type==="language"?(i(),v(Y,{key:4,modelValue:l.row.value,"onUpdate:modelValue":u=>l.row.value=u},null,8,["modelValue","onUpdate:modelValue"])):l.row.type==="dataSymbols"?(i(),v(se,{key:5,modelValue:l.row.value,"onUpdate:modelValue":u=>l.row.value=u},null,8,["modelValue","onUpdate:modelValue"])):D("",!0)]),_:1}),m(s,{prop:"key",label:"变量名",width:"200"})]),_:1},8,["data"]),r("div",ie,[m(O,{onClick:k},{default:b(()=>a[0]||(a[0]=[J("重置")])),_:1}),m(O,{type:"primary",onClick:f},{default:b(()=>a[1]||(a[1]=[J("保存")])),_:1})])])}}}),he=$(re,[["__scopeId","data-v-3cbe8a52"]]);export{he as default};
