{"version": 3, "sources": ["../../src/errors/index.ts"], "sourcesContent": ["export { default as BaseError } from './base-error';\n\nexport { default as DatabaseError } from './database-error';\nexport { default as AggregateError } from './aggregate-error';\nexport { default as AssociationError } from './association-error';\nexport { default as BulkRecordError } from './bulk-record-error';\nexport { default as ConnectionError } from './connection-error';\nexport { default as EagerLoadingError } from './eager-loading-error';\nexport { default as EmptyResultError } from './empty-result-error';\nexport { default as InstanceError } from './instance-error';\nexport { default as OptimisticLockError } from './optimistic-lock-error';\nexport { default as QueryError } from './query-error';\nexport { default as SequelizeScopeError } from './sequelize-scope-error';\nexport {\n  default as ValidationError,\n  ValidationErrorItem,\n  ValidationErrorItemOrigin,\n  ValidationErrorItemType\n} from './validation-error';\n\nexport { default as AccessDeniedError } from './connection/access-denied-error';\nexport { default as ConnectionAcquireTimeoutError } from './connection/connection-acquire-timeout-error';\nexport { default as ConnectionRefusedError } from './connection/connection-refused-error';\nexport { default as ConnectionTimedOutError } from './connection/connection-timed-out-error';\nexport { default as HostNotFoundError } from './connection/host-not-found-error';\nexport { default as HostNotReachableError } from './connection/host-not-reachable-error';\nexport { default as InvalidConnectionError } from './connection/invalid-connection-error';\n\nexport { default as ExclusionConstraintError } from './database/exclusion-constraint-error';\nexport { default as ForeignKeyConstraintError } from './database/foreign-key-constraint-error';\nexport { default as TimeoutError } from './database/timeout-error';\nexport { default as UnknownConstraintError } from './database/unknown-constraint-error';\n\nexport { default as UniqueConstraintError } from './validation/unique-constraint-error';\n\nexport { AsyncQueueError } from '../dialects/mssql/async-queue';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAAqC;AAErC,4BAAyC;AACzC,6BAA0C;AAC1C,+BAA4C;AAC5C,+BAA2C;AAC3C,8BAA2C;AAC3C,iCAA6C;AAC7C,gCAA4C;AAC5C,4BAAyC;AACzC,mCAA+C;AAC/C,yBAAsC;AACtC,mCAA+C;AAC/C,8BAKO;AAEP,iCAA6C;AAC7C,8CAAyD;AACzD,sCAAkD;AAClD,wCAAmD;AACnD,kCAA6C;AAC7C,sCAAiD;AACjD,sCAAkD;AAElD,wCAAoD;AACpD,0CAAqD;AACrD,2BAAwC;AACxC,sCAAkD;AAElD,qCAAiD;AAEjD,yBAAgC;", "names": []}