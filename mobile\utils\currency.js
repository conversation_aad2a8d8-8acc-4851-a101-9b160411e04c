/**
 * 全局货币格式化工具
 * 确保所有金额显示统一格式：小数点后两位，即使是0也显示
 */

/**
 * 格式化货币显示
 * @param {number|string} value - 金额值
 * @param {string} currency - 货币符号，默认为菲律宾比索
 * @param {number} decimals - 小数位数，默认为2
 * @returns {string} 格式化后的货币字符串
 */
export function formatCurrency(value, currency = '₱', decimals = 2) {
  // 处理空值或无效值
  if (value === null || value === undefined || value === '') {
    return `${currency}0.${'0'.repeat(decimals)}`;
  }

  // 转换为数字
  let numValue;
  if (typeof value === 'string') {
    // 移除可能的货币符号和空格
    const cleanValue = value.replace(/[₱$¥€£,\s]/g, '');
    numValue = parseFloat(cleanValue);
  } else {
    numValue = Number(value);
  }

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return `${currency}0.${'0'.repeat(decimals)}`;
  }

  // 格式化为指定小数位数
  const formattedValue = numValue.toFixed(decimals);
  
  return `${currency}${formattedValue}`;
}

/**
 * 格式化金额（不带货币符号）
 * @param {number|string} value - 金额值
 * @param {number} decimals - 小数位数，默认为2
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmount(value, decimals = 2) {
  // 处理空值或无效值
  if (value === null || value === undefined || value === '') {
    return `0.${'0'.repeat(decimals)}`;
  }

  // 转换为数字
  let numValue;
  if (typeof value === 'string') {
    // 移除可能的货币符号和空格
    const cleanValue = value.replace(/[₱$¥€£,\s]/g, '');
    numValue = parseFloat(cleanValue);
  } else {
    numValue = Number(value);
  }

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return `0.${'0'.repeat(decimals)}`;
  }

  // 格式化为指定小数位数
  return numValue.toFixed(decimals);
}

/**
 * 格式化货币显示（带千位分隔符）
 * @param {number|string} value - 金额值
 * @param {string} currency - 货币符号，默认为菲律宾比索
 * @param {number} decimals - 小数位数，默认为2
 * @returns {string} 格式化后的货币字符串
 */
export function formatCurrencyWithCommas(value, currency = '₱', decimals = 2) {
  // 处理空值或无效值
  if (value === null || value === undefined || value === '') {
    return `${currency}0.${'0'.repeat(decimals)}`;
  }

  // 转换为数字
  let numValue;
  if (typeof value === 'string') {
    // 移除可能的货币符号和空格
    const cleanValue = value.replace(/[₱$¥€£,\s]/g, '');
    numValue = parseFloat(cleanValue);
  } else {
    numValue = Number(value);
  }

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return `${currency}0.${'0'.repeat(decimals)}`;
  }

  // 使用toLocaleString格式化，添加千位分隔符
  const formattedValue = numValue.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
  
  return `${currency}${formattedValue}`;
}

/**
 * 菲律宾比索格式化（快捷方法）
 * @param {number|string} value - 金额值
 * @returns {string} 格式化后的菲律宾比索字符串
 */
export function formatPHP(value) {
  return formatCurrency(value, '₱', 2);
}

/**
 * 菲律宾比索格式化（带千位分隔符）
 * @param {number|string} value - 金额值
 * @returns {string} 格式化后的菲律宾比索字符串
 */
export function formatPHPWithCommas(value) {
  return formatCurrencyWithCommas(value, '₱', 2);
}

/**
 * 验证金额格式
 * @param {string} value - 输入的金额字符串
 * @returns {boolean} 是否为有效的金额格式
 */
export function isValidAmount(value) {
  if (!value || typeof value !== 'string') {
    return false;
  }
  
  // 移除货币符号和空格
  const cleanValue = value.replace(/[₱$¥€£,\s]/g, '');
  
  // 检查是否为有效的数字格式
  const numberRegex = /^\d+(\.\d{1,2})?$/;
  return numberRegex.test(cleanValue) && !isNaN(parseFloat(cleanValue));
}

/**
 * 解析金额字符串为数字
 * @param {string} value - 金额字符串
 * @returns {number} 解析后的数字
 */
export function parseAmount(value) {
  if (!value) return 0;
  
  // 移除货币符号和空格
  const cleanValue = value.replace(/[₱$¥€£,\s]/g, '');
  const numValue = parseFloat(cleanValue);
  
  return isNaN(numValue) ? 0 : numValue;
}

// 默认导出主要的格式化函数
export default {
  formatCurrency,
  formatAmount,
  formatCurrencyWithCommas,
  formatPHP,
  formatPHPWithCommas,
  isValidAmount,
  parseAmount
};
