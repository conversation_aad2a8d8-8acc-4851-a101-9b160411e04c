/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                 *//* empty css                    *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                      *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                  *//* empty css                 */import{d as nl,r as b,a as ne,q as we,a5 as $,o as sl,a6 as K,p as g,c as D,b as n,a7 as pe,e as t,w as s,m as il,f as rl,i as ul,a8 as se,a9 as ie,aa as dl,ab as ml,ac as cl,n as v,x as pl,j as V,ad as re,ae as Se,af as vl,V as E,ag as fl,ah as bl,ai as gl,aj as ke,ak as Ml,al as Cl,am as yl,y as _,an as Vl,ao as xl,ap as _l,aq as hl,ar as Al,as as Ll,E as wl,h as Sl,at as kl,au as ve,av as Fl,aw as Ul,ax as Pl,ay as $l,az as Dl,aA as zl,g as x,_ as Tl}from"./index-LncY9lAB.js";import{s as J}from"./request-Cd-6Wde0.js";import{getProjects as Il}from"./project-DkiLnx-I.js";import"./index-t--hEgTQ.js";function ue(L){return J({url:"/api/admin/users",method:"get",params:L}).then(C=>({code:200,message:"获取成功",data:C}))}function Fe(L,C){return J({url:`/api/admin/users/${L}`,method:"put",data:C}).then(y=>y.code===void 0?{code:200,message:"更新成功",data:y}:y)}function El(L,C){return J({url:`/api/admin/users/${L}/balance`,method:"put",data:C}).then(y=>y.code===void 0?{code:200,message:"操作成功",data:y}:y)}function Yl(L,C){return J({url:`/api/admin/users/${L}/gift-investment`,method:"post",data:C}).then(y=>y.code===void 0?{code:200,message:"赠送投资成功",data:y}:y)}function jl(L,C){return J({url:`/api/admin/users/${L}/subordinates`,method:"get",params:C}).then(y=>({code:200,message:"获取成功",data:y}))}const Nl={class:"members-container"},Rl={class:"toolbar"},Hl={class:"toolbar-left"},Bl={class:"toolbar-right"},Gl={key:0,class:"filter-tags-container"},Ol={class:"filter-tags-header"},ql={class:"filter-tags"},Kl={class:"table-wrapper"},Jl={class:"operation-buttons-container"},Ql={class:"pagination-container"},Wl={class:"sub-table-header"},Xl={class:"dialog-footer"},Zl={class:"info-display"},et={class:"info-value"},lt={class:"info-display"},tt={class:"info-value"},at={class:"info-display"},ot={class:"info-value"},nt={class:"info-display"},st={class:"info-value"},it={class:"info-display"},rt={class:"info-value"},ut={class:"dialog-footer"},dt={class:"info-display"},mt={class:"info-value"},ct={class:"info-display"},pt={class:"info-value"},vt={key:0,class:"empty-projects-tip"},ft={class:"info-display"},bt={class:"info-value"},gt={class:"dialog-footer"},Mt=["src"],Ct={class:"dialog-footer"},yt={class:"filter-container"},Vt={class:"filter-section"},xt={class:"section-content"},_t={class:"filter-grid"},ht={class:"filter-item"},At={class:"filter-item"},Lt={class:"filter-item"},wt={class:"filter-item"},St={class:"filter-item"},kt={class:"filter-item"},Ft={class:"filter-item"},Ut={class:"filter-item"},Pt={class:"filter-item"},$t={class:"filter-section"},Dt={class:"section-content"},zt={class:"filter-grid"},Tt={class:"filter-item"},It={class:"range-inputs"},Et={class:"filter-item"},Yt={class:"range-inputs"},jt={class:"filter-item"},Nt={class:"range-inputs"},Rt={class:"filter-item"},Ht={class:"range-inputs"},Bt={class:"filter-item"},Gt={class:"range-inputs"},Ot={class:"filter-item"},qt={class:"range-inputs"},Kt={class:"filter-item"},Jt={class:"range-inputs"},Qt={class:"filter-section"},Wt={class:"section-content"},Xt={class:"filter-grid"},Zt={class:"filter-item"},ea={class:"range-inputs"},la={class:"filter-item"},ta={class:"range-inputs"},aa={class:"filter-item"},oa={class:"range-inputs"},na={class:"filter-item"},sa={class:"range-inputs"},ia={class:"filter-item"},ra={class:"range-inputs"},ua={class:"filter-item"},da={class:"range-inputs"},ma={class:"filter-item"},ca={class:"range-inputs"},pa={class:"filter-item"},va={class:"range-inputs"},fa={class:"filter-section"},ba={class:"section-content"},ga={class:"filter-grid"},Ma={class:"filter-item"},Ca={class:"range-inputs"},ya={class:"filter-item"},Va={class:"filter-item"},xa={class:"filter-item"},_a={class:"filter-footer"},ha=nl({__name:"index",setup(L){const C=b(!1),y=b(!1),Y=b(""),j=b(""),F=b(1),N=b(10),w=b(0),Q=b(1),R=b(10),W=b(0),X=b([]),H=b(!1),Z=b(!1),ee=b(!1),B=b(!1),G=b(!1),z=b("all"),I=b(null),U=b([]);b([]);const le=b([]),k=b([]),p=ne({userId:"",username:"",totalAssetCurrency:"",incomeAccountCurrency:"",depositAccountCurrency:"",accountType:"income",currencyType:"currency",amount:""}),f=ne({userId:"",username:"",balance:"",projectId:"",amount:""}),c=ne({id:"",mobile:"",countryCode:"",realName:"",level:"",frozenAmount:"",avatar:"",gender:"男",status:"正常",allowPurchase:!0}),l=ne({id:"",username:"",directParent:"",agent:"",email:"",promotionCode:"",countryCode:"",realName:"",level:"",levelMin:"",levelMax:"",gender:"",pointsMin:"",pointsMax:"",totalDepositMin:"",totalDepositMax:"",frozenAmountMin:"",frozenAmountMax:"",totalAssetCurrencyMin:"",totalAssetCurrencyMax:"",incomeAccountCurrencyMin:"",incomeAccountCurrencyMax:"",depositAccountCurrencyMin:"",depositAccountCurrencyMax:"",withdrawalAmountMin:"",withdrawalAmountMax:"",totalSubordinatesMin:"",totalSubordinatesMax:"",firstLevelSubordinatesMin:"",firstLevelSubordinatesMax:"",secondLevelSubordinatesMin:"",secondLevelSubordinatesMax:"",thirdLevelSubordinatesMin:"",thirdLevelSubordinatesMax:"",totalCommissionMin:"",totalCommissionMax:"",firstLevelCommissionMin:"",firstLevelCommissionMax:"",secondLevelCommissionMin:"",secondLevelCommissionMax:"",thirdLevelCommissionMin:"",thirdLevelCommissionMax:"",registerTimeRange:[],status:"",allowPurchase:""}),de=we(()=>{let o=0;return l.id&&o++,l.username&&o++,l.directParent&&o++,l.agent&&o++,l.email&&o++,l.promotionCode&&o++,l.countryCode&&o++,l.realName&&o++,l.gender&&o++,l.status&&o++,l.allowPurchase!==""&&o++,(l.levelMin||l.levelMax)&&o++,(l.pointsMin||l.pointsMax)&&o++,(l.totalDepositMin||l.totalDepositMax)&&o++,(l.frozenAmountMin||l.frozenAmountMax)&&o++,(l.totalAssetCurrencyMin||l.totalAssetCurrencyMax)&&o++,(l.incomeAccountCurrencyMin||l.incomeAccountCurrencyMax)&&o++,(l.depositAccountCurrencyMin||l.depositAccountCurrencyMax)&&o++,(l.withdrawalAmountMin||l.withdrawalAmountMax)&&o++,(l.totalSubordinatesMin||l.totalSubordinatesMax)&&o++,(l.firstLevelSubordinatesMin||l.firstLevelSubordinatesMax)&&o++,(l.secondLevelSubordinatesMin||l.secondLevelSubordinatesMax)&&o++,(l.thirdLevelSubordinatesMin||l.thirdLevelSubordinatesMax)&&o++,(l.totalCommissionMin||l.totalCommissionMax)&&o++,(l.firstLevelCommissionMin||l.firstLevelCommissionMax)&&o++,(l.secondLevelCommissionMin||l.secondLevelCommissionMax)&&o++,(l.thirdLevelCommissionMin||l.thirdLevelCommissionMax)&&o++,l.registerTimeRange&&l.registerTimeRange.length===2&&o++,o}),Ue=we(()=>{const o=[];if(l.id&&o.push({key:"id",label:`ID: ${l.id}`}),l.username&&o.push({key:"username",label:`用户名: ${l.username}`}),l.directParent&&o.push({key:"directParent",label:`直属上级: ${l.directParent}`}),l.agent&&o.push({key:"agent",label:`上级代理商: ${l.agent}`}),l.email&&o.push({key:"email",label:`邮箱: ${l.email}`}),l.promotionCode&&o.push({key:"promotionCode",label:`推广码: ${l.promotionCode}`}),l.countryCode&&o.push({key:"countryCode",label:`国家区号: ${l.countryCode}`}),l.realName&&o.push({key:"realName",label:`姓名: ${l.realName}`}),l.gender&&o.push({key:"gender",label:`性别: ${l.gender}`}),l.status&&o.push({key:"status",label:`状态: ${l.status}`}),l.allowPurchase!==""&&o.push({key:"allowPurchase",label:`允许购买: ${l.allowPurchase?"是":"否"}`}),l.levelMin||l.levelMax){const e=l.levelMin||"不限",r=l.levelMax||"不限";o.push({key:"level",label:`等级: ${e} - ${r}`})}if(l.pointsMin||l.pointsMax){const e=l.pointsMin||"不限",r=l.pointsMax||"不限";o.push({key:"points",label:`积分: ${e} - ${r}`})}if(l.totalDepositMin||l.totalDepositMax){const e=l.totalDepositMin||"不限",r=l.totalDepositMax||"不限";o.push({key:"totalDeposit",label:`总充值: ${e} - ${r}`})}if(l.frozenAmountMin||l.frozenAmountMax){const e=l.frozenAmountMin||"不限",r=l.frozenAmountMax||"不限";o.push({key:"frozenAmount",label:`冻结金额: ${e} - ${r}`})}if(l.totalAssetCurrencyMin||l.totalAssetCurrencyMax){const e=l.totalAssetCurrencyMin||"不限",r=l.totalAssetCurrencyMax||"不限";o.push({key:"totalAssetCurrency",label:`总资产: ${e} - ${r}`})}if(l.incomeAccountCurrencyMin||l.incomeAccountCurrencyMax){const e=l.incomeAccountCurrencyMin||"不限",r=l.incomeAccountCurrencyMax||"不限";o.push({key:"incomeAccountCurrency",label:`收入账户: ${e} - ${r}`})}if(l.depositAccountCurrencyMin||l.depositAccountCurrencyMax){const e=l.depositAccountCurrencyMin||"不限",r=l.depositAccountCurrencyMax||"不限";o.push({key:"depositAccountCurrency",label:`充值账户: ${e} - ${r}`})}if(l.withdrawalAmountMin||l.withdrawalAmountMax){const e=l.withdrawalAmountMin||"不限",r=l.withdrawalAmountMax||"不限";o.push({key:"withdrawalAmount",label:`取款金额: ${e} - ${r}`})}if(l.totalSubordinatesMin||l.totalSubordinatesMax){const e=l.totalSubordinatesMin||"不限",r=l.totalSubordinatesMax||"不限";o.push({key:"totalSubordinates",label:`下级: ${e} - ${r}`})}if(l.firstLevelSubordinatesMin||l.firstLevelSubordinatesMax){const e=l.firstLevelSubordinatesMin||"不限",r=l.firstLevelSubordinatesMax||"不限";o.push({key:"firstLevelSubordinates",label:`一级: ${e} - ${r}`})}if(l.secondLevelSubordinatesMin||l.secondLevelSubordinatesMax){const e=l.secondLevelSubordinatesMin||"不限",r=l.secondLevelSubordinatesMax||"不限";o.push({key:"secondLevelSubordinates",label:`二级: ${e} - ${r}`})}if(l.thirdLevelSubordinatesMin||l.thirdLevelSubordinatesMax){const e=l.thirdLevelSubordinatesMin||"不限",r=l.thirdLevelSubordinatesMax||"不限";o.push({key:"thirdLevelSubordinates",label:`三级: ${e} - ${r}`})}if(l.totalCommissionMin||l.totalCommissionMax){const e=l.totalCommissionMin||"不限",r=l.totalCommissionMax||"不限";o.push({key:"totalCommission",label:`总返佣: ${e} - ${r}`})}if(l.firstLevelCommissionMin||l.firstLevelCommissionMax){const e=l.firstLevelCommissionMin||"不限",r=l.firstLevelCommissionMax||"不限";o.push({key:"firstLevelCommission",label:`一级返佣: ${e} - ${r}`})}if(l.secondLevelCommissionMin||l.secondLevelCommissionMax){const e=l.secondLevelCommissionMin||"不限",r=l.secondLevelCommissionMax||"不限";o.push({key:"secondLevelCommission",label:`二级返佣: ${e} - ${r}`})}if(l.thirdLevelCommissionMin||l.thirdLevelCommissionMax){const e=l.thirdLevelCommissionMin||"不限",r=l.thirdLevelCommissionMax||"不限";o.push({key:"thirdLevelCommission",label:`三级返佣: ${e} - ${r}`})}if(l.registerTimeRange&&l.registerTimeRange.length===2){const e=$(l.registerTimeRange[0]).format("YYYY-MM-DD"),r=$(l.registerTimeRange[1]).format("YYYY-MM-DD");o.push({key:"registerTimeRange",label:`注册时间: ${e} - ${r}`})}return o}),te=async()=>{try{const o={count_only:!0,keyword:Y.value||void 0},e=await ue(o);if(e.code===200&&e.data){const r=e.data.total||0;w.value=r}}catch{}};sl(()=>{te(),S(),ge()}),K(Y,(o,e)=>{e&&!o&&(te(),S())}),K(z,()=>{H.value&&O()}),K(j,(o,e)=>{H.value&&O()}),K(()=>f.projectId,o=>{if(o&&o!==""){const e=k.value.find(r=>{const m=r.id.toString(),d=o.toString();return m===d});e?f.amount=e.amount.toFixed(2):f.amount=""}else f.amount=""});const S=()=>{C.value=!0;const o={page:F.value,limit:N.value,keyword:Y.value||void 0,count_only:!1};ue(o).then(e=>{let r=[],m=0;if(e.code===200&&e.data)r=e.data.items||[],m=e.data.total||0;else{g.error(e.message||"获取会员列表失败"),U.value=[],w.value=0,C.value=!1;return}if(!r||r.length===0){U.value=[],w.value=0,C.value=!1;return}const d=r.map(u=>({id:u.id,user_id:u.user_id||"-",username:u.username,directParent:u.parent_username||"-",agent:u.agent_username||"-",email:u.email||"-",promotionCode:u.invite_code||"-",countryCode:u.country_code||"+86",realName:u.name||"-",avatar:u.avatar||"https://via.placeholder.com/30",level:u.level||0,level_name:u.level_name||"",gender:u.gender||"男",points:u.points||0,totalDeposit:parseFloat(u.total_deposit)||0,frozenAmount:parseFloat(u.frozen_amount)||0,totalAssetCurrency:parseFloat(u.total_balance)||parseFloat(u.balance)||0,totalAssetUsdt:parseFloat(u.usdt_balance)||0,incomeAccountCurrency:parseFloat(u.income_balance)||0,incomeAccountUsdt:parseFloat(u.income_usdt_balance)||0,depositAccountCurrency:parseFloat(u.deposit_balance)||0,depositAccountUsdt:parseFloat(u.deposit_usdt_balance)||0,withdrawalAmount:parseFloat(u.withdrawal_amount)||0,totalSubordinates:u.total_subordinates||0,firstLevelSubordinates:u.first_level_subordinates||0,secondLevelSubordinates:u.second_level_subordinates||0,thirdLevelSubordinates:u.third_level_subordinates||0,totalCommission:parseFloat(u.total_commission)||0,firstLevelCommission:parseFloat(u.first_level_commission)||0,secondLevelCommission:parseFloat(u.second_level_commission)||0,thirdLevelCommission:parseFloat(u.third_level_commission)||0,registerTime:u.created_at?$(u.created_at).format("YYYY-MM-DD HH:mm:ss"):"-",status:u.status==="active"?"正常":"禁止购买",allowPurchase:u.status==="active"}));U.value=d,w.value=m}).catch(e=>{g.error("获取会员列表失败，请稍后再试"),U.value=[],w.value=0}).finally(()=>{C.value=!1})},Pe=o=>{X.value=o},fe=o=>{N.value=o,F.value=1,S()},$e=o=>{F.value=o,S()},be=o=>{R.value=o,O()},De=o=>{Q.value=o,O()},ze=o=>{I.value=o,H.value=!0,z.value="all",O()},O=()=>{if(!I.value)return;y.value=!0;const o={page:Q.value,limit:R.value};z.value!=="all"&&(o.level=z.value==="first"?1:z.value==="second"?2:3),j.value&&(o.keyword=j.value),jl(I.value.id,o).then(e=>{if(e.code===200){const{items:r,total:m}=e.data,d=r.map(u=>({id:u.id,username:u.username,parent:u.parent_username||"-",agent:u.agent_username||"-",promotionCode:u.invite_code||"-",level:u.level||0,level_name:u.level_name||"",totalAssetCurrency:parseFloat(u.total_balance)||parseFloat(u.balance)||0,incomeAccountCurrency:parseFloat(u.income_balance)||0,depositAccountCurrency:parseFloat(u.deposit_balance)||0,withdrawalAmount:parseFloat(u.withdrawal_amount)||0,totalSubordinates:u.total_subordinates||0,firstLevelSubordinates:u.first_level_subordinates||0,secondLevelSubordinates:u.second_level_subordinates||0,thirdLevelSubordinates:u.third_level_subordinates||0,totalCommission:parseFloat(u.total_commission)||0,firstLevelCommission:parseFloat(u.first_level_commission)||0,secondLevelCommission:parseFloat(u.second_level_commission)||0,thirdLevelCommission:parseFloat(u.third_level_commission)||0,registerTime:u.created_at?$(u.created_at).format("YYYY-MM-DD HH:mm:ss"):"-",allowPurchase:u.status==="active",status:u.status==="active"?"正常":"禁止购买"}));le.value=d,W.value=m}else g.error(e.message||"获取下级会员列表失败"),le.value=[],W.value=0}).catch(e=>{g.error("获取下级会员列表失败，请稍后再试"),le.value=[],W.value=0}).finally(()=>{y.value=!1})},Te=o=>{I.value=o,p.userId=o.id.toString(),p.username=o.username,p.totalAssetCurrency=(typeof o.totalAssetCurrency=="number"?o.totalAssetCurrency:parseFloat(o.totalAssetCurrency)||0).toFixed(2),p.incomeAccountCurrency=(typeof o.incomeAccountCurrency=="number"?o.incomeAccountCurrency:parseFloat(o.incomeAccountCurrency)||0).toFixed(2),p.depositAccountCurrency=(typeof o.depositAccountCurrency=="number"?o.depositAccountCurrency:parseFloat(o.depositAccountCurrency)||0).toFixed(2),p.accountType="income",p.currencyType="法币",p.amount="",Z.value=!0},Ie=()=>{if(!p.amount){g.warning("请输入金额");return}const o={amount:parseFloat(p.amount),type:parseFloat(p.amount)>=0?"add":"subtract",account_type:p.accountType,remark:`${p.accountType==="income"?"收入账户":"充值账户"} 法币 赠送`};o.type==="subtract"&&(o.amount=Math.abs(o.amount)),El(p.userId,o).then(e=>{e.code===200?(g.success("赠金操作成功"),Z.value=!1,S()):g.error(e.message||"赠金操作失败")}).catch(e=>{g.error("赠金操作失败，请稍后再试")})},Ee=()=>{p.accountType="income",p.currencyType="法币",p.amount=""},ge=()=>{Il({status:1,limit:100}).then(o=>{try{const e=o.items||[];if(e.length>0){const r=e.map(m=>{const d=m.id||0,u=m.type||"项目",h=m.name||"未命名项目",i=typeof m.price=="number"?m.price:typeof m.price=="string"?parseFloat(m.price):0;return{id:d,label:`(${u}) ${h} (${i.toFixed(2)})`,amount:i}});k.value=r}else k.value=[]}catch{k.value=[]}}).catch(o=>{k.value=[]})},Ye=o=>{I.value=o,f.userId=o.id.toString(),f.username=o.username,f.balance=(typeof o.totalAssetCurrency=="number"?o.totalAssetCurrency:parseFloat(o.totalAssetCurrency)||0).toFixed(2),f.projectId="",f.amount="",k.value.length===0&&ge(),ee.value=!0},je=()=>{if(!f.projectId){g.warning("请选择投资项目");return}const o={projectId:parseInt(f.projectId)};Yl(f.userId,o).then(e=>{if(e.code===200)g.success("赠送投资操作成功"),ee.value=!1,S();else throw new Error(e.message||"赠送投资失败")}).catch(e=>{g.error(e.message||"赠送投资失败，请稍后再试")})},Ne=o=>{if(!o){f.amount="0";return}const e=k.value.find(r=>r.id.toString()===o);e?f.amount=e.amount.toFixed(2):f.amount="0"},Re=()=>{f.projectId="",f.amount="0"},Me=o=>{I.value=o,Object.assign(c,{id:o.id.toString(),mobile:o.username,countryCode:o.countryCode.replace("+",""),realName:o.realName,level:o.level.toString(),frozenAmount:o.frozenAmount.toString(),avatar:o.avatar,gender:o.gender,status:o.status,allowPurchase:o.allowPurchase}),B.value=!0},He=o=>{c.avatar=o.url},Be=()=>{const o={name:c.realName,country_code:c.countryCode?`+${c.countryCode}`:"+86",level:parseInt(c.level)||1,frozen_amount:parseFloat(c.frozenAmount)||0,avatar:c.avatar,gender:c.gender,status:c.allowPurchase?"active":"inactive"};Fe(c.id,o).then(e=>{e.code===200?(g.success("会员信息更新成功"),B.value=!1,S()):g.error(e.message||"更新失败")}).catch(e=>{console.error("更新会员信息错误:",e),g.error("更新会员信息失败，请稍后再试")})},Ge=()=>{B.value=!1},Oe=()=>{G.value=!0},Ce=()=>{Object.keys(l).forEach(o=>{const e=o;e==="registerTimeRange"?l[e]=[]:l[e]=""})},ye=()=>{const o={page:1,limit:1e3},e=[];l.id&&e.push(l.id),l.username&&e.push(l.username),l.email&&e.push(l.email),l.promotionCode&&e.push(l.promotionCode),l.realName&&e.push(l.realName),e.length>0&&(o.keyword=e[0]),l.status?o.status=l.status==="正常"?"active":"inactive":l.allowPurchase!==""&&(o.status=l.allowPurchase?"active":"inactive"),F.value=1,G.value=!1;const r={...o,count_only:!0};C.value=!0,ue(r).then(m=>{if(m.code===200&&m.data){const d=m.data.total||0;w.value=d}return ue(o)}).then(m=>{if(m.code===200){const{items:d,total:u}=m.data;let h=d.map(i=>({id:i.id,user_id:i.user_id||"-",username:i.username,directParent:i.parent_username||"-",agent:i.agent_username||"-",email:i.email||"-",promotionCode:i.invite_code||"-",countryCode:i.country_code||"+86",realName:i.name||"-",avatar:i.avatar||"https://via.placeholder.com/30",level:i.level||1,gender:i.gender||"男",points:i.points||0,totalDeposit:parseFloat(i.total_deposit)||0,frozenAmount:parseFloat(i.frozen_amount)||0,totalAssetCurrency:parseFloat(i.total_balance)||parseFloat(i.balance)||0,totalAssetUsdt:parseFloat(i.usdt_balance)||0,incomeAccountCurrency:parseFloat(i.income_balance)||0,incomeAccountUsdt:parseFloat(i.income_usdt_balance)||0,depositAccountCurrency:parseFloat(i.deposit_balance)||0,depositAccountUsdt:parseFloat(i.deposit_usdt_balance)||0,withdrawalAmount:parseFloat(i.withdrawal_amount)||0,totalSubordinates:i.total_subordinates||0,firstLevelSubordinates:i.first_level_subordinates||0,secondLevelSubordinates:i.second_level_subordinates||0,thirdLevelSubordinates:i.third_level_subordinates||0,totalCommission:parseFloat(i.total_commission)||0,firstLevelCommission:parseFloat(i.first_level_commission)||0,secondLevelCommission:parseFloat(i.second_level_commission)||0,thirdLevelCommission:parseFloat(i.third_level_commission)||0,registerTime:i.created_at?$(i.created_at).format("YYYY-MM-DD HH:mm:ss"):"-",allowPurchase:i.status==="active",status:i.status==="active"?"正常":"禁止购买"}));h=Qe(h),U.value=h,w.value=h.length,g.success(`筛选到 ${h.length} 条符合条件的记录`)}else g.error(m.message||"获取会员列表失败"),U.value=[],w.value=0}).catch(m=>{g.error("筛选会员列表失败，请稍后再试"),U.value=[],w.value=0}).finally(()=>{C.value=!1})},me=()=>{F.value=1,te(),S()},qe=()=>{X.value.length===1&&Me(X.value[0])},Ve=o=>{const e={status:o.allowPurchase?"active":"inactive"};o.status=o.allowPurchase?"正常":"禁止购买",C.value=!0,Fe(o.id,e).then(r=>{r.code===200||r.status==="success"?g.success(`${o.allowPurchase?"允许":"禁止"}用户购买产品成功`):(o.allowPurchase=!o.allowPurchase,o.status=o.allowPurchase?"正常":"禁止购买",g.error(r.message||"更新失败"))}).catch(r=>{o.allowPurchase=!o.allowPurchase,o.status=o.allowPurchase?"正常":"禁止购买",g.error("更新用户状态失败，请稍后再试")}).finally(()=>{C.value=!1})},Ke=o=>{o.allowPurchase=!o.allowPurchase,Ve(o)},Je=()=>{c.status=c.allowPurchase?"正常":"禁止购买"};K(()=>c.status,o=>{c.allowPurchase=o==="正常"});const Qe=o=>o.filter(e=>{if(l.directParent&&!e.directParent.includes(l.directParent)||l.agent&&!e.agent.includes(l.agent)||l.countryCode&&!e.countryCode.includes(l.countryCode)||l.gender&&e.gender!==l.gender||l.levelMin&&e.level<parseInt(l.levelMin)||l.levelMax&&e.level>parseInt(l.levelMax)||l.pointsMin&&e.points<parseFloat(l.pointsMin)||l.pointsMax&&e.points>parseFloat(l.pointsMax)||l.totalDepositMin&&e.totalDeposit<parseFloat(l.totalDepositMin)||l.totalDepositMax&&e.totalDeposit>parseFloat(l.totalDepositMax)||l.frozenAmountMin&&e.frozenAmount<parseFloat(l.frozenAmountMin)||l.frozenAmountMax&&e.frozenAmount>parseFloat(l.frozenAmountMax)||l.totalAssetCurrencyMin&&e.totalAssetCurrency<parseFloat(l.totalAssetCurrencyMin)||l.totalAssetCurrencyMax&&e.totalAssetCurrency>parseFloat(l.totalAssetCurrencyMax)||l.incomeAccountCurrencyMin&&e.incomeAccountCurrency<parseFloat(l.incomeAccountCurrencyMin)||l.incomeAccountCurrencyMax&&e.incomeAccountCurrency>parseFloat(l.incomeAccountCurrencyMax)||l.depositAccountCurrencyMin&&e.depositAccountCurrency<parseFloat(l.depositAccountCurrencyMin)||l.depositAccountCurrencyMax&&e.depositAccountCurrency>parseFloat(l.depositAccountCurrencyMax)||l.withdrawalAmountMin&&e.withdrawalAmount<parseFloat(l.withdrawalAmountMin)||l.withdrawalAmountMax&&e.withdrawalAmount>parseFloat(l.withdrawalAmountMax)||l.totalSubordinatesMin&&e.totalSubordinates<parseInt(l.totalSubordinatesMin)||l.totalSubordinatesMax&&e.totalSubordinates>parseInt(l.totalSubordinatesMax)||l.firstLevelSubordinatesMin&&e.firstLevelSubordinates<parseInt(l.firstLevelSubordinatesMin)||l.firstLevelSubordinatesMax&&e.firstLevelSubordinates>parseInt(l.firstLevelSubordinatesMax)||l.secondLevelSubordinatesMin&&e.secondLevelSubordinates<parseInt(l.secondLevelSubordinatesMin)||l.secondLevelSubordinatesMax&&e.secondLevelSubordinates>parseInt(l.secondLevelSubordinatesMax)||l.thirdLevelSubordinatesMin&&e.thirdLevelSubordinates<parseInt(l.thirdLevelSubordinatesMin)||l.thirdLevelSubordinatesMax&&e.thirdLevelSubordinates>parseInt(l.thirdLevelSubordinatesMax)||l.totalCommissionMin&&e.totalCommission<parseFloat(l.totalCommissionMin)||l.totalCommissionMax&&e.totalCommission>parseFloat(l.totalCommissionMax)||l.firstLevelCommissionMin&&e.firstLevelCommission<parseFloat(l.firstLevelCommissionMin)||l.firstLevelCommissionMax&&e.firstLevelCommission>parseFloat(l.firstLevelCommissionMax)||l.secondLevelCommissionMin&&e.secondLevelCommission<parseFloat(l.secondLevelCommissionMin)||l.secondLevelCommissionMax&&e.secondLevelCommission>parseFloat(l.secondLevelCommissionMax)||l.thirdLevelCommissionMin&&e.thirdLevelCommission<parseFloat(l.thirdLevelCommissionMin)||l.thirdLevelCommissionMax&&e.thirdLevelCommission>parseFloat(l.thirdLevelCommissionMax))return!1;if(l.registerTimeRange&&l.registerTimeRange.length===2){const r=$(e.registerTime),m=$(l.registerTimeRange[0]),d=$(l.registerTimeRange[1]);if(r.isBefore(m)||r.isAfter(d))return!1}return!0}),We=()=>{Ce(),F.value=1,te(),S(),g.success("已清除所有筛选条件")},Xe=()=>{g.info("功能尚未实现")},Ze=o=>{switch(o){case"id":l.id="";break;case"username":l.username="";break;case"directParent":l.directParent="";break;case"agent":l.agent="";break;case"email":l.email="";break;case"promotionCode":l.promotionCode="";break;case"countryCode":l.countryCode="";break;case"realName":l.realName="";break;case"gender":l.gender="";break;case"status":l.status="";break;case"allowPurchase":l.allowPurchase="";break;case"level":l.levelMin="",l.levelMax="";break;case"points":l.pointsMin="",l.pointsMax="";break;case"totalDeposit":l.totalDepositMin="",l.totalDepositMax="";break;case"frozenAmount":l.frozenAmountMin="",l.frozenAmountMax="";break;case"totalAssetCurrency":l.totalAssetCurrencyMin="",l.totalAssetCurrencyMax="";break;case"incomeAccountCurrency":l.incomeAccountCurrencyMin="",l.incomeAccountCurrencyMax="";break;case"depositAccountCurrency":l.depositAccountCurrencyMin="",l.depositAccountCurrencyMax="";break;case"withdrawalAmount":l.withdrawalAmountMin="",l.withdrawalAmountMax="";break;case"totalSubordinates":l.totalSubordinatesMin="",l.totalSubordinatesMax="";break;case"firstLevelSubordinates":l.firstLevelSubordinatesMin="",l.firstLevelSubordinatesMax="";break;case"secondLevelSubordinates":l.secondLevelSubordinatesMin="",l.secondLevelSubordinatesMax="";break;case"thirdLevelSubordinates":l.thirdLevelSubordinatesMin="",l.thirdLevelSubordinatesMax="";break;case"totalCommission":l.totalCommissionMin="",l.totalCommissionMax="";break;case"firstLevelCommission":l.firstLevelCommissionMin="",l.firstLevelCommissionMax="";break;case"secondLevelCommission":l.secondLevelCommissionMin="",l.secondLevelCommissionMax="";break;case"thirdLevelCommission":l.thirdLevelCommissionMin="",l.thirdLevelCommissionMax="";break;case"registerTimeRange":l.registerTimeRange=[];break}ye()};return(o,e)=>{const r=pl,m=il,d=ul,u=bl,h=Vl,i=Cl,el=yl,xe=xl,_e=_l,ll=dl,A=kl,T=hl,he=ml,ae=Ll,tl=Al,q=cl,M=Sl,ce=wl,al=Fl,oe=$l,Ae=Pl,ol=zl,Le=Ml;return x(),D("div",Nl,[n("div",Rl,[n("div",Hl,[t(m,{class:"toolbar-button",type:"default",onClick:S},{default:s(()=>[t(r,null,{default:s(()=>[t(V(re))]),_:1}),e[68]||(e[68]=v("刷新 "))]),_:1}),t(m,{class:"toolbar-button",type:"default",disabled:X.value.length!==1,onClick:qe},{default:s(()=>[t(r,null,{default:s(()=>[t(V(Se))]),_:1}),e[69]||(e[69]=v("编辑 "))]),_:1},8,["disabled"])]),n("div",Bl,[t(d,{modelValue:Y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>Y.value=a),placeholder:"搜索用户名",class:"search-input",onKeyup:rl(me,["enter"]),onBlur:me},null,8,["modelValue"]),t(m,{class:"search-button",type:"primary",onClick:me},{default:s(()=>[t(r,null,{default:s(()=>[t(V(vl))]),_:1})]),_:1}),t(m,{class:"toolbar-button filter-button",type:"default",onClick:Oe},{default:s(()=>[t(r,null,{default:s(()=>[t(V(fl))]),_:1}),e[70]||(e[70]=v("筛选 ")),de.value>0?(x(),E(u,{key:0,value:de.value,class:"filter-badge"},null,8,["value"])):pe("",!0)]),_:1}),t(m,{class:"toolbar-button export-button",type:"default",onClick:Xe},{default:s(()=>[t(r,null,{default:s(()=>[t(V(gl))]),_:1}),e[71]||(e[71]=v("导出 "))]),_:1})])]),de.value>0?(x(),D("div",Gl,[n("div",Ol,[e[73]||(e[73]=n("span",{class:"filter-tags-title"},"当前筛选条件：",-1)),t(m,{size:"small",type:"text",onClick:We,class:"clear-all-btn"},{default:s(()=>e[72]||(e[72]=[v(" 清除所有筛选 ")])),_:1})]),n("div",ql,[(x(!0),D(se,null,ie(Ue.value,a=>(x(),E(h,{key:a.key,closable:"",onClose:P=>Ze(a.key),class:"filter-tag"},{default:s(()=>[v(_(a.label),1)]),_:2},1032,["onClose"]))),128))])])):pe("",!0),t(ll,{class:"table-card"},{default:s(()=>[n("div",Kl,[ke((x(),E(_e,{data:U.value,style:{width:"100%"},border:"",stripe:"","highlight-current-row":"",onSelectionChange:Pe,"cell-style":{whiteSpace:"nowrap",overflow:"visible"},"header-cell-style":{whiteSpace:"nowrap",overflow:"visible"},"show-overflow-tooltip":!1,"table-layout":"auto"},{default:s(()=>[t(i,{type:"selection",width:"40",align:"center",fixed:"left"}),t(i,{prop:"id",label:"ID",align:"center",fixed:"left","min-width":"60"}),t(i,{prop:"user_id",label:"用户ID",align:"center",fixed:"left","min-width":"90"}),t(i,{prop:"username",label:"用户名",align:"center",fixed:"left","min-width":"120"}),t(i,{prop:"directParent",label:"直属上级",align:"center",fixed:"left","min-width":"120"}),t(i,{prop:"agent",label:"上级代理商",align:"center",fixed:"left","min-width":"120"}),t(i,{prop:"email",label:"邮箱",align:"center",fixed:"left"}),t(i,{prop:"promotionCode",label:"推广码",align:"center"}),t(i,{prop:"countryCode",label:"国家区号",align:"center"}),t(i,{prop:"realName",label:"姓名",align:"center"}),t(i,{label:"头像",width:"60",align:"center"},{default:s(a=>[t(el,{size:24,src:a.row.avatar},null,8,["src"])]),_:1}),t(i,{label:"等级",align:"center"},{default:s(a=>[v(_(a.row.level_name?`${a.row.level_name} (${a.row.level})`:a.row.level),1)]),_:1}),t(i,{prop:"gender",label:"性别",align:"center"}),t(i,{prop:"points",label:"积分",align:"center"}),t(i,{prop:"totalDeposit",label:"总充值",align:"center"}),t(i,{prop:"frozenAmount",label:"冻结金额",align:"center"}),t(i,{prop:"totalAssetCurrency",label:"总资产(法币)",align:"center"}),t(i,{prop:"incomeAccountCurrency",label:"收入账户(法币)",align:"center"}),t(i,{prop:"depositAccountCurrency",label:"充值账户(法币)",align:"center"}),t(i,{prop:"withdrawalAmount",label:"取款金额",align:"center"}),t(i,{prop:"totalSubordinates",label:"下级",align:"center"}),t(i,{prop:"firstLevelSubordinates",label:"一级",align:"center"}),t(i,{prop:"secondLevelSubordinates",label:"二级",align:"center"}),t(i,{prop:"thirdLevelSubordinates",label:"三级",align:"center"}),t(i,{prop:"totalCommission",label:"总返佣",align:"center"}),t(i,{prop:"firstLevelCommission",label:"一级返佣",align:"center"}),t(i,{prop:"secondLevelCommission",label:"二级返佣",align:"center"}),t(i,{prop:"thirdLevelCommission",label:"三级返佣",align:"center"}),t(i,{prop:"registerTime",label:"注册时间",align:"center","min-width":"160"}),t(i,{label:"状态","min-width":"100",align:"center"},{default:s(a=>[t(h,{type:a.row.allowPurchase?"success":"warning",size:"small",onClick:P=>Ke(a.row),style:{cursor:"pointer"}},{default:s(()=>[v(_(a.row.allowPurchase?"正常":"禁止购买"),1)]),_:2},1032,["type","onClick"])]),_:1}),t(i,{label:"允许购买","min-width":"80",align:"center"},{default:s(a=>[t(xe,{modelValue:a.row.allowPurchase,"onUpdate:modelValue":P=>a.row.allowPurchase=P,"active-value":!0,"inactive-value":!1,size:"small",onChange:P=>Ve(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(i,{label:"操作",fixed:"right","min-width":"180",align:"center"},{default:s(a=>[n("div",Jl,[t(m,{class:"operation-button",size:"small",type:"primary",onClick:P=>ze(a.row)},{default:s(()=>e[74]||(e[74]=[v(" 下级 ")])),_:2},1032,["onClick"]),t(m,{class:"operation-button",size:"small",type:"success",onClick:P=>Te(a.row)},{default:s(()=>e[75]||(e[75]=[v(" 赠金 ")])),_:2},1032,["onClick"]),t(m,{class:"operation-button",size:"small",type:"warning",onClick:P=>Ye(a.row)},{default:s(()=>e[76]||(e[76]=[v(" 赠投 ")])),_:2},1032,["onClick"]),t(m,{class:"operation-button icon-only",size:"small",type:"default",onClick:P=>Me(a.row)},{default:s(()=>[t(r,null,{default:s(()=>[t(V(Se))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Le,C.value]])])]),_:1}),n("div",Ql,[t(he,{"current-page":F.value,"onUpdate:currentPage":e[1]||(e[1]=a=>F.value=a),"page-size":N.value,"onUpdate:pageSize":e[2]||(e[2]=a=>N.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:w.value,onSizeChange:fe,onCurrentChange:$e,"pager-count":7,background:""},{sizes:s(()=>[t(T,{"model-value":N.value,onChange:fe,class:"custom-page-size"},{default:s(()=>[(x(),D(se,null,ie([10,20,50,100],a=>t(A,{key:a,value:a,label:`${a}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),t(q,{modelValue:H.value,"onUpdate:modelValue":e[7]||(e[7]=a=>H.value=a),title:"下级会员",width:"90%","close-on-click-modal":!0},{footer:s(()=>[n("div",Xl,[t(he,{"current-page":Q.value,"onUpdate:currentPage":e[5]||(e[5]=a=>Q.value=a),"page-size":R.value,"onUpdate:pageSize":e[6]||(e[6]=a=>R.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:W.value,onSizeChange:be,onCurrentChange:De,background:"",class:"sub-pagination"},{sizes:s(()=>[t(T,{"model-value":R.value,onChange:be,class:"custom-page-size"},{default:s(()=>[(x(),D(se,null,ie([10,20,50,100],a=>t(A,{key:a,value:a,label:`${a}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])])]),default:s(()=>[t(tl,{modelValue:z.value,"onUpdate:modelValue":e[3]||(e[3]=a=>z.value=a)},{default:s(()=>[t(ae,{label:"全部",name:"all"}),t(ae,{label:"一级",name:"first"}),t(ae,{label:"二级",name:"second"}),t(ae,{label:"三级",name:"third"})]),_:1},8,["modelValue"]),n("div",Wl,[t(m,{class:"toolbar-button",type:"default"},{default:s(()=>[t(r,null,{default:s(()=>[t(V(re))]),_:1}),e[77]||(e[77]=v("刷新 "))]),_:1}),t(d,{modelValue:j.value,"onUpdate:modelValue":e[4]||(e[4]=a=>j.value=a),placeholder:"搜索用户名",class:"sub-search-input"},null,8,["modelValue"])]),ke((x(),E(_e,{data:le.value,border:"",stripe:"","highlight-current-row":"",style:{width:"100%"},class:"sub-table","cell-style":{whiteSpace:"nowrap",overflow:"visible"},"header-cell-style":{whiteSpace:"nowrap",overflow:"visible"},"show-overflow-tooltip":!1,"table-layout":"auto"},{default:s(()=>[t(i,{prop:"id",label:"ID",align:"center","min-width":"60"}),t(i,{prop:"username",label:"用户名",align:"center","min-width":"120"}),t(i,{prop:"parent",label:"直属上级",align:"center","min-width":"120"}),t(i,{prop:"agent",label:"上级代理商",align:"center","min-width":"120"}),t(i,{prop:"promotionCode",label:"推广码",align:"center"}),t(i,{label:"级别",align:"center"},{default:s(a=>[v(_(a.row.level_name?`${a.row.level_name} (${a.row.level})`:a.row.level),1)]),_:1}),t(i,{prop:"totalAssetCurrency",label:"总资产(法币)",align:"center"}),t(i,{prop:"incomeAccountCurrency",label:"收入账户(法币)",align:"center"}),t(i,{prop:"depositAccountCurrency",label:"充值账户(法币)",align:"center"}),t(i,{prop:"withdrawalAmount",label:"取款金额",align:"center"}),t(i,{prop:"totalSubordinates",label:"下级",align:"center"}),t(i,{prop:"firstLevelSubordinates",label:"一级",align:"center"}),t(i,{prop:"secondLevelSubordinates",label:"二级",align:"center"}),t(i,{prop:"thirdLevelSubordinates",label:"三级",align:"center"}),t(i,{prop:"totalCommission",label:"总返佣",align:"center"}),t(i,{prop:"firstLevelCommission",label:"一级返佣",align:"center"}),t(i,{prop:"secondLevelCommission",label:"二级返佣",align:"center"}),t(i,{prop:"thirdLevelCommission",label:"三级返佣",align:"center"}),t(i,{prop:"registerTime",label:"注册时间",align:"center","min-width":"160"}),t(i,{label:"状态",width:"100",align:"center"},{default:s(a=>[t(h,{type:a.row.allowPurchase?"success":"warning"},{default:s(()=>[v(_(a.row.allowPurchase?"正常":"禁止购买"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])),[[Le,y.value]])]),_:1},8,["modelValue"]),t(q,{modelValue:Z.value,"onUpdate:modelValue":e[10]||(e[10]=a=>Z.value=a),title:"赠金",width:"400px","close-on-click-modal":!0},{footer:s(()=>[n("div",ut,[t(m,{class:"dialog-button",type:"primary",onClick:Ie},{default:s(()=>[t(r,null,{default:s(()=>[t(V(ve))]),_:1}),e[79]||(e[79]=v("确定 "))]),_:1}),t(m,{class:"dialog-button",onClick:Ee},{default:s(()=>[t(r,null,{default:s(()=>[t(V(re))]),_:1}),e[80]||(e[80]=v("重置 "))]),_:1})])]),default:s(()=>[t(ce,{model:p,"label-width":"140px"},{default:s(()=>[t(M,{label:"用户名："},{default:s(()=>[n("div",Zl,[n("span",et,_(p.username),1)])]),_:1}),t(M,{label:"总资产(法币)："},{default:s(()=>[n("div",lt,[n("span",tt,_(p.totalAssetCurrency),1)])]),_:1}),t(M,{label:"收入账户(法币)："},{default:s(()=>[n("div",at,[n("span",ot,_(p.incomeAccountCurrency),1)])]),_:1}),t(M,{label:"充值账户(法币)："},{default:s(()=>[n("div",nt,[n("span",st,_(p.depositAccountCurrency),1)])]),_:1}),t(M,{label:"账户："},{default:s(()=>[t(T,{modelValue:p.accountType,"onUpdate:modelValue":e[8]||(e[8]=a=>p.accountType=a),placeholder:"请选择账户类型"},{default:s(()=>[t(A,{label:"收入账户",value:"income"}),t(A,{label:"充值账户",value:"deposit"})]),_:1},8,["modelValue"])]),_:1}),t(M,{label:"货币："},{default:s(()=>[n("div",it,[n("span",rt,_(p.currencyType||"法币"),1)])]),_:1}),t(M,{label:"金额(赠送/扣除)："},{default:s(()=>[t(d,{modelValue:p.amount,"onUpdate:modelValue":e[9]||(e[9]=a=>p.amount=a),placeholder:"0"},null,8,["modelValue"])]),_:1}),e[78]||(e[78]=n("div",{class:"gift-notice"},"金额>0是赠送，<0是扣除",-1))]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(q,{modelValue:ee.value,"onUpdate:modelValue":e[12]||(e[12]=a=>ee.value=a),title:"赠送投资项目",width:"400px","close-on-click-modal":!0},{footer:s(()=>[n("div",gt,[t(m,{class:"dialog-button",type:"primary",onClick:je},{default:s(()=>[t(r,null,{default:s(()=>[t(V(ve))]),_:1}),e[82]||(e[82]=v("确定 "))]),_:1}),t(m,{class:"dialog-button",onClick:Re},{default:s(()=>[t(r,null,{default:s(()=>[t(V(re))]),_:1}),e[83]||(e[83]=v("重置 "))]),_:1})])]),default:s(()=>[t(ce,{model:f,"label-width":"100px"},{default:s(()=>[t(M,{label:"用户名："},{default:s(()=>[n("div",dt,[n("span",mt,_(f.username),1)])]),_:1}),t(M,{label:"用户余额："},{default:s(()=>[n("div",ct,[n("span",pt,_(f.balance),1)])]),_:1}),t(M,{label:"投资项目："},{default:s(()=>[t(T,{modelValue:f.projectId,"onUpdate:modelValue":e[11]||(e[11]=a=>f.projectId=a),placeholder:"请选择投资项目",onChange:Ne},{default:s(()=>[(x(!0),D(se,null,ie(k.value,a=>(x(),E(A,{key:a.id,label:a.label,value:a.id.toString()},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),k.value.length===0?(x(),D("div",vt," 暂无可用投资项目 ")):pe("",!0)]),_:1}),t(M,{label:"项目价值："},{default:s(()=>[n("div",ft,[n("span",bt,_(f.amount||"0"),1)])]),_:1}),e[81]||(e[81]=n("div",{class:"invest-notice"},"选择投资项目后将自动显示项目价值",-1))]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(q,{modelValue:B.value,"onUpdate:modelValue":e[21]||(e[21]=a=>B.value=a),title:"编辑",width:"420px","close-on-click-modal":!0},{footer:s(()=>[n("div",Ct,[t(m,{class:"dialog-button",type:"primary",onClick:Be},{default:s(()=>[t(r,null,{default:s(()=>[t(V(ve))]),_:1}),e[88]||(e[88]=v("确定 "))]),_:1}),t(m,{class:"dialog-button",onClick:Ge},{default:s(()=>[t(r,null,{default:s(()=>[t(V(Dl))]),_:1}),e[89]||(e[89]=v("取消 "))]),_:1})])]),default:s(()=>[t(ce,{model:c,"label-width":"100px"},{default:s(()=>[t(M,{label:"手机："},{default:s(()=>[t(d,{modelValue:c.mobile,"onUpdate:modelValue":e[13]||(e[13]=a=>c.mobile=a)},null,8,["modelValue"])]),_:1}),t(M,{label:"国家区号："},{default:s(()=>[t(d,{modelValue:c.countryCode,"onUpdate:modelValue":e[14]||(e[14]=a=>c.countryCode=a)},null,8,["modelValue"])]),_:1}),t(M,{label:"姓名："},{default:s(()=>[t(d,{modelValue:c.realName,"onUpdate:modelValue":e[15]||(e[15]=a=>c.realName=a)},null,8,["modelValue"])]),_:1}),t(M,{label:"VIP等级："},{default:s(()=>[t(d,{modelValue:c.level,"onUpdate:modelValue":e[16]||(e[16]=a=>c.level=a)},null,8,["modelValue"])]),_:1}),t(M,{label:"冻结金额："},{default:s(()=>[t(d,{modelValue:c.frozenAmount,"onUpdate:modelValue":e[17]||(e[17]=a=>c.frozenAmount=a)},null,8,["modelValue"])]),_:1}),t(M,{label:"头像："},{default:s(()=>[t(al,{class:"avatar-uploader",action:"/api/upload","show-file-list":!1,"on-success":He},{default:s(()=>[c.avatar?(x(),D("img",{key:0,src:c.avatar,class:"avatar"},null,8,Mt)):(x(),E(r,{key:1,class:"avatar-uploader-icon"},{default:s(()=>[t(V(Ul))]),_:1}))]),_:1})]),_:1}),t(M,{label:"性别："},{default:s(()=>[t(Ae,{modelValue:c.gender,"onUpdate:modelValue":e[18]||(e[18]=a=>c.gender=a)},{default:s(()=>[t(oe,{label:"男"},{default:s(()=>e[84]||(e[84]=[v("男")])),_:1}),t(oe,{label:"女"},{default:s(()=>e[85]||(e[85]=[v("女")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(M,{label:"状态："},{default:s(()=>[t(Ae,{modelValue:c.status,"onUpdate:modelValue":e[19]||(e[19]=a=>c.status=a)},{default:s(()=>[t(oe,{label:"正常"},{default:s(()=>e[86]||(e[86]=[v("正常")])),_:1}),t(oe,{label:"禁止购买"},{default:s(()=>e[87]||(e[87]=[v("禁止购买")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(M,{label:"允许购买："},{default:s(()=>[t(xe,{modelValue:c.allowPurchase,"onUpdate:modelValue":e[20]||(e[20]=a=>c.allowPurchase=a),"active-value":!0,"inactive-value":!1,onChange:Je},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(q,{modelValue:G.value,"onUpdate:modelValue":e[67]||(e[67]=a=>G.value=a),title:"筛选条件",width:"900px","close-on-click-modal":!0,class:"filter-dialog"},{footer:s(()=>[n("div",_a,[t(m,{class:"filter-button",type:"primary",onClick:ye},{default:s(()=>e[138]||(e[138]=[v(" 搜索 ")])),_:1}),t(m,{class:"filter-button",onClick:Ce},{default:s(()=>e[139]||(e[139]=[v(" 重置 ")])),_:1}),t(m,{class:"filter-button",onClick:e[66]||(e[66]=a=>G.value=!1)},{default:s(()=>e[140]||(e[140]=[v(" 取消 ")])),_:1})])]),default:s(()=>[n("div",yt,[n("div",Vt,[e[99]||(e[99]=n("div",{class:"section-header"},[n("div",{class:"section-title"},"基本信息")],-1)),n("div",xt,[n("div",_t,[n("div",ht,[e[90]||(e[90]=n("div",{class:"filter-label"},"ID",-1)),t(d,{modelValue:l.id,"onUpdate:modelValue":e[22]||(e[22]=a=>l.id=a),placeholder:"请输入ID"},null,8,["modelValue"])]),n("div",At,[e[91]||(e[91]=n("div",{class:"filter-label"},"用户名",-1)),t(d,{modelValue:l.username,"onUpdate:modelValue":e[23]||(e[23]=a=>l.username=a),placeholder:"请输入用户名"},null,8,["modelValue"])]),n("div",Lt,[e[92]||(e[92]=n("div",{class:"filter-label"},"直属上级",-1)),t(d,{modelValue:l.directParent,"onUpdate:modelValue":e[24]||(e[24]=a=>l.directParent=a),placeholder:"请输入直属上级"},null,8,["modelValue"])]),n("div",wt,[e[93]||(e[93]=n("div",{class:"filter-label"},"上级代理商",-1)),t(d,{modelValue:l.agent,"onUpdate:modelValue":e[25]||(e[25]=a=>l.agent=a),placeholder:"请输入上级代理商"},null,8,["modelValue"])]),n("div",St,[e[94]||(e[94]=n("div",{class:"filter-label"},"邮箱",-1)),t(d,{modelValue:l.email,"onUpdate:modelValue":e[26]||(e[26]=a=>l.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),n("div",kt,[e[95]||(e[95]=n("div",{class:"filter-label"},"推广码",-1)),t(d,{modelValue:l.promotionCode,"onUpdate:modelValue":e[27]||(e[27]=a=>l.promotionCode=a),placeholder:"请输入推广码"},null,8,["modelValue"])]),n("div",Ft,[e[96]||(e[96]=n("div",{class:"filter-label"},"国家区号",-1)),t(d,{modelValue:l.countryCode,"onUpdate:modelValue":e[28]||(e[28]=a=>l.countryCode=a),placeholder:"请输入国家区号"},null,8,["modelValue"])]),n("div",Ut,[e[97]||(e[97]=n("div",{class:"filter-label"},"姓名",-1)),t(d,{modelValue:l.realName,"onUpdate:modelValue":e[29]||(e[29]=a=>l.realName=a),placeholder:"请输入姓名"},null,8,["modelValue"])]),n("div",Pt,[e[98]||(e[98]=n("div",{class:"filter-label"},"性别",-1)),t(T,{modelValue:l.gender,"onUpdate:modelValue":e[30]||(e[30]=a=>l.gender=a),placeholder:"请选择",clearable:""},{default:s(()=>[t(A,{label:"男",value:"男"}),t(A,{label:"女",value:"女"})]),_:1},8,["modelValue"])])])])]),n("div",$t,[e[114]||(e[114]=n("div",{class:"section-header"},[n("div",{class:"section-title"},"资产信息")],-1)),n("div",Dt,[n("div",zt,[n("div",Tt,[e[101]||(e[101]=n("div",{class:"filter-label"},"总充值",-1)),n("div",It,[t(d,{modelValue:l.totalDepositMin,"onUpdate:modelValue":e[31]||(e[31]=a=>l.totalDepositMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[100]||(e[100]=n("span",null,"-",-1)),t(d,{modelValue:l.totalDepositMax,"onUpdate:modelValue":e[32]||(e[32]=a=>l.totalDepositMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",Et,[e[103]||(e[103]=n("div",{class:"filter-label"},"冻结金额",-1)),n("div",Yt,[t(d,{modelValue:l.frozenAmountMin,"onUpdate:modelValue":e[33]||(e[33]=a=>l.frozenAmountMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[102]||(e[102]=n("span",null,"-",-1)),t(d,{modelValue:l.frozenAmountMax,"onUpdate:modelValue":e[34]||(e[34]=a=>l.frozenAmountMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",jt,[e[105]||(e[105]=n("div",{class:"filter-label"},"总资产(法币)",-1)),n("div",Nt,[t(d,{modelValue:l.totalAssetCurrencyMin,"onUpdate:modelValue":e[35]||(e[35]=a=>l.totalAssetCurrencyMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[104]||(e[104]=n("span",null,"-",-1)),t(d,{modelValue:l.totalAssetCurrencyMax,"onUpdate:modelValue":e[36]||(e[36]=a=>l.totalAssetCurrencyMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",Rt,[e[107]||(e[107]=n("div",{class:"filter-label"},"收入账户(法币)",-1)),n("div",Ht,[t(d,{modelValue:l.incomeAccountCurrencyMin,"onUpdate:modelValue":e[37]||(e[37]=a=>l.incomeAccountCurrencyMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[106]||(e[106]=n("span",null,"-",-1)),t(d,{modelValue:l.incomeAccountCurrencyMax,"onUpdate:modelValue":e[38]||(e[38]=a=>l.incomeAccountCurrencyMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",Bt,[e[109]||(e[109]=n("div",{class:"filter-label"},"充值账户(法币)",-1)),n("div",Gt,[t(d,{modelValue:l.depositAccountCurrencyMin,"onUpdate:modelValue":e[39]||(e[39]=a=>l.depositAccountCurrencyMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[108]||(e[108]=n("span",null,"-",-1)),t(d,{modelValue:l.depositAccountCurrencyMax,"onUpdate:modelValue":e[40]||(e[40]=a=>l.depositAccountCurrencyMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",Ot,[e[111]||(e[111]=n("div",{class:"filter-label"},"取款金额",-1)),n("div",qt,[t(d,{modelValue:l.withdrawalAmountMin,"onUpdate:modelValue":e[41]||(e[41]=a=>l.withdrawalAmountMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[110]||(e[110]=n("span",null,"-",-1)),t(d,{modelValue:l.withdrawalAmountMax,"onUpdate:modelValue":e[42]||(e[42]=a=>l.withdrawalAmountMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",Kt,[e[113]||(e[113]=n("div",{class:"filter-label"},"积分",-1)),n("div",Jt,[t(d,{modelValue:l.pointsMin,"onUpdate:modelValue":e[43]||(e[43]=a=>l.pointsMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[112]||(e[112]=n("span",null,"-",-1)),t(d,{modelValue:l.pointsMax,"onUpdate:modelValue":e[44]||(e[44]=a=>l.pointsMax=a),placeholder:"最大值"},null,8,["modelValue"])])])])])]),n("div",Qt,[e[131]||(e[131]=n("div",{class:"section-header"},[n("div",{class:"section-title"},"下级信息")],-1)),n("div",Wt,[n("div",Xt,[n("div",Zt,[e[116]||(e[116]=n("div",{class:"filter-label"},"下级",-1)),n("div",ea,[t(d,{modelValue:l.totalSubordinatesMin,"onUpdate:modelValue":e[45]||(e[45]=a=>l.totalSubordinatesMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[115]||(e[115]=n("span",null,"-",-1)),t(d,{modelValue:l.totalSubordinatesMax,"onUpdate:modelValue":e[46]||(e[46]=a=>l.totalSubordinatesMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",la,[e[118]||(e[118]=n("div",{class:"filter-label"},"一级",-1)),n("div",ta,[t(d,{modelValue:l.firstLevelSubordinatesMin,"onUpdate:modelValue":e[47]||(e[47]=a=>l.firstLevelSubordinatesMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[117]||(e[117]=n("span",null,"-",-1)),t(d,{modelValue:l.firstLevelSubordinatesMax,"onUpdate:modelValue":e[48]||(e[48]=a=>l.firstLevelSubordinatesMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",aa,[e[120]||(e[120]=n("div",{class:"filter-label"},"二级",-1)),n("div",oa,[t(d,{modelValue:l.secondLevelSubordinatesMin,"onUpdate:modelValue":e[49]||(e[49]=a=>l.secondLevelSubordinatesMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[119]||(e[119]=n("span",null,"-",-1)),t(d,{modelValue:l.secondLevelSubordinatesMax,"onUpdate:modelValue":e[50]||(e[50]=a=>l.secondLevelSubordinatesMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",na,[e[122]||(e[122]=n("div",{class:"filter-label"},"三级",-1)),n("div",sa,[t(d,{modelValue:l.thirdLevelSubordinatesMin,"onUpdate:modelValue":e[51]||(e[51]=a=>l.thirdLevelSubordinatesMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[121]||(e[121]=n("span",null,"-",-1)),t(d,{modelValue:l.thirdLevelSubordinatesMax,"onUpdate:modelValue":e[52]||(e[52]=a=>l.thirdLevelSubordinatesMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",ia,[e[124]||(e[124]=n("div",{class:"filter-label"},"总返佣",-1)),n("div",ra,[t(d,{modelValue:l.totalCommissionMin,"onUpdate:modelValue":e[53]||(e[53]=a=>l.totalCommissionMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[123]||(e[123]=n("span",null,"-",-1)),t(d,{modelValue:l.totalCommissionMax,"onUpdate:modelValue":e[54]||(e[54]=a=>l.totalCommissionMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",ua,[e[126]||(e[126]=n("div",{class:"filter-label"},"一级返佣",-1)),n("div",da,[t(d,{modelValue:l.firstLevelCommissionMin,"onUpdate:modelValue":e[55]||(e[55]=a=>l.firstLevelCommissionMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[125]||(e[125]=n("span",null,"-",-1)),t(d,{modelValue:l.firstLevelCommissionMax,"onUpdate:modelValue":e[56]||(e[56]=a=>l.firstLevelCommissionMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",ma,[e[128]||(e[128]=n("div",{class:"filter-label"},"二级返佣",-1)),n("div",ca,[t(d,{modelValue:l.secondLevelCommissionMin,"onUpdate:modelValue":e[57]||(e[57]=a=>l.secondLevelCommissionMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[127]||(e[127]=n("span",null,"-",-1)),t(d,{modelValue:l.secondLevelCommissionMax,"onUpdate:modelValue":e[58]||(e[58]=a=>l.secondLevelCommissionMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",pa,[e[130]||(e[130]=n("div",{class:"filter-label"},"三级返佣",-1)),n("div",va,[t(d,{modelValue:l.thirdLevelCommissionMin,"onUpdate:modelValue":e[59]||(e[59]=a=>l.thirdLevelCommissionMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[129]||(e[129]=n("span",null,"-",-1)),t(d,{modelValue:l.thirdLevelCommissionMax,"onUpdate:modelValue":e[60]||(e[60]=a=>l.thirdLevelCommissionMax=a),placeholder:"最大值"},null,8,["modelValue"])])])])])]),n("div",fa,[e[137]||(e[137]=n("div",{class:"section-header"},[n("div",{class:"section-title"},"其他信息")],-1)),n("div",ba,[n("div",ga,[n("div",Ma,[e[133]||(e[133]=n("div",{class:"filter-label"},"等级",-1)),n("div",Ca,[t(d,{modelValue:l.levelMin,"onUpdate:modelValue":e[61]||(e[61]=a=>l.levelMin=a),placeholder:"最小值"},null,8,["modelValue"]),e[132]||(e[132]=n("span",null,"-",-1)),t(d,{modelValue:l.levelMax,"onUpdate:modelValue":e[62]||(e[62]=a=>l.levelMax=a),placeholder:"最大值"},null,8,["modelValue"])])]),n("div",ya,[e[134]||(e[134]=n("div",{class:"filter-label"},"注册时间",-1)),t(ol,{modelValue:l.registerTimeRange,"onUpdate:modelValue":e[63]||(e[63]=a=>l.registerTimeRange=a),type:"datetimerange","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),n("div",Va,[e[135]||(e[135]=n("div",{class:"filter-label"},"状态",-1)),t(T,{modelValue:l.status,"onUpdate:modelValue":e[64]||(e[64]=a=>l.status=a),placeholder:"请选择",clearable:""},{default:s(()=>[t(A,{label:"正常",value:"正常"}),t(A,{label:"禁用",value:"禁用"})]),_:1},8,["modelValue"])]),n("div",xa,[e[136]||(e[136]=n("div",{class:"filter-label"},"允许购买",-1)),t(T,{modelValue:l.allowPurchase,"onUpdate:modelValue":e[65]||(e[65]=a=>l.allowPurchase=a),placeholder:"请选择",clearable:""},{default:s(()=>[t(A,{label:"是",value:!0}),t(A,{label:"否",value:!1})]),_:1},8,["modelValue"])])])])])])]),_:1},8,["modelValue"])])}}}),Ja=Tl(ha,[["__scopeId","data-v-8a0a9d21"]]);export{Ja as default};
