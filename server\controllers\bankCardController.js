const { BankCard, User, Bank, BankChannelMapping } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 管理员端 - 获取银行卡列表
exports.getBankCards = async (req, res) => {
  try {
    console.log('获取银行卡列表请求参数:', req.query);
    const { page = 1, limit = 10, keyword, card_type, status, username, user_id_str } = req.query;

    // 构建查询条件
    const where = {};

    // 构建用户关联查询条件
    const userWhere = {};
    let userRequired = false;

    // 处理关键词搜索
    if (keyword) {
      where[Op.or] = [
        { bank_name: { [Op.like]: `%${keyword}%` } },
        { card_holder: { [Op.like]: `%${keyword}%` } },
        { card_number: { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 处理用户名搜索
    if (username) {
      userWhere.username = { [Op.like]: `%${username}%` };
      userRequired = true;
    }

    // 处理用户ID搜索
    if (user_id_str) {
      userWhere.user_id = { [Op.like]: `%${user_id_str}%` };
      userRequired = true;
    }

    if (card_type) {
      where.card_type = card_type;
    }

    if (status !== undefined) {
      where.status = status === 'true';
    }

    console.log('查询条件:', { where, userWhere, userRequired });

    // 分页查询
    const offset = (page - 1) * limit;

    console.log('分页参数:', { page, limit, offset });

    const { count, rows } = await BankCard.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'name', 'user_id'],
          where: Object.keys(userWhere).length > 0 ? userWhere : undefined,
          required: userRequired
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    console.log('查询结果:', { count, rowsLength: rows.length });

    const response = {
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    };

    console.log('响应数据:', JSON.stringify(response));

    return res.status(200).json(response);
  } catch (error) {
    console.error('获取银行卡列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取银行卡详情
exports.getBankCard = async (req, res) => {
  try {
    const { id } = req.params;

    const bankCard = await BankCard.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'name', 'user_id'],
          required: false
        }
      ]
    });

    if (!bankCard) {
      return res.status(404).json({
        code: 404,
        message: '银行卡不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: bankCard
    });
  } catch (error) {
    console.error('获取银行卡详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 创建银行卡
exports.createBankCard = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { user_id, bank_name, card_number, card_holder, bank_code, card_type, daily_limit, status } = req.body;

    // 验证请求数据
    if (!bank_name || !card_number || !card_holder || !card_type) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '银行名称、卡号、持卡人和卡类型不能为空',
        data: null
      });
    }



    // 创建银行卡
    const bankCard = await BankCard.create({
      user_id,
      bank_name,
      card_number,
      card_holder,
      bank_code,

      card_type,
      daily_limit,
      status: status !== undefined ? status : true
    }, { transaction });

    await transaction.commit();

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: bankCard
    });
  } catch (error) {
    await transaction.rollback();
    console.error('创建银行卡错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 更新银行卡
exports.updateBankCard = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const { bank_name, card_number, card_holder, bank_code, daily_limit, status } = req.body;

    // 查找银行卡
    const bankCard = await BankCard.findByPk(id);
    if (!bankCard) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '银行卡不存在',
        data: null
      });
    }



    // 更新银行卡信息
    if (bank_name) bankCard.bank_name = bank_name;
    if (card_number) bankCard.card_number = card_number;
    if (card_holder) bankCard.card_holder = card_holder;
    if (bank_code !== undefined) bankCard.bank_code = bank_code;
    if (is_default !== undefined) bankCard.is_default = is_default;
    if (daily_limit !== undefined) bankCard.daily_limit = daily_limit;
    if (status !== undefined) bankCard.status = status;

    await bankCard.save({ transaction });
    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: bankCard
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新银行卡错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 删除银行卡
exports.deleteBankCard = async (req, res) => {
  try {
    const { id } = req.params;

    // 查找银行卡
    const bankCard = await BankCard.findByPk(id);
    if (!bankCard) {
      return res.status(404).json({
        code: 404,
        message: '银行卡不存在',
        data: null
      });
    }

    // 删除银行卡
    await bankCard.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除银行卡错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 批量删除银行卡
exports.batchDeleteBankCards = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '请选择要删除的银行卡',
        data: null
      });
    }

    // 查找所有要删除的银行卡
    const bankCards = await BankCard.findAll({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });

    if (bankCards.length === 0) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到要删除的银行卡',
        data: null
      });
    }

    // 删除银行卡
    await BankCard.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      },
      transaction
    });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('批量删除银行卡错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户端 - 获取用户银行卡列表
exports.getUserBankCards = async (req, res) => {
  try {
    const userId = req.user.id;

    const bankCards = await BankCard.findAll({
      where: {
        user_id: userId,
        card_type: 'user',
        status: true
      },
      order: [
        ['created_at', 'DESC']
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: bankCards
    });
  } catch (error) {
    console.error('获取用户银行卡列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户端 - 添加银行卡
exports.addUserBankCard = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const userId = req.user.id;
    const { bank_id, card_number, card_holder } = req.body;

    // 验证请求数据
    if (!bank_id || !card_number || !card_holder) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '银行ID、卡号和持卡人不能为空',
        data: null
      });
    }

    // 查找银行
    const bank = await Bank.findOne({
      where: { id: bank_id, status: true }
    });

    if (!bank) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '银行不存在或已禁用',
        data: null
      });
    }

    // 创建银行卡
    const bankCard = await BankCard.create({
      user_id: userId,
      bank_id,
      bank_name: bank.name, // 保存银行名称，方便显示
      card_number,
      card_holder,
      card_type: 'user',
      status: true
    }, { transaction });

    await transaction.commit();

    // 查询银行卡详情，包含银行信息
    const bankCardWithBank = await BankCard.findByPk(bankCard.id, {
      include: [
        {
          model: Bank,
          as: 'bank',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(201).json({
      code: 201,
      message: '添加成功',
      data: bankCardWithBank
    });
  } catch (error) {
    await transaction.rollback();
    console.error('添加用户银行卡错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户端 - 更新银行卡
exports.updateUserBankCard = async (req, res) => {
  const transaction = await sequelize.transaction();
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { bank_id, card_number, card_holder } = req.body;

    // 查找银行卡
    const bankCard = await BankCard.findOne({
      where: {
        id,
        user_id: userId,
        card_type: 'user'
      }
    });

    if (!bankCard) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '银行卡不存在',
        data: null
      });
    }

    // 如果更新了银行ID，需要查找银行
    if (bank_id && bank_id !== bankCard.bank_id) {
      const bank = await Bank.findOne({
        where: { id: bank_id, status: true }
      });

      if (!bank) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '银行不存在或已禁用',
          data: null
        });
      }

      bankCard.bank_id = bank_id;
      bankCard.bank_name = bank.name;
    }

    // 更新银行卡信息
    if (card_number) bankCard.card_number = card_number;
    if (card_holder) bankCard.card_holder = card_holder;

    await bankCard.save({ transaction });
    await transaction.commit();

    // 查询银行卡详情，包含银行信息
    const bankCardWithBank = await BankCard.findByPk(bankCard.id, {
      include: [
        {
          model: Bank,
          as: 'bank',
          attributes: ['id', 'name']
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: bankCardWithBank
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新用户银行卡错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 用户端 - 删除银行卡
exports.deleteUserBankCard = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // 查找银行卡
    const bankCard = await BankCard.findOne({
      where: {
        id,
        user_id: userId,
        card_type: 'user'
      }
    });

    if (!bankCard) {
      return res.status(404).json({
        code: 404,
        message: '银行卡不存在',
        data: null
      });
    }

    // 删除银行卡
    await bankCard.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除用户银行卡错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取银行列表
exports.getBanks = async (req, res) => {
  try {
    const { payment_channel_id } = req.query;

    // 从数据库获取银行列表
    const banks = await Bank.findAll({
      where: { status: true },
      order: [['name', 'ASC']]
    });

    // 如果指定了支付通道ID，则获取对应的银行编码
    if (payment_channel_id) {
      // 查询银行编码映射
      const bankIds = banks.map(bank => bank.id);
      const bankMappings = await BankChannelMapping.findAll({
        where: {
          bank_id: { [Op.in]: bankIds },
          payment_channel_id,
          status: true
        }
      });

      // 将银行代收方式添加到银行信息中
      const banksWithCode = banks.map(bank => {
        const mapping = bankMappings.find(m => m.bank_id === bank.id);
        return {
          id: bank.id,
          name: bank.name,
          payin_method: mapping ? mapping.payin_method : null
        };
      });

      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: banksWithCode
      });
    } else {
      // 不需要银行编码，直接返回银行列表
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: banks
      });
    }
  } catch (error) {
    console.error('获取银行列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
