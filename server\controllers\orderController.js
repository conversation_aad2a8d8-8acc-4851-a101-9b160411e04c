const { Order, Project, User } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

// 生成订单号
const generateOrderNo = () => {
  const now = new Date();
  const year = now.getFullYear().toString();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `FOX${year}${month}${day}${hours}${minutes}${seconds}${random}`;
};

// 管理员端 - 获取订单列表
exports.getOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, status, start_date, end_date } = req.query;
    
    // 构建查询条件
    const where = {};
    
    if (keyword) {
      where[Op.or] = [
        { order_no: { [Op.like]: `%${keyword}%` } }
      ];
    }
    
    if (status) {
      where.status = status;
    }
    
    if (start_date && end_date) {
      where.createdAt = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      where.createdAt = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      where.createdAt = {
        [Op.lte]: new Date(end_date)
      };
    }
    
    // 分页查询
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Order.findAndCountAll({
      where,
      include: [
        {
          association: 'user',
          attributes: ['id', 'username', 'name', 'phone']
        },
        {
          association: 'project',
          attributes: ['id', 'title', 'type', 'expected_return', 'duration']
        }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit)
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取订单列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取订单详情
exports.getOrder = async (req, res) => {
  try {
    const { id } = req.params;
    
    const order = await Order.findByPk(id, {
      include: [
        {
          association: 'user',
          attributes: ['id', 'username', 'name', 'phone', 'email']
        },
        {
          association: 'project',
          attributes: [
            'id', 'title', 'type', 'expected_return', 'duration',
            'start_time', 'end_time', 'status'
          ]
        }
      ]
    });
    
    if (!order) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: order
    });
  } catch (error) {
    console.error('获取订单详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 更新订单状态
exports.updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, actual_return } = req.body;
    
    // 验证请求数据
    if (!status) {
      return res.status(400).json({
        code: 400,
        message: '状态不能为空',
        data: null
      });
    }
    
    // 查找订单
    const order = await Order.findByPk(id, {
      include: [
        {
          association: 'user'
        },
        {
          association: 'project'
        }
      ]
    });
    
    if (!order) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在',
        data: null
      });
    }
    
    // 开启事务
    const transaction = await sequelize.transaction();
    
    try {
      // 更新订单状态
      order.status = status;
      
      // 根据状态执行不同操作
      if (status === 'completed') {
        // 设置实际收益
        if (actual_return !== undefined) {
          order.actual_return = actual_return;
        }
        
        // 设置结束时间
        order.end_time = new Date();
        
        // 将收益加入用户余额
        const returnAmount = parseFloat(actual_return || order.expected_return);
        const totalReturn = parseFloat(order.amount) + returnAmount;
        
        order.user.balance = parseFloat(order.user.balance) + totalReturn;
        await order.user.save({ transaction });
        
        // TODO: 记录余额变动日志
      } else if (status === 'cancelled' || status === 'refunded') {
        // 如果订单已支付，退还用户余额
        if (order.status === 'paid') {
          order.user.balance = parseFloat(order.user.balance) + parseFloat(order.amount);
          await order.user.save({ transaction });
          
          // 更新项目已募集金额
          order.project.current_amount = parseFloat(order.project.current_amount) - parseFloat(order.amount);
          await order.project.save({ transaction });
          
          // TODO: 记录余额变动日志
        }
      }
      
      await order.save({ transaction });
      
      // 提交事务
      await transaction.commit();
      
      return res.status(200).json({
        code: 200,
        message: '更新成功',
        data: order
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('更新订单状态错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 创建订单
exports.createOrder = async (req, res) => {
  try {
    const { project_id, amount, payment_method } = req.body;
    const user = req.user;
    
    // 验证请求数据
    if (!project_id || !amount) {
      return res.status(400).json({
        code: 400,
        message: '项目ID和金额不能为空',
        data: null
      });
    }
    
    // 查找项目
    const project = await Project.findByPk(project_id);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }
    
    // 检查项目状态
    if (project.status !== 'active') {
      return res.status(400).json({
        code: 400,
        message: '项目不可投资',
        data: null
      });
    }
    
    // 检查项目时间
    const now = new Date();
    if (now < new Date(project.start_time) || now > new Date(project.end_time)) {
      return res.status(400).json({
        code: 400,
        message: '不在项目投资时间范围内',
        data: null
      });
    }
    
    // 检查投资金额
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return res.status(400).json({
        code: 400,
        message: '投资金额必须大于0',
        data: null
      });
    }
    
    if (numAmount < parseFloat(project.min_investment)) {
      return res.status(400).json({
        code: 400,
        message: `投资金额不能低于最小投资额${project.min_investment}`,
        data: null
      });
    }
    
    if (project.max_investment && numAmount > parseFloat(project.max_investment)) {
      return res.status(400).json({
        code: 400,
        message: `投资金额不能高于最大投资额${project.max_investment}`,
        data: null
      });
    }
    
    // 检查项目剩余可投资金额
    const remainingAmount = parseFloat(project.total_amount) - parseFloat(project.current_amount);
    if (numAmount > remainingAmount) {
      return res.status(400).json({
        code: 400,
        message: `超出项目剩余可投资金额${remainingAmount}`,
        data: null
      });
    }
    
    // 计算预期收益
    const expectedReturn = (numAmount * parseFloat(project.expected_return) / 100 * project.duration / 365).toFixed(2);
    
    // 生成订单号
    const orderNo = generateOrderNo();
    
    // 创建订单
    const order = await Order.create({
      order_no: orderNo,
      user_id: user.id,
      project_id: project.id,
      amount: numAmount,
      expected_return: expectedReturn,
      status: 'pending',
      payment_method: payment_method || null
    });
    
    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: order
    });
  } catch (error) {
    console.error('创建订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 支付订单
exports.payOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const { payment_method } = req.body;
    const user = req.user;
    
    // 验证请求数据
    if (!payment_method) {
      return res.status(400).json({
        code: 400,
        message: '支付方式不能为空',
        data: null
      });
    }
    
    // 查找订单
    const order = await Order.findOne({
      where: {
        id,
        user_id: user.id
      },
      include: [
        {
          association: 'project'
        }
      ]
    });
    
    if (!order) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在',
        data: null
      });
    }
    
    // 检查订单状态
    if (order.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: '订单状态不正确',
        data: null
      });
    }
    
    // 检查项目状态
    if (order.project.status !== 'active') {
      return res.status(400).json({
        code: 400,
        message: '项目不可投资',
        data: null
      });
    }
    
    // 开启事务
    const transaction = await sequelize.transaction();
    
    try {
      // 处理支付
      if (payment_method === 'balance') {
        // 检查余额
        if (parseFloat(user.balance) < parseFloat(order.amount)) {
          await transaction.rollback();
          return res.status(400).json({
            code: 400,
            message: '余额不足',
            data: null
          });
        }
        
        // 扣除余额
        user.balance = parseFloat(user.balance) - parseFloat(order.amount);
        await user.save({ transaction });
        
        // TODO: 记录余额变动日志
      } else {
        // 其他支付方式，这里简化处理，直接认为支付成功
        // 实际应用中需要对接第三方支付接口
      }
      
      // 更新订单状态
      order.status = 'paid';
      order.payment_method = payment_method;
      order.payment_time = new Date();
      order.start_time = new Date();
      
      // 计算结束时间
      const endTime = new Date();
      endTime.setDate(endTime.getDate() + order.project.duration);
      order.end_time = endTime;
      
      await order.save({ transaction });
      
      // 更新项目已募集金额
      order.project.current_amount = parseFloat(order.project.current_amount) + parseFloat(order.amount);
      await order.project.save({ transaction });
      
      // 提交事务
      await transaction.commit();
      
      return res.status(200).json({
        code: 200,
        message: '支付成功',
        data: order
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('支付订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 获取用户订单列表
exports.getUserOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const user = req.user;
    
    // 构建查询条件
    const where = { user_id: user.id };
    
    if (status) {
      where.status = status;
    }
    
    // 分页查询
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Order.findAndCountAll({
      where,
      include: [
        {
          association: 'project',
          attributes: ['id', 'title', 'type', 'expected_return', 'duration']
        }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(limit)
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取用户订单列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 获取订单详情
exports.getUserOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const order = await Order.findOne({
      where: {
        id,
        user_id: user.id
      },
      include: [
        {
          association: 'project',
          attributes: [
            'id', 'title', 'type', 'expected_return', 'duration',
            'start_time', 'end_time', 'status'
          ]
        }
      ]
    });
    
    if (!order) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: order
    });
  } catch (error) {
    console.error('获取订单详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 移动端 - 取消订单
exports.cancelOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    // 查找订单
    const order = await Order.findOne({
      where: {
        id,
        user_id: user.id
      }
    });
    
    if (!order) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在',
        data: null
      });
    }
    
    // 检查订单状态
    if (order.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: '只能取消待支付的订单',
        data: null
      });
    }
    
    // 更新订单状态
    order.status = 'cancelled';
    await order.save();
    
    return res.status(200).json({
      code: 200,
      message: '取消成功',
      data: order
    });
  } catch (error) {
    console.error('取消订单错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
