'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 添加 type 字段
    await queryInterface.addColumn('commissions', 'type', {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: '佣金类型'
    });

    // 修改 investment_id 字段为可为 null
    await queryInterface.changeColumn('commissions', 'investment_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: '投资ID'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // 删除 type 字段
    await queryInterface.removeColumn('commissions', 'type');

    // 恢复 investment_id 字段为不可为 null
    await queryInterface.changeColumn('commissions', 'investment_id', {
      type: Sequelize.INTEGER,
      allowNull: false,
      comment: '投资ID'
    });
  }
};
