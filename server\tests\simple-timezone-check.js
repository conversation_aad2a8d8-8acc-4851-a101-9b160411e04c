/**
 * 简单的时区状态检查
 */

async function checkTimezone() {
  console.log('🔍 简单时区检查...\n');

  try {
    const mysql = require('mysql2/promise');
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'MySQL3352~!',
      database: 'fox_db'
    });

    console.log('✅ 数据库连接成功\n');

    // 检查时区设置
    console.log('1. 检查数据库时区：');
    const [result1] = await connection.execute('SELECT @@session.time_zone as session_timezone');
    console.log(`  会话时区: ${result1[0].session_timezone}`);

    const [result2] = await connection.execute('SELECT @@system_time_zone as system_timezone');
    console.log(`  系统时区: ${result2[0].system_timezone}`);

    const [result3] = await connection.execute('SELECT NOW() as db_time');
    console.log(`  当前时间: ${result3[0].db_time}`);

    const [result4] = await connection.execute('SELECT UTC_TIMESTAMP() as utc_time');
    console.log(`  UTC时间: ${result4[0].utc_time}\n`);

    // 检查系统参数
    console.log('2. 检查系统参数：');
    const [paramResult] = await connection.execute(
      "SELECT param_value FROM system_params WHERE param_key = '[site.timezone]' LIMIT 1"
    );

    if (paramResult.length > 0) {
      console.log(`  配置的时区: ${paramResult[0].param_value}`);
    } else {
      console.log('  未找到时区配置');
    }

    // 检查最新数据
    console.log('\n3. 检查最新投资记录：');
    const [investResult] = await connection.execute(
      'SELECT id, start_time, created_at FROM investments ORDER BY id DESC LIMIT 2'
    );

    investResult.forEach((inv, index) => {
      console.log(`  投资${index + 1}: ID=${inv.id}, 开始=${inv.start_time}, 创建=${inv.created_at}`);
    });

    console.log('\n4. 分析结果：');
    const sessionTz = result1[0].session_timezone;
    const configTz = paramResult.length > 0 ? paramResult[0].param_value : null;

    if (sessionTz === '+00:00' || sessionTz === 'UTC') {
      console.log('  ✅ 数据库已设置为UTC时区');
    } else {
      console.log(`  ⚠️  数据库时区为: ${sessionTz}`);
    }

    if (configTz === '+00:00') {
      console.log('  ✅ 系统参数已配置为UTC');
    } else if (configTz) {
      console.log(`  ⚠️  系统参数配置为: ${configTz}`);
    } else {
      console.log('  ❌ 未找到系统时区配置');
    }

    console.log('\n5. 建议：');
    if (sessionTz === '+00:00' && configTz === '+00:00') {
      console.log('  🎉 系统已完全配置为UTC，可以直接测试！');
    } else if (sessionTz === '+00:00') {
      console.log('  📝 数据库是UTC，但需要更新系统参数配置');
    } else {
      console.log('  🔄 需要执行UTC迁移');
    }

    await connection.end();

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

checkTimezone();
