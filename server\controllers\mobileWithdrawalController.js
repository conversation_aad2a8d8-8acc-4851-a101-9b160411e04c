/**
 * 移动端提现控制器
 * 处理用户提现相关操作
 */
const { Withdrawal, User, BankCard, SystemParam, AccountBalance } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { generateOrderNumber } = require('../utils/orderUtils');
const balanceService = require('../services/balanceService');
const withdrawalFeeService = require('../services/withdrawalFeeService');

// 创建提现订单
exports.createWithdrawal = async (req, res) => {
  try {
    const { amount, bank_card_id, password } = req.body;
    const user = req.user;

    // 验证请求数据
    if (!amount || !bank_card_id) {
      return res.status(400).json({
        code: 400,
        message: '金额和银行卡ID不能为空',
        data: null
      });
    }

    // 验证金额是否为数字
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return res.status(400).json({
        code: 400,
        message: '金额必须大于0',
        data: null
      });
    }

    // 获取最低提现金额参数
    const minWithdrawalParam = await SystemParam.findOne({
      where: {
        param_key: '[site.min_withdrawal_amount]'
      }
    });

    // 验证最低提现金额
    if (minWithdrawalParam) {
      const minWithdrawalAmount = parseFloat(minWithdrawalParam.param_value);
      if (!isNaN(minWithdrawalAmount) && numAmount < minWithdrawalAmount) {
        return res.status(400).json({
          code: 400,
          message: `单笔最低提现金额为${minWithdrawalAmount}`,
          data: null
        });
      }
    }

    // 验证当前是否为工作日
    // 获取取款工作日参数
    const workingDaysParam = await SystemParam.findOne({
      where: {
        param_key: '[site.cashout_weeks]'
      }
    });

    if (workingDaysParam) {
      // 获取当前是星期几（0-6，0表示星期日）
      const today = new Date().getDay();
      // 转换为1-7，1表示星期一，7表示星期日
      const dayOfWeek = today === 0 ? 7 : today;

      // 解析工作日设置
      const workingDays = workingDaysParam.param_value.split(',').map(day => parseInt(day.trim()));

      // 检查当前是否为工作日
      if (!workingDays.includes(dayOfWeek)) {
        return res.status(400).json({
          code: 400,
          message: '非工作日无法进行取款操作，请在工作日尝试',
          data: null
        });
      }
    }

    // 验证当前是否在取款时间范围内
    // 获取取款时间范围参数
    const cashoutHoursParam = await SystemParam.findOne({
      where: {
        param_key: '[site.cashout_hours]'
      }
    });

    if (cashoutHoursParam) {
      // 获取当前小时
      const currentHour = new Date().getHours();

      // 解析取款时间范围
      const timeRange = cashoutHoursParam.param_value.split('-');
      if (timeRange.length === 2) {
        const startHour = parseInt(timeRange[0].trim());
        const endHour = parseInt(timeRange[1].trim());

        // 检查当前是否在取款时间范围内
        let isWithinTimeRange = false;

        if (startHour <= endHour) {
          // 正常时间范围，如9-21
          isWithinTimeRange = currentHour >= startHour && currentHour < endHour;
        } else {
          // 跨日时间范围，如22-6
          isWithinTimeRange = currentHour >= startHour || currentHour < endHour;
        }

        if (!isWithinTimeRange) {
          return res.status(400).json({
            code: 400,
            message: `当前不在取款时间范围内，请在${startHour}:00至${endHour}:00之间尝试`,
            data: null
          });
        }
      }
    }

    // 验证银行卡是否存在
    const bankCard = await BankCard.findOne({
      where: {
        id: bank_card_id,
        user_id: user.id
      }
    });

    if (!bankCard) {
      return res.status(404).json({
        code: 404,
        message: '银行卡不存在',
        data: null
      });
    }

    // 获取用户的收入账户余额
    const incomeAccount = await AccountBalance.findOne({
      where: {
        user_id: user.id,
        account_type: 'income'
      }
    });

    if (!incomeAccount) {
      return res.status(400).json({
        code: 400,
        message: '收入账户不存在',
        data: null
      });
    }

    const incomeBalance = parseFloat(incomeAccount.balance);

    console.log(`用户ID: ${user.id}, 提现金额: ${numAmount}, 收入账户余额: ${incomeBalance}`);

    // 验证余额是否足够
    if (incomeBalance < numAmount) {
      return res.status(400).json({
        code: 400,
        message: `余额不足，当前余额: ${incomeBalance}，需要: ${numAmount}`,
        data: null
      });
    }

    // 使用动态手续费计算服务
    let feeCalculation;
    try {
      feeCalculation = await withdrawalFeeService.calculateWithdrawalFee(numAmount);
    } catch (feeError) {
      console.error('计算手续费失败:', feeError);
      return res.status(400).json({
        code: 400,
        message: `手续费计算失败: ${feeError.message}`,
        data: null
      });
    }

    const fee = feeCalculation.feeAmount;
    const actualAmount = feeCalculation.actualAmount;

    // 生成订单号
    const orderNumber = generateOrderNumber('WM');

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 使用余额服务扣减用户余额
      const balanceResult = await balanceService.adjustBalance(
        user.id,
        'income', // 从收入账户扣减
        -numAmount, // 负数表示扣减
        'subtract',
        'withdrawal', // 交易类型为提现
        `提现申请 ${numAmount}`,
        null,
        'withdrawal', // 引用类型也改为withdrawal
        transaction
      );

      // 创建提现订单
      const withdrawal = await Withdrawal.create({
        user_id: user.id,
        order_number: orderNumber,
        amount: numAmount,
        fee,
        actual_amount: actualAmount,
        bank_card_id,
        status: 'pending', // 初始状态为待审核
        remark: '用户提现',
        transaction_id: balanceResult.transactionId // 关联交易ID
      }, { transaction });

      // 提交事务
      await transaction.commit();

      return res.status(201).json({
        code: 201,
        message: '提现申请提交成功',
        data: {
          order_id: withdrawal.id,
          order_number: withdrawal.order_number,
          amount: withdrawal.amount,
          fee: withdrawal.fee,
          actual_amount: withdrawal.actual_amount,
          status: withdrawal.status,
          created_at: withdrawal.created_at
        }
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('创建提现订单错误:', error);
    console.error('错误详情:', JSON.stringify({
      userId: req.user ? req.user.id : 'unknown',
      amount: numAmount, // 使用numAmount而不是amount
      bank_card_id: bank_card_id,
      errorMessage: error.message,
      errorStack: error.stack
    }));
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误: ' + error.message,
      data: null
    });
  }
};

// 获取用户提现记录
exports.getUserWithdrawals = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const user = req.user;

    // 构建查询条件
    const where = {
      user_id: user.id
    };

    // 根据状态筛选
    if (status && status !== 'all') {
      where.status = status;
    }

    // 分页查询
    const offset = (page - 1) * limit;

    const { count, rows } = await Withdrawal.findAndCountAll({
      where,
      include: [
        {
          model: BankCard,
          as: 'bank_card',
          attributes: ['id', 'bank_name', 'card_number', 'card_holder']
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取用户提现记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

module.exports = exports;
