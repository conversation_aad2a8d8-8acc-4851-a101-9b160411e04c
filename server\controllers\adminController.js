const { Admin, Role, AdminRole } = require('../models');
const sequelize = require('../config/database');
const { Op } = require('sequelize');

// 获取管理员列表
exports.getAdmins = async (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, status } = req.query;

    // 构建查询条件
    const where = {};

    if (keyword) {
      where[Op.or] = [
        { username: { [Op.like]: `%${keyword}%` } },
        { nickname: { [Op.like]: `%${keyword}%` } }
      ];
    }

    if (status !== undefined) {
      where.status = status === 'true';
    }

    // 分页查询
    const offset = (page - 1) * limit;
    const pageSize = parseInt(limit);

    const { count, rows } = await Admin.findAndCountAll({
      where,
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: pageSize
    });

    console.log('管理员列表数据:', JSON.stringify({
      total: count,
      page: parseInt(page),
      limit: pageSize,
      items: rows
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: pageSize,
        items: rows
      }
    });
  } catch (error) {
    console.error('获取管理员列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 获取管理员详情
exports.getAdmin = async (req, res) => {
  try {
    const { id } = req.params;

    const admin = await Admin.findByPk(id, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ]
    });

    if (!admin) {
      return res.status(404).json({
        code: 404,
        message: '管理员不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: admin
    });
  } catch (error) {
    console.error('获取管理员详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 创建管理员
exports.createAdmin = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    console.log('创建管理员请求数据:', req.body);
    const { username, password, nickname, role_id, is_super, status } = req.body;

    // 验证请求数据
    if (!username || !password) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '用户名和密码不能为空',
        data: null
      });
    }

    // 检查用户名是否已存在
    const existingAdmin = await Admin.findOne({ where: { username } });
    if (existingAdmin) {
      await transaction.rollback();
      return res.status(409).json({
        code: 409,
        message: '用户名已存在',
        data: null
      });
    }

    // 检查角色是否存在
    if (role_id) {
      const role = await Role.findByPk(role_id);
      if (!role) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '角色不存在',
          data: null
        });
      }
    }

    // 创建管理员
    const admin = await Admin.create({
      username,
      password,
      nickname: nickname || username, // 如果没有昵称，使用用户名
      is_super: !!is_super,
      status: status !== undefined ? !!status : true
    }, { transaction });

    console.log('创建的管理员:', admin.toJSON());

    // 分配角色
    if (role_id) {
      await AdminRole.create({
        admin_id: admin.id,
        role_id
      }, { transaction });
      console.log('分配的角色ID:', role_id);
    }

    await transaction.commit();

    // 获取包含角色的管理员信息
    const adminWithRoles = await Admin.findByPk(admin.id, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ]
    });

    console.log('返回的管理员数据:', adminWithRoles.toJSON());

    return res.status(201).json({
      code: 201,
      message: '创建成功',
      data: adminWithRoles
    });
  } catch (error) {
    await transaction.rollback();
    console.error('创建管理员错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 更新管理员
exports.updateAdmin = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { nickname, role_id, status, is_super } = req.body;

    // 查找管理员
    const admin = await Admin.findByPk(id, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ]
    });

    if (!admin) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '管理员不存在',
        data: null
      });
    }

    // 检查是否为超级管理员
    if (admin.is_super && !req.admin.is_super) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '无权修改超级管理员',
        data: null
      });
    }

    // 检查角色是否存在
    if (role_id) {
      const role = await Role.findByPk(role_id);
      if (!role) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '角色不存在',
          data: null
        });
      }
    }

    // 更新管理员信息
    if (nickname) admin.nickname = nickname;
    if (status !== undefined && req.admin.is_super) admin.status = !!status;
    if (is_super !== undefined && req.admin.is_super) {
      admin.is_super = !!is_super;
    }

    await admin.save({ transaction });

    // 更新角色关联
    if (role_id !== undefined) {
      // 删除旧的角色关联
      await AdminRole.destroy({
        where: { admin_id: admin.id },
        transaction
      });

      // 添加新的角色关联
      if (role_id) {
        await AdminRole.create({
          admin_id: admin.id,
          role_id
        }, { transaction });
      }
    }

    await transaction.commit();

    // 获取更新后的管理员信息
    const updatedAdmin = await Admin.findByPk(admin.id, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name'],
          through: { attributes: [] }
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: updatedAdmin
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新管理员错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 重置管理员密码
exports.resetPassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { password } = req.body;

    // 验证请求数据
    if (!password) {
      return res.status(400).json({
        code: 400,
        message: '密码不能为空',
        data: null
      });
    }

    // 查找管理员
    const admin = await Admin.findByPk(id);
    if (!admin) {
      return res.status(404).json({
        code: 404,
        message: '管理员不存在',
        data: null
      });
    }

    // 检查是否为超级管理员
    if (admin.is_super && !req.admin.is_super) {
      return res.status(403).json({
        code: 403,
        message: '无权修改超级管理员密码',
        data: null
      });
    }

    // 更新密码
    admin.password = password;
    await admin.save();

    return res.status(200).json({
      code: 200,
      message: '密码重置成功',
      data: null
    });
  } catch (error) {
    console.error('重置管理员密码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 删除管理员
exports.deleteAdmin = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // 查找管理员
    const admin = await Admin.findByPk(id);
    if (!admin) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '管理员不存在',
        data: null
      });
    }

    // 检查是否为超级管理员
    if (admin.is_super) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '无法删除超级管理员',
        data: null
      });
    }

    // 检查是否为当前登录的管理员
    if (admin.id === req.admin.id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '无法删除当前登录的管理员',
        data: null
      });
    }

    // 删除管理员的角色关联
    await AdminRole.destroy({
      where: { admin_id: admin.id },
      transaction
    });

    // 删除管理员
    await admin.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('删除管理员错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};
