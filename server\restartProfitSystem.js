/**
 * 重启收益系统
 * 用于在修复代码后重启收益系统
 */
const profitSystem = require('./services/profitSystem');
const logger = require('./utils/logger');

async function restartProfitSystem() {
  try {
    logger.info('开始重启收益系统...');
    
    // 停止收益系统
    const stopResult = profitSystem.stopProfitSystem();
    logger.info('停止收益系统结果:', stopResult);
    
    // 启动收益系统
    const startResult = await profitSystem.startProfitSystem();
    logger.info('启动收益系统结果:', startResult);
    
    logger.info('收益系统重启完成');
    
    process.exit(0);
  } catch (error) {
    logger.error('重启收益系统失败:', error);
    process.exit(1);
  }
}

// 执行重启
restartProfitSystem();
