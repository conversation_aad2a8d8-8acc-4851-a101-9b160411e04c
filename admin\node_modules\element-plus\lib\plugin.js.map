{"version": 3, "file": "plugin.js", "sources": ["../../../packages/element-plus/plugin.ts"], "sourcesContent": ["import { ElInfiniteScroll } from '@element-plus/components/infinite-scroll'\nimport { ElLoading } from '@element-plus/components/loading'\nimport { ElMessage } from '@element-plus/components/message'\nimport { ElMessageBox } from '@element-plus/components/message-box'\nimport { ElNotification } from '@element-plus/components/notification'\nimport { ElPopoverDirective } from '@element-plus/components/popover'\n\nimport type { Plugin } from 'vue'\n\nexport default [\n  ElInfiniteScroll,\n  ElLoading,\n  ElMessage,\n  ElMessageBox,\n  ElNotification,\n  ElPopoverDirective,\n] as Plugin[]\n"], "names": ["ElInfiniteScroll", "ElLoading", "ElMessage", "ElMessageBox", "ElNotification", "ElPopoverDirective"], "mappings": ";;;;;;;;;;;AAMA,cAAe;AACf,EAAEA,sBAAgB;AAClB,EAAEC,iBAAS;AACX,EAAEC,iBAAS;AACX,EAAEC,oBAAY;AACd,EAAEC,sBAAc;AAChB,EAAEC,0BAAkB;AACpB,CAAC;;;;"}