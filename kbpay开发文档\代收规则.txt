代收下单（Form表单提交参数）
测试地址：
https://api.kbpay.io/debug/payin/submit
正式地址：
https://api.kbpay.io/online/payin/submit
请求方式：
POST
Header：
'Content-Type': 'application/x-www-form-urlencoded'
♥ 请求参数
参数	示例值	参数名	类型	是否必填	说明
merchant_no	6035002	商户号	String	Y	平台分配唯一
order_no	ZF1655990009416	商户单号	String	Y	保证每笔订单唯一
order_amount	251.00	交易金额【单位：元】	Float	Y	总金额【单位：元，float类型, 保留2位小数】
notify_url	http://192.168.0.195:8082/demo/pay/notice	支付成功通知地址	String	Y	通知支付的请求地址（说明：该地址请勿携带【 ?、=、*、. 】等特殊符号，切记！切记！切记！）
timestamp	1655991423	秒级时间戳【10位】	Integer	Y	秒级时间戳【10位】
payin_method	1004	代收方式【可选参数】	Integer	N	请查阅商户后台【首页】->【代收方式】
return_url	https://www.google.com/pay/api	支付成功跳转地址	String	N	不超过200字节
order_attach	-	附加参数	String	N	USDT商户必传会员ID，下单就返回会员ID以及会员专属TRC20地址（需Base64解码）。其他货币商户可填写商户订单附加信息，如有填写，通知支付中原样返回，泰国和韩国需要在该字段传付款人的真实姓名和账户，格式：Lily@123456789
sign	bdc835ed694bcf42f8069945e6876541	签名	String	Y	不参与签名，使用【代收密钥->MD5密钥】对query_string&key=payin_key进行md5运算得到sign的值

♥ 成功响应示例
{
  "code": 0, //0为成功，其他数字表示异常，异常描述在message字段中
  "data": {
    "merchant_no": "6035002", //商户号
    "order_amount": 251.00, //下单金额（float）
    "order_no": "ZF1655990009416", //商户单号
    "trade_no": "P16559914233497888", //平台单号
    "url": "http://192.168.0.195:8082/api/stage/pay?trade_no=P16559914233497888" //收银台地址
  }, //返回数据
  "message": "success" //描述信息
}
 ♥失败响应示例
{
  "code": 1000, //0为成功，其他数字表示异常，异常描述在message字段中
  "message": "商户不存在" //描述信息
} 

♥ 代收下单 go DEMO
package main

import (
	"api/util"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
)

func main() {
	//代收密钥
	payin_key := "ertwekmd923423kle90wdlsld23"
	params := url.Values{}
	params.Add("merchant_no", "6011567")
	// params.Add("payin_method", 1043)
	params.Add("order_no", "2343452342")
	// params.Add("order_amount", 100.12) //金额（float）
        params.Add("order_amount", fmt.Sprintf("%.2f", 200.00)) //金额（float）
	params.Add("notify_url", "https://www.xxx1111.com")
	params.Add("timestamp", 1655991423)
	unescape, _ := url.QueryUnescape(params.Encode())
	sign_str := unescape + "&key=" + payin_key
	sign := util.Md5(sign_str)
	params.Add("sign", sign)
	//使用post form 方式提交到支付网址，请自行替换为真实支付网址
	response, rsp_err := http.PostForm("https://api.kbpay.io/debug/payin/submit", params)
	if rsp_err != nil {
		fmt.Println(rsp_err.Error())
	}
	response_body, _ := ioutil.ReadAll(response.Body)
	fmt.Println("pay submit rs body is:", string(response_body))
}
♥ 代收下单 php DEMO
<?php
//定义curl的post请求方法
function post_form($url, $data) {
   //初使化init方法
   $ch = curl_init();
   //指定URL
   curl_setopt($ch, CURLOPT_URL, $url);
   //设定请求后返回结果
   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
   //声明使用POST方式来进行发送
   curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
   //发送的数据
   curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
   //忽略证书
   curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
   curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
   //不需要响应的header头信息
   curl_setopt($ch, CURLOPT_HEADER, false);
   //设置请求的header头信息
   curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/x-www-form-urlencoded",   
   ));
   //设置超时时间
   curl_setopt($ch, CURLOPT_TIMEOUT, 10);
   //发送请求
   $object = new stdClass ();
   $object->response = curl_exec ( $ch );
   $object->info = curl_getinfo ( $ch );
   $object->error_code = curl_errno ( $ch );
   $object->error = curl_error ( $ch );
   //关闭curl
   curl_close($ch);
   //返回数据
   return $object;
}
//代收密钥
$payin_key ="43CQVasnjric0f1srdX8mQ8MEV1LYdMl";  
$data["merchant_no"]="6056003";
$data["order_no"]="p2342343";
// $data["order_amount"]=120.12; //金额（float）
$data["order_amount"]= sprintf("%.2f", 200); 金额（float）
$data["notify_url"]="http://www.test111.com";
$data["timestamp"]= time();
//排序
ksort($data);
//拼接字符串
$unescape = urldecode(http_build_query($data));
$sign_str = $unescape . "&key=" . $payin_key;
//签名
$data["sign"] = md5($sign_str);
//发送post的form请求
$url = "https://api.kbpay.io/debug/payin/submit";
$rs = post_form($url, $data);
var_export($rs);

代收通知（返回Form表单提交的通知数据）
请求地址：
支付请求参数中的notify_url（说明：该地址请勿携带【 ?、=、*、. 】等特殊符号，切记！切记！切记！）
请求方式：
POST
Header：
'Content-Type': 'application/x-www-form-urlencoded'
说明：当请求notify_url地址返回http【状态码为200】【输出文本内容为"success"】即表示通知已送达。否则服务器将尝试3次请求后停止发送通知。
重要的事情说三遍：会员上分金额请使用【实际支付金额 trade_amount 】，会员上分金额请使用【实际支付金额 trade_amount 】，会员上分金额请使用【实际支付金额 trade_amount 】。
♥ 请求参数（Form表单）
参数	示例值	参数名	类型	是否必填	说明
merchant_no	6035002	商户号	String	Y	平台分配唯一
order_no	***************	商户单号	String	Y	保证每笔订单唯一
trade_no	P16559900261120728	平台单号	String	Y	平台分配唯一
order_amount	251.00	订单金额【单位：元】	Float	Y	订单金额【单位：元，float类型，保留2位小数】
trade_amount	251.00	实际支付金额【单位：元】	Float	Y	实际支付金额【单位：元，float类型，保留2位小数】
trade_status	1	支付状态	Integer	Y	支付状态【0 未支付】【1 支付成功】
timestamp	1655991106	秒级时间戳【10位】	Integer	Y	秒级时间戳【10位】
transfer_ref_no	-	唯一参考	String	N	交易参考号
order_attach	-	商户自定义参数	String	N	商户自定义参数
sign	fa93dbd2498a5d266ddf70092def4f2a	签名	String	Y	不参与签名，使用【代收密钥->MD5密钥】对query_string&key=payin_key进行md5运算得到sign的值
代收查询（Form表单提交查询数据）
测试地址：
https://api.kbpay.io/debug/payin/query
正式地址：
https://api.kbpay.io/online/payin/query
请求方式：
POST
Header：
'Content-Type': 'application/x-www-form-urlencoded'
♥ 请求参数
参数	示例值	参数名	类型	是否必填	说明
merchant_no	6035002	商户号	String	Y	平台分配唯一
order_no	16559900261120728	商户单号	String	Y	商户提供的唯一单号
timestamp	1655990282	秒级时间戳【10位】	Integer	Y	秒级时间戳【10位】
sign	1d0f41868fa6f157e477ea5319f1c80f	签名	String	Y	不参与签名，使用【代收密钥->MD5密钥】对query_string&key=payin_key进行md5运算得到sign的值
♥ 成功响应示例
{
  "code": 0, //0为成功，其他数字表示异常，异常描述在message字段中
  "data": {
    "merchant_no": "6035002", //商户号
    "order_amount": 251.00, //订单金额【单位：元，float类型，保留2位小数】
    "order_no": "***************", //商户单号
    "trade_amount": 251.00, //实际支付金额【单位：元，float类型，保留2位小数】, 会员上分金额请使用【实际支付金额】
    "trade_status": 0, //支付状态[0未支付][1支付成功]
    "trade_no": "P16559900261120728", //平台单号
    "trade_status_format": "wait" // 交易状态格式化
    "transfer_account_no": "123456" // 转账账号
    "transfer_ref_no": "**********"   // 唯一参考
  }, //返回数据
  "message": "success" //描述信息
}
♥ 失败响应示例
{
  "code": 1000, //0为成功，其他数字表示异常，异常描述在message字段中
  "message": "订单不存在" //描述信息
}
♥ 返回参数
merchant_no	String	商户号
order_no	String	商户单号
order_amount	Float	订单金额【单位：元，float类型，保留2位小数】
trade_amount	Float	代收金额【单位：元，float类型，保留2位小数】
trade_status	Integer	代收状态【0 未支付】【1 支付成功】
trade_no	String	平台单号
transfer_ref_no	String	唯一参考
♥ 代收查询 go DEMO
package main

import (
	"api/util"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
)

func main() {
	//代收密钥
	payin_key := "ertwekmd923423kle90wdlsld23"
	params := url.Values{}
	params.Add("merchant_no", "6011567")
	params.Add("order_no", "P234345324234")
	params.Add("timestamp", 1655991423 )
	unescape, _ := url.QueryUnescape(params.Encode())
	sign_str := unescape + "&key=" + payin_key
	sign := util.Md5(sign_str)
	params.Add("sign", sign)
	//使用post form 方式提交到支付网址，请自行替换为真实支付网址
	response, rsp_err := http.PostForm("https://api.kbpay.io/debug/payin/query", params)
	if rsp_err != nil {
		fmt.Println(rsp_err.Error())
	}
	response_body, _ := ioutil.ReadAll(response.Body)
	fmt.Println("pay submit rs body is:", string(response_body))
}
♥ 代收查询php DEMO
<?php
/定义curl的post请求方法
function post_form($url, $data) {
   //初使化init方法
   $ch = curl_init();
   //指定URL
   curl_setopt($ch, CURLOPT_URL, $url);
   //设定请求后返回结果
   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
   //声明使用POST方式来进行发送
   curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
   //发送的数据
   curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
   //忽略证书
   curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
   curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
   //不需要响应的header头信息
   curl_setopt($ch, CURLOPT_HEADER, false);
   //设置请求的header头信息
   curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      "Content-Type: application/x-www-form-urlencoded",   
   ));
   //设置超时时间
   curl_setopt($ch, CURLOPT_TIMEOUT, 10);
   //发送请求
   $object = new stdClass ();
   $object->response = curl_exec ( $ch );
   $object->info = curl_getinfo ( $ch );
   $object->error_code = curl_errno ( $ch );
   $object->error = curl_error ( $ch );
   //关闭curl
   curl_close($ch);
   //返回数据
   return $object;
}
//代收密钥
$payin_key ="43CQVasnjric0f1srdX8mQ8MEV1LYdMl";  
$data["merchant_no"]="6056003";
$data["order_no"]="P234345324234";
$data["timestamp"]= time();
//排序
ksort($data);
//拼接字符串
$unescape = urldecode(http_build_query($data));
$sign_str = $unescape . "&key=" . $payin_key;
//签名
$data["sign"] = md5($sign_str);
//发送post的form请求
$url = "https://api.kbpay.io/debug/payin/query";
$rs = post_form($url, $data);
var_export($rs);