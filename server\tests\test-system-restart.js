/**
 * 系统重启测试
 * 测试方案3.1的自动补发功能
 */

const { Investment, Project, User, InvestmentProfit } = require('../models');
const sequelize = require('../config/database');
const profitSystem = require('../services/profitSystem');
const redisClient = require('../utils/redisClient');
const logger = require('../utils/logger');
const { BalanceTracker, TestDataManager, TestAssert } = require('./test-utils');

/**
 * 测试1：模拟遗漏收益场景
 */
const testMissedProfitScenario = async () => {
  console.log('\n=== 测试1：模拟遗漏收益场景 ===');
  
  try {
    // 找到一个活跃投资
    const investment = await Investment.findOne({
      where: { status: 'active' },
      include: [
        { model: Project, as: 'project' },
        { model: User, as: 'user' }
      ]
    });
    
    if (!investment) {
      console.log('❌ 没有找到活跃投资');
      return false;
    }
    
    console.log(`✅ 找到测试投资: ${investment.id}`);
    console.log(`   - 用户: ${investment.user.username} (ID: ${investment.user.id})`);
    console.log(`   - 项目: ${investment.project.name} (收益周期: ${investment.project.profit_time}小时)`);
    console.log(`   - 投资金额: ${investment.amount}`);
    console.log(`   - 当前最后收益时间: ${investment.last_profit_time || '无'}`);
    
    // 模拟遗漏收益：将最后收益时间设置为过去
    const now = new Date();
    const profitCycleMs = investment.project.profit_time * 60 * 60 * 1000;
    
    // 设置最后收益时间为3个周期前，模拟遗漏2笔收益
    const simulatedLastProfitTime = new Date(now.getTime() - 3 * profitCycleMs);
    
    await Investment.update(
      { last_profit_time: simulatedLastProfitTime },
      { where: { id: investment.id } }
    );
    
    console.log(`✅ 模拟遗漏收益场景设置完成`);
    console.log(`   - 设置最后收益时间: ${simulatedLastProfitTime.toISOString()}`);
    console.log(`   - 当前时间: ${now.toISOString()}`);
    console.log(`   - 预期遗漏收益: 2笔`);
    
    // 保存测试数据
    global.testInvestmentRestart = investment;
    global.originalLastProfitTime = investment.last_profit_time;
    global.simulatedLastProfitTime = simulatedLastProfitTime;
    
    return true;
  } catch (error) {
    console.log('❌ 模拟遗漏收益场景失败:', error.message);
    return false;
  }
};

/**
 * 测试2：记录补发前状态
 */
const testRecordBeforeState = async () => {
  console.log('\n=== 测试2：记录补发前状态 ===');

  try {
    const investment = global.testInvestmentRestart;

    // 使用标准化工具记录补发前状态
    const beforeSnapshot = await BalanceTracker.snapshot(
      investment.user.id,
      investment.id,
      '补发前状态'
    );

    // 保存快照供后续验证使用
    global.beforeCompensationSnapshot = beforeSnapshot;

    console.log(`✅ 补发前状态记录完成`);

    return true;
  } catch (error) {
    console.log('❌ 记录补发前状态失败:', error.message);
    return false;
  }
};

/**
 * 测试3：执行系统重启（模拟）
 */
const testSystemRestart = async () => {
  console.log('\n=== 测试3：执行系统重启（模拟） ===');
  
  try {
    console.log('🔄 模拟系统重启...');
    
    // 停止收益系统
    const stopResult = profitSystem.stopProfitSystem();
    if (stopResult.success) {
      console.log('✅ 收益系统已停止');
    }
    
    // 清理Redis中的任务（模拟系统重启后Redis数据丢失）
    const investment = global.testInvestmentRestart;
    await redisClient.removeProfitTask(investment.id);
    console.log('✅ Redis任务已清理');
    
    // 等待一小段时间（模拟重启过程）
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 重新启动收益系统（这里会触发自动补发）
    console.log('🚀 重新启动收益系统...');
    const startResult = await profitSystem.startProfitSystem();
    
    if (startResult.success) {
      console.log('✅ 收益系统重启成功');
      console.log(`   - Redis可用: ${startResult.redis_available}`);
      console.log(`   - 补发收益数量: ${startResult.compensated_profits || 0} 笔`);
      
      global.compensatedCount = startResult.compensated_profits || 0;
      
      if (startResult.compensated_profits > 0) {
        console.log('🎉 检测到自动补发！');
      } else {
        console.log('⚠️  没有检测到自动补发');
      }
      
    } else {
      console.log('❌ 收益系统重启失败:', startResult.message);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 系统重启测试失败:', error.message);
    return false;
  }
};

/**
 * 测试4：验证补发结果
 */
const testVerifyCompensation = async () => {
  console.log('\n=== 测试4：验证补发结果 ===');

  try {
    const investment = global.testInvestmentRestart;

    // 创建补发后快照
    const afterSnapshot = await BalanceTracker.snapshot(
      investment.user.id,
      investment.id,
      '补发后验证'
    );

    // 计算变化（使用保存的补发前快照）
    const beforeSnapshot = global.beforeCompensationSnapshot;
    const validation = BalanceTracker.validateChange(beforeSnapshot, afterSnapshot, 10);

    // 检查最后收益时间是否更新
    const updatedInvestment = await Investment.findByPk(investment.id);

    console.log(`📊 补发结果验证:`);
    console.log(`   - 系统报告补发: ${global.compensatedCount} 笔`);
    console.log(`   - 实际记录增加: ${validation.profitChange} 笔`);
    console.log(`   - 余额变化: ${validation.balanceChange}`);
    console.log(`   - 预期余额变化: ${validation.expectedBalanceChange}`);
    console.log(`   - 最后收益时间: ${updatedInvestment.last_profit_time}`);

    // 验证补发是否正确
    let verificationPassed = true;

    // 验证补发数量匹配
    if (global.compensatedCount !== validation.profitChange) {
      console.log(`❌ 补发数量不匹配: 系统报告${global.compensatedCount}笔，实际增加${validation.profitChange}笔`);
      verificationPassed = false;
    } else {
      console.log(`✅ 补发数量匹配: ${validation.profitChange} 笔`);
    }

    // 验证余额变化一致性
    if (validation.isValid) {
      console.log(`✅ 余额变化一致性验证通过`);
    } else {
      console.log(`❌ 余额变化一致性验证失败`);
      verificationPassed = false;
    }

    // 验证最后收益时间更新
    if (updatedInvestment.last_profit_time && new Date(updatedInvestment.last_profit_time) > new Date(global.simulatedLastProfitTime)) {
      console.log(`✅ 最后收益时间已更新`);
    } else {
      console.log(`❌ 最后收益时间未正确更新`);
      verificationPassed = false;
    }

    return verificationPassed;
  } catch (error) {
    console.log('❌ 验证补发结果失败:', error.message);
    return false;
  }
};

/**
 * 测试5：验证Redis任务重建
 */
const testRedisTaskRebuild = async () => {
  console.log('\n=== 测试5：验证Redis任务重建 ===');
  
  try {
    const investment = global.testInvestmentRestart;
    
    // 检查Redis中是否有该投资的任务
    const allTasks = await redisClient.getAllProfitTasks();
    const investmentTask = allTasks.find(task => task.investment_id === investment.id);
    
    if (investmentTask) {
      console.log('✅ Redis任务重建成功');
      console.log(`   - 投资ID: ${investmentTask.investment_id}`);
      console.log(`   - 下次收益时间: ${investmentTask.next_profit_time}`);
      
      // 验证下次收益时间是否合理
      const nextProfitTime = new Date(investmentTask.next_profit_time);
      const now = new Date();
      
      if (nextProfitTime > now) {
        console.log('✅ 下次收益时间设置合理（未来时间）');
      } else {
        console.log('⚠️  下次收益时间可能不合理（过去时间）');
      }
      
    } else {
      console.log('❌ Redis任务重建失败，未找到投资任务');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('❌ 验证Redis任务重建失败:', error.message);
    return false;
  }
};

/**
 * 清理测试数据
 */
const cleanupTestData = async () => {
  try {
    const investment = global.testInvestmentRestart;
    
    if (investment && global.originalLastProfitTime !== undefined) {
      // 恢复原始的最后收益时间
      await Investment.update(
        { last_profit_time: global.originalLastProfitTime },
        { where: { id: investment.id } }
      );
      
      console.log('✅ 测试数据清理完成，已恢复原始状态');
    }
  } catch (error) {
    console.log('⚠️  清理测试数据时出错:', error.message);
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('开始执行系统重启测试...\n');
  
  let passedTests = 0;
  const totalTests = 5;
  
  try {
    // 执行测试
    if (await testMissedProfitScenario()) passedTests++;
    if (await testRecordBeforeState()) passedTests++;
    if (await testSystemRestart()) passedTests++;
    if (await testVerifyCompensation()) passedTests++;
    if (await testRedisTaskRebuild()) passedTests++;
    
  } catch (error) {
    console.log('\n❌ 测试执行过程中出现错误:', error.message);
  } finally {
    // 清理测试数据
    await cleanupTestData();
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log(`测试完成: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有系统重启测试通过！');
    console.log('✅ 方案3.1的自动补发功能工作正常');
    return true;
  } else {
    console.log('⚠️  部分测试失败，请检查自动补发逻辑');
    return false;
  }
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testMissedProfitScenario,
  testRecordBeforeState,
  testSystemRestart,
  testVerifyCompensation,
  testRedisTaskRebuild,
  cleanupTestData
};
