/**
 * 数据修复脚本
 * 查找并修复重复的收益记录
 */
const { sequelize, Investment, InvestmentProfit, Transaction } = require('../models');
const balanceService = require('../services/balanceService');
const logger = require('../utils/logger');
const dateUtils = require('../utils/dateUtils');

/**
 * 查找所有重复的收益记录
 * @returns {Promise<Array>} - 重复的收益记录组
 */
const findDuplicateProfits = async () => {
  try {
    console.log('开始查找重复收益记录...');
    
    // 使用SQL查询找出同一投资在同一理论收益时间有多条收益记录的情况
    const [duplicates] = await sequelize.query(`
      SELECT 
        investment_id, 
        DATE(theoretical_profit_time) as profit_date, 
        COUNT(*) as count,
        GROUP_CONCAT(id) as profit_ids
      FROM 
        investment_profits
      WHERE
        theoretical_profit_time IS NOT NULL
      GROUP BY 
        investment_id, DATE(theoretical_profit_time)
      HAVING 
        COUNT(*) > 1
      ORDER BY 
        investment_id, profit_date
    `);
    
    console.log(`找到 ${duplicates.length} 组重复收益记录`);
    
    // 展开每组重复记录，保留最早的一条，标记其余为重复
    const duplicateDetails = [];
    
    for (const group of duplicates) {
      const profitIds = group.profit_ids.split(',').map(id => parseInt(id));
      
      // 查询这组收益记录的详细信息
      const profits = await InvestmentProfit.findAll({
        where: {
          id: profitIds
        },
        order: [['created_at', 'ASC']]
      });
      
      // 保留最早的一条，标记其余为重复
      const keepProfit = profits[0];
      const duplicateProfits = profits.slice(1);
      
      duplicateDetails.push({
        investment_id: group.investment_id,
        profit_date: group.profit_date,
        keep_profit_id: keepProfit.id,
        duplicate_profit_ids: duplicateProfits.map(p => p.id),
        duplicate_profits: duplicateProfits
      });
    }
    
    return duplicateDetails;
  } catch (error) {
    console.error('查找重复收益记录失败:', error);
    throw error;
  }
};

/**
 * 修复重复收益
 * @returns {Promise<Object>} - 修复结果
 */
const fixDuplicateProfits = async () => {
  try {
    const duplicateGroups = await findDuplicateProfits();
    console.log(`找到 ${duplicateGroups.length} 组重复收益记录，开始修复...`);
    
    let successCount = 0;
    let failedCount = 0;
    
    for (const group of duplicateGroups) {
      console.log(`处理投资ID ${group.investment_id} 在 ${group.profit_date} 的重复收益，保留ID ${group.keep_profit_id}，删除 ${group.duplicate_profit_ids.join(', ')}`);
      
      for (const duplicateProfit of group.duplicate_profits) {
        const transaction = await sequelize.transaction();
        
        try {
          // 1. 标记收益记录为无效
          duplicateProfit.status = 'invalid';
          duplicateProfit.remark = `重复收益，已修正。保留ID ${group.keep_profit_id}`;
          await duplicateProfit.save({ transaction });
          
          // 2. 从用户余额中扣除金额
          await balanceService.adjustBalance(
            duplicateProfit.user_id,
            'income',
            duplicateProfit.amount,
            'subtract',
            'correction',
            `修正重复收益 ${duplicateProfit.id}`,
            duplicateProfit.investment_id,
            'investment',
            transaction
          );
          
          // 3. 更新投资记录
          const investment = await Investment.findByPk(duplicateProfit.investment_id, { transaction });
          investment.profit_count -= 1;
          investment.total_profit = (parseFloat(investment.total_profit || 0) - parseFloat(duplicateProfit.amount)).toFixed(2);
          await investment.save({ transaction });
          
          await transaction.commit();
          console.log(`修复收益ID ${duplicateProfit.id} 成功`);
          successCount++;
        } catch (error) {
          await transaction.rollback();
          console.error(`修复收益ID ${duplicateProfit.id} 失败:`, error);
          failedCount++;
        }
      }
    }
    
    console.log(`修复完成: 成功 ${successCount}, 失败 ${failedCount}`);
    return { success: successCount, failed: failedCount };
  } catch (error) {
    console.error('修复重复收益失败:', error);
    throw error;
  }
};

// 执行修复
fixDuplicateProfits()
  .then(result => {
    console.log('修复结果:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('修复过程出错:', error);
    process.exit(1);
  });
