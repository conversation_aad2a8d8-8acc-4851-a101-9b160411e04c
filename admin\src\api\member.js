import request from '@/utils/request';

/**
 * 获取会员列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.status - 状态筛选
 * @returns {Promise} - 返回会员列表
 */
export function getMembers(params) {
  return request({
    url: '/api/admin/users',
    method: 'get',
    params
  }).then(response => {
    // 直接返回响应，因为 request 拦截器已经处理了数据格式
    return {
      code: 200,
      message: '获取成功',
      data: response
    }
  });
}

/**
 * 获取会员详情
 * @param {number} id - 会员ID
 * @returns {Promise} - 返回会员详情
 */
export function getMemberDetail(id) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'get'
  });
}

/**
 * 更新会员信息
 * @param {number} id - 会员ID
 * @param {Object} data - 会员信息
 * @returns {Promise} - 返回更新结果
 */
export function updateMember(id, data) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'put',
    data
  }).then(response => {
    // 如果响应中没有code字段，添加一个成功的code
    if (response.code === undefined) {
      return {
        code: 200,
        message: '更新成功',
        data: response
      };
    }
    return response;
  });
}

/**
 * 调整会员余额
 * @param {number} id - 会员ID
 * @param {Object} data - 余额调整信息
 * @param {number} data.amount - 金额
 * @param {string} data.type - 操作类型 (add/subtract)
 * @param {string} data.account_type - 账户类型 (income/deposit)
 * @param {string} data.remark - 备注
 * @returns {Promise} - 返回调整结果
 */
export function adjustBalance(id, data) {
  return request({
    url: `/api/admin/users/${id}/balance`,
    method: 'put',
    data
  }).then(response => {
    // 如果响应中没有code字段，添加一个成功的code
    if (response.code === undefined) {
      return {
        code: 200,
        message: '操作成功',
        data: response
      };
    }
    return response;
  });
}

/**
 * 赠送投资
 * @param {number} id - 用户ID
 * @param {object} data - 投资数据
 * @param {number} data.projectId - 项目ID
 * @param {number} data.amount - 投资金额
 * @returns {Promise} - 返回赠送结果
 */
export function giftInvestment(id, data) {
  return request({
    url: `/api/admin/users/${id}/gift-investment`,
    method: 'post',
    data
  }).then(response => {
    // 如果响应中没有code字段，添加一个成功的code
    if (response.code === undefined) {
      return {
        code: 200,
        message: '赠送投资成功',
        data: response
      };
    }
    return response;
  });
}

/**
 * 获取会员订单列表
 * @param {number} id - 会员ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 状态筛选
 * @returns {Promise} - 返回会员订单列表
 */
export function getMemberOrders(id, params) {
  return request({
    url: `/api/admin/users/${id}/orders`,
    method: 'get',
    params
  });
}

/**
 * 获取会员下级列表
 * @param {number} id - 会员ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {number} params.level - 下级级别（1-3）
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise} - 返回会员下级列表
 */
export function getMemberSubordinates(id, params) {
  return request({
    url: `/api/admin/users/${id}/subordinates`,
    method: 'get',
    params
  }).then(response => {
    // 直接返回响应，因为 request 拦截器已经处理了数据格式
    return {
      code: 200,
      message: '获取成功',
      data: response
    }
  });
}
