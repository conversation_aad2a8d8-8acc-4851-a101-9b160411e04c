{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/config-provider/src/constants.ts"], "sourcesContent": ["import type { ConfigProviderProps } from './config-provider-props'\nimport type { InjectionKey, Ref } from 'vue'\n\nexport type ConfigProviderContext = Partial<ConfigProviderProps>\n\nexport const configProviderContextKey: InjectionKey<\n  Ref<ConfigProviderContext>\n> = Symbol()\n"], "names": [], "mappings": "AAAY,MAAC,wBAAwB,GAAG,MAAM;;;;"}