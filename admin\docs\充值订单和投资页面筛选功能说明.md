# 充值订单和投资页面筛选功能说明

## 功能概述

为充值订单页面（deposits）和用户投资页面（investments）添加了完整的筛选标签显示和管理功能，提供与会员列表页面一致的用户体验。

## 新增功能特性

### 1. 筛选按钮徽章
- **功能**：在筛选按钮上显示当前激活的筛选条件数量
- **样式**：红色圆形徽章，显示筛选条件计数
- **位置**：筛选按钮右上角

### 2. 筛选标签区域
- **显示条件**：仅在有筛选条件时显示
- **布局**：标题 + 清除按钮 + 标签列表
- **样式**：浅灰色背景，圆角边框，整洁布局

### 3. 智能标签生成
- **自动生成**：根据筛选表单自动生成对应标签
- **分类显示**：按筛选类型分组显示标签内容
- **范围合并**：将最小值-最大值合并为单个标签

### 4. 标签管理功能
- **单个移除**：点击标签关闭按钮移除单个筛选条件
- **批量清除**：点击"清除所有筛选"按钮移除所有条件
- **实时更新**：移除筛选条件后立即刷新数据

## 充值订单页面筛选功能

### 支持的筛选条件

#### 基本信息筛选
- **ID**：订单ID精确匹配
- **用户ID**：用户ID精确匹配
- **用户名**：用户名模糊匹配
- **订单号**：订单号精确匹配
- **支付平台订单号**：第三方订单号精确匹配
- **订单类型**：订单类型选择

#### 金额筛选
- **订单金额范围**：最小金额 - 最大金额
- **实付金额范围**：最小实付 - 最大实付

#### 状态筛选
- **支付状态**：未支付/已支付/支付失败
- **订单状态**：待处理/处理中/已完成/已退回
- **支付通道**：支付方式选择
- **回调状态**：通道回调/模拟回调/未回调
- **返佣状态**：已返佣/未返佣

#### 时间筛选
- **支付时间范围**：支付开始时间 - 支付结束时间
- **创建时间范围**：创建开始时间 - 创建结束时间

### 标签示例
```
ID: 12345
用户名: testuser
订单金额: 100 - 1000
支付状态: 已支付
支付时间: 2024-01-01 至 2024-01-31
```

## 用户投资页面筛选功能

### 支持的筛选条件

#### 基本信息筛选
- **ID**：投资记录ID精确匹配
- **投资方式**：购买/赠送
- **用户ID**：用户ID精确匹配
- **用户名**：用户名模糊匹配
- **项目名称**：项目名称模糊匹配
- **状态**：进行中/暂停/完成

#### 数量和金额筛选
- **数量范围**：最小数量 - 最大数量
- **投资金额范围**：最小金额 - 最大金额
- **总收益范围**：最小收益 - 最大收益

#### 收益相关筛选
- **收益周期范围**：最小周期 - 最大周期（小时）
- **收益率范围**：最小收益率 - 最大收益率（%）
- **收益次数范围**：最小次数 - 最大次数

#### 时间筛选
- **最后收益时间范围**：最后收益开始时间 - 结束时间
- **开始时间范围**：投资开始时间范围
- **结束时间范围**：投资结束时间范围

### 标签示例
```
投资方式: 购买
用户名: investor01
投资金额: 1000 - 5000
收益率: 5 - 15%
状态: 进行中
开始时间: 2024-01-01 至 2024-01-31
```

## 技术实现

### 1. 数据结构
```javascript
// 筛选标签数据结构
const filterTags = ref<Array<{key: string, label: string}>>([])
const activeFilterCount = computed(() => filterTags.value.length)
```

### 2. 标签生成逻辑
```javascript
// 生成筛选标签
const generateFilterTags = () => {
  const tags: Array<{key: string, label: string}> = []
  
  // 基本信息标签
  if (filterForm.id) tags.push({ key: 'id', label: `ID: ${filterForm.id}` })
  
  // 范围筛选标签
  if (filterForm.amountMin || filterForm.amountMax) {
    const min = filterForm.amountMin || '0'
    const max = filterForm.amountMax || '∞'
    tags.push({ key: 'amount', label: `金额: ${min} - ${max}` })
  }
  
  filterTags.value = tags
}
```

### 3. 标签移除逻辑
```javascript
// 移除单个筛选标签
const removeFilterTag = (key: string) => {
  // 根据key清除对应的筛选条件
  switch (key) {
    case 'id':
      filterForm.id = ''
      break
    case 'amount':
      filterForm.amountMin = ''
      filterForm.amountMax = ''
      break
  }
  
  // 重新生成标签并刷新数据
  generateFilterTags()
  currentPage.value = 1
  fetchData()
}
```

## 样式设计

### 1. 筛选标签容器
- **背景色**：#f8f9fa（浅灰色）
- **边框**：1px solid #e9ecef
- **圆角**：6px
- **内边距**：12px 16px

### 2. 筛选标签样式
- **背景色**：#e3f2fd（浅蓝色）
- **边框色**：#90caf9
- **文字色**：#1565c0
- **字体大小**：12px

### 3. 筛选按钮徽章
- **背景色**：#f56565（红色）
- **位置**：绝对定位，右上角
- **大小**：16px × 16px
- **字体大小**：10px

## 用户操作流程

### 1. 设置筛选条件
1. 点击工具栏中的"筛选"按钮
2. 在弹出对话框中设置各种筛选条件
3. 点击"搜索"按钮应用筛选

### 2. 查看筛选状态
1. 筛选按钮上的红色徽章显示筛选条件数量
2. 表格上方显示筛选标签区域
3. 每个标签显示具体的筛选条件

### 3. 管理筛选条件
1. **移除单个条件**：点击标签右侧的关闭按钮
2. **清除所有条件**：点击"清除所有筛选"按钮
3. **修改筛选条件**：重新打开筛选对话框进行修改

## 功能优势

### 1. 用户体验优化
- **直观显示**：清晰展示当前应用的筛选条件
- **便捷管理**：支持单个和批量移除筛选条件
- **一致性**：与会员列表页面保持一致的交互体验

### 2. 操作效率提升
- **快速识别**：通过徽章快速了解筛选状态
- **精确控制**：可以精确移除特定筛选条件
- **即时反馈**：操作后立即更新数据和界面

### 3. 界面美观性
- **整洁布局**：合理的间距和对齐
- **色彩搭配**：统一的色彩方案
- **响应式设计**：适配不同屏幕尺寸

## 注意事项

### 1. 性能考虑
- 筛选条件变更时会重新获取数据
- 标签生成采用高效的条件判断
- 避免不必要的重复渲染

### 2. 数据一致性
- 移除筛选条件后立即刷新数据
- 确保标签显示与实际筛选条件一致
- 重置功能清除所有相关状态

### 3. 用户反馈
- 操作成功后显示相应提示信息
- 筛选结果为空时给出友好提示
- 错误情况下提供明确的错误信息

## 总结

通过为充值订单和投资页面添加完整的筛选标签功能，显著提升了管理员的数据筛选和管理体验。用户现在可以：

1. **清晰了解**：通过徽章和标签清楚了解当前筛选状态
2. **精确控制**：可以精确移除或修改特定筛选条件
3. **高效操作**：通过标签快速管理筛选条件，无需重新打开对话框
4. **一致体验**：在所有页面享受统一的筛选管理体验

这些功能增强了系统的易用性和专业性，为管理员提供了更加强大和便捷的数据管理工具。
