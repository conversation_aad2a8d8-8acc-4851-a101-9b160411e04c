const express = require('express');
const router = express.Router();
const bannerController = require('../controllers/bannerController');
const { verifyAdminToken, checkAdminRole } = require('../middlewares/authMiddleware');

// 所有路由都需要验证token
router.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/banners:
 *   get:
 *     summary: 获取轮播图列表
 *     tags: [轮播图管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 状态筛选
 *       - in: query
 *         name: position
 *         schema:
 *           type: string
 *         description: 位置筛选
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/', bannerController.getBanners);

/**
 * @swagger
 * /api/admin/banners/{id}:
 *   get:
 *     summary: 获取轮播图详情
 *     tags: [轮播图管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 轮播图ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 轮播图不存在
 */
router.get('/:id', bannerController.getBannerById);

/**
 * @swagger
 * /api/admin/banners:
 *   post:
 *     summary: 创建轮播图
 *     tags: [轮播图管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - attachment_id
 *             properties:
 *               title:
 *                 type: string
 *                 description: 标题
 *               attachment_id:
 *                 type: integer
 *                 description: 附件ID
 *               url:
 *                 type: string
 *                 description: 链接地址
 *               position:
 *                 type: string
 *                 description: 显示位置
 *                 default: home
 *               sort_order:
 *                 type: integer
 *                 description: 排序
 *                 default: 0
 *               status:
 *                 type: boolean
 *                 description: 状态
 *                 default: true
 *               start_time:
 *                 type: string
 *                 format: date-time
 *                 description: 开始时间
 *               end_time:
 *                 type: string
 *                 format: date-time
 *                 description: 结束时间
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 */
router.post('/', bannerController.createBanner);

/**
 * @swagger
 * /api/admin/banners/{id}:
 *   put:
 *     summary: 更新轮播图
 *     tags: [轮播图管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 轮播图ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: 标题
 *               attachment_id:
 *                 type: integer
 *                 description: 附件ID
 *               url:
 *                 type: string
 *                 description: 链接地址
 *               position:
 *                 type: string
 *                 description: 显示位置
 *               sort_order:
 *                 type: integer
 *                 description: 排序
 *               status:
 *                 type: boolean
 *                 description: 状态
 *               start_time:
 *                 type: string
 *                 format: date-time
 *                 description: 开始时间
 *               end_time:
 *                 type: string
 *                 format: date-time
 *                 description: 结束时间
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 轮播图不存在
 */
router.put('/:id', bannerController.updateBanner);

/**
 * @swagger
 * /api/admin/banners/{id}:
 *   delete:
 *     summary: 删除轮播图
 *     tags: [轮播图管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 轮播图ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 轮播图不存在
 */
router.delete('/:id', bannerController.deleteBanner);

/**
 * @swagger
 * /api/admin/banners:
 *   delete:
 *     summary: 批量删除轮播图
 *     tags: [轮播图管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 轮播图ID数组
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 参数错误
 */
router.delete('/', bannerController.batchDeleteBanners);

/**
 * @swagger
 * /api/admin/banners/{id}/sort:
 *   put:
 *     summary: 更新轮播图排序
 *     tags: [轮播图管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 轮播图ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sort_order
 *             properties:
 *               sort_order:
 *                 type: integer
 *                 description: 排序值
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 轮播图不存在
 */
router.put('/:id/sort', bannerController.updateBannerSortOrder);

module.exports = router;
