const express = require('express');
const router = express.Router();
const investmentController = require('../controllers/investmentController');
const { verifyAdminToken } = require('../middlewares/authMiddleware');

/**
 * @swagger
 * tags:
 *   name: Investments
 *   description: 投资管理
 */

/**
 * @swagger
 * /api/admin/investments:
 *   get:
 *     summary: 获取所有投资列表
 *     tags: [Investments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 状态
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 关键词
 *     responses:
 *       200:
 *         description: 成功
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/', verifyAdminToken, investmentController.getAllInvestments);

/**
 * @swagger
 * /api/admin/investments/{id}:
 *   get:
 *     summary: 获取投资详情
 *     tags: [Investments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: 投资ID
 *     responses:
 *       200:
 *         description: 成功
 *       401:
 *         description: 未授权
 *       404:
 *         description: 投资记录不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', verifyAdminToken, investmentController.getInvestmentById);

/**
 * @swagger
 * /api/admin/investments/{id}/profits:
 *   get:
 *     summary: 获取投资收益记录
 *     tags: [Investments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: 投资ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 成功
 *       401:
 *         description: 未授权
 *       404:
 *         description: 投资记录不存在
 *       500:
 *         description: 服务器错误
 */
router.get('/:id/profits', verifyAdminToken, investmentController.getInvestmentProfits);

/**
 * @swagger
 * /api/admin/investments/{id}:
 *   delete:
 *     summary: 删除投资记录
 *     tags: [Investments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: 投资ID
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 已完成的投资记录不能删除
 *       401:
 *         description: 未授权
 *       404:
 *         description: 投资记录不存在
 *       500:
 *         description: 服务器错误
 */
router.delete('/:id', verifyAdminToken, investmentController.deleteInvestment);

/**
 * @swagger
 * /api/admin/investments/batch:
 *   delete:
 *     summary: 批量删除投资记录
 *     tags: [Investments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 投资ID列表
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 已完成的投资记录不能删除
 *       401:
 *         description: 未授权
 *       404:
 *         description: 部分投资记录不存在
 *       500:
 *         description: 服务器错误
 */
router.delete('/batch', verifyAdminToken, investmentController.batchDeleteInvestments);

/**
 * @swagger
 * /api/admin/users/{userId}/gift-investment:
 *   post:
 *     summary: 管理员赠送投资
 *     tags: [Investments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         schema:
 *           type: integer
 *         required: true
 *         description: 用户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectId:
 *                 type: integer
 *                 description: 项目ID
 *               amount:
 *                 type: number
 *                 description: 投资金额
 *     responses:
 *       200:
 *         description: 成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 用户或项目不存在
 *       500:
 *         description: 服务器错误
 */
// 注意：这个路由需要在主路由文件中注册，因为路径不在当前路由的基础路径下

module.exports = router;
