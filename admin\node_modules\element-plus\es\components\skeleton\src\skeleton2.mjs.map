{"version": 3, "file": "skeleton2.mjs", "sources": ["../../../../../../packages/components/skeleton/src/skeleton.vue"], "sourcesContent": ["<template>\n  <template v-if=\"uiLoading\">\n    <div :class=\"[ns.b(), ns.is('animated', animated)]\" v-bind=\"$attrs\">\n      <template v-for=\"i in count\" :key=\"i\">\n        <slot v-if=\"uiLoading\" :key=\"i\" name=\"template\">\n          <el-skeleton-item :class=\"ns.is('first')\" variant=\"p\" />\n          <el-skeleton-item\n            v-for=\"item in rows\"\n            :key=\"item\"\n            :class=\"[\n              ns.e('paragraph'),\n              ns.is('last', item === rows && rows > 1),\n            ]\"\n            variant=\"p\"\n          />\n        </slot>\n      </template>\n    </div>\n  </template>\n  <template v-else>\n    <slot v-bind=\"$attrs\" />\n  </template>\n</template>\n\n<script lang=\"ts\" setup>\nimport { toRef } from 'vue'\nimport { useNamespace, useThrottleRender } from '@element-plus/hooks'\nimport { skeletonProps } from './skeleton'\nimport ElSkeletonItem from './skeleton-item.vue'\n\ndefineOptions({\n  name: 'ElSkeleton',\n})\nconst props = defineProps(skeletonProps)\n\nconst ns = useNamespace('skeleton')\nconst uiLoading = useThrottleRender(toRef(props, 'loading'), props.throttle)\n\ndefineExpose({\n  /** @description loading state */\n  uiLoading,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;mCA8Bc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAClC,IAAA,MAAM,YAAY,iBAAkB,CAAA,KAAA,CAAM,OAAO,SAAS,CAAA,EAAG,MAAM,QAAQ,CAAA,CAAA;AAE3E,IAAa,MAAA,CAAA;AAAA,MAAA,SAAA;AAAA,KAEX,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}