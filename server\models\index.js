// 管理员相关模型
const Admin = require('./Admin');
const Role = require('./Role');
const Permission = require('./Permission');
const RolePermission = require('./RolePermission');
const AdminRole = require('./AdminRole');

// 用户相关模型
const User = require('./User');
const UserLevel = require('./UserLevel');
const AccountBalance = require('./AccountBalance');
const InviteCode = require('./InviteCode');
const UserRelation = require('./UserRelation');

// 银行卡相关模型
const BankCard = require('./BankCard');
const Bank = require('./Bank');
const BankChannelMapping = require('./BankChannelMapping');
const PaymentChannel = require('./PaymentChannel');

// 项目相关模型
const Project = require('./Project');
const Investment = require('./Investment');
const InvestmentProfit = require('./InvestmentProfit');
const Commission = require('./Commission');

// 交易相关模型
const Transaction = require('./Transaction');
const Deposit = require('./Deposit');
const Withdrawal = require('./Withdrawal');
const Order = require('./Order');

// 系统相关模型
const Attachment = require('./Attachment');
const SystemParam = require('./SystemParam');
const Banner = require('./Banner');
const CustomerService = require('./CustomerService');
const CustomerServiceImage = require('./CustomerServiceImage');

// 统计相关模型
const DailyStatistic = require('./DailyStatistic');
const TotalStatistic = require('./TotalStatistic');

// 定义模型之间的关系

// 角色和权限的关联
Role.belongsToMany(Permission, { through: RolePermission, foreignKey: 'role_id', otherKey: 'permission_id', as: 'permissions' });
Permission.belongsToMany(Role, { through: RolePermission, foreignKey: 'permission_id', otherKey: 'role_id', as: 'roles' });

// 管理员和角色的关联
Admin.belongsToMany(Role, { through: AdminRole, foreignKey: 'admin_id', otherKey: 'role_id', as: 'roles' });
Role.belongsToMany(Admin, { through: AdminRole, foreignKey: 'role_id', otherKey: 'admin_id', as: 'admins' });

// 用户和邀请码的关联
User.hasMany(InviteCode, { foreignKey: 'user_id', as: 'inviteCodes' });
InviteCode.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户和邀请人的关联
User.belongsTo(User, { foreignKey: 'inviter_id', as: 'inviter' });
User.hasMany(User, { foreignKey: 'inviter_id', as: 'invitees' });

// 用户和用户关系的关联
User.hasMany(UserRelation, { foreignKey: 'user_id', as: 'relations' });
UserRelation.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

User.hasMany(UserRelation, { foreignKey: 'parent_id', as: 'parentRelations' });
UserRelation.belongsTo(User, { foreignKey: 'parent_id', as: 'parent' });

// 邀请码和用户关系的关联
InviteCode.hasMany(UserRelation, { foreignKey: 'invite_code_id', as: 'userRelations' });
UserRelation.belongsTo(InviteCode, { foreignKey: 'invite_code_id', as: 'inviteCode' });

// 用户和用户级别的关联
User.belongsTo(UserLevel, { foreignKey: 'level_id', as: 'level' });
UserLevel.hasMany(User, { foreignKey: 'level_id', as: 'users' });

// 用户和账户余额的关联
User.hasMany(AccountBalance, { foreignKey: 'user_id', as: 'account_balances' });
AccountBalance.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户和银行卡的关联
User.hasMany(BankCard, { foreignKey: 'user_id', as: 'bank_cards' });
BankCard.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 银行和银行卡的关联
Bank.hasMany(BankCard, { foreignKey: 'bank_id', as: 'bank_cards' });
BankCard.belongsTo(Bank, { foreignKey: 'bank_id', as: 'bank' });

// 银行和银行编码映射的关联
Bank.hasMany(BankChannelMapping, { foreignKey: 'bank_id', as: 'mappings' });
BankChannelMapping.belongsTo(Bank, { foreignKey: 'bank_id', as: 'bank' });

// 支付通道和银行编码映射的关联
PaymentChannel.hasMany(BankChannelMapping, { foreignKey: 'payment_channel_id', as: 'bank_mappings' });
BankChannelMapping.belongsTo(PaymentChannel, { foreignKey: 'payment_channel_id', as: 'payment_channel' });

// 支付通道和充值订单的关联
PaymentChannel.hasMany(Deposit, { foreignKey: 'payment_channel_id', as: 'deposits' });
Deposit.belongsTo(PaymentChannel, { foreignKey: 'payment_channel_id', as: 'payment_channel' });

// 用户和交易记录的关联
User.hasMany(Transaction, { foreignKey: 'user_id', as: 'transactions' });
Transaction.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户和投资记录的关联
User.hasMany(Investment, { foreignKey: 'user_id', as: 'investments' });
Investment.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户和投资收益的关联
User.hasMany(InvestmentProfit, { foreignKey: 'user_id', as: 'investment_profits' });
InvestmentProfit.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户和佣金记录的关联
User.hasMany(Commission, { foreignKey: 'user_id', as: 'commissions' });
Commission.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

User.hasMany(Commission, { foreignKey: 'from_user_id', as: 'generated_commissions' });
Commission.belongsTo(User, { foreignKey: 'from_user_id', as: 'from_user' });

// 用户和充值订单的关联
User.hasMany(Deposit, { foreignKey: 'user_id', as: 'deposits' });
Deposit.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用户和提现记录的关联
User.hasMany(Withdrawal, { foreignKey: 'user_id', as: 'withdrawals' });
Withdrawal.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 项目和附件的关联
Project.belongsTo(Attachment, { foreignKey: 'image_id', as: 'image' });
Attachment.hasMany(Project, { foreignKey: 'image_id', as: 'projects_images' });

Project.belongsTo(Attachment, { foreignKey: 'video_id', as: 'video' });
Attachment.hasMany(Project, { foreignKey: 'video_id', as: 'projects_videos' });

// 项目和VIP级别的关联
Project.belongsTo(UserLevel, { foreignKey: 'vip_level_id', as: 'vip_level' });
UserLevel.hasMany(Project, { foreignKey: 'vip_level_id', as: 'projects' });

// 项目和投资记录的关联
Project.hasMany(Investment, { foreignKey: 'project_id', as: 'investments' });
Investment.belongsTo(Project, { foreignKey: 'project_id', as: 'project' });

// 投资和收益记录的关联
Investment.hasMany(InvestmentProfit, { foreignKey: 'investment_id', as: 'profits' });
InvestmentProfit.belongsTo(Investment, { foreignKey: 'investment_id', as: 'investment' });

// 投资和佣金记录的关联
Investment.hasMany(Commission, { foreignKey: 'investment_id', as: 'commissions' });
Commission.belongsTo(Investment, { foreignKey: 'investment_id', as: 'investment' });

// 交易和充值订单的关联
Transaction.hasOne(Deposit, { foreignKey: 'transaction_id', as: 'deposit' });
Deposit.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });

// 交易和提现记录的关联
Transaction.hasOne(Withdrawal, { foreignKey: 'transaction_id', as: 'withdrawal' });
Withdrawal.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });

// 交易和收益记录的关联
Transaction.hasOne(InvestmentProfit, { foreignKey: 'transaction_id', as: 'investment_profit' });
InvestmentProfit.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });

// 交易和佣金记录的关联
Transaction.hasOne(Commission, { foreignKey: 'transaction_id', as: 'commission' });
Commission.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });

// 银行卡和充值订单的关联
BankCard.hasMany(Deposit, { foreignKey: 'receiving_card_id', as: 'deposits' });
Deposit.belongsTo(BankCard, { foreignKey: 'receiving_card_id', as: 'receiving_card' });

// 用户银行卡和充值订单的关联
BankCard.hasMany(Deposit, { foreignKey: 'bank_card_id', as: 'user_deposits' });
Deposit.belongsTo(BankCard, { foreignKey: 'bank_card_id', as: 'bank_card' });

// 银行卡和提现记录的关联
BankCard.hasMany(Withdrawal, { foreignKey: 'bank_card_id', as: 'withdrawals' });
Withdrawal.belongsTo(BankCard, { foreignKey: 'bank_card_id', as: 'bank_card' });

// 用户级别和附件的关联
UserLevel.belongsTo(Attachment, { foreignKey: 'image_id', as: 'image' });
Attachment.hasMany(UserLevel, { foreignKey: 'image_id', as: 'user_levels' });

// 轮播图和附件的关联
Banner.belongsTo(Attachment, { foreignKey: 'attachment_id', as: 'attachment' });
Attachment.hasMany(Banner, { foreignKey: 'attachment_id', as: 'banners' });

// 客服图片和附件的关联
CustomerServiceImage.belongsTo(Attachment, { foreignKey: 'attachment_id', as: 'attachment' });
Attachment.hasMany(CustomerServiceImage, { foreignKey: 'attachment_id', as: 'customer_service_images' });

module.exports = {
  // 管理员相关模型
  Admin,
  Role,
  Permission,
  RolePermission,
  AdminRole,

  // 用户相关模型
  User,
  UserLevel,
  AccountBalance,
  InviteCode,
  UserRelation,

  // 银行卡相关模型
  BankCard,
  Bank,
  BankChannelMapping,
  PaymentChannel,

  // 项目相关模型
  Project,
  Investment,
  InvestmentProfit,
  Commission,

  // 交易相关模型
  Transaction,
  Deposit,
  Withdrawal,
  Order,

  // 系统相关模型
  Attachment,
  SystemParam,
  Banner,
  CustomerService,
  CustomerServiceImage,

  // 统计相关模型
  DailyStatistic,
  TotalStatistic
};
