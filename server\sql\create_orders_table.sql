CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL,
  `user_id` int(11) NOT NULL,
  `project_id` int(11) NOT NULL,
  `amount` decimal(12,2) NOT NULL,
  `expected_return` decimal(12,2) NOT NULL COMMENT '预期收益',
  `actual_return` decimal(12,2) DEFAULT NULL COMMENT '实际收益',
  `status` enum('pending','paid','completed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `payment_method` enum('balance','alipay','wechat','bank') DEFAULT NULL,
  `payment_time` datetime DEFAULT NULL,
  `start_time` datetime DEFAULT NULL COMMENT '投资开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '投资结束时间',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `project_id` (`project_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
