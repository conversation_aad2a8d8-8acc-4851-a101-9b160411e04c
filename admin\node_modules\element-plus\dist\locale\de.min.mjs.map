{"version": 3, "file": "de.min.mjs", "sources": ["../../../../packages/locale/lang/de.ts"], "sourcesContent": ["export default {\n  name: 'de',\n  el: {\n    breadcrumb: {\n      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON>zt',\n      today: 'Heute',\n      cancel: 'Abbrechen',\n      clear: '<PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Datum wählen',\n      selectTime: 'Uhrzeit wählen',\n      startDate: 'Startdatum',\n      startTime: 'Startzeit',\n      endDate: 'Enddatum',\n      endTime: 'Endzeit',\n      prevYear: 'Letztes Jahr',\n      nextYear: 'Nächtes Jahr',\n      prevMonth: 'Letzter Monat',\n      nextMonth: 'Nächster Monat',\n      day: 'Tag',\n      week: 'Woche',\n      month: '<PERSON><PERSON>',\n      year: '',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'M<PERSON>rz',\n      month4: 'April',\n      month5: 'Mai',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Okto<PERSON>',\n      month11: 'November',\n      month12: 'Dezember',\n      weeks: {\n        sun: 'So',\n        mon: 'Mo',\n        tue: 'Di',\n        wed: 'Mi',\n        thu: 'Do',\n        fri: 'Fr',\n        sat: 'Sa',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: '<PERSON><PERSON>r',\n        apr: 'Apr',\n        may: '<PERSON>',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dez',\n      },\n    },\n    select: {\n      loading: 'Lädt.',\n      noMatch: 'Nichts gefunden.',\n      noData: 'Keine Daten',\n      placeholder: 'Daten wählen',\n    },\n    mention: {\n      loading: 'Lädt.',\n    },\n    cascader: {\n      noMatch: 'Nichts gefunden.',\n      loading: 'Lädt.',\n      placeholder: 'Daten wählen',\n      noData: 'Keine Daten',\n    },\n    pagination: {\n      goto: 'Gehe zu',\n      pagesize: ' pro Seite',\n      total: 'Gesamt {total}',\n      pageClassifier: '',\n      page: 'Seite',\n      prev: 'Zur vorherigen Seite gehen',\n      next: 'Zur nächsten Seite gehen',\n      currentPage: 'Seite {pager}',\n      prevPages: 'Vorherige {pager} Seiten',\n      nextPages: 'Nächste {pager} Seiten',\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Abbrechen',\n      error: 'Fehler',\n    },\n    upload: {\n      deleteTip: 'Klicke löschen zum entfernen',\n      delete: 'Löschen',\n      preview: 'Vorschau',\n      continue: 'Fortsetzen',\n    },\n    table: {\n      emptyText: 'Keine Daten',\n      confirmFilter: 'Anwenden',\n      resetFilter: 'Zurücksetzen',\n      clearFilter: 'Alles ',\n      sumText: 'Summe',\n    },\n    tour: {\n      next: 'Weiter',\n      previous: 'Zurück',\n      finish: 'Fertig',\n    },\n    tree: {\n      emptyText: 'Keine Einträge',\n    },\n    transfer: {\n      noMatch: 'Nichts gefunden.',\n      noData: 'Keine Einträge',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Einträge filtern',\n      noCheckedFormat: '{total} Einträge',\n      hasCheckedFormat: '{checked}/{total} ausgewählt',\n    },\n    image: {\n      error: 'FEHLGESCHLAGEN',\n    },\n    pageHeader: {\n      title: 'Zurück',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ja',\n      cancelButtonText: 'Nein',\n    },\n    carousel: {\n      leftArrow: 'Karussell-Pfeil links',\n      rightArrow: 'Karussell-Pfeil rechts',\n      indicator: 'Karussell zu Index {index} wechseln',\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,mBAAmB,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC,0BAA0B,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,eAAe,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,UAAU,CAAC,wBAAwB,CAAC,SAAS,CAAC,qCAAqC,CAAC,CAAC,CAAC;;;;"}