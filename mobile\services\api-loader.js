/**
 * API加载器
 * 提供统一的API模块加载方法，避免动态导入路径问题
 */

/**
 * 加载用户API模块
 * @returns {Promise<Object>} 用户API模块
 */
export const loadUserApi = async () => {
  try {
    return await import('./api/user.js');
  } catch (error) {
    throw new Error('无法加载用户API模块');
  }
};

/**
 * 加载轮播图API模块
 * @returns {Promise<Object>} 轮播图API模块
 */
export const loadBannerApi = async () => {
  try {
    return await import('./api/banner.js');
  } catch (error) {
    throw new Error('无法加载轮播图API模块');
  }
};

/**
 * 加载客服API模块
 * @returns {Promise<Object>} 客服API模块
 */
export const loadCustomerServiceApi = async () => {
  try {
    return await import('./api/customerService.js');
  } catch (error) {
    throw new Error('无法加载客服API模块');
  }
};

/**
 * 加载统计数据API模块
 * @returns {Promise<Object>} 统计数据API模块
 */
export const loadStatsApi = async () => {
  try {
    return await import('./api/stats.js');
  } catch (error) {
    throw new Error('无法加载统计数据API模块');
  }
};

/**
 * 加载交易记录API模块
 * @returns {Promise<Object>} 交易记录API模块
 */
export const loadTransactionApi = async () => {
  try {
    return await import('./api/transaction.js');
  } catch (error) {
    throw new Error('无法加载交易记录API模块');
  }
};

/**
 * 加载投资API模块
 * @returns {Promise<Object>} 投资API模块
 */
export const loadInvestmentApi = async () => {
  try {
    return await import('./api/investment.js');
  } catch (error) {
    throw new Error('无法加载投资API模块');
  }
};

/**
 * 加载项目API模块
 * @returns {Promise<Object>} 项目API模块
 */
export const loadProjectApi = async () => {
  try {
    return await import('./api/project.js');
  } catch (error) {
    throw new Error('无法加载项目API模块');
  }
};
