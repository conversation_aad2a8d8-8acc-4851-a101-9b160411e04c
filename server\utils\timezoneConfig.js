/**
 * 全局时区配置模块
 * 负责加载和提供系统时区设置，作为整个系统的唯一时区源
 */
const sequelize = require('../config/database');
const moment = require('moment-timezone');

// 缓存时区设置
let systemTimezone = null;
let isInitialized = false;

/**
 * 初始化时区配置
 * 在应用启动时最早期调用
 * @returns {Promise<string>} 系统时区设置
 */
async function initialize() {
  try {
    console.log('初始化全局时区配置...');

    // 安全检查：确保sequelize已正确加载
    if (!sequelize || typeof sequelize.query !== 'function') {
      throw new Error('Sequelize实例未正确加载');
    }

    // 从数据库加载时区设置
    const [results] = await sequelize.query(
      "SELECT param_value FROM system_params WHERE param_key = '[site.timezone]' LIMIT 1"
    );

    // 方案5.1：默认使用UTC时区，支持动态配置
    let targetTimezone = '+00:00'; // 默认UTC时区

    // 如果数据库中有设置时区，则使用该设置
    if (results.length > 0 && results[0].param_value) {
      targetTimezone = results[0].param_value;
      systemTimezone = targetTimezone;
    } else {
      // 如果数据库中没有设置时区，使用UTC
      systemTimezone = targetTimezone;
    }

    // 设置 Node.js 环境变量
    process.env.TZ = convertOffsetToTZ(systemTimezone);
    process.env.SYSTEM_TIMEZONE = targetTimezone;

    // 方案5.1：动态设置数据库会话时区
    await sequelize.query(`SET time_zone = '${targetTimezone}'`);
    console.log(`数据库会话时区已设置为: ${targetTimezone}`);

    // 输出时区信息
    console.log(`系统时区已设置为: ${systemTimezone}`);
    console.log(`Node.js 环境变量 TZ: ${process.env.TZ}`);
    console.log(`当前时间: ${new Date().toString()}`);

    // 标记为已初始化
    isInitialized = true;

    return systemTimezone;
  } catch (error) {
    console.error('初始化时区配置失败:', error);
    // 出错时使用服务器本地时区
    systemTimezone = getLocalTimezoneOffset();
    return systemTimezone;
  }
}

/**
 * 获取系统时区设置
 * @returns {string} 系统时区设置
 */
function getTimezone() {
  if (!isInitialized) {
    console.warn('时区配置尚未初始化，返回默认值');
  }
  return systemTimezone || getLocalTimezoneOffset();
}

/**
 * 获取服务器本地时区偏移量
 * @returns {string} 服务器本地时区偏移量，格式为 +/-HH:MM
 */
function getLocalTimezoneOffset() {
  const now = new Date();
  const timezoneOffset = now.getTimezoneOffset();
  const hours = Math.abs(Math.floor(timezoneOffset / 60));
  const minutes = Math.abs(timezoneOffset % 60);
  const sign = timezoneOffset <= 0 ? '+' : '-';

  // 格式化为 +/-HH:MM 格式
  return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

/**
 * 将偏移量格式转换为时区名称
 * 例如: '+08:00' -> 'Asia/Shanghai'
 * 注意: 这是一个简化的转换，不考虑夏令时等复杂情况
 * @param {string} offset - 时区偏移量，格式为 +/-HH:MM
 * @returns {string} 时区名称或原始偏移量
 */
function convertOffsetToTZ(offset) {
  // 简单映射常见偏移量到时区名称
  const offsetToTZ = {
    '+08:00': 'Asia/Shanghai',    // 中国标准时间
    '+00:00': 'UTC',              // 协调世界时
    '-05:00': 'America/New_York', // 美国东部时间
    '-06:00': 'America/Chicago',  // 美国中部时间
    '-07:00': 'America/Denver',   // 美国山地时间
    '-08:00': 'America/Los_Angeles', // 美国太平洋时间
  };

  return offsetToTZ[offset] || offset;
}

/**
 * 刷新时区设置
 * 当管理员更改时区设置时调用
 * @returns {Promise<string>} 更新后的系统时区设置
 */
async function refreshTimezone() {
  try {
    console.log('刷新全局时区配置...');

    // 安全检查：确保sequelize已正确加载
    if (!sequelize || typeof sequelize.query !== 'function') {
      throw new Error('Sequelize实例未正确加载');
    }

    // 重新从数据库加载时区设置
    const [results] = await sequelize.query(
      "SELECT param_value FROM system_params WHERE param_key = '[site.timezone]' LIMIT 1"
    );

    // 方案5.1：默认使用UTC时区
    let newTimezone = '+00:00'; // 默认UTC时区

    if (results.length > 0 && results[0].param_value) {
      newTimezone = results[0].param_value;
    }

    // 更新缓存的时区设置
    systemTimezone = newTimezone;

    // 更新环境变量
    process.env.TZ = convertOffsetToTZ(newTimezone);
    process.env.SYSTEM_TIMEZONE = newTimezone;

    // 更新数据库会话时区
    await sequelize.query(`SET time_zone = '${newTimezone}'`);

    console.log(`全局时区配置已刷新为: ${newTimezone}`);
    return newTimezone;
  } catch (error) {
    console.error('刷新时区配置失败:', error);
    throw error;
  }
}

module.exports = {
  initialize,
  getTimezone,
  refreshTimezone,
  getLocalTimezoneOffset
};
