-- 添加Base支付通道配置
-- 执行此脚本前请确保已经有KBPay支付通道作为参考

-- 插入Base支付通道
INSERT INTO payment_channels (
    name,
    code,
    country_code,
    icon,
    status,
    deposit_enabled,
    withdraw_enabled,
    min_deposit_amount,
    max_deposit_amount,
    is_default,
    weight,
    config,
    created_at,
    updated_at
) VALUES (
    'Base支付',
    'basepay',
    'PH',
    NULL,
    TRUE,
    TRUE,
    TRUE,
    100.00,
    50000.00,
    FALSE,
    50,
    JSON_OBJECT(
        'merchant_no', '*********',
        'payin_key', '1c9f2952e09a4b3d9e5b8de0a185b11f',
        'payout_key', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
        'pay_type', '1720',
        'bank_code', 'IDPT0001',
        'payin_url', 'https://pay.aiffpay.com/pay/web',
        'payout_url', 'https://pay.aiffpay.com/pay/transfer'
    ),
    NOW(),
    NOW()
);

-- 查看插入的记录
SELECT * FROM payment_channels WHERE code = 'basepay';
