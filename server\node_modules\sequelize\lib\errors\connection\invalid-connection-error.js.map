{"version": 3, "sources": ["../../../src/errors/connection/invalid-connection-error.ts"], "sourcesContent": ["import ConnectionError from '../connection-error';\n\n/**\n * Thrown when a connection to a database has invalid values for any of the connection parameters\n */\nclass InvalidConnectionError extends ConnectionError {\n  constructor(parent: Error) {\n    super(parent);\n    this.name = 'SequelizeInvalidConnectionError';\n  }\n}\n\nexport default InvalidConnectionError;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,8BAA4B;AAK5B,qCAAqC,gCAAgB;AAAA,EACnD,YAAY,QAAe;AACzB,UAAM;AACN,SAAK,OAAO;AAAA;AAAA;AAIhB,IAAO,mCAAQ;", "names": []}