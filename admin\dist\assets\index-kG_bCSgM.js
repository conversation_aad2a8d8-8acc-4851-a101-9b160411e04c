/* empty css             *//* empty css                   *//* empty css                      *//* empty css                *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{d as te,r as f,a as z,q as R,o as ae,p,c as K,b as m,e as t,w as r,m as oe,f as ie,i as le,aa as re,ab as de,ac as ce,n as w,x as ue,j as k,ad as me,af as pe,aj as fe,ak as _e,V as ge,al as he,an as ve,y as ye,aP as be,ae as we,ap as ke,aq as Ce,a8 as Pe,a9 as Ee,at as xe,E as ze,h as Ae,bf as Ie,g as A,_ as Se}from"./index-LncY9lAB.js";import{g as <PERSON>,a as Ve,u as De}from"./role-B80O1_oO.js";import"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";const Re={class:"roles-container"},Ke={class:"toolbar"},Me={class:"toolbar-left"},je={class:"toolbar-right"},Fe={class:"table-wrapper"},Ne={class:"operation-buttons-container"},Be={class:"pagination-container"},Oe={class:"tree-container"},Ue={class:"dialog-footer"},Le=te({__name:"index",setup(qe){const C=f(!1),v=f([]),M=f([]),d=z({currentPage:1,pageSize:10,total:0}),y=f(""),_=f(!1),b=f(),g=f(),l=z({id:null,name:"",status:!0,permissions:[]}),j=z({permissions:[{type:"array",required:!0,message:"请选择至少一个权限",trigger:"change"}]}),u=f([]),F=async()=>{try{const e=await Ve();if(console.log("权限数据响应:",e),e&&e.code===200&&Array.isArray(e.data)){const s={dashboard:{id:"menu_dashboard",name:"首页",children:[]},members:{id:"menu_members",name:"会员列表",children:[]},deposits:{id:"menu_deposits",name:"充值订单",children:[]},investments:{id:"menu_investments",name:"用户投资",children:[]},withdrawals:{id:"menu_withdrawals",name:"取款记录",children:[]},transactions:{id:"menu_transactions",name:"用户流水",children:[]},commissions:{id:"menu_commissions",name:"佣金记录",children:[]},"user-cards":{id:"menu_user-cards",name:"用户银行卡",children:[]},"receiving-cards":{id:"menu_receiving-cards",name:"收款银行卡",children:[]},agents:{id:"menu_agents",name:"代理管理",children:[]},settings:{id:"menu_settings",name:"系统设置",children:[]},notifications:{id:"menu_notifications",name:"通知消息",children:[]},admins:{id:"menu_admins",name:"管理员设置",children:[]},roles:{id:"menu_roles",name:"角色管理",children:[]}};e.data.forEach(a=>{if(a.type==="operation"){const i=a.code.split(":")[0];s[i]&&s[i].children.push({id:a.id,name:a.name,code:a.code})}}),Object.keys(s).forEach(a=>{s[a].children.sort((i,h)=>i.id-h.id)});const o=Object.values(s).filter(a=>a.children.length>0);u.value=o;const n={menu_dashboard:1,menu_members:2,menu_deposits:3,menu_investments:4,menu_withdrawals:5,menu_transactions:6,menu_commissions:7,"menu_user-cards":8,"menu_receiving-cards":9,menu_agents:10,menu_settings:11,menu_notifications:12,menu_admins:13,menu_roles:14};u.value.sort((a,i)=>(n[a.id]||99)-(n[i.id]||99)),console.log("权限树数据:",u.value)}else console.error("权限数据格式不正确:",e),p.error("加载权限数据失败")}catch(e){console.error("加载权限数据失败",e),p.error("加载权限数据失败")}},P=R(()=>{let e=[...v.value];if(y.value){const s=y.value.toLowerCase();e=e.filter(o=>o.name&&o.name.toLowerCase().includes(s))}return e}),N=R(()=>{d.total=P.value.length;const e=(d.currentPage-1)*d.pageSize,s=e+d.pageSize;return P.value.slice(e,s)});ae(()=>{I(),F()});const I=async()=>{C.value=!0;try{const{currentPage:e,pageSize:s}=d,o={page:e,limit:s,keyword:y.value},n=await Te(o);if(console.log("角色列表响应:",n),n&&n.data){const a=n.data.items.map(i=>({id:i.id,name:i.name,status:i.status,permissions:i.permissions||[]}));v.value=a,d.total=n.data.total}else p.error(n.message||"加载数据失败")}catch(e){p.error("加载数据失败"),console.error(e)}finally{C.value=!1}},B=()=>{d.currentPage=1,I()},O=e=>{M.value=e},S=()=>{d.currentPage=1},U=((e,s)=>{let o=null;return function(...n){o&&clearTimeout(o),o=window.setTimeout(()=>{e.apply(this,n),o=null},s)}})(e=>{if(!e||_.value)return;if(e.id===1){p.warning("超级管理员组不能被编辑");return}if(e.permissions.filter(o=>{const n=u.value.flatMap(a=>a.children).find(a=>a.id===o);if(n){const a=n.code;return a.startsWith("admins:")||a.startsWith("roles:")}return!1}).length>0){p.warning("管理员设置和角色组的权限不能被编辑");return}T(),l.id=e.id||null,l.name=e.name||"",l.status=typeof e.status=="boolean"?e.status:!0,e.permissions&&Array.isArray(e.permissions)?l.permissions=[...e.permissions]:l.permissions=[],setTimeout(()=>{_.value=!0,setTimeout(()=>{G()},100)},0)},300),T=()=>{Object.assign(l,{id:null,name:"",status:!0,permissions:[]}),b.value&&b.value.resetFields()},L=(e,s)=>{if(s&&Array.isArray(s.checkedKeys)){const o=E(),n=s.checkedKeys.filter(a=>!o.includes(a));l.permissions=n,g.value&&g.value.setCheckedKeys(n)}else l.permissions=[]},q=async()=>{if(b.value)try{if(await b.value.validate()){const s=E(),n={permissions:Array.isArray(l.permissions)?l.permissions.filter(i=>!s.includes(i)):[]},a=await De(l.id,n);if(a.code===200){const i=v.value.findIndex(h=>h.id===l.id);if(i!==-1){const x={...v.value[i],permissions:Array.isArray(l.permissions)?[...l.permissions]:[]};v.value[i]=x}p.success("角色权限更新成功"),_.value=!1}else p.error(a.message||"更新失败")}}catch(e){console.log("验证失败",e),p.error("更新失败")}},V=e=>{d.pageSize=e,d.currentPage>Math.ceil(P.value.length/d.pageSize)&&(d.currentPage=1)},W=e=>{d.currentPage=e},E=()=>{const e=[];return u.value.forEach(s=>{(s.id==="menu_admins"||s.id==="menu_roles")&&s.children.forEach(o=>{e.push(o.id)})}),e},$=()=>{console.log("对话框开始关闭")},G=()=>{if(!g.value)return;const e=u.value.findIndex(n=>n.id==="menu_admins"),s=u.value.findIndex(n=>n.id==="menu_roles");e!==-1&&u.value.splice(e,1),s!==-1&&s<u.value.length&&u.value.splice(s,1);const o=E();l.permissions=l.permissions.filter(n=>!o.includes(n)),g.value&&g.value.setCheckedKeys(l.permissions)},H=()=>{setTimeout(()=>{T(),console.log("对话框已完全关闭，表单已重置")},200)};return(e,s)=>{const o=ue,n=oe,a=le,i=he,h=ve,x=ke,J=re,Q=xe,X=Ce,Y=de,D=Ae,Z=Ie,ee=ze,se=ce,ne=_e;return A(),K("div",Re,[s[9]||(s[9]=m("div",{class:"page-header"},[m("h2",{class:"page-title"},"角色组"),m("p",{class:"page-desc"},"角色组是分配给管理员的权限集合，可以根据实际需要设置不同的角色组")],-1)),m("div",Ke,[m("div",Me,[t(n,{class:"toolbar-button",type:"default",onClick:B},{default:r(()=>[t(o,null,{default:r(()=>[t(k(me))]),_:1}),s[6]||(s[6]=w("刷新 "))]),_:1})]),m("div",je,[t(a,{modelValue:y.value,"onUpdate:modelValue":s[0]||(s[0]=c=>y.value=c),placeholder:"搜索角色组名称",class:"search-input",onKeyup:ie(S,["enter"])},null,8,["modelValue"]),t(n,{class:"search-button",type:"primary",onClick:S},{default:r(()=>[t(o,null,{default:r(()=>[t(k(pe))]),_:1})]),_:1})])]),t(J,{class:"table-card"},{default:r(()=>[m("div",Fe,[fe((A(),ge(x,{ref:"rolesTable",data:N.value,border:"",stripe:"","highlight-current-row":"","row-key":"id",style:{width:"100%"},onSelectionChange:O,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:r(()=>[t(i,{type:"selection",width:"40",align:"center"}),t(i,{prop:"id",label:"ID","min-width":"70",align:"center"}),t(i,{prop:"parentId",label:"父级","min-width":"70",align:"center"}),t(i,{prop:"name",label:"名称","min-width":"120",align:"center"}),t(i,{label:"状态",width:"100",align:"center"},{default:r(c=>[t(h,{type:c.row.status?"success":"danger"},{default:r(()=>[w(ye(c.row.status?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(i,{label:"操作",width:"100",align:"center",fixed:"right"},{default:r(c=>[m("div",Ne,[t(n,{class:"operation-button icon-only",size:"small",type:"primary",onClick:be(Ge=>k(U)(c.row),["stop"]),title:"编辑角色组"},{default:r(()=>[t(o,null,{default:r(()=>[t(k(we))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[ne,C.value]])])]),_:1}),m("div",Be,[t(Y,{"current-page":d.currentPage,"onUpdate:currentPage":s[1]||(s[1]=c=>d.currentPage=c),"page-size":d.pageSize,"onUpdate:pageSize":s[2]||(s[2]=c=>d.pageSize=c),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:d.total,onSizeChange:V,onCurrentChange:W,"pager-count":7,background:""},{sizes:r(()=>[t(X,{"model-value":d.pageSize,onChange:V,class:"custom-page-size"},{default:r(()=>[(A(),K(Pe,null,Ee([10,20,50,100],c=>t(Q,{key:c,value:c,label:`${c}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),t(se,{modelValue:_.value,"onUpdate:modelValue":s[5]||(s[5]=c=>_.value=c),title:"编辑角色组",width:"650px",center:"","destroy-on-close":"","close-on-click-modal":!0,"close-on-press-escape":!0,"append-to-body":!0,onClosed:H,onClose:$},{footer:r(()=>[m("div",Ue,[t(n,{type:"primary",onClick:q},{default:r(()=>s[7]||(s[7]=[w("确定")])),_:1}),t(n,{onClick:s[4]||(s[4]=c=>_.value=!1)},{default:r(()=>s[8]||(s[8]=[w("取消")])),_:1})])]),default:r(()=>[t(ee,{ref_key:"formRef",ref:b,model:l,rules:j,"label-width":"100px",class:"role-form"},{default:r(()=>[t(D,{label:"名称",prop:"name"},{default:r(()=>[t(a,{modelValue:l.name,"onUpdate:modelValue":s[3]||(s[3]=c=>l.name=c),disabled:""},null,8,["modelValue"])]),_:1}),t(D,{label:"权限",prop:"permissions"},{default:r(()=>[m("div",Oe,[t(Z,{ref_key:"permissionTree",ref:g,data:u.value,"show-checkbox":"","node-key":"id",props:{label:"name",children:"children"},"default-checked-keys":l.permissions||[],onCheck:L,"default-expand-all":""},null,8,["data","default-checked-keys"])])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),us=Se(Le,[["__scopeId","data-v-36ff21b2"]]);export{us as default};
