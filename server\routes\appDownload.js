/**
 * APP下载路由
 */
const express = require('express');
const router = express.Router();
const appDownloadController = require('../controllers/appDownloadController');

/**
 * @swagger
 * /api/app/qrcode:
 *   get:
 *     summary: 生成APP下载二维码
 *     tags: [APP下载]
 *     responses:
 *       200:
 *         description: 生成成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "生成成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     qrCodeDataURL:
 *                       type: string
 *                       description: 二维码图片的Data URL
 *                     downloadUrl:
 *                       type: string
 *                       description: 下载链接
 *                     baseUrl:
 *                       type: string
 *                       description: 基础域名
 */
router.get('/qrcode', appDownloadController.generateQRCode);

/**
 * @swagger
 * /api/app/download-info:
 *   get:
 *     summary: 获取APP下载信息
 *     tags: [APP下载]
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     version:
 *                       type: string
 *                       description: APP版本号
 *                     size:
 *                       type: string
 *                       description: APP文件大小
 *                     updateTime:
 *                       type: string
 *                       description: 更新时间
 *                     downloadUrl:
 *                       type: string
 *                       description: 下载链接
 *                     qrCodeUrl:
 *                       type: string
 *                       description: 二维码获取链接
 */
router.get('/download-info', appDownloadController.getDownloadInfo);

/**
 * @swagger
 * /download:
 *   get:
 *     summary: APP下载重定向
 *     tags: [APP下载]
 *     description: 重定向到实际的APP下载链接
 *     responses:
 *       302:
 *         description: 重定向到下载链接
 *       500:
 *         description: 服务器错误
 */
router.get('/redirect', appDownloadController.downloadRedirect);

module.exports = router;
