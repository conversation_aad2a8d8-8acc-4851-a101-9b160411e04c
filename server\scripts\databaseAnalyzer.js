/**
 * 数据库查询性能分析脚本
 * 用于分析数据库查询性能
 */
const sequelize = require('../config/database');
const { measureAsyncFunctionExecution } = require('./performanceAnalyzer');
const fs = require('fs');
const path = require('path');

// 创建日志目录
const logDir = path.join(__dirname, '../logs/database');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * 分析 SQL 查询性能
 * @param {string} sql - SQL 查询语句
 * @param {Array} replacements - 查询参数
 * @param {string} name - 查询名称
 * @returns {Promise<Object>} - 查询结果和性能数据
 */
async function analyzeSqlQuery(sql, replacements = [], name = 'query') {
  try {
    // 使用性能分析工具测量查询执行时间
    const result = await measureAsyncFunctionExecution(
      async () => {
        const startTime = Date.now();
        const queryResult = await sequelize.query(sql, {
          replacements,
          type: sequelize.QueryTypes.SELECT,
          logging: false
        });
        const endTime = Date.now();
        
        return {
          result: queryResult,
          duration: endTime - startTime,
          rowCount: queryResult.length
        };
      },
      `DB-${name}`
    );
    
    // 获取查询执行计划
    const explainResult = await sequelize.query(`EXPLAIN ${sql}`, {
      replacements,
      type: sequelize.QueryTypes.SELECT,
      logging: false
    });
    
    return {
      name,
      sql,
      duration: `${result.duration}ms`,
      rowCount: result.rowCount,
      explain: explainResult,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      name,
      sql,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 分析常用查询
 * @returns {Promise<Array>} - 查询性能数据
 */
async function analyzeCommonQueries() {
  const queries = [
    {
      name: 'users-count',
      sql: 'SELECT COUNT(*) as count FROM users'
    },
    {
      name: 'recent-transactions',
      sql: 'SELECT * FROM transactions ORDER BY created_at DESC LIMIT 100'
    },
    {
      name: 'user-with-transactions',
      sql: `
        SELECT u.*, COUNT(t.id) as transaction_count 
        FROM users u 
        LEFT JOIN transactions t ON u.id = t.user_id 
        GROUP BY u.id 
        ORDER BY transaction_count DESC 
        LIMIT 10
      `
    },
    {
      name: 'system-params',
      sql: 'SELECT * FROM system_params'
    }
  ];
  
  console.log('开始分析数据库查询性能...');
  
  const results = [];
  for (const query of queries) {
    console.log(`分析查询: ${query.name}`);
    const result = await analyzeSqlQuery(query.sql, [], query.name);
    results.push(result);
  }
  
  console.log('\n数据库查询性能分析结果:');
  results.forEach(result => {
    console.log(`\n查询: ${result.name}`);
    console.log(`持续时间: ${result.duration}`);
    console.log(`行数: ${result.rowCount || 'N/A'}`);
    
    if (result.explain) {
      console.log('执行计划:');
      console.table(result.explain);
    }
    
    if (result.error) {
      console.log(`错误: ${result.error}`);
    }
  });
  
  // 保存结果
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filepath = path.join(logDir, `db-performance-${timestamp}.json`);
  fs.writeFileSync(filepath, JSON.stringify(results, null, 2));
  console.log(`\n数据库性能分析结果已保存到: ${filepath}`);
  
  return results;
}

/**
 * 分析表结构和索引
 * @returns {Promise<Object>} - 表结构和索引数据
 */
async function analyzeTableStructure() {
  try {
    // 获取所有表
    const tables = await sequelize.query(
      "SHOW TABLES",
      { type: sequelize.QueryTypes.SHOWTABLES }
    );
    
    const tableData = {};
    
    // 分析每个表
    for (const table of tables) {
      // 获取表结构
      const structure = await sequelize.query(
        `DESCRIBE ${table}`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      // 获取表索引
      const indices = await sequelize.query(
        `SHOW INDEX FROM ${table}`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      // 获取表统计信息
      const stats = await sequelize.query(
        `SHOW TABLE STATUS LIKE '${table}'`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      tableData[table] = {
        structure,
        indices,
        stats: stats[0]
      };
    }
    
    // 保存结果
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filepath = path.join(logDir, `table-structure-${timestamp}.json`);
    fs.writeFileSync(filepath, JSON.stringify(tableData, null, 2));
    console.log(`表结构分析结果已保存到: ${filepath}`);
    
    return tableData;
  } catch (error) {
    console.error('分析表结构时出错:', error);
    return { error: error.message };
  }
}

// 主函数
async function main() {
  try {
    console.log('开始数据库性能分析...');
    
    // 分析表结构和索引
    console.log('\n分析表结构和索引...');
    await analyzeTableStructure();
    
    // 分析常用查询
    console.log('\n分析常用查询性能...');
    await analyzeCommonQueries();
    
    console.log('\n数据库性能分析完成!');
  } catch (error) {
    console.error('数据库性能分析过程中出错:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 如果作为独立脚本运行，则执行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('脚本执行过程中出错:', error);
    process.exit(1);
  });
}

module.exports = {
  analyzeSqlQuery,
  analyzeCommonQueries,
  analyzeTableStructure
};
