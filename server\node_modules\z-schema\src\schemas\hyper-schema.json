{"$schema": "http://json-schema.org/draft-04/hyper-schema#", "id": "http://json-schema.org/draft-04/hyper-schema#", "title": "JSON Hyper-Schema", "allOf": [{"$ref": "http://json-schema.org/draft-04/schema#"}], "properties": {"additionalItems": {"anyOf": [{"type": "boolean"}, {"$ref": "#"}]}, "additionalProperties": {"anyOf": [{"type": "boolean"}, {"$ref": "#"}]}, "dependencies": {"additionalProperties": {"anyOf": [{"$ref": "#"}, {"type": "array"}]}}, "items": {"anyOf": [{"$ref": "#"}, {"$ref": "#/definitions/schemaArray"}]}, "definitions": {"additionalProperties": {"$ref": "#"}}, "patternProperties": {"additionalProperties": {"$ref": "#"}}, "properties": {"additionalProperties": {"$ref": "#"}}, "allOf": {"$ref": "#/definitions/schemaArray"}, "anyOf": {"$ref": "#/definitions/schemaArray"}, "oneOf": {"$ref": "#/definitions/schemaArray"}, "not": {"$ref": "#"}, "links": {"type": "array", "items": {"$ref": "#/definitions/linkDescription"}}, "fragmentResolution": {"type": "string"}, "media": {"type": "object", "properties": {"type": {"description": "A media type, as described in RFC 2046", "type": "string"}, "binaryEncoding": {"description": "A content encoding scheme, as described in RFC 2045", "type": "string"}}}, "pathStart": {"description": "Instances' URIs must start with this value for this schema to apply to them", "type": "string", "format": "uri"}}, "definitions": {"schemaArray": {"type": "array", "items": {"$ref": "#"}}, "linkDescription": {"title": "Link Description Object", "type": "object", "required": ["href", "rel"], "properties": {"href": {"description": "a URI template, as defined by RFC 6570, with the addition of the $, ( and ) characters for pre-processing", "type": "string"}, "rel": {"description": "relation to the target resource of the link", "type": "string"}, "title": {"description": "a title for the link", "type": "string"}, "targetSchema": {"description": "JSON Schema describing the link target", "$ref": "#"}, "mediaType": {"description": "media type (as defined by RFC 2046) describing the link target", "type": "string"}, "method": {"description": "method for requesting the target of the link (e.g. for HTTP this might be \"GET\" or \"DELETE\")", "type": "string"}, "encType": {"description": "The media type in which to submit data along with the request", "type": "string", "default": "application/json"}, "schema": {"description": "<PERSON><PERSON><PERSON> describing the data to submit along with the request", "$ref": "#"}}}}}