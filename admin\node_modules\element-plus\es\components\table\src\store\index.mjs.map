{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../packages/components/table/src/store/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport { getCurrentInstance, nextTick, unref } from 'vue'\nimport { isNull } from 'lodash-unified'\nimport { useNamespace } from '@element-plus/hooks'\nimport useWatcher from './watcher'\n\nimport type { Ref } from 'vue'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { Filter, Sort, Table } from '../table/defaults'\n\ninterface WatcherPropsData<T> {\n  data: Ref<T[]>\n  rowKey: Ref<string>\n}\n\nfunction replaceColumn<T>(\n  array: TableColumnCtx<T>[],\n  column: TableColumnCtx<T>\n) {\n  return array.map((item) => {\n    if (item.id === column.id) {\n      return column\n    } else if (item.children?.length) {\n      item.children = replaceColumn(item.children, column)\n    }\n    return item\n  })\n}\n\nfunction sortColumn<T>(array: TableColumnCtx<T>[]) {\n  array.forEach((item) => {\n    item.no = item.getColumnIndex?.()\n    if (item.children?.length) {\n      sortColumn(item.children)\n    }\n  })\n  array.sort((cur, pre) => cur.no - pre.no)\n}\n\nfunction useStore<T>() {\n  const instance = getCurrentInstance() as Table<T>\n  const watcher = useWatcher<T>()\n  const ns = useNamespace('table')\n  type StoreStates = typeof watcher.states\n  const mutations = {\n    setData(states: StoreStates, data: T[]) {\n      const dataInstanceChanged = unref(states._data) !== data\n      states.data.value = data\n      states._data.value = data\n      instance.store.execQuery()\n      // 数据变化，更新部分数据。\n      // 没有使用 computed，而是手动更新部分数据 https://github.com/vuejs/vue/issues/6660#issuecomment-331417140\n      instance.store.updateCurrentRowData()\n      instance.store.updateExpandRows()\n      instance.store.updateTreeData(\n        instance.store.states.defaultExpandAll.value\n      )\n      if (unref(states.reserveSelection)) {\n        instance.store.assertRowKey()\n        instance.store.updateSelectionByRowKey()\n      } else {\n        if (dataInstanceChanged) {\n          instance.store.clearSelection()\n        } else {\n          instance.store.cleanSelection()\n        }\n      }\n      instance.store.updateAllSelected()\n      if (instance.$ready) {\n        instance.store.scheduleLayout()\n      }\n    },\n\n    insertColumn(\n      states: StoreStates,\n      column: TableColumnCtx<T>,\n      parent: TableColumnCtx<T>,\n      updateColumnOrder: () => void\n    ) {\n      const array = unref(states._columns)\n      let newColumns = []\n      if (!parent) {\n        array.push(column)\n        newColumns = array\n      } else {\n        if (parent && !parent.children) {\n          parent.children = []\n        }\n        parent.children.push(column)\n        newColumns = replaceColumn(array, parent)\n      }\n      sortColumn(newColumns)\n      states._columns.value = newColumns\n      states.updateOrderFns.push(updateColumnOrder)\n      if (column.type === 'selection') {\n        states.selectable.value = column.selectable\n        states.reserveSelection.value = column.reserveSelection\n      }\n      if (instance.$ready) {\n        instance.store.updateColumns() // hack for dynamics insert column\n        instance.store.scheduleLayout()\n      }\n    },\n\n    updateColumnOrder(states: StoreStates, column: TableColumnCtx<T>) {\n      const newColumnIndex = column.getColumnIndex?.()\n      if (newColumnIndex === column.no) return\n\n      sortColumn(states._columns.value)\n\n      if (instance.$ready) {\n        instance.store.updateColumns()\n      }\n    },\n\n    removeColumn(\n      states: StoreStates,\n      column: TableColumnCtx<T>,\n      parent: TableColumnCtx<T>,\n      updateColumnOrder: () => void\n    ) {\n      const array = unref(states._columns) || []\n      if (parent) {\n        parent.children.splice(\n          parent.children.findIndex((item) => item.id === column.id),\n          1\n        )\n        // fix #10699, delete parent.children immediately will trigger again\n        nextTick(() => {\n          if (parent.children?.length === 0) {\n            delete parent.children\n          }\n        })\n        states._columns.value = replaceColumn(array, parent)\n      } else {\n        const index = array.indexOf(column)\n        if (index > -1) {\n          array.splice(index, 1)\n          states._columns.value = array\n        }\n      }\n\n      const updateFnIndex = states.updateOrderFns.indexOf(updateColumnOrder)\n      updateFnIndex > -1 && states.updateOrderFns.splice(updateFnIndex, 1)\n\n      if (instance.$ready) {\n        instance.store.updateColumns() // hack for dynamics remove column\n        instance.store.scheduleLayout()\n      }\n    },\n\n    sort(states: StoreStates, options: Sort) {\n      const { prop, order, init } = options\n      if (prop) {\n        const column = unref(states.columns).find(\n          (column) => column.property === prop\n        )\n        if (column) {\n          column.order = order\n          instance.store.updateSort(column, prop, order)\n          instance.store.commit('changeSortCondition', { init })\n        }\n      }\n    },\n\n    changeSortCondition(states: StoreStates, options: Sort) {\n      // 修复 pr https://github.com/ElemeFE/element/pull/15012 导致的 bug\n      // https://github.com/element-plus/element-plus/pull/4640\n      const { sortingColumn, sortProp, sortOrder } = states\n      const columnValue = unref(sortingColumn),\n        propValue = unref(sortProp),\n        orderValue = unref(sortOrder)\n      if (isNull(orderValue)) {\n        states.sortingColumn.value = null\n        states.sortProp.value = null\n      }\n      const ignore = { filter: true }\n      instance.store.execQuery(ignore)\n\n      if (!options || !(options.silent || options.init)) {\n        instance.emit('sort-change', {\n          column: columnValue,\n          prop: propValue,\n          order: orderValue,\n        })\n      }\n\n      instance.store.updateTableScrollY()\n    },\n\n    filterChange(_states: StoreStates, options: Filter<T>) {\n      const { column, values, silent } = options\n      const newFilters = instance.store.updateFilters(column, values)\n      instance.store.execQuery()\n\n      if (!silent) {\n        instance.emit('filter-change', newFilters)\n      }\n      instance.store.updateTableScrollY()\n    },\n\n    toggleAllSelection() {\n      instance.store.toggleAllSelection()\n    },\n\n    rowSelectedChanged(_states, row: T) {\n      instance.store.toggleRowSelection(row)\n      instance.store.updateAllSelected()\n    },\n\n    setHoverRow(states: StoreStates, row: T) {\n      states.hoverRow.value = row\n    },\n\n    setCurrentRow(_states, row: T) {\n      instance.store.updateCurrentRow(row)\n    },\n  }\n  const commit = function (name: keyof typeof mutations, ...args) {\n    const mutations = instance.store.mutations\n    if (mutations[name]) {\n      mutations[name].apply(instance, [instance.store.states].concat(args))\n    } else {\n      throw new Error(`Action not found: ${name}`)\n    }\n  }\n  const updateTableScrollY = function () {\n    nextTick(() => instance.layout.updateScrollY.apply(instance.layout))\n  }\n  return {\n    ns,\n    ...watcher,\n    mutations,\n    commit,\n    updateTableScrollY,\n  }\n}\n\nexport default useStore\n\nclass HelperStore<T> {\n  Return = useStore<T>()\n}\n\ntype StoreFilter = Record<string, string[]>\ntype Store<T> = HelperStore<T>['Return']\nexport type { WatcherPropsData, Store, StoreFilter }\n"], "names": [], "mappings": ";;;;;AAIA,SAAS,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE;AACtC,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAC7B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE;AAC/B,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK,MAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;AAClE,MAAM,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC1B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1E,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;AAC3D,MAAM,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAC5C,CAAC;AACD,SAAS,QAAQ,GAAG;AACpB,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;AAC/B,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACnC,EAAE,MAAM,SAAS,GAAG;AACpB,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE;AAC1B,MAAM,MAAM,mBAAmB,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC;AAC/D,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC/B,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AAChC,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;AACjC,MAAM,QAAQ,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;AAC5C,MAAM,QAAQ,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;AACxC,MAAM,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAClF,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE;AAC1C,QAAQ,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AACtC,QAAQ,QAAQ,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC;AACjD,OAAO,MAAM;AACb,QAAQ,IAAI,mBAAmB,EAAE;AACjC,UAAU,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1C,SAAS,MAAM;AACf,UAAU,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;AAC1C,SAAS;AACT,OAAO;AACP,MAAM,QAAQ,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;AACzC,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B,QAAQ,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;AACxC,OAAO;AACP,KAAK;AACL,IAAI,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE;AAC5D,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC3C,MAAM,IAAI,UAAU,GAAG,EAAE,CAAC;AAC1B,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQ,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC3B,QAAQ,UAAU,GAAG,KAAK,CAAC;AAC3B,OAAO,MAAM;AACb,QAAQ,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACxC,UAAU,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC/B,SAAS;AACT,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,QAAQ,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAClD,OAAO;AACP,MAAM,UAAU,CAAC,UAAU,CAAC,CAAC;AAC7B,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC;AACzC,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACpD,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;AACvC,QAAQ,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;AACpD,QAAQ,MAAM,CAAC,gBAAgB,CAAC,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAChE,OAAO;AACP,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B,QAAQ,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;AACvC,QAAQ,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;AACxC,OAAO;AACP,KAAK;AACL,IAAI,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE;AACtC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,cAAc,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC7F,MAAM,IAAI,cAAc,KAAK,MAAM,CAAC,EAAE;AACtC,QAAQ,OAAO;AACf,MAAM,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxC,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B,QAAQ,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE;AAC5D,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACjD,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9F,QAAQ,QAAQ,CAAC,MAAM;AACvB,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,IAAI,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,MAAM,CAAC,EAAE;AAC3E,YAAY,OAAO,MAAM,CAAC,QAAQ,CAAC;AACnC,WAAW;AACX,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7D,OAAO,MAAM;AACb,QAAQ,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC5C,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACxB,UAAU,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACjC,UAAU,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AACxC,SAAS;AACT,OAAO;AACP,MAAM,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC7E,MAAM,aAAa,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B,QAAQ,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;AACvC,QAAQ,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;AACxC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE;AAC1B,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AAC5C,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;AAC1F,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AAC/B,UAAU,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACzD,UAAU,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;AACjE,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,mBAAmB,CAAC,MAAM,EAAE,OAAO,EAAE;AACzC,MAAM,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;AAC5D,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,aAAa,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AAC3G,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE;AAC9B,QAAQ,MAAM,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1C,QAAQ,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AACrC,OAAO;AACP,MAAM,MAAM,MAAM,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACtC,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACvC,MAAM,IAAI,CAAC,OAAO,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;AACzD,QAAQ,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE;AACrC,UAAU,MAAM,EAAE,WAAW;AAC7B,UAAU,IAAI,EAAE,SAAS;AACzB,UAAU,KAAK,EAAE,UAAU;AAC3B,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;AAC1C,KAAK;AACL,IAAI,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE;AACnC,MAAM,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AACjD,MAAM,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACtE,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;AACjC,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQ,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;AACnD,OAAO;AACP,MAAM,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;AAC1C,KAAK;AACL,IAAI,kBAAkB,GAAG;AACzB,MAAM,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;AAC1C,KAAK;AACL,IAAI,kBAAkB,CAAC,OAAO,EAAE,GAAG,EAAE;AACrC,MAAM,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC7C,MAAM,QAAQ,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;AACzC,KAAK;AACL,IAAI,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE;AAC7B,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAC;AAClC,KAAK;AACL,IAAI,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE;AAChC,MAAM,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC3C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,SAAS,IAAI,EAAE,GAAG,IAAI,EAAE;AACzC,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;AAChD,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;AAC1B,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7E,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACnD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,WAAW;AACxC,IAAI,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACzE,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,GAAG,OAAO;AACd,IAAI,SAAS;AACb,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,GAAG,CAAC;AACJ;;;;"}