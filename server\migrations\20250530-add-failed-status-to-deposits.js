'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('开始添加failed状态到deposits表的status字段...');
      
      // 检查表是否存在
      const [tables] = await queryInterface.sequelize.query("SHOW TABLES LIKE 'deposits'");
      if (tables.length === 0) {
        console.log('deposits表不存在，跳过迁移');
        return;
      }

      // 获取当前ENUM值
      const [columns] = await queryInterface.sequelize.query(`
        SHOW COLUMNS FROM deposits WHERE Field = 'status'
      `);
      
      if (columns.length === 0) {
        console.log('status字段不存在，跳过迁移');
        return;
      }

      const currentType = columns[0].Type;
      console.log('当前status字段类型:', currentType);

      // 检查是否已经包含failed状态
      if (currentType.includes('failed')) {
        console.log('status字段已包含failed状态，跳过迁移');
        return;
      }

      // 修改ENUM类型，添加failed状态
      await queryInterface.sequelize.query(`
        ALTER TABLE deposits 
        MODIFY COLUMN status ENUM('pending', 'paid', 'cancelled', 'completed', 'failed') 
        NOT NULL DEFAULT 'pending' 
        COMMENT '状态：pending=待支付, paid=已支付, cancelled=已取消, completed=已完成, failed=失败'
      `);

      console.log('成功添加failed状态到deposits表的status字段');
    } catch (error) {
      console.error('添加failed状态失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      console.log('开始回滚：移除failed状态...');
      
      // 检查表是否存在
      const [tables] = await queryInterface.sequelize.query("SHOW TABLES LIKE 'deposits'");
      if (tables.length === 0) {
        console.log('deposits表不存在，跳过回滚');
        return;
      }

      // 检查是否有记录使用failed状态
      const [failedRecords] = await queryInterface.sequelize.query(`
        SELECT COUNT(*) as count FROM deposits WHERE status = 'failed'
      `);

      if (failedRecords[0].count > 0) {
        console.log(`警告：有${failedRecords[0].count}条记录使用failed状态，将其改为cancelled状态`);
        
        // 将failed状态的记录改为cancelled
        await queryInterface.sequelize.query(`
          UPDATE deposits SET status = 'cancelled' WHERE status = 'failed'
        `);
      }

      // 移除failed状态
      await queryInterface.sequelize.query(`
        ALTER TABLE deposits 
        MODIFY COLUMN status ENUM('pending', 'paid', 'cancelled', 'completed') 
        NOT NULL DEFAULT 'pending' 
        COMMENT '状态：pending=待支付, paid=已支付, cancelled=已取消, completed=已完成'
      `);

      console.log('成功移除failed状态');
    } catch (error) {
      console.error('回滚失败:', error);
      throw error;
    }
  }
};
