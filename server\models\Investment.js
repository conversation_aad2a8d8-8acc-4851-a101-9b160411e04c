const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Investment = sequelize.define('Investment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
  },

  project_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '项目ID',
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '投资金额',
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '购买数量',
  },
  profit_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    comment: '收益率(%)',
  },
  profit_cycle: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '收益周期(天)',
  },
  total_profit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总收益',
  },
  profit_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '已发放收益次数',
  },
  last_profit_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后收益时间',
  },
  status: {
    type: DataTypes.ENUM('active', 'paused', 'completed'),
    allowNull: false,
    defaultValue: 'active',
    comment: '状态：active=进行中, paused=暂停, completed=已完成',
  },
  start_time: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '开始时间',
  },
  end_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '结束时间',
  },
  is_gift: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否为赠送投资',
  },
  // 虚拟字段，用于在前端显示投资方式
  investment_type: {
    type: DataTypes.VIRTUAL,
    get() {
      return this.is_gift ? '赠送' : '购买';
    },
    set(value) {
      this.setDataValue('is_gift', value === '赠送');
    }
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'investments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Investment.associate = (models) => {
  // 投资与用户 (通过user_id)
  Investment.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });

  // 投资与项目
  Investment.belongsTo(models.Project, {
    foreignKey: 'project_id',
    as: 'project',
  });

  // 投资与收益记录
  Investment.hasMany(models.InvestmentProfit, {
    foreignKey: 'investment_id',
    as: 'profits',
  });

  // 投资与佣金记录
  Investment.hasMany(models.Commission, {
    foreignKey: 'investment_id',
    as: 'commissions',
  });
};

module.exports = Investment;
