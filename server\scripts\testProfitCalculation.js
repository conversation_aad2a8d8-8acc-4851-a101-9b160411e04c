/**
 * 测试投资收益计算逻辑
 * 
 * 这个脚本用于测试修改后的投资收益计算逻辑是否正确
 */

// 导入必要的模块
const { sequelize } = require('../models');
const { Investment, Project, User } = require('../models');
const profitService = require('../services/profitService');

// 测试收益计算函数
async function testProfitCalculation() {
  console.log('开始测试收益计算逻辑...');

  try {
    // 获取一个活跃的投资记录用于测试
    const investment = await Investment.findOne({
      where: { status: 'active' },
      include: [
        { model: Project, as: 'project' },
        { model: User, as: 'user' }
      ]
    });

    if (!investment) {
      console.log('没有找到活跃的投资记录，无法进行测试');
      return;
    }

    console.log('测试投资记录:');
    console.log(`ID: ${investment.id}`);
    console.log(`用户: ${investment.user.username} (ID: ${investment.user_id})`);
    console.log(`项目: ${investment.project.name} (ID: ${investment.project_id})`);
    console.log(`投资金额: ${investment.amount}`);
    console.log(`收益率: ${investment.profit_rate}%`);
    console.log(`收益周期: ${investment.profit_cycle}小时`);
    console.log(`已获得收益次数: ${investment.profit_count}次`);
    console.log(`总收益: ${investment.total_profit}`);
    console.log(`最后收益时间: ${investment.last_profit_time}`);
    console.log(`投资状态: ${investment.status}`);
    console.log('---');

    // 测试旧的收益计算方式（年化收益率）
    const oldCalculation = investment.amount * (investment.profit_rate / 365 / 100);
    console.log(`旧的收益计算方式（年化收益率）: ${oldCalculation.toFixed(2)}`);

    // 测试新的收益计算方式（直接百分比）
    const newCalculation = investment.amount * (investment.profit_rate / 100);
    console.log(`新的收益计算方式（直接百分比）: ${newCalculation.toFixed(2)}`);

    // 使用profitService的calculateProfit函数计算
    const profit = profitService.calculateProfit(investment, investment.project);
    console.log(`profitService计算结果: ${profit.amount}`);

    // 验证计算结果是否符合预期
    if (parseFloat(profit.amount) === parseFloat(newCalculation.toFixed(2))) {
      console.log('✅ 收益计算逻辑修改成功！');
    } else {
      console.log('❌ 收益计算逻辑修改失败！');
      console.log(`期望结果: ${newCalculation.toFixed(2)}, 实际结果: ${profit.amount}`);
    }

    // 计算投资周期内可能的最大收益次数
    const duration = investment.project.duration;
    const durationUnit = investment.project.duration_unit;
    const profitCycle = investment.profit_cycle;
    const maxProfitTimes = investment.project.max_profit_times;

    // 将投资周期转换为小时
    let durationInHours = 0;
    switch (durationUnit) {
      case 'day':
        durationInHours = duration * 24;
        break;
      case 'week':
        durationInHours = duration * 7 * 24;
        break;
      case 'month':
        durationInHours = duration * 30 * 24;
        break;
      case 'year':
        durationInHours = duration * 365 * 24;
        break;
      default:
        durationInHours = duration * 24;
    }

    // 计算投资周期内可能的最大收益次数
    const maxPossibleTimes = Math.floor(durationInHours / profitCycle);

    // 如果设置了最大收益次数限制，取较小值
    const actualTimes = maxProfitTimes > 0 ? Math.min(maxPossibleTimes, maxProfitTimes) : maxPossibleTimes;

    // 计算总收益
    const totalProfit = newCalculation * actualTimes;

    console.log('---');
    console.log('投资周期计算:');
    console.log(`投资周期: ${duration} ${durationUnit}`);
    console.log(`投资周期转换为小时: ${durationInHours}小时`);
    console.log(`收益周期: ${profitCycle}小时`);
    console.log(`最大收益次数限制: ${maxProfitTimes === 0 ? '无限制' : maxProfitTimes}次`);
    console.log(`投资周期内可能的最大收益次数: ${maxPossibleTimes}次`);
    console.log(`实际最大收益次数: ${actualTimes}次`);
    console.log(`预计总收益: ${totalProfit.toFixed(2)}`);

  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行测试
testProfitCalculation()
  .then(() => {
    console.log('测试完成');
    process.exit(0);
  })
  .catch(err => {
    console.error('测试失败:', err);
    process.exit(1);
  });
