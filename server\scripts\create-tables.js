require('dotenv').config();
const sequelize = require('../config/database');
const { Role, Permission, RolePermission, AdminRole } = require('../models');

async function createTables() {
  try {
    // 创建角色表
    await sequelize.query('DROP TABLE IF EXISTS `role_permissions`;');
    await sequelize.query('DROP TABLE IF EXISTS `admin_roles`;');
    await sequelize.query('DROP TABLE IF EXISTS `roles`;');
    await sequelize.query('CREATE TABLE IF NOT EXISTS `roles` (`id` INTEGER auto_increment , `name` VARCHAR(50) NOT NULL UNIQUE, `description` VARCHAR(255), `parent_id` INTEGER DEFAULT 0, `status` TINYINT(1) NOT NULL DEFAULT true, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`)) ENGINE=InnoDB;');
    console.log('角色表创建成功');

    // 创建权限表
    await sequelize.query('DROP TABLE IF EXISTS `permissions`;');
    await sequelize.query('CREATE TABLE IF NOT EXISTS `permissions` (`id` INTEGER auto_increment , `name` VARCHAR(50) NOT NULL UNIQUE, `description` VARCHAR(255), `module` VARCHAR(50) NOT NULL, `action` VARCHAR(50) NOT NULL, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`)) ENGINE=InnoDB;');
    console.log('权限表创建成功');

    // 创建角色权限关联表
    await sequelize.query('CREATE TABLE IF NOT EXISTS `role_permissions` (`id` INTEGER auto_increment , `role_id` INTEGER NOT NULL, `permission_id` INTEGER NOT NULL, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, UNIQUE `role_permissions_role_id_permission_id` (`role_id`, `permission_id`), PRIMARY KEY (`id`), FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;');
    console.log('角色权限关联表创建成功');

    // 创建管理员角色关联表
    await sequelize.query('CREATE TABLE IF NOT EXISTS `admin_roles` (`id` INTEGER auto_increment , `admin_id` INTEGER NOT NULL, `role_id` INTEGER NOT NULL, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, UNIQUE `admin_roles_admin_id_role_id` (`admin_id`, `role_id`), PRIMARY KEY (`id`), FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;');
    console.log('管理员角色关联表创建成功');

    console.log('所有表创建成功');
    process.exit(0);
  } catch (error) {
    console.error('创建表失败:', error);
    process.exit(1);
  }
}

createTables();
