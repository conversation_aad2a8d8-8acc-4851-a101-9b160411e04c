/**
 * 每日统计数据更新脚本
 * 用于定时更新前一天的统计数据
 */
const moment = require('moment');
const statisticsService = require('../services/statisticsService');
const logger = require('../utils/logger');

async function main() {
  try {
    // 获取昨天的日期
    const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
    
    logger.info(`开始更新 ${yesterday} 的统计数据`);
    
    // 更新昨天的统计数据
    const result = await statisticsService.updateDailyStatistics(yesterday);
    
    if (result.success) {
      logger.info(`${yesterday} 的统计数据更新成功`);
    } else {
      logger.error(`${yesterday} 的统计数据更新失败: ${result.message}`);
    }
    
    // 退出进程
    process.exit(0);
  } catch (error) {
    logger.error(`统计数据更新失败: ${error.message}`, error);
    process.exit(1);
  }
}

// 执行主函数
main();
