const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Withdrawal = sequelize.define('Withdrawal', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
  },
  order_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '订单号',
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '提现金额',
  },
  actual_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '实际到账金额',
  },
  bank_card_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '银行卡ID',
  },
  transaction_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '交易ID',
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'processing', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态：pending=待审核, approved=审核通过, rejected=已拒绝, processing=处理中, completed=已完成, failed=失败',
  },
  payment_channel_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '支付通道ID',
  },
  payment_channel_order_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '支付通道订单号',
  },
  payment_platform_order_no: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '支付平台订单号',
  },
  payment_channel_data: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '支付通道返回数据',
  },
  callback_status: {
    type: DataTypes.ENUM('none', 'channel_callback', 'manual'),
    allowNull: false,
    defaultValue: 'none',
    comment: '回调状态：none=未回调, channel_callback=通道回调, manual=手动完成',
  },
  fee: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    defaultValue: 0,
    comment: '手续费',
  },
  remark: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '备注',
  },
  approval_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '审核时间',
  },
  completion_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '完成时间',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'withdrawals',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
Withdrawal.associate = (models) => {
  // 提现与用户
  Withdrawal.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });

  // 提现与银行卡
  Withdrawal.belongsTo(models.BankCard, {
    foreignKey: 'bank_card_id',
    as: 'bank_card',
  });

  // 提现与交易
  Withdrawal.belongsTo(models.Transaction, {
    foreignKey: 'transaction_id',
    as: 'transaction',
  });

  // 提现与退款交易
  Withdrawal.belongsTo(models.Transaction, {
    foreignKey: 'refund_transaction_id',
    as: 'refund_transaction',
  });
};

module.exports = Withdrawal;
