# 团队页面动态返佣逻辑实现

## 🎯 **实现概述**

保持原有的样式布局不变，但修改逻辑来实现动态返佣展示：
- 只显示返佣比例大于0的级别
- 如果所有级别都是0%，则隐藏整个团队信息区域
- 支持非连续级别展示（如1级和3级有效，2级无效）

## 📊 **当前测试配置**

### **收益返佣比例配置**
```javascript
commissionRates: {
  1: 10,  // Level 1: 10% - 显示
  2: 0,   // Level 2: 0%  - 不显示
  3: 5    // Level 3: 5%  - 显示
}
```

### **团队数据**
```javascript
teamData: {
  1: { members: 45, commission: '1200.50' },
  2: { members: 0, commission: '0.00' },
  3: { members: 12, commission: '245.80' }
}
```

### **预期显示效果**
- ✅ Level 1 (10%) - 45 Members, ₱1,200.50
- ❌ Level 2 (0%) - 不显示
- ✅ Level 3 (5%) - 12 Members, ₱245.80

## 🔧 **核心实现逻辑**

### **1. 动态级别计算**
```javascript
computed: {
  activeTeamLevels() {
    const activeLevels = [];
    
    // 遍历1-3级，找出返佣比例大于0的级别
    for (let level = 1; level <= 3; level++) {
      const rate = this.commissionRates[level];
      if (rate && rate > 0) {
        const teamInfo = this.teamData[level] || { members: 0, commission: '0.00' };
        activeLevels.push({
          level: level,
          rate: rate,
          members: teamInfo.members,
          commission: teamInfo.commission
        });
      }
    }
    
    return activeLevels;
  }
}
```

### **2. 模板条件渲染**
```html
<!-- 只有当有有效级别时才显示团队容器 -->
<view class="team-container" v-if="activeTeamLevels.length > 0">
  <text class="team-title">Team</text>
  
  <!-- 遍历有效级别 -->
  <view class="team-card" v-for="(level, index) in activeTeamLevels" :key="index">
    <!-- 显示实际级别数字和返佣比例 -->
    <text class="level-title">Level {{ level.level }}</text>
    <text class="level-rate">{{ level.rate }}%</text>
    <!-- 其他内容保持不变 -->
  </view>
</view>
```

## 📱 **界面变化**

### **原来的显示**
```
Team
┌─────────────────────────────────┐
│ Level 1    Up to 10%            │
│ Earnings: ₱0  Referrals: 0      │
└─────────────────────────────────┘
┌─────────────────────────────────┐
│ Level 2    Up to 3%             │
│ Earnings: ₱0  Referrals: 0      │
└─────────────────────────────────┘
┌─────────────────────────────────┐
│ Level 3    Up to 2%             │
│ Earnings: ₱0  Referrals: 0      │
└─────────────────────────────────┘
```

### **现在的显示（基于测试数据）**
```
Team
┌─────────────────────────────────┐
│ Level 1    10%                  │
│ Earnings: ₱1,200.50  Referrals: 45 │
└─────────────────────────────────┘
┌─────────────────────────────────┐
│ Level 3    5%                   │
│ Earnings: ₱245.80  Referrals: 12  │
└─────────────────────────────────┘
```

## 🔴 **需要后台交互获取的数据**

### **1. 系统参数 - 收益返佣比例**
```javascript
// 需要获取的系统参数
const params = [
  'site.income_commission_rate_1',
  'site.income_commission_rate_2', 
  'site.income_commission_rate_3'
];

// 建议API调用
const rates = await getSystemParams(params);
// 或者
const rates = await getIncomeCommissionRates();
```

### **2. 团队统计数据**
```javascript
// 需要获取的团队数据
const teamStats = await getUserCommissionStats();
// 或者现有的API调用，获取各级别的：
// - 推荐人数 (members)
// - 收益返佣金额 (commission)
```

## ✅ **真实数据集成完成**

### **1. 返佣比例配置 - 从系统参数获取**
```javascript
// 在 fetchCommissionRates() 方法中实现
async fetchCommissionRates() {
  try {
    const promises = [
      getSystemParam('[site.income_commission_rate_1]'),
      getSystemParam('[site.income_commission_rate_2]'),
      getSystemParam('[site.income_commission_rate_3]')
    ];

    const responses = await Promise.all(promises);
    responses.forEach((response, index) => {
      const level = index + 1;
      if (response && response.code === 200 && response.data) {
        const rate = parseFloat(response.data.param_value) || 0;
        this.commissionRates[level] = rate;
      }
    });
  } catch (error) {
    console.error('获取收益返佣比例失败:', error);
  }
}
```

### **2. 团队数据 - 从API获取**
```javascript
// 成员数量：从 fetchLevelInvitees() 方法获取
async fetchLevelInvitees(level) {
  const response = await getInvitees({ level, page: 1, limit: 1 });
  if (response && response.code === 200 && response.data) {
    this.teamData[level].members = response.data.total;
  }
}

// 佣金金额：从 fetchCommissionStats() 方法获取
async fetchCommissionStats() {
  const response = await getUserCommissionStats();
  if (response && response.code === 200 && response.data) {
    const { level_stats: levelStats } = response.data;
    levelStats.forEach(stat => {
      if (stat.level >= 1 && stat.level <= 3) {
        this.teamData[stat.level].commission = parseFloat(stat.total_amount || 0).toFixed(2);
      }
    });
  }
}
```

## 🎨 **样式保持不变**

- ✅ 保留原有的 `team-card` 样式
- ✅ 保留原有的布局结构
- ✅ 保留原有的颜色和间距
- ✅ 保留原有的交互效果

## 🔄 **测试场景**

### **场景1：只启用1级返佣**
```javascript
commissionRates: { 1: 10, 2: 0, 3: 0 }
// 结果：只显示1个Level 1卡片
```

### **场景2：启用1级和3级返佣**
```javascript
commissionRates: { 1: 10, 2: 0, 3: 5 }
// 结果：显示Level 1和Level 3两个卡片（当前测试）
```

### **场景3：启用所有级别**
```javascript
commissionRates: { 1: 10, 2: 5, 3: 3 }
// 结果：显示三个卡片
```

### **场景4：全部禁用**
```javascript
commissionRates: { 1: 0, 2: 0, 3: 0 }
// 结果：团队信息区域完全隐藏
```

## 🔄 **API调用流程**

### **页面加载时的调用顺序**
```javascript
onLoad() {
  // 1. 获取网站域名
  this.fetchSiteDomain();

  // 2. 获取邀请码
  this.fetchInviteCode();

  // 3. 获取收益返佣比例配置 ⭐ 新增
  this.fetchCommissionRates();

  // 4. 获取邀请列表（成员数量）
  this.fetchInvitees();

  // 5. 获取团队规则
  this.fetchTeamRules();

  // 6. 获取佣金统计数据（佣金金额）⭐ 修改
  this.fetchCommissionStats();
}
```

### **数据更新机制**
1. **返佣比例**：页面加载时获取，缓存在 `commissionRates` 中
2. **成员数量**：通过 `getInvitees` API 获取各级别总数
3. **佣金金额**：通过 `getUserCommissionStats` API 获取各级别佣金总额
4. **动态渲染**：计算属性 `activeTeamLevels` 自动响应数据变化

### **错误处理**
- ✅ API调用失败时使用默认值
- ✅ 数据格式异常时的容错处理
- ✅ 网络错误时的用户提示

## ✨ **实现特点**

- 🎯 **逻辑驱动**：完全基于返佣比例配置动态展示
- 🎨 **样式保持**：保留原有的美观界面
- 🔧 **易于维护**：清晰的数据结构和计算逻辑
- 📱 **用户友好**：不显示无效的0%级别，避免困惑
- 🚀 **性能优化**：使用计算属性，自动响应数据变化

这样实现既满足了动态展示的需求，又保持了原有界面的美观性和一致性。
