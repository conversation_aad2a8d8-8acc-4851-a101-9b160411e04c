const express = require('express');
const router = express.Router();
const configController = require('../controllers/configController');
const { verifyAdminToken } = require('../middlewares/authMiddleware');

// 所有路由都需要验证token
router.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/config/project-types:
 *   get:
 *     summary: 获取项目类型列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/project-types', configController.getProjectTypes);

/**
 * @swagger
 * /api/admin/config/project-categories:
 *   get:
 *     summary: 获取项目分类列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/project-categories', configController.getProjectCategories);

/**
 * @swagger
 * /api/admin/config/currency-types:
 *   get:
 *     summary: 获取货币类型列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/currency-types', configController.getCurrencyTypes);

/**
 * @swagger
 * /api/admin/config/price-types:
 *   get:
 *     summary: 获取价格类型列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/price-types', configController.getPriceTypes);

/**
 * @swagger
 * /api/admin/config/payment-methods:
 *   get:
 *     summary: 获取支付方式列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/payment-methods', configController.getPaymentMethods);

/**
 * @swagger
 * /api/admin/config/status-options:
 *   get:
 *     summary: 获取状态列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/status-options', configController.getStatusOptions);

/**
 * @swagger
 * /api/admin/config/sell-status-options:
 *   get:
 *     summary: 获取出售状态列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/sell-status-options', configController.getSellStatusOptions);

/**
 * @swagger
 * /api/admin/config/weekly-profit-days:
 *   get:
 *     summary: 获取每周收益日列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/weekly-profit-days', configController.getWeeklyProfitDays);

/**
 * @swagger
 * /api/admin/config/timezones:
 *   get:
 *     summary: 获取时区列表
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/timezones', configController.getTimezones);

/**
 * @swagger
 * /api/admin/config/all:
 *   get:
 *     summary: 获取所有配置数据
 *     tags: [配置管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
router.get('/all', configController.getAllConfigs);

module.exports = router;
