{"name": "set-function-name", "version": "2.0.2", "description": "Set a function's name property", "main": "index.js", "types": "./index.d.ts", "directories": {"test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-function-name.git"}, "keywords": ["set", "assign", "function", "name", "function.name"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/set-function-name/issues"}, "homepage": "https://github.com/ljharb/set-function-name#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/call-bind": "^1.0.5", "@types/define-properties": "^1.1.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/function.prototype.name": "^1.1.3", "@types/functions-have-names": "^1.2.2", "@types/has-property-descriptors": "^1.0.3", "@types/make-arrow-function": "^1.2.2", "@types/make-async-function": "^1.0.2", "@types/make-async-generator-function": "^1.0.3", "@types/make-generator-function": "^2.0.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "function.prototype.name": "^1.1.6", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test", "!*.d.ts", "!*.d.ts.map"]}}