/**
 * 方案5.1：时间函数测试脚本
 * 测试UTC时间处理和时区转换功能
 */

const dateUtils = require('../utils/dateUtils');
const timezoneUtils = require('../utils/timezoneUtils');
const moment = require('moment');

/**
 * 测试dateUtils.js中的UTC时间处理
 */
function testDateUtils() {
  console.log('🕐 测试 dateUtils.js UTC时间处理...\n');

  // 测试1：formatDateTime函数
  console.log('1. 测试 formatDateTime 函数：');
  const testDate = '2024-01-15 10:30:00'; // 假设这是UTC时间
  const formatted = dateUtils.formatDateTime(testDate);
  console.log(`  输入: ${testDate}`);
  console.log(`  输出: ${formatted}`);
  console.log(`  预期: UTC时间格式化，不进行本地转换\n`);

  // 测试2：getCurrentTime vs getCurrentUTCTime
  console.log('2. 测试时间获取函数：');
  const currentTime = dateUtils.getCurrentTime();
  const currentUTCTime = dateUtils.getCurrentUTCTime();
  console.log(`  getCurrentTime(): ${currentTime.toISOString()}`);
  console.log(`  getCurrentUTCTime(): ${currentUTCTime.toISOString()}`);
  console.log(`  时间差异: ${Math.abs(currentTime.getTime() - currentUTCTime.getTime())}ms\n`);

  // 测试3：今天开始/结束时间（UTC）
  console.log('3. 测试今天开始/结束时间（UTC）：');
  const todayStart = dateUtils.getTodayStart();
  const todayEnd = dateUtils.getTodayEnd();
  console.log(`  今天开始(UTC): ${todayStart.toISOString()}`);
  console.log(`  今天结束(UTC): ${todayEnd.toISOString()}`);
  
  // 验证是否是UTC时间的开始/结束
  const utcStart = moment.utc().startOf('day');
  const utcEnd = moment.utc().endOf('day');
  console.log(`  期望开始(UTC): ${utcStart.toISOString()}`);
  console.log(`  期望结束(UTC): ${utcEnd.toISOString()}`);
  console.log(`  开始时间匹配: ${Math.abs(todayStart.getTime() - utcStart.toDate().getTime()) < 1000}`);
  console.log(`  结束时间匹配: ${Math.abs(todayEnd.getTime() - utcEnd.toDate().getTime()) < 1000}\n`);

  return {
    formatDateTime: formatted,
    timeConsistency: Math.abs(currentTime.getTime() - currentUTCTime.getTime()) < 1000,
    utcDayBoundaries: {
      startMatch: Math.abs(todayStart.getTime() - utcStart.toDate().getTime()) < 1000,
      endMatch: Math.abs(todayEnd.getTime() - utcEnd.toDate().getTime()) < 1000
    }
  };
}

/**
 * 测试timezoneUtils.js中的时区转换
 */
async function testTimezoneUtils() {
  console.log('🌍 测试 timezoneUtils.js 时区转换...\n');

  // 测试1：getCurrentTime返回UTC时间
  console.log('1. 测试 getCurrentTime 返回UTC时间：');
  const currentTime = timezoneUtils.getCurrentTime();
  console.log(`  timezoneUtils.getCurrentTime(): ${currentTime.format('YYYY-MM-DD HH:mm:ss')} (${currentTime.utc().format('YYYY-MM-DD HH:mm:ss')} UTC)`);
  console.log(`  是否为UTC时间: ${currentTime.utc().format('YYYY-MM-DD HH:mm:ss') === currentTime.format('YYYY-MM-DD HH:mm:ss')}\n`);

  // 测试2：UTC时区的今天开始/结束
  console.log('2. 测试UTC时区的今天开始/结束：');
  const todayStart = timezoneUtils.getTodayStart();
  const todayEnd = timezoneUtils.getTodayEnd();
  console.log(`  今天开始: ${todayStart.format('YYYY-MM-DD HH:mm:ss')} UTC`);
  console.log(`  今天结束: ${todayEnd.format('YYYY-MM-DD HH:mm:ss')} UTC\n`);

  // 测试3：时区转换功能
  console.log('3. 测试时区转换功能：');
  const utcTime = '2024-01-15 10:30:00';
  console.log(`  UTC时间: ${utcTime}`);
  
  // 转换为不同时区
  const chinaTime = timezoneUtils.convertFromUTC(utcTime, '+08:00');
  const australiaTime = timezoneUtils.convertFromUTC(utcTime, '+10:00');
  const usTime = timezoneUtils.convertFromUTC(utcTime, '-05:00');
  
  console.log(`  中国时间(+08:00): ${chinaTime.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  澳洲时间(+10:00): ${australiaTime.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  美国时间(-05:00): ${usTime.format('YYYY-MM-DD HH:mm:ss')}\n`);

  // 测试4：系统时区获取
  console.log('4. 测试系统时区获取：');
  try {
    const systemTimezone = await timezoneUtils.getSystemTimezone();
    console.log(`  系统时区: ${systemTimezone}\n`);
    
    return {
      utcTimeCorrect: currentTime.utc().format('YYYY-MM-DD HH:mm:ss') === currentTime.format('YYYY-MM-DD HH:mm:ss'),
      timezoneConversion: {
        china: chinaTime.format('YYYY-MM-DD HH:mm:ss'),
        australia: australiaTime.format('YYYY-MM-DD HH:mm:ss'),
        us: usTime.format('YYYY-MM-DD HH:mm:ss')
      },
      systemTimezone: systemTimezone
    };
  } catch (error) {
    console.log(`  系统时区获取失败: ${error.message}\n`);
    return {
      utcTimeCorrect: false,
      error: error.message
    };
  }
}

/**
 * 测试收益时间计算逻辑
 */
function testProfitTimeCalculation() {
  console.log('💰 测试收益时间计算逻辑...\n');

  // 模拟投资数据
  const mockInvestment = {
    id: 1,
    start_time: '2024-01-15 02:00:00', // UTC时间
    last_profit_time: '2024-01-15 14:00:00', // UTC时间
    amount: 1000,
    profit_rate: 5
  };

  const mockProject = {
    id: 1,
    profit_time: 24 // 24小时周期
  };

  console.log('1. 模拟投资数据：');
  console.log(`  投资ID: ${mockInvestment.id}`);
  console.log(`  开始时间(UTC): ${mockInvestment.start_time}`);
  console.log(`  上次收益时间(UTC): ${mockInvestment.last_profit_time}`);
  console.log(`  收益周期: ${mockProject.profit_time}小时\n`);

  // 测试下一次收益时间计算
  console.log('2. 计算下一次收益时间：');
  const lastProfitTime = new Date(mockInvestment.last_profit_time);
  const nextProfitTime = new Date(lastProfitTime.getTime() + mockProject.profit_time * 60 * 60 * 1000);
  
  console.log(`  上次收益时间: ${lastProfitTime.toISOString()}`);
  console.log(`  下次收益时间: ${nextProfitTime.toISOString()}`);
  
  // 计算时间差
  const now = new Date();
  const timeDiff = nextProfitTime.getTime() - now.getTime();
  const hoursDiff = timeDiff / (1000 * 60 * 60);
  
  console.log(`  当前时间: ${now.toISOString()}`);
  console.log(`  距离下次收益: ${hoursDiff.toFixed(2)}小时`);
  console.log(`  是否应该发放: ${now >= nextProfitTime}\n`);

  // 测试跨时区一致性
  console.log('3. 测试跨时区一致性：');
  const chinaLocal = moment(nextProfitTime).utcOffset('+08:00');
  const australiaLocal = moment(nextProfitTime).utcOffset('+10:00');
  const usLocal = moment(nextProfitTime).utcOffset('-05:00');
  
  console.log(`  中国本地时间: ${chinaLocal.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  澳洲本地时间: ${australiaLocal.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  美国本地时间: ${usLocal.format('YYYY-MM-DD HH:mm:ss')}`);
  console.log(`  UTC时间戳一致: ${nextProfitTime.getTime()}\n`);

  return {
    nextProfitTime: nextProfitTime.toISOString(),
    hoursTillNext: hoursDiff,
    shouldDistribute: now >= nextProfitTime,
    timezoneConsistency: {
      china: chinaLocal.format('YYYY-MM-DD HH:mm:ss'),
      australia: australiaLocal.format('YYYY-MM-DD HH:mm:ss'),
      us: usLocal.format('YYYY-MM-DD HH:mm:ss'),
      utcTimestamp: nextProfitTime.getTime()
    }
  };
}

/**
 * 主测试函数
 */
async function runTimeTests() {
  console.log('🧪 方案5.1 时间函数测试');
  console.log('=====================================\n');

  const results = {};

  try {
    // 测试dateUtils
    results.dateUtils = testDateUtils();
    
    // 测试timezoneUtils
    results.timezoneUtils = await testTimezoneUtils();
    
    // 测试收益时间计算
    results.profitCalculation = testProfitTimeCalculation();

    // 输出测试总结
    console.log('📋 测试结果总结：');
    console.log(`dateUtils UTC处理: ${results.dateUtils.timeConsistency ? '✅ 通过' : '❌ 失败'}`);
    console.log(`timezoneUtils UTC时间: ${results.timezoneUtils.utcTimeCorrect ? '✅ 通过' : '❌ 失败'}`);
    console.log(`收益时间计算: ${results.profitCalculation.nextProfitTime ? '✅ 通过' : '❌ 失败'}`);
    
    if (results.timezoneUtils.systemTimezone) {
      console.log(`系统时区配置: ${results.timezoneUtils.systemTimezone}`);
    }

    console.log('\n🎉 时间函数测试完成！');
    
    return results;

  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    return { error: error.message };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTimeTests();
}

module.exports = {
  testDateUtils,
  testTimezoneUtils,
  testProfitTimeCalculation,
  runTimeTests
};
