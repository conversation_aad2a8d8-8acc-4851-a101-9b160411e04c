/**
 * 累计统计表模型
 */
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const TotalStatistic = sequelize.define('TotalStatistic', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  // 会员数据
  total_user_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '会员总数',
  },
  // 充值数据
  total_deposit_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总充值金额',
  },
  total_deposit_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总充值笔数',
  },
  total_deposit_user_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总充值人数',
  },
  // 取款数据
  total_withdrawal_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总取款金额',
  },
  total_withdrawal_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总取款笔数',
  },
  total_withdrawal_user_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总取款人数',
  },
  // 投资数据
  total_investment_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总投资金额',
  },
  total_investment_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总投资笔数',
  },
  total_investment_user_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总投资人数',
  },
  // 收益数据
  total_profit_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总收益金额',
  },
  total_profit_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总收益笔数',
  },
  // 佣金数据
  total_commission_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总佣金金额',
  },
  total_commission_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '总佣金笔数',
  },
  // 平台利润
  total_platform_profit: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总平台利润',
  },
  // 元数据
  last_updated_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '最后更新日期',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'total_statistics',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

module.exports = TotalStatistic;
