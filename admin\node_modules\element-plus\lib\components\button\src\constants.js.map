{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/button/src/constants.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\n\nimport type { ButtonProps } from './button'\n\nexport interface ButtonGroupContext {\n  size?: ButtonProps['size']\n  type?: ButtonProps['type']\n}\n\nexport const buttonGroupContextKey: InjectionKey<ButtonGroupContext> = Symbol(\n  'buttonGroupContextKey'\n)\n"], "names": [], "mappings": ";;;;AAAY,MAAC,qBAAqB,GAAG,MAAM,CAAC,uBAAuB;;;;"}