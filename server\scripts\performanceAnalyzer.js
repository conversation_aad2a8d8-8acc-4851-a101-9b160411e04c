/**
 * 性能分析脚本
 * 用于分析 Node.js 应用程序的性能
 */
const { PerformanceObserver, performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');

// 创建性能观察者
const obs = new PerformanceObserver((items) => {
  const entries = items.getEntries();
  const results = entries.map(entry => ({
    name: entry.name,
    duration: `${entry.duration.toFixed(2)}ms`,
    startTime: entry.startTime
  }));
  
  console.log('性能测量结果:');
  console.table(results);
  
  // 将结果保存到文件
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const dir = path.join(__dirname, '../logs/performance');
  
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  const filepath = path.join(dir, `performance-${timestamp}.json`);
  fs.writeFileSync(filepath, JSON.stringify(results, null, 2));
  console.log(`性能测量结果已保存到: ${filepath}`);
});

// 订阅所有性能条目
obs.observe({ entryTypes: ['measure'], buffered: true });

/**
 * 测量函数执行时间
 * @param {Function} fn - 要测量的函数
 * @param {string} name - 测量名称
 * @param {Array} args - 函数参数
 * @returns {any} - 函数返回值
 */
function measureFunctionExecution(fn, name, ...args) {
  const markStart = `${name}-start`;
  const markEnd = `${name}-end`;
  
  performance.mark(markStart);
  const result = fn(...args);
  performance.mark(markEnd);
  
  performance.measure(name, markStart, markEnd);
  
  return result;
}

/**
 * 测量异步函数执行时间
 * @param {Function} fn - 要测量的异步函数
 * @param {string} name - 测量名称
 * @param {Array} args - 函数参数
 * @returns {Promise<any>} - 函数返回值的 Promise
 */
async function measureAsyncFunctionExecution(fn, name, ...args) {
  const markStart = `${name}-start`;
  const markEnd = `${name}-end`;
  
  performance.mark(markStart);
  const result = await fn(...args);
  performance.mark(markEnd);
  
  performance.measure(name, markStart, markEnd);
  
  return result;
}

// 导出函数
module.exports = {
  measureFunctionExecution,
  measureAsyncFunctionExecution
};
