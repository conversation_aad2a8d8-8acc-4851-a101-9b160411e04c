const sequelize = require('../config/database');
const Admin = require('../models/Admin');
const bcrypt = require('bcryptjs');
const argon2 = require('argon2');

async function fixAdminPassword() {
  try {
    console.log('开始修复admin密码...');

    // 查找admin用户
    const admin = await Admin.findOne({ where: { username: 'admin' } });

    if (!admin) {
      console.log('未找到admin用户');
      return;
    }

    console.log('找到admin用户，当前密码:', admin.password);

    // 检查密码是否已经加密
    if (admin.password.startsWith('$argon2') || admin.password.startsWith('$2')) {
      console.log('密码已经是加密格式，无需修复');
      return;
    }

    // 如果是明文密码，手动加密
    const plainPassword = admin.password;
    let hashedPassword;

    try {
      // 使用Argon2加密密码
      hashedPassword = await argon2.hash(plainPassword, {
        type: argon2.argon2id,
        memoryCost: 2**16,
        timeCost: 3,
        parallelism: 1
      });
      console.log('使用Argon2加密成功');
    } catch (error) {
      // 如果Argon2失败，回退到bcrypt
      console.warn('使用Argon2加密失败，回退到bcrypt:', error);
      hashedPassword = await bcrypt.hash(plainPassword, 12);
      console.log('使用bcrypt加密成功');
    }

    // 直接更新数据库
    await admin.update({ password: hashedPassword });

    console.log('密码修复完成！新的加密密码:', hashedPassword);

    // 重新查询验证
    const updatedAdmin = await Admin.findOne({ where: { username: 'admin' } });
    const isValid = await updatedAdmin.validatePassword(plainPassword);
    console.log('密码验证结果:', isValid ? '成功' : '失败');

  } catch (error) {
    console.error('修复密码时出错:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行修复脚本
fixAdminPassword();
