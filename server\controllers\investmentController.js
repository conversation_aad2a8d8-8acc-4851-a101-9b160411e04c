const { Investment, User, Project, InvestmentProfit } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const transactionController = require('./transactionController');
const queueService = require('../services/queueService');
const profitScheduler = require('../services/profitScheduler');

// 管理员端 - 获取所有投资列表
exports.getAllInvestments = async (req, res) => {
  try {
    console.log('获取投资列表请求参数:', req.query);
    const { page = 1, limit = 10, status, keyword, investment_type, user_id, project_id } = req.query;

    // 构建查询条件
    const where = {};

    // 根据状态筛选
    if (status) {
      where.status = status;
      console.log('根据状态筛选:', status);
    }

    // 根据投资方式筛选
    if (investment_type) {
      where.investment_type = investment_type;
      console.log('根据投资方式筛选:', investment_type);
    }

    // 根据用户ID筛选
    if (user_id) {
      where.user_id = user_id;
      console.log('根据用户ID筛选:', user_id);
    }

    // 根据项目ID筛选
    if (project_id) {
      where.project_id = project_id;
      console.log('根据项目ID筛选:', project_id);
    }

    // 关键词搜索（用户名或项目名）
    if (keyword) {
      console.log('关键词搜索:', keyword);
      // 使用关联查询，搜索用户名或项目名
      where[Op.or] = [
        { '$user.username$': { [Op.like]: `%${keyword}%` } },
        { '$project.name$': { [Op.like]: `%${keyword}%` } }
      ];
    }

    console.log('查询条件:', where);

    // 分页查询
    const offset = (page - 1) * limit;
    console.log('分页参数:', { page, limit, offset });

    try {
      const { count, rows } = await Investment.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email', 'invite_code']
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'name', 'type', 'price', 'expected_return', 'duration', 'duration_unit']
          }
        ],
        order: [['created_at', 'DESC']],
        offset,
        limit: parseInt(limit)
      });

      console.log('查询结果数量:', count);
      console.log('查询结果第一条:', rows.length > 0 ? rows[0].id : '无数据');

      // 调试：检查第一条记录的last_profit_time字段
      if (rows.length > 0 && rows[0].last_profit_time) {
        console.log('第一条记录的last_profit_time原始值:', rows[0].last_profit_time);
        console.log('第一条记录的last_profit_time类型:', typeof rows[0].last_profit_time);
        // 如果需要ISO字符串，可以先将字符串转换为Date对象
        // const lastProfitDate = new Date(rows[0].last_profit_time);
        // console.log('第一条记录的last_profit_time ISO字符串:', lastProfitDate.toISOString());
      }

      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          items: rows
        }
      });
    } catch (error) {
      console.error('查询数据库错误:', error);
      throw error;
    }
  } catch (error) {
    console.error('获取投资列表错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取投资详情
exports.getInvestmentById = async (req, res) => {
  try {
    const { id } = req.params;

    const investment = await Investment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'user_id', 'username', 'name', 'phone', 'email', 'invite_code']
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'type', 'price', 'expected_return', 'duration', 'duration_unit']
        }
      ]
    });

    if (!investment) {
      return res.status(404).json({
        code: 404,
        message: '投资记录不存在',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: investment
    });
  } catch (error) {
    console.error('获取投资详情错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 获取投资收益记录
exports.getInvestmentProfits = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;

    // 检查投资记录是否存在
    const investment = await Investment.findByPk(id);
    if (!investment) {
      return res.status(404).json({
        code: 404,
        message: '投资记录不存在',
        data: null
      });
    }

    // 分页查询
    const offset = (page - 1) * limit;

    // 查询所有收益记录，按照收益时间排序
    const { count, rows } = await InvestmentProfit.findAndCountAll({
      where: { investment_id: id },
      attributes: ['id', 'investment_id', 'user_id', 'amount', 'profit_time', 'status', 'transaction_id', 'created_at', 'updated_at'],
      order: [
        ['profit_time', 'DESC'], // 按实际收益时间排序
        ['created_at', 'DESC'] // 其次按创建时间排序
      ],
      offset,
      limit: parseInt(limit)
    });

    // 记录日志，查看返回的数据
    console.log(`投资ID ${id} 的收益记录数量: ${count}`);
    if (rows.length > 0) {
      console.log(`第一条收益记录: ID=${rows[0].id}, 金额=${rows[0].amount}, 时间=${rows[0].profit_time}`);
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        items: rows
      }
    });
  } catch (error) {
    console.error('获取投资收益记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 删除投资记录
exports.deleteInvestment = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查投资记录是否存在
    const investment = await Investment.findByPk(id);
    if (!investment) {
      return res.status(404).json({
        code: 404,
        message: '投资记录不存在',
        data: null
      });
    }

    // 检查投资状态
    if (investment.status === 'completed') {
      return res.status(400).json({
        code: 400,
        message: '已完成的投资记录不能删除',
        data: null
      });
    }

    // 删除投资记录
    await investment.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除投资记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 批量删除投资记录
exports.batchDeleteInvestments = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的ID列表',
        data: null
      });
    }

    // 检查投资记录是否存在
    const investments = await Investment.findAll({
      where: { id: { [Op.in]: ids } }
    });

    if (investments.length !== ids.length) {
      return res.status(404).json({
        code: 404,
        message: '部分投资记录不存在',
        data: null
      });
    }

    // 检查是否有已完成的投资记录
    const completedInvestments = investments.filter(investment => investment.status === 'completed');
    if (completedInvestments.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '已完成的投资记录不能删除',
        data: null
      });
    }

    // 批量删除投资记录
    await Investment.destroy({
      where: { id: { [Op.in]: ids } }
    });

    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: null
    });
  } catch (error) {
    console.error('批量删除投资记录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};

// 管理员端 - 赠送投资
exports.giftInvestment = async (req, res) => {
  try {
    const { userId } = req.params;
    const { projectId } = req.body;

    // 验证请求数据
    if (!projectId) {
      return res.status(400).json({
        code: 400,
        message: '项目ID不能为空',
        data: null
      });
    }

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 查找项目
    const project = await Project.findByPk(projectId);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }

    // 开始事务
    const transaction = await sequelize.transaction();

    try {
      // 创建投资记录 - 使用项目的价格，而不是输入的金额
      const investment = await Investment.create({
        user_id: user.id,
        project_id: project.id,
        amount: parseFloat(project.price), // 使用项目的价格，而不是输入的金额
        quantity: 1,
        profit_rate: project.expected_return,
        profit_cycle: project.profit_time,
        status: 'active',
        start_time: new Date(), // 使用 Node.js 当前时间，Sequelize 配置确保时区正确
        is_gift: true, // 标记为赠送的投资
      }, { transaction });

      // 创建交易记录 - 使用investment_gift类型
      if (transactionController) {
        // 对于赠送投资，交易前余额和交易后余额相同，不影响用户余额
        const userBalance = user.balance;

        await transactionController.createTransaction(
          user.id,
          'investment_gift', // 使用investment_gift类型，表示赠送投资
          0, // 金额设为0，表示不影响用户余额
          userBalance, // 交易前余额
          userBalance, // 交易后余额（与交易前余额相同）
          `管理员赠送项目-${project.name}(价值:${parseFloat(project.price).toFixed(2)})`,
          investment.id,
          'investment',
          'success',
          'CNY',
          transaction
        );
      }

      // 将投资记录添加到新的收益系统
      try {
        // 直接使用已创建的投资记录和项目数据
        const investmentWithProject = {
          ...investment.toJSON(),
          project: project.toJSON()
        };

        // 添加到新的收益系统
        const scheduleResult = await profitScheduler.scheduleNextProfitTask(investmentWithProject, project);
        console.log('投资记录添加到收益系统结果:', scheduleResult);
      } catch (scheduleError) {
        console.error('将投资记录添加到收益系统失败:', scheduleError);
        // 不影响主流程，继续执行
      }

      // 提交事务
      await transaction.commit();

      // 触发统计数据更新（异步执行，不影响主流程）
      try {
        const statsUpdateService = require('../services/statsUpdateService');
        statsUpdateService.triggerFullStatsUpdate().catch(err => {
          console.error('赠送投资后更新统计数据失败:', err);
        });
      } catch (error) {
        console.error('触发统计数据更新失败:', error);
      }

      return res.status(200).json({
        code: 200,
        message: '赠送投资成功',
        data: investment
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('赠送投资错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      data: null
    });
  }
};