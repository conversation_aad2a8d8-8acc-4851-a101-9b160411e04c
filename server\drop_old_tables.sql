-- 删除旧表脚本
-- 作者：FOX开发团队
-- 日期：2025-06-04

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 删除带有_old后缀的表
DROP TABLE IF EXISTS attachments_old;
DROP TABLE IF EXISTS banners_old;
DROP TABLE IF EXISTS commissions_old;
DROP TABLE IF EXISTS deposits_old;
DROP TABLE IF EXISTS investment_profits_old;
DROP TABLE IF EXISTS investments_old;
DROP TABLE IF EXISTS projects_old;
DROP TABLE IF EXISTS system_params_old;
DROP TABLE IF EXISTS transactions_old;
DROP TABLE IF EXISTS user_levels_old;
DROP TABLE IF EXISTS users_old;
DROP TABLE IF EXISTS withdrawals_old;

-- 删除带有_backup后缀的表
DROP TABLE IF EXISTS admin_roles_backup;
DROP TABLE IF EXISTS admins_backup;
DROP TABLE IF EXISTS attachments_backup;
DROP TABLE IF EXISTS commissions_backup;
DROP TABLE IF EXISTS deposits_backup;
DROP TABLE IF EXISTS investment_profits_backup;
DROP TABLE IF EXISTS investments_backup;
DROP TABLE IF EXISTS permissions_backup;
DROP TABLE IF EXISTS projects_backup;
DROP TABLE IF EXISTS receiving_bank_cards_backup;
DROP TABLE IF EXISTS role_permissions_backup;
DROP TABLE IF EXISTS roles_backup;
DROP TABLE IF EXISTS system_params_backup;
DROP TABLE IF EXISTS transactions_backup;
DROP TABLE IF EXISTS user_bank_cards_backup;
DROP TABLE IF EXISTS user_levels_backup;
DROP TABLE IF EXISTS users_backup;
DROP TABLE IF EXISTS withdrawals_backup;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
