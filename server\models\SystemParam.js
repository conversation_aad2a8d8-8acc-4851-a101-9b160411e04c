const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const SystemParam = sequelize.define('SystemParam', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  param_key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '参数键',
  },
  param_value: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '参数值',
  },
  param_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'text',
    comment: '参数类型',
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '参数描述',
  },
  group_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'basic',
    comment: '参数分组',
  },
  sort_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'system_params',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

module.exports = SystemParam;
