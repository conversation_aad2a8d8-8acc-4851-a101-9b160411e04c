/**
 * 全局URL配置和处理工具
 * 集中管理所有与URL相关的配置和处理逻辑
 */

// 服务器IP配置
const SERVER_IP = '***********';

/**
 * 获取适当的API基础URL
 * 根据当前环境动态确定基础URL
 * @returns {string} API基础URL
 */
export const getApiBaseUrl = () => {
  // 首先检查环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL;
  }

  // 如果是本地开发环境
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // 本地开发环境使用localhost:3000
    return 'http://localhost:3000';
  }

  // 生产环境使用当前域名
  return window.location.origin;
};

/**
 * 获取适当的静态资源基础URL
 * @returns {string} 静态资源基础URL
 */
export const getStaticBaseUrl = () => {
  // 如果是本地开发环境
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // 本地开发环境使用localhost:3000
    return 'http://localhost:3000';
  }

  // 生产环境使用当前域名
  return window.location.origin;
};

/**
 * 获取服务器IP
 * @returns {string} 服务器IP
 */
export const getServerIp = () => {
  return SERVER_IP;
};

/**
 * 获取完整的图片URL
 * @param {string|null|undefined} url - 图片路径
 * @param {string} [defaultImage=''] - 默认图片路径
 * @returns {string} 完整的图片URL
 */
export const getFullImageUrl = (url, defaultImage = '') => {
  if (!url) return defaultImage;

  // 如果已经是完整URL（以http://或https://开头），则直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // 使用静态资源基础URL
  const baseUrl = getStaticBaseUrl();

  // 如果是相对路径（以/开头），则添加基础URL
  if (url.startsWith('/')) {
    return `${baseUrl}${url}`;
  }

  // 其他情况，可能是相对路径但没有/开头，添加/
  return `${baseUrl}/${url}`;
};

/**
 * 检查图片是否存在
 * @param {string} url - 图片URL
 * @returns {Promise<boolean>} 图片是否存在
 */
export const checkImageExists = (url) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

/**
 * 尝试不同的URL格式并返回第一个可用的
 * @param {string} originalUrl - 原始URL
 * @param {string} [position] - 可选的position字段
 * @returns {Promise<string>} 可用的URL
 */
export const findWorkingImageUrl = async (originalUrl, position = '') => {
  if (!originalUrl) return '';

  // 使用当前域名和服务器IP
  const baseUrl = getStaticBaseUrl();
  const serverIp = getServerIp();

  // 尝试不同的URL组合
  const urls = [
    originalUrl, // 原始URL
    `${baseUrl}${originalUrl.startsWith('/') ? '' : '/'}${originalUrl}`, // 当前域名 + 原始URL
    `${baseUrl}/${originalUrl.replace(/^\//, '')}`, // 当前域名 + 去掉开头斜杠的原始URL
    `http://${serverIp}${originalUrl.startsWith('/') ? '' : '/'}${originalUrl}`, // 服务器IP + 原始URL
    `http://${serverIp}/${originalUrl.replace(/^\//, '')}` // 服务器IP + 去掉开头斜杠的原始URL
  ];

  // 如果提供了position字段，也尝试使用它
  if (position) {
    urls.push(
      `${baseUrl}${position.startsWith('/') ? '' : '/'}${position}`, // 当前域名 + position
      `${baseUrl}/${position.replace(/^\//, '')}` // 当前域名 + 去掉开头斜杠的position
    );
  }

  // 如果URL包含文件名，尝试使用uploads路径
  const fileName = originalUrl.split('/').pop();
  if (fileName) {
    urls.push(`${baseUrl}/uploads/${fileName}`); // 当前域名 + uploads + 文件名
    urls.push(`http://${serverIp}/uploads/${fileName}`); // 服务器IP + uploads + 文件名
  }

  for (const url of urls) {
    const exists = await checkImageExists(url);
    if (exists) {
      return url;
    }
  }

  // 如果所有URL都不可用，返回原始URL
  return originalUrl;
};

export default {
  getApiBaseUrl,
  getStaticBaseUrl,
  getServerIp,
  getFullImageUrl,
  checkImageExists,
  findWorkingImageUrl
};
