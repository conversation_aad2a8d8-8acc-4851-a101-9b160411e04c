/**
 * 统计数据初始化脚本
 * 用于初始化历史统计数据
 */
const moment = require('moment');
const statisticsService = require('../services/statisticsService');
const logger = require('../utils/logger');

async function main() {
  try {
    // 默认初始化最近30天的数据
    const endDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
    const startDate = moment().subtract(30, 'days').format('YYYY-MM-DD');
    
    // 从命令行参数获取日期范围
    const args = process.argv.slice(2);
    let customStartDate = args[0];
    let customEndDate = args[1];
    
    // 如果提供了自定义日期范围，使用自定义日期范围
    if (customStartDate) {
      if (!moment(customStartDate, 'YYYY-MM-DD', true).isValid()) {
        logger.error(`无效的开始日期格式: ${customStartDate}，应为 YYYY-MM-DD`);
        process.exit(1);
      }
    } else {
      customStartDate = startDate;
    }
    
    if (customEndDate) {
      if (!moment(customEndDate, 'YYYY-MM-DD', true).isValid()) {
        logger.error(`无效的结束日期格式: ${customEndDate}，应为 YYYY-MM-DD`);
        process.exit(1);
      }
    } else {
      customEndDate = endDate;
    }
    
    logger.info(`开始初始化 ${customStartDate} 到 ${customEndDate} 的统计数据`);
    
    // 初始化历史统计数据
    const result = await statisticsService.initializeHistoricalStatistics(customStartDate, customEndDate);
    
    if (result.success) {
      logger.info(`历史统计数据初始化成功: ${result.message}`);
    } else {
      logger.error(`历史统计数据初始化失败: ${result.message}`);
    }
    
    // 退出进程
    process.exit(0);
  } catch (error) {
    logger.error(`统计数据初始化失败: ${error.message}`, error);
    process.exit(1);
  }
}

// 执行主函数
main();
