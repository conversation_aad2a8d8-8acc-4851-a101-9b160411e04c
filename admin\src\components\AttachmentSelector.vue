<template>
  <el-dialog
    v-model="dialogVisible"
    :title="props.fileType === 'image' ? '选择图片' : props.fileType === 'video' ? '选择视频' : '选择附件'"
    width="900px"
    center
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div class="attachment-selector">
      <!-- 搜索和筛选区域 -->
      <div class="selector-header" v-if="!showDirectUrlInput">
        <el-input
          v-model="searchInput"
          placeholder="搜索文件名"
          clearable
          @keyup.enter="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>

      <!-- 直接输入URL区域 - 仅在获取附件列表失败时显示 -->
      <div class="direct-url-input" v-if="showDirectUrlInput">
        <el-alert
          :title="props.fileType === 'image' ? '输入图片URL' : props.fileType === 'video' ? '输入视频URL' : '输入文件URL'"
          type="warning"
          :description="props.fileType === 'image' ? '无法获取附件列表，请直接输入图片URL地址，支持jpg、png、gif等格式' : props.fileType === 'video' ? '无法获取附件列表，请直接输入视频URL地址，支持mp4、webm等格式' : '无法获取附件列表，请直接输入文件URL地址'"
          show-icon
          :closable="false"
          style="margin-bottom: 15px;"
        />
        <el-input
          v-model="directUrl"
          :placeholder="props.fileType === 'image' ? '请输入图片URL地址' : props.fileType === 'video' ? '请输入视频URL地址' : '请输入文件URL地址'"
          clearable
        >
          <template #append>
            <el-button @click="confirmDirectUrl" type="primary" :disabled="!isValidUrl(directUrl)">确认</el-button>
          </template>
        </el-input>

        <!-- 预览区域 -->
        <div class="preview-container" v-if="directUrl && isValidUrl(directUrl)">
          <div class="preview-title">预览</div>
          <div class="preview-content">
            <el-image
              v-if="props.fileType === 'image' && isImage(getFileExtension(directUrl))"
              :src="directUrl"
              fit="contain"
              class="url-preview-image"
              :preview-src-list="[directUrl]"
              preview-teleported
              hide-on-click-modal
            />
            <video
              v-else-if="props.fileType === 'video' && isVideo(getFileExtension(directUrl))"
              :src="directUrl"
              controls
              class="url-preview-video"
            ></video>
            <div v-else class="url-preview-file">
              <el-icon :size="48"><Document /></el-icon>
              <div class="file-name">{{ getFileName(directUrl) }}</div>
            </div>
          </div>
        </div>

        <!-- 上传区域 -->
        <div class="upload-container">
          <div class="upload-title">或者上传新文件</div>
          <el-upload
            class="attachment-uploader"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :accept="getAcceptTypes()"
            :limit="1"
            :file-list="fileList"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              {{ props.fileType === 'image' ? '选择图片' : props.fileType === 'video' ? '选择视频' : '选择文件' }}
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                {{
                  props.fileType === 'image' ? '支持jpg、png、gif等格式的图片文件' :
                  props.fileType === 'video' ? '支持mp4、webm等格式的视频文件' :
                  '支持各种类型的文件'
                }}
              </div>
            </template>
          </el-upload>
        </div>
      </div>

      <!-- 附件列表 - 始终显示，除非显示URL输入框 -->
      <div class="attachment-grid" v-if="!showDirectUrlInput" v-loading="loading">
        <div
          v-for="item in attachments"
          :key="item.id"
          class="attachment-item"
          :class="{ 'selected': selectedAttachment && selectedAttachment.id === item.id }"
          @click="selectAttachment(item)"
        >
          <div class="attachment-preview">
            <el-image
              v-if="isImage(item.fileType)"
              :src="item.url"
              fit="cover"
              class="preview-image"
              :preview-src-list="[item.url]"
              preview-teleported
              hide-on-click-modal
              @error="handleImageError"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>图片加载失败</span>
                </div>
              </template>
            </el-image>
            <el-icon v-else :size="40" class="file-icon">
              <VideoPlay v-if="isVideo(item.fileType)" />
              <Document v-else-if="item.fileType === 'pdf'" />
              <Files v-else-if="item.fileType === 'doc' || item.fileType === 'docx'" />
              <Briefcase v-else-if="item.fileType === 'zip' || item.fileType === 'rar'" />
              <DocumentCopy v-else />
            </el-icon>
          </div>
          <div class="attachment-info">
            <div class="attachment-name">{{ item.filename }}</div>
            <div class="attachment-meta">
              <span>{{ formatFileSize(item.fileSize) }}</span>
              <span>{{ item.fileType }}</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="attachments.length === 0" description="暂无附件" />
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="!showDirectUrlInput && attachments.length > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 36, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer attachment-dialog-footer">
        <el-button
          type="primary"
          @click="showDirectUrlInput ? confirmDirectUrl() : confirmSelection()"
          :disabled="showDirectUrlInput ? (!isValidUrl(directUrl) && fileList.length === 0) : !selectedAttachment"
        >
          确定
        </el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Document, Files, Briefcase, DocumentCopy, VideoPlay, Picture, Upload } from '@element-plus/icons-vue'
import { getAttachmentList } from '@/api/attachments'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  fileType: {
    type: String,
    default: '' // 可以是 'image', 'video', 'document' 或空字符串（表示所有类型）
  }
})

const emit = defineEmits(['update:visible', 'select', 'close'])

// 状态变量
const dialogVisible = ref(false)
const loading = ref(false)
const searchInput = ref('')
const fileTypeFilter = ref('')
const attachments = ref<any[]>([])
const selectedAttachment = ref<any>(null)
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 直接URL输入相关
const showDirectUrlInput = ref(false) // 默认不显示URL输入框，只有在获取附件列表失败时才显示
const directUrl = ref('')

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 重置状态
    showDirectUrlInput.value = false
    directUrl.value = ''
    selectedAttachment.value = null

    // 设置默认文件类型筛选
    fileTypeFilter.value = props.fileType

    // 加载附件数据
    fetchAttachments()

    console.log('对话框打开，showDirectUrlInput =', showDirectUrlInput.value)
  }
})

// 监听对话框可见性变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    emit('close')
  }
})

// 获取附件列表
const fetchAttachments = async () => {
  loading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加搜索条件
    if (searchInput.value) {
      params.filename = searchInput.value
    }

    // 添加文件类型筛选
    // 如果是从项目图片选择，强制只显示图片
    if (props.fileType === 'image') {
      // 使用通用的图片类型标识符
      params.fileType = 'image';
    } else if (props.fileType === 'video') {
      // 使用通用的视频类型标识符
      params.fileType = 'video';
    } else if (props.fileType === 'document') {
      // 使用通用的文档类型标识符
      params.fileType = 'document';
    } else if (fileTypeFilter.value) {
      // 其他情况下使用用户选择的筛选条件
      params.fileType = fileTypeFilter.value;
    }

    const response = await getAttachmentList(params)
    console.log('完整响应对象:', response);

    // 注意：response已经是处理过的数据，不需要再访问response.data
    if (response && response.list) {
      let attachmentList = response.list || [];

      // 添加调试日志
      console.log('原始附件列表:', JSON.stringify(attachmentList));

      // 如果是从项目图片选择，只显示图片类型的附件
      if (props.fileType === 'image') {
        // 记录每个附件的fileType和isImage结果
        attachmentList.forEach(item => {
          console.log(`附件 ${item.id} (${item.filename}): fileType=${item.fileType}, isImage=${isImage(item.fileType)}`);
        });

        // 过滤图片类型
        const filteredList = attachmentList.filter(item => isImage(item.fileType));

        // 如果过滤后列表为空但原始列表不为空，尝试使用所有附件
        if (filteredList.length === 0 && attachmentList.length > 0) {
          console.warn('没有找到符合条件的图片附件，使用所有附件');
          // 不过滤，使用所有附件
        } else {
          attachmentList = filteredList;
        }
      }

      // 更新附件列表和总数
      attachments.value = attachmentList;
      total.value = props.fileType === 'image'
        ? attachmentList.length
        : (response.total || 0);

      // 如果附件列表为空但原始列表不为空，显示警告
      if (attachmentList.length === 0 && response.list && response.list.length > 0) {
        ElMessage.warning('没有找到符合条件的图片附件');
        // 显示直接URL输入选项
        showDirectUrlInput.value = true;
      }
    } else {
      attachments.value = []
      total.value = 0
      ElMessage.error('获取附件列表失败: 数据格式不正确')
    }
  } catch (error) {
    // 提供更详细的错误信息
    if (error.response && error.response.status) {

      // 如果是400错误，可能是参数问题，尝试不带参数重试
      if (error.response.status === 400) {
        try {
          console.log('尝试不带参数重新获取附件列表');
          const retryResponse = await getAttachmentList({ page: 1, pageSize: 12 });
          if (retryResponse && retryResponse.list) {
            let attachmentList = retryResponse.list || [];

            // 如果是从项目图片选择，只显示图片类型的附件
            if (props.fileType === 'image') {
              attachmentList = attachmentList.filter(item => isImage(item.fileType));
            } else if (props.fileType === 'video') {
              attachmentList = attachmentList.filter(item => isVideo(item.fileType));
            }

            attachments.value = attachmentList;
            total.value = retryResponse.total || 0;

            ElMessage.warning('已重置筛选条件并获取所有附件');
            return;
          }
        } catch (retryError) {
          console.error('重试获取附件列表失败', retryError);
        }
      }

      ElMessage.error(`获取附件列表失败: 服务器返回 ${error.response.status} 错误`);
    } else if (error.message) {
      ElMessage.error(`获取附件列表失败: ${error.message}`);
    } else {
      ElMessage.error('获取附件列表失败: 未知错误');
    }

    // 尝试加载一些默认数据或使用本地缓存
    attachments.value = [];
    total.value = 0;

    // 显示直接URL输入选项
    showDirectUrlInput.value = true;

    // 如果是筛选导致的错误，清除筛选条件
    if (fileTypeFilter.value) {
      ElMessage.warning('已清除筛选条件，请重试');
      fileTypeFilter.value = '';
      // 不立即重试，让用户决定是否重试
    }
  } finally {
    loading.value = false;
  }
}

// 选择附件
const selectAttachment = (attachment: any) => {
  selectedAttachment.value = attachment
}

// 确认选择
const confirmSelection = () => {
  if (selectedAttachment.value) {
    emit('select', selectedAttachment.value)
    dialogVisible.value = false
  } else {
    ElMessage.warning('请选择一个附件')
  }
}

// 处理对话框关闭
const handleClosed = () => {
  // 重置状态
  selectedAttachment.value = null
  searchInput.value = ''
  directUrl.value = ''
  showDirectUrlInput.value = false

  // 重置其他状态
  fileTypeFilter.value = ''
  currentPage.value = 1
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchAttachments()
}

// 验证URL是否有效
const isValidUrl = (url: string): boolean => {
  if (!url) return false

  try {
    // 简单验证URL格式
    const urlObj = new URL(url)

    // 获取文件扩展名
    const ext = urlObj.pathname.split('.').pop()?.toLowerCase() || '';

    // 检查是否是图片URL（如果是图片选择器）
    if (props.fileType === 'image') {
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
      return imageTypes.includes(ext);
    }
    // 检查是否是视频URL（如果是视频选择器）
    else if (props.fileType === 'video') {
      const videoTypes = ['mp4', 'webm', 'avi', 'mov', 'flv'];
      return videoTypes.includes(ext);
    }
    // 检查是否是文档URL
    else if (props.fileType === 'document') {
      const docTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
      return docTypes.includes(ext);
    }

    // 如果没有指定类型，则接受任何URL
    return true;
  } catch (e) {
    return false
  }
}

// 确认直接输入的URL
const confirmDirectUrl = () => {
  if (!isValidUrl(directUrl.value)) {
    ElMessage.error('请输入有效的URL地址')
    return
  }

  // 创建一个模拟的附件对象
  const attachment = {
    id: -1, // 使用负数ID表示这是手动输入的URL
    url: directUrl.value,
    filename: directUrl.value.split('/').pop() || '自定义URL',
    fileType: directUrl.value.split('.').pop()?.toLowerCase() || '',
    fileSize: 0,
    width: 0,
    height: 0,
    uploadTime: new Date().toISOString(),
    isCustomUrl: true
  }

  emit('select', attachment)
  dialogVisible.value = false
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchAttachments()
}

// 处理每页数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchAttachments()
}

// 判断是否为图片
const isImage = (fileType: string): boolean => {
  if (!fileType) return false

  // 转换为小写
  const type = fileType.toLowerCase()

  // 常见图片类型列表
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'tif', 'ico', 'jfif', 'pjpeg', 'pjp']

  // 直接检查是否在列表中
  if (imageTypes.includes(type)) {
    return true
  }

  // 检查是否以图片类型开头（处理可能的变体，如jpeg-xxx）
  for (const imgType of imageTypes) {
    if (type.startsWith(imgType)) {
      return true
    }
  }

  // 检查MIME类型前缀
  if (type.startsWith('image/')) {
    return true
  }

  return false
}

// 判断是否为视频
const isVideo = (fileType: string): boolean => {
  if (!fileType) return false
  const videoTypes = ['mp4', 'webm', 'avi', 'mov', 'flv']
  return videoTypes.includes(fileType.toLowerCase())
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
  }
}

// 获取文件扩展名
const getFileExtension = (url: string): string => {
  try {
    return url.split('.').pop()?.toLowerCase() || '';
  } catch (e) {
    return '';
  }
}

// 获取文件名
const getFileName = (url: string): string => {
  try {
    return url.split('/').pop() || '未知文件';
  } catch (e) {
    return '未知文件';
  }
}

// 获取接受的文件类型
const getAcceptTypes = (): string => {
  if (props.fileType === 'image') {
    return '.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg,image/*';
  } else if (props.fileType === 'video') {
    return '.mp4,.webm,.avi,.mov,.flv,video/*';
  } else if (props.fileType === 'document') {
    return '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt';
  }
  return '*/*';
}

// 文件上传相关
const fileList = ref<any[]>([]);

// 处理文件变更
const handleFileChange = (file: any) => {
  fileList.value = [file];

  // 如果是图片，可以预览
  if (file.raw && isImage(file.raw.name.split('.').pop())) {
    directUrl.value = URL.createObjectURL(file.raw);
  } else if (file.raw && isVideo(file.raw.name.split('.').pop())) {
    directUrl.value = URL.createObjectURL(file.raw);
  } else {
    directUrl.value = '';
  }
}

// 处理图片加载错误
const handleImageError = (e: Error) => {
  console.error('图片加载失败:', e);
  // 可以在这里添加更多的错误处理逻辑
}
</script>

<style scoped>
.attachment-selector {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.selector-header {
  display: flex;
  margin-bottom: 16px;
  gap: 10px;
}

.search-input {
  flex: 1;
}

.type-filter {
  width: 150px;
}

.attachment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  overflow-y: auto;
  flex: 1;
  padding: 8px;
}

.attachment-item {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  height: 180px;
  display: flex;
  flex-direction: column;
}

.attachment-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.attachment-item.selected {
  border: 2px solid #409EFF;
  box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
}

.attachment-preview {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-icon {
  font-size: 40px;
  color: #909399;
}

.attachment-info {
  padding: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.attachment-name {
  font-size: 13px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.direct-url-input {
  margin: 10px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.preview-container {
  margin: 15px 0;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.preview-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.url-preview-image {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
}

.url-preview-video {
  max-width: 100%;
  max-height: 300px;
}

.url-preview-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.file-name {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
  word-break: break-all;
  max-width: 300px;
  text-align: center;
}

.upload-container {
  margin: 15px 0 5px;
}

.upload-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.image-error .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 附件选择器对话框底部按钮居中 */
.attachment-dialog-footer {
  display: flex;
  justify-content: center !important;
  width: 100%;
}
</style>
