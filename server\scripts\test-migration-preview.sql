-- =====================================================
-- 方案5.1：迁移预览测试脚本（只读，不修改数据）
-- =====================================================
-- 
-- 此脚本只查看数据，不进行任何修改
-- 用于验证迁移前的数据状态和迁移后的预期结果
-- 
-- =====================================================

-- 显示当前数据库时区设置
SELECT 
    '当前数据库时区设置' as info,
    @@session.time_zone as session_timezone,
    @@system_time_zone as system_timezone,
    @@global.time_zone as global_timezone;

-- 显示当前时间对比
SELECT 
    '当前时间对比' as info,
    NOW() as db_current_time,
    UTC_TIMESTAMP() as utc_time,
    TIMESTAMPDIFF(HOUR, UTC_TIMESTAMP(), NOW()) as timezone_offset_hours;

-- 检查系统参数中的时区设置
SELECT 
    '系统时区参数' as info,
    param_key, 
    param_value, 
    created_at, 
    updated_at
FROM system_params 
WHERE param_key = '[site.timezone]'
LIMIT 1;

-- 检查投资表的时间数据样本
SELECT 
    '投资表时间样本' as info,
    id,
    start_time,
    last_profit_time,
    created_at,
    updated_at
FROM investments 
ORDER BY created_at DESC 
LIMIT 5;

-- 检查收益表的时间数据样本
SELECT 
    '收益表时间样本' as info,
    id,
    investment_id,
    profit_time,
    created_at,
    updated_at
FROM investment_profits 
ORDER BY created_at DESC 
LIMIT 5;

-- 检查用户表的时间数据样本
SELECT 
    '用户表时间样本' as info,
    id,
    username,
    created_at,
    updated_at,
    last_login_time
FROM users 
ORDER BY created_at DESC 
LIMIT 5;

-- 预览UTC转换后的结果（不修改数据）
SELECT 
    'UTC转换预览 - 投资表' as info,
    id,
    start_time as original_start_time,
    CONVERT_TZ(start_time, '+08:00', '+00:00') as utc_start_time,
    last_profit_time as original_last_profit,
    CASE 
        WHEN last_profit_time IS NOT NULL 
        THEN CONVERT_TZ(last_profit_time, '+08:00', '+00:00')
        ELSE NULL 
    END as utc_last_profit
FROM investments 
ORDER BY created_at DESC 
LIMIT 3;

-- 预览UTC转换后的结果（不修改数据）
SELECT 
    'UTC转换预览 - 收益表' as info,
    id,
    investment_id,
    profit_time as original_profit_time,
    CONVERT_TZ(profit_time, '+08:00', '+00:00') as utc_profit_time
FROM investment_profits 
ORDER BY created_at DESC 
LIMIT 3;

-- 统计各表的记录数量
SELECT 
    '数据统计' as info,
    'investments' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM investments
UNION ALL
SELECT 
    '数据统计' as info,
    'investment_profits' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM investment_profits
UNION ALL
SELECT 
    '数据统计' as info,
    'users' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM users;

-- 检查时间字段的数据类型
SELECT 
    '时间字段数据类型' as info,
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'fox_db' 
AND DATA_TYPE IN ('datetime', 'timestamp', 'date', 'time')
ORDER BY TABLE_NAME, COLUMN_NAME;
