'use strict';

module.exports.Messages = {
  RETRY_JOB_NOT_EXIST: "Couldn't retry job: The job doesn't exist",
  RETRY_JOB_IS_LOCKED: "Couldn't retry job: The job is locked",
  RETRY_JOB_NOT_FAILED:
    "Couldn't retry job: The job has been already retried or has not failed",
  MISSING_REDIS_OPTS: `Using a redis instance with enableReadyCheck or maxRetriesPerRequest for bclient/subscriber is not permitted.
  see https://github.com/OptimalBits/bull/issues/1873
  `
};
