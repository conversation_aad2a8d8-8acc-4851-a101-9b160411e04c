/**
 * 客服管理模型
 */
const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CustomerService = sequelize.define('CustomerService', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '客服类型，如WhatsApp、Telegram等',
  },
  title: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '客服标题',
  },
  url: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '客服链接',
  },
  status: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '状态：true=启用，false=禁用',
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '图标',
  },
  weight: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '权重，用于排序',
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '描述',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '创建时间',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '更新时间',
  },
}, {
  tableName: 'customer_services',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

module.exports = CustomerService;
