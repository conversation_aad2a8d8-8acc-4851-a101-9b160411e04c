 {
    <%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page import="java.io.*"%>
<%@ page import="cn.test.utils.*"%>
<%@ page import="java.util.*"%>

<%
    request.setCharacterEncoding("UTF-8");
    String mch_transferId = request.getParameter("mch_transferId");
    String mch_id = request.getParameter("mch_id");
    String sign_type = request.getParameter("sign_type");

    StringBuffer signSrc = new StringBuffer();
    signSrc.append("mch_id=").append(mch_id).append("&");
    signSrc.append("mch_transferId=").append(mch_transferId);

    Map<String, String> reqMap = new HashMap<String, String>();
    reqMap.put("sign_type", sign_type);
    reqMap.put("mch_id", mch_id);
    reqMap.put("mch_transferId", mch_transferId);

    String signInfo = signSrc.toString();

    String result = null;

    String reqUrl = "";
    String merchant_key = "";

    String sign = SignAPI.sign(signInfo, merchant_key);

    reqMap.put("sign", sign);

    System.out.println("reqMap：" + reqMap.toString().length() + " --> " + reqMap.toString());
    System.out.println("签名参数排序：" + signInfo.length() + " --> " + signInfo);
    System.out.println("sign值：" + sign.length() + " --> " + sign);

    result = HttpClientUtil.doPost(reqUrl, reqMap, "utf-8");
    System.out.println("result值：" + result);

    PrintWriter pw = response.getWriter();
    pw.write(result);
    pw.flush();
    pw.close();
%>

    }
  }