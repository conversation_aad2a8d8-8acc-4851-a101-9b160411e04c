const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const InvestmentProfit = sequelize.define('InvestmentProfit', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  investment_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '投资ID',
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: '收益金额',
  },
  profit_time: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: '收益时间（用户应该获得收益的理论时间）',
  },
  distribution_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '发放时间（系统实际执行发放操作的时间）',
  },
  theoretical_profit_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '理论收益时间（已废弃，使用profit_time代替）',
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态：pending=待发放, paid=已发放',
  },
  transaction_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '交易ID',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'investment_profits',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
});

// 定义关联关系
InvestmentProfit.associate = (models) => {
  // 收益与投资
  InvestmentProfit.belongsTo(models.Investment, {
    foreignKey: 'investment_id',
    as: 'investment',
  });

  // 收益与用户
  InvestmentProfit.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user',
  });

  // 收益与交易
  InvestmentProfit.belongsTo(models.Transaction, {
    foreignKey: 'transaction_id',
    as: 'transaction',
  });
};

module.exports = InvestmentProfit;
