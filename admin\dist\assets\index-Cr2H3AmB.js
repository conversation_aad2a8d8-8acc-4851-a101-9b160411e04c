/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                *//* empty css                        *//* empty css                    */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  */import{d as ye,r as c,a as W,q as ke,o as Le,c as A,b as i,e as l,w as a,m as he,f as xe,i as Ue,aa as Te,ab as De,ac as Ee,n as u,x as qe,j as g,ad as Se,ae as G,aB as H,af as ze,ag as Ae,ai as Oe,aj as Me,ak as Re,V as Ie,al as Ye,aI as Fe,y as x,ao as $e,ap as je,aq as Be,a8 as Pe,a9 as Ke,at as Qe,E as We,h as Ge,av as He,aC as Je,aA as Xe,p as V,a0 as Ze,g as U,_ as el}from"./index-LncY9lAB.js";const ll={class:"bank-cards-container"},tl={class:"toolbar"},al={class:"toolbar-left"},ol={class:"toolbar-right"},nl={class:"table-wrapper"},il={class:"operation-buttons-container"},dl={class:"pagination-container"},sl={class:"dialog-footer"},ul={class:"dialog-footer"},rl={class:"qrcode-container"},ml=["src"],pl={key:1,class:"no-qrcode"},cl={class:"filter-container"},fl={class:"filter-group"},vl={class:"filter-row"},gl={class:"filter-row"},bl={class:"filter-group"},_l={class:"filter-row"},Vl={class:"range-input"},Cl={class:"filter-row"},wl={class:"filter-group"},Nl={class:"filter-row"},yl={class:"dialog-footer"},kl=ye({__name:"index",setup(Ll){const T=c(""),F=c(""),w=c(1),D=c(10),O=c(5),M=c(!1),b=c([]),$=c([]),n=W({id:0,bankName:"",recipientName:"",accountNumber:"",qrCode:"",totalLimit:0,remainingLimit:0,receivedAmount:0,currency:"CNY",sortOrder:1,status:1}),C=c(!1),N=c(!1),R=c(!1),E=c(!1),k=c(!1),L=c(null),I=c(""),d=W({id:"",bankName:"",recipientName:"",accountNumber:"",totalLimitMin:void 0,totalLimitMax:void 0,currency:"",status:"",createTimeRange:[],updateTimeRange:[]}),p=c([{id:8,bankName:"农业银行",recipientName:"王小明",accountNumber:"6217 0000 0000 0000",qrCode:"https://example.com/qrcode1.png",totalLimit:5e5,remainingLimit:289e3,receivedAmount:211e3,currency:"CNY",sortOrder:1,status:1,createTime:"2025-04-01 08:30:00",updateTime:"2025-04-05 15:23:10"},{id:7,bankName:"建设银行",recipientName:"李小红",accountNumber:"6227 0000 0000 0000",qrCode:"https://example.com/qrcode2.png",totalLimit:3e5,remainingLimit:156e3,receivedAmount:144e3,currency:"CNY",sortOrder:2,status:1,createTime:"2025-04-01 10:15:20",updateTime:"2025-04-04 12:45:30"},{id:6,bankName:"工商银行",recipientName:"张小华",accountNumber:"6212 0000 0000 0000",qrCode:"https://example.com/qrcode3.png",totalLimit:4e5,remainingLimit:183e3,receivedAmount:217e3,currency:"CNY",sortOrder:3,status:1,createTime:"2025-04-01 11:40:15",updateTime:"2025-04-05 09:10:45"},{id:5,bankName:"招商银行",recipientName:"赵小龙",accountNumber:"6225 0000 0000 0000",qrCode:"https://example.com/qrcode4.png",totalLimit:35e4,remainingLimit:182e3,receivedAmount:168e3,currency:"CNY",sortOrder:4,status:1,createTime:"2025-04-01 13:20:30",updateTime:"2025-04-03 16:50:20"},{id:4,bankName:"中国银行",recipientName:"钱小伟",accountNumber:"6216 0000 0000 0000",qrCode:"https://example.com/qrcode5.png",totalLimit:45e4,remainingLimit:24e4,receivedAmount:21e4,currency:"CNY",sortOrder:5,status:0,createTime:"2025-04-01 14:45:10",updateTime:"2025-04-05 11:30:40"}]),J=ke(()=>{let o=p.value;if(T.value){const e=T.value.toLowerCase();o=o.filter(s=>s.bankName.toLowerCase().includes(e)||s.recipientName.toLowerCase().includes(e))}if(F.value!==""){const e=parseInt(F.value);o=o.filter(s=>s.status===e)}return o}),Y=o=>`¥${o.toLocaleString("zh-CN")}`,j=()=>{w.value=1,y()},X=o=>{w.value=o,y()},B=o=>{D.value=o,w.value=1,y()},Z=o=>{b.value=o},y=()=>{M.value=!0,setTimeout(()=>{M.value=!1},500)},ee=()=>{k.value=!0,ne(),C.value=!0},le=()=>{if(b.value.length!==1){V.warning("请选择一条记录进行编辑");return}k.value=!1;const o=b.value[0];Object.assign(n,o),C.value=!0},te=()=>{if(b.value.length===0){V.warning("请至少选择一条记录进行删除");return}L.value=null,N.value=!0},ae=o=>{k.value=!1,Object.assign(n,o),C.value=!0},oe=o=>{L.value=o,N.value=!0},ne=()=>{n.id=0,n.bankName="",n.recipientName="",n.accountNumber="",n.qrCode="",n.totalLimit=0,n.remainingLimit=0,n.receivedAmount=0,n.currency="CNY",n.sortOrder=p.value.length>0?Math.max(...p.value.map(o=>o.sortOrder))+1:1,n.status=1,$.value=[]},ie=()=>{if(k.value){const o={...n,id:Math.max(...p.value.map(e=>e.id))+1,remainingLimit:n.totalLimit,receivedAmount:0,createTime:new Date().toLocaleString("zh-CN",{hour12:!1}),updateTime:new Date().toLocaleString("zh-CN",{hour12:!1})};p.value.unshift(o),O.value=p.value.length,V.success("添加成功")}else{const o=p.value.findIndex(e=>e.id===n.id);if(o!==-1){p.value[o].remainingLimit;const e=p.value[o].receivedAmount,s=n.totalLimit-e;p.value[o]={...p.value[o],...n,remainingLimit:s,updateTime:new Date().toLocaleString("zh-CN",{hour12:!1})},V.success("保存成功")}}C.value=!1},de=()=>{if(L.value)p.value=p.value.filter(o=>{var e;return o.id!==((e=L.value)==null?void 0:e.id)}),L.value=null;else{const o=b.value.map(e=>e.id);p.value=p.value.filter(e=>!o.includes(e.id)),b.value=[]}O.value=p.value.length,V.success("删除成功"),N.value=!1},se=o=>{I.value=o.qrCode||"",R.value=!0},ue=o=>{V.success(`银行卡状态已${o.status===1?"开启":"关闭"}`)},re=()=>{V.info("功能尚未实现")},me=(o,e)=>{console.log(o,e)},pe=o=>{console.log(o)},ce=(o,e)=>Ze.confirm(`确定移除 ${o.name}？`),fe=(o,e,s)=>{n.qrCode=URL.createObjectURL(e.raw)},ve=()=>{E.value=!0},ge=()=>{Object.keys(d).forEach(o=>{const e=o;e==="createTimeRange"||e==="updateTimeRange"?d[e]=[]:e==="totalLimitMin"||e==="totalLimitMax"?d[e]=void 0:d[e]=""})},be=()=>{E.value=!1,w.value=1,y(),V.success("筛选条件已应用")};return Le(()=>{y()}),(o,e)=>{const s=qe,r=he,_=Ue,f=Ye,P=$e,_e=je,Ve=Te,v=Qe,q=Be,Ce=De,m=Ge,we=He,S=Je,K=We,z=Ee,Q=Xe,Ne=Re;return U(),A("div",ll,[i("div",tl,[i("div",al,[l(r,{class:"toolbar-button",type:"default",onClick:y},{default:a(()=>[l(s,null,{default:a(()=>[l(g(Se))]),_:1}),e[26]||(e[26]=u("刷新 "))]),_:1}),l(r,{class:"toolbar-button",type:"success",onClick:ee},{default:a(()=>[l(s,{class:"el-icon-plus"}),e[27]||(e[27]=u("添加 "))]),_:1}),l(r,{class:"toolbar-button",type:"default",disabled:b.value.length!==1,onClick:le},{default:a(()=>[l(s,null,{default:a(()=>[l(g(G))]),_:1}),e[28]||(e[28]=u("编辑 "))]),_:1},8,["disabled"]),l(r,{class:"toolbar-button",type:"danger",disabled:b.value.length===0,onClick:te},{default:a(()=>[l(s,null,{default:a(()=>[l(g(H))]),_:1}),e[29]||(e[29]=u("删除 "))]),_:1},8,["disabled"])]),i("div",ol,[l(_,{modelValue:T.value,"onUpdate:modelValue":e[0]||(e[0]=t=>T.value=t),placeholder:"搜索银行名称/收款人",class:"search-input",onKeyup:xe(j,["enter"])},null,8,["modelValue"]),l(r,{class:"search-button",type:"primary",onClick:j},{default:a(()=>[l(s,null,{default:a(()=>[l(g(ze))]),_:1})]),_:1}),l(r,{class:"toolbar-button filter-button",type:"default",onClick:ve},{default:a(()=>[l(s,null,{default:a(()=>[l(g(Ae))]),_:1}),e[30]||(e[30]=u("筛选 "))]),_:1}),l(r,{class:"toolbar-button export-button",type:"default",onClick:re},{default:a(()=>[l(s,null,{default:a(()=>[l(g(Oe))]),_:1}),e[31]||(e[31]=u("导出 "))]),_:1})])]),l(Ve,{class:"table-card"},{default:a(()=>[i("div",nl,[Me((U(),Ie(_e,{ref:"bankCardsTable",data:J.value,border:"",stripe:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:Z,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:a(()=>[l(f,{type:"selection",width:"40",align:"center",fixed:"left"}),l(f,{prop:"id",label:"ID",width:"70",align:"center",fixed:"left"}),l(f,{prop:"bankName",label:"银行名称",width:"120",align:"center"}),l(f,{prop:"recipientName",label:"收款人",width:"100",align:"center"}),l(f,{prop:"accountNumber",label:"收款账号/地址","min-width":"180",align:"center"}),l(f,{label:"收款码",width:"80",align:"center"},{default:a(t=>[l(r,{type:"success",size:"small",circle:"",onClick:h=>se(t.row)},{default:a(()=>[l(s,null,{default:a(()=>[l(g(Fe))]),_:1})]),_:2},1032,["onClick"])]),_:1}),l(f,{prop:"totalLimit",label:"总额度",width:"100",align:"center"},{default:a(t=>[u(x(Y(t.row.totalLimit)),1)]),_:1}),l(f,{prop:"remainingLimit",label:"剩余额度",width:"100",align:"center"},{default:a(t=>[u(x(Y(t.row.remainingLimit)),1)]),_:1}),l(f,{prop:"receivedAmount",label:"已收款金额",width:"120",align:"center"},{default:a(t=>[u(x(Y(t.row.receivedAmount)),1)]),_:1}),l(f,{prop:"currency",label:"货币",width:"60",align:"center"}),l(f,{prop:"sortOrder",label:"排序",width:"60",align:"center"}),l(f,{label:"状态",width:"80",align:"center"},{default:a(t=>[l(P,{modelValue:t.row.status,"onUpdate:modelValue":h=>t.row.status=h,"active-value":1,"inactive-value":0,onChange:h=>ue(t.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(f,{prop:"createTime",label:"添加时间",width:"150",align:"center",sortable:""},{default:a(t=>[u(x(t.row.createTime),1)]),_:1}),l(f,{prop:"updateTime",label:"更新时间",width:"150",align:"center",sortable:""},{default:a(t=>[u(x(t.row.updateTime),1)]),_:1}),l(f,{label:"操作",width:"140",align:"center",fixed:"right"},{default:a(t=>[i("div",il,[l(r,{class:"operation-button icon-only",size:"small",type:"default",onClick:h=>ae(t.row)},{default:a(()=>[l(s,null,{default:a(()=>[l(g(G))]),_:1})]),_:2},1032,["onClick"]),l(r,{type:"danger",size:"small",onClick:h=>oe(t.row),class:"operation-button"},{default:a(()=>[l(s,null,{default:a(()=>[l(g(H))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Ne,M.value]])])]),_:1}),i("div",dl,[l(Ce,{"current-page":w.value,"onUpdate:currentPage":e[1]||(e[1]=t=>w.value=t),"page-size":D.value,"onUpdate:pageSize":e[2]||(e[2]=t=>D.value=t),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:O.value,onSizeChange:B,onCurrentChange:X,"pager-count":7,background:""},{sizes:a(()=>[l(q,{"model-value":D.value,onChange:B,class:"custom-page-size"},{default:a(()=>[(U(),A(Pe,null,Ke([10,20,50,100],t=>l(v,{key:t,value:t,label:`${t}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),l(z,{modelValue:C.value,"onUpdate:modelValue":e[11]||(e[11]=t=>C.value=t),title:k.value?"添加收款银行卡":"编辑收款银行卡",width:"500px",center:""},{footer:a(()=>[i("span",sl,[l(r,{type:"primary",onClick:ie},{default:a(()=>e[34]||(e[34]=[u("确定")])),_:1}),l(r,{onClick:e[10]||(e[10]=t=>C.value=!1)},{default:a(()=>e[35]||(e[35]=[u("取消")])),_:1})])]),default:a(()=>[l(K,{model:n,"label-position":"top","label-width":"100px"},{default:a(()=>[l(m,{label:"银行名称"},{default:a(()=>[l(_,{modelValue:n.bankName,"onUpdate:modelValue":e[3]||(e[3]=t=>n.bankName=t)},null,8,["modelValue"])]),_:1}),l(m,{label:"收款人"},{default:a(()=>[l(_,{modelValue:n.recipientName,"onUpdate:modelValue":e[4]||(e[4]=t=>n.recipientName=t)},null,8,["modelValue"])]),_:1}),l(m,{label:"收款账号/地址"},{default:a(()=>[l(_,{modelValue:n.accountNumber,"onUpdate:modelValue":e[5]||(e[5]=t=>n.accountNumber=t)},null,8,["modelValue"])]),_:1}),l(m,{label:"收款码"},{default:a(()=>[l(we,{class:"upload-demo",action:"https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15","on-preview":pe,"on-remove":me,"before-remove":ce,"on-success":fe,"file-list":$.value,"list-type":"picture"},{tip:a(()=>e[33]||(e[33]=[i("div",{class:"el-upload__tip"},"请上传收款二维码图片，格式为jpg/png，大小不超过2MB",-1)])),default:a(()=>[l(r,{type:"primary"},{default:a(()=>e[32]||(e[32]=[u("上传收款码")])),_:1})]),_:1},8,["file-list"])]),_:1}),l(m,{label:"总额度"},{default:a(()=>[l(S,{modelValue:n.totalLimit,"onUpdate:modelValue":e[6]||(e[6]=t=>n.totalLimit=t),min:0,step:1e3,"controls-position":"right"},null,8,["modelValue"])]),_:1}),l(m,{label:"货币"},{default:a(()=>[l(q,{modelValue:n.currency,"onUpdate:modelValue":e[7]||(e[7]=t=>n.currency=t),placeholder:"请选择货币"},{default:a(()=>[l(v,{label:"CNY",value:"CNY"}),l(v,{label:"USD",value:"USD"})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"排序"},{default:a(()=>[l(S,{modelValue:n.sortOrder,"onUpdate:modelValue":e[8]||(e[8]=t=>n.sortOrder=t),min:1,step:1,"controls-position":"right"},null,8,["modelValue"])]),_:1}),l(m,{label:"状态"},{default:a(()=>[l(P,{modelValue:n.status,"onUpdate:modelValue":e[9]||(e[9]=t=>n.status=t),"active-value":1,"inactive-value":0,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),l(z,{modelValue:N.value,"onUpdate:modelValue":e[13]||(e[13]=t=>N.value=t),title:"确认删除",width:"400px",center:""},{footer:a(()=>[i("span",ul,[l(r,{type:"danger",onClick:de},{default:a(()=>e[36]||(e[36]=[u("确定删除")])),_:1}),l(r,{onClick:e[12]||(e[12]=t=>N.value=!1)},{default:a(()=>e[37]||(e[37]=[u("取消")])),_:1})])]),default:a(()=>[e[38]||(e[38]=i("p",{class:"delete-confirm-text"},"确定要删除所选银行卡吗？此操作不可恢复。",-1))]),_:1},8,["modelValue"]),l(z,{modelValue:R.value,"onUpdate:modelValue":e[14]||(e[14]=t=>R.value=t),title:"收款码",width:"400px",center:""},{default:a(()=>[i("div",rl,[I.value?(U(),A("img",{key:0,src:I.value,class:"qrcode-image",alt:"收款码"},null,8,ml)):(U(),A("div",pl,"暂无收款码"))])]),_:1},8,["modelValue"]),l(z,{modelValue:E.value,"onUpdate:modelValue":e[25]||(e[25]=t=>E.value=t),title:"筛选条件",width:"850px",center:""},{footer:a(()=>[i("span",yl,[l(r,{onClick:ge},{default:a(()=>e[43]||(e[43]=[u("重置")])),_:1}),l(r,{type:"primary",onClick:be},{default:a(()=>e[44]||(e[44]=[u("应用")])),_:1})])]),default:a(()=>[i("div",cl,[l(K,{"label-position":"top",model:d,"label-width":"100px"},{default:a(()=>[i("div",fl,[e[39]||(e[39]=i("div",{class:"filter-group-title"},"基本信息",-1)),i("div",vl,[l(m,{label:"ID"},{default:a(()=>[l(_,{modelValue:d.id,"onUpdate:modelValue":e[15]||(e[15]=t=>d.id=t),placeholder:"请输入ID"},null,8,["modelValue"])]),_:1}),l(m,{label:"银行名称"},{default:a(()=>[l(_,{modelValue:d.bankName,"onUpdate:modelValue":e[16]||(e[16]=t=>d.bankName=t),placeholder:"请输入银行名称"},null,8,["modelValue"])]),_:1})]),i("div",gl,[l(m,{label:"收款人"},{default:a(()=>[l(_,{modelValue:d.recipientName,"onUpdate:modelValue":e[17]||(e[17]=t=>d.recipientName=t),placeholder:"请输入收款人"},null,8,["modelValue"])]),_:1}),l(m,{label:"收款账号"},{default:a(()=>[l(_,{modelValue:d.accountNumber,"onUpdate:modelValue":e[18]||(e[18]=t=>d.accountNumber=t),placeholder:"请输入收款账号"},null,8,["modelValue"])]),_:1})])]),i("div",bl,[e[41]||(e[41]=i("div",{class:"filter-group-title"},"额度信息",-1)),i("div",_l,[l(m,{label:"总额度范围"},{default:a(()=>[i("div",Vl,[l(S,{modelValue:d.totalLimitMin,"onUpdate:modelValue":e[19]||(e[19]=t=>d.totalLimitMin=t),min:0,placeholder:"最小金额"},null,8,["modelValue"]),e[40]||(e[40]=i("span",{class:"range-separator"},"至",-1)),l(S,{modelValue:d.totalLimitMax,"onUpdate:modelValue":e[20]||(e[20]=t=>d.totalLimitMax=t),min:0,placeholder:"最大金额"},null,8,["modelValue"])])]),_:1}),l(m,{label:"货币"},{default:a(()=>[l(q,{modelValue:d.currency,"onUpdate:modelValue":e[21]||(e[21]=t=>d.currency=t),placeholder:"全部",style:{width:"100%"}},{default:a(()=>[l(v,{label:"全部",value:""}),l(v,{label:"CNY",value:"CNY"}),l(v,{label:"USD",value:"USD"})]),_:1},8,["modelValue"])]),_:1})]),i("div",Cl,[l(m,{label:"状态"},{default:a(()=>[l(q,{modelValue:d.status,"onUpdate:modelValue":e[22]||(e[22]=t=>d.status=t),placeholder:"全部状态",style:{width:"100%"}},{default:a(()=>[l(v,{label:"全部状态",value:""}),l(v,{label:"开启",value:"1"}),l(v,{label:"关闭",value:"0"})]),_:1},8,["modelValue"])]),_:1})])]),i("div",wl,[e[42]||(e[42]=i("div",{class:"filter-group-title"},"时间范围",-1)),i("div",Nl,[l(m,{label:"添加时间"},{default:a(()=>[l(Q,{modelValue:d.createTimeRange,"onUpdate:modelValue":e[23]||(e[23]=t=>d.createTimeRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(m,{label:"更新时间"},{default:a(()=>[l(Q,{modelValue:d.updateTimeRange,"onUpdate:modelValue":e[24]||(e[24]=t=>d.updateTimeRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}}}),Bl=el(kl,[["__scopeId","data-v-d0dd75e2"]]);export{Bl as default};
