import request from '@/utils/request'

// API响应类型定义
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface CustomerServiceImageData {
  id: number
  attachment_id: number
  file_name: string
  file_path: string
  file_size: number
  status: number
  created_at: string
  updated_at: string
  attachment?: {
    id: number
    filename: string
    file_path: string
    file_size: number
    file_type: string
    mime_type: string
  }
}

export interface CustomerServiceImageListData {
  items: CustomerServiceImageData[]
  total: number
}

export interface CustomerServiceImageQuery {
  page: number
  limit: number
  status?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface CustomerServiceImageFormData {
  attachment_id: number
  file_name?: string
  file_path: string
  file_size?: number
  status?: number
}

// 获取客服图片列表
export function getCustomerServiceImageList(params: CustomerServiceImageQuery) {
  return request<ApiResponse<CustomerServiceImageListData>>({
    url: '/api/admin/customer-service-images',
    method: 'get',
    params
  })
}

// 获取客服图片详情
export function getCustomerServiceImage(id: number) {
  return request<ApiResponse<CustomerServiceImageData>>({
    url: `/api/admin/customer-service-images/${id}`,
    method: 'get'
  })
}

// 创建客服图片
export function addCustomerServiceImage(data: CustomerServiceImageFormData) {
  return request<ApiResponse<CustomerServiceImageData>>({
    url: '/api/admin/customer-service-images',
    method: 'post',
    data
  })
}

// 更新客服图片
export function updateCustomerServiceImage(id: number, data: Partial<CustomerServiceImageFormData>) {
  return request<ApiResponse<CustomerServiceImageData>>({
    url: `/api/admin/customer-service-images/${id}`,
    method: 'put',
    data
  })
}

// 删除客服图片
export function deleteCustomerServiceImage(id: number) {
  console.log('删除客服图片 API - ID:', id, '类型:', typeof id);

  // 确保ID是数字
  if (typeof id !== 'number' || isNaN(id)) {
    console.error('删除客服图片 API - 无效的ID:', id);
    return Promise.reject(new Error('无效的ID'));
  }

  return request<ApiResponse<null>>({
    url: `/api/admin/customer-service-images/${id}`,
    method: 'delete'
  })
}

// 批量删除客服图片
export function batchDeleteCustomerServiceImage(ids: number[]) {
  console.log('批量删除客服图片 - 前端发送的IDs:', ids);
  console.log('批量删除客服图片 - 前端发送的IDs类型:', typeof ids, Array.isArray(ids));
  console.log('批量删除客服图片 - 前端发送的IDs JSON:', JSON.stringify({ ids }));

  return request<ApiResponse<{ deleted_count: number }>>({
    url: '/api/admin/customer-service-images/batch-delete',
    method: 'post',
    data: { ids },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}


