{"version": 3, "file": "Delta.js", "sourceRoot": "", "sources": ["../src/Delta.ts"], "names": [], "mappings": ";;;;AAAA,wDAA6B;AAC7B,sEAAyC;AACzC,kEAAqC;AACrC,gEAA0C;AAC1C,4CAAsB;AAEtB,IAAM,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;AAEtF;IAKE,eAAY,GAA0B;QACpC,wCAAwC;QACxC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACtB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;SAChB;aAAM,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAChD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;SACpB;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;SACf;IACH,CAAC;IAED,sBAAM,GAAN,UAAO,GAAoB,EAAE,UAAyB;QACpD,IAAM,KAAK,GAAO,EAAE,CAAC;QACrB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/C,OAAO,IAAI,CAAC;SACb;QACD,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;QACnB,IACE,UAAU,IAAI,IAAI;YAClB,OAAO,UAAU,KAAK,QAAQ;YAC9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAClC;YACA,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,sBAAM,GAAN,UAAO,MAAc;QACnB,IAAI,MAAM,IAAI,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,sBAAM,GAAN,UAAO,MAAc,EAAE,UAAyB;QAC9C,IAAI,MAAM,IAAI,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QACD,IAAM,KAAK,GAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QACrC,IACE,UAAU,IAAI,IAAI;YAClB,OAAO,UAAU,KAAK,QAAQ;YAC9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAClC;YACA,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,oBAAI,GAAJ,UAAK,KAAS;QACZ,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC5B,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACjC,KAAK,GAAG,0BAAS,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,IACE,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ;gBAChC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EACjC;gBACA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC/D,OAAO,IAAI,CAAC;aACb;YACD,oFAAoF;YACpF,gCAAgC;YAChC,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,EAAE;gBAC7D,KAAK,IAAI,CAAC,CAAC;gBACX,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC7B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACxB,OAAO,IAAI,CAAC;iBACb;aACF;YACD,IAAI,wBAAO,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE;gBAChD,IACE,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ;oBAChC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EACjC;oBACA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC/D,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,EAAE;wBACxC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;qBACnD;oBACD,OAAO,IAAI,CAAC;iBACb;qBAAM,IACL,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ;oBAChC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EACjC;oBACA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC/D,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,EAAE;wBACxC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;qBACnD;oBACD,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QACD,IAAI,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;SAClC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oBAAI,GAAJ;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7C,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACjD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,sBAAM,GAAN,UAAO,SAA6C;QAClD,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,uBAAO,GAAP,UAAQ,SAA0C;QAChD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED,mBAAG,GAAH,UAAO,SAAuC;QAC5C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IAED,yBAAS,GAAT,UAAU,SAA8B;QACtC,IAAM,MAAM,GAAS,EAAE,CAAC;QACxB,IAAM,MAAM,GAAS,EAAE,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,UAAC,EAAE;YACd,IAAM,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED,sBAAM,GAAN,UACE,SAAmD,EACnD,YAAe;QAEf,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IAClD,CAAC;IAED,4BAAY,GAAZ;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,IAAI;YAC9B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,OAAO,MAAM,GAAG,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aACjC;iBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;gBACtB,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;aAC7B;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAED,sBAAM,GAAN;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,IAAI;YAC9B,OAAO,MAAM,GAAG,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAED,qBAAK,GAAL,UAAM,KAAS,EAAE,GAAc;QAAzB,sBAAA,EAAA,SAAS;QAAE,oBAAA,EAAA,cAAc;QAC7B,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YACpC,IAAI,MAAM,SAAA,CAAC;YACX,IAAI,KAAK,GAAG,KAAK,EAAE;gBACjB,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;aACnC;iBAAM;gBACL,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;gBAChC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAClB;YACD,KAAK,IAAI,YAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;IAED,uBAAO,GAAP,UAAQ,KAAY;QAClB,IAAM,QAAQ,GAAG,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,IAAM,SAAS,GAAG,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAM,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;QACpC,IACE,UAAU,IAAI,IAAI;YAClB,OAAO,UAAU,CAAC,MAAM,KAAK,QAAQ;YACrC,UAAU,CAAC,UAAU,IAAI,IAAI,EAC7B;YACA,IAAI,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;YAClC,OACE,QAAQ,CAAC,QAAQ,EAAE,KAAK,QAAQ;gBAChC,QAAQ,CAAC,UAAU,EAAE,IAAI,SAAS,EAClC;gBACA,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACnC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aAC3B;YACD,IAAI,UAAU,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,EAAE;gBACrC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;aAC/C;SACF;QACD,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE;YAChD,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBACrC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;aAC9B;iBAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBAC3C,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aAC7B;iBAAM;gBACL,IAAM,QAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;gBACvE,IAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;gBACrC,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;gBACvC,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;oBACtC,IAAM,KAAK,GAAO,EAAE,CAAC;oBACrB,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;wBACrC,KAAK,CAAC,MAAM,GAAG,QAAM,CAAC;qBACvB;yBAAM;wBACL,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;qBAC9B;oBACD,8EAA8E;oBAC9E,IAAM,UAAU,GAAG,sBAAY,CAAC,OAAO,CACrC,MAAM,CAAC,UAAU,EACjB,OAAO,CAAC,UAAU,EAClB,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,CAClC,CAAC;oBACF,IAAI,UAAU,EAAE;wBACd,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;qBAC/B;oBACD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAElB,+CAA+C;oBAC/C,IACE,CAAC,SAAS,CAAC,OAAO,EAAE;wBACpB,wBAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAC/C;wBACA,IAAM,IAAI,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;wBACxC,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;qBAClC;oBAED,6DAA6D;oBAC7D,8BAA8B;iBAC/B;qBAAM,IACL,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ;oBAClC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EACjC;oBACA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrB;aACF;SACF;QACD,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,sBAAM,GAAN,UAAO,KAAY;QACjB,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;QAC1C,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAClD;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,oBAAI,GAAJ,UAAK,KAAY,EAAE,MAAiC;QAClD,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE;YAC1B,OAAO,IAAI,KAAK,EAAE,CAAC;SACpB;QACD,IAAM,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK;YACtC,OAAO,KAAK;iBACT,GAAG,CAAC,UAAC,EAAE;gBACN,IAAI,EAAE,CAAC,MAAM,IAAI,IAAI,EAAE;oBACrB,OAAO,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC;iBACnE;gBACD,IAAM,IAAI,GAAG,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC7C,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,GAAG,eAAe,CAAC,CAAC;YAC7D,CAAC,CAAC;iBACD,IAAI,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QACH,IAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;QAC7B,IAAM,UAAU,GAAG,mBAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACxD,IAAM,QAAQ,GAAG,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,IAAM,SAAS,GAAG,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAoB;YACtC,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACjC,OAAO,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;gBACjB,QAAQ,SAAS,CAAC,CAAC,CAAC,EAAE;oBACpB,KAAK,mBAAI,CAAC,MAAM;wBACd,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,MAAM,CAAC,CAAC;wBACpD,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACxC,MAAM;oBACR,KAAK,mBAAI,CAAC,MAAM;wBACd,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;wBACnD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACxB,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAC1B,MAAM;oBACR,KAAK,mBAAI,CAAC,KAAK;wBACb,QAAQ,GAAG,IAAI,CAAC,GAAG,CACjB,QAAQ,CAAC,UAAU,EAAE,EACrB,SAAS,CAAC,UAAU,EAAE,EACtB,MAAM,CACP,CAAC;wBACF,IAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACvC,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACzC,IAAI,wBAAO,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;4BAC1C,QAAQ,CAAC,MAAM,CACb,QAAQ,EACR,sBAAY,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CACzD,CAAC;yBACH;6BAAM;4BACL,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;yBACzC;wBACD,MAAM;iBACT;gBACD,MAAM,IAAI,QAAQ,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,wBAAQ,GAAR,UACE,SAImB,EACnB,OAAc;QAAd,wBAAA,EAAA,cAAc;QAEd,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,IAAI,CAAC,OAAO,EAAE,EAAE;YACrB,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBAChC,OAAO;aACR;YACD,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAC3B,IAAM,KAAK,GAAG,YAAE,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACpD,IAAM,KAAK,GACT,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;gBAC/B,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,KAAK;gBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;aACxB;iBAAM,IAAI,KAAK,GAAG,CAAC,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7B;iBAAM;gBACL,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;oBAC/D,OAAO;iBACR;gBACD,CAAC,IAAI,CAAC,CAAC;gBACP,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;aACpB;SACF;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YACrB,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACxB;IACH,CAAC;IAED,sBAAM,GAAN,UAAO,IAAW;QAChB,IAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,UAAC,SAAS,EAAE,EAAE;YACxB,IAAI,EAAE,CAAC,MAAM,EAAE;gBACb,QAAQ,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;aAChC;iBAAM,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,UAAU,IAAI,IAAI,EAAE;gBAC7C,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC3B,OAAO,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;aAC9B;iBAAM,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,UAAU,CAAC,EAAE;gBACpD,IAAM,QAAM,GAAG,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,CAAW,CAAC;gBAClD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,GAAG,QAAM,CAAC,CAAC;gBACxD,KAAK,CAAC,OAAO,CAAC,UAAC,MAAM;oBACnB,IAAI,EAAE,CAAC,MAAM,EAAE;wBACb,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACvB;yBAAM,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,UAAU,EAAE;wBACrC,QAAQ,CAAC,MAAM,CACb,YAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EACjB,sBAAY,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CACtD,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;gBACH,OAAO,SAAS,GAAG,QAAM,CAAC;aAC3B;YACD,OAAO,SAAS,CAAC;QACnB,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAID,yBAAS,GAAT,UAAU,GAAmB,EAAE,QAAgB;QAAhB,yBAAA,EAAA,gBAAgB;QAC7C,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;QACtB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SAC9C;QACD,IAAM,KAAK,GAAU,GAAG,CAAC;QACzB,IAAM,QAAQ,GAAG,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,IAAM,SAAS,GAAG,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,IAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE;YAChD,IACE,QAAQ,CAAC,QAAQ,EAAE,KAAK,QAAQ;gBAChC,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,EAC/C;gBACA,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aAC1C;iBAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBAC5C,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;aAC9B;iBAAM;gBACL,IAAM,QAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;gBACvE,IAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;gBACrC,IAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;gBACvC,IAAI,MAAM,CAAC,MAAM,EAAE;oBACjB,yEAAyE;oBACzE,SAAS;iBACV;qBAAM,IAAI,OAAO,CAAC,MAAM,EAAE;oBACzB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrB;qBAAM;oBACL,0CAA0C;oBAC1C,KAAK,CAAC,MAAM,CACV,QAAM,EACN,sBAAY,CAAC,SAAS,CACpB,MAAM,CAAC,UAAU,EACjB,OAAO,CAAC,UAAU,EAClB,QAAQ,CACT,CACF,CAAC;iBACH;aACF;SACF;QACD,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,iCAAiB,GAAjB,UAAkB,KAAa,EAAE,QAAgB;QAAhB,yBAAA,EAAA,gBAAgB;QAC/C,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;QACtB,IAAM,QAAQ,GAAG,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,MAAM,IAAI,KAAK,EAAE;YAC5C,IAAM,QAAM,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;YACrC,IAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBACzB,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,QAAM,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;gBAC1C,SAAS;aACV;iBAAM,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACjE,KAAK,IAAI,QAAM,CAAC;aACjB;YACD,MAAM,IAAI,QAAM,CAAC;SAClB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAzbM,QAAE,GAAG,YAAE,CAAC;IACR,kBAAY,GAAG,sBAAY,CAAC;IAybrC,YAAC;CAAA,AA3bD,IA2bC;AAED,iBAAS,KAAK,CAAC"}