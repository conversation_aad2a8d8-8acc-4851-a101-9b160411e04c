/* empty css             *//* empty css                 *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                     *//* empty css                  *//* empty css                          *//* empty css                  *//* empty css               *//* empty css                  *//* empty css                 */import{d as q,r as L,a as H,o as K,c as d,e as o,b as p,w as i,al as Q,y as x,V as f,a7 as S,i as X,a8 as V,a9 as A,at as Y,aq as Z,ao as ee,aT as te,n as y,aS as ae,m as le,ap as oe,x as ie,j as B,aB as R,ax as se,ay as ne,p as k,g as r,a0 as re,_ as me}from"./index-LncY9lAB.js";import{a as $}from"./index-t--hEgTQ.js";const ue={class:"params-form"},de={key:0,class:"section-title"},_e={key:1},ce={key:0},pe={key:5,class:"param-inner-table"},ye={class:"table-actions"},fe={key:6,class:"withdraw-fee-config"},ve={key:0,class:"fixed-fee-config"},he={key:1,class:"dynamic-fee-config"},ke={class:"table-actions"},we={class:"amount-range"},xe={class:"fee-config"},ge={class:"form-actions"},be=q({__name:"RatePanel",setup(Ve){const v=L([{title:"充值返佣比例设置",value:"",key:"",type:"header"},{title:"1级充值返佣比例(%)",value:"10",key:"[site.recharge_commission_rate_1]",type:"text"},{title:"2级充值返佣比例(%)",value:"3",key:"[site.recharge_commission_rate_2]",type:"text"},{title:"3级充值返佣比例(%)",value:"2",key:"[site.recharge_commission_rate_3]",type:"text"},{title:"4级充值返佣比例(%)",value:"0",key:"[site.recharge_commission_rate_4]",type:"text"},{title:"5级充值返佣比例(%)",value:"0",key:"[site.recharge_commission_rate_5]",type:"text"},{title:"6级充值返佣比例(%)",value:"0",key:"[site.recharge_commission_rate_6]",type:"text"},{title:"7级充值返佣比例(%)",value:"0",key:"[site.recharge_commission_rate_7]",type:"text"},{title:"8级充值返佣比例(%)",value:"0",key:"[site.recharge_commission_rate_8]",type:"text"},{title:"9级充值返佣比例(%)",value:"0",key:"[site.recharge_commission_rate_9]",type:"text"},{title:"10级充值返佣比例(%)",value:"0",key:"[site.recharge_commission_rate_10]",type:"text"},{title:"收益返佣比例设置",value:"",key:"",type:"header"},{title:"1级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_1]",type:"text"},{title:"2级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_2]",type:"text"},{title:"3级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_3]",type:"text"},{title:"4级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_4]",type:"text"},{title:"5级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_5]",type:"text"},{title:"6级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_6]",type:"text"},{title:"7级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_7]",type:"text"},{title:"8级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_8]",type:"text"},{title:"9级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_9]",type:"text"},{title:"10级收益返佣比例(%)",value:"0",key:"[site.income_commission_rate_10]",type:"text"},{title:"单笔最低存款金额",value:"100",key:"[site.min_deposit_amount]",type:"text"},{title:"单笔最低取款金额",value:"50",key:"[site.min_withdrawal_amount]",type:"text"},{title:"单笔最高取款金额",value:"100000",key:"[site.max_withdraw]",type:"text"},{title:"快捷充值金额",value:"20000,10000,5000,2000,1000,500,200",key:"[site.recharges]",type:"text"},{title:"充值时间",value:"0-24",key:"[site.recharge_hours]",type:"text"},{title:"每天最多取款次数",value:"10",key:"[site.withdraw_limit]",type:"text"},{title:"每周最多取款次数",value:"70",key:"[site.withdraw_week_limit]",type:"text"},{title:"首次充值返现比例(%)",value:"0",key:"[site.first_recharge_bonus_rate]",type:"text"},{title:"再次充值返现比例(%)",value:"0",key:"[site.second_recharge_bonus_rate]",type:"text"},{title:"取款工作日",value:"1,2,3,4,5,6,7",key:"[site.cashout_weeks]",type:"checkbox",options:[{label:"周一",value:"1"},{label:"周二",value:"2"},{label:"周三",value:"3"},{label:"周四",value:"4"},{label:"周五",value:"5"},{label:"周六",value:"6"},{label:"周日",value:"7"}]},{title:"取款时间范围",value:"9-21",key:"[site.cashout_hours]",type:"text"},{title:"取款手续费类型",value:"固定",key:"[site.withdraw_fee_type]",type:"select",options:[{label:"固定",value:"固定"},{label:"浮动",value:"浮动"}]},{title:"取款阶梯手续费",value:"",key:"[site.withdraw_fee_step]",type:"withdraw_fee_config",fixedAmount:"0",data:[{minAmount:"",maxAmount:"",feeType:"percentage",feeValue:""}]}]),N=()=>{re.confirm("确定要重置所有设置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C(),k.success("表单已重置")}).catch(()=>{})},P=async()=>{try{const a=v.value.filter(s=>s.type!=="header").map(s=>{let n=s.value;return s.type==="table"?n=JSON.stringify(s.data):s.type==="withdraw_fee_config"&&(g()==="固定"?n=JSON.stringify({type:"fixed",amount:s.fixedAmount||"0"}):n=JSON.stringify({type:"dynamic",data:s.data||[]})),{param_key:s.key,param_value:n,param_type:s.type,group_name:"rate"}}),l=await $.post("/api/admin/system-params",{params:a},{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});l.data.code===200?k.success("设置已保存"):k.error(l.data.message||"保存失败")}catch(a){console.error("保存资金配置参数失败:",a),k.error("保存失败，请稍后重试")}},C=async()=>{try{console.log("获取资金配置参数");const a=await $.get("/api/admin/system-params",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(a.data.code===200){const l=a.data.data,s=v.value.map(n=>{if(n.type==="header")return{...n};const _=l.find(m=>m.param_key===n.key);if(_){const m={...n};if(n.type==="table")try{m.data=JSON.parse(_.param_value)||[{amount:"",fee:""}]}catch(c){console.error(`解析JSON失败: ${_.param_key}`,c),m.data=[{amount:"",fee:""}]}else if(n.type==="withdraw_fee_config")try{const c=JSON.parse(_.param_value);c.type==="fixed"?(m.fixedAmount=c.amount||"0",m.data=[{minAmount:"",maxAmount:"",feeType:"percentage",feeValue:""}]):(m.fixedAmount="0",m.data=c.data||[{minAmount:"",maxAmount:"",feeType:"percentage",feeValue:""}])}catch(c){console.error(`解析取款手续费配置失败: ${_.param_key}`,c),m.fixedAmount="0",m.data=[{minAmount:"",maxAmount:"",feeType:"percentage",feeValue:""}]}else m.value=_.param_value||"";return m}return{...n}});v.value=s,T()}}catch(a){console.error("获取资金配置参数失败:",a),k.error("获取数据失败，请稍后重试")}},w=H({}),T=()=>{v.value.forEach(a=>{a.type==="checkbox"&&a.value&&(w[a.key]=a.value.split(","))})},O=a=>{a.value=w[a.key].join(",")},J=a=>{const l={};a.columns.forEach(s=>{l[s.prop]=""}),a.data.push(l)},g=()=>{const a=v.value.find(l=>l.key==="[site.withdraw_fee_type]");return(a==null?void 0:a.value)||"固定"},F=a=>{const l={minAmount:"",maxAmount:"",feeType:"percentage",feeValue:""};a.data.push(l)},U=(a,l)=>{a.data.splice(l,1)},I=({row:a})=>a.type==="header"?"section-header":a.type==="switch"?"switch-row":"",D=a=>["[site.max_withdraw]","[site.recharges]","[site.withdraw_limit]","[site.withdraw_week_limit]","[site.first_recharge_bonus_rate]","[site.second_recharge_bonus_rate]","[site.recharge_commission_rate_4]","[site.recharge_commission_rate_5]","[site.recharge_commission_rate_6]","[site.recharge_commission_rate_7]","[site.recharge_commission_rate_8]","[site.recharge_commission_rate_9]","[site.recharge_commission_rate_10]","[site.income_commission_rate_4]","[site.income_commission_rate_5]","[site.income_commission_rate_6]","[site.income_commission_rate_7]","[site.income_commission_rate_8]","[site.income_commission_rate_9]","[site.income_commission_rate_10]"].includes(a);return K(()=>{C(),T()}),(a,l)=>{const s=Q,n=X,_=Y,m=Z,c=ee,M=te,j=ae,h=le,E=ie,b=oe,z=ne,G=se;return r(),d("div",ue,[o(b,{data:v.value,class:"param-table","row-class-name":I},{default:i(()=>[o(s,{prop:"title",label:"变量标题",width:"150"},{default:i(t=>[t.row.type==="header"?(r(),d("span",de,x(t.row.title),1)):(r(),d("span",_e,x(t.row.title),1))]),_:1}),o(s,{prop:"value",label:"变量值"},{default:i(t=>[t.row.type==="header"?(r(),d("span",ce)):t.row.type==="text"?(r(),f(n,{key:1,modelValue:t.row.value,"onUpdate:modelValue":e=>t.row.value=e,placeholder:`请输入${t.row.title}`,disabled:D(t.row.key)},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])):t.row.type==="select"?(r(),f(m,{key:2,modelValue:t.row.value,"onUpdate:modelValue":e=>t.row.value=e,placeholder:`请选择${t.row.title}`,style:{width:"100%"}},{default:i(()=>[(r(!0),d(V,null,A(t.row.options,e=>(r(),f(_,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):t.row.type==="switch"?(r(),f(c,{key:3,modelValue:t.row.value,"onUpdate:modelValue":e=>t.row.value=e,"active-value":"1","inactive-value":"0","active-text":t.row.activeText,"inactive-text":t.row.inactiveText},null,8,["modelValue","onUpdate:modelValue","active-text","inactive-text"])):t.row.type==="checkbox"?(r(),f(j,{key:4,modelValue:w[t.row.key],"onUpdate:modelValue":e=>w[t.row.key]=e,onChange:e=>O(t.row)},{default:i(()=>[(r(!0),d(V,null,A(t.row.options,e=>(r(),f(M,{key:e.value,label:e.value},{default:i(()=>[y(x(e.label),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):t.row.type==="table"?(r(),d("div",pe,[p("div",ye,[o(h,{type:"primary",size:"small",onClick:e=>J(t.row)},{default:i(()=>l[0]||(l[0]=[y("添加")])),_:2},1032,["onClick"])]),o(b,{data:t.row.data,border:"",size:"small"},{default:i(()=>[(r(!0),d(V,null,A(t.row.columns,e=>(r(),f(s,{key:e.prop,prop:e.prop,label:e.label},{default:i(({row:u})=>[o(n,{modelValue:u[e.prop],"onUpdate:modelValue":W=>u[e.prop]=W,size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","label"]))),128)),o(s,{label:"操作",width:"100"},{default:i(({$index:e})=>[o(h,{type:"danger",size:"small",circle:"",onClick:u=>U(t.row,e)},{default:i(()=>[o(E,null,{default:i(()=>[o(B(R))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])):t.row.type==="withdraw_fee_config"?(r(),d("div",fe,[g()==="固定"?(r(),d("div",ve,[o(n,{modelValue:t.row.fixedAmount,"onUpdate:modelValue":e=>t.row.fixedAmount=e,placeholder:"请输入固定手续费金额",type:"number",min:0,step:.01},null,8,["modelValue","onUpdate:modelValue"]),l[1]||(l[1]=p("div",{class:"fee-tip"},"设置固定的取款手续费金额",-1))])):g()==="浮动"?(r(),d("div",he,[p("div",ke,[o(h,{type:"primary",size:"small",onClick:e=>F(t.row)},{default:i(()=>l[2]||(l[2]=[y(" 添加阶梯 ")])),_:2},1032,["onClick"])]),o(b,{data:t.row.data,border:"",size:"small"},{default:i(()=>[o(s,{label:"金额范围",width:"200"},{default:i(({row:e})=>[p("div",we,[o(n,{modelValue:e.minAmount,"onUpdate:modelValue":u=>e.minAmount=u,placeholder:"最小金额",type:"number",size:"small",min:0},null,8,["modelValue","onUpdate:modelValue"]),l[3]||(l[3]=p("span",null,"-",-1)),o(n,{modelValue:e.maxAmount,"onUpdate:modelValue":u=>e.maxAmount=u,placeholder:"最大金额",type:"number",size:"small",min:0},null,8,["modelValue","onUpdate:modelValue"])])]),_:1}),o(s,{label:"手续费设置",width:"280"},{default:i(({row:e})=>[p("div",xe,[o(G,{modelValue:e.feeType,"onUpdate:modelValue":u=>e.feeType=u,size:"small"},{default:i(()=>[o(z,{label:"percentage"},{default:i(()=>l[4]||(l[4]=[y("百分比")])),_:1}),o(z,{label:"fixed"},{default:i(()=>l[5]||(l[5]=[y("固定金额")])),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"]),o(n,{modelValue:e.feeValue,"onUpdate:modelValue":u=>e.feeValue=u,placeholder:e.feeType==="percentage"?"手续费率(%)":"手续费金额",type:"number",size:"small",min:0,step:(e.feeType==="percentage",.01)},{append:i(()=>[y(x(e.feeType==="percentage"?"%":""),1)]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","step"])])]),_:1}),o(s,{label:"操作",width:"100"},{default:i(({$index:e})=>[o(h,{type:"danger",size:"small",circle:"",onClick:u=>U(t.row,e)},{default:i(()=>[o(E,null,{default:i(()=>[o(B(R))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"]),l[6]||(l[6]=p("div",{class:"fee-tip"},"根据取款金额范围设置不同的手续费率或固定金额",-1))])):S("",!0)])):S("",!0)]),_:1}),o(s,{prop:"key",label:"变量名",width:"200"})]),_:1},8,["data"]),p("div",ge,[o(h,{onClick:N},{default:i(()=>l[7]||(l[7]=[y("重置")])),_:1}),o(h,{type:"primary",onClick:P},{default:i(()=>l[8]||(l[8]=[y("保存")])),_:1})])])}}}),Je=me(be,[["__scopeId","data-v-52273d0c"]]);export{Je as default};
