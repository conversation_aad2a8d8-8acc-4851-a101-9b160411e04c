/* uni.scss */



/* 文字颜色 */
$text-color: #333333;
$text-color-secondary: #666666;
$text-color-hint: #999999;

/* 边框颜色 */
$border-color: #E5E5E5;
$border-color-light: rgba(229, 229, 229, 0.5);

/* 间距 */
$spacing-sm: 10rpx;
$spacing-base: 20rpx;
$spacing-md: 30rpx;
$spacing-lg: 40rpx;
$spacing-xl: 60rpx;

/* 字体大小 */
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-md: 32rpx;
$font-size-lg: 36rpx;
$font-size-xl: 48rpx;
$font-size-xxl: 96rpx;

/* 圆角 */
$border-radius-sm: 6rpx;
$border-radius-base: 12rpx;
$border-radius-lg: 24rpx;

/* 阴影 */
$box-shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
$box-shadow-dark: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);

/* 组件高度 */
$button-height: 88rpx;
$input-height: 88rpx;
$list-item-height: 100rpx;
$header-height: 180rpx;
$tabbar-height: 100rpx;
