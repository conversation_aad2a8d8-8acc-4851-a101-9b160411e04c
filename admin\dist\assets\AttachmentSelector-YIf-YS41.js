/* empty css             *//* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                    *//* empty css                        *//* empty css                 *//* empty css                  */import{d as ne,r as d,a6 as N,V as v,w as r,ac as re,b as u,c as m,a7 as L,aj as pe,e as p,m as ue,n as j,x as ce,j as g,af as de,f as fe,i as ve,b7 as me,aL as ge,K as O,y as x,bc as ye,av as _e,ak as he,a8 as Te,a9 as we,v as be,M as ke,bd as Le,b1 as xe,b2 as Ue,b3 as Ce,be as je,ab as Se,p as y,g as o,_ as Ee}from"./index-LncY9lAB.js";import{g as K}from"./attachments-CNgOoo0P.js";const Re={class:"attachment-selector"},Ve={key:0,class:"selector-header"},ze={key:1,class:"direct-url-input"},Ie={key:0,class:"preview-container"},Be={class:"preview-content"},Ae=["src"],Fe={key:2,class:"url-preview-file"},$e={class:"file-name"},De={class:"upload-container"},Ne={class:"el-upload__tip"},Oe={key:2,class:"attachment-grid"},Ke=["onClick"],Me={class:"attachment-preview"},Pe={class:"image-error"},We={class:"attachment-info"},Ge={class:"attachment-name"},Je={class:"attachment-meta"},qe={key:3,class:"pagination-container"},He={class:"dialog-footer attachment-dialog-footer"},Qe=ne({__name:"AttachmentSelector",props:{visible:{type:Boolean,default:!1},fileType:{type:String,default:""}},emits:["update:visible","select","close"],setup(M,{emit:P}){const l=M,S=P,h=d(!1),I=d(!1),U=d(""),b=d(""),T=d([]),_=d(null),w=d(1),E=d(12),C=d(0),f=d(!1),i=d("");N(()=>l.visible,e=>{h.value=e,e&&(f.value=!1,i.value="",_.value=null,b.value=l.fileType,R(),console.log("对话框打开，showDirectUrlInput =",f.value))}),N(()=>h.value,e=>{S("update:visible",e),e||S("close")});const R=async()=>{I.value=!0;try{const e={page:w.value,pageSize:E.value};U.value&&(e.filename=U.value),l.fileType==="image"?e.fileType="image":l.fileType==="video"?e.fileType="video":l.fileType==="document"?e.fileType="document":b.value&&(e.fileType=b.value);const t=await K(e);if(console.log("完整响应对象:",t),t&&t.list){let s=t.list||[];if(console.log("原始附件列表:",JSON.stringify(s)),l.fileType==="image"){s.forEach(c=>{console.log(`附件 ${c.id} (${c.filename}): fileType=${c.fileType}, isImage=${k(c.fileType)}`)});const n=s.filter(c=>k(c.fileType));n.length===0&&s.length>0?console.warn("没有找到符合条件的图片附件，使用所有附件"):s=n}T.value=s,C.value=l.fileType==="image"?s.length:t.total||0,s.length===0&&t.list&&t.list.length>0&&(y.warning("没有找到符合条件的图片附件"),f.value=!0)}else T.value=[],C.value=0,y.error("获取附件列表失败: 数据格式不正确")}catch(e){if(e.response&&e.response.status){if(e.response.status===400)try{console.log("尝试不带参数重新获取附件列表");const t=await K({page:1,pageSize:12});if(t&&t.list){let s=t.list||[];l.fileType==="image"?s=s.filter(n=>k(n.fileType)):l.fileType==="video"&&(s=s.filter(n=>z(n.fileType))),T.value=s,C.value=t.total||0,y.warning("已重置筛选条件并获取所有附件");return}}catch(t){console.error("重试获取附件列表失败",t)}y.error(`获取附件列表失败: 服务器返回 ${e.response.status} 错误`)}else e.message?y.error(`获取附件列表失败: ${e.message}`):y.error("获取附件列表失败: 未知错误");T.value=[],C.value=0,f.value=!0,b.value&&(y.warning("已清除筛选条件，请重试"),b.value="")}finally{I.value=!1}},W=e=>{_.value=e},G=()=>{_.value?(S("select",_.value),h.value=!1):y.warning("请选择一个附件")},J=()=>{_.value=null,U.value="",i.value="",f.value=!1,b.value="",w.value=1},A=()=>{w.value=1,R()},V=e=>{var t;if(!e)return!1;try{const n=((t=new URL(e).pathname.split(".").pop())==null?void 0:t.toLowerCase())||"";return l.fileType==="image"?["jpg","jpeg","png","gif","webp","bmp","svg"].includes(n):l.fileType==="video"?["mp4","webm","avi","mov","flv"].includes(n):l.fileType==="document"?["pdf","doc","docx","xls","xlsx","ppt","pptx","txt"].includes(n):!0}catch{return!1}},F=()=>{var t;if(!V(i.value)){y.error("请输入有效的URL地址");return}const e={id:-1,url:i.value,filename:i.value.split("/").pop()||"自定义URL",fileType:((t=i.value.split(".").pop())==null?void 0:t.toLowerCase())||"",fileSize:0,width:0,height:0,uploadTime:new Date().toISOString(),isCustomUrl:!0};S("select",e),h.value=!1},q=e=>{w.value=e,R()},H=e=>{E.value=e,w.value=1,R()},k=e=>{if(!e)return!1;const t=e.toLowerCase(),s=["jpg","jpeg","png","gif","bmp","webp","svg","tiff","tif","ico","jfif","pjpeg","pjp"];if(s.includes(t))return!0;for(const n of s)if(t.startsWith(n))return!0;return!!t.startsWith("image/")},z=e=>e?["mp4","webm","avi","mov","flv"].includes(e.toLowerCase()):!1,Q=e=>e<1024?e+" B":e<1024*1024?(e/1024).toFixed(1)+" KB":e<1024*1024*1024?(e/(1024*1024)).toFixed(1)+" MB":(e/(1024*1024*1024)).toFixed(1)+" GB",$=e=>{var t;try{return((t=e.split(".").pop())==null?void 0:t.toLowerCase())||""}catch{return""}},X=e=>{try{return e.split("/").pop()||"未知文件"}catch{return"未知文件"}},Y=()=>l.fileType==="image"?".jpg,.jpeg,.png,.gif,.webp,.bmp,.svg,image/*":l.fileType==="video"?".mp4,.webm,.avi,.mov,.flv,video/*":l.fileType==="document"?".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt":"*/*",B=d([]),Z=e=>{B.value=[e],e.raw&&k(e.raw.name.split(".").pop())||e.raw&&z(e.raw.name.split(".").pop())?i.value=URL.createObjectURL(e.raw):i.value=""},ee=e=>{console.error("图片加载失败:",e)};return(e,t)=>{const s=ce,n=ue,c=ve,te=me,D=ge,le=_e,ae=je,se=Se,ie=re,oe=he;return o(),v(ie,{modelValue:h.value,"onUpdate:modelValue":t[6]||(t[6]=a=>h.value=a),title:l.fileType==="image"?"选择图片":l.fileType==="video"?"选择视频":"选择附件",width:"900px",center:"","close-on-click-modal":!1,onClosed:J},{footer:r(()=>[u("div",He,[p(n,{type:"primary",onClick:t[4]||(t[4]=a=>f.value?F():G()),disabled:f.value?!V(i.value)&&B.value.length===0:!_.value},{default:r(()=>t[12]||(t[12]=[j(" 确定 ")])),_:1},8,["disabled"]),p(n,{onClick:t[5]||(t[5]=a=>h.value=!1)},{default:r(()=>t[13]||(t[13]=[j("取消")])),_:1})])]),default:r(()=>[u("div",Re,[f.value?L("",!0):(o(),m("div",Ve,[p(c,{modelValue:U.value,"onUpdate:modelValue":t[0]||(t[0]=a=>U.value=a),placeholder:"搜索文件名",clearable:"",onKeyup:fe(A,["enter"]),class:"search-input"},{prefix:r(()=>[p(s,null,{default:r(()=>[p(g(de))]),_:1})]),append:r(()=>[p(n,{onClick:A},{default:r(()=>t[7]||(t[7]=[j("搜索")])),_:1})]),_:1},8,["modelValue"])])),f.value?(o(),m("div",ze,[p(te,{title:l.fileType==="image"?"输入图片URL":l.fileType==="video"?"输入视频URL":"输入文件URL",type:"warning",description:l.fileType==="image"?"无法获取附件列表，请直接输入图片URL地址，支持jpg、png、gif等格式":l.fileType==="video"?"无法获取附件列表，请直接输入视频URL地址，支持mp4、webm等格式":"无法获取附件列表，请直接输入文件URL地址","show-icon":"",closable:!1,style:{"margin-bottom":"15px"}},null,8,["title","description"]),p(c,{modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=a=>i.value=a),placeholder:l.fileType==="image"?"请输入图片URL地址":l.fileType==="video"?"请输入视频URL地址":"请输入文件URL地址",clearable:""},{append:r(()=>[p(n,{onClick:F,type:"primary",disabled:!V(i.value)},{default:r(()=>t[8]||(t[8]=[j("确认")])),_:1},8,["disabled"])]),_:1},8,["modelValue","placeholder"]),i.value&&V(i.value)?(o(),m("div",Ie,[t[9]||(t[9]=u("div",{class:"preview-title"},"预览",-1)),u("div",Be,[l.fileType==="image"&&k($(i.value))?(o(),v(D,{key:0,src:i.value,fit:"contain",class:"url-preview-image","preview-src-list":[i.value],"preview-teleported":"","hide-on-click-modal":""},null,8,["src","preview-src-list"])):l.fileType==="video"&&z($(i.value))?(o(),m("video",{key:1,src:i.value,controls:"",class:"url-preview-video"},null,8,Ae)):(o(),m("div",Fe,[p(s,{size:48},{default:r(()=>[p(g(O))]),_:1}),u("div",$e,x(X(i.value)),1)]))])])):L("",!0),u("div",De,[t[10]||(t[10]=u("div",{class:"upload-title"},"或者上传新文件",-1)),p(le,{class:"attachment-uploader",action:"#","auto-upload":!1,"on-change":Z,accept:Y(),limit:1,"file-list":B.value},{tip:r(()=>[u("div",Ne,x(l.fileType==="image"?"支持jpg、png、gif等格式的图片文件":l.fileType==="video"?"支持mp4、webm等格式的视频文件":"支持各种类型的文件"),1)]),default:r(()=>[p(n,{type:"primary"},{default:r(()=>[p(s,null,{default:r(()=>[p(g(ye))]),_:1}),j(" "+x(l.fileType==="image"?"选择图片":l.fileType==="video"?"选择视频":"选择文件"),1)]),_:1})]),_:1},8,["accept","file-list"])])])):L("",!0),f.value?L("",!0):pe((o(),m("div",Oe,[(o(!0),m(Te,null,we(T.value,a=>(o(),m("div",{key:a.id,class:be(["attachment-item",{selected:_.value&&_.value.id===a.id}]),onClick:Xe=>W(a)},[u("div",Me,[k(a.fileType)?(o(),v(D,{key:0,src:a.url,fit:"cover",class:"preview-image","preview-src-list":[a.url],"preview-teleported":"","hide-on-click-modal":"",onError:ee},{error:r(()=>[u("div",Pe,[p(s,null,{default:r(()=>[p(g(ke))]),_:1}),t[11]||(t[11]=u("span",null,"图片加载失败",-1))])]),_:2},1032,["src","preview-src-list"])):(o(),v(s,{key:1,size:40,class:"file-icon"},{default:r(()=>[z(a.fileType)?(o(),v(g(Le),{key:0})):a.fileType==="pdf"?(o(),v(g(O),{key:1})):a.fileType==="doc"||a.fileType==="docx"?(o(),v(g(xe),{key:2})):a.fileType==="zip"||a.fileType==="rar"?(o(),v(g(Ue),{key:3})):(o(),v(g(Ce),{key:4}))]),_:2},1024))]),u("div",We,[u("div",Ge,x(a.filename),1),u("div",Je,[u("span",null,x(Q(a.fileSize)),1),u("span",null,x(a.fileType),1)])])],10,Ke))),128)),T.value.length===0?(o(),v(ae,{key:0,description:"暂无附件"})):L("",!0)])),[[oe,I.value]]),!f.value&&T.value.length>0?(o(),m("div",qe,[p(se,{"current-page":w.value,"onUpdate:currentPage":t[2]||(t[2]=a=>w.value=a),"page-size":E.value,"onUpdate:pageSize":t[3]||(t[3]=a=>E.value=a),"page-sizes":[12,24,36,48],layout:"total, sizes, prev, pager, next, jumper",total:C.value,onSizeChange:H,onCurrentChange:q},null,8,["current-page","page-size","total"])])):L("",!0)])]),_:1},8,["modelValue","title"])}}}),ct=Ee(Qe,[["__scopeId","data-v-81e14213"]]);export{ct as A};
