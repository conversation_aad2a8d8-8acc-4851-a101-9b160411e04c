const speakeasy = require('speakeasy');
const QRCode = require('qrcode');

/**
 * 生成2FA密钥
 * @param {string} username 用户名
 * @returns {Object} 包含密钥和二维码URL的对象
 */
async function generateTwoFactorAuthSecret(username) {
  // 生成密钥
  const secret = speakeasy.generateSecret({
    length: 20,
    name: `FOX投资理财系统:${username}`
  });

  // 生成二维码数据URL
  const qrCodeDataUrl = await QRCode.toDataURL(secret.otpauth_url);

  return {
    secret: secret.base32,
    otpauth_url: secret.otpauth_url,
    qrcode: qrCodeDataUrl
  };
}

/**
 * 验证2FA令牌
 * @param {string} secret 用户的密钥
 * @param {string} token 用户提供的令牌
 * @returns {boolean} 验证是否成功
 */
function verifyTwoFactorAuthToken(secret, token) {
  return speakeasy.totp.verify({
    secret: secret,
    encoding: 'base32',
    token: token,
    window: 1 // 允许前后1个时间窗口的令牌，增加容错性
  });
}

/**
 * 生成恢复码
 * @returns {string} 恢复码
 */
function generateRecoveryCodes() {
  const codes = [];
  for (let i = 0; i < 10; i++) {
    // 生成16位随机字符串
    const code = Math.random().toString(36).substring(2, 6) + 
                 Math.random().toString(36).substring(2, 6) + 
                 Math.random().toString(36).substring(2, 6) + 
                 Math.random().toString(36).substring(2, 6);
    codes.push(code);
  }
  return codes;
}

module.exports = {
  generateTwoFactorAuthSecret,
  verifyTwoFactorAuthToken,
  generateRecoveryCodes
};
