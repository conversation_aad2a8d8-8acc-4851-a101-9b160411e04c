/* empty css             *//* empty css                   *//* empty css                      *//* empty css                *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                 */import{_ as Te,q as ge,r as b,o as Me,p as i,c as X,g as M,b as c,e as a,y as ee,w as n,b7 as Be,V as R,as as oa,aj as ye,ap as Ie,al as Se,an as Je,n as V,m as Ue,ak as Le,E as Pe,h as ze,i as Ae,ar as sa,aq as Qe,a8 as Ze,a9 as Ge,at as Xe,a7 as ke,aC as ea,ao as Re,ac as Ne,a0 as he,d as ia,a as qe,aJ as da,f as ra,aa as ua,ab as ma,x as ca,j as Y,ad as Fe,ae as Ke,b8 as Oe,af as He,ag as pa,ai as fa,aP as va,aQ as ga,aA as ya,az as ha,b9 as ba}from"./index-LncY9lAB.js";import{s as L}from"./request-Cd-6Wde0.js";/* empty css                    */import"./index-t--hEgTQ.js";const O={getPaymentChannels(_){return L({url:"/api/admin/payment-channels",method:"get",params:_})},getPaymentChannel(_){return L({url:`/api/admin/payment-channels/${_}`,method:"get"})},createPaymentChannel(_){return L({url:"/api/admin/payment-channels",method:"post",data:_}).then(v=>typeof v=="object"&&v!==null?v:{code:200,message:"创建成功",data:v})},updatePaymentChannel(_,v){return L({url:`/api/admin/payment-channels/${_}`,method:"put",data:v}).then(u=>typeof u=="object"&&u!==null?u:{code:200,message:"更新成功",data:u})},deletePaymentChannel(_){return L({url:`/api/admin/payment-channels/${_}`,method:"delete"}).then(v=>typeof v=="object"&&v!==null?v:{code:200,message:"删除成功",data:null})},updatePaymentChannelStatus(_,v,u){return L({url:`/api/admin/payment-channels/${_}/status`,method:"put",data:{field:v,value:u}}).then(U=>typeof U=="object"&&U!==null?U:{code:200,message:"更新成功",data:U})},updatePaymentChannelWeight(_,v){return L({url:`/api/admin/payment-channels/${_}/status`,method:"put",data:{field:"weight",value:v}}).then(u=>typeof u=="object"&&u!==null?u:{code:200,message:"更新成功",data:u})},updatePaymentChannelWeights(_){return L({url:"/api/admin/payment-channels/batch-weights",method:"post",data:_}).then(v=>typeof v=="object"&&v!==null?v:{code:200,message:"批量更新权重成功",data:v})}},H={getBankMappings(_){return L({url:"/api/admin/bank-mappings",method:"get",params:_})},upsertBankMapping(_){return L({url:"/api/admin/bank-mappings",method:"post",data:_})},deleteBankMapping(_){return L({url:`/api/admin/bank-mappings/${_}`,method:"delete"})},getAllBanks(){return L({url:"/api/admin/banks",method:"get"})},getBank(_){return L({url:`/api/admin/banks/${_}`,method:"get"})},createBank(_){return L({url:"/api/admin/banks",method:"post",data:_})},updateBank(_,v){return L({url:`/api/admin/banks/${_}`,method:"put",data:v})},updateBankStatus(_,v){return L({url:`/api/admin/banks/${_}/status`,method:"put",data:{status:v}})},deleteBank(_){return L({url:`/api/admin/banks/${_}`,method:"delete"})}},_a={class:"bank-code-mapping-panel"},wa={class:"panel-header"},ka={class:"panel-content"},Ca={class:"tab-content"},Va={class:"panel-footer"},Ea={class:"tab-content"},Da={class:"panel-footer"},xa={key:1},$a={class:"panel-footer"},Ta={key:2,class:"form-tip"},Ma={key:2,class:"form-tip"},Ba={class:"dialog-footer"},Ia={__name:"BankCodeMappingPanel",props:{paymentChannelId:{type:Number,required:!0},paymentChannelName:{type:String,default:"未知"},paymentChannelCode:{type:String,default:""}},setup(_){const v=_,u=ge(()=>v.paymentChannelCode==="basepay"||v.paymentChannelName.toLowerCase().includes("base")),U=ge(()=>v.paymentChannelCode==="kbpay"||v.paymentChannelName.toLowerCase().includes("kbpay")),B=ge(()=>u.value||U.value),$=b(!1),z=b([]),P=b([]),S=b(!1),D=b(!1),E=b({id:null,bank_id:null,bank_name:"",payin_method:null,payout_method:null,status:!0}),ae=b("bankMapping"),W=b(!1),J=b(!1),m=b({merchant_no:"",payin_key:"",payout_key:""}),o=b({}),d=async()=>{$.value=!0;try{if(!v.paymentChannelId){console.error("支付通道ID为空，无法获取银行编码映射"),i.warning("支付通道ID无效，请重试"),$.value=!1;return}const r=await H.getBankMappings({payment_channel_id:v.paymentChannelId});r&&typeof r=="object"?r.code===200?z.value=r.data||[]:Array.isArray(r)?z.value=r:(console.error("获取银行编码映射失败:",r.message),i.error(r.message||"获取银行编码映射失败")):(console.error("获取银行编码映射响应格式错误"),i.error("获取银行编码映射失败，响应格式错误"))}catch(r){console.error("获取银行编码映射错误:",r),i.error("获取银行编码映射失败，请检查网络连接")}finally{$.value=!1}},q=async()=>{try{console.log("正在获取所有银行");const r=await H.getAllBanks();console.log("获取银行列表响应:",r),r&&typeof r=="object"?r.code===200?(P.value=r.data||[],console.log("成功获取银行列表，数量:",P.value.length)):Array.isArray(r)?(P.value=r,console.log("成功获取银行列表，数量:",P.value.length)):(console.error("获取银行列表失败:",r.message),i.error(r.message||"获取银行列表失败")):(console.error("获取银行列表响应格式错误"),i.error("获取银行列表失败，响应格式错误"))}catch(r){console.error("获取银行列表错误:",r),i.error("获取银行列表失败，请检查网络连接")}},Q=r=>{var t;D.value=!1,E.value={id:r.id,bank_id:r.bank_id,bank_name:((t=r.bank)==null?void 0:t.name)||"",payin_method:r.payin_method,payout_method:r.payout_method,status:r.status},S.value=!0},x=async()=>{await q();const r=z.value.map(t=>t.bank_id);if(P.value=P.value.filter(t=>!r.includes(t.id)),P.value.length===0){i.warning("所有银行都已配置支付方式，无法添加新的映射");return}D.value=!0,E.value={id:null,bank_id:null,bank_name:"",payin_method:null,payout_method:null,status:!0},S.value=!0},se=async r=>{try{const t=await H.upsertBankMapping({id:r.id,bank_id:r.bank_id,payment_channel_id:v.paymentChannelId,bank_code:r.bank_code,status:!r.status});if(t&&typeof t=="object"&&t.code===200)i.success(`${r.status?"禁用":"启用"}成功`),d();else{const k=t&&t.message?t.message:`${r.status?"禁用":"启用"}失败`;i.error(k)}}catch(t){console.error(`${r.status?"禁用":"启用"}银行编码映射错误:`,t),i.error(`${r.status?"禁用":"启用"}失败`)}},te=async r=>{var t;try{await he.confirm(`确定要删除 ${((t=r.bank)==null?void 0:t.name)||"未知银行"} 的银行编码映射吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const k=await H.deleteBankMapping(r.id);if(k&&typeof k=="object"&&k.code===200)i.success("删除成功"),d();else{const T=k&&k.message?k.message:"删除失败";i.error(T)}}catch(k){if(k==="cancel"||k.toString().includes("cancel"))return;console.error("删除银行编码映射错误:",k),i.error("删除失败，请稍后重试")}},le=async()=>{if(D.value&&!E.value.bank_id){i.warning("请选择银行");return}try{const r={bank_id:E.value.bank_id,payment_channel_id:v.paymentChannelId,payin_method:E.value.payin_method,payout_method:E.value.payout_method,status:E.value.status};!D.value&&E.value.id&&(r.id=E.value.id);const t=await H.upsertBankMapping(r);if(t&&typeof t=="object"&&t.code===200)i.success("保存成功"),S.value=!1,d();else{const k=t&&t.message?t.message:"保存失败";i.error(k)}}catch(r){console.error("保存银行编码映射错误:",r),i.error("保存失败")}},ue=async()=>{if(B.value){W.value=!0;try{const r=u.value?"Base支付":"KBPay",t=await O.getPaymentChannel(v.paymentChannelId);if(t&&t.code===200&&t.data){const k=t.data;let T={};if(k.config)try{T=typeof k.config=="string"?JSON.parse(k.config):k.config}catch(Z){console.warn("解析商户配置失败:",Z)}m.value={merchant_no:T.merchant_no||"",payin_key:T.payin_key||"",payout_key:T.payout_key||""},o.value={...m.value}}else i.error("获取商户配置失败")}catch(r){const t=u.value?"Base支付":"KBPay";console.error(`获取${t}商户配置错误:`,r),i.error("获取商户配置失败，请检查网络连接")}finally{W.value=!1}}},me=async()=>{if(!m.value.merchant_no.trim()){i.warning("请输入商户号");return}if(!m.value.payin_key.trim()){i.warning("请输入代收密钥");return}if(!m.value.payout_key.trim()){i.warning("请输入代付密钥");return}J.value=!0;try{const r=await O.getPaymentChannel(v.paymentChannelId);if(!r||r.code!==200||!r.data){i.error("获取支付通道信息失败");return}const t=r.data;let k={};if(t.config)try{k=typeof t.config=="string"?JSON.parse(t.config):t.config}catch(ne){console.warn("解析现有配置失败:",ne)}const T={...k,merchant_no:m.value.merchant_no.trim(),payin_key:m.value.payin_key.trim(),payout_key:m.value.payout_key.trim()},Z={name:t.name,code:t.code,countryCode:t.countryCode||"PH",depositEnabled:t.depositEnabled,withdrawEnabled:t.withdrawEnabled,isDefault:t.isDefault,weight:t.weight,config:JSON.stringify(T)},I=await O.updatePaymentChannel(v.paymentChannelId,Z);I&&I.code===200?(i.success("商户配置保存成功"),o.value={...m.value}):i.error((I==null?void 0:I.message)||"保存商户配置失败")}catch(r){const t=u.value?"Base支付":"KBPay";console.error(`保存${t}商户配置失败:`,r),i.error("保存商户配置失败："+(r.message||"未知错误"))}finally{J.value=!1}},ce=()=>{m.value={...o.value},i.info("商户配置已重置")};return Me(()=>{v.paymentChannelId?(d(),B.value&&ue()):(console.warn("支付通道ID为空，无法获取配置"),i.warning("支付通道ID无效，请重试"))}),(r,t)=>{const k=Be,T=Se,Z=Je,I=Ue,ne=Ie,pe=oa,G=Ae,F=ze,be=Pe,Ce=sa,Ve=Xe,Ee=Qe,ie=ea,fe=Re,ve=Ne,de=Le;return M(),X("div",_a,[c("div",wa,[c("h3",null,ee(_.paymentChannelName)+" 支付通道配置",1),a(k,{type:"warning",closable:!1,"show-icon":""},{default:n(()=>t[13]||(t[13]=[c("p",null,"注意：此功能为高级配置，请谨慎操作。修改配置可能会影响支付处理。",-1)])),_:1})]),c("div",ka,[B.value?(M(),R(Ce,{key:0,modelValue:ae.value,"onUpdate:modelValue":t[3]||(t[3]=p=>ae.value=p),class:"config-tabs"},{default:n(()=>[a(pe,{label:"银行编码配置",name:"bankMapping"},{default:n(()=>[c("div",Ca,[ye((M(),R(ne,{data:z.value,border:"",style:{width:"100%"}},{default:n(()=>[a(T,{prop:"bank.name",label:"银行名称",width:"150"}),a(T,{prop:"payin_method",label:u.value?"代收编码(pay_type)":"代收方式",width:"140"},null,8,["label"]),a(T,{prop:"payout_method",label:u.value?"代付编码(bank_code)":"代付方式",width:"140"},null,8,["label"]),a(T,{prop:"status",label:"状态",width:"100"},{default:n(p=>[a(Z,{type:p.row.status?"success":"danger"},{default:n(()=>[V(ee(p.row.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(T,{label:"操作"},{default:n(p=>[a(I,{type:"primary",size:"small",onClick:oe=>Q(p.row)},{default:n(()=>t[14]||(t[14]=[V("编辑")])),_:2},1032,["onClick"]),a(I,{type:p.row.status?"danger":"success",size:"small",onClick:oe=>se(p.row)},{default:n(()=>[V(ee(p.row.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),a(I,{type:"danger",size:"small",onClick:oe=>te(p.row)},{default:n(()=>t[15]||(t[15]=[V(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,$.value]]),c("div",Va,[a(I,{type:"primary",onClick:x},{default:n(()=>t[16]||(t[16]=[V("添加银行编码")])),_:1})])])]),_:1}),a(pe,{label:"商户配置",name:"merchantConfig"},{default:n(()=>[c("div",Ea,[ye((M(),R(be,{model:m.value,"label-width":"120px"},{default:n(()=>[a(F,{label:"商户号",required:""},{default:n(()=>[a(G,{modelValue:m.value.merchant_no,"onUpdate:modelValue":t[0]||(t[0]=p=>m.value.merchant_no=p),placeholder:u.value?"请输入Base支付商户号":"请输入KBPay商户号",clearable:""},null,8,["modelValue","placeholder"])]),_:1}),a(F,{label:"代收密钥",required:""},{default:n(()=>[a(G,{modelValue:m.value.payin_key,"onUpdate:modelValue":t[1]||(t[1]=p=>m.value.payin_key=p),placeholder:"请输入代收密钥",type:"password","show-password":"",clearable:""},null,8,["modelValue"]),t[17]||(t[17]=c("div",{class:"form-tip"},"用于客户充值功能的签名密钥",-1))]),_:1}),a(F,{label:"代付密钥",required:""},{default:n(()=>[a(G,{modelValue:m.value.payout_key,"onUpdate:modelValue":t[2]||(t[2]=p=>m.value.payout_key=p),placeholder:"请输入代付密钥",type:"password","show-password":"",clearable:""},null,8,["modelValue"]),t[18]||(t[18]=c("div",{class:"form-tip"},"用于提现功能的签名密钥",-1))]),_:1})]),_:1},8,["model"])),[[de,W.value]]),c("div",Da,[a(I,{type:"primary",onClick:me,loading:J.value},{default:n(()=>t[19]||(t[19]=[V(" 保存商户配置 ")])),_:1},8,["loading"]),a(I,{onClick:ce},{default:n(()=>t[20]||(t[20]=[V(" 重置 ")])),_:1})])])]),_:1})]),_:1},8,["modelValue"])):(M(),X("div",xa,[ye((M(),R(ne,{data:z.value,border:"",style:{width:"100%"}},{default:n(()=>[a(T,{prop:"bank.name",label:"银行名称",width:"150"}),a(T,{prop:"payin_method",label:"代收方式",width:"140"}),a(T,{prop:"payout_method",label:"代付方式",width:"140"}),a(T,{prop:"status",label:"状态",width:"100"},{default:n(p=>[a(Z,{type:p.row.status?"success":"danger"},{default:n(()=>[V(ee(p.row.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(T,{label:"操作"},{default:n(p=>[a(I,{type:"primary",size:"small",onClick:oe=>Q(p.row)},{default:n(()=>t[21]||(t[21]=[V("编辑")])),_:2},1032,["onClick"]),a(I,{type:p.row.status?"danger":"success",size:"small",onClick:oe=>se(p.row)},{default:n(()=>[V(ee(p.row.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),a(I,{type:"danger",size:"small",onClick:oe=>te(p.row)},{default:n(()=>t[22]||(t[22]=[V(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[de,$.value]]),c("div",$a,[a(I,{type:"primary",onClick:x},{default:n(()=>t[23]||(t[23]=[V("添加银行编码")])),_:1})])]))]),a(ve,{modelValue:S.value,"onUpdate:modelValue":t[12]||(t[12]=p=>S.value=p),title:D.value?"添加银行支付方式":"编辑银行支付方式",width:"500px","append-to-body":"","destroy-on-close":""},{footer:n(()=>[c("span",Ba,[a(I,{onClick:t[11]||(t[11]=p=>S.value=!1)},{default:n(()=>t[24]||(t[24]=[V("取消")])),_:1}),a(I,{type:"primary",onClick:le},{default:n(()=>t[25]||(t[25]=[V("确定")])),_:1})])]),default:n(()=>[a(be,{model:E.value,"label-width":"100px"},{default:n(()=>[D.value?(M(),R(F,{key:0,label:"银行"},{default:n(()=>[a(Ee,{modelValue:E.value.bank_id,"onUpdate:modelValue":t[4]||(t[4]=p=>E.value.bank_id=p),placeholder:"请选择银行"},{default:n(()=>[(M(!0),X(Ze,null,Ge(P.value,p=>(M(),R(Ve,{key:p.id,label:p.name,value:p.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):(M(),R(F,{key:1,label:"银行"},{default:n(()=>[a(G,{modelValue:E.value.bank_name,"onUpdate:modelValue":t[5]||(t[5]=p=>E.value.bank_name=p),disabled:""},null,8,["modelValue"])]),_:1})),a(F,{label:u.value?"代收编码(pay_type)":"代收方式"},{default:n(()=>[u.value?(M(),R(G,{key:1,modelValue:E.value.payin_method,"onUpdate:modelValue":t[7]||(t[7]=p=>E.value.payin_method=p),placeholder:"请输入代收编码，如：1720",clearable:"",style:{width:"100%"}},null,8,["modelValue"])):(M(),R(ie,{key:0,modelValue:E.value.payin_method,"onUpdate:modelValue":t[6]||(t[6]=p=>E.value.payin_method=p),placeholder:"请输入代收方式编号",min:1,max:9999,style:{width:"100%"}},null,8,["modelValue"])),u.value?(M(),X("div",Ta,"Base支付代收编码，如：1720（菲律宾二类）")):ke("",!0)]),_:1},8,["label"]),a(F,{label:u.value?"代付编码(bank_code)":"代付方式"},{default:n(()=>[u.value?(M(),R(G,{key:1,modelValue:E.value.payout_method,"onUpdate:modelValue":t[9]||(t[9]=p=>E.value.payout_method=p),placeholder:"请输入代付编码，如：IDPT0001",clearable:"",style:{width:"100%"}},null,8,["modelValue"])):(M(),R(ie,{key:0,modelValue:E.value.payout_method,"onUpdate:modelValue":t[8]||(t[8]=p=>E.value.payout_method=p),placeholder:"请输入代付方式编号",min:1,max:9999,style:{width:"100%"}},null,8,["modelValue"])),u.value?(M(),X("div",Ma,"Base支付代付编码，如：IDPT0001")):ke("",!0)]),_:1},8,["label"]),a(F,{label:"状态"},{default:n(()=>[a(fe,{modelValue:E.value.status,"onUpdate:modelValue":t[10]||(t[10]=p=>E.value.status=p)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Sa=Te(Ia,[["__scopeId","data-v-bded701a"]]),Ua={class:"bank-management-panel"},La={class:"panel-header"},Pa={class:"panel-content"},za={class:"panel-footer"},Aa={class:"dialog-footer"},Ra={__name:"BankManagementPanel",setup(_){const v=b(!1),u=b([]),U=b(!1),B=b(!1),$=b({id:null,name:"",status:!0}),z=b(null),P={name:[{required:!0,message:"请输入银行名称",trigger:"blur"},{min:2,max:50,message:"银行名称长度应在2到50个字符之间",trigger:"blur"}]},S=async()=>{v.value=!0;try{const m=await H.getAllBanks();m&&typeof m=="object"?m.code===200?u.value=m.data||[]:Array.isArray(m)?u.value=m:i.error(m.message||"获取银行列表失败"):i.error("获取银行列表失败，响应格式错误")}catch(m){console.error("获取银行列表错误:",m),i.error("获取银行列表失败，请检查网络连接")}finally{v.value=!1}},D=()=>{B.value=!0,$.value={id:null,name:"",status:!0},U.value=!0},E=m=>{B.value=!1,$.value={id:m.id,name:m.name,status:m.status},U.value=!0},ae=async m=>{try{const o=await H.updateBankStatus(m.id,!m.status);if(o&&typeof o=="object"&&o.code===200)i.success(`${m.status?"禁用":"启用"}成功`),S();else{const d=o&&o.message?o.message:`${m.status?"禁用":"启用"}失败`;i.error(d)}}catch(o){console.error(`${m.status?"禁用":"启用"}银行错误:`,o),i.error(`${m.status?"禁用":"启用"}失败`)}},W=async m=>{try{await he.confirm(`确定要删除银行 "${m.name}" 吗？此操作不可恢复，且可能影响已关联的银行卡和银行编码映射。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const o=await H.deleteBank(m.id);if(o&&typeof o=="object")if(o.code===200)i.success("删除成功"),S();else if(o.code===400&&o.message)o.message.includes("银行编码映射引用")?he.alert("该银行已被银行编码映射引用，无法直接删除。请先删除相关的银行编码映射，然后再尝试删除此银行。","无法删除",{confirmButtonText:"我知道了",type:"warning"}):o.message.includes("银行卡引用")?he.alert("该银行已被用户的银行卡引用，无法直接删除。请先删除或修改相关的银行卡信息，然后再尝试删除此银行。","无法删除",{confirmButtonText:"我知道了",type:"warning"}):i.error(o.message||"删除失败");else{const d=o.message||"删除失败";i.error(d)}else i.error("删除失败，响应格式错误")}catch(o){if(o==="cancel"||o.toString().includes("cancel"))return;console.error("删除银行错误:",o),i.error("删除失败，请稍后重试")}},J=async()=>{if(z.value)try{await z.value.validate();let m;if(B.value?m=await H.createBank($.value):m=await H.updateBank($.value.id,$.value),m&&typeof m=="object"&&m.code===200)i.success(B.value?"添加成功":"更新成功"),U.value=!1,S();else{const o=m&&m.message?m.message:B.value?"添加失败":"更新失败";i.error(o)}}catch(m){if(m.name==="ValidationError")return;console.error("保存银行错误:",m),i.error(B.value?"添加失败":"更新失败")}};return Me(()=>{S()}),(m,o)=>{const d=Be,q=Se,Q=Je,x=Ue,se=Ie,te=Ae,le=ze,ue=Re,me=Pe,ce=Ne,r=Le;return M(),X("div",Ua,[c("div",La,[o[5]||(o[5]=c("h3",null,"银行信息管理",-1)),a(d,{type:"info",closable:!1,"show-icon":""},{default:n(()=>o[4]||(o[4]=[c("p",null,"在此页面可以添加、编辑和删除银行信息。这些银行信息将用于银行编码映射和用户银行卡管理。",-1)])),_:1})]),c("div",Pa,[ye((M(),R(se,{data:u.value,border:"",style:{width:"100%"}},{default:n(()=>[a(q,{prop:"id",label:"ID",width:"80"}),a(q,{prop:"name",label:"银行名称","min-width":"180"}),a(q,{prop:"status",label:"状态",width:"100"},{default:n(t=>[a(Q,{type:t.row.status?"success":"danger"},{default:n(()=>[V(ee(t.row.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(q,{prop:"created_at",label:"创建时间",width:"180"}),a(q,{label:"操作",width:"200"},{default:n(t=>[a(x,{type:"primary",size:"small",onClick:k=>E(t.row)},{default:n(()=>o[6]||(o[6]=[V("编辑")])),_:2},1032,["onClick"]),a(x,{type:t.row.status?"danger":"success",size:"small",onClick:k=>ae(t.row)},{default:n(()=>[V(ee(t.row.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),a(x,{type:"danger",size:"small",onClick:k=>W(t.row)},{default:n(()=>o[7]||(o[7]=[V(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[r,v.value]]),c("div",za,[a(x,{type:"primary",onClick:D},{default:n(()=>o[8]||(o[8]=[V("添加银行")])),_:1})])]),a(ce,{modelValue:U.value,"onUpdate:modelValue":o[3]||(o[3]=t=>U.value=t),title:B.value?"添加银行":"编辑银行",width:"500px","append-to-body":"","destroy-on-close":""},{footer:n(()=>[c("span",Aa,[a(x,{onClick:o[2]||(o[2]=t=>U.value=!1)},{default:n(()=>o[9]||(o[9]=[V("取消")])),_:1}),a(x,{type:"primary",onClick:J},{default:n(()=>o[10]||(o[10]=[V("确定")])),_:1})])]),default:n(()=>[a(me,{model:$.value,"label-width":"100px",rules:P,ref_key:"editFormRef",ref:z},{default:n(()=>[a(le,{label:"银行名称",prop:"name"},{default:n(()=>[a(te,{modelValue:$.value.name,"onUpdate:modelValue":o[0]||(o[0]=t=>$.value.name=t),placeholder:"请输入银行名称"},null,8,["modelValue"])]),_:1}),a(le,{label:"状态"},{default:n(()=>[a(ue,{modelValue:$.value.status,"onUpdate:modelValue":o[1]||(o[1]=t=>$.value.status=t)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Na=Te(Ra,[["__scopeId","data-v-59fee938"]]),ja={class:"payment-channels-container"},Ya={class:"toolbar"},qa={class:"toolbar-left"},Fa={class:"toolbar-right"},Ka={class:"table-wrapper"},Oa={class:"operation-buttons-container"},Ha={class:"pagination-container"},Wa={class:"form-section"},Ja={class:"form-row"},Qa={class:"form-section"},Za={class:"form-row"},Ga={class:"form-row"},Xa={class:"range-input"},et={class:"form-section"},at={class:"form-row"},tt={class:"filter-footer"},lt={class:"form-section"},nt={class:"form-row"},ot={class:"form-row"},st={class:"form-section"},it={class:"form-row"},dt={class:"form-row"},rt={class:"form-section"},ut={class:"form-row"},mt={class:"dialog-footer"},We=48,ct=ia({__name:"index",setup(_){const v=b(!1),u=b([]),U=b([]),B=b(1),$=b(10),z=b(""),P=b(!1),S=b(!1),D=b(""),E=b(!1),ae=b(null),W=b(""),J=b(""),m=b(!1),o=qe({id:0,name:"",code:"",countryCode:"",depositEnabled:0,withdrawEnabled:0,isDefault:0,weight:0,createTime:"",updateTime:""}),d=qe({id:"",name:"",code:"",depositEnabled:null,withdrawEnabled:null,isDefault:null,weightMin:null,weightMax:null,createTimeRange:null}),q=ge(()=>{if(!Array.isArray(u.value)||u.value.length===0)return[];let l=[...u.value];try{if(z.value){const e=z.value.toLowerCase();l=l.filter(f=>f&&f.name&&typeof f.name=="string"&&f.name.toLowerCase().includes(e))}d.id&&(l=l.filter(e=>e&&e.id&&String(e.id).includes(d.id))),d.name&&(l=l.filter(e=>e&&e.name&&typeof e.name=="string"&&e.name.toLowerCase().includes(d.name.toLowerCase())))}catch(e){return console.error("过滤数据时出错:",e),[]}try{if(d.code&&(l=l.filter(e=>e&&e.code&&typeof e.code=="string"&&e.code.toLowerCase().includes(d.code.toLowerCase()))),d.depositEnabled!==null&&(l=l.filter(e=>e&&e.depositEnabled!==void 0&&e.depositEnabled===d.depositEnabled)),d.withdrawEnabled!==null&&(l=l.filter(e=>e&&e.withdrawEnabled!==void 0&&e.withdrawEnabled===d.withdrawEnabled)),d.isDefault!==null&&(l=l.filter(e=>e&&e.isDefault!==void 0&&e.isDefault===d.isDefault)),d.weightMin!==null&&(l=l.filter(e=>e&&e.weight!==void 0&&e.weight>=d.weightMin)),d.weightMax!==null&&(l=l.filter(e=>e&&e.weight!==void 0&&e.weight<=d.weightMax)),d.createTimeRange&&d.createTimeRange.length===2){const e=new Date(d.createTimeRange[0]).getTime(),f=new Date(d.createTimeRange[1]).getTime();l=l.filter(g=>{if(!g||!g.createTime)return!1;try{const y=new Date(g.createTime).getTime();return!isNaN(y)&&y>=e&&y<=f}catch(y){return console.error("解析创建时间出错:",y),!1}})}}catch(e){return console.error("应用筛选条件时出错:",e),[]}return l}),Q=ge(()=>{if(!Array.isArray(q.value))return[];const l=(B.value-1)*$.value,e=l+$.value;return q.value.slice(l,e)}),x=async()=>{v.value=!0;try{const l={};d.id&&(l.id=d.id),d.name&&(l.name=d.name),d.code&&(l.code=d.code),d.depositEnabled!==null&&(l.depositEnabled=d.depositEnabled),d.withdrawEnabled!==null&&(l.withdrawEnabled=d.withdrawEnabled),d.isDefault!==null&&(l.isDefault=d.isDefault),d.weightMin!==null&&(l.weightMin=d.weightMin),d.weightMax!==null&&(l.weightMax=d.weightMax),d.createTimeRange&&d.createTimeRange.length===2&&(l.createTimeStart=d.createTimeRange[0],l.createTimeEnd=d.createTimeRange[1]);const e=await O.getPaymentChannels(l);D.value="";let f;e&&e.code===200&&e.data?f=e.data:f=e;const g=f;if(g&&g.items&&Array.isArray(g.items))try{const y=g.items.map(h=>({id:Number(h.id),name:String(h.name||""),code:String(h.code||""),countryCode:String(h.countryCode||""),depositEnabled:Number(h.depositEnabled||0),withdrawEnabled:Number(h.withdrawEnabled||0),isDefault:Number(h.isDefault||0),weight:Number(h.weight||0),config:h.config,createTime:String(h.createTime||""),updateTime:String(h.updateTime||"")}));u.value=y}catch{D.value="处理数据时出错",i.error("处理数据时出错"),u.value=[]}else D.value="数据格式不正确",i.error("数据格式不正确"),u.value=[]}catch(l){D.value="",l.message&&typeof l.message=="string"&&l.message.includes("Network Error")?D.value="网络连接失败，请检查网络设置":l.message&&typeof l.message=="string"&&l.message.includes("timeout")?D.value="请求超时，请稍后再试":D.value="获取数据失败，请稍后重试",i.error(D.value),u.value=[]}finally{v.value=!1}},se=l=>{U.value=l},te=()=>{B.value=1},le=l=>{let e;if(l)e=l;else if(U.value.length===1)e=U.value[0];else{i.warning("请选择一条记录进行编辑");return}Object.assign(o,{...e}),S.value=!0},ue=async l=>{const e=l.depositEnabled;try{l.depositLoading=!0;const f=await O.updatePaymentChannelStatus(l.id,"depositEnabled",l.depositEnabled);f&&f.code===200?(i.success(`${l.name} 存款开关已${l.depositEnabled===1?"开启":"关闭"}`),await x()):(l.depositEnabled=e,i.error(f&&f.message||"更新失败"))}catch{i.error("更新失败"),l.depositEnabled=e}finally{l.depositLoading=!1}},me=async l=>{const e=l.withdrawEnabled;try{l.withdrawLoading=!0;const f=await O.updatePaymentChannelStatus(l.id,"withdrawEnabled",l.withdrawEnabled);f&&f.code===200?(i.success(`${l.name} 取款开关已${l.withdrawEnabled===1?"开启":"关闭"}`),await x()):(l.withdrawEnabled=e,i.error(f&&f.message||"更新失败"))}catch{i.error("更新失败"),l.withdrawEnabled=e}finally{l.withdrawLoading=!1}},ce=async l=>{const e=l.isDefault;if(e===1&&l.isDefault===0){i.warning("默认通道不能被关闭，请先设置其他通道为默认"),l.isDefault=1;return}try{l.defaultLoading=!0,l.isDefault===1&&u.value.forEach(g=>{g.id!==l.id&&(g.isDefault=0)});const f=await O.updatePaymentChannelStatus(l.id,"isDefault",l.isDefault);f&&f.code===200?(l.isDefault===1?i.success(`${l.name} 已设置为默认通道`):i.success(`${l.name} 已取消默认通道设置`),await x()):(l.isDefault=e,e===0&&l.isDefault===1&&await x(),i.error(f&&f.message||"更新失败"))}catch{i.error("更新失败"),l.isDefault=e,await x()}finally{l.defaultLoading=!1}},r=b(-1),t=b(null),k=b(!1),T=b(0),Z=(l,e,f)=>{r.value=f,k.value=!0,T.value=l.clientY,document.body.style.userSelect="none",document.body.style.cursor="grabbing",document.addEventListener("mousemove",I),document.addEventListener("mouseup",ne);const g=document.querySelectorAll(".el-table__body .el-table__row");g[r.value]&&(t.value=g[r.value],t.value.classList.add("row-dragging"),t.value.style.zIndex="1000"),l.preventDefault()},I=l=>{if(!k.value||!t.value)return;const e=l.clientY-T.value;t.value.style.transform=`translateY(${e}px)`;const f=Math.round(e/We)+r.value;document.querySelectorAll(".el-table__body .el-table__row").forEach((y,h)=>{h===f&&h!==r.value?y.classList.add("row-drop-target"):y.classList.remove("row-drop-target")})},ne=l=>{if(!k.value||r.value===-1||!t.value){pe();return}const e=l.clientY-T.value,f=Math.round(e/We)+r.value;f>=0&&f<Q.value.length&&f!==r.value&&G(r.value,f),pe()},pe=()=>{document.body.style.userSelect="",document.body.style.cursor="",t.value&&(t.value.style.transform="",t.value.style.zIndex="",t.value.classList.remove("row-dragging")),document.querySelectorAll(".el-table__body .el-table__row").forEach(e=>e.classList.remove("row-drop-target")),k.value=!1,r.value=-1,t.value=null,document.removeEventListener("mousemove",I),document.removeEventListener("mouseup",ne)},G=async(l,e)=>{const f=Q.value[l],g=Q.value[e],y=u.value.findIndex(C=>C.id===f.id),h=u.value.findIndex(C=>C.id===g.id);if(y!==-1&&h!==-1){u.value[y].weightLoading=!0,u.value[h].weightLoading=!0;try{const C=[...u.value];C.sort((w,A)=>w.id-A.id),C.sort((w,A)=>A.weight-w.weight);const K=C.findIndex(w=>w.id===f.id),De=C.findIndex(w=>w.id===g.id),[xe]=C.splice(K,1);C.splice(De,0,xe),C.forEach((w,A)=>{w.weight=(C.length-A)*10}),u.value.forEach(w=>{const A=C.find($e=>$e.id===w.id);A&&(w.weight=A.weight)});const N=new Date().toISOString().replace("T"," ").substring(0,19);u.value[y].updateTime=N,u.value[h].updateTime=N,u.value.sort((w,A)=>A.weight-w.weight),u.value=[...u.value];const re=u.value.map(w=>({id:w.id,weight:w.weight})),_e=await O.updatePaymentChannelWeights(re);_e&&_e.code===200?(i.success("已更新所有支付通道的排序权重"),await x()):i.warning("排序已在前端更新，但服务器可能未保存，请刷新页面")}catch{i.warning("排序已在前端更新，但服务器保存失败，请刷新页面"),setTimeout(()=>{x()},2e3)}finally{u.value[y]&&(u.value[y].weightLoading=!1),u.value[h]&&(u.value[h].weightLoading=!1)}}},F=()=>{P.value=!0},be=()=>{B.value=1,P.value=!1,i.success("筛选条件已应用")},Ce=()=>{Object.keys(d).forEach(l=>{l==="id"||l==="name"||l==="code"?d[l]="":d[l]=null}),B.value=1,i.success("筛选条件已重置")},Ve=async()=>{try{if(!o.name||!o.code){i.warning("请填写必填字段");return}if(!/^[a-zA-Z0-9_-]+$/.test(o.code)){i.warning("通道代码只能包含字母、数字、下划线和连字符");return}if(!o.id&&(o.weight===void 0||o.weight===0)){const y=Math.max(0,...u.value.map(h=>h.weight));o.weight=y+10}!u.value.some(y=>y.isDefault===1&&y.id!==o.id)&&o.isDefault!==1&&(o.isDefault=1,i.info("系统必须有一个默认通道，已自动设置为默认"));const e={name:o.name,code:o.code,countryCode:o.countryCode||"CN",depositEnabled:o.depositEnabled!==void 0?o.depositEnabled:0,withdrawEnabled:o.withdrawEnabled!==void 0?o.withdrawEnabled:0,isDefault:o.isDefault!==void 0?o.isDefault:0,weight:o.weight!==void 0?o.weight:0};let f=ba.service({lock:!0,text:"保存中...",background:"rgba(0, 0, 0, 0.7)"}),g;if(o.id?g=await O.updatePaymentChannel(o.id,e):g=await O.createPaymentChannel(e),f.close(),!g){i.error("服务器未返回有效响应，但数据可能已更新，请刷新页面查看最新数据"),S.value=!1,await x();return}if(g.code===200){if(o.id){const y=u.value.findIndex(h=>h.id===o.id);if(y!==-1&&g.data){const h=g.data;h.isDefault===1&&u.value.forEach(C=>{C.id!==h.id&&(C.isDefault=0)}),u.value[y]=h,u.value.sort((C,K)=>K.weight-C.weight)}else await x()}else await x();i.success(o.id?"通道信息更新成功":"通道创建成功"),S.value=!1}else i.error(g.message||"操作失败"),g.code>=500&&await x()}catch(l){loadingInstance&&loadingInstance.close(),l instanceof Error?i.error(`操作失败: ${l.message}`):i.error("操作失败，请稍后重试"),await x(),S.value=!1}},Ee=()=>{i.info("功能尚未实现")},ie=b(!1),fe=b(!1),ve=b(!1),de=l=>{l.key.toLowerCase()==="p"?ie.value=!0:l.key.toLowerCase()==="k"?fe.value=!0:l.key.toLowerCase()==="l"&&(ve.value=!0)},p=l=>{l.key.toLowerCase()==="p"?ie.value=!1:l.key.toLowerCase()==="k"?fe.value=!1:l.key.toLowerCase()==="l"&&(ve.value=!1)},oe=(l,e)=>{try{ie.value?he.confirm("请选择要执行的操作","银行编码配置",{confirmButtonText:"配置银行编码",cancelButtonText:"取消",type:"info",showClose:!0,closeOnClickModal:!0,closeOnPressEscape:!0,distinguishCancelAndClose:!0}).then(()=>{ae.value=l.id,W.value=l.name,J.value=l.code,E.value=!0}).catch(f=>{}):i.info("功能开发中")}catch{i.error("操作失败，请稍后重试")}},aa=()=>{fe.value&&ve.value?m.value=!0:i.info('请使用表格操作栏中的"更多"按钮进行操作')},je=l=>{$.value=l,B.value=1},ta=l=>{B.value=l},la=()=>{D.value="",z.value="",B.value=1,x()};return Me(()=>{D.value="",x(),document.addEventListener("keydown",de),document.addEventListener("keyup",p)}),da(()=>{document.removeEventListener("keydown",de),document.removeEventListener("keyup",p)}),(l,e)=>{const f=Be,g=ca,y=Ue,h=Ae,C=Se,K=Re,De=Ie,xe=ua,N=Xe,re=Qe,_e=ma,w=ze,A=ea,$e=ya,Ye=Pe,we=Ne,na=Le;return M(),X("div",ja,[D.value&&D.value.length>0?(M(),R(f,{key:0,title:D.value,type:"error",closable:!0,onClose:e[0]||(e[0]=s=>D.value=""),"show-icon":"",style:{"margin-bottom":"10px"}},{default:n(()=>[e[28]||(e[28]=c("p",null,"如果您看到此错误，请尝试刷新页面。如果问题仍然存在，请联系技术支持。",-1)),c("p",null,"错误消息: "+ee(D.value),1)]),_:1},8,["title"])):ke("",!0),ke("",!0),c("div",Ya,[c("div",qa,[a(y,{class:"toolbar-button",type:"default",onClick:la},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(Fe))]),_:1}),e[30]||(e[30]=V("刷新 "))]),_:1}),a(y,{class:"toolbar-button",type:"primary",disabled:U.value.length!==1,onClick:le},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(Ke))]),_:1}),e[31]||(e[31]=V("编辑 "))]),_:1},8,["disabled"]),a(y,{class:"toolbar-button",type:"default",onClick:aa},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(Oe))]),_:1}),e[32]||(e[32]=V("更多 "))]),_:1})]),c("div",Fa,[a(h,{modelValue:z.value,"onUpdate:modelValue":e[1]||(e[1]=s=>z.value=s),placeholder:"搜索通道名称",class:"search-input",onKeyup:ra(te,["enter"])},null,8,["modelValue"]),a(y,{class:"search-button",type:"primary",onClick:te},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(He))]),_:1})]),_:1}),a(y,{class:"toolbar-button filter-button",type:"default",onClick:F},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(pa))]),_:1}),e[33]||(e[33]=V("筛选 "))]),_:1}),a(y,{class:"toolbar-button export-button",type:"default",onClick:Ee},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(fa))]),_:1}),e[34]||(e[34]=V("导出 "))]),_:1})])]),a(xe,{class:"table-card"},{default:n(()=>[c("div",Ka,[ye((M(),R(De,{ref:"channelsTable",data:u.value,border:"",stripe:"","highlight-current-row":"","row-key":"id",style:{width:"100%"},onSelectionChange:se,"cell-style":{padding:"0"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0"}},{default:n(()=>[a(C,{type:"selection",width:"40",align:"center",fixed:"left"}),a(C,{prop:"id",label:"ID","min-width":"70",align:"center",fixed:"left"}),a(C,{prop:"name",label:"通道名称","min-width":"120",align:"center",fixed:"left"}),a(C,{prop:"code",label:"通道标识","min-width":"120",align:"center"}),a(C,{prop:"countryCode",label:"国家区号","min-width":"100",align:"center"}),a(C,{label:"存款开关","min-width":"100",align:"center"},{default:n(s=>[a(K,{modelValue:s.row.depositEnabled,"onUpdate:modelValue":j=>s.row.depositEnabled=j,"active-value":1,"inactive-value":0,loading:s.row.depositLoading,onChange:j=>ue(s.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),a(C,{label:"取款开关","min-width":"100",align:"center"},{default:n(s=>[a(K,{modelValue:s.row.withdrawEnabled,"onUpdate:modelValue":j=>s.row.withdrawEnabled=j,"active-value":1,"inactive-value":0,loading:s.row.withdrawLoading,onChange:j=>me(s.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),a(C,{label:"默认","min-width":"80",align:"center"},{default:n(s=>[a(K,{modelValue:s.row.isDefault,"onUpdate:modelValue":j=>s.row.isDefault=j,"active-value":1,"inactive-value":0,loading:s.row.defaultLoading,onChange:j=>ce(s.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),a(C,{prop:"weight",label:"权重(从大到小)","min-width":"140",align:"center"}),a(C,{prop:"createTime",label:"创建时间","min-width":"160",align:"center",sortable:""}),a(C,{label:"操作","min-width":"160",align:"center",fixed:"right"},{default:n(s=>[c("div",Oa,[a(y,{class:"operation-button icon-only drag-handle",size:"small",type:"primary",onMousedown:va(j=>Z(j,s.row,s.$index),["prevent"])},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(ga))]),_:1})]),_:2},1032,["onMousedown"]),a(y,{class:"operation-button icon-only",size:"small",type:"default",onClick:j=>le(s.row)},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(Ke))]),_:1})]),_:2},1032,["onClick"]),a(y,{class:"operation-button icon-only",size:"small",type:"default",onClick:j=>oe(s.row,j)},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(Oe))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[na,v.value]])])]),_:1}),c("div",Ha,[a(_e,{"current-page":B.value,"onUpdate:currentPage":e[2]||(e[2]=s=>B.value=s),"page-size":$.value,"onUpdate:pageSize":e[3]||(e[3]=s=>$.value=s),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:u.value.length,onSizeChange:je,onCurrentChange:ta,"pager-count":7,background:""},{sizes:n(()=>[a(re,{"model-value":$.value,onChange:je,class:"custom-page-size"},{default:n(()=>[(M(),X(Ze,null,Ge([10,20,50,100],s=>a(N,{key:s,value:s,label:`${s}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),a(we,{modelValue:P.value,"onUpdate:modelValue":e[14]||(e[14]=s=>P.value=s),title:"筛选条件",width:"800px",center:"",top:"5vh",class:"filter-dialog","append-to-body":!0,"destroy-on-close":!0,"close-on-click-modal":!1},{footer:n(()=>[c("div",tt,[a(y,{class:"filter-button",type:"primary",onClick:be},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(He))]),_:1}),e[39]||(e[39]=V("搜索 "))]),_:1}),a(y,{class:"filter-button",type:"default",onClick:Ce},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(Fe))]),_:1}),e[40]||(e[40]=V("重置 "))]),_:1}),a(y,{class:"filter-button",type:"default",onClick:e[13]||(e[13]=s=>P.value=!1)},{default:n(()=>[a(g,null,{default:n(()=>[a(Y(ha))]),_:1}),e[41]||(e[41]=V("取消 "))]),_:1})])]),default:n(()=>[a(Ye,{model:d,"label-position":"top","label-width":"100px"},{default:n(()=>[c("div",Wa,[e[35]||(e[35]=c("h3",{class:"form-section-title"},"基本信息",-1)),c("div",Ja,[a(w,{label:"ID"},{default:n(()=>[a(h,{modelValue:d.id,"onUpdate:modelValue":e[4]||(e[4]=s=>d.id=s),placeholder:"请输入ID",size:"small"},null,8,["modelValue"])]),_:1}),a(w,{label:"通道名称"},{default:n(()=>[a(h,{modelValue:d.name,"onUpdate:modelValue":e[5]||(e[5]=s=>d.name=s),placeholder:"请输入通道名称",size:"small"},null,8,["modelValue"])]),_:1}),a(w,{label:"通道标识"},{default:n(()=>[a(h,{modelValue:d.code,"onUpdate:modelValue":e[6]||(e[6]=s=>d.code=s),placeholder:"请输入通道标识",size:"small"},null,8,["modelValue"])]),_:1})])]),c("div",Qa,[e[37]||(e[37]=c("h3",{class:"form-section-title"},"状态设置",-1)),c("div",Za,[a(w,{label:"存款开关"},{default:n(()=>[a(re,{modelValue:d.depositEnabled,"onUpdate:modelValue":e[7]||(e[7]=s=>d.depositEnabled=s),placeholder:"请选择",style:{width:"100%"},size:"small"},{default:n(()=>[a(N,{label:"全部",value:null}),a(N,{label:"开启",value:1}),a(N,{label:"关闭",value:0})]),_:1},8,["modelValue"])]),_:1}),a(w,{label:"取款开关"},{default:n(()=>[a(re,{modelValue:d.withdrawEnabled,"onUpdate:modelValue":e[8]||(e[8]=s=>d.withdrawEnabled=s),placeholder:"请选择",style:{width:"100%"},size:"small"},{default:n(()=>[a(N,{label:"全部",value:null}),a(N,{label:"开启",value:1}),a(N,{label:"关闭",value:0})]),_:1},8,["modelValue"])]),_:1}),a(w,{label:"默认状态"},{default:n(()=>[a(re,{modelValue:d.isDefault,"onUpdate:modelValue":e[9]||(e[9]=s=>d.isDefault=s),placeholder:"请选择",style:{width:"100%"},size:"small"},{default:n(()=>[a(N,{label:"全部",value:null}),a(N,{label:"默认",value:1}),a(N,{label:"非默认",value:0})]),_:1},8,["modelValue"])]),_:1})]),c("div",Ga,[a(w,{label:"权重范围"},{default:n(()=>[c("div",Xa,[a(A,{modelValue:d.weightMin,"onUpdate:modelValue":e[10]||(e[10]=s=>d.weightMin=s),min:0,step:5,"controls-position":"right",placeholder:"最小值",size:"small"},null,8,["modelValue"]),e[36]||(e[36]=c("span",{class:"range-separator"},"至",-1)),a(A,{modelValue:d.weightMax,"onUpdate:modelValue":e[11]||(e[11]=s=>d.weightMax=s),min:0,step:5,"controls-position":"right",placeholder:"最大值",size:"small"},null,8,["modelValue"])])]),_:1})])]),c("div",et,[e[38]||(e[38]=c("h3",{class:"form-section-title"},"时间范围",-1)),c("div",at,[a(w,{label:"创建时间"},{default:n(()=>[a($e,{modelValue:d.createTimeRange,"onUpdate:modelValue":e[12]||(e[12]=s=>d.createTimeRange=s),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(we,{modelValue:E.value,"onUpdate:modelValue":e[15]||(e[15]=s=>E.value=s),title:"银行编码配置",width:"800px","destroy-on-close":""},{default:n(()=>[a(Sa,{"payment-channel-id":ae.value,"payment-channel-name":W.value,"payment-channel-code":J.value},null,8,["payment-channel-id","payment-channel-name","payment-channel-code"])]),_:1},8,["modelValue"]),a(we,{modelValue:m.value,"onUpdate:modelValue":e[16]||(e[16]=s=>m.value=s),title:"银行信息管理",width:"900px","destroy-on-close":""},{default:n(()=>[a(Na)]),_:1},8,["modelValue"]),a(we,{modelValue:S.value,"onUpdate:modelValue":e[27]||(e[27]=s=>S.value=s),title:"编辑支付通道",width:"700px",center:"",top:"5vh",class:"edit-dialog"},{footer:n(()=>[c("div",mt,[a(y,{type:"primary",onClick:Ve},{default:n(()=>e[45]||(e[45]=[V("确定")])),_:1}),a(y,{onClick:e[26]||(e[26]=s=>S.value=!1)},{default:n(()=>e[46]||(e[46]=[V("取消")])),_:1})])]),default:n(()=>[a(Ye,{model:o,"label-position":"top","label-width":"100px"},{default:n(()=>[c("div",lt,[e[42]||(e[42]=c("h3",{class:"form-section-title"},"基本信息",-1)),c("div",nt,[a(w,{label:"ID"},{default:n(()=>[a(h,{modelValue:o.id,"onUpdate:modelValue":e[17]||(e[17]=s=>o.id=s),disabled:""},null,8,["modelValue"])]),_:1}),a(w,{label:"通道名称",required:""},{default:n(()=>[a(h,{modelValue:o.name,"onUpdate:modelValue":e[18]||(e[18]=s=>o.name=s),placeholder:"请输入通道名称"},null,8,["modelValue"])]),_:1})]),c("div",ot,[a(w,{label:"通道标识",required:""},{default:n(()=>[a(h,{modelValue:o.code,"onUpdate:modelValue":e[19]||(e[19]=s=>o.code=s),placeholder:"请输入通道标识"},null,8,["modelValue"])]),_:1}),a(w,{label:"国家区号"},{default:n(()=>[a(h,{modelValue:o.countryCode,"onUpdate:modelValue":e[20]||(e[20]=s=>o.countryCode=s),placeholder:"请输入国家区号"},null,8,["modelValue"])]),_:1})])]),c("div",st,[e[43]||(e[43]=c("h3",{class:"form-section-title"},"状态设置",-1)),c("div",it,[a(w,{label:"存款开关"},{default:n(()=>[a(K,{modelValue:o.depositEnabled,"onUpdate:modelValue":e[21]||(e[21]=s=>o.depositEnabled=s),"active-value":1,"inactive-value":0,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1}),a(w,{label:"取款开关"},{default:n(()=>[a(K,{modelValue:o.withdrawEnabled,"onUpdate:modelValue":e[22]||(e[22]=s=>o.withdrawEnabled=s),"active-value":1,"inactive-value":0,"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),c("div",dt,[a(w,{label:"默认通道"},{default:n(()=>[a(K,{modelValue:o.isDefault,"onUpdate:modelValue":e[23]||(e[23]=s=>o.isDefault=s),"active-value":1,"inactive-value":0,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1}),a(w,{label:"权重"},{default:n(()=>[a(A,{modelValue:o.weight,"onUpdate:modelValue":e[24]||(e[24]=s=>o.weight=s),min:0,step:5,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])]),_:1})])]),c("div",rt,[e[44]||(e[44]=c("h3",{class:"form-section-title"},"其他信息",-1)),c("div",ut,[a(w,{label:"创建时间"},{default:n(()=>[a(h,{modelValue:o.createTime,"onUpdate:modelValue":e[25]||(e[25]=s=>o.createTime=s),disabled:""},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Ut=Te(ct,[["__scopeId","data-v-901529b4"]]);export{Ut as default};
