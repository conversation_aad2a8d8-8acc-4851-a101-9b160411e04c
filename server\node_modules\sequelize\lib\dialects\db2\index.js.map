{"version": 3, "sources": ["../../../src/dialects/db2/index.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst AbstractDialect = require('../abstract');\nconst ConnectionManager = require('./connection-manager');\nconst Query = require('./query');\nconst QueryGenerator = require('./query-generator');\nconst DataTypes = require('../../data-types').db2;\nconst { Db2QueryInterface } = require('./query-interface');\n\nclass Db2Dialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new ConnectionManager(this, sequelize);\n    this.queryGenerator = new QueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n    this.queryInterface = new Db2QueryInterface(sequelize, this.queryGenerator);\n  }\n}\n\nDb2Dialect.prototype.supports = _.merge(_.cloneDeep(AbstractDialect.prototype.supports), {\n  'DEFAULT': true,\n  'DEFAULT VALUES': false,\n  'VALUES ()': false,\n  'LIMIT ON UPDATE': false,\n  'ORDER NULLS': false,\n  lock: false,\n  transactions: true,\n  migrations: false,\n  returnValues: false,\n  schemas: true,\n  finalTable: true,\n  autoIncrement: {\n    identityInsert: false,\n    defaultValue: false,\n    update: true\n  },\n  constraints: {\n    restrict: true,\n    default: false\n  },\n  index: {\n    collate: false,\n    length: false,\n    parser: false,\n    type: false,\n    using: false,\n    where: true\n  },\n  NUMERIC: true,\n  tmpTableTrigger: true\n});\n\nDb2Dialect.prototype.defaultVersion = '1.0.0'; // Db2 supported version comes here\nDb2Dialect.prototype.Query = Query;\nDb2Dialect.prototype.name = 'db2';\nDb2Dialect.prototype.TICK_CHAR = '\"';\nDb2Dialect.prototype.TICK_CHAR_LEFT = '\"';\nDb2Dialect.prototype.TICK_CHAR_RIGHT = '\"';\nDb2Dialect.prototype.DataTypes = DataTypes;\n\nmodule.exports = Db2Dialect;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,oBAAoB,QAAQ;AAClC,MAAM,QAAQ,QAAQ;AACtB,MAAM,iBAAiB,QAAQ;AAC/B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,sBAAsB,QAAQ;AAEtC,yBAAyB,gBAAgB;AAAA,EACvC,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACrD,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,UAAU;AAAA,MACV;AAAA;AAEF,SAAK,iBAAiB,IAAI,kBAAkB,WAAW,KAAK;AAAA;AAAA;AAIhE,WAAW,UAAU,WAAW,EAAE,MAAM,EAAE,UAAU,gBAAgB,UAAU,WAAW;AAAA,EACvF,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,MAAM;AAAA,EACN,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,eAAe;AAAA,IACb,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,QAAQ;AAAA;AAAA,EAEV,aAAa;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA;AAAA,EAEX,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA;AAAA,EAET,SAAS;AAAA,EACT,iBAAiB;AAAA;AAGnB,WAAW,UAAU,iBAAiB;AACtC,WAAW,UAAU,QAAQ;AAC7B,WAAW,UAAU,OAAO;AAC5B,WAAW,UAAU,YAAY;AACjC,WAAW,UAAU,iBAAiB;AACtC,WAAW,UAAU,kBAAkB;AACvC,WAAW,UAAU,YAAY;AAEjC,OAAO,UAAU;", "names": []}