'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var af = require('./lang/af.js');
var arEg = require('./lang/ar-eg.js');
var ar = require('./lang/ar.js');
var az = require('./lang/az.js');
var bg = require('./lang/bg.js');
var bn = require('./lang/bn.js');
var ca = require('./lang/ca.js');
var ckb = require('./lang/ckb.js');
var cs = require('./lang/cs.js');
var da = require('./lang/da.js');
var de = require('./lang/de.js');
var el = require('./lang/el.js');
var en = require('./lang/en.js');
var eo = require('./lang/eo.js');
var es = require('./lang/es.js');
var et = require('./lang/et.js');
var eu = require('./lang/eu.js');
var fa = require('./lang/fa.js');
var fi = require('./lang/fi.js');
var fr = require('./lang/fr.js');
var he = require('./lang/he.js');
var hi = require('./lang/hi.js');
var hr = require('./lang/hr.js');
var hu = require('./lang/hu.js');
var hyAm = require('./lang/hy-am.js');
var id = require('./lang/id.js');
var it = require('./lang/it.js');
var ja = require('./lang/ja.js');
var kk = require('./lang/kk.js');
var km = require('./lang/km.js');
var ko = require('./lang/ko.js');
var ku = require('./lang/ku.js');
var ky = require('./lang/ky.js');
var lt = require('./lang/lt.js');
var lv = require('./lang/lv.js');
var mg = require('./lang/mg.js');
var mn = require('./lang/mn.js');
var ms = require('./lang/ms.js');
var my = require('./lang/my.js');
var nbNo = require('./lang/nb-no.js');
var nl = require('./lang/nl.js');
var no = require('./lang/no.js');
var pa = require('./lang/pa.js');
var pl = require('./lang/pl.js');
var ptBr = require('./lang/pt-br.js');
var pt = require('./lang/pt.js');
var ro = require('./lang/ro.js');
var ru = require('./lang/ru.js');
var sk = require('./lang/sk.js');
var sl = require('./lang/sl.js');
var sr = require('./lang/sr.js');
var sv = require('./lang/sv.js');
var sw = require('./lang/sw.js');
var ta = require('./lang/ta.js');
var te = require('./lang/te.js');
var th = require('./lang/th.js');
var tk = require('./lang/tk.js');
var tr = require('./lang/tr.js');
var ugCn = require('./lang/ug-cn.js');
var uk = require('./lang/uk.js');
var uzUz = require('./lang/uz-uz.js');
var vi = require('./lang/vi.js');
var zhCn = require('./lang/zh-cn.js');
var zhTw = require('./lang/zh-tw.js');
var zhHk = require('./lang/zh-hk.js');
var zhMo = require('./lang/zh-mo.js');



exports.af = af["default"];
exports.arEg = arEg["default"];
exports.ar = ar["default"];
exports.az = az["default"];
exports.bg = bg["default"];
exports.bn = bn["default"];
exports.ca = ca["default"];
exports.ckb = ckb["default"];
exports.cs = cs["default"];
exports.da = da["default"];
exports.de = de["default"];
exports.el = el["default"];
exports.en = en["default"];
exports.eo = eo["default"];
exports.es = es["default"];
exports.et = et["default"];
exports.eu = eu["default"];
exports.fa = fa["default"];
exports.fi = fi["default"];
exports.fr = fr["default"];
exports.he = he["default"];
exports.hi = hi["default"];
exports.hr = hr["default"];
exports.hu = hu["default"];
exports.hyAm = hyAm["default"];
exports.id = id["default"];
exports.it = it["default"];
exports.ja = ja["default"];
exports.kk = kk["default"];
exports.km = km["default"];
exports.ko = ko["default"];
exports.ku = ku["default"];
exports.ky = ky["default"];
exports.lt = lt["default"];
exports.lv = lv["default"];
exports.mg = mg["default"];
exports.mn = mn["default"];
exports.ms = ms["default"];
exports.my = my["default"];
exports.nbNo = nbNo["default"];
exports.nl = nl["default"];
exports.no = no["default"];
exports.pa = pa["default"];
exports.pl = pl["default"];
exports.ptBr = ptBr["default"];
exports.pt = pt["default"];
exports.ro = ro["default"];
exports.ru = ru["default"];
exports.sk = sk["default"];
exports.sl = sl["default"];
exports.sr = sr["default"];
exports.sv = sv["default"];
exports.sw = sw["default"];
exports.ta = ta["default"];
exports.te = te["default"];
exports.th = th["default"];
exports.tk = tk["default"];
exports.tr = tr["default"];
exports.ugCn = ugCn["default"];
exports.uk = uk["default"];
exports.uzUz = uzUz["default"];
exports.vi = vi["default"];
exports.zhCn = zhCn["default"];
exports.zhTw = zhTw["default"];
exports.zhHk = zhHk["default"];
exports.zhMo = zhMo["default"];
//# sourceMappingURL=index.js.map
