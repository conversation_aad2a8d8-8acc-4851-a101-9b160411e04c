{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/tree-select/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TreeSelect from './src/tree-select.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTreeSelect: SFCWithInstall<typeof TreeSelect> =\n  withInstall(TreeSelect)\n\nexport default ElTreeSelect\n"], "names": [], "mappings": ";;;AAEY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU;;;;"}