-- FOX投资平台数据库重构脚本
-- 作者：FOX开发团队
-- 日期：2025-06-04

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 备份表已经创建，跳过这一步

-- 创建新表结构

-- 1. 用户表 (users) - 优化版
DROP TABLE IF EXISTS users_new;
CREATE TABLE users_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    country_code VARCHAR(10) DEFAULT '+86',
    avatar VARCHAR(255) DEFAULT 'https://via.placeholder.com/30',
    invite_code VARCHAR(20) NOT NULL UNIQUE,
    inviter_id INT,
    level_id INT,  -- 关联到用户级别表
    gender ENUM('男', '女') DEFAULT '男',
    points INT DEFAULT 0,
    balance DECIMAL(15,2) DEFAULT 0.00,
    frozen_amount DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',  -- 合并status和allow_purchase
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_invite_code (invite_code),
    INDEX idx_inviter_id (inviter_id),
    INDEX idx_status (status)
);

-- 2. 账户余额表 (account_balances) - 新表
DROP TABLE IF EXISTS account_balances;
CREATE TABLE account_balances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    account_type ENUM('income', 'deposit') NOT NULL,
    currency ENUM('CNY', 'USDT') NOT NULL DEFAULT 'CNY',
    balance DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (user_id, account_type, currency),
    INDEX idx_user_id (user_id)
);

-- 3. 投资项目表 (projects) - 优化版
DROP TABLE IF EXISTS projects_new;
CREATE TABLE projects_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    category VARCHAR(50),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image_id INT,  -- 关联到附件表
    video_id INT,  -- 关联到附件表
    price DECIMAL(15,2) NOT NULL,
    min_investment DECIMAL(15,2) NOT NULL,
    max_investment DECIMAL(15,2) NOT NULL,
    duration INT NOT NULL,
    duration_unit VARCHAR(10) DEFAULT '天',
    expected_return DECIMAL(5,2) NOT NULL,
    profit_time INT NOT NULL,
    quantity INT DEFAULT 0,
    sold_quantity INT DEFAULT 0,
    vip_level_id INT,  -- 关联到用户级别表
    currency VARCHAR(10) DEFAULT 'CNY',
    status BOOLEAN DEFAULT TRUE,
    commission_enabled BOOLEAN DEFAULT FALSE,
    level1_commission DECIMAL(5,2) DEFAULT 0.00,
    level2_commission DECIMAL(5,2) DEFAULT 0.00,
    level3_commission DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_vip_level_id (vip_level_id)
);

-- 4. 交易表 (transactions) - 优化版
DROP TABLE IF EXISTS transactions_new;
CREATE TABLE transactions_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal', 'investment', 'profit', 'commission', 'bonus', 'deduction') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'CNY',
    status ENUM('pending', 'success', 'failed') DEFAULT 'pending',
    reference_id INT,  -- 关联到相关记录（如订单ID、投资ID等）
    reference_type VARCHAR(50),  -- 关联的表名（如'deposits', 'investments'等）
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_reference (reference_id, reference_type)
);

-- 5. 投资记录表 (investments) - 优化版
DROP TABLE IF EXISTS investments_new;
CREATE TABLE investments_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    quantity INT DEFAULT 1,
    profit_rate DECIMAL(5,2) NOT NULL,
    profit_cycle INT NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    profit_count INT DEFAULT 0,
    status ENUM('active', 'paused', 'completed') DEFAULT 'active',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 6. 投资收益表 (investment_profits) - 优化版
DROP TABLE IF EXISTS investment_profits_new;
CREATE TABLE investment_profits_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    investment_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    profit_time DATETIME NOT NULL,
    status ENUM('pending', 'paid') DEFAULT 'pending',
    transaction_id INT,  -- 关联到交易表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_investment_id (investment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_profit_time (profit_time),
    INDEX idx_status (status)
);

-- 7. 佣金记录表 (commissions) - 优化版
DROP TABLE IF EXISTS commissions_new;
CREATE TABLE commissions_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    from_user_id INT NOT NULL,
    investment_id INT NOT NULL,
    level INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    rate DECIMAL(5,2) NOT NULL,
    status ENUM('pending', 'paid') DEFAULT 'pending',
    transaction_id INT,  -- 关联到交易表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_from_user_id (from_user_id),
    INDEX idx_investment_id (investment_id),
    INDEX idx_status (status)
);

-- 8. 银行卡表 (bank_cards) - 新表，合并用户银行卡和收款银行卡
DROP TABLE IF EXISTS bank_cards;
CREATE TABLE bank_cards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,  -- NULL表示系统收款卡
    bank_name VARCHAR(100) NOT NULL,
    card_number VARCHAR(50) NOT NULL,
    card_holder VARCHAR(100) NOT NULL,
    branch VARCHAR(100),
    is_default BOOLEAN DEFAULT FALSE,
    card_type ENUM('user', 'system') NOT NULL,  -- 用户卡或系统收款卡
    daily_limit DECIMAL(15,2),  -- 仅对系统卡有效
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_card_type (card_type),
    INDEX idx_status (status)
);

-- 9. 充值订单表 (deposits) - 优化版
DROP TABLE IF EXISTS deposits_new;
CREATE TABLE deposits_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    actual_amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_account VARCHAR(100),
    receiving_card_id INT,
    transaction_id INT,  -- 关联到交易表
    status ENUM('pending', 'paid', 'cancelled', 'completed') DEFAULT 'pending',
    remark VARCHAR(255),
    payment_time DATETIME,
    completion_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_order_number (order_number),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 10. 提现记录表 (withdrawals) - 优化版
DROP TABLE IF EXISTS withdrawals_new;
CREATE TABLE withdrawals_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(15,2) NOT NULL,
    actual_amount DECIMAL(15,2) NOT NULL,
    bank_card_id INT NOT NULL,
    transaction_id INT,  -- 关联到交易表
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    remark VARCHAR(255),
    approval_time DATETIME,
    completion_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_order_number (order_number),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 11. 用户级别表 (user_levels) - 优化版
DROP TABLE IF EXISTS user_levels_new;
CREATE TABLE user_levels_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    level INT NOT NULL UNIQUE,
    upgrade_users INT DEFAULT 0,
    upgrade_amount DECIMAL(15,2) DEFAULT 0.00,
    return_rate DECIMAL(5,2) DEFAULT 0.00,
    upgrade_bonus DECIMAL(15,2) DEFAULT 0.00,
    image_id INT,  -- 关联到附件表
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_level (level)
);

-- 12. 附件表 (attachments) - 优化版
DROP TABLE IF EXISTS attachments_new;
CREATE TABLE attachments_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(50) DEFAULT '未归类',
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(20) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    metadata JSON,
    storage_engine VARCHAR(20) DEFAULT 'local',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_file_type (file_type)
);

-- 13. 系统参数表 (system_params) - 优化版
DROP TABLE IF EXISTS system_params_new;
CREATE TABLE system_params_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    param_key VARCHAR(100) NOT NULL UNIQUE,
    param_value TEXT,
    param_type VARCHAR(50) NOT NULL DEFAULT 'text',
    description VARCHAR(255),
    group_name VARCHAR(50) NOT NULL DEFAULT 'basic',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_param_key (param_key),
    INDEX idx_group_name (group_name)
);

-- 14. 轮播图表 (banners) - 优化版
DROP TABLE IF EXISTS banners_new;
CREATE TABLE banners_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100),
    attachment_id INT NOT NULL,  -- 关联到附件表
    url VARCHAR(255),
    position VARCHAR(50) DEFAULT 'home',
    sort_order INT DEFAULT 0,
    status BOOLEAN DEFAULT TRUE,
    start_time DATETIME,
    end_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_position (position),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
);

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
