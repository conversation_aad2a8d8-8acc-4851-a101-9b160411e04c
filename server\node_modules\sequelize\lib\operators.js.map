{"version": 3, "sources": ["../src/operators.ts"], "sourcesContent": ["interface OpTypes {\n  /**\n   * Operator -|- (PG range is adjacent to operator)\n   *\n   * ```js\n   * [Op.adjacent]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * -|- [1, 2)\n   * ```\n   */\n  readonly adjacent: unique symbol;\n  /**\n   * Operator ALL\n   *\n   * ```js\n   * [Op.gt]: {\n   *  [Op.all]: literal('SELECT 1')\n   * }\n   * ```\n   * In SQL\n   * ```sql\n   * > ALL (SELECT 1)\n   * ```\n   */\n  readonly all: unique symbol;\n  /**\n   * Operator AND\n   *\n   * ```js\n   * [Op.and]: {a: 5}\n   * ```\n   * In SQL\n   * ```sql\n   * AND (a = 5)\n   * ```\n   */\n  readonly and: unique symbol;\n  /**\n   * Operator ANY ARRAY (PG only)\n   *\n   * ```js\n   * [Op.any]: [2,3]\n   * ```\n   * In SQL\n   * ```sql\n   * ANY ARRAY[2, 3]::INTEGER\n   * ```\n   *\n   * Operator LIKE ANY ARRAY (also works for iLike and notLike)\n   *\n   * ```js\n   * [Op.like]: { [Op.any]: ['cat', 'hat']}\n   * ```\n   * In SQL\n   * ```sql\n   * LIKE ANY ARRAY['cat', 'hat']\n   * ```\n   */\n  readonly any: unique symbol;\n  /**\n   * Operator BETWEEN\n   *\n   * ```js\n   * [Op.between]: [6, 10]\n   * ```\n   * In SQL\n   * ```sql\n   * BETWEEN 6 AND 10\n   * ```\n   */\n  readonly between: unique symbol;\n  /**\n   * With dialect specific column identifiers (PG in this example)\n   *\n   * ```js\n   * [Op.col]: 'user.organization_id'\n   * ```\n   * In SQL\n   * ```sql\n   * = \"user\".\"organization_id\"\n   * ```\n   */\n  readonly col: unique symbol;\n  /**\n   * Operator <@ (PG array contained by operator)\n   *\n   * ```js\n   * [Op.contained]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * <@ [1, 2)\n   * ```\n   */\n  readonly contained: unique symbol;\n  /**\n   * Operator @> (PG array contains operator)\n   *\n   * ```js\n   * [Op.contains]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * @> [1, 2)\n   * ```\n   */\n  readonly contains: unique symbol;\n  /**\n   * Operator LIKE\n   *\n   * ```js\n   * [Op.endsWith]: 'hat'\n   * ```\n   * In SQL\n   * ```sql\n   * LIKE '%hat'\n   * ```\n   */\n  readonly endsWith: unique symbol;\n  /**\n   * Operator =\n   *\n   * ```js\n   * [Op.eq]: 3\n   * ```\n   * In SQL\n   * ```sql\n   * = 3\n   * ```\n   */\n  readonly eq: unique symbol;\n  /**\n   * Operator >\n   *\n   * ```js\n   * [Op.gt]: 6\n   * ```\n   * In SQL\n   * ```sql\n   * > 6\n   * ```\n   */\n  readonly gt: unique symbol;\n  /**\n   * Operator >=\n   *\n   * ```js\n   * [Op.gte]: 6\n   * ```\n   * In SQL\n   * ```sql\n   * >= 6\n   * ```\n   */\n  readonly gte: unique symbol;\n\n  /**\n   * Operator ILIKE (case insensitive) (PG only)\n   *\n   * ```js\n   * [Op.iLike]: '%hat'\n   * ```\n   * In SQL\n   * ```sql\n   * ILIKE '%hat'\n   * ```\n   */\n  readonly iLike: unique symbol;\n  /**\n   * Operator IN\n   *\n   * ```js\n   * [Op.in]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * IN [1, 2]\n   * ```\n   */\n  readonly in: unique symbol;\n  /**\n   * Operator ~* (PG only)\n   *\n   * ```js\n   * [Op.iRegexp]: '^[h|a|t]'\n   * ```\n   * In SQL\n   * ```sql\n   * ~* '^[h|a|t]'\n   * ```\n   */\n  readonly iRegexp: unique symbol;\n  /**\n   * Operator IS\n   *\n   * ```js\n   * [Op.is]: null\n   * ```\n   * In SQL\n   * ```sql\n   * IS null\n   * ```\n   */\n  readonly is: unique symbol;\n  /**\n   * Operator LIKE\n   *\n   * ```js\n   * [Op.like]: '%hat'\n   * ```\n   * In SQL\n   * ```sql\n   * LIKE '%hat'\n   * ```\n   */\n  readonly like: unique symbol;\n  /**\n   * Operator <\n   *\n   * ```js\n   * [Op.lt]: 10\n   * ```\n   * In SQL\n   * ```sql\n   * < 10\n   * ```\n   */\n  readonly lt: unique symbol;\n  /**\n   * Operator <=\n   *\n   * ```js\n   * [Op.lte]: 10\n   * ```\n   * In SQL\n   * ```sql\n   * <= 10\n   * ```\n   */\n  readonly lte: unique symbol;\n  /**\n   * Operator @@\n   *\n   * ```js\n   * [Op.match]: Sequelize.fn('to_tsquery', 'fat & rat')`\n   * ```\n   * In SQL\n   * ```sql\n   * @@ to_tsquery('fat & rat')\n   * ```\n   */\n  readonly match: unique symbol;\n  /**\n   * Operator !=\n   *\n   * ```js\n   * [Op.ne]: 20\n   * ```\n   * In SQL\n   * ```sql\n   * != 20\n   * ```\n   */\n  readonly ne: unique symbol;\n  /**\n   * Operator &> (PG range does not extend to the left of operator)\n   *\n   * ```js\n   * [Op.noExtendLeft]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * &> [1, 2)\n   * ```\n   */\n  readonly noExtendLeft: unique symbol;\n  /**\n   * Operator &< (PG range does not extend to the right of operator)\n   *\n   * ```js\n   * [Op.noExtendRight]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * &< [1, 2)\n   * ```\n   */\n  readonly noExtendRight: unique symbol;\n  /**\n   * Operator NOT\n   *\n   * ```js\n   * [Op.not]: true\n   * ```\n   * In SQL\n   * ```sql\n   * IS NOT TRUE\n   * ```\n   */\n  readonly not: unique symbol;\n  /**\n   * Operator NOT BETWEEN\n   *\n   * ```js\n   * [Op.notBetween]: [11, 15]\n   * ```\n   * In SQL\n   * ```sql\n   * NOT BETWEEN 11 AND 15\n   * ```\n   */\n  readonly notBetween: unique symbol;\n  /**\n   * Operator NOT ILIKE (case insensitive) (PG only)\n   *\n   * ```js\n   * [Op.notILike]: '%hat'\n   * ```\n   * In SQL\n   * ```sql\n   * NOT ILIKE '%hat'\n   * ```\n   */\n  readonly notILike: unique symbol;\n  /**\n   * Operator NOT IN\n   *\n   * ```js\n   * [Op.notIn]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * NOT IN [1, 2]\n   * ```\n   */\n  readonly notIn: unique symbol;\n  /**\n   * Operator !~* (PG only)\n   *\n   * ```js\n   * [Op.notIRegexp]: '^[h|a|t]'\n   * ```\n   * In SQL\n   * ```sql\n   * !~* '^[h|a|t]'\n   * ```\n   */\n  readonly notIRegexp: unique symbol;\n  /**\n   * Operator NOT LIKE\n   *\n   * ```js\n   * [Op.notLike]: '%hat'\n   * ```\n   * In SQL\n   * ```sql\n   * NOT LIKE '%hat'\n   * ```\n   */\n  readonly notLike: unique symbol;\n  /**\n   * Operator NOT REGEXP (MySQL/PG only)\n   *\n   * ```js\n   * [Op.notRegexp]: '^[h|a|t]'\n   * ```\n   * In SQL\n   * ```sql\n   * NOT REGEXP/!~ '^[h|a|t]'\n   * ```\n   */\n  readonly notRegexp: unique symbol;\n  /**\n   * Operator OR\n   *\n   * ```js\n   * [Op.or]: [{a: 5}, {a: 6}]\n   * ```\n   * In SQL\n   * ```sql\n   * (a = 5 OR a = 6)\n   * ```\n   */\n  readonly or: unique symbol;\n  /**\n   * Operator && (PG array overlap operator)\n   *\n   * ```js\n   * [Op.overlap]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * && [1, 2)\n   * ```\n   */\n  readonly overlap: unique symbol;\n  /**\n   * Internal placeholder\n   *\n   * ```js\n   * [Op.placeholder]: true\n   * ```\n   */\n  readonly placeholder: unique symbol;\n  /**\n   * Operator REGEXP (MySQL/PG only)\n   *\n   * ```js\n   * [Op.regexp]: '^[h|a|t]'\n   * ```\n   * In SQL\n   * ```sql\n   * REGEXP/~ '^[h|a|t]'\n   * ```\n   */\n  readonly regexp: unique symbol;\n  /**\n   * Operator LIKE\n   *\n   * ```js\n   * [Op.startsWith]: 'hat'\n   * ```\n   * In SQL\n   * ```sql\n   * LIKE 'hat%'\n   * ```\n   */\n  readonly startsWith: unique symbol;\n  /**\n   * Operator << (PG range strictly left of operator)\n   *\n   * ```js\n   * [Op.strictLeft]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * << [1, 2)\n   * ```\n   */\n  readonly strictLeft: unique symbol;\n  /**\n   * Operator >> (PG range strictly right of operator)\n   *\n   * ```js\n   * [Op.strictRight]: [1, 2]\n   * ```\n   * In SQL\n   * ```sql\n   * >> [1, 2)\n   * ```\n   */\n  readonly strictRight: unique symbol;\n  /**\n   * Operator LIKE\n   *\n   * ```js\n   * [Op.substring]: 'hat'\n   * ```\n   * In SQL\n   * ```sql\n   * LIKE '%hat%'\n   * ```\n   */\n  readonly substring: unique symbol;\n  /**\n   * Operator VALUES\n   *\n   * ```js\n   * [Op.values]: [4, 5, 6]\n   * ```\n   * In SQL\n   * ```sql\n   * VALUES (4), (5), (6)\n   * ```\n   */\n  readonly values: unique symbol;\n}\n\n// Note: These symbols are registered in the Global Symbol Registry\n//  to counter bugs when two different versions of this library are loaded\n//  Source issue: https://github.com/sequelize/sequelize/issues/8663\n// This is not an endorsement of having two different versions of the library loaded at the same time,\n//  a lot more is going to silently break if you do this.\nexport const Op: OpTypes = {\n  eq: Symbol.for('eq'),\n  ne: Symbol.for('ne'),\n  gte: Symbol.for('gte'),\n  gt: Symbol.for('gt'),\n  lte: Symbol.for('lte'),\n  lt: Symbol.for('lt'),\n  not: Symbol.for('not'),\n  is: Symbol.for('is'),\n  in: Symbol.for('in'),\n  notIn: Symbol.for('notIn'),\n  like: Symbol.for('like'),\n  notLike: Symbol.for('notLike'),\n  iLike: Symbol.for('iLike'),\n  notILike: Symbol.for('notILike'),\n  startsWith: Symbol.for('startsWith'),\n  endsWith: Symbol.for('endsWith'),\n  substring: Symbol.for('substring'),\n  regexp: Symbol.for('regexp'),\n  notRegexp: Symbol.for('notRegexp'),\n  iRegexp: Symbol.for('iRegexp'),\n  notIRegexp: Symbol.for('notIRegexp'),\n  between: Symbol.for('between'),\n  notBetween: Symbol.for('notBetween'),\n  overlap: Symbol.for('overlap'),\n  contains: Symbol.for('contains'),\n  contained: Symbol.for('contained'),\n  adjacent: Symbol.for('adjacent'),\n  strictLeft: Symbol.for('strictLeft'),\n  strictRight: Symbol.for('strictRight'),\n  noExtendRight: Symbol.for('noExtendRight'),\n  noExtendLeft: Symbol.for('noExtendLeft'),\n  and: Symbol.for('and'),\n  or: Symbol.for('or'),\n  any: Symbol.for('any'),\n  all: Symbol.for('all'),\n  values: Symbol.for('values'),\n  col: Symbol.for('col'),\n  placeholder: Symbol.for('placeholder'),\n  join: Symbol.for('join'),\n  match: Symbol.for('match')\n} as OpTypes;\n\nexport default Op;\n\n// https://github.com/sequelize/sequelize/issues/13791\n// remove me in v7: kept for backward compatibility as `export default Op` is\n// transpiled to `module.exports.default` instead of `module.exports`\nmodule.exports = Op;\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AAqeO,MAAM,KAAc;AAAA,EACzB,IAAI,OAAO,IAAI;AAAA,EACf,IAAI,OAAO,IAAI;AAAA,EACf,KAAK,OAAO,IAAI;AAAA,EAChB,IAAI,OAAO,IAAI;AAAA,EACf,KAAK,OAAO,IAAI;AAAA,EAChB,IAAI,OAAO,IAAI;AAAA,EACf,KAAK,OAAO,IAAI;AAAA,EAChB,IAAI,OAAO,IAAI;AAAA,EACf,IAAI,OAAO,IAAI;AAAA,EACf,OAAO,OAAO,IAAI;AAAA,EAClB,MAAM,OAAO,IAAI;AAAA,EACjB,SAAS,OAAO,IAAI;AAAA,EACpB,OAAO,OAAO,IAAI;AAAA,EAClB,UAAU,OAAO,IAAI;AAAA,EACrB,YAAY,OAAO,IAAI;AAAA,EACvB,UAAU,OAAO,IAAI;AAAA,EACrB,WAAW,OAAO,IAAI;AAAA,EACtB,QAAQ,OAAO,IAAI;AAAA,EACnB,WAAW,OAAO,IAAI;AAAA,EACtB,SAAS,OAAO,IAAI;AAAA,EACpB,YAAY,OAAO,IAAI;AAAA,EACvB,SAAS,OAAO,IAAI;AAAA,EACpB,YAAY,OAAO,IAAI;AAAA,EACvB,SAAS,OAAO,IAAI;AAAA,EACpB,UAAU,OAAO,IAAI;AAAA,EACrB,WAAW,OAAO,IAAI;AAAA,EACtB,UAAU,OAAO,IAAI;AAAA,EACrB,YAAY,OAAO,IAAI;AAAA,EACvB,aAAa,OAAO,IAAI;AAAA,EACxB,eAAe,OAAO,IAAI;AAAA,EAC1B,cAAc,OAAO,IAAI;AAAA,EACzB,KAAK,OAAO,IAAI;AAAA,EAChB,IAAI,OAAO,IAAI;AAAA,EACf,KAAK,OAAO,IAAI;AAAA,EAChB,KAAK,OAAO,IAAI;AAAA,EAChB,QAAQ,OAAO,IAAI;AAAA,EACnB,KAAK,OAAO,IAAI;AAAA,EAChB,aAAa,OAAO,IAAI;AAAA,EACxB,MAAM,OAAO,IAAI;AAAA,EACjB,OAAO,OAAO,IAAI;AAAA;AAGpB,IAAO,oBAAQ;AAKf,OAAO,UAAU;", "names": []}