<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收益补发管理 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .status-ok {
            color: #27ae60;
            font-weight: bold;
        }

        .status-error {
            color: #e74c3c;
            font-weight: bold;
        }

        .compensation-card {
            border-left: 4px solid #f39c12;
            background: #fff8e1;
        }

        .compensation-card h3 {
            color: #f39c12;
        }

        .alert-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }

        .alert-info p {
            margin-bottom: 8px;
        }

        .alert-info strong {
            color: #d68910;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal h3 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .system-status {
            margin-bottom: 20px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        .details-table td {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .details-table td:first-child {
            font-weight: 500;
            color: #666;
        }

        .details-table td:last-child {
            text-align: right;
        }

        .risk-warning {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }

        .risk-warning h4 {
            color: #e53e3e;
            margin-bottom: 10px;
        }

        .risk-warning ul {
            margin-left: 20px;
        }

        .risk-warning li {
            margin-bottom: 5px;
            color: #744210;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .progress-section {
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-weight: bold;
            color: #2c3e50;
        }

        .real-time-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .stat-item.success {
            background: #d4edda;
            color: #155724;
        }

        .stat-item.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .stat-item.amount {
            background: #d1ecf1;
            color: #0c5460;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>收益补发管理系统</h1>
            <p>管理员可以通过此页面监控系统状态并执行收益补发操作</p>
        </div>

        <!-- 仪表板 -->
        <div class="dashboard">
            <!-- 系统状态卡片 -->
            <div class="card">
                <h3>系统状态</h3>
                <div class="status-item">
                    <span>Redis状态:</span>
                    <span class="status-ok" id="redis-status">✅ 正常</span>
                </div>
                <div class="status-item">
                    <span>数据库状态:</span>
                    <span class="status-ok" id="db-status">✅ 正常</span>
                </div>
                <div class="status-item">
                    <span>最后发放时间:</span>
                    <span id="last-profit-time">2025-01-15 14:30:25</span>
                </div>
                <div class="status-item">
                    <span>系统负载:</span>
                    <span id="system-load">正常 (45%)</span>
                </div>
            </div>

            <!-- 补发提醒卡片 -->
            <div class="card compensation-card" id="compensation-alert">
                <h3>⚠️ 收益补发提醒</h3>
                <div class="alert-info">
                    <p>有 <strong id="pending-count">156</strong> 个投资需要补发收益</p>
                    <p>最早延迟时间: <strong id="oldest-delay">2小时15分钟</strong></p>
                    <p>预计补发金额: <strong id="estimated-amount">₱45,680.50</strong></p>
                    <p>影响用户数量: <strong id="affected-users">89</strong> 人</p>
                    <p style="color: #e74c3c; font-weight: bold;">⚠️ 检测到Redis已发放部分收益，请立即补发遗漏部分</p>
                </div>
                <button class="btn btn-primary" onclick="showCompensationModal()">
                    立即处理补发
                </button>
                <button class="btn btn-warning" onclick="showMissedDetails()" style="margin-left: 10px;">
                    查看遗漏详情
                </button>
            </div>

            <!-- 自动检测状态 -->
            <div class="card">
                <h3>自动检测状态</h3>
                <div class="status-item">
                    <span>上次检测时间:</span>
                    <span id="last-check-time">2025-01-15 14:25:00</span>
                </div>
                <div class="status-item">
                    <span>检测频率:</span>
                    <span>每小时自动检测</span>
                </div>
                <div class="status-item">
                    <span>遗漏收益检测:</span>
                    <span class="status-error">发现遗漏</span>
                </div>
                <button class="btn btn-secondary" onclick="manualCheck()" style="margin-top: 10px;">
                    手动检测
                </button>
            </div>

            <!-- 最近操作记录 -->
            <div class="card">
                <h3>最近补发记录</h3>
                <div class="status-item">
                    <span>2025-01-14 09:30</span>
                    <span class="status-ok">成功 (45笔)</span>
                </div>
                <div class="status-item">
                    <span>2025-01-12 15:20</span>
                    <span class="status-ok">成功 (12笔)</span>
                </div>
                <div class="status-item">
                    <span>2025-01-10 11:45</span>
                    <span class="status-error">部分失败 (23/25)</span>
                </div>
            </div>
        </div>

        <!-- 补发确认弹窗 -->
        <div class="modal" id="compensation-modal">
            <div class="modal-content">
                <h3>收益补发确认</h3>
                
                <!-- 系统状态检查 -->
                <div class="system-status">
                    <h4>系统状态检查</h4>
                    <div class="status-grid">
                        <div class="status-item">
                            <span>Redis状态:</span>
                            <span class="status-ok">✅ 正常</span>
                        </div>
                        <div class="status-item">
                            <span>数据库状态:</span>
                            <span class="status-ok">✅ 正常</span>
                        </div>
                    </div>
                </div>
                
                <!-- 补发详情 -->
                <div class="compensation-details">
                    <h4>补发详情</h4>
                    <table class="details-table">
                        <tr>
                            <td>影响用户数量:</td>
                            <td><strong>89</strong> 人</td>
                        </tr>
                        <tr>
                            <td>补发收益笔数:</td>
                            <td><strong>156</strong> 笔</td>
                        </tr>
                        <tr>
                            <td>预计补发金额:</td>
                            <td><strong>₱45,680.50</strong></td>
                        </tr>
                        <tr>
                            <td>预计执行时间:</td>
                            <td><strong>3-5</strong> 分钟</td>
                        </tr>
                    </table>
                </div>
                
                <!-- 风险提示 -->
                <div class="risk-warning">
                    <h4>⚠️ 重要提示</h4>
                    <ul>
                        <li>补发操作不可撤销，请确认数据无误</li>
                        <li>补发过程中请勿关闭页面或重启系统</li>
                        <li>大量补发可能影响系统性能，建议在低峰期执行</li>
                        <li>操作将被记录到系统日志中</li>
                    </ul>
                </div>
                
                <!-- 操作按钮 -->
                <div class="modal-actions">
                    <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button class="btn btn-danger" onclick="executeCompensation()">
                        确认执行补发
                    </button>
                </div>
            </div>
        </div>

        <!-- 补发进度弹窗 -->
        <div class="modal" id="progress-modal">
            <div class="modal-content">
                <h3>正在执行收益补发...</h3>
                
                <!-- 总体进度 -->
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text" id="progress-text">0 / 156 (0%)</div>
                </div>
                
                <!-- 批次进度 -->
                <div style="text-align: center; margin: 20px 0;">
                    <p>当前批次: <span id="current-batch">1</span> / <span id="total-batches">4</span></p>
                    <p>预计剩余时间: <span id="estimated-time">4</span> 分钟</p>
                </div>
                
                <!-- 实时统计 -->
                <div class="real-time-stats">
                    <div class="stat-item success">
                        <div>成功</div>
                        <div><strong id="success-count">0</strong></div>
                    </div>
                    <div class="stat-item failed">
                        <div>失败</div>
                        <div><strong id="failed-count">0</strong></div>
                    </div>
                    <div class="stat-item amount">
                        <div>已发放</div>
                        <div><strong id="distributed-amount">₱0</strong></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 补发完成弹窗 -->
        <div class="modal" id="result-modal">
            <div class="modal-content">
                <h3>✅ 收益补发完成</h3>
                
                <!-- 执行概要 -->
                <div class="compensation-details">
                    <h4>执行概要</h4>
                    <table class="details-table">
                        <tr>
                            <td>执行时间:</td>
                            <td><strong id="execution-time">4.2</strong> 分钟</td>
                        </tr>
                        <tr>
                            <td>处理投资:</td>
                            <td><strong>156</strong> 个</td>
                        </tr>
                        <tr>
                            <td>成功补发:</td>
                            <td><strong id="final-success">154</strong> 笔</td>
                        </tr>
                        <tr>
                            <td>失败数量:</td>
                            <td><strong id="final-failed">2</strong> 笔</td>
                        </tr>
                        <tr>
                            <td>发放金额:</td>
                            <td><strong id="final-amount">₱45,234.80</strong></td>
                        </tr>
                    </table>
                </div>
                
                <!-- 用户通知状态 -->
                <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin: 20px 0;">
                    <h4 style="color: #155724;">用户通知</h4>
                    <p style="color: #155724;">已向 89 名用户发送补发通知</p>
                </div>
                
                <!-- 操作按钮 -->
                <div class="modal-actions">
                    <button class="btn btn-secondary" onclick="closeResultModal()">关闭</button>
                    <button class="btn btn-warning" onclick="showFailedDetails()">查看失败详情</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        let compensationData = {
            pendingCount: 156,
            affectedUsers: 89,
            estimatedAmount: 45680.50,
            oldestDelay: "2小时15分钟"
        };

        // 显示补发确认弹窗
        function showCompensationModal() {
            document.getElementById('compensation-modal').classList.add('show');
        }

        // 关闭弹窗
        function closeModal() {
            document.getElementById('compensation-modal').classList.remove('show');
        }

        // 执行补发
        function executeCompensation() {
            closeModal();
            document.getElementById('progress-modal').classList.add('show');
            
            // 模拟补发进度
            simulateProgress();
        }

        // 模拟补发进度
        function simulateProgress() {
            let processed = 0;
            let successful = 0;
            let failed = 0;
            let distributedAmount = 0;
            const total = 156;
            const batchSize = 40;
            
            const interval = setInterval(() => {
                // 更新进度
                const increment = Math.min(batchSize, total - processed);
                processed += increment;
                successful += Math.floor(increment * 0.98); // 98%成功率
                failed += increment - Math.floor(increment * 0.98);
                distributedAmount += increment * 290; // 平均每笔290
                
                const progress = Math.round((processed / total) * 100);
                
                // 更新界面
                document.getElementById('progress-fill').style.width = progress + '%';
                document.getElementById('progress-text').textContent = `${processed} / ${total} (${progress}%)`;
                document.getElementById('success-count').textContent = successful;
                document.getElementById('failed-count').textContent = failed;
                document.getElementById('distributed-amount').textContent = `₱${distributedAmount.toLocaleString()}`;
                
                // 更新批次信息
                const currentBatch = Math.ceil(processed / batchSize);
                const totalBatches = Math.ceil(total / batchSize);
                document.getElementById('current-batch').textContent = currentBatch;
                document.getElementById('total-batches').textContent = totalBatches;
                
                const remainingTime = Math.max(0, Math.ceil((total - processed) / batchSize));
                document.getElementById('estimated-time').textContent = remainingTime;
                
                // 完成时显示结果
                if (processed >= total) {
                    clearInterval(interval);
                    setTimeout(() => {
                        document.getElementById('progress-modal').classList.remove('show');
                        showResultModal(successful, failed, distributedAmount);
                    }, 1000);
                }
            }, 800);
        }

        // 显示结果弹窗
        function showResultModal(successful, failed, amount) {
            document.getElementById('final-success').textContent = successful;
            document.getElementById('final-failed').textContent = failed;
            document.getElementById('final-amount').textContent = `₱${amount.toLocaleString()}`;
            document.getElementById('result-modal').classList.add('show');
            
            // 更新仪表板数据
            updateDashboard();
        }

        // 关闭结果弹窗
        function closeResultModal() {
            document.getElementById('result-modal').classList.remove('show');
        }

        // 更新仪表板
        function updateDashboard() {
            document.getElementById('pending-count').textContent = '0';
            document.getElementById('compensation-alert').style.display = 'none';
        }

        // 显示失败详情
        function showFailedDetails() {
            alert('失败详情：\n1. 投资ID 12345 - 用户账户异常\n2. 投资ID 67890 - 数据库连接超时');
        }

        // 显示遗漏详情
        function showMissedDetails() {
            const details = `遗漏收益详情：

系统检测到以下情况：
• Redis已在系统恢复后发放了部分正常收益
• 但仍有历史收益未补发

具体情况：
1. 投资ID 10001 - 用户张三
   - 遗漏：2025-06-09 21:09 (第1笔)
   - 遗漏：2025-06-10 21:09 (第2笔)
   - 已发放：2025-06-11 21:09 (第3笔，Redis正常发放)

2. 投资ID 10002 - 用户李四
   - 遗漏：2025-06-10 15:30 (第1笔)
   - 已发放：2025-06-11 15:30 (第2笔，Redis正常发放)

建议：立即执行智能补发，系统会自动跳过已发放的收益`;

            alert(details);
        }

        // 手动检测
        function manualCheck() {
            document.getElementById('last-check-time').textContent = new Date().toLocaleString('zh-CN');
            alert('手动检测完成！\n\n检测结果：\n• 发现 23 个新的遗漏收益\n• 更新了补发队列\n• 建议立即执行补发操作');
        }

        // 模拟系统状态更新
        setInterval(() => {
            const now = new Date();
            document.getElementById('last-profit-time').textContent = 
                now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
        }, 30000);
    </script>
</body>
</html>
