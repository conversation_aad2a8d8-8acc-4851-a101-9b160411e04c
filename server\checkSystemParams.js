/**
 * 检查系统参数
 */
const { SystemParam } = require('./models');
const sequelize = require('./config/database');
const { Op } = require('sequelize');

async function checkSystemParams() {
  try {
    // 检查连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 查询佣金相关的系统参数
    const commissionParams = await SystemParam.findAll({
      where: {
        param_key: {
          [Op.or]: [
            { [Op.like]: '%commission%' },
            { [Op.like]: '%profit%' }
          ]
        }
      }
    });
    
    console.log('佣金相关的系统参数:');
    commissionParams.forEach(param => {
      console.log(`键: ${param.param_key}`);
      console.log(`值: ${param.param_value}`);
      console.log(`描述: ${param.description || '无描述'}`);
      console.log('---');
    });
    
    process.exit(0);
  } catch (error) {
    console.error('检查系统参数失败:', error);
    process.exit(1);
  }
}

checkSystemParams();
