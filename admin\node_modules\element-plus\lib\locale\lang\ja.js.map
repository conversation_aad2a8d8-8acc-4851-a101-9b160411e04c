{"version": 3, "file": "ja.js", "sources": ["../../../../../packages/locale/lang/ja.ts"], "sourcesContent": ["export default {\n  name: 'ja',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'クリア',\n    },\n    datepicker: {\n      now: '現在',\n      today: '今日',\n      cancel: 'キャンセル',\n      clear: 'クリア',\n      confirm: 'OK',\n      selectDate: '日付を選択',\n      selectTime: '時間を選択',\n      startDate: '開始日',\n      startTime: '開始時間',\n      endDate: '終了日',\n      endTime: '終了時間',\n      prevYear: '前年',\n      nextYear: '翌年',\n      prevMonth: '前月',\n      nextMonth: '翌月',\n      year: '年',\n      month1: '1月',\n      month2: '2月',\n      month3: '3月',\n      month4: '4月',\n      month5: '5月',\n      month6: '6月',\n      month7: '7月',\n      month8: '8月',\n      month9: '9月',\n      month10: '10月',\n      month11: '11月',\n      month12: '12月',\n      // week: '週次',\n      weeks: {\n        sun: '日',\n        mon: '月',\n        tue: '火',\n        wed: '水',\n        thu: '木',\n        fri: '金',\n        sat: '土',\n      },\n      months: {\n        jan: '1月',\n        feb: '2月',\n        mar: '3月',\n        apr: '4月',\n        may: '5月',\n        jun: '6月',\n        jul: '7月',\n        aug: '8月',\n        sep: '9月',\n        oct: '10月',\n        nov: '11月',\n        dec: '12月',\n      },\n    },\n    select: {\n      loading: 'ロード中',\n      noMatch: 'データなし',\n      noData: 'データなし',\n      placeholder: '選択してください',\n    },\n    mention: {\n      loading: 'ロード中',\n    },\n    cascader: {\n      noMatch: 'データなし',\n      loading: 'ロード中',\n      placeholder: '選択してください',\n      noData: 'データなし',\n    },\n    pagination: {\n      goto: '',\n      pagesize: '件/ページ',\n      total: '総計 {total} 件',\n      pageClassifier: 'ページ目へ',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'メッセージ',\n      confirm: 'OK',\n      cancel: 'キャンセル',\n      error: '正しくない入力',\n    },\n    upload: {\n      deleteTip: 'Delキーを押して削除する',\n      delete: '削除する',\n      preview: 'プレビュー',\n      continue: '続行する',\n    },\n    table: {\n      emptyText: 'データなし',\n      confirmFilter: '確認',\n      resetFilter: '初期化',\n      clearFilter: 'すべて',\n      sumText: '合計',\n    },\n    tour: {\n      next: '次へ',\n      previous: '前へ',\n      finish: 'ツアー終了',\n    },\n    tree: {\n      emptyText: 'データなし',\n    },\n    transfer: {\n      noMatch: 'データなし',\n      noData: 'データなし',\n      titles: ['リスト 1', 'リスト 2'],\n      filterPlaceholder: 'キーワードを入力',\n      noCheckedFormat: '総計 {total} 件',\n      hasCheckedFormat: '{checked}/{total} を選択した',\n    },\n    image: {\n      error: '失敗',\n    },\n    pageHeader: {\n      title: '戻る',\n    },\n    popconfirm: {\n      confirmButtonText: 'はい',\n      cancelButtonText: 'いいえ',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,cAAc;AACzB,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,gCAAgC;AAClD,MAAM,UAAU,EAAE,gCAAgC;AAClD,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,WAAW,EAAE,kDAAkD;AACrE,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,0BAA0B;AACzC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,WAAW,EAAE,kDAAkD;AACrE,MAAM,MAAM,EAAE,gCAAgC;AAC9C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,KAAK,EAAE,6BAA6B;AAC1C,MAAM,cAAc,EAAE,gCAAgC;AACtD,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,iEAAiE;AAClF,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gCAAgC;AACjD,MAAM,aAAa,EAAE,cAAc;AACnC,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,cAAc;AAC7B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,MAAM,EAAE,gCAAgC;AAC9C,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gCAAgC;AACjD,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;AAC9D,MAAM,iBAAiB,EAAE,kDAAkD;AAC3E,MAAM,eAAe,EAAE,6BAA6B;AACpD,MAAM,gBAAgB,EAAE,kDAAkD;AAC1E,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,cAAc;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,cAAc;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,cAAc;AACvC,MAAM,gBAAgB,EAAE,oBAAoB;AAC5C,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}