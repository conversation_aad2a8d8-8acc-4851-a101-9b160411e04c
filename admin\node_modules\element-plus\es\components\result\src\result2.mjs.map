{"version": 3, "file": "result2.mjs", "sources": ["../../../../../../packages/components/result/src/result.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport {\n  CircleCheckFilled,\n  CircleCloseFilled,\n  InfoFilled,\n  WarningFilled,\n} from '@element-plus/icons-vue'\nimport type { Component, ExtractPropTypes } from 'vue'\nimport type Result from './result.vue'\n\nexport const IconMap = {\n  success: 'icon-success',\n  warning: 'icon-warning',\n  error: 'icon-error',\n  info: 'icon-info',\n} as const\n\nexport const IconComponentMap: Record<\n  typeof IconMap[keyof typeof IconMap],\n  Component\n> = {\n  [IconMap.success]: CircleCheckFilled,\n  [IconMap.warning]: WarningFilled,\n  [IconMap.error]: CircleCloseFilled,\n  [IconMap.info]: InfoFilled,\n}\n\nexport const resultProps = buildProps({\n  /**\n   * @description title of result\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description sub title of result\n   */\n  subTitle: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description icon type of result\n   */\n  icon: {\n    type: String,\n    values: ['success', 'warning', 'info', 'error'],\n    default: 'info',\n  },\n} as const)\n\nexport type ResultProps = ExtractPropTypes<typeof resultProps>\n\nexport type ResultInstance = InstanceType<typeof Result> & unknown\n"], "names": [], "mappings": ";;;AAOY,MAAC,OAAO,GAAG;AACvB,EAAE,OAAO,EAAE,cAAc;AACzB,EAAE,OAAO,EAAE,cAAc;AACzB,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE;AACU,MAAC,gBAAgB,GAAG;AAChC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,iBAAiB;AACtC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,aAAa;AAClC,EAAE,CAAC,OAAO,CAAC,KAAK,GAAG,iBAAiB;AACpC,EAAE,CAAC,OAAO,CAAC,IAAI,GAAG,UAAU;AAC5B,EAAE;AACU,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACnD,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}