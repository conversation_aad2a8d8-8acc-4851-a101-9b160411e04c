/* empty css             *//* empty css                  *//* empty css                     */import"./el-tooltip-l0sNRNKZ.js";import{d as z,r as C,q as E,s as F,o as O,c as X,b as s,e,w as l,t as A,v as m,x as G,y as B,z as H,A as J,m as K,B as L,j as n,C as Q,n as u,k as $,D as W,F as M,G as Y,H as Z,I,J as h,K as tt,L as S,M as et,N as lt,O as st,P as nt,Q as ot,R as at,S as ut,T as dt,U as it,l as rt,V as D,W as mt,X as ft,Y as pt,Z as _t,$ as vt,u as ct,g as c,a0 as N,_ as gt}from"./index-LncY9lAB.js";import{u as xt}from"./user-DIT1kost.js";import"./request-Cd-6Wde0.js";import"./index-t--hEgTQ.js";const bt={class:"app-container"},wt={class:"custom-menu-item disabled"},kt={class:"custom-menu-item disabled"},yt={class:"custom-menu-item disabled"},Ct={class:"custom-menu-item disabled"},Et={class:"main-content"},Bt={class:"header"},$t={class:"header-left"},Mt={class:"header-right"},It={class:"user-info"},St={class:"page-content"},Dt={class:"test-buttons"},Nt=z({__name:"index",setup(Tt){const g=F(),_=ct(),v=xt(),p=C(!1),x=C("管理员"),i=E(()=>g.path),T=E(()=>g.meta.title||"首页"),V=()=>{p.value=!p.value},j=a=>{a==="logout"?N.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{v.logout()}).catch(()=>{}):a==="changePassword"&&(_.push("/dashboard"),N.alert("该功能尚未实现","提示"))},f=a=>{try{_.push(a).catch(()=>{_.replace(a).catch(()=>{window.location.href=a})})}catch{}},b=a=>{console.log("直接导航到:",a),window.location.href=a};return O(()=>{v.token&&v.fetchUserInfo().then(a=>{a&&(x.value=a.username)}).catch(()=>{})}),(a,t)=>{const o=G,d=L,w=h,P=A,k=vt,R=_t,U=H,q=J("router-view"),y=K;return c(),X("div",bt,[s("div",{class:m(["sidebar",{"is-collapsed":p.value}])},[t[31]||(t[31]=s("div",{class:"sidebar-logo"},[s("h1",null,"FOX")],-1)),e(P,{"default-active":i.value,class:"sidebar-menu",collapse:p.value,"background-color":"#001529","text-color":"rgba(255, 255, 255, 0.65)","active-text-color":"#fff","unique-opened":!0,router:""},{default:l(()=>[e(d,{index:"/dashboard"},{title:l(()=>t[8]||(t[8]=[u("首页")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(Q))]),_:1})]),_:1}),e(d,{index:"/members"},{title:l(()=>t[9]||(t[9]=[u("会员列表")])),default:l(()=>[e(o,null,{default:l(()=>[e(n($))]),_:1})]),_:1}),e(d,{index:"/deposits"},{title:l(()=>t[10]||(t[10]=[u("充值订单")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(W))]),_:1})]),_:1}),e(d,{index:"/investments"},{title:l(()=>t[11]||(t[11]=[u("用户投资")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(M))]),_:1})]),_:1}),e(d,{index:"/withdrawals"},{title:l(()=>t[12]||(t[12]=[u("取款记录")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(M))]),_:1})]),_:1}),e(d,{index:"/transactions"},{title:l(()=>t[13]||(t[13]=[u("用户流水")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(Y))]),_:1})]),_:1}),e(d,{index:"/commissions"},{title:l(()=>t[14]||(t[14]=[u("佣金记录")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(Z))]),_:1})]),_:1}),e(d,{index:"/user-cards"},{title:l(()=>t[15]||(t[15]=[u("用户银行卡")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(I))]),_:1})]),_:1}),e(d,{index:"/bank-cards",disabled:""},{title:l(()=>t[16]||(t[16]=[u("收款银行卡")])),default:l(()=>[e(o,null,{default:l(()=>[e(n(I))]),_:1})]),_:1}),e(w,{index:"system"},{title:l(()=>[e(o,null,{default:l(()=>[e(n(S))]),_:1}),t[17]||(t[17]=s("span",null,"系统设置",-1))]),default:l(()=>[s("div",{class:m(["custom-menu-item",{"is-active":i.value==="/projects"}]),onClick:t[0]||(t[0]=r=>f("/projects"))},[e(o,null,{default:l(()=>[e(n(tt))]),_:1}),t[18]||(t[18]=s("span",null,"投资项目",-1))],2),s("div",{class:m(["custom-menu-item",{"is-active":i.value==="/system-params"}]),onClick:t[1]||(t[1]=r=>f("/system-params"))},[e(o,null,{default:l(()=>[e(n(S))]),_:1}),t[19]||(t[19]=s("span",null,"参数设置",-1))],2),s("div",{class:m(["custom-menu-item",{"is-active":i.value==="/attachments"}]),onClick:t[2]||(t[2]=r=>f("/attachments"))},[e(o,null,{default:l(()=>[e(n(et))]),_:1}),t[20]||(t[20]=s("span",null,"附件管理",-1))],2),s("div",wt,[e(o,null,{default:l(()=>[e(n(lt))]),_:1}),t[21]||(t[21]=s("span",null,"个人资料",-1))]),s("div",kt,[e(o,null,{default:l(()=>[e(n(st))]),_:1}),t[22]||(t[22]=s("span",null,"用户级别",-1))]),s("div",{class:m(["custom-menu-item",{"is-active":i.value==="/payment-channels"}]),onClick:t[3]||(t[3]=r=>f("/payment-channels"))},[e(o,null,{default:l(()=>[e(n(nt))]),_:1}),t[23]||(t[23]=s("span",null,"支付通道",-1))],2),s("div",yt,[e(o,null,{default:l(()=>[e(n(ot))]),_:1}),t[24]||(t[24]=s("span",null,"邀请奖励",-1))]),s("div",{class:m(["custom-menu-item",{"is-active":i.value==="/customer-service"}]),onClick:t[4]||(t[4]=r=>f("/customer-service"))},[e(o,null,{default:l(()=>[e(n(at))]),_:1}),t[25]||(t[25]=s("span",null,"客服管理",-1))],2),s("div",Ct,[e(o,null,{default:l(()=>[e(n(ut))]),_:1}),t[26]||(t[26]=s("span",null,"谷歌验证码",-1))]),s("div",{class:m(["custom-menu-item",{"is-active":i.value==="/banners"}]),onClick:t[5]||(t[5]=r=>f("/banners"))},[e(o,null,{default:l(()=>[e(n(dt))]),_:1}),t[27]||(t[27]=s("span",null,"轮播图",-1))],2)]),_:1}),e(w,{index:"permission"},{title:l(()=>[e(o,null,{default:l(()=>[e(n(rt))]),_:1}),t[28]||(t[28]=s("span",null,"权限管理",-1))]),default:l(()=>[e(d,{index:"/admins"},{default:l(()=>[e(o,null,{default:l(()=>[e(n($))]),_:1}),t[29]||(t[29]=s("span",null,"管理员设置",-1))]),_:1}),e(d,{index:"/roles"},{default:l(()=>[e(o,null,{default:l(()=>[e(n(it))]),_:1}),t[30]||(t[30]=s("span",null,"角色组",-1))]),_:1})]),_:1})]),_:1},8,["default-active","collapse"])],2),s("div",Et,[s("div",Bt,[s("div",$t,[e(o,{class:"toggle-sidebar",onClick:V},{default:l(()=>[p.value?(c(),D(n(mt),{key:0})):(c(),D(n(ft),{key:1}))]),_:1}),s("h2",null,B(T.value),1)]),s("div",Mt,[e(U,{trigger:"click",onCommand:j},{dropdown:l(()=>[e(R,null,{default:l(()=>[e(k,{command:"changePassword"},{default:l(()=>t[32]||(t[32]=[u("修改密码")])),_:1}),e(k,{divided:"",command:"logout"},{default:l(()=>t[33]||(t[33]=[u("退出登录")])),_:1})]),_:1})]),default:l(()=>[s("div",It,[s("span",null,B(x.value),1),e(o,{class:"el-icon--right"},{default:l(()=>[e(n(pt))]),_:1})])]),_:1})])]),s("div",St,[e(q)])]),s("div",Dt,[e(y,{type:"primary",onClick:t[6]||(t[6]=r=>b("/test-params"))},{default:l(()=>t[34]||(t[34]=[u("测试参数页面")])),_:1}),e(y,{type:"success",onClick:t[7]||(t[7]=r=>b("/system-params"))},{default:l(()=>t[35]||(t[35]=[u("系统参数页面")])),_:1})])])}}}),Ot=gt(Nt,[["__scopeId","data-v-6d19b3bf"]]);export{Ot as default};
