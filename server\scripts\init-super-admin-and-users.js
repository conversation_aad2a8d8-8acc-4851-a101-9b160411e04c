require('dotenv').config();
const bcrypt = require('bcryptjs');
const argon2 = require('argon2');
const crypto = require('crypto');
const sequelize = require('../config/database');
const {
  Admin, Role, Permission, RolePermission, AdminRole,
  User, UserLevel, AccountBalance, InviteCode, UserRelation
} = require('../models');

/**
 * 创建超级管理员账号和初始用户的脚本
 * 
 * 超级管理员关系映射：
 * - Admin: 管理员基本信息
 * - Role: 角色系统
 * - Permission: 权限系统  
 * - AdminRole: 管理员-角色关联
 * - RolePermission: 角色-权限关联
 * 
 * 初始用户关系映射：
 * - User: 用户基本信息（包含user_id、邀请码等）
 * - UserLevel: 用户等级系统
 * - AccountBalance: 用户账户余额（收入账户、充值账户）
 * - InviteCode: 邀请码管理
 * - UserRelation: 用户关系链
 */

// 生成安全的密码哈希
async function hashPassword(password) {
  try {
    // 优先使用Argon2
    return await argon2.hash(password, {
      type: argon2.argon2id,
      memoryCost: 2**16, // 64MB
      timeCost: 3,
      parallelism: 1
    });
  } catch (error) {
    console.warn('使用Argon2加密失败，回退到bcrypt:', error);
    return await bcrypt.hash(password, 12);
  }
}

// 生成唯一邀请码
async function generateUniqueInviteCode() {
  let code;
  let exists = true;
  
  while (exists) {
    code = crypto.randomBytes(3).toString('hex').toUpperCase();
    const existingCode = await InviteCode.findOne({ where: { code } });
    exists = !!existingCode;
  }
  
  return code;
}

// 创建基础权限数据
async function createPermissions() {
  console.log('正在创建基础权限...');
  
  const permissions = [
    // 首页
    { name: '首页查看', code: 'dashboard:view', type: 'menu' },
    
    // 会员管理
    { name: '会员列表查看', code: 'members:view', type: 'menu' },
    { name: '会员详情查看', code: 'members:detail', type: 'operation' },
    { name: '会员编辑', code: 'members:edit', type: 'operation' },
    { name: '会员删除', code: 'members:delete', type: 'operation' },
    
    // 充值订单
    { name: '充值订单查看', code: 'deposits:view', type: 'menu' },
    { name: '充值订单审核', code: 'deposits:audit', type: 'operation' },
    
    // 提现订单
    { name: '提现订单查看', code: 'withdrawals:view', type: 'menu' },
    { name: '提现订单审核', code: 'withdrawals:audit', type: 'operation' },
    
    // 项目管理
    { name: '项目列表查看', code: 'projects:view', type: 'menu' },
    { name: '项目创建', code: 'projects:create', type: 'operation' },
    { name: '项目编辑', code: 'projects:edit', type: 'operation' },
    { name: '项目删除', code: 'projects:delete', type: 'operation' },
    
    // 投资订单
    { name: '投资订单查看', code: 'investments:view', type: 'menu' },
    
    // 系统设置
    { name: '系统参数设置', code: 'system:params', type: 'menu' },
    { name: '支付通道管理', code: 'system:payment', type: 'menu' },
    { name: '银行管理', code: 'system:banks', type: 'menu' },
    
    // 角色权限管理
    { name: '角色管理', code: 'roles:manage', type: 'menu' },
    { name: '管理员管理', code: 'admins:manage', type: 'menu' },
  ];
  
  for (const permData of permissions) {
    await Permission.findOrCreate({
      where: { code: permData.code },
      defaults: permData
    });
  }
  
  console.log('基础权限创建完成');
}

// 创建角色
async function createRoles() {
  console.log('正在创建角色...');
  
  // 创建超级管理员角色
  const [superAdminRole] = await Role.findOrCreate({
    where: { name: '超级管理员组' },
    defaults: {
      name: '超级管理员组',
      description: '拥有所有权限的超级管理员角色',
      status: true
    }
  });
  
  // 创建普通管理员角色
  const [adminRole] = await Role.findOrCreate({
    where: { name: '管理员组' },
    defaults: {
      name: '管理员组', 
      description: '普通管理员角色',
      status: true
    }
  });
  
  // 为超级管理员角色分配所有权限
  const allPermissions = await Permission.findAll();
  await superAdminRole.setPermissions(allPermissions);
  
  // 为普通管理员角色分配除角色管理和管理员管理外的权限
  const adminPermissions = allPermissions.filter(p => 
    !['roles:manage', 'admins:manage'].includes(p.code)
  );
  await adminRole.setPermissions(adminPermissions);
  
  console.log('角色创建完成');
  return { superAdminRole, adminRole };
}

// 创建超级管理员账号
async function createSuperAdmin(superAdminRole) {
  console.log('正在创建超级管理员账号...');

  const adminPassword = await hashPassword('xt00159258');

  const [admin, created] = await Admin.findOrCreate({
    where: { username: 'task0012' },
    defaults: {
      username: 'task0012',
      password: adminPassword,
      nickname: '超级管理员',
      is_super: true,
      status: true
    }
  });
  
  if (created) {
    console.log('超级管理员账号创建成功');
  } else {
    console.log('超级管理员账号已存在');
  }
  
  // 关联超级管理员角色
  await admin.setRoles([superAdminRole]);
  
  console.log('超级管理员角色关联完成');
  return admin;
}

// 创建用户等级
async function createUserLevels() {
  console.log('正在创建用户等级...');
  
  const levels = [
    {
      name: 'VIP1',
      level: 1,
      upgrade_users: 0,
      upgrade_amount: 0,
      return_rate: 0.00,
      upgrade_bonus: 0,
      content: '初级用户'
    },
    {
      name: 'VIP2', 
      level: 2,
      upgrade_users: 5,
      upgrade_amount: 10000,
      return_rate: 0.50,
      upgrade_bonus: 100,
      content: '中级用户'
    },
    {
      name: 'VIP3',
      level: 3, 
      upgrade_users: 10,
      upgrade_amount: 50000,
      return_rate: 1.00,
      upgrade_bonus: 500,
      content: '高级用户'
    }
  ];
  
  for (const levelData of levels) {
    await UserLevel.findOrCreate({
      where: { level: levelData.level },
      defaults: levelData
    });
  }
  
  console.log('用户等级创建完成');
}

// 创建初始用户
async function createInitialUsers() {
  console.log('正在创建初始用户...');

  const transaction = await sequelize.transaction();

  try {
    // 获取VIP1等级
    const vip1Level = await UserLevel.findOne({ where: { level: 1 } });

    // 创建初始用户（提供邀请码给其他用户注册）
    const userInviteCode = await generateUniqueInviteCode();
    const userPassword = await hashPassword('user00125!');

    const [user, userCreated] = await User.findOrCreate({
      where: { username: 'user' },
      defaults: {
        username: 'user',
        password: userPassword,
        name: 'Initial User',
        phone: '13800138001',
        invite_code: userInviteCode,
        user_id: 'U000001',
        level_id: vip1Level.id,
        status: 'active'
      },
      transaction
    });

    if (userCreated) {
      console.log(`初始用户创建成功，用户ID: ${user.user_id}, 邀请码: ${userInviteCode}`);
      console.log(`其他用户可以使用邀请码 ${userInviteCode} 进行注册`);

      // 为用户创建邀请码记录
      await InviteCode.create({
        code: userInviteCode,
        user_id: user.id,
        used_count: 0,
        max_uses: 0, // 0表示无限制
        status: true
      }, { transaction });

      // 为用户创建账户余额
      await AccountBalance.create({
        user_id: user.id,
        account_type: 'income',
        balance: 0
      }, { transaction });

      await AccountBalance.create({
        user_id: user.id,
        account_type: 'deposit',
        balance: 0
      }, { transaction });
    } else {
      console.log('初始用户已存在');
      // 如果用户已存在，获取其邀请码
      const existingInviteCode = await InviteCode.findOne({
        where: { user_id: user.id }
      }, { transaction });
      if (existingInviteCode) {
        console.log(`现有用户的邀请码: ${existingInviteCode.code}`);
      }
    }

    await transaction.commit();
    console.log('初始用户创建完成');

  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// 主函数
async function initSuperAdminAndUsers() {
  try {
    console.log('=== 开始初始化超级管理员账号和初始用户 ===');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 同步数据库模型（确保表结构存在）
    await sequelize.sync();
    console.log('数据库模型同步完成');

    // 1. 创建基础权限
    await createPermissions();

    // 2. 创建角色并分配权限
    const { superAdminRole, adminRole } = await createRoles();

    // 3. 创建超级管理员账号
    const admin = await createSuperAdmin(superAdminRole);

    // 4. 创建用户等级
    await createUserLevels();

    // 5. 创建初始用户
    await createInitialUsers();

    console.log('=== 初始化完成 ===');
    console.log('');
    console.log('超级管理员账号信息：');
    console.log('  用户名: task0012');
    console.log('  密码: xt00159258');
    console.log('  角色: 超级管理员');
    console.log('');
    console.log('初始用户账号信息：');
    console.log('  用户名: user');
    console.log('  密码: user00125!');
    console.log('  用户ID: U000001');
    console.log('');
    console.log('网站现在可以上线了！');
    console.log('新用户注册时可以使用初始用户的邀请码。');

    process.exit(0);

  } catch (error) {
    console.error('初始化失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行初始化
if (require.main === module) {
  initSuperAdminAndUsers();
}

module.exports = {
  initSuperAdminAndUsers,
  createPermissions,
  createRoles,
  createSuperAdmin,
  createUserLevels,
  createInitialUsers
};
