{"version": 3, "file": "table-grid.mjs", "sources": ["../../../../../../packages/components/table-v2/src/table-grid.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport {\n  DynamicSizeGrid,\n  FixedSizeGrid,\n} from '@element-plus/components/virtual-list'\nimport { isNumber, isObject } from '@element-plus/utils'\nimport { Header } from './components'\nimport { TableV2InjectionKey } from './tokens'\nimport { tableV2GridProps } from './grid'\nimport { sum } from './utils'\n\nimport type { UnwrapRef } from 'vue'\nimport type {\n  DynamicSizeGridInstance,\n  GridDefaultSlotParams,\n  GridItemKeyGetter,\n  GridItemRenderedEvtParams,\n  GridScrollOptions,\n  ResetAfterIndex,\n  Alignment as ScrollStrategy,\n} from '@element-plus/components/virtual-list'\nimport type { TableV2HeaderInstance } from './components'\nimport type { TableV2GridProps } from './grid'\n\nconst COMPONENT_NAME = 'ElTableV2Grid'\n\nconst useTableGrid = (props: TableV2GridProps) => {\n  const headerRef = ref<TableV2HeaderInstance>()\n  const bodyRef = ref<DynamicSizeGridInstance>()\n  const scrollLeft = ref(0)\n\n  const totalHeight = computed(() => {\n    const { data, rowHeight, estimatedRowHeight } = props\n\n    if (estimatedRowHeight) {\n      return\n    }\n\n    return data.length * (rowHeight as number)\n  })\n\n  const fixedRowHeight = computed(() => {\n    const { fixedData, rowHeight } = props\n\n    return (fixedData?.length || 0) * (rowHeight as number)\n  })\n\n  const headerHeight = computed(() => sum(props.headerHeight))\n\n  const gridHeight = computed(() => {\n    const { height } = props\n    return Math.max(0, height - unref(headerHeight) - unref(fixedRowHeight))\n  })\n\n  const hasHeader = computed(() => {\n    return unref(headerHeight) + unref(fixedRowHeight) > 0\n  })\n\n  const itemKey: GridItemKeyGetter = ({ data, rowIndex }) =>\n    data[rowIndex][props.rowKey]\n\n  function onItemRendered({\n    rowCacheStart,\n    rowCacheEnd,\n    rowVisibleStart,\n    rowVisibleEnd,\n  }: GridItemRenderedEvtParams) {\n    props.onRowsRendered?.({\n      rowCacheStart,\n      rowCacheEnd,\n      rowVisibleStart,\n      rowVisibleEnd,\n    })\n  }\n\n  function resetAfterRowIndex(index: number, forceUpdate: boolean) {\n    bodyRef.value?.resetAfterRowIndex(index, forceUpdate)\n  }\n\n  function scrollTo(x: number, y: number): void\n  function scrollTo(options: GridScrollOptions): void\n  function scrollTo(leftOrOptions: number | GridScrollOptions, top?: number) {\n    const header$ = unref(headerRef)\n    const body$ = unref(bodyRef)\n\n    if (isObject(leftOrOptions)) {\n      header$?.scrollToLeft(leftOrOptions.scrollLeft)\n      scrollLeft.value = leftOrOptions.scrollLeft!\n      body$?.scrollTo(leftOrOptions)\n    } else {\n      header$?.scrollToLeft(leftOrOptions)\n      scrollLeft.value = leftOrOptions\n      body$?.scrollTo({\n        scrollLeft: leftOrOptions,\n        scrollTop: top,\n      })\n    }\n  }\n\n  function scrollToTop(scrollTop: number) {\n    unref(bodyRef)?.scrollTo({\n      scrollTop,\n    })\n  }\n\n  function scrollToRow(row: number, strategy: ScrollStrategy) {\n    unref(bodyRef)?.scrollToItem(row, 1, strategy)\n  }\n\n  function forceUpdate() {\n    unref(bodyRef)?.$forceUpdate()\n    unref(headerRef)?.$forceUpdate()\n  }\n\n  watch(\n    () => props.bodyWidth,\n    () => {\n      if (isNumber(props.estimatedRowHeight))\n        bodyRef.value?.resetAfter({ columnIndex: 0 }, false)\n    }\n  )\n\n  return {\n    bodyRef,\n    forceUpdate,\n    fixedRowHeight,\n    gridHeight,\n    hasHeader,\n    headerHeight,\n    headerRef,\n    totalHeight,\n\n    itemKey,\n    onItemRendered,\n    resetAfterRowIndex,\n    scrollTo,\n    scrollToTop,\n    scrollToRow,\n    scrollLeft,\n  }\n}\n\nconst TableGrid = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2GridProps,\n  setup(props, { slots, expose }) {\n    const { ns } = inject(TableV2InjectionKey)!\n\n    const {\n      bodyRef,\n      fixedRowHeight,\n      gridHeight,\n      hasHeader,\n      headerRef,\n      headerHeight,\n      totalHeight,\n\n      forceUpdate,\n      itemKey,\n      onItemRendered,\n      resetAfterRowIndex,\n      scrollTo,\n      scrollToTop,\n      scrollToRow,\n      scrollLeft,\n    } = useTableGrid(props)\n\n    provide('tableV2GridScrollLeft', scrollLeft)\n\n    expose({\n      forceUpdate,\n      /**\n       * @description fetch total height\n       */\n      totalHeight,\n      /**\n       * @description scroll to a position\n       */\n      scrollTo,\n      /**\n       * @description scroll vertically to position y\n       */\n      scrollToTop,\n      /**\n       * @description scroll to a given row\n       * @params row {Number} which row to scroll to\n       * @params strategy {ScrollStrategy} use what strategy to scroll to\n       */\n      scrollToRow,\n      /**\n       * @description reset rendered state after row index\n       */\n      resetAfterRowIndex,\n    })\n\n    const getColumnWidth = () => props.bodyWidth\n\n    return () => {\n      const {\n        cache,\n        columns,\n        data,\n        fixedData,\n        useIsScrolling,\n        scrollbarAlwaysOn,\n        scrollbarEndGap,\n        scrollbarStartGap,\n        style,\n        rowHeight,\n        bodyWidth,\n        estimatedRowHeight,\n        headerWidth,\n        height,\n        width,\n\n        getRowHeight,\n        onScroll,\n      } = props\n\n      const isDynamicRowEnabled = isNumber(estimatedRowHeight)\n      const Grid = isDynamicRowEnabled ? DynamicSizeGrid : FixedSizeGrid\n      const _headerHeight = unref(headerHeight)\n\n      return (\n        <div role=\"table\" class={[ns.e('table'), props.class]} style={style}>\n          <Grid\n            ref={bodyRef}\n            // special attrs\n            data={data}\n            useIsScrolling={useIsScrolling}\n            itemKey={itemKey}\n            // column attrs\n            columnCache={0}\n            columnWidth={isDynamicRowEnabled ? getColumnWidth : bodyWidth}\n            totalColumn={1}\n            // row attrs\n            totalRow={data.length}\n            rowCache={cache}\n            rowHeight={isDynamicRowEnabled ? getRowHeight : rowHeight}\n            // DOM attrs\n            width={width}\n            height={unref(gridHeight)}\n            class={ns.e('body')}\n            role=\"rowgroup\"\n            scrollbarStartGap={scrollbarStartGap}\n            scrollbarEndGap={scrollbarEndGap}\n            scrollbarAlwaysOn={scrollbarAlwaysOn}\n            // handlers\n            onScroll={onScroll}\n            onItemRendered={onItemRendered}\n            perfMode={false}\n          >\n            {{\n              default: (params: GridDefaultSlotParams) => {\n                const rowData = data[params.rowIndex]\n                return slots.row?.({\n                  ...params,\n                  columns,\n                  rowData,\n                })\n              },\n            }}\n          </Grid>\n          {unref(hasHeader) && (\n            <Header\n              ref={headerRef}\n              class={ns.e('header-wrapper')}\n              columns={columns}\n              headerData={data}\n              headerHeight={props.headerHeight}\n              fixedHeaderData={fixedData}\n              rowWidth={headerWidth}\n              rowHeight={rowHeight}\n              width={width}\n              height={Math.min(_headerHeight + unref(fixedRowHeight), height)}\n            >\n              {{\n                dynamic: slots.header,\n                fixed: slots.row,\n              }}\n            </Header>\n          )}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableGrid\n\nexport type TableGridRowSlotParams = {\n  columns: TableV2GridProps['columns']\n  rowData: any\n} & GridDefaultSlotParams\n\nexport type TableGridInstance = InstanceType<typeof TableGrid> &\n  UnwrapRef<{\n    forceUpdate: () => void\n    /**\n     * @description fetch total height\n     */\n    totalHeight: number\n\n    /**\n     * @description scrollTo a position\n     * @param { number | ScrollToOptions } arg1\n     * @param { number } arg2\n     */\n    scrollTo(leftOrOptions: number | GridScrollOptions, top?: number): void\n\n    /**\n     * @description scroll vertically to position y\n     */\n    scrollToTop(scrollTop: number): void\n    /**\n     * @description scroll to a given row\n     * @params row {Number} which row to scroll to\n     * @params @optional strategy {ScrollStrategy} use what strategy to scroll to\n     */\n    scrollToRow(row: number, strategy: ScrollStrategy): void\n    /**\n     * @description reset rendered state after row index\n     * @param { number } rowIndex\n     * @param { boolean } forceUpdate\n     */\n    resetAfterRowIndex: ResetAfterIndex\n  }>\n"], "names": ["COMPONENT_NAME", "useTableGrid", "props", "headerRef", "ref", "bodyRef", "scrollLeft", "data", "rowHeight", "estimatedRowHeight", "headerHeight", "gridHeight", "height", "Math", "max", "unref", "<PERSON><PERSON><PERSON><PERSON>", "itemKey", "rowIndex", "rowCacheStart", "rowCacheEnd", "rowVisibleEnd", "forceUpdate", "leftOrOptions", "header$", "scrollToLeft", "value", "scrollTop", "top", "scrollToTop", "$forceUpdate", "watch", "columnIndex", "fixedRowHeight", "totalHeight", "onItemRendered", "resetAfterRowIndex", "TableGrid", "name", "expose", "ns", "inject", "TableV2InjectionKey", "scrollTo", "scrollToRow", "provide", "_createVNode", "cache", "columns", "fixedData", "useIsScrolling", "scrollbarAlwaysOn", "scrollbarEndGap", "scrollbarStartGap", "style", "bodyWidth", "headerWidth", "width", "onScroll", "isDynamicRowEnabled", "DynamicSizeGrid", "_headerHeight", "class"], "mappings": ";;;;;;;;;;AAgCA,MAAMA,cAAc,GAAG,eAAvB,CAAA;;AAEA,EAAMC,MAAAA,SAAAA,GAAgBC,GAAAA,EAAAA,CAAD;EACnB,MAAMC,OAAAA,GAAYC,GAAAA,EAAG,CAArB;EACA,MAAMC,UAAUD,GAAG,GAAnB,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,MAAME,WAAU,GAAM,QAAtB,CAAA,MAAA;AAEA,IAAA,MAAiB;MACT,IAAA;MAAEC,SAAF;MAAQC,kBAAR;AAAmBC,KAAAA,GAAAA,KAAAA,CAAAA;AAAnB,IAAA,IAA0CP,kBAAhD,EAAA;;AAEA,KAAA;AACE,IAAA,OAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA;AACD,GAAA,CAAA,CAAA;;AAED,IAAA,MAAA;AACD,MARD,SAAA;AAUA,MAAA,SAAoB;KACZ,GAAA,KAAA,CAAA;WAAA,CAAA,CAAA,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,SAAA,CAAA;AAAaM,GAAAA,CAAAA,CAAAA;AAAb,EAAA,MAA2BN,YAAjC,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEA,EAAA,MAAA,UAAiB,GAAA,QAAT,CAAqB;AAC9B,IAJD,MAAA;MAMMQ,MAAAA;AAEN,KAAA,GAAMC,KAAU,CAAA;IACd,OAAM,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,CAAA,YAAA,CAAA,GAAA,KAAA,CAAA,cAAA,CAAA,CAAA,CAAA;AAAEC,GAAAA,CAAAA,CAAAA;AAAF,EAAA,MAAaV,SAAnB,GAAA,QAAA,CAAA,MAAA;AACA,IAAA,OAAOW,KAAKC,CAAAA,YAAa,CAAA,GAAQ,KAACJ,eAAf,CAA+BK,KAAK;AACxD,GAH0B,CAA3B,CAAA;AAKA,EAAA,MAAMC,OAAS,GAAA,CAAA;IACb,IAAOD;AACR,IAFD,QAAA;;EAIA,SAAME,cAA8B,CAAA;IAAEV,aAAF;AAAQW,IAAAA,WAAAA;IAAT,eAC5BA;;AAEP,GAAA,EAAA;IACEC,IADsB,EAAA,CAAA;IAEtBC,CAFsB,EAAA,GAAA,KAAA,CAAA,cAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA;MAAA,aAAA;AAItBC,MAAAA,WAAAA;AAJsB,MAKM,eAAA;MACvB;MACHF,CADqB;;WAAA,kBAAA,CAAA,KAAA,EAAA,YAAA,EAAA;AAIrBE,IAAAA,IAAAA,EAAAA,CAAAA;IAJqB,CAAvB,EAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,KAAA,EAAA,YAAA,CAAA,CAAA;AAMD,GAAA;;AAED,IAAA,MAAA,OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAA2CC;AACzCjB,IAAAA,MAAAA,KAAA,GAAA;AACD,IAAA,IAAA,QAAA,CAAA,aAAA,CAAA,EAAA;;AAID,MAAA,UAAA,CAAA,KAAA,GAAA,aAAA,CAA2E,UAAA,CAAA;AACzE,MAAA,KAAa,IAAA,IAAA,GAAQ,cAArB,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AACA,KAAA,MAAW;;AAEX,MAAA,UAAY,CAACkB,KAAD,GAAA,aAAiB,CAAA;AAC3BC,MAAAA,KAAAA,IAASC,IAAAA,GAAAA,KAAT,CAAsBF,GAAAA,KAAAA,CAAAA,QAAtB,CAAA;AACAjB,QAAAA,UAAWoB,EAAAA,aAAQH;QACd,SAAL,EAAA,GAAgBA;AACjB,OAAM,CAAA,CAAA;;;WAGA,WAAW,CAAA,SAAA,EAAA;AACdjB,IAAAA,IAAAA,EAAAA,CAAAA;AACAqB,IAAAA,CAAAA,EAAAA,GAAAA,KAAAA,CAAAA,OAAWC,CAAAA,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,QAAAA,CAAAA;MAFG,SAAhB;AAID,KAAA,CAAA,CAAA;AACF,GAAA;;IAEQC,IAAAA,EAAAA,CAAAA;AACPd,IAAAA,CAAAA,EAAAA,GAAMV,KAAAA,CAAAA,aAAmB,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,EAAA,QAAA,CAAA,CAAA;AACvBsB,GAAAA;WADF,WAAA,GAAA;AAGD,IAAA,IAAA,EAAA,EAAA,EAAA,CAAA;;AAED,IAAA,CAAA,EAAA,GAAA,KAAA,CAAA,SAAA,CAAA,YAA4D,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;;AAE3D,EAAA,KAAA,CAAA,MAAA,KAAA,CAAA,SAAA,EAAA,MAAA;;AAED,IAAA,IAAA,cAAA,CAAuB,kBAAA,CAAA;AACrBZ,MAAAA,CAAAA,EAAK,GAACV,OAAN,CAAgByB,KAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AACAf,QAAAA,WAAK,EAAL,CAAkBe;AACnB,OAAA,EAAA,KAAA,CAAA,CAAA;;AAEDC,EAAAA,OACQ7B;AAEJ,IAAA,OAAY;AACkB8B,IAAAA,WAAAA;AAAF,IAAA,cAA1B;AACH,IALH,UAAA;IAQO,SAAA;IACL3B,YADK;IAELiB,SAFK;IAGLW,WAHK;IAILtB,OAJK;IAKLK,cALK;IAMLN,kBANK;IAOLP,QAPK;IAQL+B,WARK;IAULjB,WAVK;IAWLkB,UAXK;IAYLC;;MAZK,SAAA,GAAA,eAAA,CAAA;MAAA,EAAA,cAAA;AAgBL9B,EAAAA,KAAAA,EAAAA,gBAAAA;EAhBK,KAAP,CAAA,KAAA,EAAA;AAkBD,IAlHD,KAAA;;AAoHA,GAAM+B,EAAAA;AACJC,IAAAA,MADgC;AAEhCpC,MAAAA;;IACK;MAAQ,OAAA;AAASqC,MAAAA,cAAAA;AAAT,MAAmB,UAAA;MACxB,SAAA;AAAEC,MAAAA,SAAAA;MAAOC,YAAOC;MAEhB,WAAA;MACJrC,WADI;MAEJ4B,OAFI;MAGJtB,cAHI;MAIJK,kBAJI;MAKJb,QALI;MAMJO,WANI;MAOJwB,WAPI;MASJZ,UATI;QAAA,YAAA,CAAA,KAAA,CAAA,CAAA;WAAA,CAAA,uBAAA,EAAA,UAAA,CAAA,CAAA;UAAA,CAAA;MAaJqB,WAbI;MAcJd,WAdI;MAeJe,QAfI;AAgBJtC,MAAAA,WAAAA;MACEL,WAAAA;AAEJ4C,MAAAA,kBAAQ;AAERN,KAAAA,CAAAA,CAAAA;UAAO,cAAA,GAAA,MAAA,KAAA,CAAA,SAAA,CAAA;;AAEL,MAAA,MAAA;AACN,QAAA,KAAA;AACA,QAAA,OAAA;QAJW,IAAA;;AAML,QAAA,cAAA;AACN,QAAA,iBAAA;AACA,QAAA,eAAA;QARW,iBAAA;;AAUL,QAAA,SAAA;AACN,QAAA,SAAA;AACA,QAAA,kBAAA;QAZW,WAAA;;AAcL,QAAA,KAAA;AACN,QAAA,YAAA;AACA,QAAA,QAAA;AACA,OAAA,GAAA,KAAA,CAAA;AACA,MAAA,MAAA,mBAAA,GAAA,QAAA,CAAA,kBAAA,CAAA,CAAA;MACMK,MAnBK,IAAA,GAAA,mBAAA,GAAA,eAAA,GAAA,aAAA,CAAA;;AAoBL,MAAA,OAAAE,WAAA,CAAA,KAAA,EAAA;AACN,QAAA,MAAA,EAAA,OAAA;AACA,QAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA;AACMV,QAAAA,OAAAA,EAAAA,KAAAA;AAvBK,OAAP,EAAA,CAAAU,WAAA,CAAA,IAAA,EAAA;;AA0BA,QAAA,MAAoB,EAAA,IAAA;;AAEpB,QAAA,SAAa,EAAA,OAAA;QACL,aAAA,EAAA,CAAA;QACJC,aADI,EAAA,mBAAA,GAAA,cAAA,GAAA,SAAA;QAEJC,aAFI,EAAA,CAAA;QAGJzC,UAHI,EAAA,IAAA,CAAA,MAAA;QAIJ0C,UAJI,EAAA,KAAA;QAKJC,WALI,EAAA,mBAAA,GAAA,YAAA,GAAA,SAAA;QAMJC,OANI,EAAA,KAAA;QAOJC,QAPI,EAAA,KAAA,CAAA,UAAA,CAAA;QAQJC,OARI,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;QASJC,MATI,EAAA,UAAA;QAUJ9C,mBAVI,EAAA,iBAAA;QAWJ+C,iBAXI,EAAA,eAAA;QAYJ9C,mBAZI,EAAA,iBAAA;QAaJ+C,UAbI,EAAA,QAAA;QAcJ5C,gBAdI,EAAA,cAAA;QAeJ6C,UAfI,EAAA,KAAA;SAAA;AAkBJC,QAAAA,OAAAA,EAAAA,CAAAA,MAAAA,KAAAA;AAlBI,UAmBFxD,IAnBJ,EAAA,CAAA;AAqBA,UAAA,MAAyB,OAAA,GAAA,IAAA,CAAA,MAAW,CAAA;AACpC,UAAA,OAAayD,CAAAA,EAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAAA,IAAsBC,GAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAH,IAAhC,CAAA,KAAA,EAAA;;AACA,YAAMC,OAAa;;AAEnB,WAAA,CAAA,CAAA;AAAA,SAAA;QAAA,EAC2B,KAAA,CAAA,SAAC,CAAA,IAAAf,WAAqBgB,CAAAA,MADjD,EAAA;QAAA,KACgER,EAAAA,SAAAA;AADhE,QAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA;AAAA,QAAA,SAAA,EAAA,OAAA;AAAA,QAAA,YAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA,KAAA,CAAA,YAAA;AAAA,QAAA,iBAAA,EAAA,SAAA;AAAA,QAAA,UAAA,EAAA,WAAA;AAAA,QAAA,WAAA,EAAA,SAUsC;AAVtC,QAAA,OAAA,EAAA,KAAA;QAAA,QAagB/C,EAAAA,IAAAA,CAAAA,GAbhB,CAAA,aAAA,GAAA,KAAA,CAAA,cAAA,CAAA,EAAA,MAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAeoC,CAAA,MAAA;AAfpC,QAAA,KAAA,EAAA,KAAA,CAAA,GAAA;QAAA,CAkBcQ,CAAAA,CAAAA;AAlBd,KAAA,CAAA;AAAA,GAAA;AAAA,CAAA,CAAA,CAAA;AAAA,YAAA,SAAA;;;;"}