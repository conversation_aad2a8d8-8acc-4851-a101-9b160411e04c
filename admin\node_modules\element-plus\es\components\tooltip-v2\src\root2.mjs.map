{"version": 3, "file": "root2.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/root.vue"], "sourcesContent": ["<template>\n  <slot :open=\"open\" />\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { isNumber, isPropAbsent } from '@element-plus/utils'\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants'\nimport { tooltipV2RootProps } from './root'\n\ndefineOptions({\n  name: 'ElTooltipV2Root',\n})\n\nconst props = defineProps(tooltipV2RootProps)\n\n/**\n * internal open state, when no model value was provided, use this as indicator instead\n */\nconst _open = ref(props.defaultOpen)\nconst triggerRef = ref<HTMLElement | null>(null)\n\nconst open = computed<boolean>({\n  get: () => (isPropAbsent(props.open) ? _open.value : props.open),\n  set: (open) => {\n    _open.value = open\n    props['onUpdate:open']?.(open)\n  },\n})\n\nconst isOpenDelayed = computed(\n  () => isNumber(props.delayDuration) && props.delayDuration > 0\n)\n\nconst { start: onDelayedOpen, stop: clearTimer } = useTimeoutFn(\n  () => {\n    open.value = true\n  },\n  computed(() => props.delayDuration),\n  {\n    immediate: false,\n  }\n)\n\nconst ns = useNamespace('tooltip-v2')\n\nconst contentId = useId()\n\nconst onNormalOpen = () => {\n  clearTimer()\n  open.value = true\n}\n\nconst onDelayOpen = () => {\n  unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen()\n}\n\nconst onOpen = onNormalOpen\n\nconst onClose = () => {\n  clearTimer()\n  open.value = false\n}\n\nconst onChange = (open: boolean) => {\n  if (open) {\n    document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN))\n    onOpen()\n  }\n\n  props.onOpenChange?.(open)\n}\n\nwatch(open, onChange)\n\nonMounted(() => {\n  // Keeps only 1 tooltip open at a time\n  document.addEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nonBeforeUnmount(() => {\n  clearTimer()\n  document.removeEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nprovide(tooltipV2RootKey, {\n  contentId,\n  triggerRef,\n  ns,\n\n  onClose,\n  onDelayOpen,\n  onOpen,\n})\n\ndefineExpose({\n  /**\n   * @description open tooltip programmatically\n   */\n  onOpen,\n\n  /**\n   * @description close tooltip programmatically\n   */\n  onClose,\n})\n</script>\n"], "names": ["open", "_renderSlot", "_unref"], "mappings": ";;;;;;;;;mCAoBc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAOA,IAAM,MAAA,KAAA,GAAQ,GAAI,CAAA,KAAA,CAAM,WAAW,CAAA,CAAA;AACnC,IAAM,MAAA,UAAA,GAAa,IAAwB,IAAI,CAAA,CAAA;AAE/C,IAAA,MAAM,OAAO,QAAkB,CAAA;AAAA,MAC7B,GAAA,EAAK,MAAO,YAAa,CAAA,KAAA,CAAM,IAAI,CAAI,GAAA,KAAA,CAAM,QAAQ,KAAM,CAAA,IAAA;AAAA,MAC3D,GAAA,EAAK,CAACA,KAAS,KAAA;AACb,QAAA,IAAA,EAAM,CAAQA;AACd,QAAM,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAuB,QAC/B,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AAAA,OACD;AAED,KAAA,CAAA,CAAA;AAAsB,IAAA,mBACL,GAAA,QAAmB,CAAA,MAAA,cAA2B,CAAA,aAAA,CAAA,IAAA,KAAA,CAAA,aAAA,GAAA,CAAA,CAAA,CAAA;AAAA,IAC/D,MAAA,EAAA,KAAA,EAAA,aAAA,EAAA,IAAA,EAAA,UAAA,EAAA,GAAA,YAAA,CAAA,MAAA;AAEA,MAAA,IAAM,CAAE,KAAA,GAAO,IAAe,CAAA;AAAqB,KAAA,EAC3C,QAAA,CAAA,MAAA,KAAA,CAAA,aAAA,CAAA,EAAA;AACJ,MAAA,SAAa,EAAA,KAAA;AAAA,KACf,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,GAAS,YAAY,CAAa,YAAA,CAAA,CAAA;AAAA,IAClC,MAAA,SAAA,GAAA,KAAA,EAAA,CAAA;AAAA,IAAA,MACa,YAAA,GAAA,MAAA;AAAA,MACb,UAAA,EAAA,CAAA;AAAA,MACF,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,KAAM,CAAA;AAEN,IAAA,MAAM,cAAkB,MAAA;AAExB,MAAA,mBAAqB,CAAM,GAAA,aAAA,EAAA,GAAA,YAAA,EAAA,CAAA;AACzB,KAAW,CAAA;AACX,IAAA,MAAA,MAAa,GAAA,YAAA,CAAA;AAAA,IACf,MAAA,OAAA,GAAA,MAAA;AAEA,MAAA;AACE,MAAA,IAAA,CAAA,KAAmB,GAAA,KAAA,CAAA;AAAmC,KACxD,CAAA;AAEA,IAAA,MAAM,QAAS,GAAA,CAAA,KAAA,KAAA;AAEf,MAAA,IAAM;AACJ,MAAW,IAAA,KAAA,EAAA;AACX,QAAA,QAAa,CAAA,aAAA,CAAA,IAAA,WAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAAA,QACf,MAAA,EAAA,CAAA;AAEA,OAAM;AACJ,MAAA,CAAA,EAAA,GAAU,KAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AACR,KAAA,CAAA;AACA,IAAO,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AAAA,IACT,SAAA,CAAA,MAAA;AAEA,MAAA,yBAAyB,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;AAAA,KAC3B,CAAA,CAAA;AAEA,IAAA,eAAoB,CAAA,MAAA;AAEpB,MAAA,UAAgB,EAAA,CAAA;AAEd,MAAS,QAAA,CAAA,mCAAyC,EAAA,OAAA,CAAA,CAAA;AAAA,KACnD,CAAA,CAAA;AAED,IAAA,OAAA,CAAA,gBAAsB,EAAA;AACpB,MAAW,SAAA;AACX,MAAS,UAAA;AAA4C,MACtD,EAAA;AAED,MAAA,OAA0B;AAAA,MACxB,WAAA;AAAA,MACA,MAAA;AAAA,KACA,CAAA,CAAA;AAAA,IAEA,MAAA,CAAA;AAAA,MACA,MAAA;AAAA,MACA,OAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAAC,KAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,GAAA;AAAA,CAIX,CAAA,CAAA;AAAA,oBAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;;;;"}