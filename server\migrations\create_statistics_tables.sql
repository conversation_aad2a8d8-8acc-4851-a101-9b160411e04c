-- 创建日统计表
CREATE TABLE IF NOT EXISTS daily_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL UNIQUE,
    
    -- 会员数据
    new_user_count INT DEFAULT 0,                -- 新增会员数
    
    -- 充值数据
    deposit_amount DECIMAL(15,2) DEFAULT 0.00,   -- 充值金额
    deposit_count INT DEFAULT 0,                 -- 充值笔数
    deposit_user_count INT DEFAULT 0,            -- 充值人数
    
    -- 取款数据
    withdrawal_amount DECIMAL(15,2) DEFAULT 0.00, -- 取款金额
    withdrawal_count INT DEFAULT 0,              -- 取款笔数
    withdrawal_user_count INT DEFAULT 0,         -- 取款人数
    
    -- 投资数据
    investment_amount DECIMAL(15,2) DEFAULT 0.00, -- 投资金额
    investment_count INT DEFAULT 0,              -- 投资笔数
    investment_user_count INT DEFAULT 0,         -- 投资人数
    
    -- 收益数据
    profit_amount DECIMAL(15,2) DEFAULT 0.00,    -- 收益金额
    profit_count INT DEFAULT 0,                  -- 收益笔数
    
    -- 佣金数据
    commission_amount DECIMAL(15,2) DEFAULT 0.00, -- 佣金金额
    commission_count INT DEFAULT 0,              -- 佣金笔数
    
    -- 注册并充值数据
    register_deposit_count INT DEFAULT 0,        -- 注册并充值人数
    
    -- 平台利润（可以通过计算得出，也可以存储）
    platform_profit DECIMAL(15,2) DEFAULT 0.00,  -- 平台利润(充值金额-取款金额)
    
    -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_date (date)
);

-- 创建累计统计表
CREATE TABLE IF NOT EXISTS total_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- 会员数据
    total_user_count INT DEFAULT 0,              -- 会员总数
    
    -- 充值数据
    total_deposit_amount DECIMAL(15,2) DEFAULT 0.00, -- 总充值金额
    total_deposit_count INT DEFAULT 0,           -- 总充值笔数
    total_deposit_user_count INT DEFAULT 0,      -- 总充值人数
    
    -- 取款数据
    total_withdrawal_amount DECIMAL(15,2) DEFAULT 0.00, -- 总取款金额
    total_withdrawal_count INT DEFAULT 0,        -- 总取款笔数
    total_withdrawal_user_count INT DEFAULT 0,   -- 总取款人数
    
    -- 投资数据
    total_investment_amount DECIMAL(15,2) DEFAULT 0.00, -- 总投资金额
    total_investment_count INT DEFAULT 0,        -- 总投资笔数
    total_investment_user_count INT DEFAULT 0,   -- 总投资人数
    
    -- 收益数据
    total_profit_amount DECIMAL(15,2) DEFAULT 0.00, -- 总收益金额
    total_profit_count INT DEFAULT 0,            -- 总收益笔数
    
    -- 佣金数据
    total_commission_amount DECIMAL(15,2) DEFAULT 0.00, -- 总佣金金额
    total_commission_count INT DEFAULT 0,        -- 总佣金笔数
    
    -- 平台利润
    total_platform_profit DECIMAL(15,2) DEFAULT 0.00, -- 总平台利润
    
    -- 元数据
    last_updated_date DATE NOT NULL,             -- 最后更新日期
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 初始化累计统计表（确保只有一条记录）
INSERT INTO total_statistics (
    total_user_count, 
    total_deposit_amount, 
    total_deposit_count, 
    total_deposit_user_count,
    total_withdrawal_amount, 
    total_withdrawal_count, 
    total_withdrawal_user_count,
    total_investment_amount, 
    total_investment_count, 
    total_investment_user_count,
    total_profit_amount, 
    total_profit_count,
    total_commission_amount, 
    total_commission_count,
    total_platform_profit,
    last_updated_date
) VALUES (
    0, 0.00, 0, 0, 0.00, 0, 0, 0.00, 0, 0, 0.00, 0, 0.00, 0, 0.00, CURDATE()
);
