'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 1. 修改 type 列的类型，添加 increase 类型，保留 bonus 类型
    await queryInterface.sequelize.query(`
      ALTER TABLE transactions
      MODIFY COLUMN type ENUM('deposit', 'withdrawal', 'investment', 'investment_gift', 'investment_purchase', 'profit', 'commission', 'bonus', 'increase', 'deduction')
      NOT NULL
      COMMENT '交易类型：deposit=充值, withdrawal=提现, investment=投资, investment_gift=赠送投资, investment_purchase=购买投资, profit=收益, commission=佣金, bonus=赠金(已废弃), increase=增金, deduction=扣除'
    `);

    // 2. 将现有的 bonus 记录更新为 increase
    await queryInterface.sequelize.query(`
      UPDATE transactions
      SET type = 'increase'
      WHERE type = 'bonus'
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // 1. 将 increase 记录改回 bonus
    await queryInterface.sequelize.query(`
      UPDATE transactions
      SET type = 'bonus'
      WHERE type = 'increase'
    `);

    // 2. 修改 type 列的类型，恢复原来的枚举值
    await queryInterface.sequelize.query(`
      ALTER TABLE transactions
      MODIFY COLUMN type ENUM('deposit', 'withdrawal', 'investment', 'investment_gift', 'investment_purchase', 'profit', 'commission', 'bonus', 'deduction')
      NOT NULL
      COMMENT '交易类型：deposit=充值, withdrawal=提现, investment=投资, investment_gift=赠送投资, investment_purchase=购买投资, profit=收益, commission=佣金, bonus=赠金, deduction=扣除'
    `);
  }
};
