'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查表是否存在
      const [tables] = await queryInterface.sequelize.query(
        "SHOW TABLES LIKE 'bank_cards'"
      );

      if (tables.length === 0) {
        console.log('创建bank_cards表...');
        await queryInterface.createTable('bank_cards', {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          user_id: {
            type: Sequelize.INTEGER,
            allowNull: true,
            comment: '用户ID，NULL表示系统收款卡',
            references: {
              model: 'users',
              key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
          },
          bank_name: {
            type: Sequelize.STRING(100),
            allowNull: false,
            comment: '银行名称',
          },
          card_number: {
            type: Sequelize.STRING(50),
            allowNull: false,
            comment: '卡号',
          },
          card_holder: {
            type: Sequelize.STRING(100),
            allowNull: false,
            comment: '持卡人姓名',
          },
          branch: {
            type: Sequelize.STRING(100),
            allowNull: true,
            comment: '支行名称',
          },
          is_default: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否默认卡',
          },
          card_type: {
            type: Sequelize.ENUM('user', 'system'),
            allowNull: false,
            comment: '卡类型：user=用户卡, system=系统收款卡',
          },
          daily_limit: {
            type: Sequelize.DECIMAL(15, 2),
            allowNull: true,
            comment: '日限额，仅对系统卡有效',
          },
          status: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '状态：true=启用, false=禁用',
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
          },
        });

        // 添加索引
        console.log('添加索引...');
        await queryInterface.addIndex('bank_cards', ['user_id']);
        await queryInterface.addIndex('bank_cards', ['card_type']);
        await queryInterface.addIndex('bank_cards', ['is_default']);
        await queryInterface.addIndex('bank_cards', ['status']);
      } else {
        console.log('bank_cards表已存在，跳过创建');

        // 检查索引是否存在
        const [indexes] = await queryInterface.sequelize.query(
          "SHOW INDEX FROM bank_cards"
        );

        // 获取所有索引名称
        const indexNames = indexes.map(index => index.Key_name);

        // 检查并添加缺失的索引
        if (!indexNames.includes('bank_cards_user_id')) {
          await queryInterface.addIndex('bank_cards', ['user_id']);
        }

        if (!indexNames.includes('bank_cards_card_type')) {
          await queryInterface.addIndex('bank_cards', ['card_type']);
        }

        // 检查is_default字段是否存在
        const [columns] = await queryInterface.sequelize.query(
          "SHOW COLUMNS FROM bank_cards LIKE 'is_default'"
        );

        if (columns.length > 0 && !indexNames.includes('bank_cards_is_default')) {
          await queryInterface.addIndex('bank_cards', ['is_default']);
        }

        if (!indexNames.includes('bank_cards_status')) {
          await queryInterface.addIndex('bank_cards', ['status']);
        }
      }

      console.log('bank_cards表迁移完成');
    } catch (error) {
      console.error('bank_cards表迁移失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('bank_cards');
  }
};
