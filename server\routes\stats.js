/**
 * 统计数据路由
 */
const express = require('express');
const statsController = require('../controllers/statsController');
const { verifyAdminToken, verifyUserToken } = require('../middlewares/authMiddleware');

// 管理端路由
const adminRouter = express.Router();

// 所有管理端路由都需要管理员认证
adminRouter.use(verifyAdminToken);

/**
 * @swagger
 * /api/admin/stats/dashboard:
 *   get:
 *     summary: 获取管理端首页统计数据
 *     tags: [统计数据]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
adminRouter.get('/dashboard', statsController.getAdminDashboardStats);

/**
 * @swagger
 * /api/admin/stats/recent-deposits:
 *   get:
 *     summary: 获取最近充值记录（分页）
 *     tags: [统计数据]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 */
adminRouter.get('/recent-deposits', statsController.getRecentDeposits);

/**
 * @swagger
 * /api/admin/stats/recent-users:
 *   get:
 *     summary: 获取最近注册用户（分页）
 *     tags: [统计数据]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 */
adminRouter.get('/recent-users', statsController.getRecentUsers);

// 移动端路由
const mobileRouter = express.Router();

// 所有移动端路由都需要用户认证
mobileRouter.use(verifyUserToken);

/**
 * @swagger
 * /api/mobile/stats/home:
 *   get:
 *     summary: 获取首页统计数据
 *     tags: [统计数据]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
mobileRouter.get('/home', statsController.getHomeStats);

/**
 * @swagger
 * /api/mobile/stats/account:
 *   get:
 *     summary: 获取账户统计数据
 *     tags: [统计数据]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, all]
 *         description: 统计周期，可选值："today"(今天)、"week"(本周)、"month"(本月)、"all"(全部)，默认"today"
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未认证
 */
mobileRouter.get('/account', statsController.getAccountStats);

module.exports = {
  admin: adminRouter,
  mobile: mobileRouter
};
