/* empty css             *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                  *//* empty css                      *//* empty css                *//* empty css                  *//* empty css                        */import"./el-form-item-l0sNRNKZ.js";/* empty css               *//* empty css                  *//* empty css                *//* empty css                        */import"./el-tooltip-l0sNRNKZ.js";/* empty css                        */import{d as We,r as p,o as je,c as C,b as i,e as t,a8 as oe,a9 as se,w as s,m as He,f as Ye,i as Ke,aa as Xe,ab as Ge,ac as Je,p as m,v as Qe,y as O,n as f,x as Ze,j as v,ad as ne,aw as et,ae as ie,aB as re,af as de,ai as tt,aj as lt,ak as at,V as ue,al as ot,aL as st,M as q,aM as nt,an as it,aP as ce,ba as rt,ap as dt,aq as ut,at as ct,E as pt,h as mt,aI as vt,a7 as gt,aU as ft,aC as _t,ao as bt,aA as yt,az as ht,a0 as pe,g as k,bb as wt,_ as xt}from"./index-LncY9lAB.js";import{A as kt}from"./AttachmentSelector-YIf-YS41.js";import{f as Ut}from"./urlConfig-DWnoqxuv.js";import{g as Vt,p as _e,d as Ct,a as Et}from"./request-Cd-6Wde0.js";import{a as me}from"./attachments-CNgOoo0P.js";/* empty css                    *//* empty css                 */import"./index-t--hEgTQ.js";function ve(y){return Vt("/api/admin/banners",y)}function It(y){return Et("/api/admin/banners",y)}function Tt(y,E){return _e(`/api/admin/banners/${y}`,E)}function ge(y){return Ct("/api/admin/banners",{ids:y})}function fe(y,E){return _e(`/api/admin/banners/${y}/sort`,{sort_order:E})}const St={class:"banners-container"},$t={class:"tabs"},Dt=["onClick"],Mt={class:"toolbar"},Rt={class:"toolbar-left"},zt={class:"toolbar-right"},Bt={class:"table-wrapper"},Lt={class:"image-preview-cell"},Pt={class:"image-error"},At={key:1,class:"no-image"},Nt={class:"path-text"},Ot={class:"operation-buttons-container"},qt={class:"pagination-container"},Ft={class:"image-upload-container"},Wt={key:0,class:"image-container"},jt={class:"image-error"},Ht={class:"image-overlay"},Yt={key:1,class:"empty-image-container"},Kt={class:"image-actions"},Xt={key:0,class:"form-tip"},Gt={class:"dialog-footer banner-dialog-footer"},Jt={class:"filter-container"},Qt={class:"filter-section"},Zt={class:"section-content"},el={class:"filter-grid"},tl={class:"filter-item"},ll={class:"filter-item"},al={class:"filter-item"},ol={class:"filter-item"},sl={class:"filter-section"},nl={class:"section-content"},il={class:"filter-grid"},rl={class:"filter-item"},dl={class:"filter-item"},ul={class:"filter-item"},cl={class:"range-inputs"},pl={class:"filter-footer"},ml=We({__name:"index",setup(y){const E=p(null),F=p(),W=p(!1),I=p(!1),U=p(!0),j=p(""),T=p([]),P=p("all"),A=p(!1),w=p([]),V=p(1),M=p(10),X=p(0),u=p({id:"",title:"",url:"",status:"",position:"",weightMin:"",weightMax:"",createTimeRange:[],updateTimeRange:[]}),R=p(!1),d=p({title:"",attachment_id:void 0,imageUrl:"",position:"",sort_order:1,status:1}),be={attachment_id:[{required:!0,message:"请选择图片",trigger:"change"}],sort_order:[{required:!0,message:"请输入权重",trigger:"blur"}]},ye=[{label:"全部",value:"all"},{label:"启用",value:"1"},{label:"禁用",value:"0"}],he=l=>l?new Date(l).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-",we=l=>{T.value=l},xe=(l,e,a)=>{if(e.type==="selection"||e.label==="操作")return;const n=E.value;n&&n.toggleRowSelection(l)},G=l=>{M.value=l,g()},ke=l=>{V.value=l,g()},J=()=>{V.value=1,g()},Ue=l=>{P.value=l,V.value=1,g()},g=async()=>{var l,e,a;W.value=!0;try{const n={page:V.value,limit:M.value,status:P.value!=="all"?P.value:void 0,search:j.value||void 0,position:((l=u==null?void 0:u.value)==null?void 0:l.position)||void 0,sort_order_min:((e=u==null?void 0:u.value)==null?void 0:e.weightMin)||void 0,sort_order_max:((a=u==null?void 0:u.value)==null?void 0:a.weightMax)||void 0,sort:"sort_order",order:"asc"},r=await ve(n),{items:c,total:b}=r,Y=await Promise.all(c.map(async h=>{let x="";if(h.attachment_id)try{const _=await me(h.attachment_id);_&&_.file_path?x=_.file_path:_&&_.filePath?x=_.filePath:(console.warn("附件没有有效的文件路径:",_),x=h.position||"")}catch(_){console.error("获取附件详情失败:",_),x=h.position||""}else h.position&&(x=h.position);const K=h.status===!0?1:0,$=x?B(x):"";return{...h,imageUrl:$,status:K}}));w.value=Y,X.value=b}catch(n){console.error("获取轮播图数据失败:",n),m.error("获取数据失败，请检查网络连接")}finally{W.value=!1}},Ve=async l=>{if(!l)return"";try{return(await me(l)).file_path||""}catch(e){console.error("获取图片URL失败:",e)}return""},Ce=async()=>{try{const l=w.value.map((e,a)=>{const n=(a+1)*10;return e.sort_order!==n?(console.log(`更新轮播图 ${e.id} 的权重: ${e.sort_order} -> ${n}`),fe(e.id,n)):Promise.resolve()});return await Promise.all(l),console.log("所有轮播图权重值已更新"),!0}catch(l){throw console.error("更新轮播图排序值失败:",l),l}},Ee=async()=>{try{const e=(await ve({limit:1e3,sort:"sort_order",order:"asc"})).items;if(!e||e.length===0)return;const a=e.map((n,r)=>{const c=(r+1)*10;return n.sort_order!==c?(console.log(`重新计算轮播图 ${n.id} 的权重: ${n.sort_order} -> ${c}`),fe(n.id,c)):Promise.resolve()});await Promise.all(a),console.log("所有轮播图权重值已重新计算"),await g()}catch(l){throw console.error("重新计算权重值失败:",l),l}},H=p(null),z=p(-1),S=p(-1),N=p(!1),Ie=(l,e)=>{z.value=e,H.value=w.value[e],N.value=!0,document.addEventListener("mousemove",Q),document.addEventListener("mouseup",Z);const a=document.querySelectorAll(".el-table__row"),n=a[e];if(n){n.classList.add("row-dragging"),a.forEach((c,b)=>{b!==e&&(c.style.opacity="0.6",c.style.transition="opacity 0.3s ease")});const r=document.createElement("div");r.textContent="正在移动...",r.style.position="fixed",r.style.top="10px",r.style.left="50%",r.style.transform="translateX(-50%)",r.style.padding="8px 16px",r.style.backgroundColor="#409eff",r.style.color="white",r.style.borderRadius="4px",r.style.zIndex="9999",r.style.boxShadow="0 2px 12px rgba(0, 0, 0, 0.2)",r.style.opacity="0",r.style.transition="opacity 0.3s ease",r.id="drag-feedback",document.body.appendChild(r),setTimeout(()=>{r.style.opacity="1"},100)}},Q=l=>{if(!N.value)return;const e=document.elementFromPoint(l.clientX,l.clientY);if(!e)return;const a=e.closest(".el-table__row");if(!a)return;const n=Array.from(document.querySelectorAll(".el-table__row")),r=n.indexOf(a);if(r!==-1&&r!==z.value){n.forEach(b=>b.classList.remove("row-drop-target")),a.classList.add("row-drop-target");const c=document.getElementById("drag-feedback");if(c){const b=w.value[r];c.textContent=`正在移动到 "${b.title||"未命名"}" 之前...`}S.value!==r&&n[r].animate([{transform:"translateY(-3px)"},{transform:"translateY(0)"}],{duration:300,easing:"ease-out"}),S.value=r}},Z=async()=>{document.removeEventListener("mousemove",Q),document.removeEventListener("mouseup",Z);const l=document.getElementById("drag-feedback");l&&(l.style.opacity="0",setTimeout(()=>{l.remove()},300));const e=document.querySelectorAll(".el-table__row");if(e.forEach(a=>{a.style.opacity="1"}),N.value&&H.value&&S.value!==-1&&S.value!==z.value)try{m({message:"正在更新排序...",type:"info",duration:1e3});const a=w.value.splice(z.value,1)[0];w.value.splice(S.value,0,a),await Ce(),m({message:"更新排序成功，已重新计算所有权重",type:"success",duration:2e3}),await g()}catch(a){console.error("更新排序失败:",a),m.error("更新排序失败，请检查网络连接"),e.forEach(n=>{n.classList.remove("row-dragging"),n.classList.remove("row-drop-target")})}else e.forEach(a=>{a.classList.remove("row-dragging"),a.classList.remove("row-drop-target")});N.value=!1,H.value=null,z.value=-1,S.value=-1},Te=()=>{U.value=!0,d.value={title:"",attachment_id:void 0,imageUrl:"",position:"",sort_order:1,status:1},I.value=!0},ee=l=>{if(!l){if(T.value.length!==1){m.warning("请选择一条记录");return}l=T.value[0]}U.value=!1,console.log("编辑轮播图，原始状态值:",l.status,typeof l.status);const e=l.status===!0||l.status===1?1:0;console.log("编辑轮播图，转换后状态值:",e,typeof e),d.value={id:l.id,title:l.title,attachment_id:l.attachment_id,imageUrl:l.imageUrl,position:l.position,sort_order:l.sort_order,status:e},I.value=!0},Se=()=>{const l=T.value;if(l.length===0){m.warning("请至少选择一条记录");return}const e=l.map(n=>n.id),a=l.length>1?`确定要删除选中的 ${l.length} 条轮播图记录吗？`:"确定要删除该轮播图记录吗？";pe.confirm(a,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await ge(e),m.success("删除成功"),g()}catch(n){console.error("删除轮播图失败:",n),m.error("删除失败，请检查网络连接")}}).catch(()=>{})},$e=l=>{pe.confirm("确定要删除该轮播图记录吗？","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await ge([l.id]),m.success("删除成功"),g()}catch(e){console.error("删除轮播图失败:",e),m.error("删除失败，请检查网络连接")}}).catch(()=>{})},De=()=>{m.info("功能尚未实现")},B=l=>Ve(l),Me=async l=>{if(console.error("图片加载失败:",l.imageUrl),l.imageUrl){const e=await Ut(l.imageUrl,l.position);e&&e!==l.imageUrl&&(console.log(`找到可用的URL: ${e}，更新图片地址`),l.imageUrl=e,w.value=[...w.value])}m({message:`图片加载失败: ${l.title||"未命名轮播图"}`,type:"warning",duration:2e3})},Re=()=>{u.value={id:"",title:"",url:"",status:"",weightMin:"",weightMax:"",createTimeRange:[],updateTimeRange:[]}},ze=()=>{R.value=!1,V.value=1,g(),m.success("筛选条件已应用")},te=()=>{A.value=!0},Be=()=>{if(d.value.imageUrl){B(d.value.imageUrl);const l=document.createElement("div");l.className="el-image-viewer__wrapper",l.style.zIndex="2000";const e=document.createElement("div");e.className="el-image-viewer__mask",l.appendChild(e);const a=document.createElement("div");a.className="el-image-viewer__container",l.appendChild(a);const n=document.createElement("img");n.src=d.value.imageUrl,n.className="el-image-viewer__img",n.style.maxWidth="100%",n.style.maxHeight="100%",a.appendChild(n);const r=document.createElement("button");r.className="el-image-viewer__close",r.innerHTML='<i class="el-icon-close"></i>',r.onclick=()=>{document.body.removeChild(l)},l.appendChild(r),e.onclick=()=>{document.body.removeChild(l)},document.body.appendChild(l),m.success("图片预览已打开")}},Le=l=>{l&&(console.log("选中的附件:",l),d.value.attachment_id=l.id,d.value.imageUrl=l.url||l.file_path,l.file_path&&l.file_path.includes("/uploads/")?d.value.position=l.file_path.substring(l.file_path.indexOf("/uploads/")):d.value.position=l.file_path,m.success("图片选择成功"),wt(()=>{A.value=!1}))},Pe=async()=>{F.value&&await F.value.validate(async l=>{if(!l){m.error("请完善表单信息");return}d.value.start_time=void 0,d.value.end_time=void 0;try{let e;const a={...d.value};if(delete a.imageUrl,delete a.url,a.position&&a.position.includes("http")?a.position.includes("/uploads/")&&(a.position=a.position.substring(a.position.indexOf("/uploads/"))):!a.position&&a.attachment_id&&d.value.imageUrl&&(d.value.imageUrl.includes("/uploads/")?a.position=d.value.imageUrl.substring(d.value.imageUrl.indexOf("/uploads/")):a.position=d.value.imageUrl),a.status===1||a.status==="1"?a.status=!0:(a.status===0||a.status==="0")&&(a.status=!1),U.value)e=await It(a);else{const{id:n,...r}=a;e=await Tt(n,r)}m.success(U.value?"添加成功":"更新成功"),I.value=!1,g()}catch(e){console.error(U.value?"添加轮播图失败:":"更新轮播图失败:",e),m.error(U.value?"添加失败":"更新失败")}})};return je(async()=>{await g();try{await Ee(),await g()}catch(l){console.error("初始化权重计算失败:",l)}}),(l,e)=>{const a=Ze,n=He,r=Ke,c=ot,b=st,Y=nt,h=it,x=dt,K=Xe,$=ct,_=ut,Ae=Ge,L=mt,Ne=_t,Oe=bt,qe=pt,le=Je,ae=yt,Fe=at;return k(),C("div",St,[i("div",$t,[(k(),C(oe,null,se(ye,(o,D)=>i("div",{key:D,class:Qe(["tab",{active:P.value===o.value}]),onClick:vl=>Ue(o.value)},O(o.label),11,Dt)),64))]),i("div",Mt,[i("div",Rt,[t(n,{class:"toolbar-button",type:"default",onClick:g},{default:s(()=>[t(a,null,{default:s(()=>[t(v(ne))]),_:1}),e[22]||(e[22]=f("刷新 "))]),_:1}),t(n,{class:"toolbar-button",type:"success",onClick:Te},{default:s(()=>[t(a,null,{default:s(()=>[t(v(et))]),_:1}),e[23]||(e[23]=f("添加 "))]),_:1}),t(n,{class:"toolbar-button",type:"primary",disabled:T.value.length!==1,onClick:e[0]||(e[0]=o=>ee())},{default:s(()=>[t(a,null,{default:s(()=>[t(v(ie))]),_:1}),e[24]||(e[24]=f("编辑 "))]),_:1},8,["disabled"]),t(n,{class:"toolbar-button",type:"danger",disabled:T.value.length===0,onClick:Se},{default:s(()=>[t(a,null,{default:s(()=>[t(v(re))]),_:1}),e[25]||(e[25]=f("删除 "))]),_:1},8,["disabled"])]),i("div",zt,[t(r,{modelValue:j.value,"onUpdate:modelValue":e[1]||(e[1]=o=>j.value=o),placeholder:"搜索轮播图标题",class:"search-input",onKeyup:Ye(J,["enter"])},null,8,["modelValue"]),t(n,{class:"search-button",type:"primary",onClick:J},{default:s(()=>[t(a,null,{default:s(()=>[t(v(de))]),_:1})]),_:1}),t(n,{class:"toolbar-button export-button",type:"default",onClick:De},{default:s(()=>[t(a,null,{default:s(()=>[t(v(tt))]),_:1}),e[26]||(e[26]=f("导出 "))]),_:1})])]),t(K,{class:"table-card"},{default:s(()=>[i("div",Bt,[lt((k(),ue(x,{ref_key:"bannersTable",ref:E,data:w.value,border:"",stripe:"","highlight-current-row":"","row-key":"id",style:{width:"100%"},onSelectionChange:we,onRowClick:xe,"cell-style":{padding:"0",height:"44px",lineHeight:"44px"},"header-cell-style":{background:"#fafafa",color:"#606266",fontWeight:"bold",padding:"0",height:"44px"}},{default:s(()=>[t(c,{type:"selection",width:"40",align:"center"}),t(c,{prop:"id",label:"ID",width:"80",align:"center"}),t(c,{prop:"title",label:"标题","min-width":"120",align:"center"}),t(c,{label:"图片",width:"100",align:"center"},{default:s(o=>[i("div",Lt,[o.row.imageUrl?(k(),ue(b,{key:0,src:B(o.row.imageUrl),"preview-src-list":[],fit:"cover",class:"banner-image",style:{width:"40px",height:"40px","border-radius":"4px"},onError:D=>Me(o.row)},{error:s(()=>[i("div",Pt,[t(a,null,{default:s(()=>[t(v(q))]),_:1})])]),_:2},1032,["src","onError"])):(k(),C("div",At,[t(a,null,{default:s(()=>[t(v(q))]),_:1}),e[27]||(e[27]=i("span",null,"无图片",-1))]))])]),_:1}),t(c,{prop:"sort_order",label:"权重",width:"100",align:"center",sortable:""}),t(c,{prop:"position",label:"物理路径","min-width":"180",align:"center"},{default:s(o=>[t(Y,{content:o.row.position,placement:"top","show-after":500},{default:s(()=>[i("span",Nt,O(o.row.position),1)]),_:2},1032,["content"])]),_:1}),t(c,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(o=>[t(h,{type:o.row.status===1||o.row.status===!0?"success":"danger"},{default:s(()=>[f(O(o.row.status===1||o.row.status===!0?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"created_at",label:"创建时间",width:"180",align:"center",sortable:""},{default:s(o=>[f(O(he(o.row.created_at)),1)]),_:1}),t(c,{label:"操作",width:"160",align:"center",fixed:"right"},{default:s(o=>[i("div",Ot,[t(n,{class:"operation-button icon-only drag-handle",size:"small",type:"primary",onMousedown:ce(D=>Ie(D,o.$index),["prevent"])},{default:s(()=>[t(a,null,{default:s(()=>[t(v(rt))]),_:1})]),_:2},1032,["onMousedown"]),t(n,{class:"operation-button icon-only",size:"small",type:"default",onClick:D=>ee(o.row)},{default:s(()=>[t(a,null,{default:s(()=>[t(v(ie))]),_:1})]),_:2},1032,["onClick"]),t(n,{type:"danger",size:"small",onClick:D=>$e(o.row),class:"operation-button icon-only"},{default:s(()=>[t(a,null,{default:s(()=>[t(v(re))]),_:1})]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[Fe,W.value]])])]),_:1}),i("div",qt,[t(Ae,{"current-page":V.value,"onUpdate:currentPage":e[2]||(e[2]=o=>V.value=o),"page-size":M.value,"onUpdate:pageSize":e[3]||(e[3]=o=>M.value=o),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:X.value,onSizeChange:G,onCurrentChange:ke,"pager-count":7,background:""},{sizes:s(()=>[t(_,{"model-value":M.value,onChange:G,class:"custom-page-size"},{default:s(()=>[(k(),C(oe,null,se([10,20,50,100],o=>t($,{key:o,value:o,label:`${o}/页`},null,8,["value","label"])),64))]),_:1},8,["model-value"])]),_:1},8,["current-page","page-size","total"])]),t(le,{modelValue:I.value,"onUpdate:modelValue":e[9]||(e[9]=o=>I.value=o),title:U.value?"添加轮播图":"编辑轮播图",width:"500px",center:""},{footer:s(()=>[i("div",Gt,[t(n,{type:"primary",onClick:Pe},{default:s(()=>e[32]||(e[32]=[f("确定")])),_:1}),t(n,{onClick:e[8]||(e[8]=o=>I.value=!1)},{default:s(()=>e[33]||(e[33]=[f("取消")])),_:1})])]),default:s(()=>[t(qe,{model:d.value,"label-position":"top","label-width":"100px",rules:be,ref_key:"formRef",ref:F},{default:s(()=>[t(L,{label:"标题"},{default:s(()=>[t(r,{modelValue:d.value.title,"onUpdate:modelValue":e[4]||(e[4]=o=>d.value.title=o),placeholder:"请输入标题"},null,8,["modelValue"])]),_:1}),t(L,{label:"图片",prop:"attachment_id",required:""},{default:s(()=>[i("div",Ft,[i("div",{class:"image-preview-wrapper",onClick:te},[d.value.imageUrl?(k(),C("div",Wt,[t(b,{src:B(d.value.imageUrl),class:"banner-image-preview","preview-src-list":[B(d.value.imageUrl)],fit:"cover",style:{width:"100px",height:"100px","border-radius":"4px"}},{error:s(()=>[i("div",jt,[t(a,null,{default:s(()=>[t(v(q))]),_:1})])]),_:1},8,["src","preview-src-list"]),i("div",Ht,[t(n,{type:"primary",circle:"",size:"small",class:"preview-button",onClick:ce(Be,["stop"])},{default:s(()=>[t(a,null,{default:s(()=>[t(v(vt))]),_:1})]),_:1})])])):(k(),C("div",Yt,[t(a,{class:"image-uploader-icon"},{default:s(()=>[t(v(q))]),_:1}),e[28]||(e[28]=i("span",{class:"upload-text"},"点击选择图片",-1))]))]),i("div",Kt,[t(n,{type:"primary",size:"small",onClick:te},{default:s(()=>[t(a,null,{default:s(()=>[t(v(ft))]),_:1}),e[29]||(e[29]=f("从附件选择图片 "))]),_:1}),d.value.imageUrl?(k(),C("div",Xt,"鼠标移至图片上可预览")):gt("",!0)])])]),_:1}),t(L,{label:"权重",prop:"sort_order",required:""},{default:s(()=>[t(Ne,{modelValue:d.value.sort_order,"onUpdate:modelValue":e[5]||(e[5]=o=>d.value.sort_order=o),min:1,max:100,step:1,placeholder:"请输入权重(1-100)"},null,8,["modelValue"]),e[30]||(e[30]=i("div",{class:"form-tip"},"权重范围1-100，1为最高优先级，100为最低优先级",-1))]),_:1}),t(L,{label:"位置"},{default:s(()=>[t(r,{modelValue:d.value.position,"onUpdate:modelValue":e[6]||(e[6]=o=>d.value.position=o),placeholder:"图片的物理路径，自动生成，无需填写",disabled:""},null,8,["modelValue"]),e[31]||(e[31]=i("div",{class:"form-tip"},"图片的物理路径，自动生成",-1))]),_:1}),t(L,{label:"状态"},{default:s(()=>[t(Oe,{modelValue:d.value.status,"onUpdate:modelValue":e[7]||(e[7]=o=>d.value.status=o),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(kt,{visible:A.value,"onUpdate:visible":e[10]||(e[10]=o=>A.value=o),fileType:"image",onSelect:Le},null,8,["visible"]),t(le,{modelValue:R.value,"onUpdate:modelValue":e[20]||(e[20]=o=>R.value=o),title:"筛选条件",width:"800px","close-on-click-modal":!0,onClose:e[21]||(e[21]=o=>R.value=!1),class:"filter-dialog"},{footer:s(()=>[i("div",pl,[t(n,{class:"filter-button",type:"primary",onClick:ze},{default:s(()=>[t(a,null,{default:s(()=>[t(v(de))]),_:1}),e[44]||(e[44]=f("搜索 "))]),_:1}),t(n,{class:"filter-button",onClick:Re},{default:s(()=>[t(a,null,{default:s(()=>[t(v(ne))]),_:1}),e[45]||(e[45]=f("重置 "))]),_:1}),t(n,{class:"filter-button",onClick:e[19]||(e[19]=o=>R.value=!1)},{default:s(()=>[t(a,null,{default:s(()=>[t(v(ht))]),_:1}),e[46]||(e[46]=f("取消 "))]),_:1})])]),default:s(()=>[i("div",Jt,[i("div",Qt,[e[38]||(e[38]=i("div",{class:"section-header"},[i("div",{class:"section-title"},"基本信息")],-1)),i("div",Zt,[i("div",el,[i("div",tl,[e[34]||(e[34]=i("div",{class:"filter-label"},"ID",-1)),t(r,{modelValue:u.value.id,"onUpdate:modelValue":e[11]||(e[11]=o=>u.value.id=o),placeholder:"请输入ID"},null,8,["modelValue"])]),i("div",ll,[e[35]||(e[35]=i("div",{class:"filter-label"},"标题",-1)),t(r,{modelValue:u.value.title,"onUpdate:modelValue":e[12]||(e[12]=o=>u.value.title=o),placeholder:"请输入标题"},null,8,["modelValue"])]),i("div",al,[e[36]||(e[36]=i("div",{class:"filter-label"},"URL链接",-1)),t(r,{modelValue:u.value.url,"onUpdate:modelValue":e[13]||(e[13]=o=>u.value.url=o),placeholder:"请输入URL链接"},null,8,["modelValue"])]),i("div",ol,[e[37]||(e[37]=i("div",{class:"filter-label"},"状态",-1)),t(_,{modelValue:u.value.status,"onUpdate:modelValue":e[14]||(e[14]=o=>u.value.status=o),placeholder:"请选择状态",style:{width:"100%"}},{default:s(()=>[t($,{label:"全部",value:""}),t($,{label:"正常",value:1}),t($,{label:"隐藏",value:0})]),_:1},8,["modelValue"])])])])]),i("div",sl,[e[43]||(e[43]=i("div",{class:"section-header"},[i("div",{class:"section-title"},"时间信息")],-1)),i("div",nl,[i("div",il,[i("div",rl,[e[39]||(e[39]=i("div",{class:"filter-label"},"创建时间",-1)),t(ae,{modelValue:u.value.createTimeRange,"onUpdate:modelValue":e[15]||(e[15]=o=>u.value.createTimeRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),i("div",dl,[e[40]||(e[40]=i("div",{class:"filter-label"},"更新时间",-1)),t(ae,{modelValue:u.value.updateTimeRange,"onUpdate:modelValue":e[16]||(e[16]=o=>u.value.updateTimeRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"100%"}},null,8,["modelValue"])]),i("div",ul,[e[42]||(e[42]=i("div",{class:"filter-label"},"权重范围",-1)),i("div",cl,[t(r,{modelValue:u.value.weightMin,"onUpdate:modelValue":e[17]||(e[17]=o=>u.value.weightMin=o),placeholder:"最小值"},null,8,["modelValue"]),e[41]||(e[41]=i("span",null,"至",-1)),t(r,{modelValue:u.value.weightMax,"onUpdate:modelValue":e[18]||(e[18]=o=>u.value.weightMax=o),placeholder:"最大值"},null,8,["modelValue"])])])])])])])]),_:1},8,["modelValue"])])}}}),Nl=xt(ml,[["__scopeId","data-v-47c71176"]]);export{Nl as default};
