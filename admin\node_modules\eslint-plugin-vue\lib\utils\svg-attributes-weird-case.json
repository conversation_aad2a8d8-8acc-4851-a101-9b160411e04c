["accent-height", "alignment-baseline", "arabic-form", "attributeName", "attributeType", "baseFrequency", "baseline-shift", "baseProfile", "calcMode", "cap-height", "clipPathUnits", "clip-path", "clip-rule", "color-interpolation", "color-interpolation-filters", "color-profile", "color-rendering", "contentScriptType", "contentStyleType", "diffuseConstant", "dominant-baseline", "edgeMode", "enable-background", "externalResourcesRequired", "fill-opacity", "fill-rule", "filterRes", "filterUnits", "flood-color", "flood-opacity", "font-family", "font-size", "font-size-adjust", "font-stretch", "font-style", "font-variant", "font-weight", "glyph-name", "glyph-orientation-horizontal", "glyph-orientation-vertical", "glyphRef", "gradientTransform", "gradientUnits", "horiz-adv-x", "horiz-origin-x", "image-rendering", "kernelMatrix", "kernelUnitLength", "keyPoints", "keySplines", "keyTimes", "lengthAdjust", "letter-spacing", "lighting-color", "limitingConeAngle", "marker-end", "marker-mid", "marker-start", "markerHeight", "markerUnits", "marker<PERSON>id<PERSON>", "maskContentUnits", "maskUnits", "numOctaves", "overline-position", "overline-thickness", "panose-1", "paint-order", "<PERSON><PERSON><PERSON><PERSON>", "patternContentUnits", "patternTransform", "patternUnits", "pointer-events", "pointsAtX", "pointsAtY", "pointsAtZ", "preserveAlpha", "preserveAspectRatio", "primitiveUnits", "referrerPolicy", "refX", "refY", "rendering-intent", "repeatCount", "repeatDur", "requiredExtensions", "requiredFeatures", "shape-rendering", "specularConstant", "specularExponent", "spreadMethod", "startOffset", "stdDeviation", "stitchTiles", "stop-color", "stop-opacity", "strikethrough-position", "strikethrough-thickness", "stroke-dasharray", "stroke-dashoffset", "stroke-linecap", "stroke-linejoin", "stroke-miterlimit", "stroke-opacity", "stroke-width", "surfaceScale", "systemLanguage", "tableValues", "targetX", "targetY", "text-anchor", "text-decoration", "text-rendering", "textLength", "transform-origin", "underline-position", "underline-thickness", "unicode-bidi", "unicode-range", "units-per-em", "v-alphabetic", "v-hanging", "v-ideographic", "v-mathematical", "vector-effect", "vert-adv-y", "vert-origin-x", "vert-origin-y", "viewBox", "viewTarget", "word-spacing", "writing-mode", "x-height", "xChannelSelector", "yChannelSelector", "zoomAndPan"]