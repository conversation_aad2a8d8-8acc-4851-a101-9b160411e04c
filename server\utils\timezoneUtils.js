/**
 * 时区工具类
 * 使用全局时区配置模块提供的时区设置
 */
const moment = require('moment-timezone');
const timezoneConfig = require('./timezoneConfig');

/**
 * 获取当前时间（UTC时区）
 * @returns {moment.Moment} 当前UTC时间
 */
function getCurrentTime() {
  return moment.utc();
}

/**
 * 获取今天的开始时间（UTC时区）
 * @returns {moment.Moment} 今天的开始时间
 */
function getTodayStart() {
  return moment.utc().startOf('day');
}

/**
 * 获取今天的结束时间（UTC时区）
 * @returns {moment.Moment} 今天的结束时间
 */
function getTodayEnd() {
  return moment.utc().endOf('day');
}

/**
 * 获取本周的开始时间（UTC时区，周一开始）
 * @returns {moment.Moment} 本周的开始时间
 */
function getWeekStart() {
  return moment.utc().startOf('isoWeek');
}

/**
 * 获取本周的结束时间（UTC时区，周日结束）
 * @returns {moment.Moment} 本周的结束时间
 */
function getWeekEnd() {
  return moment.utc().endOf('isoWeek');
}

/**
 * 获取本月的开始时间（UTC时区）
 * @returns {moment.Moment} 本月的开始时间
 */
function getMonthStart() {
  return moment.utc().startOf('month');
}

/**
 * 获取本月的结束时间（UTC时区）
 * @returns {moment.Moment} 本月的结束时间
 */
function getMonthEnd() {
  return moment.utc().endOf('month');
}

/**
 * 将日期时间转换为系统时区
 * @param {Date|string|moment.Moment} date 日期时间
 * @returns {moment.Moment} 转换后的日期时间
 */
function convertToSystemTimezone(date) {
  if (!date) {
    console.warn('convertToSystemTimezone: 传入的日期为空');
    return null;
  }

  // 方案5.1：从UTC转换为系统时区
  const systemTimezone = timezoneConfig.getTimezone();
  return moment.utc(date).utcOffset(systemTimezone);
}

/**
 * 将UTC时间转换为指定时区
 * @param {Date|string|moment.Moment} utcDate UTC时间
 * @param {string} targetTimezone 目标时区偏移，如'+08:00'
 * @returns {moment.Moment} 转换后的时间
 */
function convertFromUTC(utcDate, targetTimezone) {
  return moment.utc(utcDate).utcOffset(targetTimezone);
}

/**
 * 将日期时间转换为UTC（简化版）
 * @param {Date|string|moment.Moment} date 日期时间
 * @returns {moment.Moment} 转换后的日期时间
 */
function convertToUTC(date) {
  return moment(date).utc();
}

/**
 * 格式化日期时间（UTC时区）
 * @param {Date|string|moment.Moment} date 日期时间
 * @param {string} format 格式
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  return moment.utc(date).format(format);
}

/**
 * 获取指定时间段的开始和结束时间（简化版，使用服务器本地时区）
 * @param {string} period 时间段，可选值：today, week, month, all
 * @returns {{start: moment.Moment, end: moment.Moment}} 开始和结束时间
 */
function getPeriodRange(period) {
  switch (period) {
    case 'today':
      return {
        start: getTodayStart(),
        end: getTodayEnd()
      };
    case 'week':
      return {
        start: getWeekStart(),
        end: getWeekEnd()
      };
    case 'month':
      return {
        start: getMonthStart(),
        end: getMonthEnd()
      };
    case 'all':
    default:
      return {
        start: moment(0), // 1970-01-01
        end: getCurrentTime()
      };
  }
}

/**
 * 获取系统时区设置
 * 使用全局时区配置模块提供的时区设置
 * @returns {Promise<string>} 系统时区设置
 */
async function getSystemTimezone() {
  // 直接使用全局时区配置模块提供的时区设置
  return timezoneConfig.getTimezone();
}

/**
 * 获取服务器本地时区偏移量
 * @returns {string} 服务器本地时区偏移量，格式为 +/-HH:MM
 */
function getServerTimezoneOffset() {
  // 直接使用全局时区配置模块提供的本地时区偏移量
  return timezoneConfig.getLocalTimezoneOffset();
}

/**
 * 获取服务器时区信息
 * @returns {Object} 服务器时区信息
 */
function getServerTimezoneInfo() {
  const now = new Date();
  const timezoneOffset = now.getTimezoneOffset();
  const hours = Math.abs(Math.floor(timezoneOffset / 60));
  const minutes = Math.abs(timezoneOffset % 60);
  const sign = timezoneOffset <= 0 ? '+' : '-';

  // 格式化为 +/-HH:MM 格式
  const formattedOffset = `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

  return {
    offset: formattedOffset,
    offsetMinutes: timezoneOffset,
    localTime: now.toString()
  };
}

module.exports = {
  getCurrentTime,
  getTodayStart,
  getTodayEnd,
  getWeekStart,
  getWeekEnd,
  getMonthStart,
  getMonthEnd,
  convertToSystemTimezone,
  convertToUTC,
  convertFromUTC, // 新增UTC转换方法
  formatDate,
  getPeriodRange,
  getSystemTimezone,
  getServerTimezoneOffset,
  getServerTimezoneInfo
};
