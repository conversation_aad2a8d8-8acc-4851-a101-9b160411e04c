{"version": 3, "sources": ["../../../src/dialects/mysql/index.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst AbstractDialect = require('../abstract');\nconst ConnectionManager = require('./connection-manager');\nconst Query = require('./query');\nconst QueryGenerator = require('./query-generator');\nconst DataTypes = require('../../data-types').mysql;\nconst { MySQLQueryInterface } = require('./query-interface');\n\nclass MysqlDialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new ConnectionManager(this, sequelize);\n    this.queryGenerator = new QueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n    this.queryInterface = new MySQLQueryInterface(\n      sequelize,\n      this.queryGenerator\n    );\n  }\n\n  canBackslashEscape() {\n    return true;\n  }\n}\n\nMysqlDialect.prototype.supports = _.merge(\n  _.cloneDeep(AbstractDialect.prototype.supports),\n  {\n    'VALUES ()': true,\n    'LIMIT ON UPDATE': true,\n    lock: true,\n    forShare: 'LOCK IN SHARE MODE',\n    settingIsolationLevelDuringTransaction: false,\n    inserts: {\n      ignoreDuplicates: ' IGNORE',\n      updateOnDuplicate: ' ON DUPLICATE KEY UPDATE'\n    },\n    index: {\n      collate: false,\n      length: true,\n      parser: true,\n      type: true,\n      using: 1\n    },\n    constraints: {\n      dropConstraint: false,\n      check: false\n    },\n    indexViaAlter: true,\n    indexHints: true,\n    NUMERIC: true,\n    GEOMETRY: true,\n    JSON: true,\n    REGEXP: true\n  }\n);\n\nMysqlDialect.prototype.defaultVersion = '5.7.0'; // minimum supported version\nMysqlDialect.prototype.Query = Query;\nMysqlDialect.prototype.QueryGenerator = QueryGenerator;\nMysqlDialect.prototype.DataTypes = DataTypes;\nMysqlDialect.prototype.name = 'mysql';\nMysqlDialect.prototype.TICK_CHAR = '`';\nMysqlDialect.prototype.TICK_CHAR_LEFT = MysqlDialect.prototype.TICK_CHAR;\nMysqlDialect.prototype.TICK_CHAR_RIGHT = MysqlDialect.prototype.TICK_CHAR;\n\nmodule.exports = MysqlDialect;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,oBAAoB,QAAQ;AAClC,MAAM,QAAQ,QAAQ;AACtB,MAAM,iBAAiB,QAAQ;AAC/B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,wBAAwB,QAAQ;AAExC,2BAA2B,gBAAgB;AAAA,EACzC,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACrD,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,UAAU;AAAA,MACV;AAAA;AAEF,SAAK,iBAAiB,IAAI,oBACxB,WACA,KAAK;AAAA;AAAA,EAIT,qBAAqB;AACnB,WAAO;AAAA;AAAA;AAIX,aAAa,UAAU,WAAW,EAAE,MAClC,EAAE,UAAU,gBAAgB,UAAU,WACtC;AAAA,EACE,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,wCAAwC;AAAA,EACxC,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,mBAAmB;AAAA;AAAA,EAErB,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA;AAAA,EAET,aAAa;AAAA,IACX,gBAAgB;AAAA,IAChB,OAAO;AAAA;AAAA,EAET,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA;AAIZ,aAAa,UAAU,iBAAiB;AACxC,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,iBAAiB;AACxC,aAAa,UAAU,YAAY;AACnC,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,YAAY;AACnC,aAAa,UAAU,iBAAiB,aAAa,UAAU;AAC/D,aAAa,UAAU,kBAAkB,aAAa,UAAU;AAEhE,OAAO,UAAU;", "names": []}