{
    <%@page import="java.util.LinkedHashMap"%>
<%@page import="java.util.Map"%>
<%@page import="cn.test.utils.SignUtil"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.io.*" %>
<%@ page import="cn.test.utils.SignAPI" %>

<%
        request.setCharacterEncoding("UTF-8");
        String tradeResult = (String) request.getParameter("tradeResult");
        String mchId = (String) request.getParameter("mchId");
        String merchant_key = "";//支付秘钥，商户后台可以查看
        String mchOrderNo  = (String) request.getParameter("mchOrderNo");
        String oriAmount = (String) request.getParameter("oriAmount");    
        String amount = (String) request.getParameter("amount");
        String orderDate= (String) request.getParameter("orderDate");
        String orderNo = (String) request.getParameter("orderNo");
        String sign = (String) request.getParameter("sign");
        String signType = (String) request.getParameter("signType");
        Map<String,String> params = new LinkedHashMap<String,String>();
        params.put("tradeResult", tradeResult);
        params.put("mchId", mchId);
        params.put("mchOrderNo", mchOrderNo);
        params.put("oriAmount", oriAmount);
        params.put("amount", amount);
        params.put("orderDate", orderDate);
        params.put("orderNo", orderNo);


         String signStr = SignUtil.sortData(params);

        String signInfo =signStr.toString();
        System.out.println("签名参数排序：" + signInfo.length() + " -->" + signInfo);        
        System.out.println("签名：" + sign.length() + " -->" + sign);                                
        boolean result = false;

        if("MD5".equals(signType)){                    
            result = SignAPI.validateSignByKey(signInfo, merchant_key, sign);
        }

        PrintWriter pw = response.getWriter();
        if(result){
            pw.print("success");
            System.out.println("验签结果result的值：" + result + " -->success");
        }else{
            pw.print("Signature Error");
            System.out.println("验签结果result的值：" + result + " -->Signature Error");
        }
        System.out.println("---------------------------------------------------------------------------------------------------------------------------------------------");         

%>

    }
  }