/**
 * 数据库时区检查脚本
 * 用于检查数据库的时区设置
 */
const sequelize = require('../config/database');
const moment = require('moment-timezone');
const timezoneUtils = require('../utils/timezoneUtils');

async function checkDatabaseTimezone() {
  try {
    console.log('开始检查数据库时区设置...');
    
    // 获取系统时区设置
    const systemTimezone = await timezoneUtils.getSystemTimezone();
    console.log(`系统配置的时区: ${systemTimezone}`);
    
    // 获取数据库会话时区
    const [sessionResult] = await sequelize.query('SELECT @@session.time_zone AS session_timezone');
    console.log(`数据库会话时区: ${sessionResult[0].session_timezone}`);
    
    // 获取数据库系统时区
    const [systemResult] = await sequelize.query('SELECT @@system_time_zone AS system_timezone');
    console.log(`数据库系统时区: ${systemResult[0].system_timezone}`);
    
    // 获取数据库全局时区
    const [globalResult] = await sequelize.query('SELECT @@global.time_zone AS global_timezone');
    console.log(`数据库全局时区: ${globalResult[0].global_timezone}`);
    
    // 获取当前数据库时间
    const [timeResult] = await sequelize.query('SELECT NOW() AS now, UTC_TIMESTAMP() AS utc_now');
    console.log(`数据库当前时间: ${timeResult[0].now}`);
    console.log(`数据库UTC时间: ${timeResult[0].utc_now}`);
    
    // 计算时差
    const dbTime = moment(timeResult[0].now);
    const utcTime = moment(timeResult[0].utc_now);
    const diffHours = dbTime.diff(utcTime, 'hours');
    const diffMinutes = dbTime.diff(utcTime, 'minutes') % 60;
    
    console.log(`数据库时间与UTC时间的差异: ${diffHours}小时${diffMinutes}分钟`);
    
    // 检查时区一致性
    if (sessionResult[0].session_timezone === systemTimezone) {
      console.log('✅ 数据库会话时区与系统配置时区一致');
    } else {
      console.log(`❌ 数据库会话时区(${sessionResult[0].session_timezone})与系统配置时区(${systemTimezone})不一致`);
    }
    
    // 尝试设置会话时区
    await sequelize.query(`SET time_zone = '${systemTimezone}'`);
    const [newSessionResult] = await sequelize.query('SELECT @@session.time_zone AS session_timezone');
    console.log(`设置后的数据库会话时区: ${newSessionResult[0].session_timezone}`);
    
    // 获取设置后的当前数据库时间
    const [newTimeResult] = await sequelize.query('SELECT NOW() AS now');
    console.log(`设置后的数据库当前时间: ${newTimeResult[0].now}`);
    
    console.log('数据库时区检查完成');
  } catch (error) {
    console.error('检查数据库时区失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行检查
checkDatabaseTimezone()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
