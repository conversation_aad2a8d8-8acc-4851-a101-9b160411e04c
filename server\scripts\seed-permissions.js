require('dotenv').config();
const { Permission } = require('../models');

const permissions = [
  // 首页
  { name: '首页查看', description: '查看首页数据', module: 'dashboard', action: 'view' },
  
  // 会员管理
  { name: '会员列表查看', description: '查看会员列表', module: 'members', action: 'view' },
  { name: '会员详情查看', description: '查看会员详情', module: 'members', action: 'detail' },
  { name: '会员编辑', description: '编辑会员信息', module: 'members', action: 'edit' },
  { name: '会员删除', description: '删除会员', module: 'members', action: 'delete' },
  
  // 充值订单
  { name: '充值订单查看', description: '查看充值订单列表', module: 'deposits', action: 'view' },
  { name: '充值订单详情', description: '查看充值订单详情', module: 'deposits', action: 'detail' },
  { name: '充值订单审核', description: '审核充值订单', module: 'deposits', action: 'audit' },
  
  // 用户投资
  { name: '用户投资查看', description: '查看用户投资列表', module: 'investments', action: 'view' },
  { name: '用户投资详情', description: '查看用户投资详情', module: 'investments', action: 'detail' },
  
  // 提现记录
  { name: '提现记录查看', description: '查看提现记录列表', module: 'withdrawals', action: 'view' },
  { name: '提现记录详情', description: '查看提现记录详情', module: 'withdrawals', action: 'detail' },
  { name: '提现审核', description: '审核提现申请', module: 'withdrawals', action: 'audit' },
  
  // 用户交易
  { name: '用户交易查看', description: '查看用户交易列表', module: 'transactions', action: 'view' },
  { name: '用户交易详情', description: '查看用户交易详情', module: 'transactions', action: 'detail' },
  
  // 佣金记录
  { name: '佣金记录查看', description: '查看佣金记录列表', module: 'commissions', action: 'view' },
  { name: '佣金记录详情', description: '查看佣金记录详情', module: 'commissions', action: 'detail' },
  
  // 用户银行卡
  { name: '用户银行卡查看', description: '查看用户银行卡列表', module: 'bankcards', action: 'view' },
  { name: '用户银行卡详情', description: '查看用户银行卡详情', module: 'bankcards', action: 'detail' },
  { name: '用户银行卡审核', description: '审核用户银行卡', module: 'bankcards', action: 'audit' },
  
  // 收款银行卡
  { name: '收款银行卡查看', description: '查看收款银行卡列表', module: 'receivingcards', action: 'view' },
  { name: '收款银行卡添加', description: '添加收款银行卡', module: 'receivingcards', action: 'add' },
  { name: '收款银行卡编辑', description: '编辑收款银行卡', module: 'receivingcards', action: 'edit' },
  { name: '收款银行卡删除', description: '删除收款银行卡', module: 'receivingcards', action: 'delete' },
  
  // 代理管理
  { name: '代理管理查看', description: '查看代理列表', module: 'agents', action: 'view' },
  { name: '代理详情查看', description: '查看代理详情', module: 'agents', action: 'detail' },
  { name: '代理编辑', description: '编辑代理信息', module: 'agents', action: 'edit' },
  { name: '代理删除', description: '删除代理', module: 'agents', action: 'delete' },
  
  // 系统设置
  { name: '系统设置查看', description: '查看系统设置', module: 'settings', action: 'view' },
  { name: '系统设置编辑', description: '编辑系统设置', module: 'settings', action: 'edit' },
  
  // 通知消息
  { name: '通知消息查看', description: '查看通知消息列表', module: 'notifications', action: 'view' },
  { name: '通知消息添加', description: '添加通知消息', module: 'notifications', action: 'add' },
  { name: '通知消息编辑', description: '编辑通知消息', module: 'notifications', action: 'edit' },
  { name: '通知消息删除', description: '删除通知消息', module: 'notifications', action: 'delete' },
  
  // 角色管理
  { name: '角色管理查看', description: '查看角色列表', module: 'roles', action: 'view' },
  { name: '角色添加', description: '添加角色', module: 'roles', action: 'add' },
  { name: '角色编辑', description: '编辑角色', module: 'roles', action: 'edit' },
  { name: '角色删除', description: '删除角色', module: 'roles', action: 'delete' },
  
  // 管理员设置
  { name: '管理员设置查看', description: '查看管理员列表', module: 'admins', action: 'view' },
  { name: '管理员添加', description: '添加管理员', module: 'admins', action: 'add' },
  { name: '管理员编辑', description: '编辑管理员', module: 'admins', action: 'edit' },
  { name: '管理员删除', description: '删除管理员', module: 'admins', action: 'delete' },
];

async function seedPermissions() {
  try {
    // 清空权限表
    await Permission.destroy({ where: {} });
    console.log('权限表已清空');
    
    // 插入权限数据
    await Permission.bulkCreate(permissions);
    console.log('权限数据插入成功');
    
    process.exit(0);
  } catch (error) {
    console.error('插入权限数据失败:', error);
    process.exit(1);
  }
}

seedPermissions();
