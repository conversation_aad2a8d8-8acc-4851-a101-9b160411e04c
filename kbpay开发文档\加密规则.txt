加密规则
1、将所有需要签名的字段按照 ASCII 码从a到z进行升序排序，并按照按照 k=v&k=v 的格式拼接字符串，生成待签名 query_string 字符串。query_string不要url_encode! 不要url_encode! 不要url_encode!
2、对生成的 query_string 字符串拼接&key="密钥"，进行 MD5 签名，得到小写签名串。（代收和代付的分别是不同的密钥）
3、除了sign以外不为空的参数都需要参与签名。
♥ 签名go Demo
unescape := `merchant_no=6022012&order_amount=116.00&order_no=9815224257&trade_amount=116.00&trade_status=2&timestamp=1656252693&trade_no=R16562506264566045`
sign_str := unescape + "&key=" + "7n4sl3BNvpkyrrSoQKTHXg16CdJdhVf6"
fmt.Println(util.Md5(sign_str))
// sign输出为：fecb85c6120c1bb4fd44d27712a553ff
