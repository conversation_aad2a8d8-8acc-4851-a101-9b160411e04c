# 页面顶部遮挡问题修复总结

## 📋 **问题描述**

用户反馈移动端的投资页面、银行卡页面、交易记录页面顶部被遮挡，显示不完整。经过检查发现是导航栏布局不一致导致的问题。

## 🔍 **问题分析**

### **根本原因**
不同页面的导航栏样式设置不一致，且没有正确处理安全区域适配，导致内容区域被固定定位的导航栏遮挡：

1. **投资页面 (investments/index.vue)**:
   - 导航栏没有设置 `position: fixed`
   - 内容区域没有考虑导航栏高度偏移

2. **银行卡页面 (bankCard/index.vue)**:
   - 导航栏设置了 `position: fixed`，但没有考虑安全区域
   - 内容区域 `margin-top: 0` 导致被遮挡
   - 缺少 `safe-area-top` 类

3. **交易记录页面 (transactions/index.vue)**:
   - 导航栏设置了 `position: fixed`，但没有考虑安全区域
   - 内容区域 `margin-top: 0` 导致被遮挡
   - 缺少 `safe-area-top` 类

4. **充值记录页面 (recharge/records.vue)**:
   - 导航栏设置了 `position: relative`
   - 内容区域没有考虑导航栏高度

5. **提现记录页面 (withdraw/records.vue)**:
   - 导航栏设置了 `position: relative`
   - 内容区域没有考虑导航栏高度

### **安全区域问题**
关键问题是没有正确处理 `env(safe-area-inset-top)` 安全区域，导致固定导航栏被状态栏遮挡。

## ✅ **修复方案**

### **最终解决方案**
经过调试发现，问题的根源是多重安全区域处理冲突。最终采用以下统一方案：

### **统一导航栏样式**
将所有页面的导航栏样式统一为固定定位，并正确处理安全区域：

```scss
.custom-header {
  background-color: $fox-bg-color-dark;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: fixed;  // 统一设置为固定定位
  top: env(safe-area-inset-top);  // 关键：相对于屏幕顶部定位
  left: 0;
  right: 0;
  z-index: 1000;  // 提高层级确保在最上层
  min-height: 120rpx;
}
```

### **统一内容区域间距**
内容区域需要考虑导航栏高度 + 安全区域：

```scss
.content-container {
  margin-top: calc(180rpx + env(safe-area-inset-top));
  padding: 20rpx 30rpx 0;
}
```

### **统一标题样式**
确保所有页面的标题样式一致：

```scss
.custom-header-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  color: $fox-primary-color;
  font-size: 38rpx;
  font-weight: 500;
  pointer-events: none;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
```

### **统一返回按钮样式**
确保所有页面的返回按钮样式一致：

```scss
.back-button-wrapper {
  position: relative;
  z-index: 10;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

### **统一占位符样式**
确保所有页面的占位符样式一致：

```scss
.custom-header-placeholder {
  width: 60rpx;
  height: 60rpx;
  visibility: hidden;
}
```

## 🔧 **具体修改内容**

### **1. 投资页面 (mobile/pages/investments/index.vue)**
- 导航栏添加 `position: fixed` 等固定定位样式
- 标题样式改为绝对定位居中
- 返回按钮样式统一
- 内容区域添加 `margin-top: 120rpx`

### **2. 银行卡页面 (mobile/pages/bankCard/index.vue)**
- 内容区域 `.bank-cards-container` 添加 `margin-top: 120rpx`

### **3. 交易记录页面 (mobile/pages/transactions/index.vue)**
- 内容区域 `.transaction-list` 添加 `margin-top: 120rpx`

### **4. 充值记录页面 (mobile/pages/recharge/records.vue)**
- 导航栏改为固定定位
- 内容区域 `.records-container` 添加 `margin-top: 120rpx`

### **5. 提现记录页面 (mobile/pages/withdraw/records.vue)**
- 导航栏改为固定定位
- 内容区域 `.records-container` 添加 `margin-top: 120rpx`

## 📱 **修复效果**

修复后的效果：
- ✅ 所有页面导航栏样式统一
- ✅ 内容区域不再被导航栏遮挡
- ✅ 页面顶部显示完整
- ✅ 保持了安全区域适配
- ✅ 维持了页面间的一致性

## 🎯 **技术要点**

1. **固定定位导航栏**: 使用 `position: fixed` 确保导航栏始终在顶部
2. **内容区域偏移**: 使用 `margin-top: 120rpx` 为固定导航栏留出空间
3. **层级管理**: 使用 `z-index` 确保导航栏在最上层
4. **响应式适配**: 保持了原有的安全区域适配机制

## 🔄 **后续维护**

为避免类似问题再次出现，建议：
1. 新增页面时参考现有页面的导航栏样式
2. 统一使用固定定位的导航栏
3. 确保内容区域有足够的顶部间距
4. 定期检查页面布局的一致性

## 📝 **相关文件**

修改的文件列表：
- `mobile/pages/investments/index.vue`
- `mobile/pages/bankCard/index.vue`
- `mobile/pages/transactions/index.vue`
- `mobile/pages/recharge/records.vue`
- `mobile/pages/withdraw/records.vue`
