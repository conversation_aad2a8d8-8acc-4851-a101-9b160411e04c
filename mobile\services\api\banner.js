/**
 * 轮播图 API 服务
 */
import { get } from '../../utils/request';

/**
 * 获取轮播图列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=10] - 每页数量
 * @param {boolean|number} [params.status] - 状态筛选
 * @returns {Promise<Object>} 轮播图列表数据
 */
export const getBanners = (params = {}) => {
  return get('/mobile/banners', {
    params: {
      page: params.page || 1,
      limit: params.limit || 10,
      status: params.status !== undefined ? params.status : true, // 默认只获取启用状态的轮播图
      ...params
    }
  });
};

/**
 * 获取轮播图详情
 * @param {number} id - 轮播图ID
 * @returns {Promise<Object>} 轮播图详情数据
 */
export const getBanner = (id) => {
  return get(`/mobile/banners/${id}`);
};
