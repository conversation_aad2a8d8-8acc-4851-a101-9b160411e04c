{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/constants.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, Ref } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\n\nexport type TooltipV2Context = {\n  onClose: () => void\n  onDelayOpen: () => void\n  onOpen: () => void\n  contentId: Ref<string>\n  triggerRef: Ref<HTMLElement | null>\n  ns: UseNamespaceReturn\n}\n\nexport type TooltipV2ContentContext = {\n  arrowRef: Ref<HTMLElement | null>\n}\n\nexport const tooltipV2RootKey: InjectionKey<TooltipV2Context> =\n  Symbol('tooltipV2')\n\nexport const tooltipV2ContentKey: InjectionKey<TooltipV2ContentContext> =\n  Symbol('tooltipV2Content')\n\nexport const TOOLTIP_V2_OPEN = 'tooltip_v2.open'\n"], "names": [], "mappings": "AAAY,MAAC,gBAAgB,GAAG,MAAM,CAAC,WAAW,EAAE;AACxC,MAAC,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,EAAE;AAClD,MAAC,eAAe,GAAG;;;;"}