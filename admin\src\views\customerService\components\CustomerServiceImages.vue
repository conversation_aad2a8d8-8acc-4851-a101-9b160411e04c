<template>
  <div class="customer-service-images-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button class="toolbar-button" type="default" @click="fetchData">
          <el-icon><Refresh /></el-icon>刷新
        </el-button>
        <el-button class="toolbar-button" type="success" @click="showAddDialog">
          <el-icon><Plus /></el-icon>添加
        </el-button>
        <el-button class="toolbar-button" type="primary" @click="() => handleEdit()" :disabled="selectedItems.length !== 1">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
        <el-button class="toolbar-button" type="danger" @click="() => handleDelete()" :disabled="selectedItems.length === 0">
          <el-icon><Delete /></el-icon>删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索图片名称"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        />
        <el-button class="search-button" type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 客服图片表格 -->
    <el-card class="table-card">
      <div class="table-wrapper">
        <el-table
          ref="imagesTable"
          v-loading="loading"
          :data="tableData"
          border
          stripe
          style="width: 100%;"
          highlight-current-row
          @selection-change="handleSelectionChange"
          :cell-style="{whiteSpace: 'nowrap', overflow: 'visible'}"
          :header-cell-style="{whiteSpace: 'nowrap', overflow: 'visible'}"
          :show-overflow-tooltip="false"
          table-layout="auto"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="60" sortable />
          <el-table-column label="图片" width="120" align="center">
            <template #default="scope">
              <div class="image-preview-cell">
                <el-image
                  v-if="scope.row.file_path"
                  :src="getFullImageUrl(scope.row.file_path)"
                  :preview-src-list="[getFullImageUrl(scope.row.file_path)]"
                  fit="cover"
                  class="image-preview"
                  style="width: 60px; height: 60px; border-radius: 4px;"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div v-else class="no-image">
                  <el-icon><Picture /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="file_name" label="文件名" min-width="150" show-overflow-tooltip />
          <el-table-column prop="file_path" label="物理路径" min-width="180" show-overflow-tooltip />
          <el-table-column prop="file_size" label="文件大小" width="120" align="center">
            <template #default="scope">
              {{ formatFileSize(scope.row.file_size) }}
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
                :loading="row.statusLoading"
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150" align="center">
            <template #default="{ row, $index }">
              <div class="operation-buttons-container">
                <el-button
                  class="operation-button icon-only"
                  size="small"
                  type="default"
                  @click="() => handleEdit(row)"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>

                <el-button
                  type="danger"
                  size="small"
                  @click="() => handleDelete(row)"
                  class="operation-button icon-only"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="7"
        background
      >
        <template #sizes>
          <el-select
            :model-value="pageSize"
            @change="handleSizeChange"
            class="custom-page-size"
          >
            <el-option
              v-for="item in [10, 20, 50, 100]"
              :key="item"
              :value="item"
              :label="`${item}/页`"
            />
          </el-select>
        </template>
      </el-pagination>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑客服图片' : '添加客服图片'"
      width="550px"
      :close-on-click-modal="false"
      center
    >
      <el-form
        ref="formRef"
        :model="form"
        label-width="100px"
        label-position="left"
        :rules="rules"
      >
        <el-form-item label="图片" prop="attachment_id" required>
          <div class="image-upload-container">
            <div class="image-preview-wrapper" @click="showAttachmentSelector">
              <div class="image-container" v-if="form.file_path">
                <el-image
                  :src="getFullImageUrl(form.file_path)"
                  class="image-preview"
                  :preview-src-list="[getFullImageUrl(form.file_path)]"
                  fit="cover"
                  style="width: 100px; height: 100px; border-radius: 4px;"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="image-overlay">
                  <el-button
                    type="primary"
                    circle
                    size="small"
                    class="preview-button"
                    @click.stop="previewImage"
                  >
                    <el-icon><View /></el-icon>
                  </el-button>
                </div>
              </div>
              <div v-else class="empty-image-container">
                <el-icon class="image-uploader-icon"><Picture /></el-icon>
                <span class="upload-text">点击选择图片</span>
              </div>
            </div>
            <div class="image-actions">
              <el-button type="primary" size="small" @click="showAttachmentSelector">
                <el-icon><Select /></el-icon>从附件选择图片
              </el-button>
              <div class="form-tip" v-if="form.file_path">鼠标移至图片上可预览</div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="文件名" prop="file_name">
          <el-input v-model="form.file_name" placeholder="请输入文件名" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="submitting" @click="handleSubmit">
            <el-icon><Check /></el-icon>确定
          </el-button>
          <el-button @click="dialogVisible = false">
            <el-icon><Close /></el-icon>取消
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附件选择器 -->
    <AttachmentSelector
      v-model:visible="attachmentSelectorVisible"
      :fileType="'image'"
      @select="handleAttachmentSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Refresh, Search, Picture, View, Select, Close, Rank, Check } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { formatDate } from '@/utils/formatters'
import AttachmentSelector from '@/components/AttachmentSelector.vue'
import {
  getCustomerServiceImageList,
  getCustomerServiceImage,
  addCustomerServiceImage,
  updateCustomerServiceImage,
  deleteCustomerServiceImage,
  batchDeleteCustomerServiceImage,
  type CustomerServiceImageData,
  type CustomerServiceImageFormData
} from '@/api/customerServiceImage'

// 定义客服图片类型
interface CustomerServiceImage {
  id: number
  attachment_id: number
  file_name: string
  file_path: string
  file_size: number
  status: number
  created_at: string
  updated_at: string
  statusLoading?: boolean
}

// 客服图片表单类型
interface ImageForm {
  id?: number
  attachment_id?: number
  file_name: string
  file_path: string
  file_size?: number
  status: number
}

// 表格引用
const imagesTable = ref<any>(null)
const formRef = ref<FormInstance | null>(null)

// 状态和加载
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const searchKeyword = ref('')
const selectedItems = ref<CustomerServiceImage[]>([])
const attachmentSelectorVisible = ref(false)

// 表格数据
const tableData = ref<CustomerServiceImage[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 添加/编辑表单
const form = ref<ImageForm>({
  file_name: '',
  file_path: '',
  status: 1
})

// 表单验证规则
const rules = {
  attachment_id: [
    { required: true, message: '请选择图片', trigger: 'change' }
  ],
  file_name: [
    { required: true, message: '请输入文件名', trigger: 'blur' }
  ]
}

// 获取完整图片URL
const getFullImageUrl = (path: string) => {
  if (!path) return ''
  if (path.startsWith('http')) return path
  return `${import.meta.env.VITE_API_BASE_URL}${path}`
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = 0
  while (size >= 1024 && i < units.length - 1) {
    size /= 1024
    i++
  }
  return `${size.toFixed(2)} ${units[i]}`
}

// 处理表格选择改变
const handleSelectionChange = (rows: CustomerServiceImage[]) => {
  selectedItems.value = rows
}

// 处理分页大小改变
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchData()
}

// 处理当前页改变
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchData()
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true

  try {
    const response = await getCustomerServiceImageList({
      page: currentPage.value,
      limit: pageSize.value,
      sort: 'id',
      order: 'desc'
    })

    if (response.code === 200 && response.data) {
      console.log('fetchData - 原始响应数据:', response.data);

      // 确保每个项目都有有效的ID
      const processedItems = response.data.items.map(item => {
        // 确保ID是数字类型
        if (item.id !== undefined && item.id !== null) {
          item.id = Number(item.id);
        }
        return item;
      });

      console.log('fetchData - 处理后的数据:', processedItems);

      tableData.value = processedItems;
      total.value = response.data.total;
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取客服图片列表失败:', error)
    ElMessage.error('获取数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理添加
const showAddDialog = () => {
  isEdit.value = false
  form.value = {
    file_name: '',
    file_path: '',
    status: 1
  }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row?: CustomerServiceImage) => {
  if (!row) {
    if (selectedItems.value.length !== 1) {
      ElMessage.warning('请选择一条记录')
      return
    }
    row = selectedItems.value[0]
  }

  isEdit.value = true
  form.value = {
    id: row.id,
    attachment_id: row.attachment_id,
    file_name: row.file_name,
    file_path: row.file_path,
    file_size: row.file_size,
    status: row.status
  }
  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row?: CustomerServiceImage) => {
  console.log('handleDelete - 传入的row:', row);

  // 检查row是否有效
  if (row) {
    console.log('handleDelete - row.id:', row.id, '类型:', typeof row.id);

    // 如果row存在但id无效，给出提示并返回
    if (row.id === undefined || row.id === null || isNaN(Number(row.id))) {
      console.error('handleDelete - 传入的row对象ID无效:', row);
      ElMessage.error('无效的记录ID');
      return;
    }
  }

  const items = row ? [row] : selectedItems.value
  console.log('handleDelete - 处理的items:', items);

  if (items.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }

  // 检查items中是否有无效的ID
  const hasInvalidId = items.some(item =>
    item === undefined ||
    item === null ||
    item.id === undefined ||
    item.id === null ||
    isNaN(Number(item.id))
  );

  if (hasInvalidId) {
    console.error('handleDelete - items中存在无效的ID:', items);
    ElMessage.error('选中的记录中包含无效的ID');
    return;
  }

  ElMessageBox.confirm(
    `确定要删除选中的${items.length}条记录吗？此操作不可撤销。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      if (items.length === 1) {
        // 单条删除
        // 确保ID是有效的数字
        const id = items[0].id
        console.log('单条删除 - ID:', id, '类型:', typeof id);

        if (id === undefined || id === null || isNaN(Number(id))) {
          console.error('单条删除 - 无效的ID:', id);
          ElMessage.error('无效的ID')
          return
        }

        const numericId = Number(id);
        console.log('单条删除 - 转换后的ID:', numericId, '类型:', typeof numericId);

        const response = await deleteCustomerServiceImage(numericId)
        console.log('单条删除 - 响应:', response);

        if (response.code === 200) {
          ElMessage.success('删除成功')
          fetchData()
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } else {
        // 批量删除
        // 过滤出有效的ID
        console.log('批量删除 - 原始选中的项目:', items);
        console.log('批量删除 - 原始选中的项目类型:', typeof items, Array.isArray(items));

        // 详细记录每个项目的ID和类型
        items.forEach((item, index) => {
          console.log(`批量删除 - 项目[${index}]:`, item);
          console.log(`批量删除 - 项目[${index}].id:`, item.id, typeof item.id);
        });

        // 检查每个项目的ID
        console.log('批量删除 - 检查每个项目的ID:');
        const validItems = [];
        const invalidItems = [];

        for (const item of items) {
          if (item && item.id !== undefined && item.id !== null && !isNaN(Number(item.id))) {
            validItems.push(item);
            console.log(`批量删除 - 有效项目ID: ${item.id}, 类型: ${typeof item.id}`);
          } else {
            invalidItems.push(item);
            console.log(`批量删除 - 无效项目ID: ${item?.id}, 类型: ${typeof item?.id}`);
          }
        }

        console.log(`批量删除 - 有效项目数量: ${validItems.length}, 无效项目数量: ${invalidItems.length}`);

        // 提取有效的ID并转换为数字
        const ids = validItems.map(item => Number(item.id));

        console.log('批量删除 - 过滤后的IDs:', ids);
        console.log('批量删除 - 过滤后的IDs类型:', typeof ids, Array.isArray(ids));
        console.log('批量删除 - 过滤后的IDs JSON:', JSON.stringify({ ids }));

        if (ids.length === 0) {
          ElMessage.error('没有有效的ID可删除')
          return
        }

        try {
          console.log('=== 批量删除 - 开始 ===');
          console.log('批量删除 - 使用单个删除API逐个删除');

          // 检查每个ID的类型
          console.log('批量删除 - 检查每个ID的类型:');
          ids.forEach((id, index) => {
            console.log(`批量删除 - ID[${index}]:`, id, typeof id, isNaN(id) ? '无效' : '有效');
          });

          // 使用单个删除API逐个删除
          let successCount = 0;
          let failCount = 0;

          for (const id of ids) {
            try {
              console.log(`批量删除 - 正在删除ID: ${id}, 类型: ${typeof id}`);
              // 确保ID是数字类型
              const numericId = Number(id);
              console.log(`批量删除 - 转换后的ID: ${numericId}, 类型: ${typeof numericId}`);

              if (isNaN(numericId)) {
                console.error(`批量删除 - ID ${id} 无效，无法转换为数字`);
                failCount++;
                continue;
              }

              const response = await deleteCustomerServiceImage(numericId);
              console.log(`批量删除 - 删除ID ${numericId} 响应:`, response);

              if (response.code === 200) {
                successCount++;
              } else {
                failCount++;
                console.error(`批量删除 - 删除ID ${numericId} 失败:`, response.message);
              }
            } catch (error) {
              failCount++;
              console.error(`批量删除 - 删除ID ${id} 出错:`, error);
            }
          }

          console.log(`批量删除 - 成功: ${successCount}, 失败: ${failCount}`);
          console.log('=== 批量删除 - 结束 ===');

          if (successCount > 0) {
            ElMessage.success(`成功删除${successCount}条记录${failCount > 0 ? `，${failCount}条记录删除失败` : ''}`);
            fetchData();
          } else {
            ElMessage.error('批量删除失败');
          }
        } catch (error) {
          console.error('批量删除请求错误:', error);
          ElMessage.error('批量删除失败，请检查网络连接')
        }
      }
    } catch (error) {
      console.error('删除客服图片失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理状态变更
const handleStatusChange = async (row: CustomerServiceImage) => {
  try {
    row.statusLoading = true
    const response = await updateCustomerServiceImage(row.id, {
      status: row.status
    })

    if (response.code === 200) {
      ElMessage.success(`状态已${row.status === 1 ? '启用' : '禁用'}`)
    } else {
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1
      ElMessage.error(response.message || '更新状态失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    console.error('更新客服图片状态失败:', error)
    ElMessage.error('更新状态失败，请重试')
  } finally {
    row.statusLoading = false
  }
}

// 处理提交
const handleSubmit = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      submitting.value = true

      try {
        const formData: CustomerServiceImageFormData = {
          attachment_id: form.value.attachment_id!,
          file_name: form.value.file_name,
          file_path: form.value.file_path,
          file_size: form.value.file_size,
          status: form.value.status
        }

        let response

        if (isEdit.value) {
          // 编辑
          if (!form.value.id || isNaN(Number(form.value.id))) {
            ElMessage.error('无效的ID')
            return
          }
          response = await updateCustomerServiceImage(Number(form.value.id), formData)
        } else {
          // 添加
          response = await addCustomerServiceImage(formData)
        }

        if (response.code === 200 || response.code === 201) {
          ElMessage.success(isEdit.value ? '编辑成功' : '添加成功')
          dialogVisible.value = false
          fetchData()
        } else {
          ElMessage.error(response.message || (isEdit.value ? '编辑失败' : '添加失败'))
        }
      } catch (error) {
        console.error(isEdit.value ? '编辑客服图片失败:' : '添加客服图片失败:', error)
        ElMessage.error(isEdit.value ? '编辑失败，请重试' : '添加失败，请重试')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 显示附件选择器
const showAttachmentSelector = () => {
  attachmentSelectorVisible.value = true
}

// 处理附件选择
const handleAttachmentSelect = (attachment: any) => {
  if (attachment) {
    form.value.attachment_id = attachment.id
    form.value.file_path = attachment.file_path
    form.value.file_name = attachment.file_name || attachment.filename || '未命名'
    form.value.file_size = attachment.file_size
    form.value.file_type = attachment.file_type || attachment.mime_type?.split('/')[1] || 'unknown'
  }
}

// 预览图片
const previewImage = (event: Event) => {
  event.stopPropagation()
  // 图片预览逻辑已通过el-image组件的preview功能实现
}



// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.customer-service-images-container {
  padding: 5px 8px;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .toolbar-left {
    display: flex;
    gap: 8px;

    .toolbar-button {
      margin-right: 0;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .search-input {
      width: 200px;

      :deep(.el-input__inner) {
        text-align: center;
      }
    }

    .search-button {
      margin-left: 0;
    }
  }
}

/* 表格卡片样式 */
.table-card {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  :deep(.el-card__body) {
    padding: 5px;
  }
}

/* 表格样式 */
.table-wrapper {
  width: 100%;
  overflow-x: auto;

  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  :deep(.el-table) {
    border-radius: 6px;
    overflow: hidden;

    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: bold;
    }

    .el-table__fixed-right::before,
    .el-table__fixed::before {
      display: none;
    }

    .el-table__fixed-right-patch {
      background-color: #f5f7fa;
    }

    .el-table--border .el-table__inner-wrapper::after {
      display: none;
    }

    .cell {
      white-space: nowrap;
    }
  }
}

/* 图片预览单元格 */
.image-preview-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.image-preview {
  object-fit: cover;
  border-radius: 4px;
}

.no-image {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 图片上传容器 */
.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.image-preview-wrapper {
  cursor: pointer;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  width: 102px;
  height: 102px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  &:hover {
    border-color: #409eff;
  }
}

.image-container {
  width: 100%;
  height: 100%;
  position: relative;

  &:hover .image-overlay {
    opacity: 1;
  }
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.empty-image-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #8c939d;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
}

.image-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
}

/* 操作按钮容器 */
.operation-buttons-container {
  display: flex;
  justify-content: center;
  gap: 5px;
}

/* 基本操作按钮样式 */
.operation-button {
  margin: 0 !important;
  padding: 5px 8px !important;
  flex: 0 0 auto;

  &:first-child {
    margin-left: 0 !important;
  }

  &:last-child {
    margin-right: 0 !important;
  }

  &.icon-only {
    padding: 5px !important;
    min-width: 28px;
    height: 28px;
  }
}

/* 图标按钮样式 */
.operation-button .el-icon {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-table__row {
  transition: transform 0.2s, box-shadow 0.2s;
}

.el-table__row:hover {
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
