{"version": 3, "file": "forward-ref.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/forward-ref.tsx"], "sourcesContent": ["import { Fragment, defineComponent, ref } from 'vue'\nimport {\n  buildProps,\n  composeRefs,\n  definePropType,\n  ensureOnlyChild,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes, VNodeArrayChildren } from 'vue'\n\nexport type RefSetter = (el: HTMLElement | null) => void\n\nexport const forwardRefProps = buildProps({\n  setRef: { type: definePropType<RefSetter>(Function), required: true },\n  onlyChild: <PERSON>olean,\n} as const)\n\nexport type ForwardRefProps = ExtractPropTypes<typeof forwardRefProps>\n\n// TODO: consider make this component a reusable component without the only child feature.\nexport default defineComponent({\n  props: forwardRefProps,\n  setup(props, { slots }) {\n    const fragmentRef = ref()\n    const setRef = composeRefs(fragmentRef, (el) => {\n      // vue fragments is represented as a text element.\n      // The first element sibling should be the first element children of fragment.\n      // This is how we get the element.\n      if (el) {\n        props.setRef(\n          (el as HTMLElement).nextElementSibling as HTMLElement | null\n        )\n      } else {\n        props.setRef(null)\n      }\n    })\n    return () => {\n      const [firstChild] = slots.default?.() || []\n      const child = props.onlyChild\n        ? ensureOnlyChild(firstChild.children as VNodeArrayChildren)\n        : firstChild.children\n      // Dunno why the ref for jsx complains about the typing issue which was not\n      // in template\n      return <Fragment ref={setRef as any}>{child}</Fragment>\n    }\n  },\n})\n"], "names": ["forwardRefProps", "buildProps", "setRef", "type", "definePropType", "Function", "required", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "props", "slots", "fragmentRef", "composeRefs", "el", "_createVNode", "_Fragment"], "mappings": ";;;;;AAYaA,MAAAA,eAAe,GAAGC,UAAU,CAAC;AACxCC,EAAAA,MAAM,EAAE;AAAEC,IAAAA,IAAI,EAAEC,cAAc,CAAYC,QAAZ,CAAtB;AAA6CC,IAAAA,QAAQ,EAAE,IAAA;GADvB;AAExCC,EAAAA,SAAS,EAAEC,OAAAA;AAF6B,CAAD,EAAlC;AAOP,iBAAA,eAAA,CAAA;AACA,EAAA,KAAA,EAAA;AACEC,EAAAA,KAAK,MADwB,EAAA;;GAExB,EAAA;AAAUC,IAAAA,MAAAA,WAAAA,GAAAA,GAAAA,EAAAA,CAAAA;AAAF,IAAW,MAAA,MAAA,GAAA,WAAA,CAAA,WAAA,EAAA,CAAA,EAAA,KAAA;MAChBC,IAAAA,EAAAA,EAAAA;AACN,QAAA,YAAeC,CAAAA,EAAAA,CAAAA,kBAAW,CAAA,CAAeC;AACvC,OAAA,MAAA;AACA,QAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA,CAAA;AACEJ,IAAAA,OAAAA,MAAMP;AAGP,MAAA,IAAM,EAAA,CAAA;YACA,CAAA,UAAL,CAAA,GAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AACD,MAAA,MAAA,KAAA,GAAA,KAAA,CAAA,SAAA,GAAA,eAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,UAAA,CAAA,QAAA,CAAA;AACF,MAXD,OAAAY,WAAA,CAAAC,QAAA,EAAA;AAYA,QAAA,KAAa,EAAA,MAAA;AACX,OAAA,EAAA,CAAA,KAAM,CAAA,CAAA,CAAA;AACN,KAAA,CAAA;AAIA,GAAA;;;;;"}