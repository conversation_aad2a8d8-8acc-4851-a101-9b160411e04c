/**
 * 银行卡相关 API 服务
 */
import request from '../request';

/**
 * 获取用户银行卡列表
 * @returns {Promise<Object>} 银行卡列表数据
 */
export function getBankCards() {
  return request({
    url: '/api/account/bank-cards',
    method: 'GET'
  });
}

/**
 * 添加银行卡
 * @param {Object} data - 银行卡数据
 * @param {number} data.bank_id - 银行ID
 * @param {string} data.card_number - 卡号
 * @param {string} data.card_holder - 持卡人姓名
 * @returns {Promise<Object>} 添加结果
 */
export function addBankCard(data) {
  return request({
    url: '/api/account/bank-cards',
    method: 'POST',
    data
  });
}

/**
 * 更新银行卡
 * @param {number} id - 银行卡ID
 * @param {Object} data - 银行卡数据
 * @param {number} data.bank_id - 银行ID
 * @param {string} data.card_number - 卡号
 * @param {string} data.card_holder - 持卡人姓名
 * @returns {Promise<Object>} 更新结果
 */
export function updateBankCard(id, data) {
  return request({
    url: `/api/account/bank-cards/${id}`,
    method: 'PUT',
    data
  });
}

/**
 * 删除银行卡
 * @param {number} id - 银行卡ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteBankCard(id) {
  return request({
    url: `/api/account/bank-cards/${id}`,
    method: 'DELETE'
  });
}

/**
 * 获取银行列表
 * @param {Object} params - 查询参数
 * @param {number} [params.payment_channel_id] - 支付通道ID，用于获取支持的银行列表
 * @returns {Promise<Object>} 银行列表数据
 */
export function getBanks(params) {
  return request({
    url: '/api/account/bank-cards/banks',
    method: 'GET',
    params
  });
}
