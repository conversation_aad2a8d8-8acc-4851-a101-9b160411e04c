Write-Host "修改Nginx配置文件..." -ForegroundColor Green

$NGINX_PATH = "E:\nginx-1.27.5"
$CONF_FILE = "$NGINX_PATH\conf\nginx.conf"

# 备份原始配置
if (Test-Path $CONF_FILE) {
    Copy-Item $CONF_FILE -Destination "$CONF_FILE.bak"
    Write-Host "已备份原始配置文件到 $CONF_FILE.bak" -ForegroundColor Yellow
}

# 创建新的配置内容
$newConfig = @"
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       8080;
        server_name  localhost;

        # 开发服务器
        location / {
            proxy_pass http://localhost:5173;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # API请求 - 使用模拟数据服务器
        location /api/ {
            # 这里使用我们的Express模拟服务器
            proxy_pass http://localhost:3000/api/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # 静态文件
        location /static/ {
            proxy_pass http://localhost:5173/static/;
        }

        # 处理WebSocket连接
        location /sockjs-node/ {
            proxy_pass http://localhost:5173/sockjs-node/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 错误页面
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
"@

# 写入新配置
Set-Content -Path $CONF_FILE -Value $newConfig
Write-Host "已成功修改Nginx配置文件" -ForegroundColor Green

Write-Host "现在，您可以通过以下命令启动Nginx：" -ForegroundColor Cyan
Write-Host "cd $NGINX_PATH" -ForegroundColor Cyan
Write-Host "nginx.exe" -ForegroundColor Cyan

Write-Host "按任意键继续..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
