[{"name": "users-count", "sql": "SELECT COUNT(*) as count FROM users", "duration": "4ms", "rowCount": 1, "explain": [{"id": 1, "select_type": "SIMPLE", "table": "users", "partitions": null, "type": "index", "possible_keys": null, "key": "idx_status", "key_len": "2", "ref": null, "rows": 52, "filtered": 100, "Extra": "Using index"}], "timestamp": "2025-05-22T21:06:29.923Z"}, {"name": "recent-transactions", "sql": "SELECT * FROM transactions ORDER BY created_at DESC LIMIT 100", "duration": "24ms", "rowCount": 100, "explain": [{"id": 1, "select_type": "SIMPLE", "table": "transactions", "partitions": null, "type": "ALL", "possible_keys": null, "key": null, "key_len": null, "ref": null, "rows": 10897, "filtered": 100, "Extra": "Using filesort"}], "timestamp": "2025-05-22T21:06:29.951Z"}, {"name": "user-with-transactions", "sql": "\n        SELECT u.*, COUNT(t.id) as transaction_count \n        FROM users u \n        LEFT JOIN transactions t ON u.id = t.user_id \n        GROUP BY u.id \n        ORDER BY transaction_count DESC \n        LIMIT 10\n      ", "duration": "10ms", "rowCount": 10, "explain": [{"id": 1, "select_type": "SIMPLE", "table": "u", "partitions": null, "type": "index", "possible_keys": "PRIMARY,username,invite_code,idx_user_id,idx_username,idx_invite_code,idx_inviter_id,idx_status,fk_users_level", "key": "PRIMARY", "key_len": "4", "ref": null, "rows": 52, "filtered": 100, "Extra": "Using temporary; Using filesort"}, {"id": 1, "select_type": "SIMPLE", "table": "t", "partitions": null, "type": "ref", "possible_keys": "idx_user_id", "key": "idx_user_id", "key_len": "4", "ref": "fox_db.u.id", "rows": 253, "filtered": 100, "Extra": "Using index"}], "timestamp": "2025-05-22T21:06:29.967Z"}, {"name": "system-params", "sql": "SELECT * FROM system_params", "duration": "5ms", "rowCount": 84, "explain": [{"id": 1, "select_type": "SIMPLE", "table": "system_params", "partitions": null, "type": "ALL", "possible_keys": null, "key": null, "key_len": null, "ref": null, "rows": 84, "filtered": 100, "Extra": null}], "timestamp": "2025-05-22T21:06:29.975Z"}]