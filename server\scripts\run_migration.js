/**
 * 运行迁移脚本
 */
const { up: upPaymentChannels } = require('../migrations/add_fields_to_payment_channels');
const { up: upDeposits } = require('../migrations/add_fields_to_deposits');

async function runMigration() {
  try {
    console.log('开始运行迁移...');

    // 运行支付通道表迁移
    console.log('运行支付通道表迁移...');
    await upPaymentChannels();

    // 运行充值订单表迁移
    console.log('运行充值订单表迁移...');
    await upDeposits();

    console.log('迁移成功完成');
    process.exit(0);
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

runMigration();
