/**
 * 测试应用服务器启动和基础功能
 */

async function testAppStartup() {
  console.log('🚀 测试应用服务器启动...\n');

  try {
    // 1. 测试数据库连接
    console.log('1. 测试数据库连接：');
    const sequelize = require('../config/database');

    await sequelize.authenticate();
    console.log('  ✅ 数据库连接成功');

    // 2. 测试时区配置初始化
    console.log('\n2. 测试时区配置初始化：');
    const timezoneConfig = require('../utils/timezoneConfig');
    
    const systemTimezone = await timezoneConfig.initialize();
    console.log(`  ✅ 时区配置初始化成功: ${systemTimezone}`);

    // 3. 测试模型加载
    console.log('\n3. 测试模型加载：');
    const { User, Investment, InvestmentProfit } = require('../models');
    
    const userCount = await User.count();
    const investmentCount = await Investment.count();
    const profitCount = await InvestmentProfit.count();
    
    console.log(`  ✅ User模型: ${userCount}条记录`);
    console.log(`  ✅ Investment模型: ${investmentCount}条记录`);
    console.log(`  ✅ InvestmentProfit模型: ${profitCount}条记录`);

    // 4. 测试时间工具函数
    console.log('\n4. 测试时间工具函数：');
    const dateUtils = require('../utils/dateUtils');
    
    const currentTime = dateUtils.getCurrentTime();
    const currentUTCTime = dateUtils.getCurrentUTCTime();
    const todayStart = dateUtils.getTodayStart();
    
    console.log(`  ✅ getCurrentTime: ${currentTime.toISOString()}`);
    console.log(`  ✅ getCurrentUTCTime: ${currentUTCTime.toISOString()}`);
    console.log(`  ✅ getTodayStart: ${todayStart.toISOString()}`);

    // 5. 测试收益服务加载
    console.log('\n5. 测试收益服务加载：');
    const profitScheduler = require('../services/profitScheduler');
    
    // 模拟投资数据
    const mockInvestment = {
      id: 999,
      start_time: '2025-06-08 10:00:00',
      last_profit_time: null
    };
    
    const mockProject = {
      profit_time: 24
    };
    
    const nextProfitTime = profitScheduler.calculateNextProfitTime(mockInvestment, mockProject);
    console.log(`  ✅ 收益计算服务: ${nextProfitTime.toISOString()}`);

    await sequelize.close();

    console.log('\n🎉 应用服务器启动测试完成！');
    console.log('\n📋 测试结果：');
    console.log('  ✅ 数据库连接正常');
    console.log('  ✅ 时区配置正常');
    console.log('  ✅ 模型加载正常');
    console.log('  ✅ 时间工具正常');
    console.log('  ✅ 收益服务正常');

    return {
      success: true,
      message: '应用服务器启动测试通过',
      details: {
        databaseConnection: true,
        timezoneConfig: systemTimezone,
        modelCounts: { userCount, investmentCount, profitCount },
        timeUtils: true,
        profitService: true
      }
    };

  } catch (error) {
    console.error('❌ 应用服务器启动测试失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
if (require.main === module) {
  testAppStartup();
}

module.exports = { testAppStartup };
