import request from '@/utils/request'

/**
 * 获取用户列表
 * @param params 查询参数
 */
export function getUsers(params?: any) {
  return request({
    url: '/api/admin/users',
    method: 'get',
    params
  })
}

/**
 * 获取用户详情
 * @param id 用户ID
 */
export function getUser(id: number) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'get'
  })
}

/**
 * 创建用户
 * @param data 用户数据
 */
export function createUser(data: any) {
  return request({
    url: '/api/admin/users',
    method: 'post',
    data
  })
}

/**
 * 更新用户
 * @param id 用户ID
 * @param data 用户数据
 */
export function updateUser(id: number, data: any) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'put',
    data
  })
}

/**
 * 重置用户密码
 * @param id 用户ID
 * @param data 密码数据
 */
export function resetUserPassword(id: number, data: { password: string }) {
  return request({
    url: `/api/admin/users/${id}/password`,
    method: 'put',
    data
  })
}

/**
 * 调整用户余额
 * @param id 用户ID
 * @param data 余额数据
 */
export function adjustUserBalance(id: number, data: { amount: number, type: 'add' | 'subtract', remark?: string }) {
  return request({
    url: `/api/admin/users/${id}/balance`,
    method: 'put',
    data
  })
}

/**
 * 获取用户订单列表
 * @param id 用户ID
 * @param params 查询参数
 */
export function getUserOrders(id: number, params?: any) {
  return request({
    url: `/api/admin/users/${id}/orders`,
    method: 'get',
    params
  })
}
