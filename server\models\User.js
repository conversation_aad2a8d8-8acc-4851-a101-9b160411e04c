const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const sequelize = require('../config/database');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },

  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [3, 50],
    },
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
    },
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: true,
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    unique: true,
    validate: {
      isEmail: true,
    },
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: false,
  },
  country_code: {
    type: DataTypes.STRING(10),
    allowNull: true,
    defaultValue: '+86',
  },
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true,
    defaultValue: 'https://via.placeholder.com/30',
  },
  invite_code: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    comment: '用户的专属邀请码',
  },
  user_id: {
    type: DataTypes.STRING(10),
    allowNull: true,
    unique: true,
    comment: '用户唯一标识',
  },
  inviter_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '邀请人ID',
  },
  level_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '用户级别ID',
  },
  gender: {
    type: DataTypes.ENUM('男', '女'),
    allowNull: true,
    defaultValue: '男',
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  },
  balance: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0.00,
    allowNull: false,
  },
  frozen_amount: {
    type: DataTypes.DECIMAL(15, 2),
    defaultValue: 0.00,
    allowNull: false,
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'blocked'),
    defaultValue: 'active',
    allowNull: false,
    comment: 'active=正常(允许购买), inactive=禁止购买, blocked=已封禁',
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        user.password = await bcrypt.hash(user.password, 10);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        user.password = await bcrypt.hash(user.password, 10);
      }
    },
  },
  scopes: {
    withoutPassword: {
      attributes: { exclude: ['password'] },
    },
  },
});

// 实例方法：验证密码
User.prototype.validatePassword = async function(password) {
  return await bcrypt.compare(password, this.password);
};

// 定义关联关系
User.associate = (models) => {
  // 自关联：用户与邀请人
  User.belongsTo(User, {
    foreignKey: 'inviter_id',
    as: 'inviter'
  });

  // 自关联：用户与下级用户
  User.hasMany(User, {
    foreignKey: 'inviter_id',
    as: 'subordinates'
  });

  // 用户与用户级别
  User.belongsTo(models.UserLevel, {
    foreignKey: 'level_id',
    as: 'level'
  });

  // 用户与账户余额
  User.hasMany(models.AccountBalance, {
    foreignKey: 'user_id',
    as: 'account_balances'
  });

  // 用户与银行卡
  User.hasMany(models.BankCard, {
    foreignKey: 'user_id',
    as: 'bank_cards'
  });

  // 用户与交易记录
  User.hasMany(models.Transaction, {
    foreignKey: 'user_id',
    as: 'transactions'
  });

  // 用户与投资记录
  User.hasMany(models.Investment, {
    foreignKey: 'user_id',
    as: 'investments'
  });

  // 用户与投资收益
  User.hasMany(models.InvestmentProfit, {
    foreignKey: 'user_id',
    as: 'investment_profits'
  });

  // 用户与佣金记录（作为接收者）
  User.hasMany(models.Commission, {
    foreignKey: 'user_id',
    as: 'commissions'
  });

  // 用户与佣金记录（作为来源）
  User.hasMany(models.Commission, {
    foreignKey: 'from_user_id',
    as: 'generated_commissions'
  });

  // 用户与充值订单
  User.hasMany(models.Deposit, {
    foreignKey: 'user_id',
    as: 'deposits'
  });

  // 用户与提现记录
  User.hasMany(models.Withdrawal, {
    foreignKey: 'user_id',
    as: 'withdrawals'
  });
};

module.exports = User;
