/**
 * 用户相关API
 *
 * @deprecated 请使用新的API服务模块：
 * import { user } from '@api';
 * const { login, register, ... } = user;
 */
import { get, post, put, delete as del } from '../utils/request';

/**
 * 用户注册
 * @param {Object} data - 注册数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.name - 姓名
 * @param {string} data.phone - 手机号
 * @param {string} data.email - 邮箱（可选）
 * @param {string} data.invite_code - 邀请码
 * @returns {Promise} - 返回注册结果
 */
export function register(data) {
  return post('/user/register', data);
}

/**
 * 用户登录
 * @param {Object} data - 登录数据
 * @param {string} data.username - 用户名/手机号/邮箱
 * @param {string} data.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export function login(data) {
  return post('/user/login', data);
}

/**
 * 用户登出
 * @returns {Promise} - 返回登出结果
 */
export function logout() {
  return post('/user/logout');
}

/**
 * 获取用户个人信息
 * @returns {Promise} - 返回用户信息
 */
export function getProfile() {
  return get('/user/profile');
}

/**
 * 更新用户密码
 * @param {Object} data - 密码数据
 * @param {string} data.old_password - 旧密码
 * @param {string} data.new_password - 新密码
 * @returns {Promise} - 返回更新结果
 */
export function updatePassword(data) {
  return put('/user/password', data);
}

/**
 * 验证邀请码
 * @param {string} code - 邀请码
 * @returns {Promise} - 返回验证结果
 */
export function validateInviteCode(code) {
  return get(`/user/invite-code/${code}`);
}

/**
 * 获取用户的邀请码
 * @returns {Promise} - 返回邀请码信息
 */
export function getInviteCode() {
  return get('/user/invite-code');
}

/**
 * 创建用户的邀请码
 * @returns {Promise} - 返回创建结果
 */
export function createInviteCode() {
  return post('/user/invite-code');
}

/**
 * 获取用户的邀请列表
 * @param {Object} params - 查询参数
 * @param {number} params.level - 邀请级别（1-3）
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise} - 返回邀请列表
 */
export function getInvitees(params) {
  return get('/user/invitees', { params });
}

// 导出所有API
export default {
  login,
  register,
  logout,
  getProfile,
  updatePassword,
  validateInviteCode,
  getInviteCode,
  createInviteCode,
  getInvitees
};
