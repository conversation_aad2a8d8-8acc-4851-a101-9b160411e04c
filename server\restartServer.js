const profitSystem = require('./services/profitSystem');

async function restartServer() {
  try {
    console.log('重启收益系统...');
    
    // 停止收益系统
    const stopResult = profitSystem.stopProfitSystem();
    console.log('停止收益系统结果:', stopResult);
    
    // 启动收益系统
    const startResult = await profitSystem.startProfitSystem();
    console.log('启动收益系统结果:', startResult);
    
    console.log('收益系统已重启');
    
    process.exit(0);
  } catch (error) {
    console.error('重启收益系统失败:', error);
    process.exit(1);
  }
}

restartServer();
