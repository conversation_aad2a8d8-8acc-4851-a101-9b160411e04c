{"version": 3, "file": "picker.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/common/picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"refPopper\"\n    :visible=\"pickerVisible\"\n    effect=\"light\"\n    pure\n    trigger=\"click\"\n    v-bind=\"$attrs\"\n    role=\"dialog\"\n    teleported\n    :transition=\"`${nsDate.namespace.value}-zoom-in-top`\"\n    :popper-class=\"[`${nsDate.namespace.value}-picker__popper`, popperClass]\"\n    :popper-options=\"elPopperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :stop-popper-mouse-event=\"false\"\n    :hide-after=\"0\"\n    persistent\n    @before-show=\"onBeforeShow\"\n    @show=\"onShow\"\n    @hide=\"onHide\"\n  >\n    <template #default>\n      <el-input\n        v-if=\"!isRangeInput\"\n        :id=\"(id as string | undefined)\"\n        ref=\"inputRef\"\n        container-role=\"combobox\"\n        :model-value=\"(displayValue as string)\"\n        :name=\"name\"\n        :size=\"pickerSize\"\n        :disabled=\"pickerDisabled\"\n        :placeholder=\"placeholder\"\n        :class=\"[nsDate.b('editor'), nsDate.bm('editor', type), $attrs.class]\"\n        :style=\"$attrs.style\"\n        :readonly=\"\n          !editable ||\n          readonly ||\n          isDatesPicker ||\n          isMonthsPicker ||\n          isYearsPicker ||\n          type === 'week'\n        \"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        :validate-event=\"false\"\n        @input=\"onUserInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @keydown=\"handleKeydownInput\"\n        @change=\"handleChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @click.stop\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"nsInput.e('icon')\"\n            @mousedown.prevent=\"onMouseDownInput\"\n            @touchstart.passive=\"onTouchStartInput\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"showClose && clearIcon\"\n            :class=\"`${nsInput.e('icon')} clear-icon`\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </el-input>\n      <picker-range-trigger\n        v-else\n        :id=\"(id as string[] | undefined)\"\n        ref=\"inputRef\"\n        :model-value=\"displayValue\"\n        :name=\"(name as string[] | undefined)\"\n        :disabled=\"pickerDisabled\"\n        :readonly=\"!editable || readonly\"\n        :start-placeholder=\"startPlaceholder\"\n        :end-placeholder=\"endPlaceholder\"\n        :class=\"rangeInputKls\"\n        :style=\"$attrs.style\"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        autocomplete=\"off\"\n        role=\"combobox\"\n        @click=\"onMouseDownInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @start-input=\"handleStartInput\"\n        @start-change=\"handleStartChange\"\n        @end-input=\"handleEndInput\"\n        @end-change=\"handleEndChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @keydown=\"handleKeydownInput\"\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"[nsInput.e('icon'), nsRange.e('icon')]\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #range-separator>\n          <slot name=\"range-separator\">\n            <span :class=\"nsRange.b('separator')\">{{ rangeSeparator }}</span>\n          </slot>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"clearIcon\"\n            :class=\"clearIconKls\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </picker-range-trigger>\n    </template>\n    <template #content>\n      <slot\n        :visible=\"pickerVisible\"\n        :actual-visible=\"pickerActualVisible\"\n        :parsed-value=\"parsedValue\"\n        :format=\"format\"\n        :date-format=\"dateFormat\"\n        :time-format=\"timeFormat\"\n        :unlink-panels=\"unlinkPanels\"\n        :type=\"type\"\n        :default-value=\"defaultValue\"\n        :show-now=\"showNow\"\n        @pick=\"onPick\"\n        @select-range=\"setSelectionRange\"\n        @set-picker-option=\"onSetPickerOption\"\n        @calendar-change=\"onCalendarChange\"\n        @panel-change=\"onPanelChange\"\n        @mousedown.stop\n      />\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  provide,\n  ref,\n  unref,\n  useAttrs,\n  watch,\n} from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { onClickOutside, unrefElement } from '@vueuse/core'\nimport {\n  useEmptyValues,\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport ElInput from '@element-plus/components/input'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { NOOP, debugWarn, isArray } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { Calendar, Clock } from '@element-plus/icons-vue'\nimport { dayOrDaysToDate, formatter, parseDate, valueEquals } from '../utils'\nimport { timePickerDefaultProps } from './props'\nimport PickerRangeTrigger from './picker-range-trigger.vue'\nimport type { InputInstance } from '@element-plus/components/input'\n\nimport type { Dayjs } from 'dayjs'\nimport type { ComponentPublicInstance, Ref } from 'vue'\nimport type { Options } from '@popperjs/core'\nimport type {\n  DateModelType,\n  DayOrDays,\n  PickerOptions,\n  SingleOrRange,\n  TimePickerDefaultProps,\n  UserInput,\n} from './props'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'Picker',\n})\n\nconst props = defineProps(timePickerDefaultProps)\nconst emit = defineEmits([\n  UPDATE_MODEL_EVENT,\n  CHANGE_EVENT,\n  'focus',\n  'blur',\n  'clear',\n  'calendar-change',\n  'panel-change',\n  'visible-change',\n  'keydown',\n])\nconst attrs = useAttrs()\n\nconst { lang } = useLocale()\n\nconst nsDate = useNamespace('date')\nconst nsInput = useNamespace('input')\nconst nsRange = useNamespace('range')\n\nconst { form, formItem } = useFormItem()\nconst elPopperOptions = inject('ElPopperOptions', {} as Options)\nconst { valueOnClear } = useEmptyValues(props, null)\n\nconst refPopper = ref<TooltipInstance>()\nconst inputRef = ref<InputInstance>()\nconst pickerVisible = ref(false)\nconst pickerActualVisible = ref(false)\nconst valueOnOpen = ref<TimePickerDefaultProps['modelValue'] | null>(null)\nlet hasJustTabExitedInput = false\n\nconst { isFocused, handleFocus, handleBlur } = useFocusController(inputRef, {\n  beforeFocus() {\n    return props.readonly || pickerDisabled.value\n  },\n  afterFocus() {\n    pickerVisible.value = true\n  },\n  beforeBlur(event) {\n    return (\n      !hasJustTabExitedInput && refPopper.value?.isFocusInsideContent(event)\n    )\n  },\n  afterBlur() {\n    handleChange()\n    pickerVisible.value = false\n    hasJustTabExitedInput = false\n    props.validateEvent &&\n      formItem?.validate('blur').catch((err) => debugWarn(err))\n  },\n})\n\nconst rangeInputKls = computed(() => [\n  nsDate.b('editor'),\n  nsDate.bm('editor', props.type),\n  nsInput.e('wrapper'),\n  nsDate.is('disabled', pickerDisabled.value),\n  nsDate.is('active', pickerVisible.value),\n  nsRange.b('editor'),\n  pickerSize ? nsRange.bm('editor', pickerSize.value) : '',\n  attrs.class,\n])\n\nconst clearIconKls = computed(() => [\n  nsInput.e('icon'),\n  nsRange.e('close-icon'),\n  !showClose.value ? nsRange.e('close-icon--hidden') : '',\n])\n\nwatch(pickerVisible, (val) => {\n  if (!val) {\n    userInput.value = null\n    nextTick(() => {\n      emitChange(props.modelValue)\n    })\n  } else {\n    nextTick(() => {\n      if (val) {\n        valueOnOpen.value = props.modelValue\n      }\n    })\n  }\n})\nconst emitChange = (\n  val: TimePickerDefaultProps['modelValue'] | null,\n  isClear?: boolean\n) => {\n  // determine user real change only\n  if (isClear || !valueEquals(val, valueOnOpen.value)) {\n    emit(CHANGE_EVENT, val)\n    // Set the value of valueOnOpen when clearing to avoid triggering change events multiple times.\n    isClear && (valueOnOpen.value = val)\n    props.validateEvent &&\n      formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n}\nconst emitInput = (input: SingleOrRange<DateModelType> | null) => {\n  if (!valueEquals(props.modelValue, input)) {\n    let formatted\n    if (isArray(input)) {\n      formatted = input.map((item) =>\n        formatter(item, props.valueFormat, lang.value)\n      )\n    } else if (input) {\n      formatted = formatter(input, props.valueFormat, lang.value)\n    }\n    emit(UPDATE_MODEL_EVENT, input ? formatted : input, lang.value)\n  }\n}\nconst emitKeydown = (e: KeyboardEvent) => {\n  emit('keydown', e)\n}\n\nconst refInput = computed<HTMLInputElement[]>(() => {\n  if (inputRef.value) {\n    return Array.from<HTMLInputElement>(\n      inputRef.value.$el.querySelectorAll('input')\n    )\n  }\n  return []\n})\n\n// @ts-ignore\nconst setSelectionRange = (start: number, end: number, pos?: 'min' | 'max') => {\n  const _inputs = refInput.value\n  if (!_inputs.length) return\n  if (!pos || pos === 'min') {\n    _inputs[0].setSelectionRange(start, end)\n    _inputs[0].focus()\n  } else if (pos === 'max') {\n    _inputs[1].setSelectionRange(start, end)\n    _inputs[1].focus()\n  }\n}\n\nconst onPick = (date: any = '', visible = false) => {\n  pickerVisible.value = visible\n  let result\n  if (isArray(date)) {\n    result = date.map((_) => _.toDate())\n  } else {\n    // clear btn emit null\n    result = date ? date.toDate() : date\n  }\n  userInput.value = null\n  emitInput(result)\n}\n\nconst onBeforeShow = () => {\n  pickerActualVisible.value = true\n}\n\nconst onShow = () => {\n  emit('visible-change', true)\n}\n\nconst onHide = () => {\n  pickerActualVisible.value = false\n  pickerVisible.value = false\n  emit('visible-change', false)\n}\n\nconst handleOpen = () => {\n  pickerVisible.value = true\n}\n\nconst handleClose = () => {\n  pickerVisible.value = false\n}\n\nconst pickerDisabled = computed(() => {\n  return props.disabled || form?.disabled\n})\n\nconst parsedValue = computed(() => {\n  let dayOrDays: DayOrDays\n  if (valueIsEmpty.value) {\n    if (pickerOptions.value.getDefaultValue) {\n      dayOrDays = pickerOptions.value.getDefaultValue()\n    }\n  } else {\n    if (isArray(props.modelValue)) {\n      dayOrDays = props.modelValue.map((d) =>\n        parseDate(d, props.valueFormat, lang.value)\n      ) as [Dayjs, Dayjs]\n    } else {\n      dayOrDays = parseDate(props.modelValue, props.valueFormat, lang.value)!\n    }\n  }\n\n  if (pickerOptions.value.getRangeAvailableTime) {\n    const availableResult = pickerOptions.value.getRangeAvailableTime(\n      dayOrDays!\n    )\n    if (!isEqual(availableResult, dayOrDays!)) {\n      dayOrDays = availableResult\n\n      // The result is corrected only when model-value exists\n      if (!valueIsEmpty.value) {\n        emitInput(dayOrDaysToDate(dayOrDays))\n      }\n    }\n  }\n  if (isArray(dayOrDays!) && dayOrDays.some((day) => !day)) {\n    dayOrDays = [] as unknown as DayOrDays\n  }\n  return dayOrDays!\n})\n\nconst displayValue = computed<UserInput>(() => {\n  if (!pickerOptions.value.panelReady) return ''\n  const formattedValue = formatDayjsToString(parsedValue.value)\n  if (isArray(userInput.value)) {\n    return [\n      userInput.value[0] || (formattedValue && formattedValue[0]) || '',\n      userInput.value[1] || (formattedValue && formattedValue[1]) || '',\n    ]\n  } else if (userInput.value !== null) {\n    return userInput.value\n  }\n  if (!isTimePicker.value && valueIsEmpty.value) return ''\n  if (!pickerVisible.value && valueIsEmpty.value) return ''\n  if (formattedValue) {\n    return isDatesPicker.value || isMonthsPicker.value || isYearsPicker.value\n      ? (formattedValue as Array<string>).join(', ')\n      : formattedValue\n  }\n  return ''\n})\n\nconst isTimeLikePicker = computed(() => props.type.includes('time'))\n\nconst isTimePicker = computed(() => props.type.startsWith('time'))\n\nconst isDatesPicker = computed(() => props.type === 'dates')\n\nconst isMonthsPicker = computed(() => props.type === 'months')\n\nconst isYearsPicker = computed(() => props.type === 'years')\n\nconst triggerIcon = computed(\n  () => props.prefixIcon || (isTimeLikePicker.value ? Clock : Calendar)\n)\n\nconst showClose = ref(false)\n\nconst onClearIconClick = (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (showClose.value) {\n    event.stopPropagation()\n    // When the handleClear Function was provided, emit null will be executed inside it\n    // There is no need for us to execute emit null twice. #14752\n    if (pickerOptions.value.handleClear) {\n      pickerOptions.value.handleClear()\n    } else {\n      emitInput(valueOnClear.value)\n    }\n    emitChange(valueOnClear.value, true)\n    showClose.value = false\n    onHide()\n  }\n  emit('clear')\n}\n\nconst valueIsEmpty = computed(() => {\n  const { modelValue } = props\n  return (\n    !modelValue || (isArray(modelValue) && !modelValue.filter(Boolean).length)\n  )\n})\n\nconst onMouseDownInput = async (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if ((event.target as HTMLElement)?.tagName !== 'INPUT' || isFocused.value) {\n    pickerVisible.value = true\n  }\n}\nconst onMouseEnter = () => {\n  if (props.readonly || pickerDisabled.value) return\n  if (!valueIsEmpty.value && props.clearable) {\n    showClose.value = true\n  }\n}\nconst onMouseLeave = () => {\n  showClose.value = false\n}\n\nconst onTouchStartInput = (event: TouchEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (\n    (event.touches[0].target as HTMLElement)?.tagName !== 'INPUT' ||\n    isFocused.value\n  ) {\n    pickerVisible.value = true\n  }\n}\n\nconst isRangeInput = computed(() => {\n  return props.type.includes('range')\n})\n\nconst pickerSize = useFormSize()\n\nconst popperEl = computed(() => unref(refPopper)?.popperRef?.contentRef)\n\nconst stophandle = onClickOutside(\n  inputRef as Ref<ComponentPublicInstance>,\n  (e: PointerEvent) => {\n    const unrefedPopperEl = unref(popperEl)\n    const inputEl = unrefElement(inputRef as Ref<ComponentPublicInstance>)\n    if (\n      (unrefedPopperEl &&\n        (e.target === unrefedPopperEl ||\n          e.composedPath().includes(unrefedPopperEl))) ||\n      e.target === inputEl ||\n      (inputEl && e.composedPath().includes(inputEl))\n    )\n      return\n    pickerVisible.value = false\n  }\n)\n\nonBeforeUnmount(() => {\n  stophandle?.()\n})\n\nconst userInput = ref<UserInput>(null)\n\nconst handleChange = () => {\n  if (userInput.value) {\n    const value = parseUserInputToDayjs(displayValue.value)\n    if (value) {\n      if (isValidValue(value)) {\n        emitInput(dayOrDaysToDate(value))\n        userInput.value = null\n      }\n    }\n  }\n  if (userInput.value === '') {\n    emitInput(valueOnClear.value)\n    emitChange(valueOnClear.value, true)\n    userInput.value = null\n  }\n}\n\nconst parseUserInputToDayjs = (value: UserInput) => {\n  if (!value) return null\n  return pickerOptions.value.parseUserInput!(value)\n}\n\nconst formatDayjsToString = (value: DayOrDays) => {\n  if (!value) return null\n  return pickerOptions.value.formatToString!(value)\n}\n\nconst isValidValue = (value: DayOrDays) => {\n  return pickerOptions.value.isValidValue!(value)\n}\n\nconst handleKeydownInput = async (event: Event | KeyboardEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n\n  const { code } = event as KeyboardEvent\n  emitKeydown(event as KeyboardEvent)\n  if (code === EVENT_CODE.esc) {\n    if (pickerVisible.value === true) {\n      pickerVisible.value = false\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    return\n  }\n\n  if (code === EVENT_CODE.down) {\n    if (pickerOptions.value.handleFocusPicker) {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    if (pickerVisible.value === false) {\n      pickerVisible.value = true\n      await nextTick()\n    }\n    if (pickerOptions.value.handleFocusPicker) {\n      pickerOptions.value.handleFocusPicker()\n      return\n    }\n  }\n\n  if (code === EVENT_CODE.tab) {\n    hasJustTabExitedInput = true\n    return\n  }\n\n  if (code === EVENT_CODE.enter || code === EVENT_CODE.numpadEnter) {\n    if (\n      userInput.value === null ||\n      userInput.value === '' ||\n      isValidValue(parseUserInputToDayjs(displayValue.value) as DayOrDays)\n    ) {\n      handleChange()\n      pickerVisible.value = false\n    }\n    event.stopPropagation()\n    return\n  }\n\n  // if user is typing, do not let picker handle key input\n  if (userInput.value) {\n    event.stopPropagation()\n    return\n  }\n  if (pickerOptions.value.handleKeydownInput) {\n    pickerOptions.value.handleKeydownInput(event as KeyboardEvent)\n  }\n}\nconst onUserInput = (e: string) => {\n  userInput.value = e\n  // Temporary fix when the picker is dismissed and the input box\n  // is focused, just mimic the behavior of antdesign.\n  if (!pickerVisible.value) {\n    pickerVisible.value = true\n  }\n}\n\nconst handleStartInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [target.value, userInput.value[1]]\n  } else {\n    userInput.value = [target.value, null]\n  }\n}\n\nconst handleEndInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [userInput.value[0], target.value]\n  } else {\n    userInput.value = [null, target.value]\n  }\n}\n\nconst handleStartChange = () => {\n  const values = userInput.value as string[]\n  const value = parseUserInputToDayjs(values && values[0]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      formatDayjsToString(value) as string,\n      displayValue.value?.[1] || null,\n    ]\n    const newValue = [value, parsedVal && (parsedVal[1] || null)] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst handleEndChange = () => {\n  const values = unref(userInput) as string[]\n  const value = parseUserInputToDayjs(values && values[1]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      unref(displayValue)?.[0] || null,\n      formatDayjsToString(value) as string,\n    ]\n    const newValue = [parsedVal && parsedVal[0], value] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst pickerOptions = ref<Partial<PickerOptions>>({})\n// @ts-ignore\nconst onSetPickerOption = <T extends keyof PickerOptions>(\n  e: [T, PickerOptions[T]]\n) => {\n  pickerOptions.value[e[0]] = e[1]\n  pickerOptions.value.panelReady = true\n}\n\n// @ts-ignore\nconst onCalendarChange = (e: [Date, null | Date]) => {\n  emit('calendar-change', e)\n}\n\n// @ts-ignore\nconst onPanelChange = (\n  value: [Dayjs, Dayjs],\n  mode: 'month' | 'year',\n  view: unknown\n) => {\n  emit('panel-change', value, mode, view)\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n}\n\nprovide('EP_PICKER_BASE', {\n  props,\n})\n\ndefineExpose({\n  /**\n   * @description focus input box.\n   */\n  focus,\n  /**\n   * @description blur input box.\n   */\n  blur,\n  /**\n   * @description opens picker\n   */\n  handleOpen,\n  /**\n   * @description closes picker\n   */\n  handleClose,\n  /**\n   * @description pick item manually\n   */\n  onPick,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;mCA6Mc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;AAcA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAE,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAE3B,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AAEpC,IAAA,MAAM,EAAE,IAAA,EAAM,QAAS,EAAA,GAAI,WAAY,EAAA,CAAA;AACvC,IAAA,MAAM,eAAkB,GAAA,MAAA,CAAO,iBAAmB,EAAA,EAAa,CAAA,CAAA;AAC/D,IAAA,MAAM,EAAE,YAAA,EAAiB,GAAA,cAAA,CAAe,OAAO,IAAI,CAAA,CAAA;AAEnD,IAAA,MAAM,YAAY,GAAqB,EAAA,CAAA;AACvC,IAAA,MAAM,WAAW,GAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,aAAA,GAAgB,IAAI,KAAK,CAAA,CAAA;AAC/B,IAAM,MAAA,mBAAA,GAAsB,IAAI,KAAK,CAAA,CAAA;AACrC,IAAM,MAAA,WAAA,GAAc,IAAiD,IAAI,CAAA,CAAA;AACzE,IAAA,IAAI,qBAAwB,GAAA,KAAA,CAAA;AAE5B,IAAA,MAAM,EAAE,SAAW,EAAA,WAAA,EAAa,UAAW,EAAA,GAAI,mBAAmB,QAAU,EAAA;AAAA,MAC1E,WAAc,GAAA;AACZ,QAAO,OAAA,KAAA,CAAM,YAAY,cAAe,CAAA,KAAA,CAAA;AAAA,OAC1C;AAAA,MACA,UAAa,GAAA;AACX,QAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AAAA,OACxB;AAAA,MACA,WAAW,KAAO,EAAA;AAChB,QAAA,IAAA,EAAA,CACE;AAAqE,QAEzE,OAAA,CAAA,qBAAA,KAAA,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,OACY;AACV,MAAa,SAAA,GAAA;AACb,QAAA,YAAA,EAAc,CAAQ;AACtB,QAAwB,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACxB,QAAM,qBAAA,GACM,KAAA,CAAA;AAA8C,QAC5D,KAAA,CAAA,aAAA,KAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACD;AAED,KAAM,CAAA,CAAA;AAA+B,IACnC,MAAA,aAAiB,GAAA,QAAA,CAAA,MAAA;AAAA,MACjB,MAAO,CAAA,CAAA,CAAA,QAAa,CAAA;AAAU,MAC9B,MAAA,CAAA,GAAU,QAAS,EAAA,KAAA,CAAA,IAAA,CAAA;AAAA,MACnB,OAAO,CAAA,CAAA,CAAG,SAAY,CAAA;AAAoB,MAC1C,MAAO,CAAA,EAAA,CAAG,UAAU,EAAA,cAAmB,CAAA,KAAA,CAAA;AAAA,MACvC,MAAA,CAAA,GAAU,QAAQ,EAAA,aAAA,CAAA,KAAA,CAAA;AAAA,MAClB,kBAAqB,CAAA;AAAiC,MACtD,UAAM,GAAA,OAAA,CAAA,EAAA,CAAA,QAAA,EAAA,UAAA,CAAA,KAAA,CAAA,GAAA,EAAA;AAAA,MACP,KAAA,CAAA,KAAA;AAED,KAAM,CAAA,CAAA;AAA8B,IAClC,MAAA,YAAgB,GAAA,QAAA,CAAA,MAAA;AAAA,MAChB,OAAA,CAAQ,EAAE,MAAY,CAAA;AAAA,MACtB,OAAW,CAAA,CAAA,CAAA,YAAgB,CAAA;AAA0B,MACtD,CAAA,SAAA,CAAA,KAAA,GAAA,OAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,EAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,KAAA,CAAI,aAAM,EAAA,CAAA,GAAA,KAAA;AACR,MAAA,IAAA,CAAA,GAAA,EAAA;AACA,QAAA,SAAS,CAAM,KAAA,GAAA,IAAA,CAAA;AACb,QAAA,QAAA,CAAA;AAA2B,UAC5B,UAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,SACI,CAAA,CAAA;AACL,OAAA,MAAA;AACE,QAAA,QAAS,CAAA,MAAA;AACP,UAAA,IAAA,GAAA,EAAA;AAA0B,YAC5B,WAAA,CAAA,KAAA,GAAA,KAAA,CAAA,UAAA,CAAA;AAAA,WACD;AAAA,SACH,CAAA,CAAA;AAAA,OACD;AACD,KAAM,CAAA,CAAA;AAKJ,IAAA,MAAI,aAAY,CAAA,GAAA,EAAA,OAAiB,KAAA;AAC/B,MAAA,IAAA,uBAAsB,CAAA,GAAA,EAAA,WAAA,CAAA,KAAA,CAAA,EAAA;AAEtB,QAAA,IAAA,CAAA,mBAAwB;AACxB,QAAM,OAAA,KAAA,WACJ,CAAU,KAAA,GAAA,GAAA,CAAA,CAAA;AAAgD,QAC9D,KAAA,CAAA,aAAA,KAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACF;AACA,KAAM,CAAA;AACJ,IAAA,MAAI,SAAC,GAAA,CAAY,KAAM,KAAA;AACrB,MAAI,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA,CAAA,EAAA;AACJ,QAAI,IAAA;AACF,QAAA,IAAA,OAAA,CAAA,KAAkB,CAAA,EAAA;AAAA,UAAA,SAChB,GAAA,KAAA,CAAA,GAAA,CAAU,UAAY,SAAA,CAAA,IAAA,OAAuB,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,SAC/C,MAAA,IAAA,KAAA,EAAA;AAAA,mBACS,GAAO,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAChB,SAAA;AAA0D,QAC5D,IAAA,CAAA,kBAAA,EAAA,KAAA,GAAA,SAAA,GAAA,KAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,OAAA;AAA8D,KAChE,CAAA;AAAA,IACF,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AACA,MAAM,IAAA,CAAA,SAAA,EAAA,CAAA,CAAc,CAAC;AACnB,KAAA,CAAA;AAAiB,IACnB,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAM,IAAA,QAAA,CAAA;AACJ,QAAA,YAAoB,CAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAClB,OAAA;AAAa,MAAA,OACF,EAAA,CAAA;AAAkC,KAC7C,CAAA,CAAA;AAAA,IACF,MAAA,iBAAA,GAAA,CAAA,KAAA,EAAA,GAAA,EAAA,GAAA,KAAA;AACA,MAAA,MAAA,OAAQ,GAAA,QAAA,CAAA,KAAA,CAAA;AAAA,MACT,IAAA,CAAA,OAAA,CAAA,MAAA;AAGD,QAAA,OAA0B;AACxB,MAAA,IAAA,CAAA,eAAyB,KAAA,EAAA;AACzB,QAAI,WAAS,iBAAQ,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AACrB,QAAI,OAAQ,CAAA,CAAA,CAAA,CAAA,KAAA,EAAQ,CAAO;AACzB,OAAA,MAAA,IAAS,GAAoB,KAAA,KAAA,EAAA;AAC7B,QAAQ,OAAA,CAAA,CAAC,EAAE,iBAAM,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACnB,OAAA,CAAA,CAAW,SAAe,CAAA;AACxB,OAAA;AACA,KAAQ,CAAA;AAAS,IACnB,MAAA,MAAA,GAAA,CAAA,IAAA,GAAA,EAAA,EAAA,OAAA,GAAA,KAAA,KAAA;AAAA,MACF,aAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AAEA,MAAA,IAAM,MAAS,CAAA;AACb,MAAA,IAAA,OAAA,CAAA,IAAsB,CAAA,EAAA;AACtB,QAAI,MAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AACJ,OAAI,MAAA;AACF,QAAA,MAAA,GAAS,OAAS,IAAO,CAAA,MAAE,SAAQ,CAAA;AAAA,OAC9B;AAEL,MAAS,SAAA,CAAA,KAAA,GAAO,IAAK,CAAA;AAAW,MAClC,SAAA,CAAA,MAAA,CAAA,CAAA;AACA,KAAA,CAAA;AACA,IAAA,MAAA,YAAgB,GAAA,MAAA;AAAA,MAClB,mBAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,MAAA,GAAA,MAAA;AAA4B,MAC9B,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA;AAA2B,MAC7B,mBAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,aAAe,CAAM,KAAA,GAAA,KAAA,CAAA;AACnB,MAAA,IAAA,CAAA,gBAA4B,EAAA,KAAA,CAAA,CAAA;AAC5B,KAAA,CAAA;AACA,IAAA,MAAA,mBAAuB;AAAK,MAC9B,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,WAAsB,GAAA,MAAA;AAAA,MACxB,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,cAAsB,GAAA,QAAA,CAAA,MAAA;AAAA,MACxB,OAAA,KAAA,CAAA,QAAA,KAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,sBAAwB,CAAA,MAAA;AAAA,MAChC,IAAA,SAAA,CAAA;AAED,MAAM,IAAA,YAAA,CAAA;AACJ,QAAI,IAAA,aAAA,CAAA,KAAA,CAAA,eAAA,EAAA;AACJ,UAAI,yBAAoB,CAAA,KAAA,CAAA,eAAA,EAAA,CAAA;AACtB,SAAI;AACF,OAAY,MAAA;AAAoC,QAClD,IAAA,OAAA,CAAA,KAAA,CAAA,UAAA,CAAA,EAAA;AAAA,UACK,SAAA,GAAA,KAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,SAAA,CAAA,CAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACL,SAAI,MAAA;AACF,UAAA,SAAA,GAAY,SAAiB,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SAAA;AACe,OAC5C;AAAA,MAAA,IACK,aAAA,CAAA,KAAA,CAAA,qBAAA,EAAA;AACL,QAAA,MAAA,kBAAsB,aAAM,CAAA,KAAkB,CAAA,sBAAkB,SAAK,CAAA,CAAA;AAAA,QACvE,IAAA,CAAA,OAAA,CAAA,eAAA,EAAA,SAAA,CAAA,EAAA;AAAA,UACF,SAAA,GAAA,eAAA,CAAA;AAEA,UAAI,IAAA,CAAA,kBAA2C,EAAA;AAC7C,YAAM,SAAA,CAAA,yBAAsC,CAAA,CAAA,CAAA;AAAA,WAC1C;AAAA,SACF;AACA,OAAA;AACE,MAAY,IAAA,OAAA,CAAA,SAAA,CAAA,IAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AAGZ,QAAI;AACF,OAAU;AAA0B,MACtC,OAAA,SAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAI,IAAA,CAAA,mBAAuB,CAAA,UAAA;AACzB,QAAA,OAAA,EAAA,CAAA;AAAa,MACf,MAAA,cAAA,GAAA,mBAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAO,IAAA,OAAA,CAAA,SAAA,CAAA,KAAA,CAAA,EAAA;AAAA,QACR,OAAA;AAED,UAAM,SAAA,CAAA,KAAe,qBAA0B,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AAC7C,UAAI,SAAC,CAAA,KAAc,CAAM,CAAA,CAAA,IAAA,cAAmB,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AAC5C,SAAM,CAAA;AACN,OAAI,MAAA,IAAA,SAAkB,CAAA,KAAA,KAAQ,IAAA,EAAA;AAC5B,QAAO,OAAA,SAAA,CAAA,KAAA,CAAA;AAAA,OAAA;AAC0D,MAAA,IAC/D,aAAgB,CAAA,SAAyB,YAAA,CAAA,KAAA;AAAsB,QACjE,OAAA,EAAA,CAAA;AAAA,MACF,IAAA,CAAA,aAAqB,CAAA,KAAA,IAAA,YAAgB,CAAA,KAAA;AACnC,QAAA,OAAO,EAAU,CAAA;AAAA,MACnB,IAAA,cAAA,EAAA;AACA,QAAA,OAAK,aAAsB,CAAA,KAAA,IAAA,cAAoB,CAAO,KAAA,IAAA,aAAA,CAAA,KAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,cAAA,CAAA;AACtD,OAAA;AACA,MAAA,OAAoB,EAAA,CAAA;AAClB,KAAO,CAAA,CAAA;AAEH,IACN,MAAA,gBAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA,IAAO,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IACT,MAAC,aAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA,CAAA;AAED,IAAA,MAAM,yBAA4B,CAAA,MAAA,UAAiB,KAAA;AAEnD,IAAA,MAAM,gBAAwB,QAAA,CAAA,WAAY,CAAK,IAAA,KAAA;AAE/C,IAAA,MAAM,WAAgB,GAAA,QAAA,CAAA,MAAe,KAAA,CAAA,eAAsB,gBAAA,CAAA,KAAA,GAAA,KAAA,GAAA,QAAA,CAAA,CAAA,CAAA;AAE3D,IAAA,MAAM,SAAiB,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAEvB,IAAA,MAAM,gBAAgB,GAAA,CAAA,KAAS,KAAM;AAErC,MAAA,IAAM,KAAc,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAAA,QACZ,OAAA;AAAsD,MAC9D,IAAA,SAAA,CAAA,KAAA,EAAA;AAEA,QAAM,KAAA,CAAA,eAAqB,EAAA,CAAA;AAE3B,QAAM,IAAA,aAAA,CAAA,KAA0C,CAAA,WAAA,EAAA;AAC9C,UAAI,aAAkB,CAAA,KAAA,CAAA,WAAA,EAAe,CAAO;AAC5C,SAAA;AACE,UAAA,SAAsB,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAGtB,SAAI;AACF,QAAA,UAAA,CAAA,YAAgC,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,QAClC,SAAO,CAAA,KAAA,GAAA,KAAA,CAAA;AACL,QAAA,MAAA,EAAA,CAAA;AAA4B,OAC9B;AACA,MAAW,IAAA,CAAA,OAAA,CAAA,CAAA;AACX,KAAA,CAAA;AACA,IAAO,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACT,MAAA,EAAA,UAAA,EAAA,GAAA,KAAA,CAAA;AACA,MAAA,OAAY,CAAA,UAAA,IAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA;AAAA,KACd,CAAA,CAAA;AAEA,IAAM,MAAA,0BAA8B,KAAA,KAAA;AAClC,MAAM,IAAA,EAAA,CAAA;AACN,MACE,IAAA,kBAAgB,cAAQ,CAAA;AAA2C,QAEtE,OAAA;AAED,MAAM,IAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAmB,SAA6B,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AACpD,QAAI,aAAkB,CAAA,KAAA,GAAA,IAAA,CAAA;AACtB,OAAA;AACE,KAAA,CAAA;AAAsB,IACxB,MAAA,YAAA,GAAA,MAAA;AAAA,MACF,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AACA,QAAA;AACE,MAAI,IAAA,CAAA,YAAkB,CAAA,KAAA,IAAA,KAAA,CAAA,SAAsB,EAAA;AAC5C,QAAA,SAAK,CAAA,KAAA,GAAsB,IAAA,CAAA;AACzB,OAAA;AAAkB,KACpB,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,MAAA;AACA,MAAA,uBAA2B,CAAA;AACzB,KAAA,CAAA;AAAkB,IACpB,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAI,IAAA,KAAA,CAAM,QAAY,IAAA,cAAA,CAAe,KAAO;AAC5C,QACG,OAAA;AAGD,MAAA,IAAA,CAAA,CAAA,EAAA,GAAA,KAAc,CAAQ,OAAA,CAAA,CAAA,CAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACxB,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAO,MAAA,YAAW,GAAA,QAAS,CAAO,MAAA;AAAA,MACnC,OAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,aAAoB,WAAA,EAAM;AAEhC,IAAA,MAAM,QAAa,GAAA,QAAA,CAAA,MAAA;AAAA,MACjB,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACA,OAAqB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,SAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AACnB,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAA,cAAuB,CAAwC,QAAA,EAAA,CAAA,CAAA,KAAA;AACrE,MAAA,MACG,uBACI,CAAA,QAAA,CAAW;AAKhB,MAAA,MAAA,OAAA,GAAA,YAAA,CAAA,QAAA,CAAA,CAAA;AACF,MAAA,IAAA,eAAsB,KAAA,CAAA,CAAA,MAAA,KAAA,eAAA,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA,OAAA,IAAA,OAAA,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AAAA,QACxB,OAAA;AAAA,MACF,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAa,eAAA,CAAA,MAAA;AAAA,MACd,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AAED,KAAM,CAAA,CAAA;AAEN,IAAA,MAAM,eAAe,CAAM,IAAA,CAAA,CAAA;AACzB,IAAA,MAAI,YAAiB,GAAA,MAAA;AACnB,MAAM,IAAA,SAAA,CAAA,KAA8B,EAAA;AACpC,QAAA,MAAW,KAAA,GAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACT,QAAI,IAAA,KAAA,EAAA;AACF,UAAU,IAAA,YAAA,CAAA,KAAA,CAAA,EAAA;AACV,YAAA,SAAA,CAAU,eAAQ,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,YACpB,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,WACF;AAAA,SACF;AACA,OAAI;AACF,MAAA,IAAA,sBAA4B,EAAA;AAC5B,QAAW,SAAA,CAAA,YAAA,CAAA;AACX,QAAA,UAAU,CAAQ,YAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,QACpB,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,qBAAe,GAAA,CAAA,KAAA,KAAA;AACnB,MAAO,IAAA,CAAA,KAAA;AAAyC,QAClD,OAAA,IAAA,CAAA;AAEA,MAAM,OAAA,aAAA,CAAA,KAAsB,CAAC,cAAqB,CAAA,KAAA,CAAA,CAAA;AAChD,KAAI,CAAA;AACJ,IAAO,MAAA,mBAAoB,GAAA,CAAA,KAAA,KAAA;AAAqB,MAClD,IAAA,CAAA,KAAA;AAEA,QAAM,OAAA,IAAA,CAAA;AACJ,MAAO,OAAA,aAAA,CAAc,KAAM,CAAA,cAAmB,CAAA,KAAA,CAAA,CAAA;AAAA,KAChD,CAAA;AAEA,IAAM,MAAA,YAAA,GAAA,CAAA,KAAqB;AACzB,MAAI,OAAA,aAAkB,CAAA,KAAA,CAAA,YAAsB,CAAA,KAAA,CAAA,CAAA;AAE5C,KAAM,CAAA;AACN,IAAA,MAAA,kBAAkC,GAAA,OAAA,KAAA,KAAA;AAClC,MAAI,IAAA,KAAA,CAAA,0BAAyB,CAAA,KAAA;AAC3B,QAAI,OAAA;AACF,MAAA,MAAA,EAAA,IAAA,EAAA,GAAA,KAAsB,CAAA;AACtB,MAAA,WAAqB,CAAA,KAAA,CAAA,CAAA;AACrB,MAAA,IAAA,IAAA,KAAsB,UAAA,CAAA,GAAA,EAAA;AAAA,QACxB,IAAA,aAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AACA,UAAA,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,UACF,KAAA,CAAA,cAAA,EAAA,CAAA;AAEA,UAAI,KAAA,CAAA,eAA0B,EAAA,CAAA;AAC5B,SAAI;AACF,QAAA,OAAA;AACA,OAAA;AAAsB,MACxB,IAAA,IAAA,KAAA,UAAA,CAAA,IAAA,EAAA;AACA,QAAI,IAAA,aAAA,CAAc,uBAAiB,EAAA;AACjC,UAAA,KAAA,CAAA,cAAsB,EAAA,CAAA;AACtB,UAAA,KAAA,CAAM,eAAS,EAAA,CAAA;AAAA,SACjB;AACA,QAAI,IAAA,aAAA,CAAc,UAAyB,KAAA,EAAA;AACzC,UAAA,aAAA,CAAc,QAAwB,IAAA,CAAA;AACtC,UAAA,MAAA,QAAA,EAAA,CAAA;AAAA,SACF;AAAA,QACF,IAAA,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA;AAEA,UAAI,oBAAoB,iBAAK,EAAA,CAAA;AAC3B,UAAwB,OAAA;AACxB,SAAA;AAAA,OACF;AAEA,MAAA,IAAI,IAAS,KAAA,UAAA,CAAW,GAAS,EAAA;AAC/B,QACE,qBAAoB,GAAA,IAAA,CAAA;AAIpB,QAAa,OAAA;AACb,OAAA;AAAsB,MACxB,IAAA,IAAA,KAAA,UAAA,CAAA,KAAA,IAAA,IAAA,KAAA,UAAA,CAAA,WAAA,EAAA;AACA,QAAA,IAAA,SAAsB,CAAA,KAAA,KAAA,IAAA,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,IAAA,YAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACtB,UAAA,YAAA,EAAA,CAAA;AAAA,UACF,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAGA,SAAA;AACE,QAAA,KAAA,CAAM,eAAgB,EAAA,CAAA;AACtB,QAAA,OAAA;AAAA,OACF;AACA,MAAI,IAAA,SAAA,CAAA;AACF,QAAc,KAAA,CAAA;AAA+C,QAC/D,OAAA;AAAA,OACF;AACA,MAAM,IAAA,aAAA,CAAc,KAAe,CAAA,kBAAA,EAAA;AACjC,QAAA,aAAkB,CAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AAGlB,OAAI;AACF,KAAA,CAAA;AAAsB,IACxB,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACF,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAEA,MAAM,IAAA,CAAA,aAAA,CAAA,KAAoB,EAAiB;AACzC,QAAA,aAAe,CAAM,KAAA,GAAA,IAAA,CAAA;AACrB,OAAA;AACE,KAAA,CAAA;AAAmD,IAAA,MAC9C,gBAAA,GAAA,CAAA,KAAA,KAAA;AACL,MAAA,MAAA,MAAU,GAAQ,KAAA,CAAC,MAAO,CAAA;AAAW,MACvC,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,OAAM,MAAA;AACJ,QAAA,eAAqB,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACrB,OAAA;AACE,KAAA,CAAA;AAAmD,IAAA,MAC9C,cAAA,GAAA,CAAA,KAAA,KAAA;AACL,MAAA,MAAA,MAAU,GAAQ,KAAA,CAAC,MAAM,CAAA;AAAY,MACvC,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAEA,OAAA;AACE,QAAA,eAAyB,GAAA,CAAA,IAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACzB,OAAA;AACA,KAAM,CAAA;AACN,IAAI,MAAA,iBAAe,GAAA,MAAW;AAC5B,MAAA,IAAA,EAAA,CAAA;AAAkB,MAAA,wBACI,CAAK,KAAA,CAAA;AAAA,MACzB,MAAA,KAAA,GAAA,qBAA2B,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAC7B,MAAA,SAAA,GAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACA,MAAA,IAAA,eAAiB,OAAC;AAClB,QAAI,SAAA,CAAA,KAAA;AACF,UAAU,mBAAA,CAAA,KAAA,CAAgB;AAC1B,UAAA,CAAA,CAAA,EAAA,GAAA,YAAkB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAAA,SACpB,CAAA;AAAA,QACF,MAAA,QAAA,GAAA,CAAA,KAAA,EAAA,SAAA,KAAA,SAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;AAAA,QACF,IAAA,YAAA,CAAA,QAAA,CAAA,EAAA;AAEA,UAAM,yBAAwB,CAAA,QAAA,CAAA,CAAA,CAAA;AAC5B,UAAM,SAAA,CAAA,QAAwB,IAAA,CAAA;AAC9B,SAAA;AACA,OAAM;AACN,KAAI,CAAA;AACF,IAAA,MAAA,eAAkB,GAAA,MAAA;AAAA,MAAA,IAChB,EAAM,CAAA;AAAsB,MAAA,8BACH,CAAA,CAAA;AAAA,MAC3B,MAAA,KAAA,GAAA,qBAAA,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,MAAA,iBAAkB,CAAA,WAAuB,CAAA,CAAA;AACzC,MAAI,IAAA,KAAA,IAAA,KAAA,CAAa,SAAW,EAAA;AAC1B,QAAU,SAAA,CAAA,KAAA,GAAA;AACV,UAAA,CAAA,CAAA,EAAA,GAAA,KAAkB,CAAA,YAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAAA,UACpB,mBAAA,CAAA,KAAA,CAAA;AAAA,SACF,CAAA;AAAA,QACF,MAAA,QAAA,GAAA,CAAA,SAAA,IAAA,SAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAEA,QAAM,IAAA,YAAA,CAAA,QAA8C,CAAA,EAAA;AAEpD,UAAM,SAAA,CAAA,eAED,CAAA,QAAA,CAAA,CAAA,CAAA;AACH,UAAA,SAAA,CAAc,QAAQ,IAAE,CAAA;AACxB,SAAA;AAAiC,OACnC;AAGA,KAAM,CAAA;AACJ,IAAA,MAAA,oBAAwB,EAAC,CAAA,CAAA;AAAA,IAC3B,MAAA,iBAAA,GAAA,CAAA,CAAA,KAAA;AAGA,MAAA,aAAsB,CAAA,KAAA,CAAA,CACpB,CACA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAGA,MAAK,aAAA,CAAA,KAAA,CAAA,UAAuB,GAAA,IAAU,CAAA;AAAA,KACxC,CAAA;AAEA,IAAA,MAAM,gBAAc,GAAA,CAAA,CAAA,KAAA;AAClB,MAAA,IAAA,CAAA,iBAAsB,EAAA,CAAA,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAA,MAAM,aAAa,GAAA,CAAA,KAAA,EAAA,IAAA,EAAA,IAAA,KAAA;AACjB,MAAA,IAAA,CAAA,cAAqB,EAAA,KAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KACvB,CAAA;AAEA,IAAA,MAAA,KAA0B,GAAA,MAAA;AAAA,MACxB,IAAA,EAAA,CAAA;AAAA,MACD,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAED,KAAa,CAAA;AAAA,IAAA,MAAA,IAAA,GAAA,MAAA;AAAA,MAAA,IAAA,EAAA,CAAA;AAAA,MAAA,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAAA,KAIX,CAAA;AAAA,IAAA,OAAA,CAAA,gBAAA,EAAA;AAAA,MAAA,KAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAIA,MAAA,CAAA;AAAA,MAAA,KAAA;AAAA,MAAA,IAAA;AAAA,MAAA,UAAA;AAAA,MAIA,WAAA;AAAA,MAAA,MAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAIA,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,SAAA,CAAA,EAAAC,UAAA,CAAA;AAAA,QAAA,OAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA,SAAA;AAAA,QAAA,OAAA,EAAA,aAAA,CAAA,KAAA;AAAA,QAIA,MAAA,EAAA,OAAA;AAAA,QACD,IAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}