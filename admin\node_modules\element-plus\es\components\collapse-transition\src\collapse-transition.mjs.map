{"version": 3, "file": "collapse-transition.mjs", "sources": ["../../../../../../packages/components/collapse-transition/src/collapse-transition.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b()\" v-on=\"on\">\n    <slot />\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport type { RendererElement } from '@vue/runtime-core'\n\ndefineOptions({\n  name: 'ElCollapseTransition',\n})\n\nconst ns = useNamespace('collapse-transition')\n\nconst reset = (el: RendererElement) => {\n  el.style.maxHeight = ''\n  el.style.overflow = el.dataset.oldOverflow\n  el.style.paddingTop = el.dataset.oldPaddingTop\n  el.style.paddingBottom = el.dataset.oldPaddingBottom\n}\n\nconst on = {\n  beforeEnter(el: RendererElement) {\n    if (!el.dataset) el.dataset = {}\n\n    el.dataset.oldPaddingTop = el.style.paddingTop\n    el.dataset.oldPaddingBottom = el.style.paddingBottom\n    if (el.style.height) el.dataset.elExistsHeight = el.style.height\n\n    el.style.maxHeight = 0\n    el.style.paddingTop = 0\n    el.style.paddingBottom = 0\n  },\n\n  enter(el: RendererElement) {\n    requestAnimationFrame(() => {\n      el.dataset.oldOverflow = el.style.overflow\n      if (el.dataset.elExistsHeight) {\n        el.style.maxHeight = el.dataset.elExistsHeight\n      } else if (el.scrollHeight !== 0) {\n        el.style.maxHeight = `${el.scrollHeight}px`\n      } else {\n        el.style.maxHeight = 0\n      }\n\n      el.style.paddingTop = el.dataset.oldPaddingTop\n      el.style.paddingBottom = el.dataset.oldPaddingBottom\n      el.style.overflow = 'hidden'\n    })\n  },\n\n  afterEnter(el: RendererElement) {\n    el.style.maxHeight = ''\n    el.style.overflow = el.dataset.oldOverflow\n  },\n\n  enterCancelled(el: RendererElement) {\n    reset(el)\n  },\n\n  beforeLeave(el: RendererElement) {\n    if (!el.dataset) el.dataset = {}\n    el.dataset.oldPaddingTop = el.style.paddingTop\n    el.dataset.oldPaddingBottom = el.style.paddingBottom\n    el.dataset.oldOverflow = el.style.overflow\n\n    el.style.maxHeight = `${el.scrollHeight}px`\n    el.style.overflow = 'hidden'\n  },\n\n  leave(el: RendererElement) {\n    if (el.scrollHeight !== 0) {\n      el.style.maxHeight = 0\n      el.style.paddingTop = 0\n      el.style.paddingBottom = 0\n    }\n  },\n\n  afterLeave(el: RendererElement) {\n    reset(el)\n  },\n\n  leaveCancelled(el: RendererElement) {\n    reset(el)\n  },\n}\n</script>\n"], "names": [], "mappings": ";;;;mCAUc,CAAA;AAAA,EACZ,IAAM,EAAA,sBAAA;AACR,CAAA,CAAA,CAAA;;;;AAEA,IAAM,MAAA,EAAA,GAAK,aAAa,qBAAqB,CAAA,CAAA;AAE7C,IAAM,MAAA,KAAA,GAAQ,CAAC,EAAwB,KAAA;AACrC,MAAA,EAAA,CAAG,MAAM,SAAY,GAAA,EAAA,CAAA;AACrB,MAAG,EAAA,CAAA,KAAA,CAAM,QAAW,GAAA,EAAA,CAAG,OAAQ,CAAA,WAAA,CAAA;AAC/B,MAAG,EAAA,CAAA,KAAA,CAAM,UAAa,GAAA,EAAA,CAAG,OAAQ,CAAA,aAAA,CAAA;AACjC,MAAG,EAAA,CAAA,KAAA,CAAM,aAAgB,GAAA,EAAA,CAAG,OAAQ,CAAA,gBAAA,CAAA;AAAA,KACtC,CAAA;AAEA,IAAA,MAAM,EAAK,GAAA;AAAA,MACT,YAAY,EAAqB,EAAA;AAC/B,QAAA,IAAI,CAAC,EAAA,CAAG,OAAS;AAEjB,UAAG,EAAA,CAAA,OAAwB,GAAA,EAAA,CAAA;AAC3B,QAAG,EAAA,CAAA,OAAA,CAAQ,aAAmB,GAAA,EAAA,CAAA,KAAS,CAAA,UAAA,CAAA;AACvC,QAAA,EAAA,CAAA,OAAa,CAAA,gBAAmB,GAAA,EAAA,CAAA,KAAA,CAAA,aAAoB,CAAM;AAE1D,QAAA,YAAqB,CAAA,MAAA;AACrB,UAAA,UAAsB,CAAA,cAAA,GAAA,EAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACtB,QAAA,EAAA,CAAG,MAAM,SAAgB,GAAA,CAAA,CAAA;AAAA,QAC3B,EAAA,CAAA,KAAA,CAAA,UAAA,GAAA,CAAA,CAAA;AAAA,gBAE2B,CAAA,aAAA,GAAA,CAAA,CAAA;AACzB,OAAA;AACE,MAAG,KAAA,CAAA,EAAA,EAAA;AACH,QAAI,qBAA2B,CAAA,MAAA;AAC7B,UAAG,EAAA,CAAA,OAAA,CAAM,WAAY,GAAA,EAAW,CAAA,KAAA,CAAA,QAAA,CAAA;AAAA,UAClC,IAAA,EAAA,CAAA,OAAc,CAAA,cAAA,EAAiB;AAC7B,YAAA,EAAA,CAAG,KAAM,CAAA,SAAA,GAAY,EAAG,CAAA,OAAe,CAAA,cAAA,CAAA;AAAA,WAClC,MAAA,IAAA,EAAA,CAAA,YAAA,KAAA,CAAA,EAAA;AACL,YAAA,EAAA,CAAG,MAAM,SAAY,GAAA,CAAA,EAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,WACvB,MAAA;AAEA,YAAG,EAAA,CAAA,KAAmB,CAAA,SAAA,GAAA,CAAA,CAAG;AACzB,WAAG;AACH,UAAA,EAAA,CAAG,MAAM,UAAW,GAAA,EAAA,CAAA,OAAA,CAAA,aAAA,CAAA;AAAA,UACrB,EAAA,CAAA,KAAA,CAAA,aAAA,GAAA,EAAA,CAAA,OAAA,CAAA,gBAAA,CAAA;AAAA,UACH,EAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA,CAAA;AAAA;AAGE,OAAA;AACA,MAAG,UAAA,CAAM,EAAW,EAAA;AAAW,QACjC,EAAA,CAAA,KAAA,CAAA,SAAA,GAAA,EAAA,CAAA;AAAA,yBAEoC,GAAA,EAAA,CAAA,OAAA,CAAA,WAAA,CAAA;AAClC,OAAA;AAAQ,MACV,cAAA,CAAA,EAAA,EAAA;AAAA,kBAEY;AACV,OAAA;AACA,MAAG,WAAA,CAAA,EAAwB,EAAA;AAC3B,QAAG,IAAA,CAAA,EAAA,CAAA,OAA2B;AAC9B,UAAG,EAAA,CAAA,OAAsB,GAAA,EAAA,CAAA;AAEzB,QAAA,EAAA,CAAG,OAAM,CAAA,aAAe,GAAG,EAAY,CAAA,KAAA,CAAA,UAAA,CAAA;AACvC,QAAA,EAAA,CAAG,OAAiB,CAAA,gBAAA,GAAA,EAAA,CAAA,KAAA,CAAA,aAAA,CAAA;AAAA,QACtB,EAAA,CAAA,OAAA,CAAA,WAAA,GAAA,EAAA,CAAA,KAAA,CAAA,QAAA,CAAA;AAAA,gBAE2B,CAAA,SAAA,GAAA,CAAA,EAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AACzB,QAAI,EAAA,CAAA,yBAAuB,CAAA;AACzB,OAAA;AACA,MAAA,KAAA,CAAA;AACA,QAAA,IAAA,eAAyB,KAAA,CAAA,EAAA;AAAA,UAC3B,EAAA,CAAA,KAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AAAA,UACF,EAAA,CAAA,KAAA,CAAA,UAAA,GAAA,CAAA,CAAA;AAAA,kBAEgC,CAAA,aAAA,GAAA,CAAA,CAAA;AAC9B,SAAA;AAAQ,OACV;AAAA,MAEA,eAAe;AACb,QAAA,KAAA,CAAM,EAAE,CAAA,CAAA;AAAA,OACV;AAAA,MACF,cAAA,CAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;"}