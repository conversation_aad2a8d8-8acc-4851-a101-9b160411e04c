const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const argon2 = require('argon2');
const sequelize = require('../config/database');

const Admin = sequelize.define('Admin', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      len: [3, 50],
    },
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
    },
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: false,
  },
  is_super: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    allowNull: false,
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true,
  },
}, {
  tableName: 'admins',
  timestamps: true,
  hooks: {
    beforeCreate: async (admin) => {
      if (admin.password) {
        try {
          // 使用Argon2加密密码
          admin.password = await argon2.hash(admin.password, {
            type: argon2.argon2id, // 使用argon2id变体，安全性和性能均衡
            memoryCost: 2**16, // 64MB
            timeCost: 3, // 3次迭代
            parallelism: 1 // 并行度
          });
        } catch (error) {
          // 如果Argon2失败，回退到bcrypt
          console.warn('使用Argon2加密失败，回退到bcrypt:', error);
          admin.password = await bcrypt.hash(admin.password, 12); // 增加监理轮数到12
        }
      }
    },
    beforeUpdate: async (admin) => {
      if (admin.changed('password')) {
        try {
          // 使用Argon2加密密码
          admin.password = await argon2.hash(admin.password, {
            type: argon2.argon2id,
            memoryCost: 2**16,
            timeCost: 3,
            parallelism: 1
          });
        } catch (error) {
          // 如果Argon2失败，回退到bcrypt
          console.warn('使用Argon2加密失败，回退到bcrypt:', error);
          admin.password = await bcrypt.hash(admin.password, 12);
        }
      }
    },
  },
});

// 实例方法：验证密码
Admin.prototype.validatePassword = async function(password) {
  try {
    // 先尝试使用Argon2验证
    if (this.password.startsWith('$argon2')) {
      return await argon2.verify(this.password, password);
    }
    // 如果不是Argon2格式，使用bcrypt
    return await bcrypt.compare(password, this.password);
  } catch (error) {
    console.error('密码验证错误:', error);
    // 如果验证过程出错，返回false
    return false;
  }
};

module.exports = Admin;
