/**
 * 浏览器限制中间件
 * 仅允许Chrome浏览器访问管理后台
 */
const browserRestrictionMiddleware = (req, res, next) => {
  // 获取User-Agent
  const userAgent = req.headers['user-agent'] || '';

  // 检查是否为管理后台路径
  const isAdminPath = req.path.startsWith('/api/admin');

  // 如果不是管理后台路径，直接放行
  if (!isAdminPath) {
    return next();
  }

  // 检查是否为Chrome浏览器
  const isChrome = userAgent.includes('Chrome') && !userAgent.includes('Edg') && !userAgent.includes('OPR');

  if (!isChrome) {
    // 记录非法访问
    console.warn(`非法浏览器访问管理后台: ${userAgent}, IP: ${req.ip}`);

    // 将IP地址记录到数据库或文件中
    const fs = require('fs');
    const path = require('path');
    const logDir = path.join(__dirname, '../logs');

    // 确保日志目录存在
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 记录非法访问
    const logEntry = {
      timestamp: new Date().toISOString(),
      ip: req.ip,
      userAgent: userAgent,
      path: req.path,
      method: req.method
    };

    fs.appendFileSync(
      path.join(logDir, 'illegal_access.log'),
      JSON.stringify(logEntry) + '\n'
    );

    // 返回403错误，使用更友好的错误信息
    return res.status(403).json({
      code: 403,
      message: '禁止访问',
      data: null
    });
  }

  next();
};

module.exports = browserRestrictionMiddleware;
